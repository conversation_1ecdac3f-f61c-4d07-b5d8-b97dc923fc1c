#!/usr/bin/env python3
"""
Test script for verifying the enhanced API integration
"""

import requests
import time
import json
from datetime import datetime

API_BASE = "http://localhost:5002/api/code-reviewer"

def test_enhanced_review():
    """Test the enhanced review flow with structured output"""
    
    print("🧪 Testing Enhanced API Integration")
    print("=" * 50)
    
    # Test data
    test_branch = "feature/test-enhanced-api"
    test_repo = "/Users/<USER>/dev/rma-mono"
    
    # 1. Health check
    print("\n1. Testing health check...")
    try:
        resp = requests.get("http://localhost:5002/api/health")
        assert resp.status_code == 200
        print("✅ Health check passed")
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return
    
    # 2. Start structured review
    print("\n2. Starting structured review...")
    payload = {
        "branch_name": test_branch,
        "repository_path": test_repo,
        "review_mode": "comprehensive_with_ac",
        "include_summary": True,
        "jira_ticket": {
            "ticket_id": "TEST-123",
            "summary": "Test ticket for enhanced API",
            "description": "Testing the new structured output",
            "acceptance_criteria": "1. API returns structured JSON\n2. German prompts are used\n3. Phase 3 summary is included"
        }
    }
    
    try:
        resp = requests.post(f"{API_BASE}/start-structured-review", json=payload)
        data = resp.json()
        assert data["success"] == True
        session_id = data["session"]["session_id"]
        print(f"✅ Review started: {session_id}")
        print(f"   SDK Enabled: {data['session'].get('sdk_enabled', False)}")
        print(f"   Structured Output: {data['session'].get('structured_output', False)}")
    except Exception as e:
        print(f"❌ Failed to start review: {e}")
        return
    
    # 3. Monitor progress
    print("\n3. Monitoring progress...")
    max_attempts = 60  # 5 minutes max
    attempt = 0
    
    while attempt < max_attempts:
        try:
            resp = requests.get(f"{API_BASE}/review-status/{session_id}")
            status_data = resp.json()
            
            if status_data["success"]:
                session = status_data["session"]
                print(f"   Progress: {session['progress']}% - {session.get('progress_message', 'Processing...')}")
                
                if session["status"] == "completed":
                    print("✅ Review completed!")
                    break
                elif session["status"] == "error":
                    print(f"❌ Review failed: {session.get('error', 'Unknown error')}")
                    return
            
            time.sleep(5)
            attempt += 1
            
        except Exception as e:
            print(f"❌ Error checking status: {e}")
            return
    
    if attempt >= max_attempts:
        print("⏱️ Review timed out")
        return
    
    # 4. Get structured results
    print("\n4. Fetching structured results...")
    try:
        resp = requests.get(f"{API_BASE}/review-results/{session_id}")
        results_data = resp.json()
        
        if results_data["success"]:
            results = results_data["results"]
            print("✅ Got structured results:")
            print(f"   Review Type: {results['review_type']}")
            print(f"   Success: {results['success']}")
            print(f"   Timestamp: {results['timestamp']}")
            
            # Check structured data
            structured = results.get("structured_data", {})
            print(f"\n   Executive Summary:")
            exec_summary = structured.get("executive_summary", {})
            print(f"     Critical Issues: {exec_summary.get('critical_issues', 0)}")
            print(f"     Warning Issues: {exec_summary.get('warning_issues', 0)}")
            print(f"     Has AC Analysis: {exec_summary.get('has_ac_analysis', False)}")
            print(f"     Has Code Analysis: {exec_summary.get('has_code_analysis', False)}")
            
            # Check metadata
            metadata = results.get("metadata", {})
            print(f"\n   Metadata:")
            print(f"     Cost: ${metadata.get('cost_usd', 0):.4f}")
            print(f"     Duration: {metadata.get('duration_ms', 0)/1000:.2f}s")
            print(f"     API Duration: {metadata.get('api_duration_ms', 0)/1000:.2f}s")
            print(f"     Turns: {metadata.get('num_turns', 0)}")
            print(f"     Changed Files: {len(metadata.get('changed_files', []))}")
            
            # Save results
            with open(f"test_results_{session_id}.json", "w") as f:
                json.dump(results, f, indent=2)
            print(f"\n📄 Results saved to test_results_{session_id}.json")
            
        else:
            print(f"❌ Failed to get results: {results_data.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error fetching results: {e}")
        return
    
    # 5. Test Phase 3 tutorial
    print("\n5. Testing Phase 3 tutorial generation...")
    try:
        # Check if tutorial is available
        resp = requests.get(f"{API_BASE}/tutorial-status/{session_id}")
        status = resp.json()
        
        if status.get("tutorial_available"):
            print("✅ Tutorial already available")
        else:
            # Generate tutorial
            print("   Generating tutorial...")
            resp = requests.post(f"{API_BASE}/generate-tutorial/{session_id}")
            gen_data = resp.json()
            
            if gen_data["success"]:
                print("✅ Tutorial generation started")
                time.sleep(10)  # Wait for generation
                
                # Get tutorial
                resp = requests.get(f"{API_BASE}/tutorial/{session_id}")
                tutorial_data = resp.json()
                
                if tutorial_data["success"]:
                    tutorial = tutorial_data["tutorial"]
                    print("✅ Got tutorial:")
                    print(f"   Tutorial ID: {tutorial['tutorial_id']}")
                    print(f"   Sections: {list(tutorial.get('structured_sections', {}).keys())}")
                    print(f"   Mermaid Diagrams: {len(tutorial.get('mermaid_diagrams', []))}")
                    print(f"   Code Examples: {len(tutorial.get('code_examples', []))}")
    
    except Exception as e:
        print(f"⚠️ Tutorial test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced API Integration Test Complete!")

if __name__ == "__main__":
    print("Make sure the API server is running at http://localhost:5002")
    print("Press Enter to start the test...")
    input()
    
    test_enhanced_review()