import unittest
import tempfile
import json
from pathlib import Path
from core.services.worktree_config_service import WorktreeConfigService, WorktreeConfig

class TestWorktreeConfigService(unittest.TestCase):
    def setUp(self):
        # Use temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        self.service = WorktreeConfigService()
        # Override config file path for testing
        self.service.CONFIG_FILE = Path(self.temp_dir) / "test-config.json"
        self.service.ensure_config_dir()
    
    def test_get_default_config(self):
        """Test getting default configuration when no config exists"""
        config = self.service.get_config("test_user")
        
        self.assertEqual(config.user_id, "test_user")
        self.assertEqual(config.base_path, self.service.DEFAULT_PATH)
        self.assertFalse(config.is_valid)
    
    def test_save_and_load_config(self):
        """Test saving and loading configuration"""
        test_config = WorktreeConfig(
            base_path="/tmp/test",
            is_valid=True,
            last_validated="2024-01-01T00:00:00",
            user_id="test_user"
        )
        
        # Save config
        success = self.service.save_config(test_config)
        self.assertTrue(success)
        
        # Load config
        loaded_config = self.service.get_config("test_user")
        self.assertEqual(loaded_config.base_path, "/tmp/test")
        self.assertTrue(loaded_config.is_valid)
        self.assertEqual(loaded_config.user_id, "test_user")
    
    def test_validate_path_valid(self):
        """Test path validation with valid path"""
        is_valid, message = self.service.validate_path("/tmp")
        self.assertTrue(is_valid)
        self.assertIn("valid", message.lower())
    
    def test_validate_path_invalid(self):
        """Test path validation with invalid path"""
        is_valid, message = self.service.validate_path("/nonexistent/path")
        self.assertFalse(is_valid)
        self.assertIn("does not exist", message)
    
    def test_list_suggested_directories(self):
        """Test listing suggested directories"""
        suggestions = self.service.list_suggested_directories()
        self.assertIsInstance(suggestions, list)
        
        # Should contain at least some suggestions
        if suggestions:
            for suggestion in suggestions:
                self.assertIn("path", suggestion)
                self.assertIn("name", suggestion)
                # All suggested paths should exist
                self.assertTrue(Path(suggestion["path"]).exists())

if __name__ == '__main__':
    unittest.main()