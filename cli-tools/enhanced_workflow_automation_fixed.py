#!/usr/bin/env python3
"""
Enhanced PR Workflow Automation mit Jira Integration
Config-basierte Workflow Automation für PR Analysis und Enhanced Code Review.
"""

import json
import os
import sys
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union

# Import enhanced components
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'core'))
from jira_integration import JiraIntegration, JiraTicket
from enhanced_claude_reviewer import EnhancedClaudeReviewer
from core.utils.worktree_utils import get_configured_worktree_path, generate_worktree_path, ensure_worktree_base_directory

try:
    from bitbucket_pr_analyzer import BitbucketPRAnalyzer
except ImportError:
    print("❌ Fehler: bitbucket_pr_analyzer.py nicht gefunden")
    print("💡 Verwenden Sie das bestehende pr_workflow_automation.py als Fallback")
    sys.exit(1)


class EnhancedWorkflowAutomation:
    """Enhanced workflow automation with config-based operation"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.pr_config = config.get('pr_config', {})
        self.jira_config = config.get('jira_config', {})
        self.review_config = config.get('review_config', {})
        
        # Initialize components
        self.jira_integration = JiraIntegration(config)
        self.current_ticket = None
        self.worktree_path = None
        self.analyzer = None
        self.reviewer = None
        
        # Extract source information
        self.source_type = self.pr_config.get('source', {}).get('type', 'branch')
        self.source_value = self.pr_config.get('source', {}).get('value', '')
        self.pr_url = self.pr_config.get('source', {}).get('pr_url', '')
        self.repo_path = Path(self.pr_config.get('repository', {}).get('path', '.')).resolve()
        
        # Workflow options
        self.create_worktree = self.pr_config.get('repository', {}).get('worktree_options', {}).get('create', True)
        self.cleanup_after = self.pr_config.get('repository', {}).get('worktree_options', {}).get('cleanup_after', False)
        
        # Initialize worktree configuration
        self.worktree_base_path = get_configured_worktree_path()
        logger.info(f"📁 Worktree base path: {self.worktree_base_path}")
        
        # Ensure base directory is ready
        if not ensure_worktree_base_directory(self.worktree_base_path):
            logger.warning(f"⚠️ Worktree base directory may not be ready: {self.worktree_base_path}")
        
        print(f"🚀 Enhanced Workflow Automation initialisiert")
        print(f"📂 Repository: {self.repo_path}")
        print(f"🎯 Source: {self.source_type} = {self.source_value}")
        print(f"🎫 Jira Integration: {'✅ Aktiviert' if self.jira_integration.enabled else '❌ Deaktiviert'}")
    
    def run_full_workflow(self) -> Dict:
        """Run complete enhanced workflow"""
        
        print("\n" + "=" * 80)
        print("🎯 ENHANCED PR WORKFLOW GESTARTET")
        print("=" * 80)
        
        results = {
            "workflow_start": datetime.now().isoformat(),
            "steps": {},
            "ticket_info": None,
            "worktree_path": None,
            "success": False,
            "error": None
        }
        
        try:
            # Step 1: Ticket Discovery & Loading
            results["steps"]["ticket_discovery"] = self._step_ticket_discovery()
            
            # Step 2: PR Analysis & Worktree Setup
            results["steps"]["pr_analysis"] = self._step_pr_analysis()
            results["worktree_path"] = str(self.worktree_path) if self.worktree_path else None
            
            # Step 3: Enhanced Code Review
            results["steps"]["enhanced_review"] = self._step_enhanced_review()
            
            # Step 4: Report Generation & Summary
            results["steps"]["report_generation"] = self._step_report_generation()
            
            results["success"] = True
            results["workflow_end"] = datetime.now().isoformat()
            
            # Final Summary
            self._print_workflow_summary(results)
            
        except Exception as e:
            results["error"] = str(e)
            results["success"] = False
            print(f"\n❌ WORKFLOW FEHLER: {e}")
            
            # Cleanup on error
            if self.worktree_path and self.worktree_path.exists():
                self._cleanup_worktree(force=True)
        
        finally:
            # Optional cleanup
            if self.cleanup_after and self.worktree_path and self.worktree_path.exists():
                self._cleanup_worktree()
        
        return results
    
    def _step_ticket_discovery(self) -> Dict:
        """Step 1: Discover and load Jira ticket"""
        
        print("\n" + "=" * 80)
        print("🎫 SCHRITT 1: JIRA TICKET DISCOVERY")
        print("=" * 80)
        
        step_result = {
            "status": "success",
            "ticket_found": False,
            "ticket_id": None,
            "ac_count": 0,
            "message": ""
        }
        
        try:
            ticket_id = None
            
            # Extract ticket ID from branch name
            if self.source_type == 'branch' and self.source_value:
                ticket_id = self.jira_integration.extract_ticket_id_from_branch(self.source_value)
            elif self.source_type == 'pr_url' and self.pr_url:
                # Try to extract from PR URL or branch in PR
                print(f"🔍 Analyse PR URL für Ticket-Extraktion: {self.pr_url}")
                # This would need PR API integration to get branch name
                pass
            
            # Manual ticket ID from config
            manual_ticket = self.jira_config.get('ticket_extraction', {}).get('manual_ticket_id')
            if manual_ticket and not ticket_id:
                ticket_id = manual_ticket
                print(f"📋 Nutze manuell konfigurierte Ticket ID: {ticket_id}")
            
            if ticket_id:
                print(f"🎯 Lade Ticket: {ticket_id}")
                self.current_ticket = self.jira_integration.get_ticket(ticket_id)
                
                if self.current_ticket:
                    step_result["ticket_found"] = True
                    step_result["ticket_id"] = ticket_id
                    step_result["ac_count"] = self.current_ticket.get_acceptance_criteria_count()
                    step_result["message"] = f"Ticket erfolgreich geladen: {self.current_ticket.summary}"
                    
                    print(f"✅ Ticket geladen: {ticket_id}")
                    print(f"📝 Summary: {self.current_ticket.summary}")
                    print(f"📋 AC Count: {step_result['ac_count']}")
                    
                    if self.current_ticket.acceptance_criteria:
                        print(f"📋 Acceptance Criteria:")
                        for i, ac in enumerate(self.current_ticket.acceptance_criteria, 1):
                            preview = ac[:80] + "..." if len(ac) > 80 else ac
                            print(f"   {i}. {preview}")
                    else:
                        print(f"⚠️  Keine Acceptance Criteria definiert")
                else:
                    step_result["message"] = f"Ticket {ticket_id} nicht gefunden"
                    print(f"❌ Ticket {ticket_id} nicht gefunden")
            else:
                step_result["message"] = "Keine Ticket ID extrahiert"
                print(f"❌ Keine Ticket ID aus '{self.source_value}' extrahiert")
                
                if self.jira_integration.enabled:
                    print(f"💡 Stellen Sie sicher, dass der Branch-Name eine Ticket-ID enthält")
                    print(f"   Beispiel: feature/CMS20-1166-description")
                else:
                    print(f"💡 Jira Integration deaktiviert - erstellen Sie eine ticket.md Datei")
            
        except Exception as e:
            step_result["status"] = "error"
            step_result["message"] = str(e)
            print(f"❌ Ticket Discovery fehlgeschlagen: {e}")
        
        return step_result
    
    def _step_pr_analysis(self) -> Dict:
        """Step 2: PR Analysis and Worktree Setup"""
        
        print("\n" + "=" * 80)
        print("🔍 SCHRITT 2: PR ANALYSE & WORKTREE SETUP")
        print("=" * 80)
        
        step_result = {
            "status": "success",
            "worktree_created": False,
            "files_analyzed": 0,
            "analysis_output": "",
            "message": ""
        }
        
        try:
            # Initialize PR analyzer
            if self.source_type == 'branch':
                self.analyzer = BitbucketPRAnalyzer(
                    branch_name=self.source_value,
                    repo_path=str(self.repo_path),
                    use_worktree=self.create_worktree,
                    cleanup_worktree=False  # We handle cleanup ourselves
                )
            elif self.source_type == 'pr_url':
                self.analyzer = BitbucketPRAnalyzer(
                    pr_url=self.pr_url,
                    repo_path=str(self.repo_path),
                    use_worktree=self.create_worktree,
                    cleanup_worktree=False
                )
            else:
                raise Exception(f"Unsupported source type: {self.source_type}")
            
            # Run analysis
            print(f"🚀 Starte PR Analyse...")
            
            analysis_options = self.pr_config.get('analysis_options', {})
            self.analyzer.analyze(
                show_full_content=analysis_options.get('show_full_content', False),
                output_dir=analysis_options.get('output_dir')
            )
            
            # Check worktree status
            self.worktree_path = self.analyzer.worktree_path
            if self.worktree_path and self.worktree_path.exists():
                step_result["worktree_created"] = True
                step_result["message"] = f"Worktree erstellt: {self.worktree_path}"
                print(f"✅ Worktree verfügbar: {self.worktree_path}")
            else:
                step_result["message"] = "Worktree nicht erstellt - arbeite im Haupt-Repository"
                print(f"⚠️  Kein Worktree erstellt")
            
            step_result["analysis_output"] = "PR Analyse abgeschlossen"
            
        except Exception as e:
            step_result["status"] = "error"
            step_result["message"] = str(e)
            print(f"❌ PR Analyse fehlgeschlagen: {e}")
            raise
        
        return step_result
    
    def _step_enhanced_review(self) -> Dict:
        """Step 3: Enhanced Code Review with Jira Integration"""
        
        print("\n" + "=" * 80)
        print("🤖 SCHRITT 3: ENHANCED CODE REVIEW")
        print("=" * 80)
        
        step_result = {
            "status": "success",
            "review_type": "",
            "ac_analysis_included": False,
            "output_file": "",
            "message": ""
        }
        
        try:
            # Initialize enhanced reviewer
            self.reviewer = EnhancedClaudeReviewer(
                config=self.config,
                worktree_path=str(self.worktree_path) if self.worktree_path else None,
                repo_path=str(self.repo_path),
                pr_url=self.pr_url,
                branch_name=self.source_value if self.source_type == 'branch' else None
            )
            
            # Set ticket if we have one
            if self.current_ticket:
                self.reviewer.current_ticket = self.current_ticket
                step_result["ac_analysis_included"] = True
                print(f"🎫 Ticket für Review gesetzt: {self.current_ticket.ticket_id}")
            
            # Determine review type
            review_config = self.review_config
            review_type = review_config.get('type', 'comprehensive')
            focus_areas = review_config.get('focus_areas', [])
            
            # Map to actual review types
            if 'acceptance_criteria_compliance' in focus_areas:
                if self.current_ticket and self.current_ticket.acceptance_criteria:
                    review_type = 'acceptance_criteria_focused'
                else:
                    print(f"⚠️  AC Focus gewünscht, aber keine AC verfügbar")
                    review_type = 'comprehensive_with_ac'
            elif review_type == 'quick':
                review_type = 'comprehensive'  # Quick wird über separaten Parameter gesteuert
            elif review_type == 'comprehensive':
                review_type = 'comprehensive_with_ac'
            
            step_result["review_type"] = review_type
            
            # Output configuration
            output_config = review_config.get('output', {})
            output_file = output_config.get('file', 'enhanced_review_report.md')
            include_progress = output_config.get('include_live_progress', True)
            include_context = output_config.get('include_context', True)
            
            # Claude Code options
            claude_options = review_config.get('claude_code_options', {})
            
            print(f"🔍 Review Type: {review_type}")
            print(f"📊 Output: {output_file}")
            print(f"🎫 AC Analysis: {'✅ Ja' if step_result['ac_analysis_included'] else '❌ Nein'}")
            
            # Perform review
            if review_config.get('type') == 'quick':
                print(f"⚡ Führe Quick Enhanced Review durch...")
                result = self.reviewer.quick_enhanced_review(
                    focus_area=review_type if 'acceptance_criteria' in review_type else None,
                    show_progress=include_progress
                )
                step_result["output_file"] = "quick_enhanced_review_report.md"
            else:
                print(f"🚀 Führe vollständiges Enhanced Review durch...")
                result = self.reviewer.perform_enhanced_review(
                    review_type=review_type,
                    output_file=output_file,
                    include_context=include_context,
                    show_progress=include_progress
                )
                step_result["output_file"] = output_file
            
            step_result["message"] = "Enhanced Review erfolgreich abgeschlossen"
            print(f"✅ Enhanced Review abgeschlossen")
            
        except Exception as e:
            step_result["status"] = "error"
            step_result["message"] = str(e)
            print(f"❌ Enhanced Review fehlgeschlagen: {e}")
            raise
        
        return step_result
    
    def _step_report_generation(self) -> Dict:
        """Step 4: Final Report Generation and Summary"""
        
        print("\n" + "=" * 80)
        print("📊 SCHRITT 4: REPORT GENERATION & SUMMARY")
        print("=" * 80)
        
        step_result = {
            "status": "success",
            "reports_generated": [],
            "message": ""
        }
        
        try:
            # Main report was already generated in review step
            main_report = self.review_config.get('output', {}).get('file', 'enhanced_review_report.md')
            working_path = self.worktree_path if self.worktree_path else self.repo_path
            main_report_path = working_path / main_report
            
            if main_report_path.exists():
                step_result["reports_generated"].append(main_report)
                print(f"📋 Haupt-Report: {main_report}")
            
            # Generate workflow summary report
            summary_report = self._generate_workflow_summary_report()
            if summary_report:
                step_result["reports_generated"].append(str(summary_report))
                print(f"📈 Workflow-Summary: {summary_report}")
            
            step_result["message"] = f"{len(step_result['reports_generated'])} Reports generiert"
            
        except Exception as e:
            step_result["status"] = "error" 
            step_result["message"] = str(e)
            print(f"❌ Report Generation fehlgeschlagen: {e}")
        
        return step_result
    
    def _generate_workflow_summary_report(self) -> Optional[Path]:
        """Generate comprehensive workflow summary report"""
        
        working_path = self.worktree_path if self.worktree_path else self.repo_path
        summary_path = working_path / "workflow_summary.md"
        
        try:
            # Collect all information
            summary_content = f"""# Enhanced Workflow Summary Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Workflow Type:** Enhanced PR Review mit Jira Integration  
**Repository:** {self.repo_path}  

---

## 📋 Workflow Configuration

### Source Information
- **Type:** {self.source_type}
- **Value:** {self.source_value}
- **PR URL:** {self.pr_url or 'N/A'}

### Repository Settings
- **Path:** {self.repo_path}
- **Worktree Created:** {'✅ Yes' if self.worktree_path else '❌ No'}
- **Worktree Path:** {self.worktree_path or 'N/A'}

### Jira Integration
- **Enabled:** {'✅ Yes' if self.jira_integration.enabled else '❌ No'}
- **Server:** {self.jira_integration.server_url or 'N/A'}
- **Ticket Loaded:** {'✅ Yes' if self.current_ticket else '❌ No'}

"""
            
            # Ticket information
            if self.current_ticket:
                ticket = self.current_ticket
                summary_content += f"""
## 🎫 Jira Ticket Details

- **ID:** [{ticket.ticket_id}]({self.jira_integration.server_url}/browse/{ticket.ticket_id})
- **Summary:** {ticket.summary}
- **Type:** {ticket.issue_type}
- **Status:** {ticket.status}
- **Priority:** {ticket.priority}
- **Assignee:** {ticket.assignee}

### Acceptance Criteria ({len(ticket.acceptance_criteria)} Items)
"""
                
                if ticket.acceptance_criteria:
                    for i, ac in enumerate(ticket.acceptance_criteria, 1):
                        summary_content += f"{i}. {ac}\n"
                else:
                    summary_content += "❌ Keine Acceptance Criteria definiert\n"
            else:
                summary_content += f"""
## 🎫 Jira Ticket Details

❌ **Kein Ticket geladen**

**Mögliche Gründe:**
- Branch-Name enthält keine Ticket-ID
- Jira API nicht konfiguriert
- Ticket existiert nicht
- Keine lokale ticket.md Datei

**Empfehlungen:**
- Branch-Namen mit Ticket-ID verwenden (z.B. `feature/CMS20-1166-description`)
- Jira API Token konfigurieren
- Lokale `ticket.md` Datei erstellen
"""
            
            # Review configuration
            summary_content += f"""
---

## 🔍 Review Configuration

- **Review Type:** {self.review_config.get('type', 'comprehensive')}
- **Focus Areas:** {', '.join(self.review_config.get('focus_areas', []))}
- **AC Analysis:** {'✅ Included' if self.current_ticket and self.current_ticket.acceptance_criteria else '❌ Not Available'}

### Output Files
"""
            
            # List generated files
            output_dir = self.worktree_path if self.worktree_path else self.repo_path
            report_files = [
                'enhanced_review_report.md',
                'quick_enhanced_review_report.md', 
                'review_report.md'
            ]
            
            for report_file in report_files:
                report_path = output_dir / report_file
                if report_path.exists():
                    file_size = report_path.stat().st_size
                    summary_content += f"- ✅ `{report_file}` ({file_size} bytes)\n"
            
            # Next steps
            summary_content += f"""
---

## 🚀 Next Steps

### If AC Issues Found:
1. **Review AC Compliance** in main report
2. **Address Critical Issues** before merging
3. **Clarify Requirements** with Product Owner if needed
4. **Update Implementation** to match AC
5. **Re-run Enhanced Review** after fixes

### Standard Process:
1. Open and review main report: `{self.review_config.get('output', {}).get('file', 'enhanced_review_report.md')}`
2. Address identified issues
3. Update tests and documentation
4. Request team review

---

*Enhanced Workflow Summary generiert von Enhanced PR Workflow Automation*
"""
            
            # Write summary
            with open(summary_path, 'w', encoding='utf-8') as f:
                f.write(summary_content)
            
            return summary_path
            
        except Exception as e:
            print(f"⚠️  Konnte Workflow Summary nicht erstellen: {e}")
            return None
    
    def _print_workflow_summary(self, results: Dict):
        """Print final workflow summary"""
        
        print("\n" + "=" * 80)
        print("🎉 ENHANCED WORKFLOW ABGESCHLOSSEN")
        print("=" * 80)
        
        print(f"\n📊 Workflow Results:")
        print(f"   ✅ Success: {results['success']}")
        print(f"   🎫 Ticket Loaded: {results['steps'].get('ticket_discovery', {}).get('ticket_found', False)}")
        print(f"   📂 Worktree Created: {results['steps'].get('pr_analysis', {}).get('worktree_created', False)}")
        print(f"   🤖 Enhanced Review: {results['steps'].get('enhanced_review', {}).get('status') == 'success'}")
        
        if self.current_ticket:
            ac_count = self.current_ticket.get_acceptance_criteria_count()
            print(f"\n🎫 Ticket Analysis:")
            print(f"   📋 Ticket: {self.current_ticket.ticket_id} - {self.current_ticket.summary}")
            print(f"   📝 AC Count: {ac_count}")
            if ac_count > 0:
                print(f"   🎯 AC Analysis: ✅ Included in Review")
            else:
                print(f"   ⚠️  AC Analysis: ❌ No AC defined")
        
        if self.worktree_path:
            print(f"\n📁 Worktree Information:")
            print(f"   📂 Path: {self.worktree_path}")
            print(f"   🗑️  Cleanup: {'Auto' if self.cleanup_after else 'Manual'}")
            if not self.cleanup_after:
                print(f"   💡 Manual cleanup: git worktree remove {self.worktree_path}")
        
        print(f"\n📋 Generated Reports:")
        report_files = results.get('steps', {}).get('report_generation', {}).get('reports_generated', [])
        for report in report_files:
            print(f"   📄 {report}")
        
        if not results['success'] and results.get('error'):
            print(f"\n❌ Error: {results['error']}")
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. Review main report")
        print(f"   2. Address AC compliance issues (if any)")
        print(f"   3. Implement recommended fixes")
        print(f"   4. Re-run review after changes")
    
    def _cleanup_worktree(self, force: bool = False):
        """Cleanup worktree"""
        if self.worktree_path and self.worktree_path.exists():
            try:
                cmd = ["git", "worktree", "remove", str(self.worktree_path)]
                if force:
                    cmd.append("--force")
                
                subprocess.run(cmd, cwd=self.repo_path, check=True)
                print(f"✅ Worktree aufgeräumt: {self.worktree_path}")
                self.worktree_path = None
            except subprocess.CalledProcessError as e:
                if not force:
                    print(f"⚠️  Worktree Cleanup fehlgeschlagen, versuche --force: {e}")
                    self._cleanup_worktree(force=True)
                else:
                    print(f"❌ Worktree Cleanup fehlgeschlagen: {e}")
                    print(f"💡 Manuell aufräumen: git worktree remove --force {self.worktree_path}")
    
    def run_analysis_only(self) -> Dict:
        """Run only PR analysis step"""
        print("🎯 Führe nur PR Analyse durch...")
        
        results = {
            "analysis_only": True,
            "ticket_discovery": self._step_ticket_discovery(),
            "pr_analysis": self._step_pr_analysis()
        }
        
        return results
    
    def run_review_only(self, worktree_path: str = None) -> Dict:
        """Run only enhanced review step"""
        print("🎯 Führe nur Enhanced Review durch...")
        
        if worktree_path:
            self.worktree_path = Path(worktree_path)
        
        results = {
            "review_only": True,
            "ticket_discovery": self._step_ticket_discovery(),
            "enhanced_review": self._step_enhanced_review(),
            "report_generation": self._step_report_generation()
        }
        
        return results


def create_sample_config(output_path: str = "pr_review_config.json") -> Path:
    """Create sample configuration file"""
    
    sample_config = {
        "pr_config": {
            "source": {
                "type": "branch",
                "value": "feature/CMS20-1166-autorefresh-von-luftqualitaet",
                "pr_url": "https://bitbucket.org/rma-digital/rma-mono/pull-requests/342"
            },
            "repository": {
                "path": "/Users/<USER>/dev/rma-mono",
                "worktree_options": {
                    "create": True,
                    "cleanup_after": False
                }
            },
            "analysis_options": {
                "show_full_content": False,
                "output_dir": None
            }
        },
        
        "jira_config": {
            "enabled": True,
            "server_url": "https://your-company.atlassian.net",
            "credentials": {
                "email": "<EMAIL>",
                "api_token": "${JIRA_API_TOKEN}"
            },
            "ticket_extraction": {
                "auto_extract_from_branch": True,
                "branch_patterns": [
                    "feature/([A-Z]+-\\d+)",
                    "bugfix/([A-Z]+-\\d+)",
                    "hotfix/([A-Z]+-\\d+)"
                ],
                "manual_ticket_id": None,
                "manual_ticket_file": None
            },
            "acceptance_criteria_fields": [
                "customfield_10020",
                "customfield_10021"
            ]
        },
        
        "review_config": {
            "type": "comprehensive",
            "focus_areas": [
                "acceptance_criteria_compliance",
                "code_quality",
                "security",
                "testing"
            ],
            "output": {
                "file": "enhanced_review_report.md",
                "include_live_progress": True,
                "include_context": True
            },
            "claude_code_options": {
                "max_turns": 8,
                "timeout_seconds": 900,
                "tools_allowed": ["View", "Edit", "Grep", "Bash"],
                "enhanced_thinking": True
            }
        },
        
        "integrations": {
            "slack": {
                "enabled": False,
                "webhook_url": "${SLACK_WEBHOOK_URL}",
                "notify_on_completion": True,
                "notify_on_critical_issues": True
            },
            "email": {
                "enabled": False,
                "smtp_server": "smtp.company.com",
                "smtp_port": 587,
                "username": "${EMAIL_USER}",
                "password": "${EMAIL_PASS}",
                "recipients": ["<EMAIL>"]
            }
        }
    }
    
    config_path = Path(output_path)
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(sample_config, f, indent=2, ensure_ascii=False)
    
    print(f"📝 Sample Config erstellt: {config_path}")
    print(f"💡 Bearbeiten Sie die Datei und passen Sie an Ihre Umgebung an:")
    print(f"   - Repository path")
    print(f"   - Branch/PR information")
    print(f"   - Jira server URL und credentials")
    print(f"   - Review focus areas")
    
    return config_path


def load_config_with_validation(config_path: str) -> Dict:
    """Load and validate configuration file"""
    try:
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Basic validation
        required_sections = ['pr_config', 'review_config']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            raise ValueError(f"Missing required config sections: {missing_sections}")
        
        # Validate pr_config
        pr_config = config.get('pr_config', {})
        source = pr_config.get('source', {})
        
        if not source.get('type') or not source.get('value'):
            if not source.get('pr_url'):
                raise ValueError("pr_config.source must have 'type'+'value' or 'pr_url'")
        
        print(f"✅ Config geladen und validiert: {config_path}")
        return config
        
    except Exception as e:
        print(f"❌ Config Fehler: {e}")
        raise


def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(
        description='Enhanced PR Workflow Automation mit Jira Integration',
        epilog="""
Beispiele:
  # Vollständiger Workflow
  python enhanced_workflow_automation_fixed.py --config pr_review_config.json

  # Nur PR Analyse
  python enhanced_workflow_automation_fixed.py --config config.json --analysis-only

  # Nur Review (mit existierendem Worktree)
  python enhanced_workflow_automation_fixed.py --config config.json --review-only --worktree /tmp/pr_analysis_xyz

  # Quick Review Workflow
  python enhanced_workflow_automation_fixed.py --config config.json --quick

  # Sample Config erstellen
  python enhanced_workflow_automation_fixed.py --create-sample-config my_config.json

  # Sample Tickets erstellen
  python enhanced_workflow_automation_fixed.py --create-sample-tickets
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Configuration
    parser.add_argument('--config', '-c',
                       help='Pfad zur JSON-Konfigurationsdatei')
    
    # Workflow modes
    parser.add_argument('--analysis-only', action='store_true',
                       help='Führt nur PR-Analyse durch (erstellt Worktree)')
    
    parser.add_argument('--review-only', action='store_true',
                       help='Führt nur Enhanced Review durch')
    
    parser.add_argument('--quick', action='store_true',
                       help='Quick Enhanced Review Workflow')
    
    # Override options
    parser.add_argument('--worktree',
                       help='Pfad zu existierendem Worktree (für --review-only)')
    
    parser.add_argument('--branch',
                       help='Branch Name (überschreibt Config)')
    
    parser.add_argument('--pr-url',
                       help='PR URL (überschreibt Config)')
    
    # Utility operations
    parser.add_argument('--create-sample-config',
                       help='Erstellt Sample-Config-Datei')
    
    parser.add_argument('--create-sample-tickets', action='store_true',
                       help='Erstellt Sample-Ticket-Dateien')
    
    parser.add_argument('--validate-config', action='store_true',
                       help='Validiert Konfiguration ohne Ausführung')
    
    parser.add_argument('--test-jira', action='store_true',
                       help='Testet Jira-Verbindung')
    
    parser.add_argument('--cleanup-worktree',
                       help='Räumt spezifischen Worktree auf')
    
    args = parser.parse_args()
    
    try:
        # Utility operations
        if args.create_sample_config:
            create_sample_config(args.create_sample_config)
            return
        
        if args.create_sample_tickets:
            from jira_integration import JiraIntegration
            
            # Create sample tickets in different formats
            sample_config = {"jira_config": {"enabled": False}}
            jira = JiraIntegration(sample_config)
            
            jira.create_sample_ticket_file("CMS20-1166", "md")
            jira.create_sample_ticket_file("CMS20-1166", "json")
            jira.create_sample_ticket_file("CMS20-1166", "yaml")
            
            print(f"📝 Sample Tickets erstellt in ./tickets/")
            return
        
        if args.cleanup_worktree:
            try:
                worktree_path = Path(args.cleanup_worktree)
                cmd = ["git", "worktree", "remove", "--force", str(worktree_path)]
                subprocess.run(cmd, check=True)
                print(f"✅ Worktree aufgeräumt: {worktree_path}")
            except Exception as e:
                print(f"❌ Cleanup fehlgeschlagen: {e}")
            return
        
        # Main operations require config
        if not args.config:
            parser.error("--config ist erforderlich für Workflow-Operationen")
        
        # Load and validate config
        config = load_config_with_validation(args.config)
        
        # Apply CLI overrides
        if args.branch:
            config['pr_config']['source']['type'] = 'branch'
            config['pr_config']['source']['value'] = args.branch
        
        if args.pr_url:
            config['pr_config']['source']['pr_url'] = args.pr_url
        
        if args.quick:
            config['review_config']['type'] = 'quick'
        
        # Store config file reference for reports
        setattr(EnhancedWorkflowAutomation, '_config_file', args.config)
        
        # Test operations
        if args.validate_config:
            print("✅ Konfiguration ist valide")
            return
        
        if args.test_jira:
            from jira_integration import JiraIntegration
            jira = JiraIntegration(config)
            validation = jira.validate_config()
            print(f"\n📊 Jira Test Results:")
            for key, value in validation.items():
                status = "✅" if value else "❌"
                print(f"   {status} {key}")
            return
        
        # Initialize workflow
        workflow = EnhancedWorkflowAutomation(config)
        
        # Run workflow
        if args.analysis_only:
            results = workflow.run_analysis_only()
        elif args.review_only:
            results = workflow.run_review_only(args.worktree)
        else:
            results = workflow.run_full_workflow()
        
        # Exit with appropriate code
        sys.exit(0 if results.get('success', False) else 1)
        
    except Exception as e:
        print(f"❌ Enhanced Workflow Fehler: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
