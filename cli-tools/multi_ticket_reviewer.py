#!/usr/bin/env python3
"""
Multi-Ticket Parallel Review System
Führt Reviews für mehrere Tickets/Branches gleichzeitig durch.
"""

import asyncio
import subprocess
import json
import sys
import time
import threading
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
# Add path to utils directory for advanced_ticket_discovery
import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))
from advanced_ticket_discovery import AdvancedTicketDiscovery, TicketMapping

class MultiTicketReviewer:
    """Parallel review system for multiple tickets/branches"""
    
    def __init__(self, config_file="pr_review_config.json", max_parallel=3, repo_path=None):
        self.config_file = config_file
        self.max_parallel = max_parallel

        # Get repo path from frontend configuration if not provided
        if repo_path is None:
            try:
                import sys
                sys.path.append(str(Path(__file__).parent.parent))
                from core.services.worktree_config_service import get_worktree_config
                config_data = get_worktree_config("default")
                if config_data.get("base_path") and config_data.get("is_valid"):
                    repo_path = config_data["base_path"]
                    print(f"📁 Using frontend configured repo path: {repo_path}")
                else:
                    raise ValueError("No valid worktree configuration found")
            except Exception as e:
                print(f"❌ Failed to get repo path from frontend config: {e}")
                raise ValueError("repo_path is required. Please configure in Settings → Git Worktree.")

        self.repo_path = repo_path
        self.results = {}
        self.start_time = None
        
        # Initialize ticket discovery system
        self.ticket_discovery = AdvancedTicketDiscovery(repo_path)
        # Load base config
        try:
            with open(config_file, 'r') as f:
                self.base_config = json.load(f)
            print(f"✅ Base config geladen: {config_file}")
        except Exception as e:
            print(f"❌ Config Fehler: {e}")
            raise
    
    def create_ticket_config(self, branch_name: str, ticket_id: str = None) -> str:
        """Create individual config file for each ticket"""
        
        # Copy base config
        ticket_config = self.base_config.copy()
        
        # Update for specific ticket
        ticket_config['pr_config']['source']['value'] = branch_name
        if ticket_id:
            ticket_config['jira_config']['ticket_extraction']['manual_ticket_id'] = ticket_id
        
        # Create unique config file
        safe_branch = branch_name.replace('/', '_').replace('-', '_')
        config_path = f"config_{safe_branch}_{int(time.time())}.json"
        
        with open(config_path, 'w') as f:
            json.dump(ticket_config, f, indent=2)
        
        return config_path
    
    def review_single_ticket(self, branch_name: str, review_mode: str = "quick", 
                           ticket_id: str = None) -> Dict:
        """Review a single ticket/branch"""
        
        start_time = time.time()
        
        # Generate unique report filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_branch_name = branch_name.replace('/', '_').replace('-', '_')
        safe_ticket_id = (ticket_id or 'unknown').replace('-', '_')
        report_filename = f"review_{safe_ticket_id}_{safe_branch_name}_{timestamp}.md"
        report_path = f"reports/{report_filename}"
        
        result = {
            'branch': branch_name,
            'ticket_id': ticket_id,
            'review_mode': review_mode,
            'status': 'running',
            'start_time': start_time,
            'end_time': None,
            'duration': None,
            'output_file': report_path,
            'error': None,
            'process_id': None
        }
        
        try:
            print(f"🚀 Starting review for: {branch_name} (mode: {review_mode})")
            
            # Create individual config
            config_path = self.create_ticket_config(branch_name, ticket_id)
            
            # Build command
            cmd = [
                'venv/bin/python', 'core/enhanced_claude_reviewer.py',
                '--config', config_path,
                '--branch', branch_name,
                '--output', report_path
            ]
            
            if review_mode == 'quick':
                cmd.append('--quick')
            elif review_mode == 'ac_only':
                cmd.append('--ac-only')
            elif review_mode == 'bug_analysis':
                cmd.append('--bug-analysis')
            # full mode = no additional flags
            
            # Execute review
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes max per review
            )
            
            result['process_id'] = None  # subprocess.run() doesn't provide PID
            
            if process.returncode == 0:
                result['status'] = 'success'
                # output_file already set above
                print(f"✅ Review completed for: {branch_name} → {report_path}")
            else:
                result['status'] = 'failed'
                result['error'] = f"Process failed: {process.stderr}"
                print(f"❌ Review failed for: {branch_name}")
                
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['error'] = "Review timed out after 30 minutes"
            print(f"⏰ Review timed out for: {branch_name}")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            print(f"❌ Review error for {branch_name}: {e}")
        
        finally:
            # Cleanup config file
            try:
                if 'config_path' in locals():
                    Path(config_path).unlink()
            except:
                pass
            
            result['end_time'] = time.time()
            result['duration'] = result['end_time'] - result['start_time']
        
        return result
    
    def review_multiple_tickets_smart(self, branches: List[str], review_mode: str = "quick", 
                                     auto_discover: bool = True, create_missing: bool = False) -> Dict:
        """
        Smart review of multiple branches with automatic ticket discovery
        
        Args:
            branches: List of branch names
            review_mode: 'quick', 'full', 'ac_only', 'bug_analysis'
            auto_discover: Automatically discover tickets for branches
            create_missing: Create sample tickets for branches without tickets
        """
        
        self.start_time = time.time()
        
        print(f"🎯 Smart Multi-Ticket Review gestartet")
        print(f"📊 Branches: {len(branches)}")
        print(f"⚡ Mode: {review_mode}")
        print(f"🔄 Max Parallel: {self.max_parallel}")
        print(f"🔍 Auto-Discovery: {'✅ Enabled' if auto_discover else '❌ Disabled'}")
        print("=" * 60)
        
        # Step 1: Discover ticket mappings
        mappings = []
        if auto_discover:
            print(f"🔍 Step 1: Auto-discovering tickets...")
            mappings = self.ticket_discovery.discover_ticket_mappings(branches)
            
            # Create missing tickets if requested
            if create_missing:
                print(f"📝 Creating sample tickets for unmapped branches...")
                created = self.ticket_discovery.create_sample_tickets(mappings)
                if created:
                    print(f"✅ Created {len(created)} sample tickets")
                    # Re-discover after creating samples
                    mappings = self.ticket_discovery.discover_ticket_mappings(branches)
            
            # Generate mapping report
            mapping_report = self.ticket_discovery.generate_mapping_report(mappings)
            print(f"📊 Ticket mapping report: {mapping_report}")
            
        else:
            # Simple mapping without discovery
            mappings = [TicketMapping(branch=branch, ticket_id=None, mapping_method="none", confidence=0.0) 
                       for branch in branches]
        
        # Step 2: Convert mappings to ticket format
        tickets = []
        for mapping in mappings:
            ticket_dict = {'branch': mapping.branch}
            if mapping.ticket_id:
                ticket_dict['ticket_id'] = mapping.ticket_id
            if mapping.ticket_file:
                ticket_dict['ticket_file'] = mapping.ticket_file
            tickets.append(ticket_dict)
        
        # Step 3: Run parallel reviews with discovered tickets
        print(f"\n🚀 Step 2: Running parallel reviews...")
        return self._run_parallel_reviews_with_mappings(tickets, mappings, review_mode)
    
    def _run_parallel_reviews_with_mappings(self, tickets: List[Dict], mappings: List[TicketMapping], 
                                          review_mode: str) -> Dict:
        """
        Run parallel reviews using ticket mappings for enhanced reporting
        """
        # Parallel execution
        with ThreadPoolExecutor(max_workers=self.max_parallel) as executor:
            # Submit all jobs
            future_to_ticket = {
                executor.submit(
                    self.review_single_ticket_with_mapping, 
                    ticket, 
                    review_mode,
                    next((m for m in mappings if m.branch == ticket['branch']), None)
                ): ticket for ticket in tickets
            }
            
            # Collect results as they complete
            results = []
            for future in as_completed(future_to_ticket):
                ticket = future_to_ticket[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Enhanced progress update with ticket info
                    completed = len(results)
                    total = len(tickets)
                    percentage = (completed / total) * 100
                    
                    mapping = next((m for m in mappings if m.branch == ticket['branch']), None)
                    ticket_info = f"({mapping.ticket_id})" if mapping and mapping.ticket_id else "(no ticket)"
                    
                    print(f"📈 Progress: {completed}/{total} ({percentage:.1f}%) - {result['branch']} {ticket_info}: {result['status']}")
                    
                except Exception as e:
                    print(f"❌ Exception for {ticket['branch']}: {e}")
                    results.append({
                        'branch': ticket['branch'],
                        'status': 'exception',
                        'error': str(e)
                    })
        
        # Generate enhanced summary with mapping info
        summary = self._generate_summary(results)
        
        # Create enhanced combined report
        combined_report = self._create_combined_report(results, summary)
        
        return {
            'summary': summary,
            'results': results,
            'mappings': mappings,
            'combined_report': combined_report,
            'total_duration': time.time() - self.start_time
        }
    
    def review_single_ticket_with_mapping(self, ticket_dict: Dict, review_mode: str, 
                                        mapping: Optional[TicketMapping] = None) -> Dict:
        """Review a single ticket with enhanced mapping information"""
        
        branch = ticket_dict['branch']
        ticket_id = ticket_dict.get('ticket_id') or (mapping.ticket_id if mapping else None)
        ticket_file = ticket_dict.get('ticket_file') or (mapping.ticket_file if mapping else None)
        
        start_time = time.time()
        
        # Generate unique report filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        safe_branch_name = branch.replace('/', '_').replace('-', '_')
        safe_ticket_id = (ticket_id or 'unknown').replace('-', '_')
        report_filename = f"review_{safe_ticket_id}_{safe_branch_name}_{timestamp}.md"
        report_path = f"reports/{report_filename}"
        
        result = {
            'branch': branch,
            'ticket_id': ticket_id,
            'ticket_file': ticket_file,
            'mapping_method': mapping.mapping_method if mapping else 'manual',
            'mapping_confidence': mapping.confidence if mapping else 1.0,
            'review_mode': review_mode,
            'status': 'running',
            'start_time': start_time,
            'end_time': None,
            'duration': None,
            'output_file': report_path,
            'error': None,
            'process_id': None
        }
        
        try:
            ticket_info = f" (ticket: {ticket_id})" if ticket_id else " (no ticket)"
            print(f"🚀 Starting review for: {branch}{ticket_info} (mode: {review_mode})")
            
            # Create individual config with ticket mapping
            config_path = self._create_enhanced_config(branch, ticket_id, ticket_file)
            
            # Build command
            cmd = [
                'venv/bin/python', 'core/enhanced_claude_reviewer.py',
                '--config', config_path,
                '--branch', branch,
                '--output', report_path
            ]
            
            if review_mode == 'quick':
                cmd.append('--quick')
            elif review_mode == 'ac_only':
                cmd.append('--ac-only')
            elif review_mode == 'bug_analysis':
                cmd.append('--bug-analysis')
            # full mode = no additional flags
            
            # Execute review
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes max per review
            )
            
            result['process_id'] = None  # subprocess.run() doesn't provide PID
            
            if process.returncode == 0:
                result['status'] = 'success'
                # output_file already set above
                print(f"✅ Review completed for: {branch} → {report_path}")
            else:
                result['status'] = 'failed'
                result['error'] = f"Process failed: {process.stderr}"
                print(f"❌ Review failed for: {branch}")
                
        except subprocess.TimeoutExpired:
            result['status'] = 'timeout'
            result['error'] = "Review timed out after 30 minutes"
            print(f"⏰ Review timed out for: {branch}")
            
        except Exception as e:
            result['status'] = 'error'
            result['error'] = str(e)
            print(f"❌ Review error for {branch}: {e}")
        
        finally:
            # Cleanup config file
            try:
                if 'config_path' in locals():
                    Path(config_path).unlink()
            except:
                pass
            
            result['end_time'] = time.time()
            result['duration'] = result['end_time'] - result['start_time']
        
        return result
    
    def _create_enhanced_config(self, branch_name: str, ticket_id: str = None, 
                              ticket_file: str = None) -> str:
        """Create enhanced config with ticket mapping information"""
        
        # Copy base config
        ticket_config = self.base_config.copy()
        
        # Update for specific ticket
        ticket_config['pr_config']['source']['value'] = branch_name
        
        if ticket_id:
            ticket_config['jira_config']['ticket_extraction']['manual_ticket_id'] = ticket_id
        
        if ticket_file:
            ticket_config['jira_config']['ticket_extraction']['manual_ticket_file'] = ticket_file
        
        # Create unique config file
        safe_branch = branch_name.replace('/', '_').replace('-', '_')
        config_path = f"config_{safe_branch}_{int(time.time())}.json"
        
        with open(config_path, 'w') as f:
            json.dump(ticket_config, f, indent=2)
        
        return config_path
    
    def _generate_summary(self, results: List[Dict]) -> Dict:
        """Generate summary statistics"""
        
        total = len(results)
        successful = len([r for r in results if r['status'] == 'success'])
        failed = len([r for r in results if r['status'] == 'failed'])
        timeout = len([r for r in results if r['status'] == 'timeout'])
        error = len([r for r in results if r['status'] == 'error'])
        
        total_duration = sum([r.get('duration', 0) for r in results if r.get('duration')])
        avg_duration = total_duration / len([r for r in results if r.get('duration')]) if results else 0
        
        return {
            'total_tickets': total,
            'successful': successful,
            'failed': failed,
            'timeout': timeout,
            'error': error,
            'success_rate': (successful / total * 100) if total > 0 else 0,
            'total_duration': total_duration,
            'average_duration': avg_duration,
            'parallel_efficiency': total_duration / (time.time() - self.start_time) if self.start_time else 0
        }
    
    def _create_combined_report(self, results: List[Dict], summary: Dict) -> str:
        """Create combined report for all reviews"""
        
        report_path = f"multi_ticket_review_report_{int(time.time())}.md"
        
        report_content = f"""# Multi-Ticket Parallel Review Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Duration:** {summary['total_duration']:.1f} seconds
**Parallel Efficiency:** {summary['parallel_efficiency']:.1f}x

---

## Executive Summary

- **Total Tickets:** {summary['total_tickets']}
- **Successful Reviews:** {summary['successful']} ({summary['success_rate']:.1f}%)
- **Failed Reviews:** {summary['failed']}
- **Timeouts:** {summary['timeout']}
- **Errors:** {summary['error']}
- **Average Duration:** {summary['average_duration']:.1f} seconds per review

---

## Individual Results

"""
        
        # Sort results by status (success first)
        sorted_results = sorted(results, key=lambda x: (x['status'] != 'success', x['branch']))
        
        for result in sorted_results:
            status_emoji = {
                'success': '✅',
                'failed': '❌', 
                'timeout': '⏰',
                'error': '💥'
            }.get(result['status'], '❓')
            
            duration_str = f"{result.get('duration', 0):.1f}s" if result.get('duration') else "N/A"
            
            report_content += f"""### {status_emoji} {result['branch']}

- **Status:** {result['status'].upper()}
- **Duration:** {duration_str}
- **Ticket ID:** {result.get('ticket_id', 'Auto-detected')}
- **Output File:** {result.get('output_file', 'None')}
"""
            
            if result.get('error'):
                report_content += f"- **Error:** {result['error']}\n"
            
            report_content += "\n"
        
        report_content += f"""---

## Performance Analysis

### Parallel Execution Benefits
- **Sequential Time (estimated):** {summary['total_duration']:.1f} seconds
- **Actual Time:** {time.time() - self.start_time:.1f} seconds  
- **Time Saved:** {summary['total_duration'] - (time.time() - self.start_time):.1f} seconds
- **Efficiency Gain:** {summary['parallel_efficiency']:.1f}x faster

### Recommendations

"""
        
        if summary['success_rate'] >= 80:
            report_content += "✅ **High Success Rate** - Parallel execution working well\n"
        else:
            report_content += "⚠️ **Low Success Rate** - Consider investigating common issues\n"
            
        if summary['parallel_efficiency'] >= 2:
            report_content += "🚀 **Good Parallel Efficiency** - Multiple CPUs utilized well\n"
        else:
            report_content += "🐌 **Low Parallel Efficiency** - Consider reducing max_parallel\n"
        
        report_content += f"""
### Failed Reviews
"""
        
        failed_reviews = [r for r in results if r['status'] != 'success']
        if failed_reviews:
            for failed in failed_reviews:
                report_content += f"- **{failed['branch']}:** {failed.get('error', 'Unknown error')}\n"
        else:
            report_content += "None - All reviews successful! 🎉\n"
        
        report_content += f"""
---

*Generated by Multi-Ticket Parallel Review System*
"""
        
        # Write report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📊 Combined report created: {report_path}")
        
        return report_path


def parse_ticket_list(ticket_input: str) -> List[Dict]:
    """Parse ticket input string into list of ticket dictionaries"""
    
    tickets = []
    
    # Support multiple formats:
    # 1. Simple: "branch1,branch2,branch3"
    # 2. With tickets: "branch1:ticket1,branch2:ticket2"
    # 3. JSON: '[{"branch":"branch1","ticket_id":"ticket1"}]'
    
    if ticket_input.startswith('['):
        # JSON format
        try:
            tickets = json.loads(ticket_input)
        except:
            print(f"❌ Invalid JSON format: {ticket_input}")
            return []
    else:
        # Simple comma-separated format
        for item in ticket_input.split(','):
            item = item.strip()
            if ':' in item:
                # branch:ticket format
                branch, ticket_id = item.split(':', 1)
                tickets.append({'branch': branch.strip(), 'ticket_id': ticket_id.strip()})
            else:
                # just branch
                tickets.append({'branch': item})
    
    return tickets


def main():
    """CLI interface for multi-ticket review"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description='Multi-Ticket Parallel Review System',
        epilog="""
Examples:
  # Quick review for multiple branches
  python multi_ticket_reviewer.py --tickets "feature/branch1,feature/branch2,feature/branch3"
  
  # Full review with specific tickets  
  python multi_ticket_reviewer.py --tickets "branch1:CMS20-1166,branch2:CMS20-1167" --mode full
  
  # Bug analysis for multiple branches
  python multi_ticket_reviewer.py --tickets "branch1,branch2" --mode bug_analysis --parallel 2
  
  # JSON format
  python multi_ticket_reviewer.py --tickets '[{"branch":"feature/branch1","ticket_id":"CMS20-1166"}]'
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--tickets', '-t', required=True,
                       help='Comma-separated list of branches or JSON format')
    
    parser.add_argument('--mode', '-m', 
                       choices=['quick', 'full', 'ac_only', 'bug_analysis'],
                       default='quick',
                       help='Review mode for all tickets')
    
    parser.add_argument('--parallel', '-p', type=int, default=3,
                       help='Maximum parallel reviews (default: 3)')
    
    parser.add_argument('--config', '-c', default='pr_review_config.json',
                       help='Base config file')
    
    parser.add_argument('--auto-discover', action='store_true', default=True,
                       help='Auto-discover tickets for branches (default: True)')
    
    parser.add_argument('--no-discovery', action='store_true',
                       help='Disable automatic ticket discovery')
    
    parser.add_argument('--create-missing', action='store_true',
                       help='Create sample tickets for branches without tickets')
    
    parser.add_argument('--repo', '-r', default='/Users/<USER>/dev/rma-mono',
                       help='Repository path for ticket discovery')
    
    args = parser.parse_args()
    
    # Parse tickets
    if args.tickets.startswith('['):
        # JSON format - use legacy method for backward compatibility
        tickets = parse_ticket_list(args.tickets)
        auto_discover = False  # JSON format implies manual ticket specification
    else:
        # Simple branch list - use smart discovery
        branches = [branch.strip() for branch in args.tickets.split(',')]
        auto_discover = args.auto_discover and not args.no_discovery
        tickets = branches  # Will be processed by smart review method
    
    if not tickets:
        print("❌ No valid tickets/branches found")
        sys.exit(1)
    
    # Determine discovery mode
    if isinstance(tickets[0], str):
        # Simple branch list - use smart method
        print(f"🎯 Found {len(tickets)} branches for smart review:")
        for branch in tickets:
            print(f"   🌿 {branch}")
        
        use_smart_review = True
    else:
        # Legacy ticket format
        print(f"🎯 Found {len(tickets)} tickets for review:")
        for ticket in tickets:
            print(f"   📋 {ticket['branch']}" + (f" (ID: {ticket['ticket_id']})" if ticket.get('ticket_id') else ""))
        
        use_smart_review = False
    
    # Initialize reviewer
    try:
        reviewer = MultiTicketReviewer(
            config_file=args.config,
            max_parallel=args.parallel,
            repo_path=args.repo
        )
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        sys.exit(1)
    
    # Run reviews based on mode
    try:
        if use_smart_review:
            # Smart review with auto-discovery
            results = reviewer.review_multiple_tickets_smart(
                branches=tickets,
                review_mode=args.mode,
                auto_discover=auto_discover,
                create_missing=args.create_missing
            )
        else:
            # Legacy method for backward compatibility
            results = reviewer.review_multiple_tickets(tickets, args.mode)
        
        print(f"\n🎉 Multi-Ticket Review Complete!")
        print(f"📊 Summary:")
        print(f"   ✅ Successful: {results['summary']['successful']}/{results['summary']['total_tickets']}")
        print(f"   ⚡ Success Rate: {results['summary']['success_rate']:.1f}%") 
        print(f"   ⏱️  Total Duration: {results['summary']['total_duration']:.1f}s")
        print(f"   🚀 Efficiency: {results['summary']['parallel_efficiency']:.1f}x")
        print(f"   📄 Report: {results['combined_report']}")
        
        # Exit code based on success rate
        if results['summary']['success_rate'] >= 80:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Multi-ticket review failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
