#!/usr/bin/env python3
"""
Bitbucket PR Analyzer mit Git Worktree Unterstützung
Analysiert einen Bitbucket Pull Request und zeigt die Änderungen lokal über Git-Befehle an.
"""

import subprocess
import re
import os
import sys
import argparse
import tempfile
import shutil
from urllib.parse import urlparse
from pathlib import Path


class BitbucketPRAnalyzer:
    def __init__(self, pr_url=None, branch_name=None, repo_path=".", use_worktree=True, cleanup_worktree=False, master_repo_path=None):
        self.repo_path = Path(repo_path).resolve()
        self.use_worktree = use_worktree
        self.cleanup_worktree = cleanup_worktree
        self.worktree_path = None
        self.working_path = self.repo_path
        self.master_repo_path = master_repo_path  # Store the master repo path from frontend
        
        # Bestimme PR URL und Branch
        if pr_url and branch_name:
            self.pr_url = pr_url
            self.branch_name = branch_name
        elif pr_url:
            self.pr_url = pr_url
            self.branch_name = None
        elif branch_name:
            self.branch_name = branch_name
            self.pr_url = self._extract_pr_url_from_branch(branch_name)
        else:
            raise ValueError("Entweder PR URL oder Branch Name muss angegeben werden")
        
        if self.pr_url:
            self.repo_info = self._parse_pr_url()
        else:
            self.repo_info = None
        
        # Get configured worktree path
        self.worktree_base_path = self._get_configured_worktree_path()
            
    def _branch_exists(self, branch_name):
        """Prüft ob ein Branch existiert (lokal oder remote)"""
        try:
            # Prüfe lokale Branches
            self._run_git_command(f"git show-ref --verify --quiet refs/heads/{branch_name}")
            return True
        except:
            try:
                # Prüfe remote Branches
                self._run_git_command(f"git show-ref --verify --quiet refs/remotes/origin/{branch_name}")
                return True
            except:
                try:
                    # Prüfe ob es ein valider commit/branch ist
                    self._run_git_command(f"git rev-parse --verify {branch_name}")
                    return True
                except:
                    return False

    def _extract_pr_url_from_branch(self, branch_name):
        """
        Versucht die PR URL aus dem Branch Namen zu extrahieren
        """
        print(f"Versuche PR URL aus Branch '{branch_name}' zu extrahieren...")
        
        # Hol branch Informationen
        self._run_git_command("git fetch --all")
        
        # Prüfe ob Branch existiert
        if not self._branch_exists(branch_name):
            print(f"⚠️  Branch '{branch_name}' existiert nicht lokal oder remote")
            print("💡 Verfügbare Branches:")
            try:
                all_branches = self._run_git_command("git branch -a").split('\n')
                # Zeige nur die ersten 10 Branches
                for branch in all_branches[:10]:
                    if branch.strip():
                        print(f"   - {branch.strip()}")
                if len(all_branches) > 10:
                    print(f"   ... und {len(all_branches) - 10} weitere")
            except:
                pass
        
        # Methode 1: Branch name patterns
        pr_patterns = [
            r'pr[/-]?(\d+)',
            r'pull[/-]?request[/-]?(\d+)',
            r'feature[/-]?pr[/-]?(\d+)',
            r'hotfix[/-]?pr[/-]?(\d+)',
        ]
        
        for pattern in pr_patterns:
            match = re.search(pattern, branch_name, re.IGNORECASE)
            if match:
                pr_id = match.group(1)
                print(f"PR ID {pr_id} aus Branch Name extrahiert")
                
                # Versuche Repository Info aus remote URL zu bekommen
                repo_info = self._extract_repo_info_from_git()
                if repo_info:
                    pr_url = f"https://bitbucket.org/{repo_info['workspace']}/{repo_info['repository']}/pull-requests/{pr_id}"
                    print(f"Generierte PR URL: {pr_url}")
                    return pr_url
        
        # Methode 2: Suche in Commit Messages (nur wenn Branch existiert)
        if self._branch_exists(branch_name):
            try:
                # Letzte 10 Commits des Branches durchsuchen
                commits = self._run_git_command(f"git log {branch_name} --oneline -10").split('\n')
                for commit in commits:
                    match = re.search(r'#(\d+)', commit)
                    if match:
                        pr_id = match.group(1)
                        print(f"PR ID {pr_id} aus Commit Message extrahiert")
                        
                        repo_info = self._extract_repo_info_from_git()
                        if repo_info:
                            pr_url = f"https://bitbucket.org/{repo_info['workspace']}/{repo_info['repository']}/pull-requests/{pr_id}"
                            print(f"Generierte PR URL: {pr_url}")
                            return pr_url
            except Exception as e:
                print(f"Warnung: Konnte Commit Messages nicht durchsuchen: {e}")
        
        # Methode 3: Branch description (nur wenn Branch existiert)
        if self._branch_exists(branch_name):
            try:
                # Versuche branch description zu lesen
                branch_info = self._run_git_command(f"git config branch.{branch_name}.description")
                if branch_info:
                    match = re.search(r'pull-requests?[/-](\d+)', branch_info, re.IGNORECASE)
                    if match:
                        pr_id = match.group(1)
                        print(f"PR ID {pr_id} aus Branch Description extrahiert")
                        
                        repo_info = self._extract_repo_info_from_git()
                        if repo_info:
                            pr_url = f"https://bitbucket.org/{repo_info['workspace']}/{repo_info['repository']}/pull-requests/{pr_id}"
                            print(f"Generierte PR URL: {pr_url}")
                            return pr_url
            except Exception:
                pass  # Branch description ist optional
        
        print("❌ Konnte PR URL nicht automatisch extrahieren")
        print("Tipp: Verwenden Sie das --pr-url Argument um die PR URL manuell anzugeben")
        return None
    
    def _extract_repo_info_from_git(self):
        """Extrahiert Repository Informationen aus Git remote URL"""
        try:
            remote_url = self._run_git_command("git remote get-url origin")
            
            # SSH Format: *****************:workspace/repository.git
            ssh_match = re.match(r'git@bitbucket\.org:([^/]+)/([^.]+)(?:\.git)?', remote_url)
            if ssh_match:
                return {
                    'workspace': ssh_match.group(1),
                    'repository': ssh_match.group(2)
                }
            
            # HTTPS Format: https://bitbucket.org/workspace/repository.git
            https_match = re.match(r'https://bitbucket\.org/([^/]+)/([^.]+)(?:\.git)?', remote_url)
            if https_match:
                return {
                    'workspace': https_match.group(1),
                    'repository': https_match.group(2)
                }
                
        except Exception as e:
            print(f"Warnung: Konnte Repository Info nicht extrahieren: {e}")
        
        return None
        
    def _parse_pr_url(self):
        """
        Parst die PR URL und extrahiert Repository-Informationen
        Beispiel: https://bitbucket.org/workspace/repository/pull-requests/123
        """
        pattern = r'https://bitbucket\.org/([^/]+)/([^/]+)/pull-requests/(\d+)'
        match = re.match(pattern, self.pr_url)
        
        if not match:
            raise ValueError(f"Ungültige Bitbucket PR URL: {self.pr_url}")
            
        return {
            'workspace': match.group(1),
            'repository': match.group(2),
            'pr_id': match.group(3)
        }
    
    def _run_git_command(self, command):
        """Führt einen Git-Befehl aus und gibt das Ergebnis zurück"""
        try:
            # Convert string command to list for sqafer execution
            if isinstance(command, str):
                import shlex
                command_list = shlex.split(command)
            else:
                command_list = command

            result = subprocess.run(
                command_list,
                shell=False,
                cwd=self.working_path,
                capture_output=True, 
                text=True, 
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            raise Exception(f"Git-Befehl fehlgeschlagen: {' '.join(command_list) if isinstance(command_list, list) else command}\nFehler: {e.stderr}")
    
    def _get_configured_worktree_path(self) -> str:
        """Get configured worktree base path from frontend or config file"""
        # Use master_repo_path from frontend if provided
        if self.master_repo_path:
            # Create worktrees in ../worktrees/ relative to master repo
            master_path = Path(self.master_repo_path)
            worktree_base = master_path.parent / "worktrees"
            print(f"📁 Using frontend configured worktree path: {worktree_base}")
            return str(worktree_base)
        
        # Fallback: Try to get from config file (for backward compatibility)
        try:
            import sys
            sys.path.append(str(Path(__file__).parent.parent))
            from core.config import get_configured_worktree_base_path
            
            path = get_configured_worktree_base_path()
            print(f"📁 Using config file worktree path: {path}")
            return path
        except Exception as e:
            print(f"⚠️  No worktree configuration found: {e}")
            print("💡 Please configure master repository path in Settings → Git Worktree")
            raise ValueError("No worktree configuration available. Please configure in Settings → Git Worktree.")
    
    def _setup_worktree(self):
        """Erstellt einen Git Worktree für den Branch"""
        if not self.use_worktree:
            return
            
        # Bestimme Branch Name
        if self.branch_name:
            branch = self.branch_name
        else:
            # Versuche Branch aus PR zu finden
            branch = self._find_pr_branch()
        
        if not branch:
            print("⚠️  Kein Branch gefunden, arbeite im Haupt-Repository")
            return
        
        # Setup Git Worktree with configured path
        self.worktree_path = self.setup_worktree(branch)
        
        if self.worktree_path:
            self.working_path = self.worktree_path
            print(f"✅ Worktree erfolgreich erstellt: {self.worktree_path}")
        else:
            print("❌ Worktree konnte nicht erstellt werden")
            return
        
        # Fetch um sicherzugehen dass wir den neuesten Stand haben
        subprocess.run(["git", "fetch", "--all"], cwd=self.repo_path, check=True)
        
        # Prüfe ob bereits ein Worktree für diesen Branch existiert
        try:
            worktree_list = subprocess.run(
                ["git", "worktree", "list", "--porcelain"],
                cwd=self.repo_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
            
            # Suche nach existierendem Worktree für diesen Branch
            existing_worktree = None
            lines = worktree_list.strip().split('\n')
            current_worktree_path = None
            
            for line in lines:
                if line.startswith('worktree '):
                    current_worktree_path = line.replace('worktree ', '')
                elif line.startswith('branch '):
                    current_branch = line.replace('branch ', '').replace('refs/heads/', '')
                    if current_branch == branch and current_worktree_path:
                        existing_worktree = current_worktree_path
                        break
            
            if existing_worktree:
                existing_path = Path(existing_worktree)
                target_dir = Path(self.worktree_base_path)
                
                # Nur verwenden wenn der existierende Worktree im gewünschten Verzeichnis ist
                if existing_path.parent == target_dir:
                    print(f"♻️  Verwende existierenden Worktree: {existing_worktree}")
                    self.worktree_path = existing_path
                    self.working_path = self.worktree_path
                    print(f"✅ Existierender Worktree gefunden für Branch '{branch}'")
                    return
                else:
                    print(f"🗑️  Entferne alten Worktree aus falschem Verzeichnis: {existing_worktree}")
                    try:
                        subprocess.run([
                            "git", "worktree", "remove", 
                            str(existing_path), "--force"
                        ], cwd=self.repo_path, check=True)
                    except subprocess.CalledProcessError as e:
                        print(f"⚠️  Konnte alten Worktree nicht entfernen: {e}")
                        # Fallback: Manuell löschen falls das Verzeichnis noch existiert
                        if existing_path.exists():
                            shutil.rmtree(existing_path, ignore_errors=True)
            
        except subprocess.CalledProcessError:
            pass  # Worktree list fehlgeschlagen, versuche normalen Weg
        
        # Create new worktree
        try:
            subprocess.run([
                "git", "worktree", "add", 
                str(self.worktree_path), 
                branch
            ], cwd=self.repo_path, check=True)
            
            print(f"✅ Worktree erfolgreich erstellt: {self.worktree_path}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Fehler beim Erstellen des Worktree: {e}")
            print(f"💡 Tipp: Eventuell existiert bereits ein Worktree für Branch '{branch}'")
            print(f"   Führen Sie 'git worktree list' aus um alle Worktrees zu sehen")
            print(f"   Oder: 'git worktree remove <pfad>' um einen alten Worktree zu entfernen")
            # Entferne das Verzeichnis falls es erstellt wurde aber git worktree add fehlschlug
            if self.worktree_path and self.worktree_path.exists():
                shutil.rmtree(self.worktree_path, ignore_errors=True)
            self.worktree_path = None
    
    def setup_worktree(self, branch):
        """Setup Git Worktree with configured path"""
        # Use configured base path instead of hardcoded
        target_dir = Path(self.worktree_base_path)
        
        # Ensure target directory exists
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate worktree name (same pattern as before)
        branch_short = branch[:10] if len(branch) > 10 else branch
        branch_safe = re.sub(r'[^\w\-_]', '-', branch_short)
        worktree_name = f"{branch_safe}-review"
        
        self.worktree_path = target_dir / worktree_name
        
        print(f"Erstelle Git Worktree für Branch '{branch}'...")
        print(f"Worktree Pfad: {self.worktree_path}")
        
        # Rest of existing worktree setup logic remains the same
        if self.worktree_path.exists():
            print(f"⚠️ Worktree bereits vorhanden: {self.worktree_path}")
            if self.cleanup_worktree:
                print("🧹 Bereinige existierenden Worktree...")
                self.cleanup_existing_worktree()
            else:
                print("✅ Verwende existierenden Worktree")
                return str(self.worktree_path)
        
        # Create new worktree
        try:
            subprocess.run([
                'git', 'worktree', 'add', 
                str(self.worktree_path), 
                branch
            ], cwd=self.repo_path, check=True, capture_output=True, text=True)
            
            print(f"✅ Worktree erfolgreich erstellt: {self.worktree_path}")
            return str(self.worktree_path)
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Fehler beim Erstellen des Worktree: {e}")
            print(f"Stderr: {e.stderr}")
            raise
    
    def _cleanup_worktree(self):
        """Räumt den Worktree auf"""
        if self.worktree_path and self.cleanup_worktree:
            print(f"Räume Worktree auf: {self.worktree_path}")
            try:
                # Entferne Worktree
                subprocess.run([
                    "git", "worktree", "remove", 
                    str(self.worktree_path)
                ], cwd=self.repo_path, check=True)
            except subprocess.CalledProcessError:
                # Fallback: Manuell löschen
                shutil.rmtree(self.worktree_path, ignore_errors=True)
            
            self.worktree_path = None
    
    def _find_pr_branch(self):
        """Findet den Branch für den PR"""
        if not self.repo_info:
            return None
            
        # Erst mal alle remote branches holen
        self._run_git_command("git fetch --all")
        
        # Versuche den PR branch zu finden
        branches = self._run_git_command("git branch -r").split('\n')
        
        # Suche nach PR branch patterns
        pr_branch_patterns = [
            f"origin/pull-request-{self.repo_info['pr_id']}",
            f"origin/pr-{self.repo_info['pr_id']}",
            f"origin/pr/{self.repo_info['pr_id']}",
            f"pull-request-{self.repo_info['pr_id']}",
            f"pr-{self.repo_info['pr_id']}",
            f"pr/{self.repo_info['pr_id']}",
        ]
        
        for pattern in pr_branch_patterns:
            for branch in branches:
                if pattern in branch.strip():
                    return branch.strip()
        
        return None
    
    def _get_pr_commits(self):
        """
        Holt die Commit-Informationen für den PR
        Verwendet git log um die Commits zwischen source und target branch zu finden
        """
        # Bestimme source branch
        if self.branch_name:
            source_branch = self.branch_name
        else:
            source_branch = self._find_pr_branch()
            
        if not source_branch:
            raise Exception("Konnte Source Branch nicht bestimmen")
            
        # Prüfe ob Branch existiert
        if not self._branch_exists(source_branch):
            raise Exception(f"Branch '{source_branch}' existiert nicht im Repository")
        
        # Normalisiere branch name (entferne origin/ prefix falls vorhanden)
        if source_branch.startswith('origin/'):
            local_branch = source_branch.replace('origin/', '')
        else:
            local_branch = source_branch
            source_branch = f"origin/{source_branch}" if not source_branch.startswith('origin/') else source_branch
        
        # Target branch (meist main oder master)
        branches = self._run_git_command("git branch -r").split('\n')
        target_branch = "origin/main"
        if not any("origin/main" in branch for branch in branches):
            target_branch = "origin/master"
            if not any("origin/master" in branch for branch in branches):
                target_branch = "origin/develop"
        
        print(f"Analysiere PR: {source_branch} -> {target_branch}")
        
        # Hole die Commit-Range
        merge_base = self._run_git_command(f"git merge-base {target_branch} {source_branch}")
        
        return {
            'source_branch': source_branch,
            'target_branch': target_branch,
            'merge_base': merge_base,
            'source_commit': self._run_git_command(f"git rev-parse {source_branch}")
        }
    
    def _get_changed_files(self, commit_info):
        """Holt die Liste der geänderten Files"""
        # Zeige nur die Files die zwischen merge-base und source branch geändert wurden
        diff_command = f"git diff --name-status {commit_info['merge_base']}..{commit_info['source_commit']}"
        diff_output = self._run_git_command(diff_command)
        
        changed_files = []
        for line in diff_output.split('\n'):
            if line.strip():
                parts = line.split('\t')
                status = parts[0]
                filename = parts[1] if len(parts) > 1 else ""
                
                # Handle renamed files (R100 oldname -> newname)
                if status.startswith('R'):
                    old_name = parts[1]
                    new_name = parts[2]
                    filename = new_name
                    
                changed_files.append({
                    'status': status,
                    'filename': filename,
                    'old_filename': old_name if status.startswith('R') else filename
                })
        
        return changed_files
    
    def _get_file_content(self, filename, commit):
        """Holt den Inhalt einer Datei zu einem bestimmten Commit"""
        try:
            return self._run_git_command(f"git show {commit}:{filename}")
        except Exception:
            return None  # Datei existiert in diesem Commit nicht
    
    def _show_diff_for_file(self, filename, commit_info):
        """Zeigt den Diff für eine einzelne Datei"""
        diff_command = f"git diff {commit_info['merge_base']}..{commit_info['source_commit']} -- {filename}"
        return self._run_git_command(diff_command)
    
    def analyze(self, show_full_content=False, output_dir=None):
        """
        Hauptanalyse-Funktion
        
        Args:
            show_full_content: Zeigt den vollständigen Dateiinhalt vor/nach Änderungen
            output_dir: Speichert die Dateien in einem Verzeichnis
        """
        try:
            # Setup Worktree
            self._setup_worktree()
            
            print(f"Analysiere Bitbucket PR...")
            if self.pr_url:
                print(f"PR URL: {self.pr_url}")
            if self.repo_info:
                print(f"Repository: {self.repo_info['workspace']}/{self.repo_info['repository']}")
                print(f"PR ID: {self.repo_info['pr_id']}")
            if self.branch_name:
                print(f"Branch: {self.branch_name}")
            print(f"Repository Pfad: {self.repo_path}")
            if self.worktree_path:
                print(f"Worktree Pfad: {self.worktree_path}")
            print("-" * 80)
            
            # Hole PR Informationen
            commit_info = self._get_pr_commits()
            
            # Hole geänderte Files
            changed_files = self._get_changed_files(commit_info)
            
            print(f"\nGeänderte Dateien ({len(changed_files)}):")
            for file_info in changed_files:
                status_desc = {
                    'M': 'Modified',
                    'A': 'Added', 
                    'D': 'Deleted',
                    'R': 'Renamed'
                }.get(file_info['status'][0], file_info['status'])
                
                print(f"  [{status_desc}] {file_info['filename']}")
            
            print("\n" + "=" * 80)
            
            # Analysiere jede Datei
            for i, file_info in enumerate(changed_files, 1):
                filename = file_info['filename']
                status = file_info['status']
                
                print(f"\n[{i}/{len(changed_files)}] Datei: {filename}")
                print(f"Status: {status}")
                print("-" * 40)
                
                if status.startswith('D'):
                    print("❌ Datei wurde gelöscht")
                    if show_full_content:
                        old_content = self._get_file_content(filename, commit_info['merge_base'])
                        if old_content:
                            print("\nInhalt vor Löschung:")
                            print(old_content)
                elif status.startswith('A'):
                    print("✅ Neue Datei hinzugefügt")
                    if show_full_content:
                        new_content = self._get_file_content(filename, commit_info['source_commit'])
                        if new_content:
                            print("\nNeuer Dateiinhalt:")
                            print(new_content)
                else:
                    # Zeige Diff
                    diff = self._show_diff_for_file(filename, commit_info)
                    if diff:
                        print("\nÄnderungen (Diff):")
                        print(diff)
                    else:
                        print("Keine Diff-Informationen verfügbar")
                    
                    if show_full_content:
                        print("\n" + "-" * 20 + " VORHER " + "-" * 20)
                        old_content = self._get_file_content(filename, commit_info['merge_base'])
                        print(old_content if old_content else "Datei existierte nicht")
                        
                        print("\n" + "-" * 20 + " NACHHER " + "-" * 20)
                        new_content = self._get_file_content(filename, commit_info['source_commit'])
                        print(new_content if new_content else "Datei wurde gelöscht")
                
                # Speichere Dateien falls output_dir angegeben
                if output_dir:
                    self._save_file_versions(filename, commit_info, output_dir, status)
            
            print(f"\n{'='*80}")
            print("Analyse abgeschlossen!")
            
            if self.worktree_path and not self.cleanup_worktree:
                print(f"\n📁 Worktree wurde beibehalten: {self.worktree_path}")
                print("💡 Für Code Review mit Claude Code verwenden Sie:")
                print(f"   cd {self.worktree_path}")
                print("   claude")
                print("\n🗑️  Zum späteren Aufräumen:")
                print(f"   git worktree remove {self.worktree_path}")
            
        finally:
            # Cleanup Worktree nur falls gewünscht
            if self.cleanup_worktree:
                self._cleanup_worktree()
        
    def _save_file_versions(self, filename, commit_info, output_dir, status):
        """Speichert die Datei-Versionen vor und nach den Änderungen"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Erstelle Unterordner für diese Datei
        safe_filename = filename.replace('/', '_').replace('\\', '_')
        file_dir = output_path / safe_filename
        file_dir.mkdir(exist_ok=True)
        
        if not status.startswith('A'):  # Nicht für neue Dateien
            old_content = self._get_file_content(filename, commit_info['merge_base'])
            if old_content:
                with open(file_dir / "before.txt", 'w', encoding='utf-8') as f:
                    f.write(old_content)
        
        if not status.startswith('D'):  # Nicht für gelöschte Dateien
            new_content = self._get_file_content(filename, commit_info['source_commit'])
            if new_content:
                with open(file_dir / "after.txt", 'w', encoding='utf-8') as f:
                    f.write(new_content)
        
        # Speichere auch den Diff
        diff = self._show_diff_for_file(filename, commit_info)
        if diff:
            with open(file_dir / "diff.patch", 'w', encoding='utf-8') as f:
                f.write(diff)


def main():
    parser = argparse.ArgumentParser(description='Analysiert einen Bitbucket Pull Request')
    
    # Hauptargumente - entweder PR URL oder Branch
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--pr-url', '-u', help='Bitbucket PR URL')
    group.add_argument('--branch', '-b', help='Git Branch Name (versucht PR URL automatisch zu extrahieren)')
    
    # Kombination: Branch + PR URL
    parser.add_argument('--pr-url-fallback', help='PR URL als Fallback wenn automatische Extraktion fehlschlägt')
    
    # Weitere Optionen
    parser.add_argument('--repo-path', '-r', default='.', 
                       help='Pfad zum lokalen Git Repository (Standard: aktuelles Verzeichnis)')
    parser.add_argument('--full-content', '-f', action='store_true',
                       help='Zeigt vollständigen Dateiinhalt vor/nach Änderungen')
    parser.add_argument('--output-dir', '-o', 
                       help='Speichert Datei-Versionen in diesem Verzeichnis')
    parser.add_argument('--no-worktree', action='store_true',
                       help='Verwendet keinen Git Worktree (arbeitet im Haupt-Repository)')
    parser.add_argument('--cleanup-worktree', action='store_true',
                       help='Räumt den Worktree nach der Analyse auf (Standard: behält Worktree)')
    parser.add_argument('--keep-worktree', action='store_true',
                       help='Behält den Worktree nach der Analyse (Standard-Verhalten, für Kompatibilität)')
    
    # Positional argument support für Rückwärtskompatibilität
    parser.add_argument('url_or_branch', nargs='?', 
                       help='PR URL oder Branch Name (Positional Argument)')
    
    args = parser.parse_args()
    
    # Bestimme PR URL und Branch aus Argumenten
    pr_url = args.pr_url
    branch_name = args.branch
    
    # Unterstütze positional argument
    if args.url_or_branch:
        if args.url_or_branch.startswith('http'):
            pr_url = args.url_or_branch
        else:
            branch_name = args.url_or_branch
    
    # Fallback URL
    if branch_name and args.pr_url_fallback:
        pr_url = args.pr_url_fallback
    
    # Validierung
    if not pr_url and not branch_name:
        parser.error("Entweder --pr-url, --branch oder positional argument muss angegeben werden")
    
    try:
        analyzer = BitbucketPRAnalyzer(
            pr_url=pr_url,
            branch_name=branch_name,
            repo_path=args.repo_path,
            use_worktree=not args.no_worktree,
            cleanup_worktree=args.cleanup_worktree
        )
        analyzer.analyze(
            show_full_content=args.full_content, 
            output_dir=args.output_dir
        )
    except Exception as e:
        print(f"❌ Fehler: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
