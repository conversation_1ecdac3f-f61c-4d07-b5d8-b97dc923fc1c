#!/usr/bin/env python3
"""
PR Workflow Automation
Kombiniert Bitbucket PR Analyzer und Claude Code Reviewer für einen kompletten Workflow:
1. Analysiert PR und erstellt Worktree
2. Führt automatisches Code Review durch
3. Erstellt umfassenden Report
"""

import subprocess
import sys
import argparse
import json
from pathlib import Path
import tempfile
import os

# Import der anderen Scripts als Module
try:
    from bitbucket_pr_analyzer import BitbucketPR<PERSON><PERSON>yzer
    # Add path to core directory for claude_code_reviewer
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'core'))
    from claude_code_reviewer import ClaudeCodeReviewer
except ImportError:
    # Fallback: Versuche Scripts im gleichen Verzeichnis zu finden
    import importlib.util
    
    def import_script(script_name):
        script_path = Path(__file__).parent / f"{script_name}.py"
        if not script_path.exists():
            raise ImportError(f"Script {script_name}.py nicht gefunden in {Path(__file__).parent}")
        
        spec = importlib.util.spec_from_file_location(script_name, script_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    
    analyzer_module = import_script("bitbucket_pr_analyzer")
    reviewer_module = import_script("claude_code_reviewer")
    
    BitbucketPRAnalyzer = analyzer_module.BitbucketPRAnalyzer
    ClaudeCodeReviewer = reviewer_module.ClaudeCodeReviewer


class PRWorkflowAutomation:
    def __init__(self, pr_url=None, branch_name=None, repo_path=".", verbose=True):
        self.pr_url = pr_url
        self.branch_name = branch_name
        self.repo_path = Path(repo_path).resolve()
        self.verbose = verbose
        self.worktree_path = None
        
        # Pfade zu den anderen Scripts (für Fallback-Imports)
        script_dir = Path(__file__).parent
    
    def _validate_scripts(self):
        """Überprüft ob die benötigten Scripts verfügbar sind"""
        missing_scripts = []
        
        if not self.analyzer_script.exists():
            missing_scripts.append(str(self.analyzer_script))
        
        if not self.reviewer_script.exists():
            missing_scripts.append(str(self.reviewer_script))
        
        if missing_scripts:
            raise Exception(
                f"❌ Benötigte Scripts fehlen:\n" +
                "\n".join(f"  - {script}" for script in missing_scripts) +
                f"\n\nStellen Sie sicher, dass alle Scripts im gleichen Verzeichnis sind: {Path(__file__).parent}"
            )
        
        print("✅ Alle benötigten Scripts gefunden")
    
    def _run_command(self, cmd, description, capture_output=True):
        """Führt einen Befehl aus und gibt das Ergebnis zurück"""
        if self.verbose:
            print(f"\n🚀 {description}")
            if isinstance(cmd, list):
                print(f"📝 Befehl: {' '.join(cmd)}")
            else:
                print(f"📝 Befehl: {cmd}")
        
        try:
            if capture_output:
                result = subprocess.run(
                    cmd,
                    cwd=self.repo_path,
                    capture_output=True,
                    text=True,
                    check=True
                )
                return result.stdout.strip(), result.stderr.strip()
            else:
                # Für interaktive Commands
                result = subprocess.run(
                    cmd,
                    cwd=self.repo_path,
                    check=True
                )
                return None, None
                
        except subprocess.CalledProcessError as e:
            error_msg = f"❌ {description} fehlgeschlagen (Exit Code: {e.returncode})"
            if e.stderr:
                error_msg += f"\nStderr: {e.stderr}"
            if e.stdout:
                error_msg += f"\nStdout: {e.stdout}"
            raise Exception(error_msg)
    
    def _extract_worktree_path(self, analyzer_output):
        """Extrahiert den Worktree-Pfad aus der Analyzer-Ausgabe"""
        lines = analyzer_output.split('\n')
        
        for line in lines:
            if "Worktree wurde beibehalten:" in line:
                # Extrahiere Pfad nach dem Doppelpunkt
                parts = line.split(":", 1)
                if len(parts) > 1:
                    path = parts[1].strip()
                    return Path(path)
            
            elif "Worktree Pfad:" in line:
                # Alternative Extraktion
                parts = line.split(":", 1)
                if len(parts) > 1:
                    path = parts[1].strip()
                    return Path(path)
        
        return None
    
    def run_pr_analysis(self, analysis_options=None):
        """
        Führt die PR-Analyse durch
        
        Args:
            analysis_options: Zusätzliche Optionen für den Analyzer
        """
        
        print("=" * 80)
        print("🔍 SCHRITT 1: PR ANALYSE")
        print("=" * 80)
        
        try:
            # Erstelle Analyzer-Instanz
            analyzer = BitbucketPRAnalyzer(
                pr_url=self.pr_url,
                branch_name=self.branch_name,
                repo_path=str(self.repo_path),
                use_worktree=not (analysis_options and analysis_options.get("no_worktree", False)),
                cleanup_worktree=False  # Standardmäßig nicht aufräumen
            )
            
            # Führe Analyse durch
            analyzer.analyze(
                show_full_content=analysis_options and analysis_options.get("full_content", False),
                output_dir=analysis_options and analysis_options.get("output_dir")
            )
            
            # Hole Worktree-Pfad
            self.worktree_path = analyzer.worktree_path
            
            if self.worktree_path and self.worktree_path.exists():
                print(f"✅ Worktree identifiziert: {self.worktree_path}")
            else:
                print("⚠️  Kein Worktree erstellt (--no-worktree verwendet)")
            
            return "Analyse erfolgreich abgeschlossen"
            
        except Exception as e:
            raise Exception(f"PR Analyse fehlgeschlagen: {e}")
    
    def run_code_review(self, review_options=None):
        """
        Führt das Code Review durch
        
        Args:
            review_options: Konfiguration für das Review
        """
        
        print("\n" + "=" * 80)
        print("🤖 SCHRITT 2: CODE REVIEW")
        print("=" * 80)
        
        try:
            # Erstelle Reviewer-Instanz
            reviewer = ClaudeCodeReviewer(
                worktree_path=str(self.worktree_path) if self.worktree_path else None,
                repo_path=str(self.repo_path),
                pr_url=self.pr_url,
                branch_name=self.branch_name
            )
            
            # Bestimme Review-Parameter
            show_progress = not (review_options and review_options.get("no_progress", False))
            
            if review_options and review_options.get("quick"):
                # Quick Review
                focus_area = review_options.get("focus")
                show_progress = not (review_options and review_options.get("no_progress", False))
                
                # Check if show_progress parameter is supported
                import inspect
                quick_review_params = inspect.signature(reviewer.quick_review).parameters
                
                if 'show_progress' in quick_review_params:
                    reviewer.quick_review(focus_area=focus_area, show_progress=show_progress)
                else:
                    reviewer.quick_review(focus_area=focus_area)
            else:
                # Vollständiges Review
                review_types = review_options.get("review_types") if review_options else None
                # Fix: Sichere Default-Werte
                if not review_types:  # Behandelt None, [], und andere falsy Werte
                    review_types = ["comprehensive"]
                
                output_file = review_options.get("output_file", "review_report.md") if review_options else "review_report.md"
                include_context = not (review_options and review_options.get("no_context", False))
                show_progress = not (review_options and review_options.get("no_progress", False))
                
                # Debug: Überprüfe Parameter
                print(f"🔍 Debug review_types: {type(review_types)} = {review_types}")
                print(f"🔍 Debug output_file: {type(output_file)} = {output_file}")
                print(f"🔍 Debug include_context: {type(include_context)} = {include_context}")
                print(f"🔍 Debug show_progress: {type(show_progress)} = {show_progress}")
                
                # Check if show_progress parameter is supported
                import inspect
                perform_review_params = inspect.signature(reviewer.perform_review).parameters
                
                if 'show_progress' in perform_review_params:
                    reviewer.perform_review(
                        review_types=review_types,
                        output_file=output_file,
                        include_context=include_context,
                        show_progress=show_progress
                    )
                else:
                    reviewer.perform_review(
                        review_types=review_types,
                        output_file=output_file,
                        include_context=include_context
                    )
            
            return "Code Review erfolgreich abgeschlossen"
            
        except Exception as e:
            raise Exception(f"Code Review fehlgeschlagen: {e}")
    
    def run_full_workflow(self, analysis_options=None, review_options=None):
        """
        Führt den kompletten Workflow durch: Analyse + Review
        
        Args:
            analysis_options: Optionen für PR-Analyse
            review_options: Optionen für Code Review
        """
        
        print("🎯 STARTE KOMPLETTEN PR WORKFLOW")
        print(f"📂 Repository: {self.repo_path}")
        if self.pr_url:
            print(f"🔗 PR URL: {self.pr_url}")
        if self.branch_name:
            print(f"🌿 Branch: {self.branch_name}")
        
        results = {
            "analysis": None,
            "review": None,
            "worktree_path": None,
            "success": False
        }
        
        try:
            # Schritt 1: PR Analyse
            analysis_result = self.run_pr_analysis(analysis_options)
            results["analysis"] = analysis_result
            results["worktree_path"] = str(self.worktree_path) if self.worktree_path else None
            
            # Schritt 2: Code Review
            review_result = self.run_code_review(review_options)
            results["review"] = review_result
            
            results["success"] = True
            
            # Zusammenfassung
            print("\n" + "=" * 80)
            print("🎉 WORKFLOW ERFOLGREICH ABGESCHLOSSEN")
            print("=" * 80)
            
            print("\n📋 Zusammenfassung:")
            print(f"✅ PR Analyse: Abgeschlossen")
            print(f"✅ Code Review: Abgeschlossen")
            
            if self.worktree_path:
                print(f"📁 Worktree: {self.worktree_path}")
                print("\n🔧 Nächste Schritte:")
                print(f"   1. Öffnen Sie den Review Report")
                print(f"   2. Arbeiten Sie im Worktree: cd {self.worktree_path}")
                print(f"   3. Implementieren Sie empfohlene Änderungen")
                print(f"   4. Worktree aufräumen: git worktree remove {self.worktree_path}")
            else:
                print("📁 Worktree: Nicht erstellt (--no-worktree verwendet)")
                print("\n🔧 Nächste Schritte:")
                print(f"   1. Öffnen Sie den Review Report")
                print(f"   2. Implementieren Sie empfohlene Änderungen")
            
            return results
            
        except Exception as e:
            results["error"] = str(e)
            print(f"\n❌ WORKFLOW FEHLER: {e}")
            
            # Cleanup bei Fehler
            if self.worktree_path and self.worktree_path.exists():
                print(f"🗑️  Räume Worktree nach Fehler auf...")
                self.cleanup_worktree(force=True)
            
            return results
    
    def cleanup_worktree(self, force=False):
        """Räumt den Worktree auf"""
        if self.worktree_path and self.worktree_path.exists():
            try:
                cmd = ["git", "worktree", "remove", str(self.worktree_path)]
                if force:
                    cmd.append("--force")
                
                subprocess.run(cmd, cwd=self.repo_path, check=True)
                print(f"✅ Worktree erfolgreich aufgeräumt: {self.worktree_path}")
                self.worktree_path = None
                return True
            except subprocess.CalledProcessError as e:
                if not force:
                    print(f"⚠️  Worktree enthält Änderungen. Versuche mit --force...")
                    return self.cleanup_worktree(force=True)
                else:
                    print(f"❌ Fehler beim Aufräumen des Worktree: {e}")
                    print(f"💡 Manuell aufräumen mit: git worktree remove --force {self.worktree_path}")
                    return False
        else:
            print("ℹ️  Kein Worktree zum Aufräumen vorhanden")
            return True


def main():
    parser = argparse.ArgumentParser(
        description='Automatisierter PR Workflow: Analyse + Code Review',
        epilog="""
Beispiele:
  # Vollständiger Workflow mit PR URL
  python pr_workflow.py --pr-url "https://bitbucket.org/workspace/repo/pull-requests/123"

  # Workflow mit Branch
  python pr_workflow.py --branch "feature/pr-123"

  # Quick Review Workflow  
  python pr_workflow.py --branch "feature/pr-123" --quick-review

  # Security-fokussiertes Review
  python pr_workflow.py --pr-url "..." --review-types security

  # Nur Analyse (ohne Review)
  python pr_workflow.py --branch "..." --analysis-only

  # Cleanup eines bestehenden Worktree
  python pr_workflow.py --cleanup-worktree /tmp/pr_analysis_xyz
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Haupt-Optionen
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--pr-url', '-u', help='Bitbucket PR URL')
    group.add_argument('--branch', '-b', help='Git Branch Name')
    
    parser.add_argument('--repo-path', '-r', default='.',
                       help='Pfad zum Git Repository (Standard: aktuelles Verzeichnis)')
    
    # Workflow-Steuerung
    parser.add_argument('--analysis-only', action='store_true',
                       help='Führt nur die PR-Analyse durch (ohne Review)')
    
    parser.add_argument('--review-only', action='store_true',
                       help='Führt nur das Code Review durch (braucht --worktree)')
    
    parser.add_argument('--worktree', '-w',
                       help='Pfad zu existierendem Worktree (für --review-only)')
    
    # Analyse-Optionen
    analysis_group = parser.add_argument_group('Analyse-Optionen')
    analysis_group.add_argument('--full-content', action='store_true',
                               help='Zeigt vollständigen Dateiinhalt in Analyse')
    analysis_group.add_argument('--analysis-output-dir',
                               help='Speichert Analyse-Dateien in diesem Verzeichnis')
    analysis_group.add_argument('--no-worktree', action='store_true',
                               help='Erstellt keinen Worktree')
    
    # Review-Optionen
    review_group = parser.add_argument_group('Review-Optionen')
    review_group.add_argument('--quick-review', '-q', action='store_true',
                             help='Schnelles Review statt umfassender Analyse')
    review_group.add_argument('--review-focus',
                             choices=['security', 'performance', 'bugs', 'style'],
                             help='Fokus für Quick Review')
    review_group.add_argument('--review-types', '-t', nargs='+',
                             choices=['comprehensive', 'security', 'performance', 'style', 'bugs'],
                             help='Review-Typen für detailliertes Review')
    review_group.add_argument('--review-output', '-o', default='review_report.md',
                             help='Output-Datei für Review Report')
    review_group.add_argument('--no-review-progress', action='store_true',
                             help='Deaktiviert Live-Progress Updates für Review')
    review_group.add_argument('--no-review-context', action='store_true',
                             help='Keine temporäre CLAUDE.md für Review-Kontext')
    
    # Utility-Optionen
    parser.add_argument('--cleanup-worktree', metavar='PATH',
                       help='Räumt einen bestehenden Worktree auf')
    parser.add_argument('--quiet', action='store_true',
                       help='Weniger verbose Ausgabe')
    
    args = parser.parse_args()
    
    # Special case: Cleanup
    if args.cleanup_worktree:
        try:
            worktree_path = Path(args.cleanup_worktree)
            if worktree_path.exists():
                # Versuche erst ohne force
                try:
                    subprocess.run(
                        ["git", "worktree", "remove", str(worktree_path)],
                        check=True
                    )
                    print(f"✅ Worktree erfolgreich aufgeräumt: {worktree_path}")
                except subprocess.CalledProcessError:
                    # Falls das fehlschlägt, versuche mit --force
                    subprocess.run(
                        ["git", "worktree", "remove", "--force", str(worktree_path)],
                        check=True
                    )
                    print(f"✅ Worktree mit --force aufgeräumt: {worktree_path}")
            else:
                print(f"❌ Worktree Pfad existiert nicht: {worktree_path}")
                sys.exit(1)
        except Exception as e:
            print(f"❌ Fehler beim Aufräumen: {e}")
            print(f"💡 Versuchen Sie manuell: git worktree remove --force {args.cleanup_worktree}")
            sys.exit(1)
        return
    
    # Validierung
    if not args.pr_url and not args.branch and not args.review_only:
        parser.error("--pr-url, --branch oder --review-only muss angegeben werden")
    
    if args.review_only and not args.worktree:
        parser.error("--review-only benötigt --worktree")
    
    try:
        # Erstelle Workflow-Manager
        workflow = PRWorkflowAutomation(
            pr_url=args.pr_url,
            branch_name=args.branch,
            repo_path=args.repo_path,
            verbose=not args.quiet
        )
        
        # Workflow-Auswahl
        if args.review_only:
            # Nur Review
            workflow.worktree_path = Path(args.worktree)
            
            review_options = {
                "quick": args.quick_review,
                "focus": args.review_focus,
                "review_types": args.review_types,
                "output_file": args.review_output,
                "no_context": args.no_review_context,
                "no_progress": args.no_review_progress
            }
            
            workflow.run_code_review(review_options)
            
        elif args.analysis_only:
            # Nur Analyse
            analysis_options = {
                "full_content": args.full_content,
                "output_dir": args.analysis_output_dir,
                "no_worktree": args.no_worktree
            }
            
            workflow.run_pr_analysis(analysis_options)
            
        else:
            # Vollständiger Workflow
            analysis_options = {
                "full_content": args.full_content,
                "output_dir": args.analysis_output_dir,
                "no_worktree": args.no_worktree
            }
            
            review_options = {
                "quick": args.quick_review,
                "focus": args.review_focus,
                "review_types": args.review_types,
                "output_file": args.review_output,
                "no_context": args.no_review_context,
                "no_progress": args.no_review_progress
            }
            
            results = workflow.run_full_workflow(analysis_options, review_options)
            
            if not results["success"]:
                sys.exit(1)
    
    except Exception as e:
        print(f"❌ Fehler: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()