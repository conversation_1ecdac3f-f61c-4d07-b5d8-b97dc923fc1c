{"pr_config": {"source": {"type": "branch", "value": "CMS20-1234-erweiterung-vom-ai-service-um", "pr_url": "https://bitbucket.org/rma-digital/rma-mono/pull-requests/411"}, "repository": {"path": "/Users/<USER>/dev/rma-mono", "worktree_options": {"create": true, "cleanup_after": false}}, "analysis_options": {"show_full_content": false, "output_dir": null}}, "jira_config": {"enabled": true, "server_url": "https://your-company.atlassian.net", "credentials": {"email": "<EMAIL>", "api_token": "${JIRA_API_TOKEN}"}, "ticket_extraction": {"auto_extract_from_branch": true, "branch_patterns": ["feature/([A-Z]+\\d+-\\d+)", "feature/([A-Z]+-\\d+)", "feature/([A-Z]+-\\d+)-", "bugfix/([A-Z]+\\d+-\\d+)", "bugfix/([A-Z]+-\\d+)", "bugfix/([A-Z]+-\\d+)-", "hotfix/([A-Z]+\\d+-\\d+)", "hotfix/([A-Z]+-\\d+)", "hotfix/([A-Z]+-\\d+)-", "^([A-Z]+\\d+-\\d+)-", "^([A-Z]+-\\d+)-", "([A-Z]+\\d+-\\d+)", "([A-Z]+-\\d+)"], "manual_ticket_id": "CMS20-1234", "manual_ticket_file": "ticket.md"}, "acceptance_criteria_fields": ["customfield_10020", "customfield_10021"]}, "review_config": {"type": "comprehensive", "focus_areas": ["acceptance_criteria_compliance", "code_quality", "security", "testing"], "output": {"file": "enhanced_review_report.md", "include_live_progress": true, "include_context": true}, "claude_code_options": {"max_turns": 15, "timeout_seconds": 900, "tools_allowed": ["View"], "enhanced_thinking": true}}, "integrations": {"slack": {"enabled": false, "webhook_url": "${SLACK_WEBHOOK_URL}", "notify_on_completion": true, "notify_on_critical_issues": true}, "email": {"enabled": false, "smtp_server": "smtp.company.com", "smtp_port": 587, "username": "${EMAIL_USER}", "password": "${EMAIL_PASS}", "recipients": ["<EMAIL>"]}}}