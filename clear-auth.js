// Script zum Löschen aller gespeicherten Auth-Daten und Atlassian Cookies
// Führe das in der Browser-Console aus

console.log('🧹 Clearing all authentication data...')

// Clear localStorage
const keysToRemove = [
  'bitbucket_auth',
  'jira_access_token', 
  'jira_refresh_token',
  'jira_token_expiry',
  'jira_base_url',
  'jira_cloud_id',
  'jira_oauth_state'
]

keysToRemove.forEach(key => {
  if (localStorage.getItem(key)) {
    console.log(`Removing: ${key}`)
    localStorage.removeItem(key)
  }
})

// Clear sessionStorage
const sessionKeys = Object.keys(sessionStorage)
sessionKeys.forEach(key => {
  if (key.includes('oauth') || key.includes('auth') || key.includes('jira') || key.includes('bitbucket')) {
    console.log(`Removing session: ${key}`)
    sessionStorage.removeItem(key)
  }
})

// Clear all cookies for Atlassian domains
function clearAtlassianCookies() {
  const atlassianDomains = [
    '.atlassian.com',
    '.atlassian.net', 
    'auth.atlassian.com',
    'id.atlassian.com',
    '.bitbucket.org'
  ]
  
  console.log('🍪 Attempting to clear Atlassian cookies...')
  
  // Note: We can only clear cookies for the current domain due to browser security
  // The user will need to manually clear cookies or use the Switch Account button
  document.cookie.split(";").forEach(function(c) { 
    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
  });
  
  console.log('⚠️  Browser security prevents clearing cross-domain cookies.')
  console.log('📝 To fully clear Atlassian session:')
  console.log('   1. Use the "Switch Account" button in the app, OR')
  console.log('   2. Manually visit https://id.atlassian.com/logout, OR')
  console.log('   3. Clear all cookies in your browser settings')
}

clearAtlassianCookies()

console.log('✅ All authentication data cleared!')
console.log('🔄 Please refresh the page to start fresh')
console.log('💡 Use the "Switch Account" button for best results')