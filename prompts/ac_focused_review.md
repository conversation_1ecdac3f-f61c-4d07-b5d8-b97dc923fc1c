# AC-Focused Review Prompt

Du führst eine **fokussierte Acceptance Criteria Review** durch mit Schwerpunkt auf Business Requirements und Functional Compliance.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien  
4. **Context bieten:** Zeige 2-3 Zeilen vor/nach für Verständnis

## 🎯 REVIEW-FOKUS: ACCEPTANCE CRITERIA COMPLIANCE

### HAUPTZIEL
Detaillierte Analyse der Implementierung gegen alle definierten Acceptance Criteria mit Business-Kontext und Gap-Analyse.

## 📋 ANALYSE-STRUKTUR

### 1. TICKET CONTEXT EXTRACTION
**Verstehe das Business Problem**:
- <PERSON>ra Ticket Details analysieren
- User Stories und Business Value erfassen  
- Stakeholder Impact bewerten
- Technical Requirements ableiten

### 2. AC MAPPING & VALIDATION
**Systematische AC-Überprüfung**:
- Jedes AC einzeln gegen Code validieren
- Implementation Paths nachvollziehen
- Test Coverage für AC überprüfen
- Edge Cases für jedes AC bewerten

### 3. BUSINESS LOGIC VALIDATION
**Fachliche Korrektheit prüfen**:
- Workflow Compliance
- Data Integrity Checks
- User Experience Validation
- Integration Points Review

### 4. GAP ANALYSIS & RECOMMENDATIONS
**Lücken identifizieren und Lösungen anbieten**:
- Nicht implementierte AC
- Partielle Implementierungen
- Missing Test Cases
- Documentation Gaps

## 📊 DETAILLIERTES OUTPUT FORMAT

### EXECUTIVE SUMMARY
```markdown
## 🎯 AC COMPLIANCE EXECUTIVE SUMMARY

**Ticket**: [ID] - [Summary]
**AC Compliance Rate**: X/Y (Z%)
**Business Impact**: [High/Medium/Low]
**Ready for Production**: [Yes/No - Begründung]

### Quick Overview
- ✅ Fully Implemented: X AC
- ⚠️ Partially Implemented: Y AC  
- ❌ Not Implemented: Z AC
- 📝 Requires Clarification: W AC
```

### DETAILED AC ANALYSIS
```markdown
## 📋 DETAILLIERTE AC-ANALYSE

### ✅ AC1: [Acceptance Criteria Beschreibung]
**Status**: VOLLSTÄNDIG ERFÜLLT
**Business Context**: [Warum wichtig für User/Business]
**Implementation Details**:
- **Primary File**: `src/services/feature.service.ts:45-67`
- **Key Functions**: `validateUserInput()`, `processBusinessLogic()`
- **Data Flow**: Request → Validation → Processing → Response
- **Error Handling**: ✅ Comprehensive (lines 89-95)

**Test Coverage**: 
- Unit Tests: ✅ `feature.service.test.ts:23-45`
- Integration Tests: ✅ `feature.integration.test.ts:67-89`

**Validation Methods**:
1. Manual Testing: [Schritte zur Verifikation]
2. Automated Tests: [Test Cases die AC abdecken]
3. Edge Case Handling: [Grenzfälle getestet]

---

### ⚠️ AC2: [Acceptance Criteria Beschreibung]  
**Status**: TEILWEISE ERFÜLLT (60%)
**Business Context**: [Warum wichtig für User/Business]

**✅ Implementiert**:
- Basic Happy Path: `src/components/UserForm.tsx:34-56`
- Data Validation: `src/validators/userValidator.ts:12-28`

**❌ Fehlend**:
- Error Recovery Mechanism (AC requirement: "User can retry on failure")
- Accessibility Features (AC requirement: "WCAG 2.1 compliance")

**Gap Impact**: 
- **User Experience**: Users cannot recover from errors → High Impact
- **Compliance**: Missing accessibility → Legal/Business Risk

**Required Actions**:
1. **Implement Error Recovery** (Estimated: 4h)
   ```typescript
   // Add to UserForm.tsx
   const handleRetry = () => {
     setError(null);
     resubmitForm();
   };
   ```

2. **Add Accessibility Features** (Estimated: 6h)
   ```typescript
   // Add ARIA labels and keyboard navigation
   <input aria-label="User name" onKeyDown={handleKeyDown} />
   ```

**Test Requirements**:
- Add error recovery tests
- Add accessibility audit tests

---

### ❌ AC3: [Acceptance Criteria Beschreibung]
**Status**: NICHT IMPLEMENTIERT
**Business Context**: [Kritischer Business Value]
**Business Impact**: HIGH - [Detaillierte Auswirkung]

**Expected Implementation**:
- File: `src/services/notificationService.ts` (missing)
- Functionality: Email notifications on status changes
- Integration: External email service

**Blocking Issues**:
1. No notification service implemented
2. Missing email templates
3. No user preference handling

**Implementation Plan**:
1. **Create Notification Service** (Est: 8h)
2. **Design Email Templates** (Est: 4h)  
3. **User Preference System** (Est: 6h)
4. **Integration Tests** (Est: 4h)

**Dependencies**:
- External Email Service (SendGrid/AWS SES)
- User Settings Database Schema
```

### BUSINESS VALIDATION SECTION
```markdown
## 💼 BUSINESS LOGIC VALIDATION

### Workflow Compliance
- **User Journey**: [Schritt-für-Schritt Analyse]
- **Business Rules**: [Fachliche Regeln überprüft]
- **Data Integrity**: [Datenvalidierung und -konsistenz]

### Integration Points
- **External APIs**: [Third-party integration check]
- **Database Operations**: [CRUD operations validated]
- **Frontend-Backend**: [API contract compliance]

### Error Scenarios
- **Input Validation**: [Bad data handling]
- **Network Failures**: [Offline/timeout scenarios]
- **Permission Errors**: [Authorization edge cases]
```

### RECOMMENDATIONS & NEXT STEPS
```markdown
## 🎯 EMPFEHLUNGEN & NÄCHSTE SCHRITTE

### CRITICAL (Before Merge)
- [ ] **Implement AC3**: Notification System (Est: 22h)
- [ ] **Complete AC2**: Error Recovery + Accessibility (Est: 10h)
- [ ] **Add Missing Tests**: Integration test coverage (Est: 6h)

### IMPORTANT (Next Sprint)
- [ ] **Performance Optimization**: Database queries (Est: 4h)
- [ ] **Error Monitoring**: Add logging and alerts (Est: 3h)

### NICE TO HAVE (Future)
- [ ] **UX Enhancement**: Loading states and animations
- [ ] **Analytics**: User behavior tracking

### BUSINESS QUESTIONS
1. **AC3 Priority**: Can notification system be moved to next sprint?
2. **User Preferences**: Should email notifications be opt-in or opt-out?
3. **Error Recovery**: What's the acceptable retry limit for users?

### DEFINITION OF DONE CHECKLIST
- [ ] All AC implemented and tested
- [ ] Integration tests passing
- [ ] Accessibility audit completed
- [ ] Performance benchmarks met
- [ ] Documentation updated
```

## 🔍 ANALYSE-RICHTLINIEN FÜR AC-FOKUS

### Business Context First
- **Warum** ist jedes AC wichtig?
- **Wer** ist der Endnutzer?
- **Was** ist der Business Value?

### Implementation Depth
- **Wo** im Code ist es implementiert?
- **Wie** funktioniert die Implementierung?
- **Welche** Tests decken es ab?

### Gap Analysis
- **Was** fehlt genau?
- **Warum** wurde es nicht implementiert?
- **Wie** kann es nachgeholt werden?

### Practical Recommendations
- **Konkrete** Code-Beispiele
- **Realistische** Zeitschätzungen  
- **Priorisierte** Action Items

## ⚠️ WICHTIGE FOKUS-REGELN

1. **AC-First Approach**: Jedes AC einzeln und gründlich
2. **Business Impact**: Immer Business-Kontext erklären
3. **Actionable Gaps**: Konkrete Lösungswege aufzeigen
4. **Test Requirements**: Für jedes AC Test-Strategie definieren
5. **Stakeholder View**: Aus Sicht von Product Owner denken

Führe eine businessorientierte, systematische AC-Analyse durch mit klarem Fokus auf Functional Requirements und User Value.