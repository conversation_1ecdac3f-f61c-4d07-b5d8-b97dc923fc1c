# Ticket Discovery and Mapping Report

**Generated:** 2025-06-07 03:26:10
**Total Branches:** 2
**Mapped Tickets:** 2
**Success Rate:** 100.0%

---

## Mapping Results

| Branch | Ticket ID | Method | Confidence | Ticket File |
|--------|-----------|--------|------------|-------------|
| `feature/CMS20-1166-autorefresh-von-luftqualität-` | CMS20-1166 | branch_name | 0.9 | None |
| `autodev-bot/CMS20-1167/65777629-efd2-414a-8bbc-e311092c6876` | CMS20-1167 | branch_name | 0.9 | None |

---

## Discovery Methods Used

1. **branch_name** (0.9): Extract ticket ID from branch name pattern
2. **branch_file** (0.8): Find branch-specific ticket file
3. **commit_message** (0.7): Search recent commit messages
4. **generic_file** (0.5): Use generic ticket.md file
5. **none** (0.0): No ticket found

---

## Recommendations

### High Confidence Mappings (≥0.8)
- ✅ `feature/CMS20-1166-autorefresh-von-luftqualität-` → `CMS20-1166` (branch_name)
- ✅ `autodev-bot/CMS20-1167/65777629-efd2-414a-8bbc-e311092c6876` → `CMS20-1167` (branch_name)

### Low Confidence or Missing (≤0.5)

---

## Next Steps

1. **Verify Low Confidence Mappings:** Review and confirm ticket assignments
2. **Create Missing Tickets:** Add ticket files for unmapped branches  
3. **Improve Branch Naming:** Use ticket IDs in branch names (e.g., `feature/CMS20-1166-description`)
4. **Standardize Ticket Files:** Store tickets in consistent location with predictable names

---

*Generated by Advanced Ticket Discovery System*
