{"pr_config": {"source": {"type": "batch", "tickets": [], "pr_urls": []}, "repository": {"path": "/Users/<USER>/dev/rma-mono", "worktree_options": {"create": true, "cleanup_after": false, "base_path": "/tmp/parallel_reviews"}}, "analysis_options": {"show_full_content": false, "output_dir": "parallel_reviews"}}, "parallel_config": {"max_parallel_reviews": 3, "timeout_per_review": 1800, "auto_cleanup": false, "stagger_start_delay": 5, "resource_monitoring": true}, "jira_config": {"enabled": true, "server_url": "https://your-company.atlassian.net", "credentials": {"email": "<EMAIL>", "api_token": "${JIRA_API_TOKEN}"}, "ticket_extraction": {"auto_extract_from_branch": true, "branch_patterns": ["feature/([A-Z]+\\d+-\\d+)", "feature/([A-Z]+-\\d+)", "feature/([A-Z]+-\\d+)-", "bugfix/([A-Z]+\\d+-\\d+)", "bugfix/([A-Z]+-\\d+)", "bugfix/([A-Z]+-\\d+)-", "hotfix/([A-Z]+\\d+-\\d+)", "hotfix/([A-Z]+-\\d+)", "hotfix/([A-Z]+-\\d+)-"], "fallback_to_manual": true, "manual_ticket_file": "ticket.md"}, "acceptance_criteria_fields": ["customfield_10020", "customfield_10021"]}, "review_config": {"type": "comprehensive", "focus_areas": ["acceptance_criteria_compliance", "code_quality", "security", "testing", "bug_detection"], "output": {"file": "enhanced_review_report.md", "include_live_progress": true, "include_context": true, "parallel_output_dir": "parallel_reviews"}, "claude_code_options": {"max_turns": 35, "timeout_seconds": 1800, "tools_allowed": ["View", "Grep", "<PERSON><PERSON>"], "enhanced_thinking": true, "parallel_safe": true}}, "batch_review_config": {"modes": {"quick": {"max_turns": 15, "timeout": 300, "focus": ["ac_compliance", "critical_bugs"]}, "full": {"max_turns": 35, "timeout": 1800, "focus": ["ac_compliance", "bug_detection", "code_quality"]}, "ac_only": {"max_turns": 25, "timeout": 900, "focus": ["ac_compliance"]}, "bug_analysis": {"max_turns": 30, "timeout": 1200, "focus": ["bug_detection", "code_quality", "security"]}}, "resource_limits": {"max_memory_per_review": "2GB", "max_cpu_per_review": "1 core", "disk_space_check": true}}, "reporting": {"individual_reports": true, "combined_report": true, "summary_metrics": true, "export_formats": ["markdown", "json"], "notifications": {"slack_webhook": "${SLACK_WEBHOOK_URL}", "email_recipients": []}}, "integrations": {"slack": {"enabled": false, "webhook_url": "${SLACK_WEBHOOK_URL}", "notify_on_completion": true, "notify_on_critical_issues": true, "batch_summary": true}, "email": {"enabled": false, "smtp_server": "smtp.company.com", "smtp_port": 587, "username": "${EMAIL_USER}", "password": "${EMAIL_PASS}", "batch_recipients": ["<EMAIL>"]}}}