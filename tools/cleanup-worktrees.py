#!/usr/bin/env python3
"""
Git Worktree Cleanup Tool
Cleans up stale, missing, or conflicting worktrees.
"""

import subprocess
import sys
from pathlib import Path
import argparse

def get_configured_master_repo():
    """Get configured master repository path"""
    try:
        # Import here to avoid path issues
        sys.path.append(str(Path(__file__).parent.parent))
        from core.config import get_configured_worktree_base_path
        return get_configured_worktree_base_path()
    except Exception as e:
        print(f"❌ Could not get configured repository path: {e}")
        print("Please configure your master repository in Settings → Git Worktree")
        return None

def prune_missing_worktrees(repo_path):
    """Remove worktree entries for missing directories"""
    try:
        print("🧹 Pruning missing worktrees...")
        result = subprocess.run([
            "git", "worktree", "prune", "-v"
        ], cwd=repo_path, capture_output=True, text=True)
        
        if result.stdout.strip():
            print(f"Pruned: {result.stdout.strip()}")
        else:
            print("✅ No worktrees to prune")
            
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to prune worktrees: {e}")
        return False

def list_worktrees(repo_path):
    """List all current worktrees"""
    try:
        result = subprocess.run([
            "git", "worktree", "list", "-v"
        ], cwd=repo_path, capture_output=True, text=True, check=True)
        
        print("\n📁 Current worktrees:")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to list worktrees: {e}")
        return False

def remove_worktree(repo_path, worktree_path, force=False):
    """Remove a specific worktree"""
    try:
        cmd = ["git", "worktree", "remove"]
        if force:
            cmd.append("--force")
        cmd.append(str(worktree_path))
        
        subprocess.run(cmd, cwd=repo_path, check=True)
        print(f"✅ Removed worktree: {worktree_path}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to remove worktree {worktree_path}: {e}")
        return False

def interactive_cleanup(repo_path):
    """Interactive cleanup of problematic worktrees"""
    try:
        # Get worktree list
        result = subprocess.run([
            "git", "worktree", "list", "--porcelain"
        ], cwd=repo_path, capture_output=True, text=True, check=True)
        
        # Parse worktrees
        lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
        worktrees = []
        current_worktree = {}
        
        for line in lines:
            if line.startswith('worktree '):
                if current_worktree:
                    worktrees.append(current_worktree)
                current_worktree = {'path': line.replace('worktree ', '')}
            elif line.startswith('branch '):
                current_worktree['branch'] = line.replace('branch ', '').replace('refs/heads/', '')
            elif line.startswith('HEAD '):
                current_worktree['head'] = line.replace('HEAD ', '')
            elif line == 'prunable':
                current_worktree['prunable'] = True
        
        if current_worktree:
            worktrees.append(current_worktree)
        
        # Find problematic worktrees
        problematic = []
        for worktree in worktrees:
            path = Path(worktree.get('path', ''))
            if not path.exists() or worktree.get('prunable', False):
                problematic.append(worktree)
        
        if not problematic:
            print("✅ No problematic worktrees found")
            return True
        
        print(f"\n⚠️ Found {len(problematic)} problematic worktrees:")
        for i, worktree in enumerate(problematic, 1):
            path = worktree.get('path', 'Unknown')
            branch = worktree.get('branch', 'Unknown')
            status = "Missing directory" if not Path(path).exists() else "Prunable"
            print(f"  {i}. {path} [{branch}] - {status}")
        
        print("\nOptions:")
        print("  a) Remove all problematic worktrees")
        print("  s) Select specific worktrees to remove")
        print("  q) Quit without changes")
        
        choice = input("\nYour choice [a/s/q]: ").lower().strip()
        
        if choice == 'a':
            # Remove all problematic
            for worktree in problematic:
                remove_worktree(repo_path, worktree['path'], force=True)
        elif choice == 's':
            # Select specific
            for i, worktree in enumerate(problematic, 1):
                path = worktree.get('path', 'Unknown')
                branch = worktree.get('branch', 'Unknown')
                
                remove = input(f"Remove {path} [{branch}]? [y/N]: ").lower().strip()
                if remove in ['y', 'yes']:
                    remove_worktree(repo_path, worktree['path'], force=True)
        else:
            print("Cancelled")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Interactive cleanup failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(
        description="Git Worktree Cleanup Tool",
        epilog="""
Examples:
  # Clean up missing worktrees automatically
  python cleanup-worktrees.py --prune
  
  # List all worktrees
  python cleanup-worktrees.py --list
  
  # Interactive cleanup
  python cleanup-worktrees.py --interactive
  
  # Remove specific worktree
  python cleanup-worktrees.py --remove /path/to/worktree
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--prune', action='store_true', 
                       help='Prune missing worktrees automatically')
    parser.add_argument('--list', action='store_true',
                       help='List all current worktrees')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='Interactive cleanup of problematic worktrees')
    parser.add_argument('--remove', metavar='PATH',
                       help='Remove specific worktree by path')
    parser.add_argument('--repo', metavar='PATH',
                       help='Repository path (uses configured path if not provided)')
    
    args = parser.parse_args()
    
    # Get repository path
    if args.repo:
        repo_path = Path(args.repo)
    else:
        repo_path_str = get_configured_master_repo()
        if not repo_path_str:
            sys.exit(1)
        repo_path = Path(repo_path_str)
    
    if not repo_path.exists():
        print(f"❌ Repository path does not exist: {repo_path}")
        sys.exit(1)
    
    # Execute requested action
    if args.prune:
        prune_missing_worktrees(repo_path)
    elif args.list:
        list_worktrees(repo_path)
    elif args.interactive:
        interactive_cleanup(repo_path)
    elif args.remove:
        remove_worktree(repo_path, args.remove, force=True)
    else:
        # Default: show status and offer interactive cleanup
        print("🔍 Git Worktree Status:")
        list_worktrees(repo_path)
        
        print("\nUse --help for cleanup options or --interactive for guided cleanup")

if __name__ == "__main__":
    main()