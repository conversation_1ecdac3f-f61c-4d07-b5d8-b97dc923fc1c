#!/bin/bash
# AI Ticket Converter Wrapper <PERSON>ript

# Activate virtual environment
source venv/bin/activate

# Check if input is provided
if [ $# -eq 0 ]; then
    echo "🎯 AI-Powered Ticket Converter"
    echo ""
    echo "Usage:"
    echo "  $0 \"raw ticket text\"                    # Convert text directly"
    echo "  $0 --file ticket.txt                     # Convert from file"
    echo "  $0 --file ticket.txt --output CMS20-1166.md  # Convert to specific file"
    echo "  $0 --stdin                               # Read from stdin"
    echo ""
    echo "Examples:"
    echo '  ./convert_ticket.sh "Autorefresh von Luftqualität-Beiträgen..."'
    echo "  ./convert_ticket.sh --file raw_ticket.txt --ticket-id CMS20-1166"
    echo "  echo 'Ticket content...' | ./convert_ticket.sh --stdin"
    echo ""
    echo "Options:"
    echo "  --file FILE        Input file with raw ticket text"
    echo "  --output FILE      Output file for structured ticket"
    echo "  --ticket-id ID     Specify ticket ID (if not auto-detected)"
    echo "  --no-analysis      Skip Claude codebase analysis (faster)"
    echo "  --stdin           Read from stdin"
    echo ""
    exit 1
fi

# Parse arguments
TICKET_TEXT=""
INPUT_FILE=""
OUTPUT_FILE=""
TICKET_ID=""
NO_ANALYSIS=""
FROM_STDIN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --file)
            INPUT_FILE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --ticket-id)
            TICKET_ID="$2"
            shift 2
            ;;
        --no-analysis)
            NO_ANALYSIS="--no-analysis"
            shift
            ;;
        --stdin)
            FROM_STDIN=true
            shift
            ;;
        *)
            # Treat as direct ticket text
            TICKET_TEXT="$1"
            shift
            ;;
    esac
done

echo "🤖 Starting AI Ticket Converter..."

# Build python command
CMD="python3 utils/ticket_converter.py"

if [ -n "$INPUT_FILE" ]; then
    CMD="$CMD --input \"$INPUT_FILE\""
    echo "📁 Input: $INPUT_FILE"
elif [ "$FROM_STDIN" = true ]; then
    echo "📝 Reading from stdin..."
elif [ -n "$TICKET_TEXT" ]; then
    CMD="$CMD \"$TICKET_TEXT\""
    echo "📝 Converting direct text input"
else
    echo "❌ No input provided"
    exit 1
fi

if [ -n "$OUTPUT_FILE" ]; then
    CMD="$CMD --output \"$OUTPUT_FILE\""
    echo "📄 Output: $OUTPUT_FILE"
fi

if [ -n "$TICKET_ID" ]; then
    CMD="$CMD --ticket-id \"$TICKET_ID\""
    echo "🎫 Ticket ID: $TICKET_ID"
fi

if [ -n "$NO_ANALYSIS" ]; then
    CMD="$CMD $NO_ANALYSIS"
    echo "⚡ Mode: Fast (no codebase analysis)"
else
    echo "🔍 Mode: Smart (with codebase analysis)"
fi

echo ""

# Execute conversion
if [ "$FROM_STDIN" = true ]; then
    eval $CMD
else
    eval $CMD
fi

RESULT=$?

if [ $RESULT -eq 0 ]; then
    echo ""
    echo "🎉 Ticket conversion successful!"
    echo "💡 Tip: Review the generated acceptance criteria and adjust if needed"
else
    echo ""
    echo "❌ Ticket conversion failed"
fi

exit $RESULT
