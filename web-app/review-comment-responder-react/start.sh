#!/bin/bash

# Start script for Review Comment Responder React

echo "🚀 Starting Review Comment Responder React..."

# Get script directory and .devtools root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
WEB_APP_DIR="$(dirname "$SCRIPT_DIR")"
DEVTOOLS_ROOT="$(dirname "$WEB_APP_DIR")"

cd "$SCRIPT_DIR"

# Check if <PERSON> is installed
if ! command -v claude &> /dev/null; then
    echo "⚠️  Warning: Claude Code CLI not found in PATH"
    echo "   The backend will not be able to process comments"
    echo "   Install from: https://github.com/anthropics/claude-code"
fi

# Check if node is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is required but not installed."
    exit 1
fi

# Install/Update Node dependencies
echo "📦 Installing/Updating Node dependencies..."
npm install

# Check if Python is installed for backend
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required for backend but not installed."
    exit 1
fi

# Navigate to devtools root for venv
cd "$DEVTOOLS_ROOT"

# Check if venv exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "   Please create it first with: python3 -m venv venv"
    exit 1
fi

# Kill any existing processes on our ports
echo "🧹 Cleaning up existing processes..."
lsof -ti:5001 | xargs kill -9 2>/dev/null || true
lsof -ti:5002 | xargs kill -9 2>/dev/null || true
lsof -ti:5173 | xargs kill -9 2>/dev/null || true

# Activate virtual environment
echo "🐍 Activating virtual environment..."
source venv/bin/activate

# Install/Update Python dependencies
echo "📦 Installing/Updating Python dependencies..."
pip install -r requirements.txt

# Start the backend for Comment Responder
echo "🔧 Starting Comment Responder backend API on http://localhost:5001..."
cd "$SCRIPT_DIR"
python claude-integration.py &
BACKEND_PID=$!

# Wait for backend to start
sleep 3

# Check if backend started successfully
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ Failed to start Comment Responder backend"
    echo "   Check the logs above for errors"
    exit 1
fi

# Start the enhanced Code Reviewer backend
echo "🚀 Starting Code Reviewer backend API on http://localhost:5002..."
cd "$DEVTOOLS_ROOT/core"
PYTHONPATH="$DEVTOOLS_ROOT" python main.py &
REVIEWER_BACKEND_PID=$!

# Wait for Code Reviewer backend to start
sleep 3

# Check if Code Reviewer backend started successfully
if ! kill -0 $REVIEWER_BACKEND_PID 2>/dev/null; then
    echo "❌ Failed to start Code Reviewer backend"
    echo "   Check the logs above for errors"
    # Still continue, as the app can work without it
fi

# Start the React dev server
echo "⚛️  Starting React dev server on http://localhost:5173..."
cd "$SCRIPT_DIR"
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 3

# Open the frontend
echo "🌐 Opening React UI..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    open http://localhost:5173
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open http://localhost:5173
else
    echo "Please open http://localhost:5173 in your browser"
fi

echo ""
echo "✅ Review Comment Responder React is running!"
echo "   Frontend: http://localhost:5173"
echo "   Comment Responder Backend: http://localhost:5001"
echo "   Code Reviewer Backend: http://localhost:5002"
echo ""
echo "Press Ctrl+C to stop..."

# Cleanup function
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $REVIEWER_BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit
}

# Wait for Ctrl+C
trap cleanup INT
wait