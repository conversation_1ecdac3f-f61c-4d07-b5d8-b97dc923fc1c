import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatResponse(text: string) {
  // Convert markdown code blocks to highlighted blocks
  text = text.replace(/```(\w+)?\n([\s\S]*?)```/g, (_, __, code) => {
    return `<div class="code-block">${escapeHtml(code.trim())}</div>`
  })
  
  // Convert inline code
  text = text.replace(/`([^`]+)`/g, '<code class="bg-primary/10 text-primary px-2 py-1 rounded text-sm font-mono">$1</code>')
  
  // Convert line breaks
  text = text.replace(/\n/g, '<br>')
  
  return text
}

export function escapeHtml(text: string) {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  }
  return text.replace(/[&<>"']/g, m => map[m])
}

export function getStatusIcon(status: string) {
  switch (status) {
    case 'pending': return 'Play'
    case 'processing': return 'Loader2'
    case 'completed': return 'CheckCircle'
    case 'error': return 'AlertCircle'
    default: return 'HelpCircle'
  }
}

export function getStatusColor(status: string) {
  switch (status) {
    case 'pending': return 'text-yellow-500 hover:bg-yellow-500/10'
    case 'processing': return 'text-primary hover:bg-primary/10'
    case 'completed': return 'text-green-500 hover:bg-green-500/10'
    case 'error': return 'text-red-500 hover:bg-red-500/10'
    default: return 'text-muted-foreground hover:bg-muted'
  }
}

export function getConfidenceColor(confidence: number) {
  if (confidence >= 0.9) return 'text-green-600'
  if (confidence >= 0.7) return 'text-yellow-600'
  return 'text-red-600'
}

export function showToast(message: string, type: 'success' | 'warning' | 'error' | 'info' = 'success') {
  // This will be handled by the Toast component in React
  console.log(`Toast: ${type} - ${message}`)
}