/**
 * Performance and Load Tests for Multi-Agent Integration
 * Testing system behavior under various load conditions
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { render, screen } from '@testing-library/react'
// @ts-ignore // React imported for JSX
import React from 'react'
import '@testing-library/jest-dom'
import { useMultiAgentReview } from '../../hooks/useMultiAgentReview'
import { MultiAgentProgressTracker } from '../../components/MultiAgentProgressTracker'
import { MultiAgentResultsAggregator } from '../../components/MultiAgentResultsAggregator'
import TestDataFactory, { FIXTURES } from '../../test-utils/test-fixtures'
import { MockMultiAgentReviewService } from '../../test-utils/mock-services'
import { setupWebSocketMock, MockWebSocketFactory } from '../../test-utils/mock-websocket'
import type { MultiAgentReviewStatus } from '../../components/MultiAgentProgressTracker'

// Performance measurement utilities
interface PerformanceMetrics {
  renderTime: number
  memoryUsage: number
  updateTime: number
  componentCount: number
}

const measurePerformance = (fn: () => void): PerformanceMetrics => {
  const startTime = performance.now()
  const startMemory = (performance as any).memory?.usedJSHeapSize || 0
  
  fn()
  
  const endTime = performance.now()
  const endMemory = (performance as any).memory?.usedJSHeapSize || 0
  
  return {
    renderTime: endTime - startTime,
    memoryUsage: endMemory - startMemory,
    updateTime: endTime - startTime,
    componentCount: document.querySelectorAll('*').length
  }
}

const createLargeDataset = (size: number) => {
  return {
    status: TestDataFactory.createMultiAgentReviewStatus({
      agent_statuses: Object.fromEntries(
        TestDataFactory.ALL_AGENT_TYPES.map((agent: any) => [
          agent,
          {
            ...TestDataFactory.createAgentStatus(agent),
            status: 'completed',
            progress: 100,
            result: TestDataFactory.createAgentResult(agent, {
              findings: Array.from({ length: size }, (_, i) =>
                TestDataFactory.createFinding({ text: `${agent}-finding-${i}` })
              )
            })
          }
        ])
      )
    }),
    results: TestDataFactory.createEnhancedReviewResults({
      overall_results: {
        summary: `Large dataset with ${size} findings per agent`,
        priority_findings: Array.from({ length: size * 7 }, (_, i) =>
          TestDataFactory.createFinding({ text: `large-finding-${i}` })
        ),
        execution_metrics: {
          total_execution_time: 300,
          parallel_efficiency: 85,
          agent_performance: Object.fromEntries(
            TestDataFactory.ALL_AGENT_TYPES.map((agent: any) => [
              agent,
              { execution_time: 40, success: true, findings_count: size }
            ])
          )
        }
      }
    })
  }
}

describe('Performance and Load Tests', () => {
  let cleanupWebSocket: () => void
  let _mockService: MockMultiAgentReviewService

  beforeEach(() => {
    cleanupWebSocket = setupWebSocketMock()
    _mockService = new MockMultiAgentReviewService('success')
    vi.clearAllMocks()
  })

  afterEach(() => {
    cleanupWebSocket()
    MockWebSocketFactory.clear()
  })

  describe('Component Rendering Performance', () => {
    it('should render MultiAgentProgressTracker efficiently with large datasets', () => {
      const largeStatus = createLargeDataset(100).status
      
      const metrics = measurePerformance(() => {
        render(<MultiAgentProgressTracker reviewStatus={largeStatus} />)
      })

      // Should render within reasonable time (< 100ms)
      expect(metrics.renderTime).toBeLessThan(100)
      
      // Verify it actually rendered correctly
      expect(screen.getByText('Multi-Agent Code Review')).toBeInTheDocument()
    })

    it('should render MultiAgentResultsAggregator efficiently with many findings', () => {
      const largeResults = createLargeDataset(200).results
      
      const metrics = measurePerformance(() => {
        render(<MultiAgentResultsAggregator results={largeResults} />)
      })

      // Should render within reasonable time (< 200ms for large datasets)
      expect(metrics.renderTime).toBeLessThan(200)
      
      // Verify core elements are present
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
    })

    it('should handle rapid re-renders without performance degradation', async () => {
      let renderCount = 0
      const renderTimes: number[] = []
      
      const TestComponent = ({ status }: { status: MultiAgentReviewStatus }) => {
        renderCount++
        return <MultiAgentProgressTracker reviewStatus={status} />
      }

      const baseStatus = TestDataFactory.createMultiAgentReviewStatus()
      const { rerender } = render(<TestComponent status={baseStatus} />)

      // Perform rapid re-renders
      for (let i = 0; i < 50; i++) {
        const startTime = performance.now()
        
        rerender(
          <TestComponent 
            status={{
              ...baseStatus,
              progress: i * 2,
              agent_statuses: {
                ...baseStatus.agent_statuses,
                bug_detection: {
                  ...baseStatus.agent_statuses.bug_detection,
                  progress: i * 2
                }
              }
            }}
          />
        )
        
        renderTimes.push(performance.now() - startTime)
      }

      // Verify all renders completed
      expect(renderCount).toBe(51) // Initial + 50 re-renders

      // Average render time should remain reasonable
      const averageRenderTime = renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length
      expect(averageRenderTime).toBeLessThan(10) // < 10ms average

      // No significant degradation over time
      const firstHalf = renderTimes.slice(0, 25)
      const secondHalf = renderTimes.slice(25)
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length
      
      expect(secondAvg).toBeLessThan(firstAvg * 2) // Not more than 2x slower
    })
  })

  describe('Hook Performance', () => {
    it('should handle useMultiAgentReview with high-frequency updates', async () => {
      const { result } = renderHook(() => 
        useMultiAgentReview({
          enable_websocket: false, // Disable WebSocket for pure hook testing
          polling_interval: 100, // Fast polling
          enable_debug_logs: false
        })
      )

      const updateTimes: number[] = []
      
      // Simulate rapid status updates
      for (let i = 0; i < 100; i++) {
        const startTime = performance.now()
        
        await act(async () => {
          // Simulate state update through reducer
          // This would normally come from WebSocket or polling
          result.current.refreshStatus()
        })
        
        updateTimes.push(performance.now() - startTime)
      }

      // Average update time should be reasonable
      const averageUpdateTime = updateTimes.reduce((a, b) => a + b, 0) / updateTimes.length
      expect(averageUpdateTime).toBeLessThan(5) // < 5ms per update
    })

    it('should manage memory efficiently during long-running sessions', async () => {
      const { result, unmount } = renderHook(() => 
        useMultiAgentReview({
          enable_websocket: false,
          track_performance: true
        })
      )

      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Simulate a long-running review with many updates
      for (let i = 0; i < 500; i++) {
        await act(async () => {
          result.current.refreshStatus()
        })
      }

      const midMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Clean up
      unmount()
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Memory usage should be reasonable
      const memoryIncrease = midMemory - initialMemory
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024) // < 10MB increase
      
      // Memory should be mostly freed after cleanup
      const memoryAfterCleanup = finalMemory - initialMemory
      expect(memoryAfterCleanup).toBeLessThan(memoryIncrease * 0.5) // At least 50% freed
    })
  })

  describe('Service Performance', () => {
    it('should handle concurrent review requests efficiently', async () => {
      const service = new MockMultiAgentReviewService('success')
      const requests = Array.from({ length: 20 }, (_, i) =>
        TestDataFactory.createMultiAgentReviewRequest({
          branch_name: `feature/concurrent-${i}`
        })
      )

      const startTime = performance.now()
      
      // Send all requests concurrently
      const promises = requests.map(request => service.startParallelReview(request))
      const responses = await Promise.all(promises)
      
      const endTime = performance.now()
      const totalTime = endTime - startTime

      // All requests should succeed
      responses.forEach(response => {
        expect(response.success).toBe(true)
      })

      // Should complete within reasonable time (concurrent processing)
      expect(totalTime).toBeLessThan(1000) // < 1 second for 20 concurrent requests
      
      // Average time per request should be low
      const averageTime = totalTime / requests.length
      expect(averageTime).toBeLessThan(50) // < 50ms per request average
    })

    it('should maintain performance under sustained load', async () => {
      const service = new MockMultiAgentReviewService('success')
      const responseTimes: number[] = []

      // Simulate sustained load over time
      for (let batch = 0; batch < 10; batch++) {
        const batchRequests = Array.from({ length: 5 }, (_, i) =>
          TestDataFactory.createMultiAgentReviewRequest({
            branch_name: `feature/batch-${batch}-${i}`
          })
        )

        const batchStartTime = performance.now()
        const batchPromises = batchRequests.map(request => service.startParallelReview(request))
        await Promise.all(batchPromises)
        const batchEndTime = performance.now()

        responseTimes.push(batchEndTime - batchStartTime)
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // Response times should remain consistent
      const firstHalf = responseTimes.slice(0, 5)
      const secondHalf = responseTimes.slice(5)
      
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length
      
      // Performance shouldn't degrade significantly over time
      expect(secondAvg).toBeLessThan(firstAvg * 1.5) // Not more than 50% slower
    })
  })

  describe('WebSocket Performance', () => {
    it('should handle high-frequency WebSocket messages efficiently', async () => {
      const messageCount = 1000
      const receivedMessages: any[] = []
      const processingTimes: number[] = []

      const { result } = renderHook(() => 
        useMultiAgentReview({
          enable_websocket: true,
          enable_debug_logs: false,
          on_status_update: (status) => {
            const startTime = performance.now()
            receivedMessages.push(status)
            processingTimes.push(performance.now() - startTime)
          }
        })
      )

      await act(async () => {
        await result.current.startReview(TestDataFactory.createMultiAgentReviewRequest())
      })

      // Get WebSocket instance
      const wsInstances = MockWebSocketFactory.getInstances()
      expect(wsInstances.length).toBeGreaterThan(0)
      const mockWS = wsInstances[0]

      // Send burst of messages
      const startTime = performance.now()
      
      for (let i = 0; i < messageCount; i++) {
        const message = {
          review_id: 'perf-test-123',
          event_type: 'agent_progress',
          agent_type: 'bug_detection',
          timestamp: Date.now() + i,
          data: { progress: (i / messageCount) * 100 }
        }
        
        mockWS.dispatchEvent(new MessageEvent('message', {
          data: JSON.stringify(message)
        }))
      }
      
      const endTime = performance.now()
      const totalTime = endTime - startTime

      // Should process all messages quickly
      expect(totalTime).toBeLessThan(1000) // < 1 second for 1000 messages
      
      // Average processing time per message should be minimal
      if (processingTimes.length > 0) {
        const avgProcessingTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length
        expect(avgProcessingTime).toBeLessThan(1) // < 1ms per message
      }
    })

    it('should handle WebSocket reconnection without memory leaks', async () => {
      const { result } = renderHook(() => 
        useMultiAgentReview({
          enable_websocket: true,
          enable_debug_logs: false
        })
      )

      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0

      // Simulate multiple connection cycles
      for (let i = 0; i < 10; i++) {
        // Start review (creates WebSocket)
        await act(async () => {
          await result.current.startReview(TestDataFactory.createMultiAgentReviewRequest())
        })

        // Simulate connection loss and reconnection
        const wsInstances = MockWebSocketFactory.getInstances()
        if (wsInstances.length > 0) {
          await act(async () => {
            wsInstances[0].simulateConnectionIssues()
          })
        }

        // Wait for reconnection
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      // Memory usage should not increase significantly
      expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024) // < 5MB increase
    })
  })

  describe('Data Processing Performance', () => {
    it('should efficiently process large finding datasets', () => {
      const largeFindings = Array.from({ length: 5000 }, (_, i) =>
        TestDataFactory.createFinding({
          id: `perf-finding-${i}`,
          title: `Performance test finding ${i}`,
          file_path: `src/components/Component${Math.floor(i / 100)}.tsx`,
          line_number: (i % 200) + 1
        })
      )

      const results = TestDataFactory.createEnhancedReviewResults({
        overall_results: {
          summary: 'Large dataset performance test',
          priority_findings: largeFindings,
          execution_metrics: TestDataFactory.createEnhancedReviewResults().overall_results.execution_metrics
        }
      })

      const startTime = performance.now()
      render(<MultiAgentResultsAggregator results={results} />)
      const endTime = performance.now()

      // Should render large datasets within reasonable time
      expect(endTime - startTime).toBeLessThan(500) // < 500ms for 5000 findings
      
      // Verify it rendered successfully
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
    })

    it('should handle complex filtering operations efficiently', () => {
      const complexResults = TestDataFactory.createEnhancedReviewResults({
        overall_results: {
          summary: 'Complex filtering test',
          priority_findings: Array.from({ length: 1000 }, (_, i) => {
            const priorities = ['high', 'medium', 'low'] as const
            const types = ['bug', 'security', 'quality', 'architecture'] as const
            
            return TestDataFactory.createFinding({
              id: `filter-finding-${i}`,
              priority: priorities[i % priorities.length],
              type: types[i % types.length],
              file_path: `src/modules/module${Math.floor(i / 50)}/file${i % 10}.ts`
            })
          }),
          execution_metrics: TestDataFactory.createEnhancedReviewResults().overall_results.execution_metrics
        }
      })

      const startTime = performance.now()
      render(<MultiAgentResultsAggregator results={complexResults} />)
      const endTime = performance.now()

      // Should handle complex datasets efficiently
      expect(endTime - startTime).toBeLessThan(300) // < 300ms for complex filtering
    })
  })

  describe('Stress Testing', () => {
    it('should handle extreme load conditions gracefully', async () => {
      // Create an extremely large dataset
      const extremeDataset = createLargeDataset(1000)
      
      // Multiple concurrent operations
      const operations = [
        () => render(<MultiAgentProgressTracker reviewStatus={extremeDataset.status} />),
        () => render(<MultiAgentResultsAggregator results={extremeDataset.results} />),
        () => TestDataFactory.createSuccessfulReviewScenario(),
        () => TestDataFactory.createFailedReviewScenario()
      ]

      const startTime = performance.now()
      
      // Execute all operations concurrently
      await Promise.all(operations.map(op => 
        new Promise(resolve => {
          try {
            op()
            resolve(true)
          } catch (error) {
            resolve(false)
          }
        })
      ))
      
      const endTime = performance.now()
      
      // Should complete within reasonable time even under extreme load
      expect(endTime - startTime).toBeLessThan(2000) // < 2 seconds
    })

    it('should maintain responsiveness during intensive operations', async () => {
      let interactionResponseTimes: number[] = []
      
      // Start intensive background operation
      const intensiveOp = async () => {
        for (let i = 0; i < 100; i++) {
          const largeDataset = createLargeDataset(100)
          render(<MultiAgentProgressTracker reviewStatus={largeDataset.status} />)
          await new Promise(resolve => setTimeout(resolve, 10))
        }
      }

      // Start background operation
      const backgroundPromise = intensiveOp()

      // Measure interaction responsiveness during intensive operation
      for (let i = 0; i < 10; i++) {
        const startTime = performance.now()
        
        // Simulate user interaction
        const simpleStatus = TestDataFactory.createMultiAgentReviewStatus()
        render(<MultiAgentProgressTracker reviewStatus={simpleStatus} />)
        
        interactionResponseTimes.push(performance.now() - startTime)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      await backgroundPromise

      // Interactions should remain responsive
      const avgResponseTime = interactionResponseTimes.reduce((a, b) => a + b, 0) / interactionResponseTimes.length
      expect(avgResponseTime).toBeLessThan(50) // < 50ms average response time
    })
  })

  describe('Memory Management', () => {
    it('should clean up resources properly after component unmount', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Create and destroy many components
      for (let i = 0; i < 100; i++) {
        const { unmount } = render(
          <MultiAgentProgressTracker 
            reviewStatus={TestDataFactory.createMultiAgentReviewStatus()} 
          />
        )
        unmount()
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      // Memory increase should be minimal
      expect(memoryIncrease).toBeLessThan(2 * 1024 * 1024) // < 2MB increase
    })

    it('should handle long-running sessions without excessive memory growth', async () => {
      const memoryCheckpoints: number[] = []
      const checkMemory = () => {
        const memory = (performance as any).memory?.usedJSHeapSize || 0
        memoryCheckpoints.push(memory)
      }

      const { result } = renderHook(() => useMultiAgentReview())
      
      checkMemory() // Initial
      
      // Simulate long session with many state changes
      for (let i = 0; i < 200; i++) {
        await act(async () => {
          result.current.refreshStatus()
        })
        
        if (i % 50 === 0) {
          checkMemory()
        }
      }

      // Memory growth should be linear and reasonable
      const memoryGrowth = memoryCheckpoints[memoryCheckpoints.length - 1] - memoryCheckpoints[0]
      expect(memoryGrowth).toBeLessThan(5 * 1024 * 1024) // < 5MB total growth
      
      // No exponential growth pattern
      for (let i = 1; i < memoryCheckpoints.length; i++) {
        const growth = memoryCheckpoints[i] - memoryCheckpoints[i - 1]
        expect(growth).toBeLessThan(2 * 1024 * 1024) // < 2MB per checkpoint
      }
    })
  })
})