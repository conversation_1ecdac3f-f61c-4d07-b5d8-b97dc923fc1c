/**
 * WebSocket Integration Tests for Multi-Agent Review System
 * Testing real-time communication and event handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { useMultiAgentReview } from '../../hooks/useMultiAgentReview'
import { useEnhancedWebSocket } from '../../hooks/useEnhancedWebSocket'
import { MockWebSocket, MockWebSocketFactory, setupWebSocketMock } from '../../test-utils/mock-websocket'
import TestDataFactory from '../../test-utils/test-fixtures'
import type { MultiAgentWebSocketEvent } from '../../types/multi-agent'

// Mock the MultiAgentReviewService
const mockMultiAgentService = {
  startParallelReview: vi.fn(),
  getReviewStatus: vi.fn(),
  getReviewResults: vi.fn(),
  cancelReview: vi.fn()
}

vi.mock('../../services/multiAgent/MultiAgentReviewService', () => ({
  MultiAgentReviewService: vi.fn().mockImplementation(() => mockMultiAgentService)
}))

describe('WebSocket Integration Tests', () => {
  let cleanupWebSocket: () => void
  let mockWebSocket: MockWebSocket

  beforeEach(() => {
    cleanupWebSocket = setupWebSocketMock()
    vi.clearAllMocks()
    
    // Setup service mocks
    mockMultiAgentService.startParallelReview.mockResolvedValue({
      success: true,
      data: TestDataFactory.createMultiAgentReviewResponse()
    })
  })

  afterEach(() => {
    cleanupWebSocket()
    MockWebSocketFactory.clear()
  })

  describe('WebSocket connection and lifecycle', () => {
    it('should establish WebSocket connection for multi-agent reviews', async () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'test-review-123',
          sessionId: 'test-session-123',
          reviewMode: 'multi_agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        expect(result.current.isConnected).toBe(true)
      })

      expect(result.current.currentService).toBe('multi-agent')
      expect(result.current.connectionStatus.connected).toBe(true)
    })

    it('should handle connection failures gracefully', async () => {
      const onError = vi.fn()
      
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError,
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      // Get the mock WebSocket instance
      const instances = MockWebSocketFactory.getInstances()
      expect(instances.length).toBeGreaterThan(0)
      mockWebSocket = instances[0]

      // Simulate connection failure
      act(() => {
        mockWebSocket.close(1006, 'Connection failed')
      })

      await waitFor(() => {
        expect(result.current.isConnected).toBe(false)
      })

      expect(result.current.hasError).toBe(true)
    })

    it('should reconnect after connection loss', async () => {
      const onConnected = vi.fn()
      const onDisconnected = vi.fn()
      
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected,
            onDisconnected
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        expect(result.current.isConnected).toBe(true)
      })

      // Get the mock WebSocket instance
      const instances = MockWebSocketFactory.getInstances()
      mockWebSocket = instances[0]

      // Simulate connection issues (this will trigger reconnection)
      act(() => {
        mockWebSocket.simulateConnectionIssues()
      })

      // Wait for disconnect
      await waitFor(() => {
        expect(result.current.isConnected).toBe(false)
      }, { timeout: 2000 })

      // Wait for reconnect
      await waitFor(() => {
        expect(result.current.isConnected).toBe(true)
      }, { timeout: 5000 })

      expect(onDisconnected).toHaveBeenCalled()
      expect(onConnected).toHaveBeenCalledTimes(2) // Initial + reconnect
    })
  })

  describe('Multi-Agent event handling', () => {
    it('should handle complete multi-agent review workflow via WebSocket', async () => {
      const onMultiAgentEvent = vi.fn()
      const events: MultiAgentWebSocketEvent[] = []
      
      const { result } = renderHook(() => 
        useMultiAgentReview({
          enable_websocket: true,
          enable_debug_logs: true,
          on_review_started: vi.fn(),
          on_status_update: vi.fn(),
          on_review_completed: vi.fn(),
          on_error: vi.fn()
        })
      )

      // Setup WebSocket with event capture
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'workflow-test-123',
          handlers: {
            onMultiAgentEvent: (event) => {
              events.push(event)
              onMultiAgentEvent(event)
            },
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Start a review to trigger the workflow
      const request = TestDataFactory.createMultiAgentReviewRequest()
      await act(async () => {
        await result.current.startReview(request)
      })

      // Simulate the complete workflow
      act(() => {
        mockWebSocket.simulateSuccessfulReview('workflow-test-123')
      })

      // Wait for events to be processed
      await waitFor(() => {
        expect(events.length).toBeGreaterThan(10) // Should have many events
      }, { timeout: 15000 })

      // Verify event sequence
      expect(events.some(e => e.event_type === 'review_started')).toBe(true)
      expect(events.some(e => e.event_type === 'agent_started')).toBe(true)
      expect(events.some(e => e.event_type === 'agent_progress')).toBe(true)
      expect(events.some(e => e.event_type === 'agent_completed')).toBe(true)
      expect(events.some(e => e.event_type === 'review_completed')).toBe(true)

      // Verify events are in chronological order
      const timestamps = events.map(e => e.timestamp)
      const sortedTimestamps = [...timestamps].sort((a, b) => a - b)
      expect(timestamps).toEqual(sortedTimestamps)
    })

    it('should handle agent-specific events correctly', async () => {
      const agentEvents: Record<string, MultiAgentWebSocketEvent[]> = {}
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'agent-test-123',
          handlers: {
            onMultiAgentEvent: (event) => {
              if (event.agent_type) {
                if (!agentEvents[event.agent_type]) {
                  agentEvents[event.agent_type] = []
                }
                agentEvents[event.agent_type].push(event)
              }
            },
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Simulate specific agent events
      const testAgents = ['acceptance_criteria', 'bug_detection', 'security_analysis']
      
      await act(async () => {
        for (const agent of testAgents) {
          mockWebSocket.simulateAgentEvent('agent-test-123', agent as any, 'agent_started')
          await new Promise(resolve => setTimeout(resolve, 100))
          
          mockWebSocket.simulateAgentEvent('agent-test-123', agent as any, 'agent_progress', { progress: 50 })
          await new Promise(resolve => setTimeout(resolve, 100))
          
          mockWebSocket.simulateAgentEvent('agent-test-123', agent as any, 'agent_completed')
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      })

      await waitFor(() => {
        expect(Object.keys(agentEvents)).toHaveLength(3)
      })

      // Verify each agent received the correct events
      testAgents.forEach(agent => {
        expect(agentEvents[agent]).toBeDefined()
        expect(agentEvents[agent]).toHaveLength(3) // started, progress, completed
        
        const eventTypes = agentEvents[agent].map(e => e.event_type)
        expect(eventTypes).toContain('agent_started')
        expect(eventTypes).toContain('agent_progress')
        expect(eventTypes).toContain('agent_completed')
      })
    })

    it('should handle review failure events', async () => {
      const errorEvents: MultiAgentWebSocketEvent[] = []
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'failure-test-123',
          handlers: {
            onMultiAgentEvent: (event) => {
              if (event.event_type === 'agent_failed' || event.event_type === 'review_failed') {
                errorEvents.push(event)
              }
            },
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Simulate failed review workflow
      act(() => {
        mockWebSocket.simulateFailedReview('failure-test-123')
      })

      await waitFor(() => {
        expect(errorEvents.length).toBeGreaterThan(0)
      }, { timeout: 5000 })

      // Verify failure events
      expect(errorEvents.some(e => e.event_type === 'agent_failed')).toBe(true)
      expect(errorEvents.some(e => e.event_type === 'review_failed')).toBe(true)

      // Verify error data structure
      const agentFailedEvent = errorEvents.find(e => e.event_type === 'agent_failed')
      expect(agentFailedEvent?.data).toHaveProperty('error_message')
      expect(agentFailedEvent?.agent_type).toBeDefined()
    })
  })

  describe('WebSocket message handling and parsing', () => {
    it('should parse WebSocket messages correctly', async () => {
      const parsedEvents: any[] = []
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: (event) => {
              parsedEvents.push(event)
            },
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Send a raw message
      const testMessage = {
        review_id: 'parse-test-123',
        event_type: 'agent_started',
        agent_type: 'bug_detection',
        timestamp: Date.now(),
        data: { started_at: new Date().toISOString() }
      }

      act(() => {
        mockWebSocket.dispatchEvent(new MessageEvent('message', {
          data: JSON.stringify(testMessage)
        }))
      })

      await waitFor(() => {
        expect(parsedEvents.length).toBeGreaterThan(0)
      })

      // Verify the message was parsed correctly
      const receivedEvent = parsedEvents.find(e => e.review_id === 'parse-test-123')
      expect(receivedEvent).toMatchObject(testMessage)
    })

    it('should handle malformed WebSocket messages gracefully', async () => {
      const errorHandler = vi.fn()
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: errorHandler,
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Send malformed JSON
      act(() => {
        mockWebSocket.dispatchEvent(new MessageEvent('message', {
          data: '{ invalid json }'
        }))
      })

      // Should not crash and may trigger error handler
      // The exact behavior depends on implementation
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Verify system remains stable
      expect(mockWebSocket.readyState).toBe(WebSocket.OPEN)
    })
  })

  describe('WebSocket performance and reliability', () => {
    it('should handle high-frequency message bursts', async () => {
      const receivedMessages: any[] = []
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: (event) => {
              receivedMessages.push(event)
            },
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Send burst of messages
      const messageCount = 100
      act(() => {
        for (let i = 0; i < messageCount; i++) {
          const message = {
            review_id: 'burst-test-123',
            event_type: 'agent_progress',
            agent_type: 'bug_detection',
            timestamp: Date.now() + i,
            data: { progress: i }
          }
          
          mockWebSocket.dispatchEvent(new MessageEvent('message', {
            data: JSON.stringify(message)
          }))
        }
      })

      await waitFor(() => {
        expect(receivedMessages.length).toBe(messageCount)
      }, { timeout: 5000 })

      // Verify all messages were received in order
      const progressValues = receivedMessages
        .filter(m => m.review_id === 'burst-test-123')
        .map(m => m.data.progress)
      
      expect(progressValues).toEqual(Array.from({ length: messageCount }, (_, i) => i))
    })

    it('should maintain message order during concurrent operations', async () => {
      const orderedEvents: MultiAgentWebSocketEvent[] = []
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'order-test-123',
          handlers: {
            onMultiAgentEvent: (event) => {
              orderedEvents.push(event)
            },
            onLegacyEvent: vi.fn(),
            onAnyEvent: vi.fn(),
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Start multiple agent workflows simultaneously
      const agents = ['acceptance_criteria', 'bug_detection', 'security_analysis']
      
      act(() => {
        agents.forEach((agent, index) => {
          setTimeout(() => {
            mockWebSocket.simulateAgentEvent('order-test-123', agent as any, 'agent_started')
          }, index * 50)
          
          setTimeout(() => {
            mockWebSocket.simulateAgentEvent('order-test-123', agent as any, 'agent_progress', { progress: 50 })
          }, index * 50 + 100)
          
          setTimeout(() => {
            mockWebSocket.simulateAgentEvent('order-test-123', agent as any, 'agent_completed')
          }, index * 50 + 200)
        })
      })

      await waitFor(() => {
        expect(orderedEvents.length).toBe(agents.length * 3)
      }, { timeout: 2000 })

      // Verify events are in chronological order
      const timestamps = orderedEvents.map(e => e.timestamp)
      const sortedTimestamps = [...timestamps].sort((a, b) => a - b)
      expect(timestamps).toEqual(sortedTimestamps)
    })
  })

  describe('WebSocket subscription management', () => {
    it('should handle subscription to specific review channels', async () => {
      const subscriptionMessages: any[] = []
      
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'subscription-test-123',
          sessionId: 'session-123',
          handlers: {
            onMultiAgentEvent: vi.fn(),
            onLegacyEvent: vi.fn(),
            onAnyEvent: (event) => {
              if (event.type === 'subscription_confirmed') {
                subscriptionMessages.push(event)
              }
            },
            onError: vi.fn(),
            onConnected: vi.fn(),
            onDisconnected: vi.fn()
          },
          autoConnect: true
        })
      )

      await waitFor(() => {
        const instances = MockWebSocketFactory.getInstances()
        expect(instances.length).toBeGreaterThan(0)
      })

      mockWebSocket = MockWebSocketFactory.getInstances()[0]

      // Send subscription message
      act(() => {
        mockWebSocket.send(JSON.stringify({
          type: 'subscribe',
          channel: 'multi-agent-review',
          review_id: 'subscription-test-123'
        }))
      })

      await waitFor(() => {
        expect(subscriptionMessages.length).toBe(1)
      })

      expect(subscriptionMessages[0]).toMatchObject({
        type: 'subscription_confirmed',
        channel: 'multi-agent-review',
        review_id: 'subscription-test-123'
      })
    })
  })
})