@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Twitch-style dark theme */
    --background: 240 11% 7%; /* Very dark gray #0e0e10 */
    --foreground: 0 0% 98%; /* Almost white #efeff1 */
    
    /* Cards - Twitch style elevated surfaces */
    --card: 240 11% 11%; /* Dark elevated #18181b */
    --card-foreground: 0 0% 98%;
    
    /* Popovers */
    --popover: 240 11% 11%;
    --popover-foreground: 0 0% 98%;
    
    /* Primary - Twitch purple */
    --primary: 265 89% 67%; /* Twitch purple #9147ff */
    --primary-foreground: 0 0% 100%;
    
    /* Secondary - Darker surfaces */
    --secondary: 240 11% 15%; /* Darker gray #1f1f23 */
    --secondary-foreground: 0 0% 85%; /* Light gray text */
    
    /* Muted elements */
    --muted: 240 11% 20%; /* Muted surfaces #2a2a2d */
    --muted-foreground: 240 6% 65%; /* Muted text #adadb8 */
    
    /* Accent - Twitch purple variations */
    --accent: 265 89% 67%; /* Same as primary */
    --accent-foreground: 0 0% 100%;
    
    /* Destructive */
    --destructive: 0 72% 51%; /* Red for errors */
    --destructive-foreground: 0 0% 100%;
    
    /* Borders - subtle in dark theme */
    --border: 240 11% 25%; /* Subtle borders #3a3a3d */
    --input: 240 11% 25%;
    --ring: 265 89% 67%; /* Purple focus ring */
    
    /* Radius */
    --radius: 0.75rem; /* Slightly larger for modern look */
    
    /* Custom glassmorphism variables */
    --glass-bg: 255 255 255 / 0.8;
    --glass-border: 255 255 255 / 0.2;
    --shadow-soft: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-medium: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-large: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .dark {
    /* Dark mode - Modern dark palette with HSL values */
    --background: 240 10% 4%; /* Almost black */
    --foreground: 0 0% 96%; /* Off white */
    
    --card: 240 10% 9%; /* Zinc-900 */
    --card-foreground: 0 0% 96%;
    
    --popover: 240 10% 9%;
    --popover-foreground: 0 0% 96%;
    
    --primary: 238 78% 77%; /* Indigo-400 */
    --primary-foreground: 240 10% 4%;
    
    --secondary: 240 6% 16%; /* Zinc-800 */
    --secondary-foreground: 240 5% 84%; /* Zinc-300 */
    
    --muted: 240 6% 16%; /* Zinc-800 */
    --muted-foreground: 240 5% 65%; /* Zinc-400 */
    
    --accent: 240 6% 16%;
    --accent-foreground: 240 5% 84%;
    
    --destructive: 0 62% 73%; /* Red-400 */
    --destructive-foreground: 240 10% 4%;
    
    --border: 240 6% 16%; /* Zinc-800 */
    --input: 240 6% 16%;
    --ring: 238 78% 77%;
    
    /* Dark glassmorphism */
    --glass-bg: 255 255 255 / 0.05;
    --glass-border: 255 255 255 / 0.1;
    --shadow-soft: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-medium: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-large: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
    @apply antialiased;
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }
}

/* Modern custom styles for the review comment responder */

/* Loading skeleton animations */
@layer utilities {
  .skeleton {
    @apply animate-pulse bg-gradient-to-r from-muted/50 via-muted/80 to-muted/50 bg-[length:200%_100%];
    animation: skeleton-shimmer 2s infinite;
  }
  
  .glass-card {
    @apply bg-card/80 backdrop-blur-md border border-border/20 shadow-lg;
  }
  
  .floating-button {
    @apply shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 active:scale-95;
  }
  
  .glow-primary {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  
  .glow-success {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  
  .glow-warning {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }
  
  .glow-error {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
}

@keyframes skeleton-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Code blocks with modern styling */
.code-block {
  @apply bg-gradient-to-br from-slate-900 to-slate-800 text-slate-200 p-6 rounded-2xl overflow-x-auto font-mono text-sm border border-slate-700/50 shadow-xl;
}

/* Diff lines with subtle styling */
.diff-line-add {
  @apply bg-gradient-to-r from-green-50 to-green-100/50 text-green-900 border-l-4 border-green-400 backdrop-blur-sm;
}

.diff-line-remove {
  @apply bg-gradient-to-r from-red-50 to-red-100/50 text-red-900 border-l-4 border-red-400 backdrop-blur-sm;
}

.diff-line-context {
  @apply bg-gradient-to-r from-muted/30 to-muted/10 text-muted-foreground;
}

/* Enhanced image styling */
.comment-image {
  @apply max-w-full h-auto rounded-2xl shadow-lg cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  max-height: 400px;
  object-fit: contain;
}

/* Modern toast notifications */
.toast {
  @apply fixed bottom-6 right-6 bg-gradient-to-r from-primary to-primary/90 text-primary-foreground px-6 py-4 rounded-2xl shadow-2xl backdrop-blur-md border border-white/20;
  transform: translateX(400px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  @apply from-green-500 to-green-600;
}

.toast.warning {
  @apply from-yellow-500 to-yellow-600;
}

.toast.error {
  @apply from-red-500 to-red-600;
}

/* Scroll improvements */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--muted)) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(var(--muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--muted-foreground));
}

/* Gradient text utilities */
.gradient-text-primary {
  @apply bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Modern radial gradient utilities */
.bg-gradient-radial {
  background-image: radial-gradient(circle at center, var(--tw-gradient-stops));
}

.bg-gradient-radial-at-tl {
  background-image: radial-gradient(circle at top left, var(--tw-gradient-stops));
}

.bg-gradient-radial-at-tr {
  background-image: radial-gradient(circle at top right, var(--tw-gradient-stops));
}

.bg-gradient-radial-at-bl {
  background-image: radial-gradient(circle at bottom left, var(--tw-gradient-stops));
}

.bg-gradient-radial-at-br {
  background-image: radial-gradient(circle at bottom right, var(--tw-gradient-stops));
}