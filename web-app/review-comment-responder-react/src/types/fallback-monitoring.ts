/**
 * TypeScript Interfaces für Fallback-Monitoring der Claude AI Integration
 */

export interface AIAnalysisMetadata {
  quality_score_analysis?: QualityScoreAnalysis;
  duplication_analysis?: DuplicationAnalysis;
  has_fallback_calculations?: boolean;
  analysis_timestamp?: string;
}

export interface QualityScoreAnalysis {
  quality_score: number;
  claude_analysis: boolean;
  calculation_method: string;
  
  // Claude AI specific fields
  ai_reasoning?: string;
  main_issues?: string[];
  strengths?: string[];
  analysis_timestamp?: string;
  
  // Fallback specific fields
  fallback_reason?: string;
  fallback_timestamp?: string;
  calculation_details?: {
    high_issues: number;
    medium_issues: number;
    low_issues: number;
    formula: string;
  };
}

export interface DuplicationAnalysis {
  duplication_percentage: number;
  claude_analysis: boolean;
  calculation_method: string;
  
  // Claude AI specific fields
  analysis_summary?: string;
  duplicated_blocks?: DuplicatedBlock[];
  refactoring_recommendations?: string[];
  analysis_timestamp?: string;
  
  // Fallback specific fields
  fallback_reason?: string;
  fallback_timestamp?: string;
  detected_patterns?: string[];
}

export interface DuplicatedBlock {
  files: string[];
  similarity: number;
  description: string;
}

export interface EffortEstimationAnalysis {
  estimated_hours: number;
  estimated_effort: string;
  confidence_level: string;
  confidence_range: string;
  claude_analysis: boolean;
  calculation_method: string;
  
  // Claude AI specific fields
  effort_breakdown?: Record<string, any>;
  risk_factors?: string[];
  
  // Fallback specific fields
  fallback_reason?: string;
  fallback_timestamp?: string;
  calculation_breakdown?: {
    base_estimation: string;
    high_severity_impact: string;
    complexity_adjustment: string;
    final_result: string;
  };
}

export interface FallbackWarning {
  type: 'quality_score' | 'duplication_detection' | 'effort_estimation' | 'complexity_analysis';
  title: string;
  message: string;
  fallback_reason: string;
  timestamp: string;
  calculation_method: string;
}

// Extended interface for the enhanced report structure
export interface EnhancedReportWithFallbackData {
  ai_analysis_metadata?: AIAnalysisMetadata;
  effort_estimation?: EffortEstimationAnalysis;
  code_quality_analysis?: {
    executive_summary?: {
      overall_score: number;
      critical_issues: number;
      code_smells: number;
      duplication_level: number;
    };
  };
}

// Utility functions for fallback detection
export const isFallbackUsed = (metadata?: AIAnalysisMetadata, effortData?: EffortEstimationAnalysis): boolean => {
  return (
    metadata?.has_fallback_calculations === true ||
    effortData?.claude_analysis === false ||
    metadata?.quality_score_analysis?.claude_analysis === false ||
    metadata?.duplication_analysis?.claude_analysis === false
  );
};

export const getFallbackCount = (metadata?: AIAnalysisMetadata, effortData?: EffortEstimationAnalysis): number => {
  let count = 0;
  if (metadata?.quality_score_analysis?.claude_analysis === false) count++;
  if (metadata?.duplication_analysis?.claude_analysis === false) count++;
  if (effortData?.claude_analysis === false) count++;
  return count;
};