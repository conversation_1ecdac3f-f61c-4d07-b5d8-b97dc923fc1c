/**
 * Multi-Agent Review Service Types
 * Integration with Multi-Agent Code Reviewer on Port 5000
 */

// Re-export existing types for compatibility
export type { 
  AgentStatus, 
  MultiAgentReviewSession, 
  AgentResult, 
  MultiAgentReviewResult 
} from './enhanced-review'

// Import types for use in this file
import type { AgentStatus, AgentResult } from './enhanced-review'

// Agent Type Union - 7 parallel agents
export type AgentType = 
  | 'acceptance_criteria'
  | 'bug_detection'
  | 'security_analysis'
  | 'logic_analysis'
  | 'quality_analysis'
  | 'architecture_analysis'
  | 'summary'

// Review Mode Types - Must match backend ReviewMode enum
export type ReviewMode = 'quick' | 'full' | 'ac_only' | 'bug_analysis' | 'summary_only'

export type ReviewStatus = 'started' | 'running' | 'completed' | 'failed' | 'cancelled'

// Multi-Agent Review Request
export interface MultiAgentReviewRequest {
  branch_name: string
  repository_path: string
  pr_url: string
  review_mode: ReviewMode
  agent_config?: {
    enabled_agents: AgentType[]
    timeout_seconds?: number
    priority?: 'high' | 'normal' | 'low'
    concurrent_agents?: number
  }
  jira_ticket?: {
    ticket_id: string
    summary: string
    description: string
    acceptance_criteria: string[]
  }
  context_config?: {
    include_git_history?: boolean
    include_related_files?: boolean
    max_context_size?: number
  }
}

// Multi-Agent Review Response (start review)
export interface MultiAgentReviewResponse {
  review_id: string
  session_id: string
  status: ReviewStatus
  initial_agent_statuses: Record<string, AgentStatus>
  estimated_completion_time: number
  websocket_session_id: string
  context_preparation_status?: Record<string, string>
  created_at: string
}

// Review Status Response (get status)
export interface ReviewStatusResponse {
  review_id: string
  session_id: string
  status: ReviewStatus
  progress: number
  agent_statuses: Record<string, AgentStatus>
  active_agents: string[]
  completed_agents: string[]
  failed_agents: string[]
  context_status?: Record<string, string>
  estimated_remaining_time?: number
  started_at: string
  completed_at?: string
  error_message?: string
}

// Enhanced Review Results Response (get results)
export interface EnhancedReviewResults {
  review_id: string
  session_id: string
  status: ReviewStatus
  overall_results: {
    summary: string
    priority_findings: Array<{
      priority: 'high' | 'medium' | 'low'
      type: string
      description: string
      agent_type: AgentType
      file?: string
      line?: number
      suggestion?: string
    }>
    execution_metrics: {
      total_execution_time: number
      parallel_efficiency: number
      agent_performance: Record<string, {
        execution_time: number
        success: boolean
        findings_count: number
      }>
    }
  }
  agent_results: Record<string, AgentResult>
  reports: {
    markdown: string
    html?: string
    json: string
  }
  jira_compliance?: {
    total_criteria: number
    fulfilled_criteria: number
    compliance_percentage: number
    unfulfilled_criteria: Array<{
      criterion: string
      status: 'NOT_FULFILLED' | 'PARTIAL'
      explanation: string
    }>
  }
  context_metadata: {
    files_analyzed: string[]
    context_size: number
    git_history_included: boolean
    related_files_included: boolean
  }
  created_at: string
  completed_at: string
}

// Service Health Check Response
export interface ServiceHealthResponse {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  service_info: {
    version: string
    uptime: number
    active_reviews: number
    available_agents: AgentType[]
  }
  agent_status: Record<AgentType, {
    available: boolean
    last_health_check: string
    response_time_ms?: number
  }>
  system_metrics?: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
  }
}

// Agent Configuration
export interface AgentConfiguration {
  agent_type: AgentType
  enabled: boolean
  priority: number
  timeout_seconds: number
  config: Record<string, unknown>
}

// WebSocket Event Types for Multi-Agent
export interface MultiAgentWebSocketEvent {
  event_type: 'review_started' | 'review_completed' | 'review_failed' | 'review_cancelled' |
             'agent_started' | 'agent_progress' | 'agent_completed' | 'agent_failed' |
             'context_ready' | 'heartbeat' | 'error'
  timestamp: number
  review_id: string
  session_id?: string
  agent_type?: AgentType
  data?: Record<string, unknown>
  message?: string
}

// Multi-Agent Progress Update
export interface MultiAgentProgressUpdate {
  review_id: string
  overall_progress: number
  agent_updates: Array<{
    agent_type: AgentType
    status: AgentStatus['status']
    progress: number
    message?: string
    estimated_remaining_time?: number
  }>
  active_agents: AgentType[]
  completed_agents: AgentType[]
  failed_agents: AgentType[]
}

// Error Types
export interface MultiAgentError {
  error_code: string
  error_message: string
  error_details?: Record<string, unknown>
  review_id?: string
  agent_type?: AgentType
  timestamp: string
  retry_possible: boolean
}

// Service Configuration
export interface MultiAgentServiceConfig {
  api_base_url: string
  websocket_url: string
  timeout_ms: number
  retry_config: {
    max_retries: number
    retry_delay_ms: number
    exponential_backoff: boolean
  }
  agent_config: {
    concurrent_limit: number
    default_timeout_seconds: number
    enabled_agents: AgentType[]
  }
}

// Review Context
export interface ReviewContext {
  review_id: string
  branch_info: {
    name: string
    commit_hash: string
    base_branch: string
  }
  pr_context?: {
    id: string
    title: string
    description: string
    author: string
    created_at: string
  }
  jira_context?: {
    ticket_id: string
    summary: string
    description: string
    acceptance_criteria: string[]
    priority: string
    status: string
  }
  files_context: {
    changed_files: Array<{
      path: string
      status: 'added' | 'modified' | 'deleted' | 'renamed'
      additions: number
      deletions: number
    }>
    total_files: number
    total_changes: number
  }
}

// Performance Metrics
export interface MultiAgentPerformanceMetrics {
  review_id: string
  total_execution_time: number
  agent_metrics: Record<AgentType, {
    start_time: string
    end_time: string
    execution_time: number
    success: boolean
    findings_generated: number
    tokens_processed?: number
    api_calls_made?: number
  }>
  parallel_efficiency: number
  resource_usage: {
    peak_memory_mb: number
    cpu_time_seconds: number
    network_requests: number
  }
  comparison_with_sequential: {
    estimated_sequential_time: number
    time_saved: number
    efficiency_gain_percentage: number
  }
}

// Export common patterns for service responses
export interface MultiAgentServiceResponse<T = unknown> {
  success: boolean
  data?: T
  error?: MultiAgentError
  timestamp: string
  request_id?: string
}

// Helper type for API calls with standard error handling
export type MultiAgentApiCall<T> = Promise<MultiAgentServiceResponse<T>>