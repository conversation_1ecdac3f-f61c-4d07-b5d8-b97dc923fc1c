/**
 * Enhanced Review Interface - ALIGNED WITH ACTUAL BACKEND RESPONSE
 * Based on backend/core/services/review_service.py:319-397
 */

import type { 
  AIAnalysisMetadata, 
  EffortEstimationAnalysis 
} from './fallback-monitoring';

export interface ReviewFinding {
  text: string;
  severity: 'high' | 'medium' | 'low';
  file?: string;
  line?: number;
  suggestion?: string;
}

/**
 * ACTUAL Backend Response Structure from review_service.py
 * This matches what the backend actually returns
 */
export interface EnhancedReviewResults {
  session_id: string;
  review_mode: string;
  branch_name: string;
  pr_url?: string;
  worktree_path?: string;
  timestamp: string;
  raw_review: string;
  metadata: {
    changed_files: string[];
    diff_summary?: string;
    file_count: number;
  };
  jira_ticket?: {
    ticket_id: string;
    summary: string;
    status: string;
    acceptance_criteria_count: number;
    acceptance_criteria: string[];
  };
  structured_findings: {
    acceptance_criteria: ReviewFinding[];
    code_quality: ReviewFinding[];
    security_issues: ReviewFinding[];
    performance_issues: ReviewFinding[];
    bugs: ReviewFinding[];
    suggestions: ReviewFinding[];
  };
  summary: {
    total_findings: number;
    high_severity_count: number;
    files_changed: number;
    review_length: number;
    categories: Record<string, number>;
    completion_status: string;
  };
  
  // NOTE: enhanced_report is NOT returned by current backend
  // It exists in old API files but not in current review_service.py
  // This field is optional for backwards compatibility
  enhanced_report?: {
    ai_analysis_metadata?: AIAnalysisMetadata;
    effort_estimation?: EffortEstimationAnalysis;
    code_quality_analysis?: {
      executive_summary?: {
        overall_score: number;
        critical_issues: number;
        code_smells: number;
        duplication_level: number;
      };
    };
  };
}

import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

export interface ReviewSession {
  session_id: string;
  branch_name: string;
  pr_url?: string;
  status: 'initializing' | 'running' | 'completed' | 'error';
  progress: number;
  progress_message?: string;
  created_at: string;
  completed_at?: string;
  results?: StructuredReviewResult | EnhancedReviewResults;
  error?: string;
  worktree_path?: string;
  structured_summary?: any; // For SDK-based results
  tutorial?: any; // For Phase 3 results
}