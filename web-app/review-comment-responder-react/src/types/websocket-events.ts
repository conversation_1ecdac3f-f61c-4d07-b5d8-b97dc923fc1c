/**
 * WebSocket Event Types
 * Comprehensive type definitions for all WebSocket events across services
 */

import type { AgentType, AgentStatus, ReviewStatus } from './multi-agent'

/**
 * Base WebSocket Event Structure
 */
export interface BaseWebSocketEvent {
  event_type: string
  timestamp: number
  data?: Record<string, unknown>
  message?: string
}

/**
 * Multi-Agent WebSocket Events (Port 5000)
 */
export namespace MultiAgentWebSocketEvents {
  
  // Review-level events
  export interface ReviewStartedEvent extends BaseWebSocketEvent {
    event_type: 'review_started'
    review_id: string
    session_id: string
    data: {
      agents_count: number
      estimated_duration: number
      enabled_agents: AgentType[]
    }
  }

  export interface ReviewCompletedEvent extends BaseWebSocketEvent {
    event_type: 'review_completed'
    review_id: string
    session_id: string
    data: {
      total_duration: number
      agents_completed: number
      agents_failed: number
      results_available: boolean
    }
  }

  export interface ReviewFailedEvent extends BaseWebSocketEvent {
    event_type: 'review_failed'
    review_id: string
    session_id: string
    data: {
      error_code: string
      error_message: string
      failed_agents: AgentType[]
      partial_results_available: boolean
    }
  }

  export interface ReviewCancelledEvent extends BaseWebSocketEvent {
    event_type: 'review_cancelled'
    review_id: string
    session_id: string
    data: {
      cancelled_by: 'user' | 'system' | 'timeout'
      reason?: string
      completed_agents: AgentType[]
    }
  }

  // Agent-specific events
  export interface AgentStartedEvent extends BaseWebSocketEvent {
    event_type: 'agent_started'
    review_id: string
    agent_type: AgentType
    data: {
      estimated_duration: number
      priority: number
      dependencies?: AgentType[]
    }
  }

  export interface AgentProgressEvent extends BaseWebSocketEvent {
    event_type: 'agent_progress'
    review_id: string
    agent_type: AgentType
    data: {
      progress: number // 0-100
      current_step: string
      estimated_remaining_time?: number
      findings_count?: number
    }
  }

  export interface AgentCompletedEvent extends BaseWebSocketEvent {
    event_type: 'agent_completed'
    review_id: string
    agent_type: AgentType
    data: {
      execution_time: number
      findings_count: number
      success: boolean
      result_summary: string
    }
  }

  export interface AgentFailedEvent extends BaseWebSocketEvent {
    event_type: 'agent_failed'
    review_id: string
    agent_type: AgentType
    data: {
      error_code: string
      error_message: string
      execution_time: number
      retry_possible: boolean
    }
  }

  // Context preparation events
  export interface ContextReadyEvent extends BaseWebSocketEvent {
    event_type: 'context_ready'
    review_id: string
    data: {
      context_type: 'git_history' | 'related_files' | 'jira_ticket' | 'pr_metadata'
      status: 'ready' | 'failed' | 'partial'
      size_bytes?: number
      error_message?: string
    }
  }

  // System events
  export interface HeartbeatEvent extends BaseWebSocketEvent {
    event_type: 'heartbeat'
    data: {
      server_time: number
      active_reviews: number
      system_load: number
    }
  }

  export interface ErrorEvent extends BaseWebSocketEvent {
    event_type: 'error'
    review_id?: string
    data: {
      error_code: string
      error_message: string
      error_type: 'network' | 'service' | 'validation' | 'system'
      recovery_possible: boolean
    }
  }

  // Status update events (consolidated)
  export interface StatusUpdateEvent extends BaseWebSocketEvent {
    event_type: 'status_update'
    review_id: string
    data: {
      status: ReviewStatus
      progress: number
      agent_statuses: Record<string, AgentStatus>
      active_agents: AgentType[]
      completed_agents: AgentType[]
      failed_agents: AgentType[]
      estimated_remaining_time?: number
    }
  }
}

/**
 * Legacy WebSocket Events (Port 5002 & 5001)
 */
export namespace LegacyWebSocketEvents {
  
  // Session events
  export interface SessionStartedEvent extends BaseWebSocketEvent {
    event_type: 'session_started'
    session_id: string
    data: {
      branch_name: string
      repository_path: string
      review_mode: string
    }
  }

  export interface ClaudeThinkingEvent extends BaseWebSocketEvent {
    event_type: 'claude_thinking'
    session_id: string
    data: {
      thinking_step: string
      complexity_level: 'low' | 'medium' | 'high'
    }
  }

  export interface ToolUsageEvent extends BaseWebSocketEvent {
    event_type: 'tool_usage'
    session_id: string
    data: {
      tool_name: string
      action: string
      progress?: number
      parameters?: Record<string, unknown>
    }
  }

  export interface ClaudeResponseStreamEvent extends BaseWebSocketEvent {
    event_type: 'claude_response_stream'
    session_id: string
    data: {
      chunk: string
      is_final: boolean
      token_count?: number
    }
  }

  export interface StructuredResultEvent extends BaseWebSocketEvent {
    event_type: 'structured_result'
    session_id: string
    data: {
      result_type: string
      findings_count: number
      processing_complete: boolean
    }
  }

  export interface Phase3StartedEvent extends BaseWebSocketEvent {
    event_type: 'phase3_started'
    session_id: string
    data: {
      tutorial_type: string
      estimated_duration: number
    }
  }

  export interface Phase3CompletedEvent extends BaseWebSocketEvent {
    event_type: 'phase3_completed'
    session_id: string
    data: {
      tutorial_length: number
      diagrams_count: number
      code_examples_count: number
    }
  }

  export interface ReviewMetadataEvent extends BaseWebSocketEvent {
    event_type: 'review_metadata'
    session_id: string
    data: {
      files_analyzed: number
      lines_of_code: number
      complexity_score?: number
      estimated_review_time?: number
    }
  }

  export interface ReviewCompletedEvent extends BaseWebSocketEvent {
    event_type: 'review_completed'
    session_id: string
    data: {
      total_duration: number
      findings_count: number
      quality_score?: number
      tutorial_available: boolean
    }
  }

  export interface ReviewErrorEvent extends BaseWebSocketEvent {
    event_type: 'review_error'
    session_id: string
    data: {
      error_code: string
      error_message: string
      error_stage: 'initialization' | 'analysis' | 'summarization' | 'tutorial'
      recovery_suggestions?: string[]
    }
  }

  // Legacy claude_progress event (backward compatibility)
  export interface ClaudeProgressEvent extends BaseWebSocketEvent {
    event_type: 'claude_progress'
    session_id: string
    type: string // Sub-event type
    turn?: number
    tool?: string
    preview?: string
    data?: Record<string, unknown>
  }
}

/**
 * WebSocket Connection Events
 */
export namespace ConnectionEvents {
  export interface ConnectedEvent extends BaseWebSocketEvent {
    event_type: 'connected'
    data: {
      client_id: string
      server_version: string
      supported_features: string[]
    }
  }

  export interface DisconnectedEvent extends BaseWebSocketEvent {
    event_type: 'disconnected'
    data: {
      reason: string
      code: number
      reconnect_possible: boolean
    }
  }

  export interface ReconnectedEvent extends BaseWebSocketEvent {
    event_type: 'reconnected'
    data: {
      attempt_number: number
      downtime_duration: number
      missed_events_count?: number
    }
  }

  export interface ReconnectFailedEvent extends BaseWebSocketEvent {
    event_type: 'reconnect_failed'
    data: {
      max_attempts_reached: boolean
      last_error: string
      manual_reconnect_required: boolean
    }
  }
}

/**
 * Consolidated WebSocket Event Types
 */
export type MultiAgentWebSocketEvent = 
  | MultiAgentWebSocketEvents.ReviewStartedEvent
  | MultiAgentWebSocketEvents.ReviewCompletedEvent
  | MultiAgentWebSocketEvents.ReviewFailedEvent
  | MultiAgentWebSocketEvents.ReviewCancelledEvent
  | MultiAgentWebSocketEvents.AgentStartedEvent
  | MultiAgentWebSocketEvents.AgentProgressEvent
  | MultiAgentWebSocketEvents.AgentCompletedEvent
  | MultiAgentWebSocketEvents.AgentFailedEvent
  | MultiAgentWebSocketEvents.ContextReadyEvent
  | MultiAgentWebSocketEvents.HeartbeatEvent
  | MultiAgentWebSocketEvents.ErrorEvent
  | MultiAgentWebSocketEvents.StatusUpdateEvent

export type LegacyWebSocketEvent = 
  | LegacyWebSocketEvents.SessionStartedEvent
  | LegacyWebSocketEvents.ClaudeThinkingEvent
  | LegacyWebSocketEvents.ToolUsageEvent
  | LegacyWebSocketEvents.ClaudeResponseStreamEvent
  | LegacyWebSocketEvents.StructuredResultEvent
  | LegacyWebSocketEvents.Phase3StartedEvent
  | LegacyWebSocketEvents.Phase3CompletedEvent
  | LegacyWebSocketEvents.ReviewMetadataEvent
  | LegacyWebSocketEvents.ReviewCompletedEvent
  | LegacyWebSocketEvents.ReviewErrorEvent
  | LegacyWebSocketEvents.ClaudeProgressEvent

export type ConnectionWebSocketEvent =
  | ConnectionEvents.ConnectedEvent
  | ConnectionEvents.DisconnectedEvent
  | ConnectionEvents.ReconnectedEvent
  | ConnectionEvents.ReconnectFailedEvent

export type AllWebSocketEvents = 
  | MultiAgentWebSocketEvent 
  | LegacyWebSocketEvent 
  | ConnectionWebSocketEvent

/**
 * WebSocket Event Handlers
 */
export interface WebSocketEventHandlers {
  // Multi-Agent event handlers
  onMultiAgentEvent?: (event: MultiAgentWebSocketEvent) => void
  onReviewStarted?: (event: MultiAgentWebSocketEvents.ReviewStartedEvent) => void
  onReviewCompleted?: (event: MultiAgentWebSocketEvents.ReviewCompletedEvent) => void
  onReviewFailed?: (event: MultiAgentWebSocketEvents.ReviewFailedEvent) => void
  onAgentStarted?: (event: MultiAgentWebSocketEvents.AgentStartedEvent) => void
  onAgentProgress?: (event: MultiAgentWebSocketEvents.AgentProgressEvent) => void
  onAgentCompleted?: (event: MultiAgentWebSocketEvents.AgentCompletedEvent) => void
  onAgentFailed?: (event: MultiAgentWebSocketEvents.AgentFailedEvent) => void
  onStatusUpdate?: (event: MultiAgentWebSocketEvents.StatusUpdateEvent) => void

  // Legacy event handlers
  onLegacyEvent?: (event: LegacyWebSocketEvent) => void
  onSessionStarted?: (event: LegacyWebSocketEvents.SessionStartedEvent) => void
  onClaudeThinking?: (event: LegacyWebSocketEvents.ClaudeThinkingEvent) => void
  onToolUsage?: (event: LegacyWebSocketEvents.ToolUsageEvent) => void
  onLegacyReviewCompleted?: (event: LegacyWebSocketEvents.ReviewCompletedEvent) => void

  // Connection event handlers
  onConnected?: (event: ConnectionEvents.ConnectedEvent) => void
  onDisconnected?: (event: ConnectionEvents.DisconnectedEvent) => void
  onReconnected?: (event: ConnectionEvents.ReconnectedEvent) => void
  onReconnectFailed?: (event: ConnectionEvents.ReconnectFailedEvent) => void

  // Generic handlers
  onAnyEvent?: (event: AllWebSocketEvents) => void
  onError?: (error: Error, event?: AllWebSocketEvents) => void
}

/**
 * WebSocket Connection Configuration
 */
export interface WebSocketConfig {
  url: string
  transports?: ('polling' | 'websocket')[]
  forceNew?: boolean
  upgrade?: boolean
  timeout?: number
  reconnection?: boolean
  reconnectionDelay?: number
  reconnectionDelayMax?: number
  reconnectionAttempts?: number
  heartbeatInterval?: number
}

/**
 * WebSocket Connection Status
 */
export interface WebSocketConnectionStatus {
  connected: boolean
  connecting: boolean
  error: string | null
  lastHeartbeat: number | null
  reconnectAttempts: number
  connectionQuality: 'excellent' | 'good' | 'poor' | 'unknown'
  latency?: number
}

/**
 * Event Type Guards
 */
export const isMultiAgentEvent = (event: AllWebSocketEvents): event is MultiAgentWebSocketEvent => {
  return event.event_type.startsWith('review_') || 
         event.event_type.startsWith('agent_') ||
         event.event_type === 'context_ready' ||
         event.event_type === 'status_update' ||
         event.event_type === 'heartbeat' ||
         event.event_type === 'error'
}

export const isLegacyEvent = (event: AllWebSocketEvents): event is LegacyWebSocketEvent => {
  return event.event_type.startsWith('session_') ||
         event.event_type.startsWith('claude_') ||
         event.event_type.startsWith('tool_') ||
         event.event_type.startsWith('structured_') ||
         event.event_type.startsWith('phase3_') ||
         event.event_type === 'review_metadata' ||
         event.event_type === 'review_error'
}

export const isConnectionEvent = (event: AllWebSocketEvents): event is ConnectionWebSocketEvent => {
  return event.event_type === 'connected' ||
         event.event_type === 'disconnected' ||
         event.event_type === 'reconnected' ||
         event.event_type === 'reconnect_failed'
}

/**
 * Event Utilities
 */
export class WebSocketEventUtils {
  
  /**
   * Create a standardized event ID
   */
  static createEventId(eventType: string, additionalData?: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const suffix = additionalData ? `_${additionalData}` : ''
    return `${eventType}_${timestamp}_${random}${suffix}`
  }

  /**
   * Extract review/session ID from event
   */
  static extractReviewId(event: AllWebSocketEvents): string | null {
    if ('review_id' in event) {
      return event.review_id || null
    }
    if ('session_id' in event) {
      return event.session_id
    }
    return null
  }

  /**
   * Determine if event indicates completion
   */
  static isCompletionEvent(event: AllWebSocketEvents): boolean {
    return event.event_type.includes('completed') || 
           event.event_type.includes('finished') ||
           (event.event_type === 'review_completed' || 
            event.event_type === 'agent_completed')
  }

  /**
   * Determine if event indicates an error
   */
  static isErrorEvent(event: AllWebSocketEvents): boolean {
    return event.event_type.includes('error') || 
           event.event_type.includes('failed') ||
           event.event_type.includes('cancelled')
  }

  /**
   * Extract progress information from event
   */
  static extractProgress(event: AllWebSocketEvents): number | null {
    if (event.data && typeof event.data === 'object' && 'progress' in event.data && typeof event.data.progress === 'number') {
      return event.data.progress
    }
    return null
  }

  /**
   * Format event for logging
   */
  static formatForLogging(event: AllWebSocketEvents): string {
    const reviewId = this.extractReviewId(event)
    const progress = this.extractProgress(event)
    const reviewIdStr = reviewId ? `[${reviewId.substring(0, 8)}]` : ''
    const progressStr = progress !== null ? `(${progress}%)` : ''
    
    return `${reviewIdStr} ${event.event_type}${progressStr}: ${event.message || 'No message'}`
  }
}