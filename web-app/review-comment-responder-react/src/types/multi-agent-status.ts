/**
 * Multi-Agent Status Types for React State Management
 * Specific types for managing Multi-Agent Review state in React components
 */

import type { 
  AgentType, 
  ReviewStatus, 
  MultiAgentReviewResponse,
  ReviewStatusResponse,
  EnhancedReviewResults,
  MultiAgentError,
  AgentResult
} from './multi-agent'
import type { AgentStatus } from './enhanced-review'

// Enhanced Multi-Agent Review Status for React State
export interface MultiAgentReviewState {
  // Core identifiers
  review_id: string | null
  session_id: string | null
  
  // Current status
  status: ReviewStatus
  progress: number
  
  // Agent states
  agent_statuses: Record<AgentType, AgentStatus>
  active_agents: AgentType[]
  completed_agents: AgentType[]
  failed_agents: AgentType[]
  
  // Timing information
  started_at?: string
  completed_at?: string
  estimated_remaining_time?: number
  
  // Context and configuration
  context_status?: Record<string, string>
  enabled_agents: AgentType[]
  
  // Results (populated when completed)
  results?: EnhancedReviewResults
  
  // Error handling
  error?: MultiAgentError
  last_error?: string
  
  // UI state
  is_loading: boolean
  is_starting: boolean
  is_cancelling: boolean
  
  // Performance metrics
  performance_metrics?: {
    parallel_efficiency: number
    total_execution_time: number
    agent_completion_order: AgentType[]
  }
}

// Hook Actions
export type MultiAgentAction = 
  | { type: 'START_REVIEW'; payload: { request: any } }
  | { type: 'REVIEW_STARTED'; payload: MultiAgentReviewResponse }
  | { type: 'STATUS_UPDATE'; payload: ReviewStatusResponse }
  | { type: 'AGENT_STARTED'; payload: { agent_type: AgentType; started_at: string } }
  | { type: 'AGENT_PROGRESS'; payload: { agent_type: AgentType; progress: number; message?: string } }
  | { type: 'AGENT_COMPLETED'; payload: { agent_type: AgentType; completed_at: string; result?: AgentResult } }
  | { type: 'AGENT_FAILED'; payload: { agent_type: AgentType; error: string } }
  | { type: 'REVIEW_COMPLETED'; payload: EnhancedReviewResults }
  | { type: 'REVIEW_FAILED'; payload: { error: MultiAgentError } }
  | { type: 'REVIEW_CANCELLED'; payload: { reason?: string } }
  | { type: 'SET_LOADING'; payload: { loading: boolean } }
  | { type: 'SET_ERROR'; payload: { error: string } }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET_STATE' }

// Hook Configuration
export interface UseMultiAgentReviewConfig {
  // Service configuration
  auto_start?: boolean
  auto_fetch_results?: boolean
  polling_interval?: number
  
  // WebSocket configuration
  enable_websocket?: boolean
  websocket_reconnect?: boolean
  
  // Error handling
  max_retries?: number
  retry_delay?: number
  
  // Performance monitoring
  track_performance?: boolean
  enable_debug_logs?: boolean
  
  // UI behavior
  auto_advance_on_completion?: boolean
  show_agent_details?: boolean
  
  // Callback configurations
  on_review_started?: (response: MultiAgentReviewResponse) => void
  on_status_update?: (status: ReviewStatusResponse) => void
  on_agent_completed?: (agent_type: AgentType, result?: AgentResult) => void
  on_review_completed?: (results: EnhancedReviewResults) => void
  on_error?: (error: MultiAgentError) => void
}

// Hook Return Type
export interface UseMultiAgentReviewResult {
  // State
  state: MultiAgentReviewState
  
  // Actions
  startReview: (request: any) => Promise<void>
  cancelReview: () => Promise<void>
  retryReview: () => Promise<void>
  refreshStatus: () => Promise<void>
  fetchResults: () => Promise<void>
  
  // Computed values
  isActive: boolean
  isCompleted: boolean
  isFailed: boolean
  hasError: boolean
  overallProgress: number
  activeAgentCount: number
  completedAgentCount: number
  failedAgentCount: number
  
  // Agent utilities
  getAgentStatus: (agent_type: AgentType) => AgentStatus | undefined
  getAgentProgress: (agent_type: AgentType) => number
  isAgentActive: (agent_type: AgentType) => boolean
  isAgentCompleted: (agent_type: AgentType) => boolean
  isAgentFailed: (agent_type: AgentType) => boolean
  
  // Performance utilities
  getExecutionTime: () => number | null
  getParallelEfficiency: () => number | null
  getAgentExecutionTime: (agent_type: AgentType) => number | null
  
  // Error utilities
  clearError: () => void
  getLastError: () => string | null
  
  // Reset utilities
  reset: () => void
}

// Status Update Event (for WebSocket)
export interface MultiAgentStatusUpdateEvent {
  review_id: string
  event_type: 'status_update'
  timestamp: number
  data: {
    status: ReviewStatus
    progress: number
    agent_statuses: Record<AgentType, AgentStatus>
    active_agents: AgentType[]
    completed_agents: AgentType[]
    failed_agents: AgentType[]
    estimated_remaining_time?: number
  }
}

// Agent Progress Event (for WebSocket)
export interface MultiAgentProgressEvent {
  review_id: string
  agent_type: AgentType
  event_type: 'agent_progress'
  timestamp: number
  data: {
    progress: number
    current_step?: string
    estimated_remaining_time?: number
    intermediate_findings?: number
  }
}

// Agent Completion Event (for WebSocket)
export interface MultiAgentCompletionEvent {
  review_id: string
  agent_type: AgentType
  event_type: 'agent_completed'
  timestamp: number
  data: {
    execution_time: number
    findings_count: number
    success: boolean
    result_preview?: {
      summary: string
      priority_findings: number
    }
  }
}

// Agent Status Statistics (for UI)
export interface AgentStatusStats {
  total_agents: number
  pending_agents: number
  running_agents: number
  completed_agents: number
  failed_agents: number
  skipped_agents: number
  success_rate: number
  average_execution_time: number
  fastest_agent?: {
    agent_type: AgentType
    execution_time: number
  }
  slowest_agent?: {
    agent_type: AgentType
    execution_time: number
  }
}

// Review Session State (for integration with existing session management)
export interface MultiAgentSessionState {
  session_id: string
  review_id: string
  status: ReviewStatus
  progress: number
  progress_message: string
  branch_name: string
  pr_url?: string
  started_at: string
  completed_at?: string
  error?: string
  
  // Multi-agent specific
  multi_agent_status: MultiAgentReviewState
  agent_count: number
  parallel_execution: boolean
  
  // Integration with legacy session format
  worktree_path?: string
  jira_ticket?: {
    ticket_id: string
    summary: string
  }
}

// Service Integration Types
export interface MultiAgentServiceIntegration {
  service_health: 'healthy' | 'unhealthy' | 'unknown'
  service_available: boolean
  last_health_check: string | null
  fallback_to_legacy: boolean
  connection_quality: 'excellent' | 'good' | 'poor' | 'unknown'
  websocket_connected: boolean
  api_response_time: number | null
}

// Feature Flag Integration
export interface MultiAgentFeatureFlags {
  enabled: boolean
  force_multi_agent: boolean
  auto_fallback: boolean
  debug_mode: boolean
  performance_tracking: boolean
  websocket_enabled: boolean
  agent_selection: {
    [K in AgentType]: boolean
  }
  experimental_features: {
    parallel_context_preparation: boolean
    advanced_error_recovery: boolean
    real_time_performance_monitoring: boolean
  }
}

// Default initial state
export const DEFAULT_MULTI_AGENT_STATE: MultiAgentReviewState = {
  review_id: null,
  session_id: null,
  status: 'started' as ReviewStatus,
  progress: 0,
  agent_statuses: {} as Record<AgentType, AgentStatus>,
  active_agents: [],
  completed_agents: [],
  failed_agents: [],
  enabled_agents: [
    'acceptance_criteria',
    'bug_detection', 
    'security_analysis',
    'logic_analysis',
    'quality_analysis',
    'architecture_analysis',
    'summary'
  ],
  is_loading: false,
  is_starting: false,
  is_cancelling: false
}

// Agent configuration mapping (for UI display)
export const AGENT_DISPLAY_CONFIG = {
  acceptance_criteria: {
    name: 'Acceptance Criteria',
    short_name: 'AC',
    description: 'Validates JIRA acceptance criteria compliance',
    priority: 1,
    estimated_duration: 45,
    color: 'blue'
  },
  bug_detection: {
    name: 'Bug Detection',
    short_name: 'Bugs',
    description: 'Scans for potential bugs and errors',
    priority: 2,
    estimated_duration: 60,
    color: 'red'
  },
  security_analysis: {
    name: 'Security Analysis',
    short_name: 'Security',
    description: 'Identifies security vulnerabilities',
    priority: 3,
    estimated_duration: 90,
    color: 'orange'
  },
  logic_analysis: {
    name: 'Logic Analysis',
    short_name: 'Logic',
    description: 'Analyzes code logic and flow',
    priority: 4,
    estimated_duration: 75,
    color: 'purple'
  },
  quality_analysis: {
    name: 'Code Quality',
    short_name: 'Quality',
    description: 'Evaluates code quality and standards',
    priority: 5,
    estimated_duration: 55,
    color: 'green'
  },
  architecture_analysis: {
    name: 'Architecture',
    short_name: 'Arch',
    description: 'Reviews architectural patterns',
    priority: 6,
    estimated_duration: 80,
    color: 'indigo'
  },
  summary: {
    name: 'Summary',
    short_name: 'Summary',
    description: 'Generates implementation summary',
    priority: 7,
    estimated_duration: 30,
    color: 'gray'
  }
} as const