export interface Comment {
  id: number
  file: File | null
  dataUrl: string
  fileName: string
  uploadTime: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  response?: CommentResponse | null
  extractedData?: {
    file: string
    line: string
  } | null
  previewChanges?: PreviewChange[]
  needsApproval?: boolean
  pendingCodeChanges?: CodeChange[]
  appliedChanges?: PreviewChange[]
  session_id?: string
  fileStatus?: Record<string, 'pending' | 'accepted' | 'rejected'>
  // New field for file-specific responses
  fileSpecificResponses?: Record<string, {
    response: CommentResponse
    previewChanges: PreviewChange[]
    timestamp: string
  }>
}

export interface CommentResponse {
  commentText?: string
  comment_text?: string
  file: string
  line: string
  text?: string
  response?: string
  confidence: number
  code_changes?: CodeChange[]
  preview_changes?: PreviewChange[]
  preview_mode?: boolean
  analysis?: {
    affected_files?: string[]
    dependencies?: string[]
    impact?: string
    todo_list?: string[]
  }
}

export interface PreviewChange {
  file: string
  reason?: string
  changes: Array<{
    type: 'replace'
    line_start: number
    line_end: number
    current_content: string
    suggested_content: string
    explanation?: string
  }>
}

export interface CodeChange {
  file: string
  reason?: string
  changes: Array<{
    type: 'replace'
    line_start: number
    line_end: number
    old_content?: string
    new_content: string
  }>
}

export interface PRContext {
  prUrl: string
  branchName: string
  ticketDescription: string
  changesSummary: string
  workingDirectory: string
  worktreePath: string
  modificationPrompt?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  response?: T
  error?: string
  session_id?: string
}

export interface GitDiffResponse {
  success: boolean
  diff: string
  changedFiles: Array<{
    status: string
    filename: string
    oldFilename?: string
    displayName?: string
    additions: number
    deletions: number
  }>
  currentBranch: string
  targetBranch: string
  stats: {
    additions: number
    deletions: number
    files: number
  }
}