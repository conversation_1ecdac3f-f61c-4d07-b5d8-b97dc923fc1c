// Bitbucket API Types
export interface BitbucketUser {
  uuid: string
  username: string
  display_name: string
  account_id: string
  links: {
    avatar: {
      href: string
    }
    html: {
      href: string
    }
  }
}

export interface BitbucketWorkspace {
  uuid: string
  name: string
  slug: string
  is_private: boolean
  links: {
    avatar?: {
      href: string
    }
    html: {
      href: string
    }
  }
}

export interface BitbucketRepository {
  uuid: string
  name: string
  full_name: string
  is_private: boolean
  description?: string
  language?: string
  size: number
  created_on: string
  updated_on: string
  workspace: BitbucketWorkspace
  project?: {
    name: string
    key: string
  }
  links: {
    html: {
      href: string
    }
    clone: Array<{
      name: string
      href: string
    }>
  }
}

export interface BitbucketBranch {
  name: string
  target: {
    hash: string
    message: string
    date: string
    author: {
      user: Bitbucket<PERSON>ser
    }
  }
  links: {
    commits: {
      href: string
    }
    html: {
      href: string
    }
  }
}

export interface BitbucketPullRequest {
  id: number
  title: string
  description?: string
  state: 'OPEN' | 'MERGED' | 'DECLINED' | 'SUPERSEDED'
  created_on: string
  updated_on: string
  author: BitbucketUser
  source: {
    branch: {
      name: string
    }
    commit: {
      hash: string
    }
    repository: BitbucketRepository
  }
  destination: {
    branch: {
      name: string
    }
    commit: {
      hash: string
    }
    repository: BitbucketRepository
  }
  merge_commit?: {
    hash: string
  }
  comment_count: number
  task_count: number
  close_source_branch: boolean
  reviewers: BitbucketUser[]
  participants: Array<{
    user: BitbucketUser
    role: 'REVIEWER' | 'PARTICIPANT'
    approved: boolean
    participated_on?: string
  }>
  links: {
    html: {
      href: string
    }
    diff: {
      href: string
    }
    comments: {
      href: string
    }
    activity: {
      href: string
    }
    commits: {
      href: string
    }
  }
}

export interface BitbucketPRComment {
  id: number
  content: {
    raw: string
    markup: string
    html: string
  }
  user: BitbucketUser
  created_on: string
  updated_on: string
  type: 'pullrequest_comment'
  parent?: {
    id: number
  }
  inline?: {
    from?: number
    to: number
    path: string
  }
  links: {
    html: {
      href: string
    }
  }
  deleted: boolean
  pending: boolean
}

export interface BitbucketDiff {
  status: 'added' | 'modified' | 'removed' | 'renamed'
  old?: {
    path: string
    escaped_path: string
    type: string
    links: {
      self: { href: string }
    }
  }
  new?: {
    path: string
    escaped_path: string
    type: string
    links: {
      self: { href: string }
    }
  }
  lines_added: number
  lines_removed: number
}

export interface BitbucketPRDiff {
  pagelen: number
  values: BitbucketDiff[]
  page: number
  size: number
}

// OAuth Types
export interface OAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scopes: string[]
}

export interface AccessTokenResponse {
  access_token: string
  token_type: 'bearer'
  expires_in: number
  refresh_token?: string
  scope: string
}

export interface AuthState {
  isAuthenticated: boolean
  user: BitbucketUser | null
  accessToken: string | null
  refreshToken: string | null
  expiresAt: number | null
  isLoading: boolean
  error: string | null
}

// API Response wrapper
export interface BitbucketApiResponse<T> {
  values: T[]
  pagelen: number
  size: number
  page: number
  next?: string
  previous?: string
}

// Error types
export interface BitbucketApiError {
  type: string
  error: {
    message: string
    detail?: string
  }
}

// PR Selection State
export interface PRSelectionState {
  selectedWorkspace: BitbucketWorkspace | null
  selectedRepository: BitbucketRepository | null
  selectedBranch: BitbucketBranch | null
  selectedPR: BitbucketPullRequest | null
  availableWorkspaces: BitbucketWorkspace[]
  availableRepositories: BitbucketRepository[]
  availableBranches: BitbucketBranch[]
  availablePRs: BitbucketPullRequest[]
  isLoading: {
    workspaces: boolean
    repositories: boolean
    branches: boolean
    prs: boolean
  }
}

// Extended comment type for our app
export interface EnhancedPRComment extends BitbucketPRComment {
  // Add our app-specific fields
  claudeResponse?: {
    sessionId: string
    response: string
    confidence: number
    previewChanges?: any[]
    isProcessing: boolean
    error?: string
  }
  isVisible: boolean
  threadReplies: EnhancedPRComment[]
}

// Comment positioning and grouping types
export interface CommentPosition {
  path: string
  line: number
  from?: number
}

export interface CommentWithPosition extends BitbucketPRComment {
  position: CommentPosition
}

export interface InlineCommentGroup {
  rootComment: BitbucketPRComment
  replies: BitbucketPRComment[]
  position: CommentPosition | null
}

// Diff parsing types
export interface DiffLine {
  type: 'addition' | 'deletion' | 'context'
  content: string
  oldLineNumber: number | null
  newLineNumber: number | null
}

export interface DiffHunk {
  filePath: string
  oldStart: number
  oldLines: number
  newStart: number
  newLines: number
  header: string
  lines: DiffLine[]
}

// PR Comments Service Types
export interface PRCommentsData {
  allComments: BitbucketPRComment[]
  inlineComments: CommentWithPosition[]
  generalComments: BitbucketPRComment[]
  commentsByFile: Record<string, CommentWithPosition[]>
}

export interface PRDiffWithComments {
  diffText: string
  diffStat: BitbucketDiff[]
  commentsData: PRCommentsData
  parsedDiff: DiffHunk[]
}

// Comment statistics
export interface CommentStatistics {
  total: number
  inline: number
  general: number
  resolved: number
  pending: number
  participants: number
}