/**
 * Application Performance Monitoring (APM) Integration
 * Comprehensive monitoring system for production observability
 */

import { env } from '../config/environment'

/**
 * Performance metric types
 */
export interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
  attributes?: Record<string, any>
}

/**
 * Error tracking interface
 */
export interface ErrorReport {
  message: string
  stack?: string
  timestamp: number
  userId?: string
  sessionId: string
  url: string
  userAgent: string
  component?: string
  errorBoundary?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  context?: Record<string, any>
  breadcrumbs?: Breadcrumb[]
}

/**
 * User action tracking
 */
export interface UserAction {
  action: string
  category: string
  label?: string
  value?: number
  timestamp: number
  userId?: string
  sessionId: string
  properties?: Record<string, any>
}

/**
 * Breadcrumb for error context
 */
export interface Breadcrumb {
  message: string
  category: string
  level: 'info' | 'warning' | 'error'
  timestamp: number
  data?: Record<string, any>
}

/**
 * System health metrics
 */
export interface HealthMetric {
  service: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  responseTime?: number
  timestamp: number
  details?: Record<string, any>
}

/**
 * Performance monitoring class
 */
class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private maxMetrics = 1000
  private flushInterval = 30000 // 30 seconds
  private flushTimer?: NodeJS.Timeout

  constructor() {
    if (env.monitoring.enablePerformanceTracking) {
      this.startPerformanceObserver()
      this.startFlushTimer()
    }
  }

  /**
   * Record a custom performance metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    if (!env.monitoring.enablePerformanceTracking) return

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags,
      attributes: {
        environment: env.NODE_ENV,
        version: env.deployment.version,
        sessionId: this.getSessionId()
      }
    }

    this.metrics.push(metric)
    this.trimMetrics()

    // Log in development
    if (env.features.debugMode) {
      console.log('📊 Performance Metric:', metric)
    }
  }

  /**
   * Record API response time
   */
  recordApiCall(endpoint: string, duration: number, status: number, size?: number): void {
    this.recordMetric('api_call_duration', duration, {
      endpoint,
      status: status.toString(),
      ...(size && { size: size.toString() })
    })
  }

  /**
   * Record component render time
   */
  recordComponentRender(componentName: string, duration: number): void {
    this.recordMetric('component_render_duration', duration, {
      component: componentName
    })
  }

  /**
   * Record WebSocket event metrics
   */
  recordWebSocketEvent(event: string, duration?: number): void {
    this.recordMetric('websocket_event', duration || 1, {
      event,
      type: 'websocket'
    })
  }

  /**
   * Record memory usage
   */
  recordMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.recordMetric('memory_used', memory.usedJSHeapSize, {
        metric: 'memory'
      })
      this.recordMetric('memory_total', memory.totalJSHeapSize, {
        metric: 'memory'
      })
    }
  }

  /**
   * Start performance observer for automatic metrics
   */
  private startPerformanceObserver(): void {
    if (typeof PerformanceObserver !== 'undefined') {
      try {
        // Navigation timing
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming
              this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.fetchStart)
              this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.fetchStart)
              this.recordMetric('first_contentful_paint', navEntry.loadEventEnd - navEntry.fetchStart)
            }
          }
        })
        navObserver.observe({ entryTypes: ['navigation'] })

        // Resource timing
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource') {
              const resourceEntry = entry as PerformanceResourceTiming
              this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.fetchStart, {
                resource: resourceEntry.name,
                type: resourceEntry.initiatorType
              })
            }
          }
        })
        resourceObserver.observe({ entryTypes: ['resource'] })

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('largest_contentful_paint', entry.startTime)
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('first_input_delay', (entry as any).processingStart - entry.startTime)
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })

      } catch (error) {
        console.warn('Performance Observer not supported:', error)
      }
    }
  }

  /**
   * Start automatic metrics flushing
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushMetrics()
    }, this.flushInterval)
  }

  /**
   * Flush metrics to monitoring service
   */
  private async flushMetrics(): Promise<void> {
    if (this.metrics.length === 0) return

    const metricsToFlush = [...this.metrics]
    this.metrics = []

    try {
      // In a real implementation, send to monitoring service
      if (env.features.debugMode) {
        console.log('📊 Flushing metrics:', metricsToFlush.length)
      }

      // Example: Send to monitoring endpoint
      // await fetch('/api/metrics', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(metricsToFlush)
      // })
      
    } catch (error) {
      console.error('Failed to flush metrics:', error)
      // Re-add metrics back to queue
      this.metrics.unshift(...metricsToFlush)
    }
  }

  /**
   * Trim metrics to prevent memory leaks
   */
  private trimMetrics(): void {
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
  }

  /**
   * Get current session ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('monitoring_session_id')
    if (!sessionId) {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('monitoring_session_id', sessionId)
    }
    return sessionId
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flushMetrics()
  }
}

/**
 * Error tracking class
 */
class ErrorTracker {
  private breadcrumbs: Breadcrumb[] = []
  private maxBreadcrumbs = 50

  constructor() {
    if (env.monitoring.enableErrorTracking) {
      this.setupGlobalErrorHandlers()
    }
  }

  /**
   * Track an error
   */
  trackError(error: Error | string, context?: Record<string, any>, severity: ErrorReport['severity'] = 'medium'): void {
    if (!env.monitoring.enableErrorTracking) return

    const errorReport: ErrorReport = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      severity,
      context,
      breadcrumbs: [...this.breadcrumbs]
    }

    // Anonymize data if required
    if (env.monitoring.anonymizeData) {
      errorReport.userAgent = this.anonymizeUserAgent(errorReport.userAgent)
      errorReport.url = this.anonymizeUrl(errorReport.url)
    }

    this.sendErrorReport(errorReport)

    // Log in development
    if (env.features.debugMode) {
      console.error('🚨 Error Tracked:', errorReport)
    }
  }

  /**
   * Add breadcrumb for error context
   */
  addBreadcrumb(message: string, category: string, level: Breadcrumb['level'] = 'info', data?: Record<string, any>): void {
    const breadcrumb: Breadcrumb = {
      message,
      category,
      level,
      timestamp: Date.now(),
      data
    }

    this.breadcrumbs.push(breadcrumb)
    
    // Trim breadcrumbs
    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.maxBreadcrumbs)
    }
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Unhandled errors
    window.addEventListener('error', (event) => {
      this.trackError(event.error || event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }, 'high')
    })

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.trackError(event.reason, {
        type: 'unhandled_promise_rejection'
      }, 'high')
    })

    // React error boundary fallback
    const originalConsoleError = console.error
    console.error = (...args) => {
      if (args[0]?.includes?.('React') || args[0]?.includes?.('Warning')) {
        this.trackError(args.join(' '), {
          type: 'react_error'
        }, 'medium')
      }
      originalConsoleError.apply(console, args)
    }
  }

  /**
   * Send error report to monitoring service
   */
  private async sendErrorReport(errorReport: ErrorReport): Promise<void> {
    try {
      // In a real implementation, send to error tracking service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // })
      
      // For now, log the error report for debugging
      console.debug('Error report would be sent:', errorReport)
    } catch (error) {
      console.warn('Failed to send error report:', error)
    }
  }

  /**
   * Anonymize user agent
   */
  private anonymizeUserAgent(userAgent: string): string {
    return userAgent.replace(/\d+\.\d+\.\d+/g, 'X.X.X')
  }

  /**
   * Anonymize URL
   */
  private anonymizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`
    } catch {
      return '[INVALID_URL]'
    }
  }

  /**
   * Get session ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('error_session_id')
    if (!sessionId) {
      sessionId = `error-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('error_session_id', sessionId)
    }
    return sessionId
  }
}

/**
 * User action tracker
 */
class UserActionTracker {
  private actions: UserAction[] = []
  private maxActions = 500
  private flushInterval = 60000 // 1 minute
  private flushTimer?: NodeJS.Timeout

  constructor() {
    if (env.monitoring.enableUsageTracking) {
      this.startFlushTimer()
      this.setupAutoTracking()
    }
  }

  /**
   * Track user action
   */
  trackAction(action: string, category: string, label?: string, value?: number, properties?: Record<string, any>): void {
    if (!env.monitoring.enableUsageTracking) return

    const userAction: UserAction = {
      action,
      category,
      label,
      value,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      properties
    }

    this.actions.push(userAction)
    this.trimActions()

    // Log in development
    if (env.features.debugMode) {
      console.log('👤 User Action:', userAction)
    }
  }

  /**
   * Track page view
   */
  trackPageView(page: string): void {
    this.trackAction('page_view', 'navigation', page)
  }

  /**
   * Track button click
   */
  trackButtonClick(buttonName: string, context?: string): void {
    this.trackAction('button_click', 'interaction', buttonName, undefined, { context })
  }

  /**
   * Track multi-agent review events
   */
  trackMultiAgentEvent(event: string, reviewId?: string, agentType?: string): void {
    this.trackAction(event, 'multi_agent_review', agentType, undefined, { reviewId })
  }

  /**
   * Track form submission
   */
  trackFormSubmission(formName: string, success: boolean): void {
    this.trackAction('form_submit', 'interaction', formName, success ? 1 : 0)
  }

  /**
   * Track search
   */
  trackSearch(query: string, resultsCount?: number): void {
    const anonymizedQuery = env.monitoring.anonymizeData ? '[SEARCH_QUERY]' : query
    this.trackAction('search', 'search', anonymizedQuery, resultsCount)
  }

  /**
   * Setup automatic tracking
   */
  private setupAutoTracking(): void {
    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.trackAction(
        document.hidden ? 'page_hidden' : 'page_visible',
        'page_visibility'
      )
    })

    // Track scroll depth
    let maxScrollDepth = 0
    const trackScrollDepth = () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      )
      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth
        if (scrollDepth % 25 === 0) { // Track at 25%, 50%, 75%, 100%
          this.trackAction('scroll_depth', 'engagement', `${scrollDepth}%`, scrollDepth)
        }
      }
    }
    window.addEventListener('scroll', trackScrollDepth, { passive: true })

    // Track time on page
    let pageStartTime = Date.now()
    window.addEventListener('beforeunload', () => {
      const timeOnPage = Date.now() - pageStartTime
      this.trackAction('time_on_page', 'engagement', undefined, timeOnPage)
    })
  }

  /**
   * Start flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushActions()
    }, this.flushInterval)
  }

  /**
   * Flush actions to analytics service
   */
  private async flushActions(): Promise<void> {
    if (this.actions.length === 0) return

    const actionsToFlush = [...this.actions]
    this.actions = []

    try {
      // In a real implementation, send to analytics service
      if (env.features.debugMode) {
        console.log('📈 Flushing user actions:', actionsToFlush.length)
      }
    } catch (error) {
      console.error('Failed to flush user actions:', error)
      this.actions.unshift(...actionsToFlush)
    }
  }

  /**
   * Trim actions to prevent memory leaks
   */
  private trimActions(): void {
    if (this.actions.length > this.maxActions) {
      this.actions = this.actions.slice(-this.maxActions)
    }
  }

  /**
   * Get session ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('analytics_session_id')
    if (!sessionId) {
      sessionId = `analytics-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('analytics_session_id', sessionId)
    }
    return sessionId
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flushActions()
  }
}

/**
 * System health monitor
 */
class HealthMonitor {
  private healthChecks: Map<string, HealthMetric> = new Map()
  private checkInterval = 30000 // 30 seconds
  private checkTimer?: NodeJS.Timeout

  constructor() {
    this.startHealthChecks()
  }

  /**
   * Register a service for health monitoring
   */
  registerService(serviceName: string, healthCheckUrl: string): void {
    this.healthChecks.set(serviceName, {
      service: serviceName,
      status: 'healthy',
      timestamp: Date.now()
    })

    // Immediate health check
    this.checkServiceHealth(serviceName, healthCheckUrl)
  }

  /**
   * Get service health status
   */
  getServiceHealth(serviceName: string): HealthMetric | null {
    return this.healthChecks.get(serviceName) || null
  }

  /**
   * Get overall system health
   */
  getOverallHealth(): { status: 'healthy' | 'degraded' | 'unhealthy'; services: HealthMetric[] } {
    const services = Array.from(this.healthChecks.values())
    const unhealthyCount = services.filter(s => s.status === 'unhealthy').length
    const degradedCount = services.filter(s => s.status === 'degraded').length

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    
    if (unhealthyCount > 0) {
      status = 'unhealthy'
    } else if (degradedCount > 0) {
      status = 'degraded'
    }

    return { status, services }
  }

  /**
   * Check service health
   */
  private async checkServiceHealth(serviceName: string, url: string): Promise<void> {
    const startTime = Date.now()
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        signal: AbortSignal.timeout(5000) // 5 second timeout
      })
      
      const responseTime = Date.now() - startTime
      const status = response.ok ? 'healthy' : 'degraded'
      
      this.healthChecks.set(serviceName, {
        service: serviceName,
        status,
        responseTime,
        timestamp: Date.now(),
        details: {
          httpStatus: response.status,
          contentType: response.headers.get('content-type')
        }
      })
      
      // Record performance metric
      performanceMonitor.recordMetric('service_health_check', responseTime, {
        service: serviceName,
        status
      })
      
    } catch (error) {
      this.healthChecks.set(serviceName, {
        service: serviceName,
        status: 'unhealthy',
        timestamp: Date.now(),
        details: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      })
    }
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    this.checkTimer = setInterval(() => {
      // Check registered services
      // In a real implementation, this would iterate through registered services
    }, this.checkInterval)
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }
  }
}

/**
 * Global monitoring instances
 */
export const performanceMonitor = new PerformanceMonitor()
export const errorTracker = new ErrorTracker()
export const userActionTracker = new UserActionTracker()
export const healthMonitor = new HealthMonitor()

/**
 * Monitoring utilities
 */
export const MonitoringUtils = {
  /**
   * Measure function execution time
   */
  measureTime: async <T>(name: string, fn: () => Promise<T> | T): Promise<T> => {
    const startTime = performance.now()
    try {
      const result = await fn()
      const duration = performance.now() - startTime
      performanceMonitor.recordMetric(name, duration)
      return result
    } catch (error) {
      errorTracker.trackError(error instanceof Error ? error : new Error(String(error)))
      throw error
    }
  },

  /**
   * Track component render
   */
  trackComponentRender: (componentName: string, duration: number) => {
    performanceMonitor.recordComponentRender(componentName, duration)
  },

  /**
   * Track user interaction
   */
  trackInteraction: (action: string, element: string, context?: Record<string, any>) => {
    userActionTracker.trackAction(action, 'interaction', element, undefined, context)
  },

  /**
   * Add error context
   */
  addErrorContext: (message: string, data?: Record<string, any>) => {
    errorTracker.addBreadcrumb(message, 'context', 'info', data)
  },

  /**
   * Cleanup all monitoring
   */
  cleanup: () => {
    performanceMonitor.destroy()
    userActionTracker.destroy()
    healthMonitor.destroy()
  }
}

/**
 * Initialize monitoring for the application
 */
export const initializeMonitoring = (): void => {
  if (env.features.debugMode) {
    console.log('🔍 Monitoring initialized')
    console.log('Performance tracking:', env.monitoring.enablePerformanceTracking)
    console.log('Error tracking:', env.monitoring.enableErrorTracking)
    console.log('Usage tracking:', env.monitoring.enableUsageTracking)
  }

  // Register core services for health monitoring
  healthMonitor.registerService('multi-agent', `${env.api.multiAgent.baseUrl}/health`)
  healthMonitor.registerService('legacy-reviewer', `${env.api.legacyCodeReviewer.baseUrl}/api/health`)
  healthMonitor.registerService('legacy-claude', `${env.api.legacyClaude.baseUrl}/api/health`)

  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    MonitoringUtils.cleanup()
  })
}

export default {
  performanceMonitor,
  errorTracker,
  userActionTracker,
  healthMonitor,
  MonitoringUtils,
  initializeMonitoring
}