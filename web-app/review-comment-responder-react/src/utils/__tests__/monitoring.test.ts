/**
 * Monitoring System Tests
 * Test suite for performance monitoring, error tracking, and analytics
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { 
  performanceMonitor, 
  errorTracker, 
  userActionTracker, 
  healthMonitor,
  MonitoringUtils,
  initializeMonitoring
} from '../monitoring'

// Mock environment
vi.mock('../../config/environment', () => ({
  env: {
    NODE_ENV: 'test',
    isDevelopment: false,
    isProduction: false,
    isStaging: false,
    features: {
      debugMode: true
    },
    monitoring: {
      enablePerformanceTracking: true,
      enableErrorTracking: true,
      enableUsageTracking: true,
      anonymizeData: false
    },
    api: {
      multiAgent: {
        baseUrl: 'http://localhost:5000'
      },
      legacyCodeReviewer: {
        baseUrl: 'http://localhost:5002'
      },
      legacyClaude: {
        baseUrl: 'http://localhost:5001'
      }
    },
    deployment: {
      version: '1.0.0',
      buildTimestamp: '2024-01-01T00:00:00Z'
    }
  }
}))

// Mock console methods
const originalConsoleLog = console.log
const originalConsoleError = console.error

describe('Monitoring System', () => {
  beforeEach(() => {
    // Reset session storage
    sessionStorage.clear()
    
    // Mock performance API
    global.performance = {
      ...global.performance,
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn()
    }

    // Mock fetch
    global.fetch = vi.fn(() => 
      Promise.resolve({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        redirected: false,
        statusText: 'OK',
        type: 'basic',
        url: 'http://localhost/api/test',
        clone: () => ({} as Response),
        body: null,
        bodyUsed: false,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
        blob: () => Promise.resolve(new Blob()),
        formData: () => Promise.resolve(new FormData()),
        json: () => Promise.resolve({}),
        text: () => Promise.resolve('')
      } as unknown as Response)
    )

    // Suppress console output in tests
    console.log = vi.fn()
    console.error = vi.fn()
  })

  afterEach(() => {
    vi.clearAllMocks()
    console.log = originalConsoleLog
    console.error = originalConsoleError
  })

  describe('Performance Monitor', () => {
    it('should record custom metrics', () => {
      const metric = 'test_metric'
      const value = 100
      const tags = { component: 'TestComponent' }

      performanceMonitor.recordMetric(metric, value, tags)

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: metric,
          value,
          tags,
          timestamp: expect.any(Number)
        })
      )
    })

    it('should record API call metrics', () => {
      const endpoint = '/api/test'
      const duration = 250
      const status = 200
      const size = 1024

      performanceMonitor.recordApiCall(endpoint, duration, status, size)

      // Verify that recordMetric was called internally
      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'api_call_duration',
          value: duration,
          tags: {
            endpoint,
            status: status.toString(),
            size: size.toString()
          }
        })
      )
    })

    it('should record component render metrics', () => {
      const componentName = 'TestComponent'
      const duration = 16.5

      performanceMonitor.recordComponentRender(componentName, duration)

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'component_render_duration',
          value: duration,
          tags: { component: componentName }
        })
      )
    })

    it('should record WebSocket events', () => {
      const event = 'connection_established'
      const duration = 50

      performanceMonitor.recordWebSocketEvent(event, duration)

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'websocket_event',
          value: duration,
          tags: { event, type: 'websocket' }
        })
      )
    })

    it('should record memory usage when available', () => {
      // Mock performance.memory
      const mockMemory = {
        usedJSHeapSize: 1024 * 1024,
        totalJSHeapSize: 2048 * 1024
      }
      
      Object.defineProperty(performance, 'memory', {
        value: mockMemory,
        configurable: true
      })

      performanceMonitor.recordMemoryUsage()

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'memory_used',
          value: mockMemory.usedJSHeapSize
        })
      )

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'memory_total',
          value: mockMemory.totalJSHeapSize
        })
      )
    })
  })

  describe('Error Tracker', () => {
    it('should track string errors', () => {
      const errorMessage = 'Test error message'
      const context = { component: 'TestComponent' }
      const severity = 'high'

      errorTracker.trackError(errorMessage, context, severity)

      // Check that the error was logged
      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          message: errorMessage,
          severity,
          context
        })
      )
    })

    it('should track Error objects', () => {
      const error = new Error('Test error')
      const context = { component: 'TestComponent' }

      errorTracker.trackError(error, context)

      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          message: error.message,
          stack: error.stack,
          context
        })
      )
    })

    it('should add breadcrumbs', () => {
      const message = 'User clicked button'
      const category = 'interaction'
      const level = 'info'
      const data = { buttonId: 'submit-btn' }

      errorTracker.addBreadcrumb(message, category, level, data)

      // Trigger an error to see breadcrumbs
      errorTracker.trackError('Test error')

      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          breadcrumbs: expect.arrayContaining([
            expect.objectContaining({
              message,
              category,
              level,
              data
            })
          ])
        })
      )
    })

    it('should handle global error events', () => {
      const error = new Error('Global error')
      const event = new ErrorEvent('error', {
        error,
        message: error.message,
        filename: 'test.js',
        lineno: 10,
        colno: 5
      })

      // Simulate global error
      window.dispatchEvent(event)

      // Should have tracked the error
      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          message: error.message,
          severity: 'high'
        })
      )
    })

    it('should handle unhandled promise rejections', () => {
      const reason = 'Promise rejection reason'
      const event = new PromiseRejectionEvent('unhandledrejection', {
        reason,
        promise: Promise.reject(reason)
      })

      // Simulate unhandled rejection
      window.dispatchEvent(event)

      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          message: reason,
          severity: 'high'
        })
      )
    })
  })

  describe('User Action Tracker', () => {
    it('should track user actions', () => {
      const action = 'button_click'
      const category = 'interaction'
      const label = 'submit-button'
      const value = 1
      const properties = { context: 'form' }

      userActionTracker.trackAction(action, category, label, value, properties)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action,
          category,
          label,
          value,
          properties
        })
      )
    })

    it('should track page views', () => {
      const page = '/dashboard'

      userActionTracker.trackPageView(page)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action: 'page_view',
          category: 'navigation',
          label: page
        })
      )
    })

    it('should track button clicks', () => {
      const buttonName = 'save-button'
      const context = 'user-form'

      userActionTracker.trackButtonClick(buttonName, context)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action: 'button_click',
          category: 'interaction',
          label: buttonName,
          properties: { context }
        })
      )
    })

    it('should track multi-agent events', () => {
      const event = 'review_started'
      const reviewId = 'review-123'
      const agentType = 'bug_detection'

      userActionTracker.trackMultiAgentEvent(event, reviewId, agentType)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action: event,
          category: 'multi_agent_review',
          label: agentType,
          properties: { reviewId }
        })
      )
    })

    it('should track form submissions', () => {
      const formName = 'contact-form'
      const success = true

      userActionTracker.trackFormSubmission(formName, success)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action: 'form_submit',
          category: 'interaction',
          label: formName,
          value: 1
        })
      )
    })

    it('should track searches', () => {
      const query = 'test query'
      const resultsCount = 10

      userActionTracker.trackSearch(query, resultsCount)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action: 'search',
          category: 'search',
          label: query,
          value: resultsCount
        })
      )
    })
  })

  describe('Health Monitor', () => {
    it('should register services for monitoring', () => {
      const serviceName = 'test-service'
      const healthUrl = 'http://localhost:3000/health'

      healthMonitor.registerService(serviceName, healthUrl)

      const health = healthMonitor.getServiceHealth(serviceName)
      expect(health).toEqual(
        expect.objectContaining({
          service: serviceName,
          status: expect.any(String),
          timestamp: expect.any(Number)
        })
      )
    })

    it('should return overall health status', () => {
      healthMonitor.registerService('service1', 'http://localhost:3000/health')
      healthMonitor.registerService('service2', 'http://localhost:3001/health')

      const overallHealth = healthMonitor.getOverallHealth()
      
      expect(overallHealth).toEqual({
        status: expect.any(String),
        services: expect.arrayContaining([
          expect.objectContaining({
            service: 'service1'
          }),
          expect.objectContaining({
            service: 'service2'
          })
        ])
      })
    })
  })

  describe('Monitoring Utils', () => {
    it('should measure function execution time', async () => {
      const testFunction = vi.fn().mockResolvedValue('result')
      const functionName = 'test_function'

      const result = await MonitoringUtils.measureTime(functionName, testFunction)

      expect(result).toBe('result')
      expect(testFunction).toHaveBeenCalled()
      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: functionName,
          value: expect.any(Number)
        })
      )
    })

    it('should handle errors in measured functions', async () => {
      const error = new Error('Test error')
      const testFunction = vi.fn().mockRejectedValue(error)
      const functionName = 'failing_function'

      await expect(
        MonitoringUtils.measureTime(functionName, testFunction)
      ).rejects.toThrow(error)

      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          message: error.message
        })
      )
    })

    it('should track component renders', () => {
      const componentName = 'TestComponent'
      const duration = 15.5

      MonitoringUtils.trackComponentRender(componentName, duration)

      expect(console.log).toHaveBeenCalledWith(
        '📊 Performance Metric:',
        expect.objectContaining({
          name: 'component_render_duration',
          value: duration
        })
      )
    })

    it('should track user interactions', () => {
      const action = 'click'
      const element = 'button'
      const context = { page: 'dashboard' }

      MonitoringUtils.trackInteraction(action, element, context)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          action,
          category: 'interaction',
          label: element,
          properties: context
        })
      )
    })

    it('should add error context', () => {
      const message = 'User performed action'
      const data = { actionType: 'click' }

      MonitoringUtils.addErrorContext(message, data)

      // Trigger an error to see breadcrumb
      errorTracker.trackError('Test error')

      expect(console.error).toHaveBeenCalledWith(
        '🚨 Error Tracked:',
        expect.objectContaining({
          breadcrumbs: expect.arrayContaining([
            expect.objectContaining({
              message,
              data
            })
          ])
        })
      )
    })
  })

  describe('Initialization', () => {
    it('should initialize monitoring system', () => {
      initializeMonitoring()

      expect(console.log).toHaveBeenCalledWith('🔍 Monitoring initialized')
      expect(console.log).toHaveBeenCalledWith('Performance tracking:', true)
      expect(console.log).toHaveBeenCalledWith('Error tracking:', true)
      expect(console.log).toHaveBeenCalledWith('Usage tracking:', true)
    })

    it('should register core services for health monitoring', () => {
      initializeMonitoring()

      // Verify services are registered
      expect(healthMonitor.getServiceHealth('multi-agent')).toBeTruthy()
      expect(healthMonitor.getServiceHealth('legacy-reviewer')).toBeTruthy()
      expect(healthMonitor.getServiceHealth('legacy-claude')).toBeTruthy()
    })
  })

  describe('Session Management', () => {
    it('should generate unique session IDs', () => {
      // Clear session storage
      sessionStorage.clear()

      // Create two different trackers to ensure unique IDs
      const tracker1 = userActionTracker
      const tracker2 = errorTracker

      tracker1.trackAction('test1', 'category1')
      tracker2.trackError('test error')

      // Both should have generated session IDs
      expect(sessionStorage.getItem('analytics_session_id')).toBeTruthy()
      expect(sessionStorage.getItem('error_session_id')).toBeTruthy()
    })

    it('should reuse existing session IDs', () => {
      const existingSessionId = 'existing-session-123'
      sessionStorage.setItem('analytics_session_id', existingSessionId)

      userActionTracker.trackAction('test', 'category')

      // Should have used the existing session ID
      expect(sessionStorage.getItem('analytics_session_id')).toBe(existingSessionId)
    })
  })

  describe('Data Anonymization', () => {
    beforeEach(() => {
      // Mock anonymization enabled
      vi.doMock('../../config/environment', () => ({
        env: {
          features: { debugMode: true },
          monitoring: {
            enableErrorTracking: true,
            enablePerformanceTracking: true,
            enableUsageTracking: true,
            anonymizeData: true,
            sampleRate: 100
          }
        }
      }))
    })

    it('should anonymize search queries when enabled', () => {
      const sensitiveQuery = '<EMAIL> password'
      const resultsCount = 5

      userActionTracker.trackSearch(sensitiveQuery, resultsCount)

      expect(console.log).toHaveBeenCalledWith(
        '👤 User Action:',
        expect.objectContaining({
          label: '[SEARCH_QUERY]'
        })
      )
    })
  })

  describe('Error Handling Edge Cases', () => {
    it('should handle malformed error objects', () => {
      const malformedError = { message: 'Not a real Error object' }

      expect(() => {
        errorTracker.trackError(malformedError as any)
      }).not.toThrow()
    })

    it('should handle circular references in context', () => {
      const circularContext: any = { name: 'test' }
      circularContext.self = circularContext

      expect(() => {
        errorTracker.trackError('Test error', circularContext)
      }).not.toThrow()
    })

    it('should handle null and undefined values', () => {
      expect(() => {
        performanceMonitor.recordMetric('test', NaN)
        userActionTracker.trackAction('', '', undefined, null as any)
        errorTracker.trackError(null as any)
      }).not.toThrow()
    })
  })
})