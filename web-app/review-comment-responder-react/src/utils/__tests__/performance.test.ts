/**
 * Performance Utilities Tests
 * Test suite for performance monitoring and optimization utilities
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  PerformanceUtils,
  componentPerformanceTracker,
  memoryMonitor,
  networkPerformanceTracker
} from '../performance'

// Mock environment
vi.mock('../../config/environment', () => ({
  env: {
    NODE_ENV: 'test',
    features: {
      debugMode: true
    },
    monitoring: {
      enablePerformanceTracking: true
    }
  }
}))

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  memory: {
    usedJSHeapSize: 10 * 1024 * 1024, // 10MB
    totalJSHeapSize: 20 * 1024 * 1024, // 20MB
    jsHeapSizeLimit: 100 * 1024 * 1024 // 100MB
  }
}

// Mock monitoring
vi.mock('../monitoring', () => ({
  performanceMonitor: {
    recordMetric: vi.fn(),
    recordComponentRender: vi.fn(),
    recordApiCall: vi.fn()
  }
}))

// Mock logger
vi.mock('../logger', () => ({
  logger: {
    child: vi.fn(() => ({
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    }))
  }
}))

describe('Performance Utils', () => {
  beforeEach(() => {
    global.performance = mockPerformance as any
    vi.clearAllMocks()
  })

  afterEach(() => {
    componentPerformanceTracker.clearMetrics()
    networkPerformanceTracker.clearMetrics()
  })

  describe('PerformanceUtils.measure', () => {
    it('should measure async function execution time', async () => {
      const mockFn = vi.fn().mockResolvedValue('result')
      const functionName = 'test-function'

      const result = await PerformanceUtils.measure(functionName, mockFn)

      expect(result).toBe('result')
      expect(mockFn).toHaveBeenCalled()
      expect(mockPerformance.mark).toHaveBeenCalledWith(`${functionName}-start`)
      expect(mockPerformance.mark).toHaveBeenCalledWith(`${functionName}-end`)
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        functionName,
        `${functionName}-start`,
        `${functionName}-end`
      )
    })

    it('should measure sync function execution time', async () => {
      const mockFn = vi.fn().mockReturnValue('sync-result')
      const functionName = 'sync-function'

      const result = await PerformanceUtils.measure(functionName, mockFn)

      expect(result).toBe('sync-result')
      expect(mockFn).toHaveBeenCalled()
    })

    it('should handle function errors and mark error', async () => {
      const error = new Error('Test error')
      const mockFn = vi.fn().mockRejectedValue(error)
      const functionName = 'error-function'

      await expect(PerformanceUtils.measure(functionName, mockFn)).rejects.toThrow(error)
      
      expect(mockPerformance.mark).toHaveBeenCalledWith(`${functionName}-error`)
    })
  })

  describe('PerformanceUtils.debounce', () => {
    it('should debounce function calls', async () => {
      const mockFn = vi.fn()
      const debouncedFn = PerformanceUtils.debounce(mockFn, 100)

      debouncedFn('arg1')
      debouncedFn('arg2')
      debouncedFn('arg3')

      expect(mockFn).not.toHaveBeenCalled()

      // Wait for debounce delay
      await new Promise(resolve => setTimeout(resolve, 150))

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('arg3')
    })
  })

  describe('PerformanceUtils.throttle', () => {
    it('should throttle function calls', async () => {
      const mockFn = vi.fn()
      const throttledFn = PerformanceUtils.throttle(mockFn, 100)

      throttledFn('arg1')
      throttledFn('arg2')
      throttledFn('arg3')

      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('arg1')

      // Wait and call again
      await new Promise(resolve => setTimeout(resolve, 150))
      throttledFn('arg4')

      expect(mockFn).toHaveBeenCalledTimes(2)
      expect(mockFn).toHaveBeenCalledWith('arg4')
    })
  })

  describe('PerformanceUtils.createVirtualList', () => {
    it('should create virtual list configuration', () => {
      const items = new Array(1000).fill(null).map((_, i) => ({ id: i }))
      const itemHeight = 50
      const containerHeight = 400

      const virtualList = PerformanceUtils.createVirtualList(items, itemHeight, containerHeight)

      expect(virtualList.totalHeight).toBe(50000) // 1000 * 50
      expect(virtualList.visibleItems).toBe(8) // Math.ceil(400 / 50)

      const visibleRange = virtualList.getVisibleRange(500) // Scrolled down 500px
      expect(visibleRange.startIndex).toBe(10) // Math.floor(500 / 50)
      expect(visibleRange.endIndex).toBe(18) // 10 + 8
    })

    it('should handle edge cases in virtual list', () => {
      const items = new Array(5).fill(null) // Only 5 items
      const itemHeight = 50
      const containerHeight = 400

      const virtualList = PerformanceUtils.createVirtualList(items, itemHeight, containerHeight)
      const visibleRange = virtualList.getVisibleRange(100)

      expect(visibleRange.endIndex).toBe(5) // Should not exceed items.length
    })
  })

  describe('PerformanceUtils.preloadResources', () => {
    it('should create preload links', () => {
      const mockAppendChild = vi.fn()
      const mockCreateElement = vi.fn(() => ({
        rel: '',
        href: '',
        as: '',
        type: ''
      }))

      Object.defineProperty(document, 'createElement', {
        value: mockCreateElement,
        writable: true
      })
      Object.defineProperty(document.head, 'appendChild', {
        value: mockAppendChild,
        writable: true
      })

      const resources = [
        { href: '/script.js', as: 'script', type: 'text/javascript' },
        { href: '/style.css', as: 'style' }
      ]

      PerformanceUtils.preloadResources(resources)

      expect(mockCreateElement).toHaveBeenCalledTimes(2)
      expect(mockAppendChild).toHaveBeenCalledTimes(2)
    })
  })

  describe('PerformanceUtils.getPerformanceSummary', () => {
    it('should return performance summary', () => {
      // Add some test data
      componentPerformanceTracker.startRender('TestComponent')
      componentPerformanceTracker.endRender('TestComponent')

      networkPerformanceTracker.trackRequest('/api/test', 'GET', 100, 1024, 200)

      const summary = PerformanceUtils.getPerformanceSummary()

      expect(summary).toEqual({
        memory: expect.objectContaining({
          usedJSHeapSize: expect.any(Number),
          totalJSHeapSize: expect.any(Number),
          jsHeapSizeLimit: expect.any(Number),
          usage: expect.any(Number),
          warning: expect.any(Boolean)
        }),
        components: {
          total: 1,
          slowest: expect.arrayContaining([
            expect.objectContaining({
              componentName: 'TestComponent'
            })
          ])
        },
        network: {
          slowestEndpoints: expect.arrayContaining([
            expect.objectContaining({
              endpoint: '/api/test',
              averageTime: 100
            })
          ])
        },
        timestamp: expect.any(Number)
      })
    })
  })
})

describe('Component Performance Tracker', () => {
  beforeEach(() => {
    componentPerformanceTracker.clearMetrics()
    mockPerformance.now.mockReturnValueOnce(100).mockReturnValueOnce(150) // 50ms render
  })

  it('should track component render times', () => {
    const componentName = 'TestComponent'

    componentPerformanceTracker.startRender(componentName)
    componentPerformanceTracker.endRender(componentName)

    const metrics = componentPerformanceTracker.getMetrics(componentName)
    
    expect(metrics).toEqual({
      componentName,
      renderCount: 1,
      totalRenderTime: 50,
      averageRenderTime: 50,
      slowestRender: 50,
      fastestRender: 50,
      lastRenderTime: 50
    })
  })

  it('should update metrics for multiple renders', () => {
    const componentName = 'TestComponent'

    // First render: 50ms
    mockPerformance.now.mockReturnValueOnce(100).mockReturnValueOnce(150)
    componentPerformanceTracker.startRender(componentName)
    componentPerformanceTracker.endRender(componentName)

    // Second render: 30ms
    mockPerformance.now.mockReturnValueOnce(200).mockReturnValueOnce(230)
    componentPerformanceTracker.startRender(componentName)
    componentPerformanceTracker.endRender(componentName)

    const metrics = componentPerformanceTracker.getMetrics(componentName)
    
    expect(metrics).toEqual({
      componentName,
      renderCount: 2,
      totalRenderTime: 80,
      averageRenderTime: 40,
      slowestRender: 50,
      fastestRender: 30,
      lastRenderTime: 30
    })
  })

  it('should get slowest components', () => {
    // Add multiple components with different render times
    const components = [
      { name: 'FastComponent', time: 10 },
      { name: 'SlowComponent', time: 100 },
      { name: 'MediumComponent', time: 50 }
    ]

    components.forEach(({ name, time }) => {
      mockPerformance.now.mockReturnValueOnce(0).mockReturnValueOnce(time)
      componentPerformanceTracker.startRender(name)
      componentPerformanceTracker.endRender(name)
    })

    const slowest = componentPerformanceTracker.getSlowestComponents(2)
    
    expect(slowest).toHaveLength(2)
    expect(slowest[0].componentName).toBe('SlowComponent')
    expect(slowest[1].componentName).toBe('MediumComponent')
  })

  it('should handle missing start time gracefully', () => {
    const duration = componentPerformanceTracker.endRender('NonExistentComponent')
    expect(duration).toBe(0)
  })
})

describe('Memory Monitor', () => {
  it('should get memory information', () => {
    const memoryInfo = memoryMonitor.getMemoryInfo()
    
    expect(memoryInfo).toEqual({
      usedJSHeapSize: 10 * 1024 * 1024,
      totalJSHeapSize: 20 * 1024 * 1024,
      jsHeapSizeLimit: 100 * 1024 * 1024,
      usage: 10, // 10MB / 100MB * 100
      warning: false
    })
  })

  it('should detect memory warnings', () => {
    // Mock high memory usage
    mockPerformance.memory = {
      usedJSHeapSize: 85 * 1024 * 1024, // 85MB
      totalJSHeapSize: 90 * 1024 * 1024, // 90MB
      jsHeapSizeLimit: 100 * 1024 * 1024 // 100MB
    }

    const memoryInfo = memoryMonitor.getMemoryInfo()
    
    expect(memoryInfo?.warning).toBe(true)
    expect(memoryInfo?.usage).toBe(85)
  })

  it('should return null when memory API is not available', () => {
    const originalMemory = mockPerformance.memory
    ;(mockPerformance as any).memory = undefined

    const memoryInfo = memoryMonitor.getMemoryInfo()
    expect(memoryInfo).toBeNull()

    // Restore
    mockPerformance.memory = originalMemory
  })
})

describe('Network Performance Tracker', () => {
  beforeEach(() => {
    networkPerformanceTracker.clearMetrics()
  })

  it('should track network requests', () => {
    const endpoint = '/api/users'
    const method = 'GET'
    const duration = 250
    const size = 1024
    const status = 200

    networkPerformanceTracker.trackRequest(endpoint, method, duration, size, status)

    const metrics = networkPerformanceTracker.getEndpointMetrics(endpoint)
    expect(metrics).toHaveLength(1)
    expect(metrics[0]).toEqual({
      endpoint,
      method,
      duration,
      size,
      status,
      cached: false,
      retries: 0
    })
  })

  it('should calculate average response time', () => {
    const endpoint = '/api/test'

    // Add multiple requests
    networkPerformanceTracker.trackRequest(endpoint, 'GET', 100, 500, 200)
    networkPerformanceTracker.trackRequest(endpoint, 'GET', 200, 800, 200)
    networkPerformanceTracker.trackRequest(endpoint, 'GET', 150, 600, 200)

    const averageTime = networkPerformanceTracker.getAverageResponseTime(endpoint)
    expect(averageTime).toBe(150) // (100 + 200 + 150) / 3
  })

  it('should get slowest endpoints', () => {
    // Add requests to different endpoints
    networkPerformanceTracker.trackRequest('/api/fast', 'GET', 50, 500, 200)
    networkPerformanceTracker.trackRequest('/api/slow', 'GET', 500, 1000, 200)
    networkPerformanceTracker.trackRequest('/api/slow', 'GET', 300, 800, 200) // Average: 400
    networkPerformanceTracker.trackRequest('/api/medium', 'GET', 150, 600, 200)

    const slowest = networkPerformanceTracker.getSlowestEndpoints(2)
    
    expect(slowest).toHaveLength(2)
    expect(slowest[0].endpoint).toBe('/api/slow')
    expect(slowest[0].averageTime).toBe(400)
    expect(slowest[1].endpoint).toBe('/api/medium')
    expect(slowest[1].averageTime).toBe(150)
  })

  it('should return 0 for average time with no requests', () => {
    const averageTime = networkPerformanceTracker.getAverageResponseTime('/api/nonexistent')
    expect(averageTime).toBe(0)
  })
})

describe('Performance Optimization Utilities', () => {
  describe('Image Optimization', () => {
    it('should return original src when no optimization service available', () => {
      const src = '/image.jpg'
      const optimized = PerformanceUtils.optimizeImage(src, {
        width: 200,
        height: 150,
        quality: 80,
        format: 'webp'
      })

      expect(optimized).toBe(src)
    })

    it('should handle empty src', () => {
      const optimized = PerformanceUtils.optimizeImage('')
      expect(optimized).toBe('')
    })
  })

  describe('Resource Preloading', () => {
    it('should preload critical resources', () => {
      const mockLink = {
        rel: '',
        href: '',
        as: '',
        type: ''
      }

      const mockCreateElement = vi.fn(() => mockLink)
      const mockAppendChild = vi.fn()

      Object.defineProperty(document, 'createElement', {
        value: mockCreateElement,
        writable: true
      })
      Object.defineProperty(document.head, 'appendChild', {
        value: mockAppendChild,
        writable: true
      })

      const resources = [
        { href: '/critical.js', as: 'script' },
        { href: '/important.css', as: 'style' }
      ]

      PerformanceUtils.preloadResources(resources)

      expect(mockCreateElement).toHaveBeenCalledTimes(2)
      expect(mockCreateElement).toHaveBeenCalledWith('link')
      expect(mockAppendChild).toHaveBeenCalledTimes(2)
    })
  })

  describe('Performance Cleanup', () => {
    it('should cleanup performance monitoring', () => {
      const clearMetricsSpy = vi.spyOn(componentPerformanceTracker, 'clearMetrics')
      const clearNetworkSpy = vi.spyOn(networkPerformanceTracker, 'clearMetrics')

      PerformanceUtils.cleanup()

      expect(clearMetricsSpy).toHaveBeenCalled()
      expect(clearNetworkSpy).toHaveBeenCalled()
    })
  })
})

describe('Edge Cases and Error Handling', () => {
  it('should handle performance API not available', () => {
    const originalPerformance = global.performance
    delete (global as any).performance

    expect(() => {
      PerformanceUtils.measure('test', () => 'result')
    }).not.toThrow()

    // Restore
    global.performance = originalPerformance
  })

  it('should handle negative durations', () => {
    mockPerformance.now.mockReturnValueOnce(150).mockReturnValueOnce(100) // Negative duration

    componentPerformanceTracker.startRender('TestComponent')
    const duration = componentPerformanceTracker.endRender('TestComponent')

    // Should handle gracefully (actual implementation might clamp to 0)
    expect(typeof duration).toBe('number')
  })

  it('should handle concurrent renders of same component', () => {
    const componentName = 'ConcurrentComponent'

    // Start first render
    componentPerformanceTracker.startRender(componentName)
    
    // Start second render (should overwrite first)
    componentPerformanceTracker.startRender(componentName)
    
    // End render
    const duration = componentPerformanceTracker.endRender(componentName)
    
    expect(typeof duration).toBe('number')
  })
})