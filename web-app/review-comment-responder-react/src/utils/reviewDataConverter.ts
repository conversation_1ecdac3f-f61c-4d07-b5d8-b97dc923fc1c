import type { EnhancedReviewResults } from '../types/enhanced-review'
import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

interface ReviewFindingData {
  text: string
  severity: 'high' | 'medium' | 'low'
  file?: string
}

// AI Review Finding interface matching EnhancedInlineDiffViewer expectations
interface AIReviewFinding {
  id: string
  text: string
  severity: 'high' | 'medium' | 'low'
  category: 'acceptance_criteria' | 'code_quality' | 'security_issues' | 'performance_issues' | 'bugs' | 'suggestions'
  file?: string
  line?: number
  suggestion?: string
  confidence: number
  auto_fixable: boolean
}

type FindingCategory = 'acceptance_criteria' | 'code_quality' | 'security_issues' | 'performance_issues' | 'bugs' | 'suggestions'

// Return type for the converted results matching EnhancedInlineDiffViewer expectations
interface ConvertedReviewResults {
  session_id: string
  review_mode: string
  branch_name: string
  structured_findings: {
    acceptance_criteria: AIReviewFinding[]
    code_quality: AIReviewFinding[]
    security_issues: AIReviewFinding[]
    performance_issues: AIReviewFinding[]
    bugs: AIReviewFinding[]
    suggestions: AIReviewFinding[]
  }
  summary: {
    total_findings: number
    high_severity_count: number
    files_changed: number
    review_length: number
    categories: Record<string, number>
    completion_status: string
  }
}

export const convertReviewResultsToAIFormat = (results: StructuredReviewResult | EnhancedReviewResults): ConvertedReviewResults | null => {
  // Handle StructuredReviewResult format
  if ('structured_data' in results && results.structured_data) {
    const structuredResult = results as StructuredReviewResult
    
    // Convert structured data to AI format
    const convertedFindings: ConvertedReviewResults['structured_findings'] = {
      acceptance_criteria: [],
      code_quality: [],
      security_issues: [],
      performance_issues: [],
      bugs: [],
      suggestions: []
    }
    
    // Convert acceptance criteria
    structuredResult.structured_data.acceptance_criteria?.forEach((ac, index) => {
      convertedFindings.acceptance_criteria.push({
        id: `ac_${index}`,
        text: `${ac.icon} ${ac.title}`,
        severity: ac.status === 'NOT_FULFILLED' ? 'high' : ac.status === 'PARTIAL' ? 'medium' : 'low',
        category: 'acceptance_criteria',
        confidence: 1.0,
        auto_fixable: false
      })
    })
    
    // Convert code quality findings
    structuredResult.structured_data.code_quality_findings?.forEach((finding, index) => {
      const category = finding.type.toLowerCase().includes('security') ? 'security_issues' :
                      finding.type.toLowerCase().includes('performance') ? 'performance_issues' :
                      finding.type.toLowerCase().includes('bug') || finding.severity === 'critical' ? 'bugs' :
                      'code_quality'
      
      convertedFindings[category].push({
        id: `${category}_${index}`,
        text: finding.description,
        severity: finding.severity === 'critical' ? 'high' : finding.severity as 'high' | 'medium' | 'low',
        category,
        confidence: 0.9,
        auto_fixable: false
      })
    })
    
    // Convert action items to suggestions
    structuredResult.structured_data.action_items?.suggestions?.forEach((suggestion, index) => {
      convertedFindings.suggestions.push({
        id: `suggestion_${index}`,
        text: suggestion,
        severity: 'low',
        category: 'suggestions',
        confidence: 0.8,
        auto_fixable: false
      })
    })
    
    return {
      session_id: structuredResult.session_id,
      review_mode: structuredResult.review_type,
      branch_name: structuredResult.metadata.branch_name || 'unknown',
      structured_findings: convertedFindings,
      summary: {
        total_findings: structuredResult.structured_data.executive_summary.critical_issues + 
                       structuredResult.structured_data.executive_summary.warning_issues,
        high_severity_count: structuredResult.structured_data.executive_summary.critical_issues,
        files_changed: structuredResult.metadata.changed_files?.length || 0,
        review_length: structuredResult.raw_content?.length || 0,
        categories: {
          acceptance_criteria: convertedFindings.acceptance_criteria.length,
          code_quality: convertedFindings.code_quality.length,
          security_issues: convertedFindings.security_issues.length,
          performance_issues: convertedFindings.performance_issues.length,
          bugs: convertedFindings.bugs.length,
          suggestions: convertedFindings.suggestions.length
        },
        completion_status: 'completed'
      }
    }
  }
  
  // Handle legacy EnhancedReviewResults format
  const legacyResults = results as EnhancedReviewResults
  if (!legacyResults || !legacyResults.structured_findings) {
    return null
  }

  console.log('🔄 DATA CONVERSION - Starting conversion of structured findings')
  console.log('🔄 DATA CONVERSION - Original structured_findings:', legacyResults.structured_findings)
  
  const convertFinding = (finding: ReviewFindingData | string, category: FindingCategory, index: number): AIReviewFinding => {
    console.log(`🔄 DATA CONVERSION - Converting ${category} finding ${index}:`, finding)
    
    // Helper function to determine default severity based on category
    const getDefaultSeverity = (cat: FindingCategory): 'high' | 'medium' | 'low' => {
      switch (cat) {
        case 'security_issues':
        case 'bugs':
          return 'high'
        case 'suggestions':
          return 'low'
        default:
          return 'medium'
      }
    }
    
    if (typeof finding === 'string') {
      console.warn(`⚠️ DATA CONVERSION - ${category} finding ${index} is a string, converting:`, finding)
      return {
        id: `${category.substring(0, 3)}-${index}`,
        text: finding,
        severity: getDefaultSeverity(category),
        category: category,
        file: undefined,
        line: undefined,
        confidence: 0.8,
        auto_fixable: false
      }
    }
    
    return {
      id: `${category.substring(0, 3)}-${index}`,
      text: finding.text || String(finding),
      severity: finding.severity || getDefaultSeverity(category),
      category: category,
      file: finding.file,
      line: undefined,
      confidence: 0.8,
      auto_fixable: false
    }
  }

  const converted: ConvertedReviewResults = {
    session_id: legacyResults.session_id,
    review_mode: legacyResults.review_mode,
    branch_name: legacyResults.branch_name,
    structured_findings: {
      acceptance_criteria: (legacyResults.structured_findings.acceptance_criteria || []).map(
        (finding, index) => convertFinding(finding, 'acceptance_criteria', index)
      ),
      code_quality: (legacyResults.structured_findings.code_quality || []).map(
        (finding, index) => convertFinding(finding, 'code_quality', index)
      ),
      security_issues: (legacyResults.structured_findings.security_issues || []).map(
        (finding, index) => convertFinding(finding, 'security_issues', index)
      ),
      performance_issues: (legacyResults.structured_findings.performance_issues || []).map(
        (finding, index) => convertFinding(finding, 'performance_issues', index)
      ),
      bugs: (legacyResults.structured_findings.bugs || []).map(
        (finding, index) => convertFinding(finding, 'bugs', index)
      ),
      suggestions: (legacyResults.structured_findings.suggestions || []).map(
        (finding, index) => convertFinding(finding, 'suggestions', index)
      )
    },
    summary: legacyResults.summary
  }
  
  console.log('🔄 DATA CONVERSION - Conversion completed:', converted.structured_findings)
  return converted
}