/**
 * Data Validation and Sanitization Utilities
 * Ensures data integrity between backend and frontend
 */

export interface ReviewFinding {
  text: string;
  severity: 'high' | 'medium' | 'low';
  file?: string;
  line?: number;
  category?: string;
  suggestion?: string;
}

export interface ValidationResult<T> {
  isValid: boolean;
  data: T;
  errors: string[];
  warnings: string[];
}

export class DataValidator {
  private static logPrefix = '🔍 DATA VALIDATOR';

  /**
   * Validates and sanitizes a single review finding
   */
  static validateReviewFinding(finding: any, index: number): ValidationResult<ReviewFinding | null> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Handle null/undefined
    if (finding === null || finding === undefined) {
      errors.push(`Finding at index ${index} is null or undefined`);
      return { isValid: false, data: null, errors, warnings };
    }
    
    // Handle string findings
    if (typeof finding === 'string') {
      if (finding.trim().length === 0) {
        errors.push(`Finding at index ${index} is an empty string`);
        return { isValid: false, data: null, errors, warnings };
      }
      
      warnings.push(`Finding at index ${index} is a string, converting to object`);
      console.warn(`${this.logPrefix} - Converting string finding[${index}]:`, finding);
      
      return {
        isValid: true,
        data: {
          text: finding.trim(),
          severity: 'medium',
          file: undefined,
          line: undefined,
          category: undefined,
          suggestion: undefined
        },
        errors,
        warnings
      };
    }
    
    // Handle non-object findings
    if (typeof finding !== 'object') {
      errors.push(`Finding at index ${index} is not an object (type: ${typeof finding})`);
      return { isValid: false, data: null, errors, warnings };
    }
    
    // Validate text field
    let text = finding.text;
    if (!text || typeof text !== 'string') {
      // Try to extract text from other fields
      text = finding.message || finding.description || finding.title || 
             (typeof finding === 'object' ? JSON.stringify(finding) : String(finding));
      
      if (!text || text.length === 0) {
        errors.push(`Finding at index ${index} has no valid text content`);
        return { isValid: false, data: null, errors, warnings };
      }
      
      warnings.push(`Finding at index ${index} missing text field, extracted from other fields`);
    }
    
    // Clean and validate text
    const cleanText = text.trim();
    if (cleanText.length < 3) {
      warnings.push(`Finding at index ${index} has very short text: "${cleanText}"`);
    }
    
    if (/^[*#\-\s]*$/.test(cleanText)) {
      warnings.push(`Finding at index ${index} contains only markdown formatting characters`);
    }
    
    // Validate severity
    const validSeverities = ['high', 'medium', 'low'];
    let severity = finding.severity;
    if (!validSeverities.includes(severity)) {
      warnings.push(`Finding at index ${index} has invalid severity "${severity}", defaulting to "medium"`);
      severity = 'medium';
    }
    
    // Validate file and line
    let file = finding.file;
    let line = finding.line;
    
    if (file && typeof file !== 'string') {
      warnings.push(`Finding at index ${index} has invalid file field (type: ${typeof file})`);
      file = undefined;
    }
    
    if (line !== undefined && (typeof line !== 'number' || line < 1)) {
      warnings.push(`Finding at index ${index} has invalid line number: ${line}`);
      line = undefined;
    }
    
    return {
      isValid: true,
      data: {
        text: cleanText,
        severity,
        file,
        line,
        category: finding.category,
        suggestion: finding.suggestion
      },
      errors,
      warnings
    };
  }

  /**
   * Validates and sanitizes an array of review findings
   */
  static validateReviewFindings(findings: any[]): ValidationResult<ReviewFinding[]> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const validFindings: ReviewFinding[] = [];
    
    if (!Array.isArray(findings)) {
      errors.push(`Findings is not an array (type: ${typeof findings})`);
      console.error(`${this.logPrefix} - Findings is not an array:`, findings);
      return { isValid: false, data: [], errors, warnings };
    }
    
    findings.forEach((finding, index) => {
      const result = this.validateReviewFinding(finding, index);
      
      errors.push(...result.errors);
      warnings.push(...result.warnings);
      
      if (result.isValid && result.data) {
        validFindings.push(result.data);
      }
    });
    
    // Log summary
    if (errors.length > 0) {
      console.error(`${this.logPrefix} - Found ${errors.length} errors in findings:`, errors);
    }
    if (warnings.length > 0) {
      console.warn(`${this.logPrefix} - Found ${warnings.length} warnings in findings:`, warnings);
    }
    
    console.log(`${this.logPrefix} - Validated ${findings.length} findings, kept ${validFindings.length} valid ones`);
    
    return {
      isValid: errors.length === 0,
      data: validFindings,
      errors,
      warnings
    };
  }

  /**
   * Validates structured findings object
   */
  static validateStructuredFindings(structuredFindings: any): ValidationResult<{
    acceptance_criteria: ReviewFinding[];
    code_quality: ReviewFinding[];
    security_issues: ReviewFinding[];
    performance_issues: ReviewFinding[];
    bugs: ReviewFinding[];
    suggestions: ReviewFinding[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    if (!structuredFindings || typeof structuredFindings !== 'object') {
      errors.push('Structured findings is not an object');
      return {
        isValid: false,
        data: {
          acceptance_criteria: [],
          code_quality: [],
          security_issues: [],
          performance_issues: [],
          bugs: [],
          suggestions: []
        },
        errors,
        warnings
      };
    }
    
    const categories = [
      'acceptance_criteria',
      'code_quality', 
      'security_issues',
      'performance_issues',
      'bugs',
      'suggestions'
    ];
    
    const result: any = {};
    
    categories.forEach(category => {
      const categoryFindings = structuredFindings[category] || [];
      const validation = this.validateReviewFindings(categoryFindings);
      
      result[category] = validation.data;
      errors.push(...validation.errors.map(err => `${category}: ${err}`));
      warnings.push(...validation.warnings.map(warn => `${category}: ${warn}`));
    });
    
    return {
      isValid: errors.length === 0,
      data: result,
      errors,
      warnings
    };
  }

  /**
   * Validates complete review results object
   */
  static validateReviewResults(results: any): ValidationResult<any> {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    console.log(`${this.logPrefix} - Validating complete review results`);
    
    if (!results || typeof results !== 'object') {
      errors.push('Review results is not an object');
      return { isValid: false, data: null, errors, warnings };
    }
    
    // Validate required fields
    const requiredFields = ['session_id', 'review_mode', 'branch_name', 'timestamp'];
    requiredFields.forEach(field => {
      if (!results[field]) {
        errors.push(`Missing required field: ${field}`);
      }
    });
    
    // Validate structured findings
    if (results.structured_findings) {
      const structuredValidation = this.validateStructuredFindings(results.structured_findings);
      if (!structuredValidation.isValid) {
        errors.push(...structuredValidation.errors);
        warnings.push(...structuredValidation.warnings);
      }
      results.structured_findings = structuredValidation.data;
    } else {
      warnings.push('No structured findings found in results');
    }
    
    // Validate summary
    if (results.summary && typeof results.summary !== 'object') {
      warnings.push('Summary is not an object');
    }
    
    // Validate metadata
    if (results.metadata && typeof results.metadata !== 'object') {
      warnings.push('Metadata is not an object');
    }
    
    console.log(`${this.logPrefix} - Validation complete. Errors: ${errors.length}, Warnings: ${warnings.length}`);
    
    return {
      isValid: errors.length === 0,
      data: results,
      errors,
      warnings
    };
  }

  /**
   * Creates a sanitized summary from findings data
   */
  static generateSummaryFromFindings(structuredFindings: any): any {
    if (!structuredFindings) {
      return {
        total_findings: 0,
        security_issues: 0,
        potential_bugs: 0,
        quality_suggestions: 0
      };
    }
    
    const counts = {
      total_findings: 0,
      security_issues: (structuredFindings.security_issues || []).length,
      potential_bugs: (structuredFindings.bugs || []).length,
      quality_suggestions: (structuredFindings.suggestions || []).length
    };
    
    // Calculate total findings
    Object.keys(structuredFindings).forEach(category => {
      const findings = structuredFindings[category] || [];
      if (Array.isArray(findings)) {
        counts.total_findings += findings.length;
      }
    });
    
    return counts;
  }
}

export default DataValidator;