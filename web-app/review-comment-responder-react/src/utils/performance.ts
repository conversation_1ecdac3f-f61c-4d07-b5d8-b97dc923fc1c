/**
 * Performance Utilities
 * Production-ready performance optimization tools and utilities
 */

import React from 'react'
import { env } from '../config/environment'
import { performanceMonitor } from './monitoring'
import { logger } from './logger'

/**
 * Performance measurement interface
 */
export interface PerformanceMeasurement {
  name: string
  startTime: number
  endTime: number
  duration: number
  metadata?: Record<string, any>
}

/**
 * Component render performance metrics
 */
export interface ComponentMetrics {
  componentName: string
  renderCount: number
  totalRenderTime: number
  averageRenderTime: number
  slowestRender: number
  fastestRender: number
  lastRenderTime: number
}

/**
 * Memory usage information
 */
export interface MemoryInfo {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
  usage: number // percentage
  warning: boolean
}

/**
 * Network performance metrics
 */
export interface NetworkMetrics {
  endpoint: string
  method: string
  duration: number
  size: number
  status: number
  cached: boolean
  retries: number
}

/**
 * Performance observer manager
 */
class PerformanceObserverManager {
  private observers: Map<string, PerformanceObserver> = new Map()
  private measurements: Map<string, PerformanceMeasurement[]> = new Map()
  private componentLogger = logger.child('PerformanceObserverManager')

  constructor() {
    if (env.monitoring.enablePerformanceTracking) {
      this.initializeObservers()
    }
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    try {
      // Navigation timing observer
      this.createObserver('navigation', ['navigation'], (entries) => {
        for (const entry of entries) {
          const navEntry = entry as PerformanceNavigationTiming
          this.recordNavigationMetrics(navEntry)
        }
      })

      // Resource timing observer
      this.createObserver('resource', ['resource'], (entries) => {
        for (const entry of entries) {
          const resourceEntry = entry as PerformanceResourceTiming
          this.recordResourceMetrics(resourceEntry)
        }
      })

      // Paint timing observer
      this.createObserver('paint', ['paint'], (entries) => {
        for (const entry of entries) {
          this.recordPaintMetrics(entry)
        }
      })

      // Layout shift observer
      this.createObserver('layout-shift', ['layout-shift'], (entries) => {
        for (const entry of entries) {
          this.recordLayoutShift(entry as any)
        }
      })

      // Long task observer
      this.createObserver('longtask', ['longtask'], (entries) => {
        for (const entry of entries) {
          this.recordLongTask(entry)
        }
      })

      // User timing observer
      this.createObserver('measure', ['measure'], (entries) => {
        for (const entry of entries) {
          this.recordMeasurement(entry)
        }
      })

    } catch (error) {
      this.componentLogger.warn('Failed to initialize performance observers', error instanceof Error ? error : new Error(String(error)))
    }
  }

  /**
   * Create a performance observer
   */
  private createObserver(
    name: string, 
    entryTypes: string[], 
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries())
      })
      observer.observe({ entryTypes })
      this.observers.set(name, observer)
    } catch (error) {
      this.componentLogger.debug(`Performance observer '${name}' not supported`, error instanceof Error ? error : new Error(String(error)))
    }
  }

  /**
   * Record navigation metrics
   */
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      dns_lookup: entry.domainLookupEnd - entry.domainLookupStart,
      tcp_connect: entry.connectEnd - entry.connectStart,
      tls_handshake: entry.secureConnectionStart > 0 ? entry.connectEnd - entry.secureConnectionStart : 0,
      server_response: entry.responseStart - entry.requestStart,
      dom_processing: entry.domComplete - entry.responseEnd,
      page_load: entry.loadEventEnd - entry.fetchStart,
      dom_content_loaded: entry.domContentLoadedEventEnd - entry.fetchStart,
      first_byte: entry.responseStart - entry.fetchStart
    }

    Object.entries(metrics).forEach(([name, value]) => {
      if (value > 0) {
        performanceMonitor.recordMetric(`navigation_${name}`, value, {
          type: 'navigation'
        })
      }
    })

    this.componentLogger.debug('Navigation metrics recorded', metrics)
  }

  /**
   * Record resource metrics
   */
  private recordResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime
    const size = entry.transferSize || 0
    
    performanceMonitor.recordMetric('resource_load_time', duration, {
      resource: entry.name,
      type: entry.initiatorType,
      size: size.toString()
    })

    // Track slow resources
    if (duration > 1000) { // Slower than 1 second
      this.componentLogger.warn('Slow resource detected', {
        resource: entry.name,
        duration: `${duration.toFixed(2)}ms`,
        size: `${(size / 1024).toFixed(2)}KB`
      })
    }
  }

  /**
   * Record paint metrics
   */
  private recordPaintMetrics(entry: PerformanceEntry): void {
    performanceMonitor.recordMetric(`paint_${entry.name.replace('-', '_')}`, entry.startTime, {
      type: 'paint'
    })
  }

  /**
   * Record layout shifts
   */
  private recordLayoutShift(entry: any): void {
    if (entry.value > 0.1) { // Significant layout shift
      performanceMonitor.recordMetric('layout_shift', entry.value, {
        type: 'layout_shift',
        hadRecentInput: entry.hadRecentInput.toString()
      })

      this.componentLogger.warn('Layout shift detected', {
        value: entry.value,
        hadRecentInput: entry.hadRecentInput
      })
    }
  }

  /**
   * Record long tasks
   */
  private recordLongTask(entry: PerformanceEntry): void {
    const duration = entry.duration
    
    performanceMonitor.recordMetric('long_task', duration, {
      type: 'longtask'
    })

    this.componentLogger.warn('Long task detected', {
      duration: `${duration.toFixed(2)}ms`,
      startTime: entry.startTime
    })
  }

  /**
   * Record custom measurements
   */
  private recordMeasurement(entry: PerformanceEntry): void {
    const measurement: PerformanceMeasurement = {
      name: entry.name,
      startTime: entry.startTime,
      endTime: entry.startTime + entry.duration,
      duration: entry.duration
    }

    const measurements = this.measurements.get(entry.name) || []
    measurements.push(measurement)
    this.measurements.set(entry.name, measurements)

    performanceMonitor.recordMetric(`custom_${entry.name}`, entry.duration, {
      type: 'custom_measurement'
    })
  }

  /**
   * Get measurements for a specific metric
   */
  getMeasurements(name: string): PerformanceMeasurement[] {
    return this.measurements.get(name) || []
  }

  /**
   * Clear all measurements
   */
  clearMeasurements(): void {
    this.measurements.clear()
  }

  /**
   * Disconnect all observers
   */
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
  }
}

/**
 * Component performance tracker
 */
class ComponentPerformanceTracker {
  private metrics: Map<string, ComponentMetrics> = new Map()
  private renderTimes: Map<string, number> = new Map()
  private componentLogger = logger.child('ComponentPerformanceTracker')

  /**
   * Start tracking component render
   */
  startRender(componentName: string): void {
    this.renderTimes.set(componentName, performance.now())
  }

  /**
   * End tracking component render
   */
  endRender(componentName: string): number {
    const startTime = this.renderTimes.get(componentName)
    if (!startTime) {
      this.componentLogger.warn('No start time found for component', { componentName })
      return 0
    }

    const endTime = performance.now()
    const duration = endTime - startTime
    
    this.updateMetrics(componentName, duration)
    this.renderTimes.delete(componentName)

    // Record in monitoring system
    performanceMonitor.recordComponentRender(componentName, duration)

    return duration
  }

  /**
   * Update component metrics
   */
  private updateMetrics(componentName: string, duration: number): void {
    const existing = this.metrics.get(componentName)
    
    if (existing) {
      const newMetrics: ComponentMetrics = {
        componentName,
        renderCount: existing.renderCount + 1,
        totalRenderTime: existing.totalRenderTime + duration,
        averageRenderTime: (existing.totalRenderTime + duration) / (existing.renderCount + 1),
        slowestRender: Math.max(existing.slowestRender, duration),
        fastestRender: Math.min(existing.fastestRender, duration),
        lastRenderTime: duration
      }
      this.metrics.set(componentName, newMetrics)
    } else {
      const newMetrics: ComponentMetrics = {
        componentName,
        renderCount: 1,
        totalRenderTime: duration,
        averageRenderTime: duration,
        slowestRender: duration,
        fastestRender: duration,
        lastRenderTime: duration
      }
      this.metrics.set(componentName, newMetrics)
    }

    // Log slow renders
    if (duration > 100) { // Slower than 100ms
      this.componentLogger.warn('Slow component render detected', {
        componentName,
        duration: `${duration.toFixed(2)}ms`
      })
    }
  }

  /**
   * Get metrics for a component
   */
  getMetrics(componentName: string): ComponentMetrics | null {
    return this.metrics.get(componentName) || null
  }

  /**
   * Get all component metrics
   */
  getAllMetrics(): ComponentMetrics[] {
    return Array.from(this.metrics.values())
  }

  /**
   * Get top slowest components
   */
  getSlowestComponents(limit: number = 10): ComponentMetrics[] {
    return this.getAllMetrics()
      .sort((a, b) => b.averageRenderTime - a.averageRenderTime)
      .slice(0, limit)
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.metrics.clear()
    this.renderTimes.clear()
  }
}

/**
 * Memory monitor
 */
class MemoryMonitor {
  private componentLogger = logger.child('MemoryMonitor')
  private monitoringInterval?: NodeJS.Timeout
  private thresholds = {
    warning: 0.8, // 80% of heap limit
    critical: 0.9  // 90% of heap limit
  }

  constructor() {
    if (env.monitoring.enablePerformanceTracking) {
      this.startMonitoring()
    }
  }

  /**
   * Get current memory information
   */
  getMemoryInfo(): MemoryInfo | null {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usage = memory.usedJSHeapSize / memory.jsHeapSizeLimit
      
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        usage: usage * 100,
        warning: usage > this.thresholds.warning
      }
    }
    return null
  }

  /**
   * Start memory monitoring
   */
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      const memInfo = this.getMemoryInfo()
      if (memInfo) {
        // Record memory metrics
        performanceMonitor.recordMemoryUsage()

        // Check for memory warnings
        if (memInfo.warning) {
          this.componentLogger.warn('High memory usage detected', {
            usage: `${memInfo.usage.toFixed(2)}%`,
            usedMB: `${(memInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            limitMB: `${(memInfo.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
          })

          // Suggest garbage collection if usage is critical
          if (memInfo.usage > this.thresholds.critical * 100) {
            this.suggestGarbageCollection()
          }
        }
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Suggest garbage collection
   */
  private suggestGarbageCollection(): void {
    this.componentLogger.warn('Critical memory usage - consider garbage collection')
    
    // Force garbage collection if available (development only)
    if (env.features.debugMode && 'gc' in window) {
      (window as any).gc()
      this.componentLogger.info('Garbage collection triggered')
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = undefined
    }
  }
}

/**
 * Network performance tracker
 */
class NetworkPerformanceTracker {
  private requests: Map<string, NetworkMetrics[]> = new Map()
  private componentLogger = logger.child('NetworkPerformanceTracker')

  /**
   * Track network request
   */
  trackRequest(
    endpoint: string,
    method: string,
    duration: number,
    size: number,
    status: number,
    cached: boolean = false,
    retries: number = 0
  ): void {
    const metric: NetworkMetrics = {
      endpoint,
      method,
      duration,
      size,
      status,
      cached,
      retries
    }

    const requests = this.requests.get(endpoint) || []
    requests.push(metric)
    this.requests.set(endpoint, requests)

    // Record in monitoring system
    performanceMonitor.recordApiCall(endpoint, duration, status, size)

    // Log slow requests
    if (duration > 5000 && !cached) { // Slower than 5 seconds (not cached)
      this.componentLogger.warn('Slow network request detected', {
        endpoint,
        method,
        duration: `${duration.toFixed(2)}ms`,
        status,
        sizeMB: `${(size / 1024 / 1024).toFixed(2)}MB`
      })
    }
  }

  /**
   * Get metrics for an endpoint
   */
  getEndpointMetrics(endpoint: string): NetworkMetrics[] {
    return this.requests.get(endpoint) || []
  }

  /**
   * Get average response time for endpoint
   */
  getAverageResponseTime(endpoint: string): number {
    const metrics = this.getEndpointMetrics(endpoint)
    if (metrics.length === 0) return 0
    
    const total = metrics.reduce((sum, metric) => sum + metric.duration, 0)
    return total / metrics.length
  }

  /**
   * Get slowest endpoints
   */
  getSlowestEndpoints(limit: number = 10): Array<{endpoint: string, averageTime: number}> {
    const endpoints = Array.from(this.requests.keys())
    return endpoints
      .map(endpoint => ({
        endpoint,
        averageTime: this.getAverageResponseTime(endpoint)
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, limit)
  }

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    this.requests.clear()
  }
}

/**
 * Global performance manager instances
 */
export const performanceObserverManager = new PerformanceObserverManager()
export const componentPerformanceTracker = new ComponentPerformanceTracker()
export const memoryMonitor = new MemoryMonitor()
export const networkPerformanceTracker = new NetworkPerformanceTracker()

/**
 * Performance utilities
 */
export const PerformanceUtils = {
  /**
   * Measure function execution time
   */
  measure: async <T>(name: string, fn: () => Promise<T> | T): Promise<T> => {
    const startTime = performance.now()
    performance.mark(`${name}-start`)
    
    try {
      const result = await fn()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
      
      performanceMonitor.recordMetric(name, duration, { type: 'function_execution' })
      
      return result
    } catch (error) {
      performance.mark(`${name}-error`)
      throw error
    }
  },

  /**
   * Debounce function for performance optimization
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  },

  /**
   * Throttle function for performance optimization
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): ((...args: Parameters<T>) => void) => {
    let lastCall = 0
    return (...args: Parameters<T>) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  },

  /**
   * Lazy load component
   */
  lazyLoad: <T extends React.ComponentType<any>>(
    importFn: () => Promise<{ default: T }>,
    fallback?: React.ComponentType
  ) => {
    const LazyComponent = React.lazy(importFn)
    
    return (props: any) => 
      React.createElement(React.Suspense, {
        fallback: fallback ? React.createElement(fallback) : React.createElement('div', {}, 'Loading...')
      }, React.createElement(LazyComponent, props))
  },

  /**
   * Virtual list for large datasets
   */
  createVirtualList: (items: any[], itemHeight: number, containerHeight: number) => {
    const visibleItems = Math.ceil(containerHeight / itemHeight)
    const totalHeight = items.length * itemHeight
    
    return {
      totalHeight,
      visibleItems,
      getVisibleRange: (scrollTop: number) => {
        const startIndex = Math.floor(scrollTop / itemHeight)
        const endIndex = Math.min(startIndex + visibleItems, items.length)
        return { startIndex, endIndex }
      }
    }
  },

  /**
   * Optimize images for performance
   */
  optimizeImage: (
    src: string,
    options: {
      width?: number
      height?: number
      quality?: number
      format?: 'webp' | 'avif' | 'jpeg' | 'png'
    } = {}
  ): string => {
    if (!src) return src
    
    // In a real implementation, this would use an image optimization service
    // For now, return the original src with options considered
    if (options.width || options.height || options.quality || options.format) {
      // Options would be used for actual optimization
      console.debug('Image optimization options:', options)
    }
    return src
  },

  /**
   * Preload critical resources
   */
  preloadResources: (resources: Array<{ href: string, as: string, type?: string }>) => {
    resources.forEach(resource => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = resource.href
      link.as = resource.as
      if (resource.type) {
        link.type = resource.type
      }
      document.head.appendChild(link)
    })
  },

  /**
   * Get performance summary
   */
  getPerformanceSummary: () => {
    const memoryInfo = memoryMonitor.getMemoryInfo()
    const componentMetrics = componentPerformanceTracker.getAllMetrics()
    const slowestComponents = componentPerformanceTracker.getSlowestComponents(5)
    const slowestEndpoints = networkPerformanceTracker.getSlowestEndpoints(5)

    return {
      memory: memoryInfo,
      components: {
        total: componentMetrics.length,
        slowest: slowestComponents
      },
      network: {
        slowestEndpoints
      },
      timestamp: Date.now()
    }
  },

  /**
   * Cleanup performance monitoring
   */
  cleanup: () => {
    performanceObserverManager.disconnect()
    componentPerformanceTracker.clearMetrics()
    memoryMonitor.stopMonitoring()
    networkPerformanceTracker.clearMetrics()
  }
}

/**
 * React HOC for performance tracking
 */
export const withPerformanceTracking = <P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) => {
  const name = componentName || Component.displayName || Component.name
  
  const WrappedComponent = (props: P) => {
    React.useLayoutEffect(() => {
      componentPerformanceTracker.startRender(name)
      return () => {
        componentPerformanceTracker.endRender(name)
      }
    })

    return React.createElement(Component, props)
  }
  
  WrappedComponent.displayName = `withPerformanceTracking(${name})`
  return WrappedComponent
}

/**
 * React hook for performance tracking
 */
export const usePerformanceTracking = (componentName: string) => {
  React.useLayoutEffect(() => {
    componentPerformanceTracker.startRender(componentName)
    return () => {
      componentPerformanceTracker.endRender(componentName)
    }
  }, [componentName])

  return {
    measureAsync: <T>(name: string, fn: () => Promise<T>) => PerformanceUtils.measure(name, fn),
    getMetrics: () => componentPerformanceTracker.getMetrics(componentName)
  }
}

export default PerformanceUtils