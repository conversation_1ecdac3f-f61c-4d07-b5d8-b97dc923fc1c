/**
 * Jira Field Detection Utilities
 * Helps identify common custom fields used for code reviewers in Jira
 */

export interface JiraFieldAnalysis {
  possibleReviewerFields: string[]
  commonCustomFields: Record<string, string>
  fieldSuggestions: Array<{
    field: string
    confidence: 'high' | 'medium' | 'low'
    description: string
  }>
}

/**
 * Common custom field IDs used for code reviewers across different Jira instances
 */
export const COMMON_REVIEWER_FIELDS = [
  'customfield_10009', // Code Reviewer
  'customfield_10010', // Technical Reviewer
  'customfield_10011', // Review Assignee
  'customfield_10012', // Reviewer
  'customfield_10013', // Code Review Lead
  'customfield_10014', // Technical Lead
  'customfield_10015', // Dev Reviewer
  'customfield_10016', // Story Points (sometimes used for reviewer assignment)
  'customfield_10017', // Sprint
  'customfield_10018', // Epic Link
  'customfield_10019', // QA Assignee
  'customfield_10020', // Acceptance Criteria (confirmed in codebase)
  'customfield_10021', // Additional AC field (confirmed in codebase)
  'customfield_10030', // Secondary reviewer
  'customfield_10031', // Peer reviewer
  'customfield_10040', // Code review status
  'customfield_10041', // Review completion date
  'customfield_10050', // Development reviewer
  'customfield_10051', // Lead reviewer
] as const

/**
 * Field names that commonly indicate reviewer fields
 */
export const REVIEWER_FIELD_PATTERNS = [
  /.*review.*assignee.*/i,
  /.*code.*review.*/i,
  /.*technical.*review.*/i,
  /.*dev.*review.*/i,
  /.*peer.*review.*/i,
  /.*review.*lead.*/i,
  /.*reviewer.*/i,
  /.*code.*assignee.*/i,
  /.*tech.*lead.*/i,
  /.*development.*lead.*/i,
] as const

/**
 * Analyzes Jira ticket fields to identify potential reviewer fields
 */
export function analyzeJiraFields(tickets: any[]): JiraFieldAnalysis {
  const fieldAnalysis: JiraFieldAnalysis = {
    possibleReviewerFields: [],
    commonCustomFields: {},
    fieldSuggestions: []
  }

  if (!tickets || tickets.length === 0) {
    return fieldAnalysis
  }

  // Count field occurrences and analyze content
  const fieldStats: Record<string, {
    count: number
    hasUserObjects: number
    hasUserArrays: number
    fieldName?: string
    sampleValues: string[]
  }> = {}

  tickets.forEach(ticket => {
    const fields = ticket.fields || {}
    
    Object.entries(fields).forEach(([fieldKey, fieldValue]) => {
      if (!fieldStats[fieldKey]) {
        fieldStats[fieldKey] = {
          count: 0,
          hasUserObjects: 0,
          hasUserArrays: 0,
          sampleValues: []
        }
      }

      const stats = fieldStats[fieldKey]
      stats.count++

      // Check if field contains user objects (typical for reviewer fields)
      if (fieldValue && typeof fieldValue === 'object') {
        if (Array.isArray(fieldValue)) {
          // Array of users
          if (fieldValue.length > 0 && fieldValue[0] && typeof fieldValue[0] === 'object' && 'displayName' in fieldValue[0]) {
            stats.hasUserArrays++
            if (stats.sampleValues.length < 3) {
              stats.sampleValues.push(`[${fieldValue.map((u: any) => u.displayName).join(', ')}]`)
            }
          }
        } else if (fieldValue && typeof fieldValue === 'object' && 'displayName' in fieldValue) {
          // Single user object
          stats.hasUserObjects++
          if (stats.sampleValues.length < 3) {
            stats.sampleValues.push(fieldValue.displayName as string)
          }
        }
      }
    })
  })

  // Analyze field names and patterns
  Object.entries(fieldStats).forEach(([fieldKey, stats]) => {
    const isCustomField = fieldKey.startsWith('customfield_')
    const hasUsers = stats.hasUserObjects > 0 || stats.hasUserArrays > 0
    const isCommonInTickets = stats.count > tickets.length * 0.1 // Present in >10% of tickets

    // Check if field name matches reviewer patterns
    const matchesReviewerPattern = REVIEWER_FIELD_PATTERNS.some(pattern => 
      pattern.test(fieldKey) || (stats.fieldName && pattern.test(stats.fieldName))
    )

    // Check if it's a known reviewer field
    const isKnownReviewerField = COMMON_REVIEWER_FIELDS.includes(fieldKey as any)

    if (isCustomField && (hasUsers || matchesReviewerPattern || isKnownReviewerField)) {
      let confidence: 'high' | 'medium' | 'low' = 'low'
      
      if (hasUsers && matchesReviewerPattern) {
        confidence = 'high'
      } else if (hasUsers && isKnownReviewerField) {
        confidence = 'high'
      } else if (hasUsers || matchesReviewerPattern) {
        confidence = 'medium'
      }

      fieldAnalysis.possibleReviewerFields.push(fieldKey)
      fieldAnalysis.fieldSuggestions.push({
        field: fieldKey,
        confidence,
        description: `${confidence.toUpperCase()} confidence: ${
          hasUsers ? 'Contains user objects, ' : ''
        }${matchesReviewerPattern ? 'Matches reviewer pattern, ' : ''}${
          isKnownReviewerField ? 'Known reviewer field, ' : ''
        }Present in ${stats.count}/${tickets.length} tickets`
      })

      if (isCommonInTickets) {
        fieldAnalysis.commonCustomFields[fieldKey] = stats.sampleValues[0] || 'No sample data'
      }
    }
  })

  // Sort suggestions by confidence
  fieldAnalysis.fieldSuggestions.sort((a, b) => {
    const confidenceOrder = { high: 3, medium: 2, low: 1 }
    return confidenceOrder[b.confidence] - confidenceOrder[a.confidence]
  })

  return fieldAnalysis
}

/**
 * Gets the most likely reviewer field from analysis
 */
export function getBestReviewerField(analysis: JiraFieldAnalysis): string | null {
  const highConfidenceFields = analysis.fieldSuggestions
    .filter(s => s.confidence === 'high')
    .map(s => s.field)

  if (highConfidenceFields.length > 0) {
    return highConfidenceFields[0]
  }

  const mediumConfidenceFields = analysis.fieldSuggestions
    .filter(s => s.confidence === 'medium')
    .map(s => s.field)

  return mediumConfidenceFields[0] || null
}

/**
 * Format field analysis for debugging/logging
 */
export function formatFieldAnalysis(analysis: JiraFieldAnalysis): string {
  const lines = [
    '🔍 Jira Reviewer Field Analysis:',
    '',
    `Found ${analysis.possibleReviewerFields.length} potential reviewer fields:`
  ]

  analysis.fieldSuggestions.forEach((suggestion, index) => {
    const confidenceEmoji = suggestion.confidence === 'high' ? '🟢' : 
                           suggestion.confidence === 'medium' ? '🟡' : '🔴'
    lines.push(`${index + 1}. ${confidenceEmoji} ${suggestion.field} - ${suggestion.description}`)
  })

  if (analysis.fieldSuggestions.length === 0) {
    lines.push('❌ No obvious reviewer fields detected')
    lines.push('')
    lines.push('💡 Common fields to check manually:')
    COMMON_REVIEWER_FIELDS.slice(0, 10).forEach(field => {
      lines.push(`   - ${field}`)
    })
  } else {
    const bestField = getBestReviewerField(analysis)
    if (bestField) {
      lines.push('')
      lines.push(`✅ Recommended field: ${bestField}`)
    }
  }

  return lines.join('\n')
}

/**
 * Extract reviewer information from a ticket using detected field
 */
export function extractReviewerFromTicket(ticket: any, reviewerField: string): string | string[] | null {
  const fields = ticket.fields || {}
  const reviewerData = fields[reviewerField]

  if (!reviewerData) {
    return null
  }

  if (Array.isArray(reviewerData)) {
    return reviewerData
      .filter(user => user && user.displayName)
      .map(user => user.displayName)
  }

  if (typeof reviewerData === 'object' && reviewerData.displayName) {
    return reviewerData.displayName
  }

  if (typeof reviewerData === 'string') {
    return reviewerData
  }

  return null
}