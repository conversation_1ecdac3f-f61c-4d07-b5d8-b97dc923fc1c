/**
 * Structured Logging System
 * Production-ready logging with levels, formatting, and remote shipping
 */

import { env } from '../config/environment'

/**
 * Log levels
 */
export const LogLevel = {
  TRACE: 0,
  DEBUG: 1,
  INFO: 2,
  WARN: 3,
  ERROR: 4,
  FATAL: 5
} as const

export type LogLevel = typeof LogLevel[keyof typeof LogLevel]

/**
 * Log entry interface
 */
export interface LogEntry {
  timestamp: string
  level: LogLevel
  levelName: string
  message: string
  component?: string
  userId?: string
  sessionId: string
  context?: Record<string, any>
  error?: {
    name: string
    message: string
    stack?: string
  }
  meta: {
    environment: string
    version: string
    url: string
    userAgent: string
  }
}

/**
 * Logger configuration
 */
interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemoteShipping: boolean
  remoteEndpoint?: string
  bufferSize: number
  flushInterval: number
  anonymizeData: boolean
  includeStackTrace: boolean
}

/**
 * Log formatter interface
 */
interface LogFormatter {
  format(entry: LogEntry): string
}

/**
 * Console formatter for development
 */
class ConsoleFormatter implements LogFormatter {
  private colors = {
    [LogLevel.TRACE]: '\x1b[90m',    // Gray
    [LogLevel.DEBUG]: '\x1b[36m',    // Cyan
    [LogLevel.INFO]: '\x1b[32m',     // Green
    [LogLevel.WARN]: '\x1b[33m',     // Yellow
    [LogLevel.ERROR]: '\x1b[31m',    // Red
    [LogLevel.FATAL]: '\x1b[35m'     // Magenta
  }
  
  private reset = '\x1b[0m'

  format(entry: LogEntry): string {
    const color = this.colors[entry.level] || ''
    const timestamp = new Date(entry.timestamp).toISOString()
    const component = entry.component ? `[${entry.component}]` : ''
    
    let message = `${color}${timestamp} ${entry.levelName}${this.reset} ${component} ${entry.message}`
    
    if (entry.context && Object.keys(entry.context).length > 0) {
      message += `\n  Context: ${JSON.stringify(entry.context, null, 2)}`
    }
    
    if (entry.error) {
      message += `\n  Error: ${entry.error.name}: ${entry.error.message}`
      if (entry.error.stack) {
        message += `\n  Stack: ${entry.error.stack}`
      }
    }
    
    return message
  }
}

/**
 * JSON formatter for production
 */
class JsonFormatter implements LogFormatter {
  format(entry: LogEntry): string {
    return JSON.stringify(entry)
  }
}

/**
 * Remote log shipper
 */
class RemoteLogShipper {
  private buffer: LogEntry[] = []
  private config: LoggerConfig
  private flushTimer?: NodeJS.Timeout

  constructor(config: LoggerConfig) {
    this.config = config
    this.startFlushTimer()
  }

  /**
   * Add log entry to buffer
   */
  addEntry(entry: LogEntry): void {
    this.buffer.push(entry)
    
    if (this.buffer.length >= this.config.bufferSize) {
      this.flush()
    }
  }

  /**
   * Flush buffer to remote endpoint
   */
  async flush(): Promise<void> {
    if (this.buffer.length === 0 || !this.config.remoteEndpoint) return

    const entriesToShip = [...this.buffer]
    this.buffer = []

    try {
      const response = await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          logs: entriesToShip,
          metadata: {
            source: 'frontend',
            environment: env.NODE_ENV,
            version: env.deployment.version,
            timestamp: Date.now()
          }
        })
      })

      if (!response.ok) {
        console.warn('Failed to ship logs:', response.status, response.statusText)
      }

    } catch (error) {
      console.warn('Log shipping failed:', error)
      // Re-add entries to buffer for retry
      this.buffer.unshift(...entriesToShip)
    }
  }

  /**
   * Start automatic flushing
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flush()
  }
}

/**
 * Main Logger class
 */
class Logger {
  private config: LoggerConfig
  private formatter: LogFormatter
  private remoteShipper?: RemoteLogShipper
  private sessionId: string

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: env.isDevelopment ? LogLevel.DEBUG : LogLevel.INFO,
      enableConsole: env.isDevelopment || env.features.debugMode,
      enableRemoteShipping: env.isProduction && env.monitoring.enableErrorTracking,
      bufferSize: 50,
      flushInterval: 30000, // 30 seconds
      anonymizeData: env.monitoring.anonymizeData,
      includeStackTrace: env.isDevelopment,
      ...config
    }

    this.formatter = env.isDevelopment 
      ? new ConsoleFormatter() 
      : new JsonFormatter()

    this.sessionId = this.generateSessionId()

    if (this.config.enableRemoteShipping) {
      this.remoteShipper = new RemoteLogShipper(this.config)
    }

    // Setup cleanup
    window.addEventListener('beforeunload', () => {
      this.remoteShipper?.destroy()
    })
  }

  /**
   * Log trace message
   */
  trace(message: string, context?: Record<string, any>, component?: string): void {
    this.log(LogLevel.TRACE, message, context, component)
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: Record<string, any>, component?: string): void {
    this.log(LogLevel.DEBUG, message, context, component)
  }

  /**
   * Log info message
   */
  info(message: string, context?: Record<string, any>, component?: string): void {
    this.log(LogLevel.INFO, message, context, component)
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: Record<string, any>, component?: string): void {
    this.log(LogLevel.WARN, message, context, component)
  }

  /**
   * Log error message
   */
  error(message: string, error?: Error, context?: Record<string, any>, component?: string): void {
    const errorInfo = error ? {
      name: error.name,
      message: error.message,
      stack: this.config.includeStackTrace ? error.stack : undefined
    } : undefined

    this.log(LogLevel.ERROR, message, context, component, errorInfo)
  }

  /**
   * Log fatal message
   */
  fatal(message: string, error?: Error, context?: Record<string, any>, component?: string): void {
    const errorInfo = error ? {
      name: error.name,
      message: error.message,
      stack: error.stack // Always include stack for fatal errors
    } : undefined

    this.log(LogLevel.FATAL, message, context, component, errorInfo)
  }

  /**
   * Log API request
   */
  logApiRequest(method: string, url: string, status: number, duration: number, component?: string): void {
    const level = status >= 400 ? LogLevel.ERROR : LogLevel.INFO
    const message = `API ${method} ${url} - ${status} (${duration}ms)`
    
    this.log(level, message, {
      apiRequest: {
        method,
        url: this.config.anonymizeData ? this.anonymizeUrl(url) : url,
        status,
        duration
      }
    }, component)
  }

  /**
   * Log WebSocket event
   */
  logWebSocketEvent(event: string, data?: any, component?: string): void {
    this.log(LogLevel.INFO, `WebSocket ${event}`, {
      websocket: {
        event,
        data: this.config.anonymizeData ? '[ANONYMIZED]' : data
      }
    }, component)
  }

  /**
   * Log user action
   */
  logUserAction(action: string, details?: Record<string, any>, component?: string): void {
    this.log(LogLevel.INFO, `User action: ${action}`, {
      userAction: {
        action,
        details: this.config.anonymizeData ? this.anonymizeObject(details) : details
      }
    }, component)
  }

  /**
   * Log performance metric
   */
  logPerformance(metric: string, value: number, unit: string = 'ms', component?: string): void {
    this.log(LogLevel.INFO, `Performance: ${metric} = ${value}${unit}`, {
      performance: {
        metric,
        value,
        unit
      }
    }, component)
  }

  /**
   * Log multi-agent review event
   */
  logMultiAgentEvent(event: string, reviewId?: string, agentType?: string, details?: Record<string, any>, component?: string): void {
    this.log(LogLevel.INFO, `Multi-Agent: ${event}`, {
      multiAgent: {
        event,
        reviewId,
        agentType,
        details
      }
    }, component)
  }

  /**
   * Core logging method
   */
  private log(
    level: LogLevel, 
    message: string, 
    context?: Record<string, any>, 
    component?: string,
    error?: LogEntry['error']
  ): void {
    // Check if level meets threshold
    if (level < this.config.level) return

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      levelName: Object.entries(LogLevel).find(([_, val]) => val === level)?.[0] || 'UNKNOWN',
      message,
      component,
      sessionId: this.sessionId,
      context: this.config.anonymizeData ? this.anonymizeObject(context) : context,
      error,
      meta: {
        environment: env.NODE_ENV,
        version: env.deployment.version,
        url: this.config.anonymizeData ? this.anonymizeUrl(window.location.href) : window.location.href,
        userAgent: this.config.anonymizeData ? this.anonymizeUserAgent(navigator.userAgent) : navigator.userAgent
      }
    }

    // Console output
    if (this.config.enableConsole) {
      const formattedMessage = this.formatter.format(entry)
      this.writeToConsole(level, formattedMessage)
    }

    // Remote shipping
    if (this.remoteShipper) {
      this.remoteShipper.addEntry(entry)
    }
  }

  /**
   * Write to console with appropriate method
   */
  private writeToConsole(level: LogLevel, message: string): void {
    switch (level) {
      case LogLevel.TRACE:
      case LogLevel.DEBUG:
        console.debug(message)
        break
      case LogLevel.INFO:
        console.info(message)
        break
      case LogLevel.WARN:
        console.warn(message)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message)
        break
    }
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    let sessionId = sessionStorage.getItem('logger_session_id')
    if (!sessionId) {
      sessionId = `log-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('logger_session_id', sessionId)
    }
    return sessionId
  }

  /**
   * Anonymize URL
   */
  private anonymizeUrl(url: string): string {
    try {
      const urlObj = new URL(url)
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`
    } catch {
      return '[INVALID_URL]'
    }
  }

  /**
   * Anonymize user agent
   */
  private anonymizeUserAgent(userAgent: string): string {
    return userAgent.replace(/\d+\.\d+\.\d+/g, 'X.X.X')
  }

  /**
   * Anonymize object by removing sensitive data
   */
  private anonymizeObject(obj?: Record<string, any>): Record<string, any> | undefined {
    if (!obj) return obj

    const anonymized = { ...obj }
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'credential']

    const anonymizeValue = (value: any): any => {
      if (typeof value === 'string') {
        // Check if key suggests sensitive data
        const key = Object.keys(anonymized).find(k => 
          sensitiveKeys.some(sensitiveKey => 
            k.toLowerCase().includes(sensitiveKey)
          )
        )
        return key ? '[REDACTED]' : value
      } else if (typeof value === 'object' && value !== null) {
        return this.anonymizeObject(value)
      }
      return value
    }

    Object.keys(anonymized).forEach(key => {
      anonymized[key] = anonymizeValue(anonymized[key])
    })

    return anonymized
  }

  /**
   * Set user context
   */
  setUserContext(userId?: string): void {
    // Implementation would set user ID for subsequent logs
    if (this.config.anonymizeData) {
      userId = userId ? `user-${this.hashString(userId)}` : undefined
    }
    // Store in session or instance variable
  }

  /**
   * Hash string for anonymization
   */
  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  /**
   * Create child logger with component context
   */
  child(component: string): ComponentLogger {
    return new ComponentLogger(this, component)
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get current configuration
   */
  getConfig(): LoggerConfig {
    return { ...this.config }
  }
}

/**
 * Component-specific logger
 */
class ComponentLogger {
  private parent: Logger
  private component: string
  
  constructor(parent: Logger, component: string) {
    this.parent = parent
    this.component = component
  }

  trace(message: string, context?: Record<string, any>): void {
    this.parent.trace(message, context, this.component)
  }

  debug(message: string, context?: Record<string, any>): void {
    this.parent.debug(message, context, this.component)
  }

  info(message: string, context?: Record<string, any>): void {
    this.parent.info(message, context, this.component)
  }

  warn(message: string, context?: Record<string, any>): void {
    this.parent.warn(message, context, this.component)
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    this.parent.error(message, error, context, this.component)
  }

  fatal(message: string, error?: Error, context?: Record<string, any>): void {
    this.parent.fatal(message, error, context, this.component)
  }

  logApiRequest(method: string, url: string, status: number, duration: number): void {
    this.parent.logApiRequest(method, url, status, duration, this.component)
  }

  logWebSocketEvent(event: string, data?: any): void {
    this.parent.logWebSocketEvent(event, data, this.component)
  }

  logUserAction(action: string, details?: Record<string, any>): void {
    this.parent.logUserAction(action, details, this.component)
  }

  logPerformance(metric: string, value: number, unit?: string): void {
    this.parent.logPerformance(metric, value, unit, this.component)
  }

  logMultiAgentEvent(event: string, reviewId?: string, agentType?: string, details?: Record<string, any>): void {
    this.parent.logMultiAgentEvent(event, reviewId, agentType, details, this.component)
  }
}

/**
 * Global logger instance
 */
export const logger = new Logger()

/**
 * Logger utilities
 */
export const LoggerUtils = {
  /**
   * Create logger for component
   */
  createComponentLogger: (component: string): ComponentLogger => {
    return logger.child(component)
  },

  /**
   * Log async function execution
   */
  logAsyncExecution: async <T>(
    name: string,
    fn: () => Promise<T>,
    component?: string
  ): Promise<T> => {
    const startTime = performance.now()
    logger.debug(`Starting: ${name}`, undefined, component)
    
    try {
      const result = await fn()
      const duration = performance.now() - startTime
      logger.info(`Completed: ${name}`, { duration: `${duration.toFixed(2)}ms` }, component)
      return result
    } catch (error) {
      const duration = performance.now() - startTime
      logger.error(`Failed: ${name}`, error instanceof Error ? error : new Error(String(error)), 
        { duration: `${duration.toFixed(2)}ms` }, component)
      throw error
    }
  },

  /**
   * Create structured error log
   */
  logStructuredError: (
    message: string,
    error: Error,
    context?: Record<string, any>,
    component?: string
  ): void => {
    logger.error(message, error, {
      ...context,
      errorDetails: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    }, component)
  },

  /**
   * Set global user context
   */
  setUserContext: (userId?: string): void => {
    logger.setUserContext(userId)
  }
}

/**
 * Initialize logging system
 */
export const initializeLogging = (): void => {
  if (env.features.debugMode) {
    logger.info('Logging system initialized', {
      level: Object.entries(LogLevel).find(([_, val]) => val === logger.getConfig().level)?.[0] || 'UNKNOWN',
      console: logger.getConfig().enableConsole,
      remote: logger.getConfig().enableRemoteShipping,
      anonymize: logger.getConfig().anonymizeData
    })
  }
}

export { Logger, ComponentLogger }
export default logger