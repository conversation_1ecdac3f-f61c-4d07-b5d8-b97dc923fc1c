import React from 'react'
import { 
  <PERSON>, 
  CheckCircle,
  Alert<PERSON>riangle,
  Clock
} from 'lucide-react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from './ui/card'
import { Progress } from './ui/progress'
import { ClaudeProgressTimeline } from './ClaudeProgressTimeline'
import type { ReviewSession } from '../types/enhanced-review'

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

interface ReviewProgress {
  session_id: string
  status: string
  progress: number
  message: string
  worktree_path?: string
}

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
}

interface ReviewProgressStepProps {
  activeSession: ReviewSession | null
  selectedPR: AssignedPR | null
  progress: ReviewProgress | null
  progressEvents: ProgressEvent[]
  currentProgressStep: string
  mode: string
}

export const ReviewProgressStep: React.FC<ReviewProgressStepProps> = ({
  activeSession,
  selectedPR,
  progress,
  progressEvents,
  currentProgressStep,
  mode
}) => {
  if (!activeSession) return null

  const getStatusIcon = () => {
    switch (activeSession.status) {
      case 'initializing':
        return <Clock className="h-5 w-5 text-yellow-500 animate-pulse" />
      case 'running':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }

  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <h3 className="text-lg font-medium">
              Review in Progress - {mode.charAt(0).toUpperCase() + mode.slice(1)} Mode
            </h3>
            <p className="text-sm text-muted-foreground font-normal">
              {progress?.message || activeSession.progress_message || 'Processing review...'}
            </p>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{Math.round(activeSession.progress || progress?.progress || 0)}%</span>
          </div>
          <Progress value={activeSession.progress || progress?.progress || 0} className="h-2" />
        </div>
        
        {selectedPR && (
          <div className="text-sm space-y-1">
            <div><strong>Branch:</strong> {selectedPR.branch}</div>
            <div><strong>Repository:</strong> {selectedPR.repository}</div>
            {(progress?.worktree_path || activeSession.worktree_path) && (
              <div><strong>Worktree:</strong> <code className="text-xs bg-muted px-1 py-0.5 rounded">{progress?.worktree_path || activeSession.worktree_path}</code></div>
            )}
          </div>
        )}

        {activeSession.status === 'error' && activeSession.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
            <strong>Error:</strong> {activeSession.error}
          </div>
        )}

        {/* Claude Progress Timeline */}
        <ClaudeProgressTimeline
          events={progressEvents}
          currentStep={currentProgressStep}
          className="mt-4"
        />
      </CardContent>
    </Card>
  )
}