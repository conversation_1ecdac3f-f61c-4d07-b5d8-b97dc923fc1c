import React, { useState, useEffect, useMemo } from 'react'
import { 
  <PERSON>, 
  CheckCircle,
  AlertTriangle,
  <PERSON>,
  C<PERSON>,
  User
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card'
import { Progress } from './ui/progress'
import { Badge } from './ui/badge'
import { ClaudeProgressTimeline } from './ClaudeProgressTimeline'
import { MultiAgentProgressTracker, type MultiAgentReviewStatus } from './MultiAgentProgressTracker'
import { ConnectionStatusIndicator, type ConnectionStatus } from './ConnectionStatusIndicator'
import { useMultiAgentReview } from '../hooks/useMultiAgentReview'
import type { ReviewSession } from '../types/enhanced-review'

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

interface ReviewProgress {
  session_id: string
  status: string
  progress: number
  message: string
  worktree_path?: string
}

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
}

interface ReviewProgressStepProps {
  activeSession: ReviewSession | null
  selectedPR: AssignedPR | null
  progress: ReviewProgress | null
  progressEvents: ProgressEvent[]
  currentProgressStep: string
  mode: string
  // Multi-agent specific props
  multiAgentStatus?: MultiAgentReviewStatus | null
  onMultiAgentStatusUpdate?: (status: MultiAgentReviewStatus) => void
  // New props for enhanced multi-agent integration
  enableMultiAgentHook?: boolean
  multiAgentReviewId?: string | null
  onMultiAgentReviewStarted?: (reviewId: string) => void
  onMultiAgentError?: (error: string) => void
}

export const ReviewProgressStep: React.FC<ReviewProgressStepProps> = ({
  activeSession,
  selectedPR,
  progress,
  progressEvents,
  currentProgressStep,
  mode,
  multiAgentStatus,
  onMultiAgentStatusUpdate,
  enableMultiAgentHook = false,
  multiAgentReviewId: _multiAgentReviewId,
  onMultiAgentReviewStarted,
  onMultiAgentError
}) => {
  const [localMultiAgentStatus, setLocalMultiAgentStatus] = useState<MultiAgentReviewStatus | null>(
    multiAgentStatus || null
  )
  
  // Multi-agent mode detection
  const isMultiAgentMode = mode === 'multi_agent' || mode === 'full'
  
  // Enhanced Multi-Agent Hook Integration
  const multiAgentHook = useMultiAgentReview({
    enable_websocket: enableMultiAgentHook && isMultiAgentMode,
    enable_debug_logs: import.meta.env.NODE_ENV === 'development',
    polling_interval: 5000,
    auto_fetch_results: true,
    on_review_started: (response) => {
      console.log('🚀 Multi-Agent Review Started:', response)
      onMultiAgentReviewStarted?.(response.review_id)
    },
    on_status_update: (status) => {
      console.log('📊 Multi-Agent Status Update:', status)
    },
    on_error: (error) => {
      console.error('❌ Multi-Agent Error:', error)
      onMultiAgentError?.(error.error_message)
    }
  })
  
  // Convert hook state to component-compatible format
  const enhancedMultiAgentStatus: MultiAgentReviewStatus | null = useMemo(() => {
    if (!enableMultiAgentHook || !multiAgentHook.state.review_id) {
      return localMultiAgentStatus
    }
    
    return {
      review_id: multiAgentHook.state.review_id,
      status: multiAgentHook.state.status,
      progress: multiAgentHook.state.progress,
      agent_statuses: multiAgentHook.state.agent_statuses,
      started_at: multiAgentHook.state.started_at,
      completed_at: multiAgentHook.state.completed_at,
      estimated_remaining_time: multiAgentHook.state.estimated_remaining_time,
      active_agents: multiAgentHook.state.active_agents,
      completed_agents: multiAgentHook.state.completed_agents,
      failed_agents: multiAgentHook.state.failed_agents,
      context_status: multiAgentHook.state.context_status
    }
  }, [
    enableMultiAgentHook, 
    multiAgentHook.state, 
    localMultiAgentStatus
  ])
  
  // Update local state when external multi-agent status changes
  useEffect(() => {
    if (multiAgentStatus) {
      setLocalMultiAgentStatus(multiAgentStatus)
    }
  }, [multiAgentStatus])
  
  // Handle local multi-agent status updates
  useEffect(() => {
    const statusToUpdate = enhancedMultiAgentStatus || localMultiAgentStatus
    if (statusToUpdate) {
      onMultiAgentStatusUpdate?.(statusToUpdate)
    }
  }, [enhancedMultiAgentStatus, localMultiAgentStatus, onMultiAgentStatusUpdate])
  
  // WebSocket connection status for multi-agent mode
  const connectionStatus: ConnectionStatus | null = useMemo(() => {
    if (!isMultiAgentMode || !enableMultiAgentHook) return null
    
    // In a real implementation, this would come from the WebSocket hook
    // For now, we'll provide a mock status based on the hook state
    return {
      connected: !multiAgentHook.hasError && (multiAgentHook.isActive || multiAgentHook.isCompleted),
      connecting: multiAgentHook.state.is_starting || multiAgentHook.state.is_loading,
      quality: multiAgentHook.hasError ? 'poor' : 'good',
      error: multiAgentHook.getLastError() || undefined,
      serviceType: 'multi-agent',
      reconnectAttempts: 0 // This would come from the WebSocket hook
    }
  }, [isMultiAgentMode, enableMultiAgentHook, multiAgentHook])
  
  if (!activeSession) return null

  const getStatusIcon = () => {
    switch (activeSession.status) {
      case 'initializing':
        return <Clock className="h-5 w-5 text-yellow-500 animate-pulse" />
      case 'running':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />
      default:
        return <Clock className="h-5 w-5 text-muted-foreground" />
    }
  }
  
  const getModeIcon = () => {
    if (isMultiAgentMode) {
      return <Cpu className="h-4 w-4" />
    } else {
      return <User className="h-4 w-4" />
    }
  }
  
  const getModeLabel = () => {
    if (isMultiAgentMode) {
      return 'Multi-Agent'
    } else {
      return 'Single Agent'
    }
  }
  
  const getModeDescription = () => {
    if (isMultiAgentMode) {
      return 'Parallel agent orchestration for comprehensive analysis'
    } else {
      return 'Sequential Claude-based analysis'
    }
  }
  

  // Multi-Agent Mode - Show MultiAgentProgressTracker
  if (isMultiAgentMode) {
    return (
      <div className="space-y-6">
        {/* Mode Indicator Header */}
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getStatusIcon()}
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-medium">
                      Code Review in Progress
                    </h3>
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {getModeIcon()}
                      {getModeLabel()}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground font-normal">
                    {getModeDescription()}
                  </p>
                </div>
              </div>
              {/* Connection Status for Multi-Agent */}
              <div className="ml-auto">
                <ConnectionStatusIndicator 
                  connectionStatus={connectionStatus}
                  compact={true}
                  showDetails={false}
                />
              </div>
            </CardTitle>
          </CardHeader>
          
          {/* Repository Information */}
          {selectedPR && (
            <CardContent className="pt-0">
              <div className="text-sm space-y-1 bg-white/60 p-3 rounded-lg border border-white/40">
                <div><strong>Branch:</strong> {selectedPR.branch}</div>
                <div><strong>Repository:</strong> {selectedPR.repository}</div>
                {(progress?.worktree_path || activeSession.worktree_path) && (
                  <div><strong>Worktree:</strong> <code className="text-xs bg-muted px-1 py-0.5 rounded">{progress?.worktree_path || activeSession.worktree_path}</code></div>
                )}
              </div>
              
              {/* Error Display */}
              {activeSession.status === 'error' && activeSession.error && (
                <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
                  <strong>Error:</strong> {activeSession.error}
                </div>
              )}
            </CardContent>
          )}
        </Card>
        
        {/* Multi-Agent Progress Tracker */}
        <MultiAgentProgressTracker
          reviewStatus={enhancedMultiAgentStatus}
        />
        
        {/* Enhanced Multi-Agent State Display (Debug Mode) */}
        {enableMultiAgentHook && import.meta.env.NODE_ENV === 'development' && (
          <Card className="border-dashed border-muted-foreground/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-xs font-mono text-muted-foreground">
                Enhanced Multi-Agent State (Dev Mode)
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <div className="font-medium mb-1">Hook State:</div>
                  <div className="space-y-1 text-muted-foreground">
                    <div>Review ID: {multiAgentHook.state.review_id || 'None'}</div>
                    <div>Status: {multiAgentHook.state.status}</div>
                    <div>Loading: {multiAgentHook.state.is_loading ? 'Yes' : 'No'}</div>
                    <div>Starting: {multiAgentHook.state.is_starting ? 'Yes' : 'No'}</div>
                  </div>
                </div>
                <div>
                  <div className="font-medium mb-1">Agent Counts:</div>
                  <div className="space-y-1 text-muted-foreground">
                    <div>Active: {multiAgentHook.activeAgentCount}</div>
                    <div>Completed: {multiAgentHook.completedAgentCount}</div>
                    <div>Failed: {multiAgentHook.failedAgentCount}</div>
                    <div>Progress: {Math.round(multiAgentHook.overallProgress)}%</div>
                  </div>
                </div>
              </div>
              {multiAgentHook.hasError && (
                <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-xs">
                  <strong>Error:</strong> {multiAgentHook.getLastError()}
                </div>
              )}
            </CardContent>
          </Card>
        )}
        
        {/* Fallback Progress Events (if multi-agent status not available) */}
        {(!enhancedMultiAgentStatus && progressEvents.length > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Progress Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <ClaudeProgressTimeline
                events={progressEvents}
                currentStep={currentProgressStep}
              />
            </CardContent>
          </Card>
        )}
      </div>
    )
  }
  
  // Legacy Mode - Show traditional progress display
  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <div className="flex items-center gap-2">
                <h3 className="text-lg font-medium">
                  Review in Progress - {mode.charAt(0).toUpperCase() + mode.slice(1)} Mode
                </h3>
                <Badge variant="outline" className="flex items-center gap-1">
                  {getModeIcon()}
                  {getModeLabel()}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground font-normal">
                {progress?.message || activeSession.progress_message || 'Processing review...'}
              </p>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{Math.round(activeSession.progress || progress?.progress || 0)}%</span>
          </div>
          <Progress value={activeSession.progress || progress?.progress || 0} className="h-2" />
        </div>
        
        {selectedPR && (
          <div className="text-sm space-y-1">
            <div><strong>Branch:</strong> {selectedPR.branch}</div>
            <div><strong>Repository:</strong> {selectedPR.repository}</div>
            {(progress?.worktree_path || activeSession.worktree_path) && (
              <div><strong>Worktree:</strong> <code className="text-xs bg-muted px-1 py-0.5 rounded">{progress?.worktree_path || activeSession.worktree_path}</code></div>
            )}
          </div>
        )}

        {activeSession.status === 'error' && activeSession.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
            <strong>Error:</strong> {activeSession.error}
          </div>
        )}

        {/* Claude Progress Timeline */}
        <ClaudeProgressTimeline
          events={progressEvents}
          currentStep={currentProgressStep}
          className="mt-4"
        />
      </CardContent>
    </Card>
  )
}