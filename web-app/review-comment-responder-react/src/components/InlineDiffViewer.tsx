import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { 
  MessageSquare, 
  Plus, 
  ChevronDown, 
  ChevronRight, 
  FileText,
  GitCommit,
  Loader2,
  AlertCircle,
  <PERSON>ly,
  Bo<PERSON>,
  File,
  Folder,
  FolderO<PERSON>,
  Minus,
  Filter,
  Check,
  Clock,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { cn } from '../lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from './ui/dropdown-menu'
import { usePullRequestComments } from '../services/bitbucket/PullRequestCommentsService'
import { claudeIntegrationService } from '../services/claude/ClaudeIntegrationService'
import { useCommentStore } from '../store/useCommentStore'
import { ClaudeResponseModal } from './ClaudeResponseModal'
import type { 
  BitbucketPullRequest, 
  BitbucketRepository,
  DiffHunk, 
  DiffLine, 
  CommentWithPosition,
  BitbucketPRComment
} from '../types/bitbucket.types'

interface InlineDiffViewerProps {
  pullRequest: BitbucketPullRequest | null
  repository: BitbucketRepository | null
  className?: string
}

interface FileViewState {
  isExpanded: boolean
  showComments: boolean
}

export const InlineDiffViewer: React.FC<InlineDiffViewerProps> = ({
  pullRequest,
  repository,
  className
}) => {
  const { getCommentsWithDiff, createComment, replyToComment } = usePullRequestComments()
  const { prContext } = useCommentStore()
  
  const [diffData, setDiffData] = useState<{
    parsedDiff: DiffHunk[]
    commentsByFile: Record<string, CommentWithPosition[]>
    allComments: BitbucketPRComment[]
  } | null>(null)
  
  const [fileStates, setFileStates] = useState<Record<string, FileViewState>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeCommentForm, setActiveCommentForm] = useState<{
    filePath: string
    lineNumber: number
  } | null>(null)
  const [newCommentText, setNewCommentText] = useState('')
  const [replyForms, setReplyForms] = useState<Record<number, string>>({})
  const [claudeResponses, setClaudeResponses] = useState<Record<number, any>>({})
  const [generatingResponse, setGeneratingResponse] = useState<Record<number, boolean>>({})
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [commentFilter, setCommentFilter] = useState<'all' | 'resolved' | 'unresolved'>('all')
  const [selectedClaudeResponse, setSelectedClaudeResponse] = useState<any | null>(null)
  const [isClaudeModalOpen, setIsClaudeModalOpen] = useState(false)
  const [claudeResponseStatus, setClaudeResponseStatus] = useState<Record<number, 'generating' | 'completed' | 'error'>>({})
  const [collapsedResolvedComments, setCollapsedResolvedComments] = useState<Set<number>>(new Set())
  const [acceptedSuggestions, setAcceptedSuggestions] = useState<Set<string>>(new Set()) // Track accepted suggestions by change ID

  // Load diff and comments when PR or repository changes
  useEffect(() => {
    if (pullRequest && repository) {
      loadDiffWithComments()
    } else {
      setDiffData(null)
      setFileStates({})
    }
  }, [pullRequest, repository])

  // Auto-collapse resolved comments when diffData changes
  useEffect(() => {
    if (diffData?.allComments) {
      const resolvedCommentIds = new Set<number>()
      
      diffData.allComments.forEach(comment => {
        if (!comment.parent && isCommentResolved(comment)) {
          resolvedCommentIds.add(comment.id)
        }
      })
      
      setCollapsedResolvedComments(resolvedCommentIds)
    }
  }, [diffData])

  // Memoized file grouping to prevent expensive recalculations
  const fileGroups = useMemo(() => {
    if (!diffData?.parsedDiff?.length) return {}
    
    return diffData.parsedDiff.reduce((acc, hunk) => {
      if (!acc[hunk.filePath]) {
        acc[hunk.filePath] = []
      }
      acc[hunk.filePath].push(hunk)
      return acc
    }, {} as Record<string, DiffHunk[]>)
  }, [diffData?.parsedDiff])

  // Memoized folder paths calculation
  const folderPaths = useMemo(() => {
    const paths = new Set<string>()
    const filePaths = Object.keys(fileGroups)
    
    filePaths.forEach(filePath => {
      const parts = filePath.split('/')
      let currentPath = ''
      for (let i = 0; i < parts.length - 1; i++) {
        currentPath = currentPath ? `${currentPath}/${parts[i]}` : parts[i]
        paths.add(currentPath)
      }
    })
    
    return paths
  }, [fileGroups])

  // Optimized effect - only runs when folderPaths actually change
  useEffect(() => {
    if (folderPaths.size > 0) {
      // Use setTimeout to defer state updates and prevent blocking
      setTimeout(() => {
        setExpandedFolders(folderPaths)
        
        // Select the first file and expand it
        const filePaths = Object.keys(fileGroups)
        if (filePaths.length > 0 && !selectedFile) {
          const firstFile = filePaths[0]
          setSelectedFile(firstFile)
          // Expand the selected file
          setFileStates(prev => ({
            ...prev,
            [firstFile]: {
              ...prev[firstFile],
              isExpanded: true
            }
          }))
        }
      }, 0)
    }
  }, [folderPaths, fileGroups, selectedFile])

  const loadDiffWithComments = useCallback(async () => {
    if (!pullRequest || !repository) return

    try {
      setIsLoading(true)
      setError(null)

      const data = await getCommentsWithDiff(
        repository.workspace.slug,
        repository.name,
        pullRequest.id
      )

      // Debug: Log first few comments to see their structure
      console.log('Sample comments:', data.commentsData.allComments.slice(0, 5).map((c: BitbucketPRComment) => ({
        id: c.id,
        deleted: c.deleted,
        pending: c.pending,
        content: c.content.raw.substring(0, 50) + '...',
        // Log any additional properties
        ...Object.keys(c).reduce((acc, key) => {
          if (!['id', 'deleted', 'pending', 'content', 'user', 'created_on', 'updated_on', 'type', 'parent', 'inline', 'links'].includes(key)) {
            acc[key] = (c as any)[key];
          }
          return acc;
        }, {} as any)
      })));
      
      console.log('Diff and comments loaded:', {
        diffHunks: data.parsedDiff.length,
        totalComments: data.commentsData.allComments.length,
        commentsByFile: Object.keys(data.commentsData.commentsByFile).length,
        resolved: data.commentsData.allComments.filter((c: BitbucketPRComment) => isCommentResolved(c)).length,
        unresolved: data.commentsData.allComments.filter((c: BitbucketPRComment) => !isCommentResolved(c)).length,
        deletedComments: data.commentsData.allComments.filter((c: BitbucketPRComment) => c.deleted === true).length,
        pendingComments: data.commentsData.allComments.filter((c: BitbucketPRComment) => c.pending === true).length
      })

      setDiffData({
        parsedDiff: data.parsedDiff,
        commentsByFile: data.commentsData.commentsByFile,
        allComments: data.commentsData.allComments
      })

      // Initialize file states
      const newFileStates: Record<string, FileViewState> = {}
      data.parsedDiff.forEach(hunk => {
        if (!newFileStates[hunk.filePath]) {
          newFileStates[hunk.filePath] = {
            isExpanded: false, // Only expand when selected
            showComments: true
          }
        }
      })
      setFileStates(newFileStates)

    } catch (error) {
      console.error('Failed to load diff with comments:', error)
      setError(error instanceof Error ? error.message : 'Failed to load diff and comments')
    } finally {
      setIsLoading(false)
    }
  }, [pullRequest, repository, getCommentsWithDiff])

  const toggleFileExpansion = (filePath: string) => {
    setFileStates(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        isExpanded: !prev[filePath]?.isExpanded
      }
    }))
  }

  const toggleFolder = (folderPath: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(folderPath)) {
        newSet.delete(folderPath)
      } else {
        newSet.add(folderPath)
      }
      return newSet
    })
  }

  const scrollToFile = (filePath: string) => {
    setSelectedFile(filePath)
    // Collapse all files and expand only the selected one
    setFileStates(prev => {
      const newStates = { ...prev }
      // Collapse all files
      Object.keys(newStates).forEach(key => {
        newStates[key] = { ...newStates[key], isExpanded: false }
      })
      // Expand only the selected file
      newStates[filePath] = { ...newStates[filePath], isExpanded: true }
      return newStates
    })
    // Then scroll to it
    setTimeout(() => {
      const element = document.getElementById(`file-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }

  const buildFileTree = (filePaths: string[]) => {
    const tree: Record<string, any> = {}
    
    filePaths.forEach(filePath => {
      const parts = filePath.split('/')
      let current = tree
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i]
        const isFile = i === parts.length - 1
        
        if (!current[part]) {
          current[part] = isFile 
            ? { type: 'file', path: filePath }
            : { type: 'folder', children: {} }
        }
        
        if (!isFile) {
          current = current[part].children
        }
      }
    })
    
    return tree
  }

  const getFileStats = (_filePath: string, hunks: DiffHunk[]) => {
    let additions = 0
    let deletions = 0
    
    hunks.forEach(hunk => {
      hunk.lines.forEach(line => {
        if (line.type === 'addition') additions++
        if (line.type === 'deletion') deletions++
      })
    })
    
    return { additions, deletions }
  }

  const renderFileTreeNode = (node: any, name: string, path: string, level: number = 0): React.JSX.Element => {
    if (node.type === 'file') {
      const isSelected = selectedFile === node.path
      
      const stats = getFileStats(node.path, fileGroups[node.path] || [])
      // Only count root comments for file statistics
      const allFileComments = diffData?.allComments.filter(c => c.inline?.path === node.path && !c.parent) || []
      const unresolvedCount = allFileComments.filter(c => !isCommentResolved(c)).length
      const resolvedCount = allFileComments.filter(c => isCommentResolved(c)).length
      
      return (
        <div
          key={path}
          className={cn(
            'flex items-center justify-between px-2 py-1.5 text-sm cursor-pointer hover:bg-muted/50 transition-colors rounded-md mx-1',
            isSelected && 'bg-primary/10 border-l-2 border-primary'
          )}
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => scrollToFile(node.path)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <File className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="font-mono text-xs truncate">{name}</span>
            <div className="flex items-center gap-1">
              {unresolvedCount > 0 && (
                <Badge variant="default" className="h-5 px-1.5 text-xs bg-orange-500 hover:bg-orange-600">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  {unresolvedCount}
                </Badge>
              )}
              {resolvedCount > 0 && (
                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                  <Check className="h-3 w-3 mr-1" />
                  {resolvedCount}
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2 text-xs">
            {stats.additions > 0 && (
              <span className="text-green-500 font-medium">+{stats.additions}</span>
            )}
            {stats.deletions > 0 && (
              <span className="text-red-500 font-medium">-{stats.deletions}</span>
            )}
          </div>
        </div>
      )
    }

    const isExpanded = expandedFolders.has(path)
    
    return (
      <div key={path}>
        <div
          className="flex items-center gap-1.5 px-2 py-1.5 text-sm cursor-pointer hover:bg-muted/30 transition-colors rounded-md mx-1"
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => toggleFolder(path)}
        >
          {isExpanded ? (
            <ChevronDown className="h-3.5 w-3.5 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
          )}
          {isExpanded ? (
            <FolderOpen className="h-4 w-4 text-primary" />
          ) : (
            <Folder className="h-4 w-4 text-muted-foreground" />
          )}
          <span className="font-medium">{name}</span>
        </div>
        {isExpanded && (
          <div>
            {Object.entries(node.children).map(([childName, childNode]) =>
              renderFileTreeNode(childNode, childName, `${path}/${childName}`, level + 1)
            )}
          </div>
        )}
      </div>
    )
  }

  const getCommentsForLine = (filePath: string, lineNumber: number): CommentWithPosition[] => {
    if (!diffData) return []
    const fileComments = diffData.commentsByFile[filePath] || []
    // Only return root comments (not replies) to avoid duplication
    const lineComments = fileComments.filter(comment => 
      comment.position.line === lineNumber && !comment.parent
    )
    
    if (lineComments.length > 0) {
      console.log(`Found ${lineComments.length} root comments for ${filePath}:${lineNumber}`)
    }
    
    return lineComments
  }

  const jumpToComment = (comment: CommentWithPosition | BitbucketPRComment) => {
    // Handle both types of comments
    const filePath = 'inline' in comment && comment.inline ? comment.inline.path : 
                    'position' in comment ? comment.position.path : null
    const lineNumber = 'inline' in comment && comment.inline ? comment.inline.to : 
                      'position' in comment ? comment.position.line : null
    
    if (!filePath || !lineNumber) return
    
    // First, select and expand the file
    setSelectedFile(filePath)
    setFileStates(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        isExpanded: true
      }
    }))
    
    // Then scroll to the specific line after a short delay
    setTimeout(() => {
      const fileElement = document.getElementById(`file-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`)
      if (fileElement) {
        fileElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
        
        // Try to scroll to the specific line
        setTimeout(() => {
          const lineElement = document.getElementById(`line-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}-${lineNumber}`)
          if (lineElement) {
            lineElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
            // Add a highlight effect
            lineElement.classList.add('bg-yellow-200/50', 'transition-colors')
            setTimeout(() => {
              lineElement.classList.remove('bg-yellow-200/50')
            }, 2000)
          }
        }, 300)
      }
    }, 100)
  }

  const isCommentResolved = (comment: BitbucketPRComment): boolean => {
    // In Bitbucket Cloud, comments can be resolved in different ways:
    // 1. The entire thread is marked as resolved (parent comment has a property)
    // 2. Individual comments might be deleted
    // 3. Check for any resolution-related properties
    
    // First check if it's deleted
    if (comment.deleted === true) {
      return true;
    }
    
    // Check for various possible resolution indicators
    const anyComment = comment as any;
    
    // Check common resolution properties
    if (anyComment.resolved === true || 
        anyComment.is_resolved === true ||
        anyComment.resolution !== undefined ||
        anyComment.resolved_on !== undefined ||
        anyComment.resolved_by !== undefined) {
      return true;
    }
    
    // Check if there's a reply that marks this comment as resolved
    if (diffData?.allComments) {
      const replies = diffData.allComments.filter(c => c.parent?.id === comment.id)
      const hasResolveReply = replies.some(reply => 
        reply.content.raw.includes('[RESOLVED]') || 
        reply.content.raw.includes('✅')
      )
      if (hasResolveReply) {
        return true
      }
    }
    
    // Default to unresolved
    return false;
  }

  const getFilteredComments = () => {
    if (!diffData) return []
    
    // Only consider root comments (not replies) for filtering
    let filtered = diffData.allComments.filter(comment => !comment.parent)
    
    if (commentFilter === 'resolved') {
      filtered = filtered.filter(comment => isCommentResolved(comment))
    } else if (commentFilter === 'unresolved') {
      filtered = filtered.filter(comment => !isCommentResolved(comment))
    }
    
    return filtered
  }

  const getCommentStats = () => {
    if (!diffData) return { total: 0, resolved: 0, unresolved: 0 }
    
    // Only count root comments (not replies) for statistics
    const rootComments = diffData.allComments.filter(c => !c.parent)
    const total = rootComments.length
    const resolved = rootComments.filter(c => isCommentResolved(c)).length
    const unresolved = total - resolved
    
    // Also provide detailed stats for debugging
    console.log('Comment stats:', {
      totalAllComments: diffData.allComments.length,
      rootComments: rootComments.length,
      replies: diffData.allComments.filter(c => c.parent).length,
      resolved,
      unresolved
    })
    
    return { total, resolved, unresolved }
  }

  const handleAddComment = (filePath: string, lineNumber: number) => {
    setActiveCommentForm({ filePath, lineNumber })
    setNewCommentText('')
  }

  const handleSubmitComment = async () => {
    if (!activeCommentForm || !newCommentText.trim() || !pullRequest || !repository) return

    try {
      await createComment(
        repository.workspace.slug,
        repository.name,
        pullRequest.id,
        newCommentText,
        {
          inline: {
            path: activeCommentForm.filePath,
            line: activeCommentForm.lineNumber
          }
        }
      )

      // Refresh data to show new comment
      await loadDiffWithComments()
      
      setActiveCommentForm(null)
      setNewCommentText('')
    } catch (error) {
      console.error('Failed to create comment:', error)
    }
  }

  const handleReplyToComment = async (commentId: number) => {
    const replyText = replyForms[commentId]
    if (!replyText?.trim() || !pullRequest || !repository) return

    try {
      await replyToComment(
        repository.workspace.slug,
        repository.name,
        pullRequest.id,
        commentId,
        replyText
      )

      // Refresh data to show new reply
      await loadDiffWithComments()
      
      setReplyForms(prev => ({ ...prev, [commentId]: '' }))
    } catch (error) {
      console.error('Failed to reply to comment:', error)
    }
  }

  const handleGenerateClaudeResponse = async (comment: BitbucketPRComment) => {
    if (!pullRequest || !repository) return

    try {
      setGeneratingResponse(prev => ({ ...prev, [comment.id]: true }))
      setClaudeResponseStatus(prev => ({ ...prev, [comment.id]: 'generating' }))

      // Find the file path for this comment
      const filePath = comment.inline?.path || 'Unknown file'
      const lineNumber = comment.inline?.to || 0

      const response = await claudeIntegrationService.generateResponse({
        comment: comment.content.raw,
        file: filePath,
        line: lineNumber,
        pullRequestContext: {
          workspace: repository.workspace.slug,
          repository: repository.name,
          prId: pullRequest.id,
          branchName: pullRequest.source.branch.name,
          title: pullRequest.title,
          worktreePath: prContext.worktreePath
        }
      })

      if (response.success) {
        // Store the full response data for the modal INCLUDING session_id
        setClaudeResponses(prev => ({
          ...prev,
          [comment.id]: {
            ...response.response,
            session_id: response.session_id  // Include session_id with the response data
          }
        }))
        setClaudeResponseStatus(prev => ({ ...prev, [comment.id]: 'completed' }))
      } else {
        console.error('Claude response failed:', response.error)
        setClaudeResponseStatus(prev => ({ ...prev, [comment.id]: 'error' }))
      }
    } catch (error) {
      console.error('Failed to generate Claude response:', error)
      setClaudeResponseStatus(prev => ({ ...prev, [comment.id]: 'error' }))
    } finally {
      setGeneratingResponse(prev => ({ ...prev, [comment.id]: false }))
    }
  }

  const handleOpenClaudeResponse = (comment: BitbucketPRComment) => {
    const responseData = claudeResponses[comment.id]
    if (responseData) {
      setSelectedClaudeResponse(responseData)
      setIsClaudeModalOpen(true)
    }
  }

  const handleAcceptChange = async (fileChange: any) => {
    try {
      console.log('Accepting change:', fileChange)
      
      // Mark this suggestion as accepted immediately for UI feedback
      const changeId = `${fileChange.file}-${fileChange.fileIndex}`
      setAcceptedSuggestions(prev => new Set([...prev, changeId]))
      
      console.log('✅ Change marked as accepted in UI (instant feedback)')
      
      // Fire-and-forget: Execute the change in background without blocking UI
      if (selectedClaudeResponse?.session_id) {
        // Don't await - let it run in background
        executeChangeInBackground(fileChange, changeId, selectedClaudeResponse.session_id)
      } else {
        console.log('⚠️ No session ID available for executing changes')
      }
    } catch (error) {
      console.error('❌ Failed to accept change:', error)
      // Remove from accepted if there was an error
      const changeId = `${fileChange.file}-${fileChange.fileIndex}`
      setAcceptedSuggestions(prev => {
        const newSet = new Set(prev)
        newSet.delete(changeId)
        return newSet
      })
    }
  }

  // Background execution with timeout and better error handling
  const executeChangeInBackground = async (fileChange: any, changeId: string, sessionId: string) => {
    try {
      console.log('🚀 Starting background execution for change:', changeId)
      
      // Create AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
        console.warn('⏰ API call timed out after 5 seconds for change:', changeId)
      }, 5000) // 5 second timeout instead of waiting 10 seconds
      
      const response = await fetch(`http://localhost:5001/api/session/${sessionId}/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          approved_changes: [fileChange]
        }),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (response.ok) {
        console.log('✅ Change applied successfully in background:', changeId)
        // Optional: Could show a toast notification here
      } else {
        console.error('❌ Backend failed to apply change:', changeId, response.status)
        // Optionally remove from UI if backend failed
        // setAcceptedSuggestions(prev => {
        //   const newSet = new Set(prev)
        //   newSet.delete(changeId)
        //   return newSet
        // })
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.warn('⏰ Change execution timed out:', changeId)
        // Keep the UI change even if backend timed out
      } else {
        console.error('❌ Failed to execute change in background:', changeId, error)
        // Optionally revert UI state on critical errors
      }
    }
  }

  const handleRejectChange = (fileChange: any) => {
    console.log('Rejecting change:', fileChange)
    
    // Remove from accepted suggestions if it was previously accepted
    const changeId = `${fileChange.file}-${fileChange.fileIndex}`
    setAcceptedSuggestions(prev => {
      const newSet = new Set(prev)
      newSet.delete(changeId)
      return newSet
    })
    
    // For reject, we just don't apply the change - no backend call needed
    console.log('✅ Change rejected:', changeId)
  }

  const handleUpdateInstructions = async (instructions: string, fileIndex?: number, changeIndex?: number) => {
    try {
      console.log('Updating instructions:', instructions, 'for file:', fileIndex, 'change:', changeIndex)
      
      if (selectedClaudeResponse?.session_id) {
        // Use session-based modification
        const response = await fetch('http://localhost:5001/api/session/' + selectedClaudeResponse.session_id + '/modify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            modification_prompt: instructions
          })
        })
        
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            // Update the modal with new response
            setSelectedClaudeResponse(result.response)
            console.log('✅ Instructions updated successfully')
          }
        }
      } else {
        console.log('⚠️ No session ID available for updating instructions')
      }
    } catch (error) {
      console.error('❌ Failed to update instructions:', error)
    }
  }

  const handleGlobalUpdate = async (instructions: string) => {
    try {
      console.log('Global update with instructions:', instructions)
      
      if (selectedClaudeResponse?.session_id) {
        // Use session-based modification for global updates
        const response = await fetch('http://localhost:5001/api/session/' + selectedClaudeResponse.session_id + '/modify', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            modification_prompt: `Global update for all suggested changes: ${instructions}`
          })
        })
        
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            // Update the modal with new response
            setSelectedClaudeResponse(result.response)
            console.log('✅ Global update applied successfully')
          }
        }
      } else {
        console.log('⚠️ No session ID available for global update')
      }
    } catch (error) {
      console.error('❌ Failed to apply global update:', error)
    }
  }

  const handleResolveComment = async (comment: BitbucketPRComment) => {
    if (!pullRequest || !repository) return

    try {
      // Instead of updating the original comment (which requires author permission),
      // we'll add a reply indicating that the comment is resolved
      const resolveText = "✅ **[RESOLVED]** - This comment has been marked as resolved."
      
      await replyToComment(
        repository.workspace.slug,
        repository.name,
        pullRequest.id,
        comment.id,
        resolveText
      )
      
      // Reload the diff to refresh comments
      await loadDiffWithComments()
      
      console.log('✅ Comment marked as resolved')
    } catch (error) {
      console.error('❌ Failed to resolve comment:', error)
    }
  }

  const toggleResolvedComment = (commentId: number) => {
    setCollapsedResolvedComments(prev => {
      const newSet = new Set(prev)
      if (newSet.has(commentId)) {
        newSet.delete(commentId)
      } else {
        newSet.add(commentId)
      }
      return newSet
    })
  }

  const renderCommentThread = (comment: BitbucketPRComment, isReply: boolean = false) => {
    const replies = diffData?.allComments.filter(c => c.parent?.id === comment.id) || []
    const isResolved = isCommentResolved(comment)
    const isCollapsed = isResolved && !isReply && collapsedResolvedComments.has(comment.id)
    
    return (
      <div key={comment.id} className={cn(
        'border rounded-lg bg-card/50 backdrop-blur-sm transition-all duration-200 p-3',
        isReply && 'ml-6 mt-2 border-l-2 border-l-primary/30'
      )}>
        {/* Resolved comment toggle button */}
        {isResolved && !isReply && (
          <div className="p-3 pb-0">
            <button
              onClick={() => toggleResolvedComment(comment.id)}
              className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300 hover:text-green-800 dark:hover:text-green-200 transition-colors"
            >
              <ChevronRight 
                className={cn(
                  'h-4 w-4 transition-transform duration-200',
                  !isCollapsed && 'rotate-90'
                )}
              />
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">
                {isCollapsed ? 'Show resolved comment' : 'Hide resolved comment'}
              </span>
            </button>
          </div>
        )}

        {/* Comment content - hidden when collapsed */}
        {!isCollapsed && (
          <div className="p-3">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center shrink-0">
              <span className="text-xs font-medium text-primary">
                {comment.user.display_name.charAt(0).toUpperCase()}
              </span>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium text-sm">{comment.user.display_name}</span>
                <span className="text-xs text-muted-foreground">
                  {new Date(comment.created_on).toLocaleDateString()}
                </span>
                {comment.deleted && (
                  <Badge variant="outline" className="text-xs">Deleted</Badge>
                )}
                {isResolved && !isReply && (
                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    ✅ Resolved
                  </Badge>
                )}
              </div>
              
              <div className="text-sm text-foreground prose prose-sm max-w-none">
                {comment.content.raw}
              </div>

              {/* Reply form */}
              {!isReply && !comment.deleted && (
                <div className="mt-3">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setReplyForms(prev => ({ 
                        ...prev, 
                        [comment.id]: prev[comment.id] || '' 
                      }))}
                      className="text-xs"
                    >
                      <Reply className="h-3 w-3 mr-1" />
                      Reply
                    </Button>
                    
                    

                    {!isResolved && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleResolveComment(comment)}
                        className="text-xs text-emerald-600"
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Resolve
                      </Button>
                    )}

                    {claudeResponseStatus[comment.id] === 'completed' ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleOpenClaudeResponse(comment)}
                        className="text-xs text-green-600"
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Open Response
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleGenerateClaudeResponse(comment)}
                        disabled={generatingResponse[comment.id]}
                        className="text-xs text-primary"
                      >
                        {generatingResponse[comment.id] ? (
                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        ) : (
                          <Bot className="h-3 w-3 mr-1" />
                        )}
                        {generatingResponse[comment.id] ? 'Generiere...' : 'Generate Claude Response'}
                      </Button>
                    )}
                  </div>

                  {replyForms[comment.id] !== undefined && (
                    <div className="mt-2 space-y-2">
                      <textarea
                        value={replyForms[comment.id]}
                        onChange={(e) => setReplyForms(prev => ({ 
                          ...prev, 
                          [comment.id]: e.target.value 
                        }))}
                        placeholder="Write a reply..."
                        className="w-full min-h-20 p-3 rounded-lg border border-border bg-background text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleReplyToComment(comment.id)}
                          disabled={!replyForms[comment.id]?.trim()}
                        >
                          Reply
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setReplyForms(prev => ({ 
                            ...prev, 
                            [comment.id]: '' 
                          }))}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}

                </div>
              )}
            </div>
          </div>
        </div>
        )}

        {/* Render replies - only show if comment is expanded */}
        {!isCollapsed && replies.map(reply => renderCommentThread(reply, true))}
      </div>
    )
  }

  const renderDiffLine = (line: DiffLine, lineIndex: number, hunk: DiffHunk) => {
    const lineNumber = line.newLineNumber || line.oldLineNumber || 0
    const comments = getCommentsForLine(hunk.filePath, lineNumber)
    const hasComments = comments.length > 0
    const isActiveCommentForm = activeCommentForm?.filePath === hunk.filePath && 
                               activeCommentForm?.lineNumber === lineNumber

    return (
      <React.Fragment key={`${hunk.filePath}-${lineIndex}`}>
        {/* Diff line */}
        <div
          id={`line-${hunk.filePath.replace(/[^a-zA-Z0-9]/g, '-')}-${lineNumber}`}
          className={cn(
            'flex items-center hover:bg-muted/30 group transition-colors',
            line.type === 'addition' && 'bg-green-500/10 border-l-2 border-l-green-500',
            line.type === 'deletion' && 'bg-red-500/10 border-l-2 border-l-red-500',
            line.type === 'context' && 'bg-background'
          )}
        >
          {/* Line numbers */}
          <div className="flex shrink-0 text-xs text-muted-foreground font-mono">
            <span className="w-12 text-right px-2 py-1 border-r border-border">
              {line.oldLineNumber || ''}
            </span>
            <span className="w-12 text-right px-2 py-1 border-r border-border">
              {line.newLineNumber || ''}
            </span>
          </div>

          {/* Add comment button */}
          <div className="w-8 flex items-center justify-center shrink-0">
            {line.newLineNumber && (
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  'w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity',
                  hasComments && 'opacity-100 bg-primary/10 text-primary'
                )}
                onClick={() => handleAddComment(hunk.filePath, line.newLineNumber!)}
              >
                {hasComments ? (
                  <MessageSquare className="h-3 w-3" />
                ) : (
                  <Plus className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>

          {/* Line content */}
          <div className="flex-1 px-3 py-1 font-mono text-sm">
            <span className={cn(
              line.type === 'addition' && 'text-green-600 dark:text-green-400',
              line.type === 'deletion' && 'text-red-600 dark:text-red-400'
            )}>
              {line.type === 'addition' && '+'}
              {line.type === 'deletion' && '-'}
              {line.type === 'context' && ' '}
            </span>
            {line.content}
          </div>
        </div>

        {/* Comments for this line */}
        {hasComments && (
          <div className="bg-muted/20 border-l-2 border-l-primary/30 pl-8 pr-4 py-3">
            <div className="space-y-3">
              {comments.map(comment => renderCommentThread(comment))}
            </div>
          </div>
        )}

        {/* Active comment form */}
        {isActiveCommentForm && (
          <div className="bg-primary/5 border-l-2 border-l-primary pl-8 pr-4 py-3">
            <div className="space-y-3">
              <textarea
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                placeholder="Add a comment..."
                className="w-full min-h-20 p-3 rounded-lg border border-border bg-background text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleSubmitComment}
                  disabled={!newCommentText.trim()}
                >
                  Add Comment
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveCommentForm(null)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </React.Fragment>
    )
  }

  const renderFileHunk = (hunk: DiffHunk, hunkIndex: number) => {
    const fileState = fileStates[hunk.filePath]
    if (!fileState?.isExpanded) return null

    return (
      <div key={`${hunk.filePath}-hunk-${hunkIndex}-${hunk.oldStart}-${hunk.newStart}`} className="border rounded-lg overflow-hidden">
        <div className="bg-muted/30 px-4 py-2 text-sm font-mono text-muted-foreground border-b">
          @@ -{hunk.oldStart},{hunk.oldLines} +{hunk.newStart},{hunk.newLines} @@ {hunk.header}
        </div>
        
        <div className="overflow-x-auto">
          {hunk.lines.map((line, lineIndex) => renderDiffLine(line, lineIndex, hunk))}
        </div>
      </div>
    )
  }

  if (!pullRequest) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Select Pull Request
          </h3>
          <p className="text-muted-foreground">
            Choose a pull request to view its diff and comments.
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading diff and comments...</p>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Error</h3>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={loadDiffWithComments} variant="outline">
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!diffData || diffData.parsedDiff.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <GitCommit className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No Changes</h3>
          <p className="text-muted-foreground">
            This pull request doesn't have any file changes.
          </p>
        </CardContent>
      </Card>
    )
  }

  // fileGroups is now available from useMemo above

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Pull Request Diff & Comments
            <Badge variant="outline" className="ml-2">
              {diffData.allComments.length} comments
            </Badge>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={loadDiffWithComments}
            className="flex items-center gap-2"
          >
            <Loader2 className="h-4 w-4" />
            Refresh
          </Button>
        </CardTitle>
        
        <div className="text-sm text-muted-foreground">
          <p>PR #{pullRequest.id}: {pullRequest.title}</p>
          <p>{Object.keys(fileGroups).length} files changed</p>
        </div>

        {/* Accepted Changes Summary */}
        {acceptedSuggestions.size > 0 && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Check className="h-4 w-4 text-green-600" />
              <span className="font-medium text-green-800 dark:text-green-200">
                Accepted Changes ({acceptedSuggestions.size})
              </span>
            </div>
            <div className="text-xs text-green-700 dark:text-green-300 space-y-1">
              {Array.from(acceptedSuggestions).map(changeId => {
                const [filePath, fileIndex] = changeId.split('-')
                return (
                  <div key={changeId} className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="font-mono">{filePath}</span>
                    <span className="text-green-600">change #{fileIndex}</span>
                  </div>
                )
              })}
            </div>
          </div>
        )}
        
        {/* Comment Filter and Dropdown */}
        <div className="flex items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={commentFilter} onValueChange={(value: 'all' | 'resolved' | 'unresolved') => setCommentFilter(value)}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  All Comments ({getCommentStats().total})
                </SelectItem>
                <SelectItem value="unresolved">
                  <span className="flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    Unresolved ({getCommentStats().unresolved})
                  </span>
                </SelectItem>
                <SelectItem value="resolved">
                  <span className="flex items-center gap-2">
                    <Check className="h-3 w-3" />
                    Resolved ({getCommentStats().resolved})
                  </span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {getFilteredComments().length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Jump to Comment
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80 max-h-96 overflow-y-auto">
                <DropdownMenuLabel>Comments (Root threads only)</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {getFilteredComments().map((comment) => {
                  const filePath = comment.inline?.path || 'General'
                  const lineNumber = comment.inline?.to
                  return (
                    <DropdownMenuItem
                      key={comment.id}
                      onClick={() => jumpToComment(comment)}
                      className="flex flex-col items-start p-3 cursor-pointer"
                    >
                      <div className="flex items-center gap-2 w-full">
                        <span className="font-mono text-xs text-muted-foreground truncate">
                          {filePath}{lineNumber ? `:${lineNumber}` : ''}
                        </span>
                        {isCommentResolved(comment) && (
                          <Badge variant="secondary" className="h-5 text-xs">
                            Resolved
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm mt-1 line-clamp-2 text-muted-foreground">
                        {comment.content.raw}
                      </p>
                      <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                        <span>{comment.user.display_name}</span>
                        <span>•</span>
                        <span>{new Date(comment.created_on).toLocaleDateString()}</span>
                      </div>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="flex border-t border-border" style={{ height: '700px' }}>
          {/* File Tree Sidebar */}
          <div className="w-80 border-r border-border overflow-y-auto bg-muted/5">
            <div className="bg-muted/20 p-3 border-b border-border font-medium sticky top-0 z-10">
              <span className="flex items-center gap-2 text-sm">
                <Folder className="h-4 w-4 text-primary" />
                Files changed ({Object.keys(fileGroups).length})
              </span>
            </div>
            
            {/* Comment Summary */}
            {diffData.allComments.length > 0 && (
              <div className="px-3 py-2 border-b border-border bg-muted/10">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="font-medium">Comments</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getCommentStats().unresolved > 0 && (
                      <Badge variant="default" className="h-5 text-xs bg-orange-500">
                        {getCommentStats().unresolved} Open
                      </Badge>
                    )}
                    {getCommentStats().resolved > 0 && (
                      <Badge variant="secondary" className="h-5 text-xs">
                        {getCommentStats().resolved} Resolved
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            <div className="py-2">
              {(() => {
                const fileTree = buildFileTree(Object.keys(fileGroups))
                return Object.entries(fileTree).map(([name, node]) =>
                  renderFileTreeNode(node, name, name, 0)
                )
              })()}
            </div>
          </div>

          {/* Diff Content Area */}
          <div className="flex-1 overflow-y-auto">
            {selectedFile && fileGroups[selectedFile] ? (
              <div className="space-y-6">
                {(() => {
                  const filePath = selectedFile
                  const hunks = fileGroups[selectedFile]
                  const fileState = fileStates[filePath]
                  const fileComments = diffData.commentsByFile[filePath] || []
                  const fileStats = getFileStats(filePath, hunks)
                  
                  return (
                    <div 
                      key={filePath} 
                      id={`file-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`}
                      className="border-b border-border last:border-b-0"
                    >
                      {/* File header */}
                      <div
                        className="bg-muted/20 px-4 py-3 flex items-center justify-between cursor-pointer hover:bg-muted/30 transition-colors sticky top-0 z-10 border-b border-border"
                        onClick={() => toggleFileExpansion(filePath)}
                      >
                        <div className="flex items-center gap-3">
                          {fileState?.isExpanded ? (
                            <ChevronDown className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          )}
                          
                          <File className="h-4 w-4 text-muted-foreground" />
                          <span className="font-mono text-sm font-medium">{filePath}</span>
                          
                          {fileComments.length > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {fileComments.length} comments
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-3 text-sm">
                          {fileStats.additions > 0 && (
                            <span className="text-green-500 flex items-center gap-1">
                              <Plus className="h-3 w-3" />
                              {fileStats.additions}
                            </span>
                          )}
                          {fileStats.deletions > 0 && (
                            <span className="text-red-500 flex items-center gap-1">
                              <Minus className="h-3 w-3" />
                              {fileStats.deletions}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* File content */}
                      {fileState?.isExpanded && (
                        <div className="space-y-0">
                          {hunks.map((hunk, hunkIndex) => renderFileHunk(hunk, hunkIndex))}
                        </div>
                      )}
                    </div>
                  )
                })()}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <File className="h-12 w-12 mx-auto mb-4" />
                  <p>Select a file to view its diff</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Claude Response Modal */}
      <ClaudeResponseModal
        isOpen={isClaudeModalOpen}
        onClose={() => setIsClaudeModalOpen(false)}
        responseData={selectedClaudeResponse}
        isLoading={false}
        onAcceptChange={handleAcceptChange}
        onRejectChange={handleRejectChange}
        onUpdateInstructions={handleUpdateInstructions}
        onGlobalUpdate={handleGlobalUpdate}
        acceptedSuggestions={acceptedSuggestions}
      />
    </Card>
  )
}