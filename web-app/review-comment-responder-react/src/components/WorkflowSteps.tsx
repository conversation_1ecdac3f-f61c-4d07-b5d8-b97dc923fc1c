import React from 'react'
import { 
  Eye, 
  Play, 
  RotateCcw, 
  CheckCircle,
  ChevronRight,
  Settings
} from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'

type ReviewStep = 'select-work' | 'configure-review' | 'review-progress' | 'review-results'

interface WorkflowStepsProps {
  currentStep: ReviewStep
  onResetWorkflow: () => void
}

export const WorkflowSteps: React.FC<WorkflowStepsProps> = ({
  currentStep,
  onResetWorkflow
}) => {
  const getStepStatus = (step: ReviewStep) => {
    const stepOrder: ReviewStep[] = ['select-work', 'configure-review', 'review-progress', 'review-results']
    const currentIndex = stepOrder.indexOf(currentStep)
    const stepIndex = stepOrder.indexOf(step)
    
    if (stepIndex < currentIndex) return 'completed'
    if (stepIndex === currentIndex) return 'current'
    return 'pending'
  }

  const steps = [
    { step: 'select-work', label: 'Select Work', icon: Eye },
    { step: 'configure-review', label: 'Configure', icon: Settings },
    { step: 'review-progress', label: 'Review', icon: Play },
    { step: 'review-results', label: 'Results', icon: CheckCircle }
  ]

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold">Review Workflow</h2>
          {currentStep !== 'select-work' && (
            <Button
              variant="outline"
              size="sm"
              onClick={onResetWorkflow}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Start Over
            </Button>
          )}
        </div>
        
        <div className="flex items-center gap-4">
          {steps.map(({ step, label, icon: Icon }, index) => {
            const status = getStepStatus(step as ReviewStep)
            return (
              <React.Fragment key={step}>
                <div className="flex items-center gap-2">
                  <div className={`p-2 rounded-full ${
                    status === 'completed' ? 'bg-green-500 text-white' :
                    status === 'current' ? 'bg-primary text-white' :
                    'bg-muted text-muted-foreground'
                  }`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <span className={`text-sm font-medium ${
                    status === 'current' ? 'text-foreground' : 'text-muted-foreground'
                  }`}>
                    {label}
                  </span>
                </div>
                {index < 3 && (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
              </React.Fragment>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}