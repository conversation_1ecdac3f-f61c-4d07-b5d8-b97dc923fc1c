/**
 * Feature Flag Provider
 * React Context Provider for feature flag management with A/B testing support
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import type { ReactNode } from 'react'
import { FeatureFlags, FeatureFlagEvaluator, type FeatureFlagContext } from '../config/featureFlags'
import { env } from '../config/environment'

/**
 * Feature flag context interface
 */
interface FeatureFlagProviderContext {
  // Feature flag evaluator
  evaluator: FeatureFlagEvaluator
  
  // Context management
  context: FeatureFlagContext
  setContext: (context: Partial<FeatureFlagContext>) => void
  
  // Feature checks
  isFeatureEnabled: (flagPath: string) => boolean
  isMultiAgentEnabled: () => boolean
  isRealtimeEnabled: () => boolean
  isFallbackEnabled: () => boolean
  
  // A/B Testing
  getExperimentGroup: (experimentName: string) => string
  isInExperimentGroup: (experimentName: string, group: string) => boolean
  
  // Configuration
  getWebSocketConfig: () => ReturnType<FeatureFlagEvaluator['getWebSocketConfig']>
  getFallbackConfig: () => ReturnType<FeatureFlagEvaluator['getFallbackConfig']>
  
  // Remote configuration
  refreshFlags: () => Promise<void>
  isLoading: boolean
  lastUpdate: Date | null
  
  // Debug utilities
  getAllEnabledFeatures: () => Record<string, boolean>
  exportDebugInfo: () => any
}

/**
 * Feature flag context
 */
const FeatureFlagContext = createContext<FeatureFlagProviderContext | null>(null)

/**
 * Remote feature flag configuration interface
 */
interface RemoteFeatureFlags {
  flags: Record<string, boolean | number | string>
  experiments: Record<string, {
    enabled: boolean
    groups: string[]
    allocation: Record<string, number> // group -> percentage
  }>
  timestamp: number
  version: string
}

/**
 * A/B Testing utilities
 */
class ABTestingManager {
  private experiments: Map<string, { groups: string[]; allocation: Record<string, number> }> = new Map()
  private userGroups: Map<string, string> = new Map()

  /**
   * Set up experiment configuration
   */
  setupExperiment(
    experimentName: string, 
    groups: string[], 
    allocation: Record<string, number>
  ): void {
    this.experiments.set(experimentName, { groups, allocation })
  }

  /**
   * Get experiment group for user
   */
  getExperimentGroup(experimentName: string, userId: string): string {
    const cacheKey = `${experimentName}:${userId}`
    
    if (this.userGroups.has(cacheKey)) {
      return this.userGroups.get(cacheKey)!
    }

    const experiment = this.experiments.get(experimentName)
    if (!experiment) {
      return 'control'
    }

    // Deterministic assignment based on userId hash
    const hash = this.hashString(`${experimentName}:${userId}`)
    const percentage = hash % 100
    let cumulative = 0

    for (const [group, allocation] of Object.entries(experiment.allocation)) {
      cumulative += allocation
      if (percentage < cumulative) {
        this.userGroups.set(cacheKey, group)
        return group
      }
    }

    // Fallback to control
    this.userGroups.set(cacheKey, 'control')
    return 'control'
  }

  /**
   * Check if user is in specific experiment group
   */
  isInExperimentGroup(experimentName: string, userId: string, targetGroup: string): boolean {
    const userGroup = this.getExperimentGroup(experimentName, userId)
    return userGroup === targetGroup
  }

  /**
   * Hash function for consistent user bucketing
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }
}

/**
 * Remote configuration manager
 */
class RemoteConfigManager {
  private cache: RemoteFeatureFlags | null = null
  private lastFetch: number = 0
  private cacheTTL: number = 5 * 60 * 1000 // 5 minutes

  /**
   * Fetch remote configuration
   */
  async fetchRemoteConfig(): Promise<RemoteFeatureFlags | null> {
    // Skip if not in production or remote config disabled
    if (!env.isProduction || !env.features.analytics) {
      return null
    }

    const now = Date.now()
    
    // Return cached config if still valid
    if (this.cache && (now - this.lastFetch) < this.cacheTTL) {
      return this.cache
    }

    try {
      // In a real implementation, this would fetch from a remote API
      // For now, return null to use static configuration
      const remoteConfig: RemoteFeatureFlags = {
        flags: {},
        experiments: {
          'multi_agent_rollout': {
            enabled: true,
            groups: ['control', 'treatment'],
            allocation: { 'control': 50, 'treatment': 50 }
          }
        },
        timestamp: now,
        version: '1.0.0'
      }

      this.cache = remoteConfig
      this.lastFetch = now
      
      return remoteConfig
    } catch (error) {
      console.warn('Failed to fetch remote feature flags:', error)
      return null
    }
  }

  /**
   * Get cached configuration
   */
  getCached(): RemoteFeatureFlags | null {
    return this.cache
  }
}

/**
 * Feature Flag Provider Props
 */
interface FeatureFlagProviderProps {
  children: ReactNode
  initialContext?: Partial<FeatureFlagContext>
  enableRemoteConfig?: boolean
}

/**
 * Feature Flag Provider Component
 */
export const FeatureFlagProvider: React.FC<FeatureFlagProviderProps> = ({
  children,
  initialContext = {},
  enableRemoteConfig = env.isProduction
}) => {
  // State
  const [evaluator] = useState(() => new FeatureFlagEvaluator())
  const [context, setContextState] = useState<FeatureFlagContext>({
    timestamp: Date.now(),
    sessionId: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    userAgent: navigator.userAgent,
    ...initialContext
  })
  const [isLoading, setIsLoading] = useState(false)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  
  // Managers
  const [abTestingManager] = useState(() => new ABTestingManager())
  const [remoteConfigManager] = useState(() => new RemoteConfigManager())

  /**
   * Update context
   */
  const setContext = useCallback((newContext: Partial<FeatureFlagContext>) => {
    setContextState(prev => {
      const updated = { ...prev, ...newContext }
      evaluator.setContext(updated)
      return updated
    })
  }, [evaluator])

  /**
   * Feature flag evaluation methods
   */
  const isFeatureEnabled = useCallback((flagPath: string): boolean => {
    try {
      const pathParts = flagPath.split('.')
      let current: any = FeatureFlags
      
      for (const part of pathParts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part]
        } else {
          return false
        }
      }
      
      return typeof current === 'boolean' ? current : false
    } catch {
      return false
    }
  }, [])

  const isMultiAgentEnabled = useCallback(() => evaluator.isMultiAgentEnabled(), [evaluator])
  const isRealtimeEnabled = useCallback(() => evaluator.isRealtimeUpdatesEnabled(), [evaluator])
  const isFallbackEnabled = useCallback(() => evaluator.isAutoFallbackEnabled(), [evaluator])

  /**
   * A/B Testing methods
   */
  const getExperimentGroup = useCallback((experimentName: string): string => {
    const userId = context.userId || 'anonymous'
    return abTestingManager.getExperimentGroup(experimentName, userId)
  }, [abTestingManager, context.userId])

  const isInExperimentGroup = useCallback((experimentName: string, group: string): boolean => {
    const userId = context.userId || 'anonymous'
    return abTestingManager.isInExperimentGroup(experimentName, userId, group)
  }, [abTestingManager, context.userId])

  /**
   * Configuration getters
   */
  const getWebSocketConfig = useCallback(() => evaluator.getWebSocketConfig(), [evaluator])
  const getFallbackConfig = useCallback(() => evaluator.getFallbackConfig(), [evaluator])

  /**
   * Refresh remote flags
   */
  const refreshFlags = useCallback(async (): Promise<void> => {
    if (!enableRemoteConfig) return

    setIsLoading(true)
    try {
      const remoteConfig = await remoteConfigManager.fetchRemoteConfig()
      
      if (remoteConfig) {
        // Setup experiments from remote config
        Object.entries(remoteConfig.experiments).forEach(([name, config]) => {
          if (config.enabled) {
            abTestingManager.setupExperiment(name, config.groups, config.allocation)
          }
        })
        
        setLastUpdate(new Date(remoteConfig.timestamp))
      }
    } catch (error) {
      console.error('Failed to refresh feature flags:', error)
    } finally {
      setIsLoading(false)
    }
  }, [enableRemoteConfig, remoteConfigManager, abTestingManager])

  /**
   * Debug utilities
   */
  const getAllEnabledFeatures = useCallback(() => evaluator.getEnabledFeatures(), [evaluator])
  
  const exportDebugInfo = useCallback(() => {
    if (!env.features.debugMode) return null
    
    return {
      context,
      enabledFeatures: getAllEnabledFeatures(),
      experiments: Array.from(abTestingManager['experiments'].entries()),
      environment: env.NODE_ENV,
      lastUpdate,
      isLoading
    }
  }, [context, getAllEnabledFeatures, abTestingManager, lastUpdate, isLoading])

  /**
   * Initialize context and remote config on mount
   */
  useEffect(() => {
    evaluator.setContext(context)
    
    if (enableRemoteConfig) {
      refreshFlags()
      
      // Set up periodic refresh in production
      if (env.isProduction) {
        const interval = setInterval(refreshFlags, 5 * 60 * 1000) // 5 minutes
        return () => clearInterval(interval)
      }
    }
  }, [evaluator, context, enableRemoteConfig, refreshFlags])

  /**
   * Log feature flag initialization
   */
  useEffect(() => {
    if (env.features.debugMode) {
      console.group('🚩 Feature Flags Initialized')
      console.log('Context:', context)
      console.log('Enabled Features:', getAllEnabledFeatures())
      console.log('Multi-Agent Enabled:', isMultiAgentEnabled())
      console.log('Realtime Updates:', isRealtimeEnabled())
      console.groupEnd()
    }
  }, [context, getAllEnabledFeatures, isMultiAgentEnabled, isRealtimeEnabled])

  /**
   * Context value
   */
  const contextValue: FeatureFlagProviderContext = {
    evaluator,
    context,
    setContext,
    isFeatureEnabled,
    isMultiAgentEnabled,
    isRealtimeEnabled,
    isFallbackEnabled,
    getExperimentGroup,
    isInExperimentGroup,
    getWebSocketConfig,
    getFallbackConfig,
    refreshFlags,
    isLoading,
    lastUpdate,
    getAllEnabledFeatures,
    exportDebugInfo
  }

  return (
    <FeatureFlagContext.Provider value={contextValue}>
      {children}
    </FeatureFlagContext.Provider>
  )
}

/**
 * Hook to use feature flags
 */
export const useFeatureFlags = (): FeatureFlagProviderContext => {
  const context = useContext(FeatureFlagContext)
  
  if (!context) {
    throw new Error('useFeatureFlags must be used within a FeatureFlagProvider')
  }
  
  return context
}

/**
 * HOC for feature flag gating
 */
export const withFeatureFlag = <P extends object>(
  Component: React.ComponentType<P>,
  flagPath: string,
  fallback?: React.ComponentType<P> | React.ReactElement | null
) => {
  const WrappedComponent = (props: P) => {
    const { isFeatureEnabled } = useFeatureFlags()
    
    if (!isFeatureEnabled(flagPath)) {
      if (fallback) {
        if (React.isValidElement(fallback)) {
          return fallback
        }
        const FallbackComponent = fallback as React.ComponentType<P>
        return <FallbackComponent {...props} />
      }
      return null
    }
    
    return <Component {...props} />
  }
  
  WrappedComponent.displayName = `withFeatureFlag(${Component.displayName || Component.name})`
  return WrappedComponent
}

/**
 * Component for conditional rendering based on feature flags
 */
interface FeatureGateProps {
  flag: string
  children: ReactNode
  fallback?: ReactNode
}

export const FeatureGate: React.FC<FeatureGateProps> = ({ flag, children, fallback = null }) => {
  const { isFeatureEnabled } = useFeatureFlags()
  
  return isFeatureEnabled(flag) ? <>{children}</> : <>{fallback}</>
}

/**
 * A/B Test Component
 */
interface ABTestProps {
  experiment: string
  children: Record<string, ReactNode>
  fallback?: ReactNode
}

export const ABTest: React.FC<ABTestProps> = ({ experiment, children, fallback = null }) => {
  const { getExperimentGroup } = useFeatureFlags()
  
  const group = getExperimentGroup(experiment)
  const content = children[group]
  
  return content ? <>{content}</> : <>{fallback}</>
}

export default FeatureFlagProvider