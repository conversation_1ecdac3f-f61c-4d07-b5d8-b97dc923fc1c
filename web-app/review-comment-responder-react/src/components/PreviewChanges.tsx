import React, { useState, useEffect } from 'react'
import { Eye, Check, X, Edit, Search } from 'lucide-react'
import { But<PERSON> } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { FilePreviewCard } from './FilePreviewCard'
import type { Comment } from '../types'
import { useCommentStore } from '../store/useCommentStore'
import { ApiService } from '../services/api'

interface PreviewChangesProps {
  comment: Comment
}

export const PreviewChanges: React.FC<PreviewChangesProps> = ({ comment }) => {
  const { updateComment } = useCommentStore()
  const [isApplying, setIsApplying] = useState(false)
  const [modificationPrompt, setModificationPrompt] = useState('')
  const [showModificationInput, setShowModificationInput] = useState(false)
// Note: File modification state moved to FilePreviewCard component

  // Initialize file status if not exists
  useEffect(() => {
    if (!comment.fileStatus && comment.previewChanges) {
      const fileStatus: Record<string, 'pending' | 'accepted' | 'rejected'> = {}
      comment.previewChanges.forEach(fc => {
        fileStatus[fc.file] = 'pending'
      })
      updateComment(comment.id, { fileStatus })
    }
  }, [comment.id, comment.fileStatus, comment.previewChanges, updateComment])

  // Note: acceptFileChange moved to FilePreviewCard component

  // Note: rejectFileChange moved to FilePreviewCard component

  const applyAcceptedChanges = async () => {
    if (!comment.previewChanges || !comment.session_id) return

    const acceptedChanges = comment.previewChanges.filter(fc => 
      comment.fileStatus?.[fc.file] === 'accepted'
    )

    if (acceptedChanges.length === 0) {
      // TODO: Show warning toast
      console.warn('No changes accepted yet')
      return
    }

    try {
      setIsApplying(true)
      
      await ApiService.executeApprovedChanges(comment.session_id, acceptedChanges)
      
      updateComment(comment.id, {
        appliedChanges: acceptedChanges,
        previewChanges: undefined,
        needsApproval: false
      })
      
      // TODO: Show success toast
      console.log(`All ${acceptedChanges.length} accepted changes applied successfully!`)
    } catch (error) {
      console.error('Error applying changes:', error)
      // TODO: Show error toast
    } finally {
      setIsApplying(false)
    }
  }

  const rejectAllChanges = () => {
    updateComment(comment.id, {
      previewChanges: undefined,
      needsApproval: false,
      fileStatus: undefined
    })
    
    // TODO: Show info toast
    console.log('All changes rejected')
  }

  const submitModification = async () => {
    if (!modificationPrompt.trim() || !comment.session_id) return

    try {
      setIsApplying(true)
      
      const result = await ApiService.modifySessionInstructions(comment.session_id, modificationPrompt)
      
      if (result.success && result.response) {
        updateComment(comment.id, {
          response: result.response,
          previewChanges: result.response.preview_changes,
          needsApproval: true,
          fileStatus: undefined // Reset file status
        })
        
        setModificationPrompt('')
        setShowModificationInput(false)
        
        // TODO: Show success toast
        console.log('New changes suggested based on your feedback')
      }
    } catch (error) {
      console.error('Error modifying instructions:', error)
      // TODO: Show error toast
    } finally {
      setIsApplying(false)
    }
  }

// Note: File modification handlers moved to FilePreviewCard component

// Note: submitFileModification moved to FilePreviewCard component

  if (!comment.previewChanges || comment.previewChanges.length === 0) {
    return null
  }

  const acceptedCount = Object.values(comment.fileStatus || {}).filter(s => s === 'accepted').length
  const totalCount = comment.previewChanges.length

  return (
    <Card className="mt-4 border-orange-200 bg-orange-50">
      <CardHeader className="bg-orange-100">
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <Eye className="h-5 w-5" />
          Preview: Suggested Changes (Not Applied Yet)
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-4">
        {/* Analysis Section */}
        {comment.response?.analysis && (
          <Card className="mb-4 bg-blue-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2 text-blue-700">
                <Search className="h-4 w-4" />
                Agent Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 text-sm">
              {comment.response.analysis.affected_files && (
                <div className="mb-2">
                  <strong>Affected Files:</strong>
                  <ul className="ml-4 mt-1 list-disc">
                    {comment.response.analysis.affected_files.map((file, i) => (
                      <li key={i}>{file}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {comment.response.analysis.impact && (
                <div className="mb-2">
                  <strong>Impact:</strong> {comment.response.analysis.impact}
                </div>
              )}
              
              {comment.response.analysis.todo_list && (
                <div>
                  <strong>Agent's Todo List:</strong>
                  <ol className="ml-4 mt-1 list-decimal">
                    {comment.response.analysis.todo_list.map((task, i) => (
                      <li key={i}>{task}</li>
                    ))}
                  </ol>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* File Changes */}
        {comment.previewChanges.map((fileChange, index) => {
          // Check if this file has a file-specific response
          const fileSpecificData = comment.fileSpecificResponses?.[fileChange.file]
          
          return (
            <FilePreviewCard
              key={`${fileChange.file}-${index}`}
              comment={comment}
              fileChange={fileChange}
              hasFileSpecificResponse={!!fileSpecificData}
              fileSpecificResponse={fileSpecificData?.response}
              fileSpecificPreviewChanges={fileSpecificData?.previewChanges}
            />
          )
        })}

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 mt-4 p-4 bg-orange-100 rounded-lg">
          <div className="text-sm font-medium">
            Status: {acceptedCount} of {totalCount} files accepted
          </div>
          
          <div className="flex gap-2 flex-wrap">
            {acceptedCount > 0 && (
              <Button 
                onClick={applyAcceptedChanges}
                disabled={isApplying}
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="h-4 w-4 mr-1" />
                Apply {acceptedCount} Accepted Change{acceptedCount > 1 ? 's' : ''}
              </Button>
            )}
            
            <Button variant="destructive" onClick={rejectAllChanges} disabled={isApplying}>
              <X className="h-4 w-4 mr-1" />
              Reject All
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => setShowModificationInput(!showModificationInput)}
              disabled={isApplying}
            >
              <Edit className="h-4 w-4 mr-1" />
              Modify Global Instructions
            </Button>
          </div>

          {/* Modification Input */}
          {showModificationInput && (
            <div className="mt-3">
              <textarea
                className="w-full p-3 border rounded-lg font-mono text-sm"
                rows={3}
                placeholder="Tell Claude how to modify the suggested changes globally..."
                value={modificationPrompt}
                onChange={(e) => setModificationPrompt(e.target.value)}
              />
              <Button 
                className="mt-2"
                onClick={submitModification}
                disabled={!modificationPrompt.trim() || isApplying}
              >
                Send Modification
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}