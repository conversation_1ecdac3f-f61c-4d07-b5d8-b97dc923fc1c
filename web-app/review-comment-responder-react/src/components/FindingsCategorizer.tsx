/**
 * Findings Categorizer Component
 * Organizes and filters findings by agent type, priority, and file
 */

import React, { useState, useMemo } from 'react'
import { 
  Filter,
  Search,
  SortAsc,
  SortDesc,
  FileText,
  AlertTriangle,
  Bug,
  Shield,
  Target,
  Settings,
  BarChart3,
  Users,
  ExternalLink,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Input } from './ui/input'
import { Alert, AlertDescription } from './ui/alert'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from './ui/collapsible'
import type { 
  AgentType
} from '../types/multi-agent'

interface Finding {
  priority: 'high' | 'medium' | 'low'
  type: string
  description: string
  agent_type: AgentType
  file?: string
  line?: number
  suggestion?: string
}

interface FindingsCategorizerProps {
  findings: Finding[]
  onFileClick?: (filename: string, line?: number) => void
  className?: string
}

type SortBy = 'priority' | 'agent' | 'file' | 'type'
type SortOrder = 'asc' | 'desc'

// Helper functions
const getAgentDisplayName = (agentType: AgentType): string => {
  const displayNames: Record<AgentType, string> = {
    'acceptance_criteria': 'Acceptance Criteria',
    'bug_detection': 'Bug Detection',
    'security_analysis': 'Security Analysis', 
    'logic_analysis': 'Logic Analysis',
    'quality_analysis': 'Quality Analysis',
    'architecture_analysis': 'Architecture Analysis',
    'summary': 'Summary & Recommendations'
  }
  return displayNames[agentType] || agentType
}

const getAgentIcon = (agentType: AgentType) => {
  const icons: Record<AgentType, React.ComponentType<{ className?: string }>> = {
    'acceptance_criteria': Target,
    'bug_detection': Bug,
    'security_analysis': Shield,
    'logic_analysis': Settings,
    'quality_analysis': BarChart3,
    'architecture_analysis': Users,
    'summary': FileText
  }
  return icons[agentType] || AlertTriangle
}

const getAgentColor = (agentType: AgentType): string => {
  const colors: Record<AgentType, string> = {
    'acceptance_criteria': 'text-green-600 bg-green-50 border-green-200',
    'bug_detection': 'text-red-600 bg-red-50 border-red-200',
    'security_analysis': 'text-orange-600 bg-orange-50 border-orange-200',
    'logic_analysis': 'text-blue-600 bg-blue-50 border-blue-200',
    'quality_analysis': 'text-purple-600 bg-purple-50 border-purple-200',
    'architecture_analysis': 'text-indigo-600 bg-indigo-50 border-indigo-200',
    'summary': 'text-gray-600 bg-gray-50 border-gray-200'
  }
  return colors[agentType] || 'text-gray-600 bg-gray-50 border-gray-200'
}

const getPriorityColor = (priority: 'high' | 'medium' | 'low'): string => {
  switch (priority) {
    case 'high': return 'text-red-700 bg-red-100 border-red-300'
    case 'medium': return 'text-yellow-700 bg-yellow-100 border-yellow-300' 
    case 'low': return 'text-blue-700 bg-blue-100 border-blue-300'
    default: return 'text-gray-700 bg-gray-100 border-gray-300'
  }
}

const getPriorityWeight = (priority: 'high' | 'medium' | 'low'): number => {
  switch (priority) {
    case 'high': return 3
    case 'medium': return 2
    case 'low': return 1
    default: return 0
  }
}

export const FindingsCategorizer: React.FC<FindingsCategorizerProps> = ({
  findings,
  onFileClick,
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAgents, setSelectedAgents] = useState<Set<AgentType>>(new Set())
  const [selectedPriorities, setSelectedPriorities] = useState<Set<string>>(new Set())
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<SortBy>('priority')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['priority', 'agent']))

  // Get unique values for filtering
  const uniqueAgents = useMemo(() => 
    Array.from(new Set(findings.map(f => f.agent_type))), [findings]
  )
  
  const uniquePriorities = useMemo(() => 
    Array.from(new Set(findings.map(f => f.priority))), [findings]
  )
  
  const uniqueFiles = useMemo(() => 
    Array.from(new Set(findings.map(f => f.file).filter(Boolean))), [findings]
  )

  // Filter and sort findings
  const filteredAndSortedFindings = useMemo(() => {
    let filtered = findings

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(f => 
        f.description.toLowerCase().includes(term) ||
        f.type.toLowerCase().includes(term) ||
        f.file?.toLowerCase().includes(term) ||
        getAgentDisplayName(f.agent_type).toLowerCase().includes(term)
      )
    }

    // Apply agent filter
    if (selectedAgents.size > 0) {
      filtered = filtered.filter(f => selectedAgents.has(f.agent_type))
    }

    // Apply priority filter
    if (selectedPriorities.size > 0) {
      filtered = filtered.filter(f => selectedPriorities.has(f.priority))
    }

    // Apply file filter
    if (selectedFiles.size > 0) {
      filtered = filtered.filter(f => f.file && selectedFiles.has(f.file))
    }

    // Sort findings
    return filtered.sort((a, b) => {
      let comparison = 0
      
      switch (sortBy) {
        case 'priority':
          comparison = getPriorityWeight(b.priority) - getPriorityWeight(a.priority)
          break
        case 'agent':
          comparison = getAgentDisplayName(a.agent_type).localeCompare(getAgentDisplayName(b.agent_type))
          break
        case 'file':
          comparison = (a.file || '').localeCompare(b.file || '')
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }
      
      return sortOrder === 'desc' ? -comparison : comparison
    })
  }, [findings, searchTerm, selectedAgents, selectedPriorities, selectedFiles, sortBy, sortOrder])

  // Group findings by different categories
  const groupedByPriority = useMemo(() => {
    const groups: Record<string, Finding[]> = {}
    filteredAndSortedFindings.forEach(finding => {
      if (!groups[finding.priority]) groups[finding.priority] = []
      groups[finding.priority].push(finding)
    })
    return groups
  }, [filteredAndSortedFindings])

  const groupedByAgent = useMemo(() => {
    const groups: Record<string, Finding[]> = {}
    filteredAndSortedFindings.forEach(finding => {
      if (!groups[finding.agent_type]) groups[finding.agent_type] = []
      groups[finding.agent_type].push(finding)
    })
    return groups
  }, [filteredAndSortedFindings])

  const groupedByFile = useMemo(() => {
    const groups: Record<string, Finding[]> = {}
    filteredAndSortedFindings.forEach(finding => {
      const file = finding.file || 'Other'
      if (!groups[file]) groups[file] = []
      groups[file].push(finding)
    })
    return groups
  }, [filteredAndSortedFindings])

  // Toggle functions
  const toggleAgentFilter = (agent: AgentType) => {
    const newSelected = new Set(selectedAgents)
    if (newSelected.has(agent)) {
      newSelected.delete(agent)
    } else {
      newSelected.add(agent)
    }
    setSelectedAgents(newSelected)
  }

  const togglePriorityFilter = (priority: string) => {
    const newSelected = new Set(selectedPriorities)
    if (newSelected.has(priority)) {
      newSelected.delete(priority)
    } else {
      newSelected.add(priority)
    }
    setSelectedPriorities(newSelected)
  }

  const toggleFileFilter = (file: string) => {
    const newSelected = new Set(selectedFiles)
    if (newSelected.has(file)) {
      newSelected.delete(file)
    } else {
      newSelected.add(file)
    }
    setSelectedFiles(newSelected)
  }

  const toggleCategoryExpansion = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const clearAllFilters = () => {
    setSearchTerm('')
    setSelectedAgents(new Set())
    setSelectedPriorities(new Set())
    setSelectedFiles(new Set())
  }

  const renderFinding = (finding: Finding, index: number) => {
    const IconComponent = getAgentIcon(finding.agent_type)
    
    return (
      <Alert key={index} className={`${getPriorityColor(finding.priority)} mb-3`}>
        <div className="flex items-start gap-3">
          <IconComponent className="h-4 w-4 mt-0.5" />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Badge variant="outline" className="text-xs">
                {getAgentDisplayName(finding.agent_type)}
              </Badge>
              <Badge variant="outline" className="text-xs capitalize">
                {finding.priority}
              </Badge>
              {finding.file && (
                <Badge variant="secondary" className="text-xs">
                  {finding.file}
                  {finding.line && `:${finding.line}`}
                </Badge>
              )}
            </div>
            <AlertDescription className="mb-2">
              <strong>{finding.type}:</strong> {finding.description}
            </AlertDescription>
            {finding.suggestion && (
              <div className="text-sm bg-white/50 rounded p-2 mt-2">
                <strong>Suggestion:</strong> {finding.suggestion}
              </div>
            )}
            {finding.file && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  if (finding.file) {
                    onFileClick?.(finding.file, finding.line)
                  }
                }}
                className="mt-2 h-7 text-xs"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                View in Code
              </Button>
            )}
          </div>
        </div>
      </Alert>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Findings Filter & Search ({filteredAndSortedFindings.length} of {findings.length})
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                disabled={searchTerm === '' && selectedAgents.size === 0 && 
                         selectedPriorities.size === 0 && selectedFiles.size === 0}
              >
                Clear Filters
              </Button>
              
              <div className="flex items-center gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortBy)}
                  className="text-sm border rounded px-2 py-1"
                >
                  <option value="priority">Priority</option>
                  <option value="agent">Agent</option>
                  <option value="file">File</option>
                  <option value="type">Type</option>
                </select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="flex items-center gap-1"
                >
                  {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search findings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Buttons */}
          <div className="space-y-4">
            {/* Agent Filters */}
            <div>
              <h4 className="text-sm font-medium mb-2">Filter by Agent:</h4>
              <div className="flex flex-wrap gap-2">
                {uniqueAgents.map(agent => {
                  const IconComponent = getAgentIcon(agent)
                  const isSelected = selectedAgents.has(agent)
                  return (
                    <Button
                      key={agent}
                      variant={isSelected ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleAgentFilter(agent)}
                      className="flex items-center gap-2"
                    >
                      <IconComponent className="h-3 w-3" />
                      {getAgentDisplayName(agent)}
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {findings.filter(f => f.agent_type === agent).length}
                      </Badge>
                    </Button>
                  )
                })}
              </div>
            </div>

            {/* Priority Filters */}
            <div>
              <h4 className="text-sm font-medium mb-2">Filter by Priority:</h4>
              <div className="flex gap-2">
                {uniquePriorities.map(priority => (
                  <Button
                    key={priority}
                    variant={selectedPriorities.has(priority) ? "default" : "outline"}
                    size="sm"
                    onClick={() => togglePriorityFilter(priority)}
                    className="capitalize"
                  >
                    {priority}
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {findings.filter(f => f.priority === priority).length}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>

            {/* File Filters */}
            {uniqueFiles.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2">Filter by File:</h4>
                <div className="flex flex-wrap gap-2 max-h-20 overflow-y-auto">
                  {uniqueFiles.map(file => (
                    <Button
                      key={file}
                      variant={selectedFiles.has(file!) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleFileFilter(file!)}
                      className="text-xs"
                    >
                      {file}
                      <Badge variant="secondary" className="ml-1 text-xs">
                        {findings.filter(f => f.file === file).length}
                      </Badge>
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Categorized Results */}
      <div className="space-y-4">
        {/* By Priority */}
        <Collapsible 
          open={expandedCategories.has('priority')}
          onOpenChange={() => toggleCategoryExpansion('priority')}
        >
          <CollapsibleTrigger asChild>
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Findings by Priority
                  </div>
                  {expandedCategories.has('priority') ? 
                    <ChevronDown className="h-4 w-4" /> : 
                    <ChevronRight className="h-4 w-4" />
                  }
                </CardTitle>
              </CardHeader>
            </Card>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <div className="space-y-4 mt-4">
              {(['high', 'medium', 'low'] as const).map(priority => {
                const priorityFindings = groupedByPriority[priority] || []
                if (priorityFindings.length === 0) return null
                
                return (
                  <Card key={priority} className={getPriorityColor(priority)}>
                    <CardHeader>
                      <CardTitle className="capitalize text-lg">
                        {priority} Priority ({priorityFindings.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {priorityFindings.map((finding, index) => renderFinding(finding, index))}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* By Agent */}
        <Collapsible 
          open={expandedCategories.has('agent')}
          onOpenChange={() => toggleCategoryExpansion('agent')}
        >
          <CollapsibleTrigger asChild>
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Findings by Agent
                  </div>
                  {expandedCategories.has('agent') ? 
                    <ChevronDown className="h-4 w-4" /> : 
                    <ChevronRight className="h-4 w-4" />
                  }
                </CardTitle>
              </CardHeader>
            </Card>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <div className="space-y-4 mt-4">
              {Object.entries(groupedByAgent).map(([agentType, agentFindings]) => {
                const IconComponent = getAgentIcon(agentType as AgentType)
                
                return (
                  <Card key={agentType} className={getAgentColor(agentType as AgentType)}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <IconComponent className="h-5 w-5" />
                        {getAgentDisplayName(agentType as AgentType)} ({agentFindings.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {agentFindings.map((finding, index) => renderFinding(finding, index))}
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* By File */}
        {Object.keys(groupedByFile).length > 1 && (
          <Collapsible 
            open={expandedCategories.has('file')}
            onOpenChange={() => toggleCategoryExpansion('file')}
          >
            <CollapsibleTrigger asChild>
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Findings by File
                    </div>
                    {expandedCategories.has('file') ? 
                      <ChevronDown className="h-4 w-4" /> : 
                      <ChevronRight className="h-4 w-4" />
                    }
                  </CardTitle>
                </CardHeader>
              </Card>
            </CollapsibleTrigger>
            
            <CollapsibleContent>
              <div className="space-y-4 mt-4">
                {Object.entries(groupedByFile).map(([file, fileFindings]) => (
                  <Card key={file} className="border-gray-200 bg-gray-50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        {file} ({fileFindings.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {fileFindings.map((finding, index) => renderFinding(finding, index))}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>

      {/* No Results Message */}
      {filteredAndSortedFindings.length === 0 && (
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No findings match your filters
              </h3>
              <p className="text-gray-500 text-sm mb-4">
                Try adjusting your search terms or clearing some filters to see more results.
              </p>
              <Button
                variant="outline"
                onClick={clearAllFilters}
              >
                Clear All Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}