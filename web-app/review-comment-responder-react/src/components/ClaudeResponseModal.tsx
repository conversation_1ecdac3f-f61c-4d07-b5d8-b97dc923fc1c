import React, { useState } from 'react'
import { Bot, Check, X, FileCode, Loader2, MessageSquare, Wand2, ChevronDown, ChevronRight } from 'lucide-react'
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription 
} from './ui/dialog'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { CodeDiffEditor } from './CodeDiffEditor'

interface ClaudeResponseData {
  comment_text: string
  file: string
  line: string
  response: string
  confidence: number
  analysis?: {
    affected_files: string[]
    dependencies: string[]
    impact: string
    todo_list: string[]
  }
  preview_changes?: Array<{
    file: string
    reason: string
    changes: Array<{
      type: string
      line_start: number
      line_end: number
      current_content: string
      suggested_content: string
      explanation: string
    }>
  }>
  session_id?: string
}

interface ClaudeResponseModalProps {
  isOpen: boolean
  onClose: () => void
  responseData: ClaudeResponseData | null
  isLoading?: boolean
  onAcceptChange?: (fileChange: any) => void
  onRejectChange?: (fileChange: any) => void
  onUpdateInstructions?: (instructions: string, fileIndex?: number, changeIndex?: number) => void
  onGlobalUpdate?: (instructions: string) => void
  acceptedSuggestions?: Set<string> // Track which suggestions are accepted
}

export const ClaudeResponseModal: React.FC<ClaudeResponseModalProps> = ({
  isOpen,
  onClose,
  responseData,
  isLoading = false,
  onAcceptChange,
  onRejectChange,
  onUpdateInstructions,
  onGlobalUpdate,
  acceptedSuggestions = new Set()
}) => {
  const [updateInstructions, setUpdateInstructions] = useState('')
  const [showGlobalUpdate, setShowGlobalUpdate] = useState(false)
  const [activeFileUpdate, setActiveFileUpdate] = useState<{fileIndex: number, changeIndex: number} | null>(null)
  const [expandedChanges, setExpandedChanges] = useState<Set<string>>(new Set())

  const getLanguageFromFile = (filename: string): string => {
    const extension = filename.split('.').pop()?.toLowerCase()
    const languageMap: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'css': 'css',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'sql': 'sql',
      'php': 'php',
      'cpp': 'cpp',
      'c': 'c',
      'cs': 'csharp',
      'go': 'go',
      'rs': 'rust',
      'rb': 'ruby',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    }
    return languageMap[extension || ''] || 'plaintext'
  }


  const handleAcceptFileChange = (fileChange: any, fileIndex: number) => {
    if (onAcceptChange) {
      onAcceptChange({ ...fileChange, fileIndex })
    }
  }

  const handleRejectFileChange = (fileChange: any, fileIndex: number) => {
    if (onRejectChange) {
      onRejectChange({ ...fileChange, fileIndex })
    }
  }

  const handleUpdateSpecificChange = (fileIndex: number, changeIndex: number) => {
    if (updateInstructions.trim() && onUpdateInstructions) {
      onUpdateInstructions(updateInstructions, fileIndex, changeIndex)
      setUpdateInstructions('')
      setActiveFileUpdate(null)
    }
  }

  const handleGlobalUpdate = () => {
    if (updateInstructions.trim() && onGlobalUpdate) {
      onGlobalUpdate(updateInstructions)
      setUpdateInstructions('')
      setShowGlobalUpdate(false)
    }
  }

  if (!responseData && !isLoading) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            Claude Code Response
            {responseData?.confidence && (
              <Badge variant="secondary" className="ml-2">
                {Math.round(responseData.confidence * 100)}% confidence
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Review the analysis and suggested code changes
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-sm text-muted-foreground">Generating response...</span>
          </div>
        ) : responseData ? (
          <div className="space-y-6">
            {/* Original Comment */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Original Comment
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  <p><strong>File:</strong> {responseData.file}</p>
                  <p><strong>Line:</strong> {responseData.line}</p>
                  <div className="mt-2 p-3 bg-muted rounded">
                    {responseData.comment_text}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Claude's Response */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  Claude's Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none text-sm whitespace-pre-wrap">
                  {responseData.response}
                </div>
              </CardContent>
            </Card>

            {/* Analysis Details */}
            {responseData.analysis && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Analysis Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {responseData.analysis.affected_files.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Affected Files:</h4>
                      <div className="flex flex-wrap gap-1">
                        {responseData.analysis.affected_files.map((file, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <FileCode className="h-3 w-3 mr-1" />
                            {file}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {responseData.analysis.dependencies.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Dependencies:</h4>
                      <ul className="text-sm space-y-1">
                        {responseData.analysis.dependencies.map((dep, index) => (
                          <li key={index} className="text-muted-foreground">• {dep}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {responseData.analysis.impact && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Impact:</h4>
                      <p className="text-sm text-muted-foreground">{responseData.analysis.impact}</p>
                    </div>
                  )}

                  {responseData.analysis.todo_list.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Action Items:</h4>
                      <ul className="text-sm space-y-1">
                        {responseData.analysis.todo_list.map((item, index) => (
                          <li key={index} className="text-muted-foreground">• {item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Code Changes */}
            {responseData.preview_changes && responseData.preview_changes.length > 0 && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Suggested Code Changes</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowGlobalUpdate(!showGlobalUpdate)}
                  >
                    <Wand2 className="h-4 w-4 mr-1" />
                    Update with AI
                  </Button>
                </div>

                {showGlobalUpdate && (
                  <Card>
                    <CardContent className="pt-4">
                      <textarea
                        value={updateInstructions}
                        onChange={(e) => setUpdateInstructions(e.target.value)}
                        placeholder="Provide global instructions to modify all suggested changes..."
                        className="w-full min-h-20 p-3 rounded-lg border border-border bg-background text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                      />
                      <div className="flex gap-2 mt-3">
                        <Button size="sm" onClick={handleGlobalUpdate}>
                          Update All Changes
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowGlobalUpdate(false)}
                        >
                          Cancel
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {responseData.preview_changes.map((fileChange, fileIndex) => (
                  <Card key={fileIndex}>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <FileCode className="h-4 w-4" />
                          {fileChange.file}
                        </div>
                        <div className="flex gap-2">
                          {(() => {
                            const changeId = `${fileChange.file}-${fileIndex}`
                            const isAccepted = acceptedSuggestions.has(changeId)
                            
                            if (isAccepted) {
                              return (
                                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  <Check className="h-3 w-3 mr-1" />
                                  Accepted
                                </Badge>
                              )
                            }
                            
                            return (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleAcceptFileChange(fileChange, fileIndex)}
                                >
                                  <Check className="h-4 w-4 mr-1" />
                                  Accept
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleRejectFileChange(fileChange, fileIndex)}
                                >
                                  <X className="h-4 w-4 mr-1" />
                                  Reject
                                </Button>
                              </>
                            )
                          })()}
                        </div>
                      </CardTitle>
                      {fileChange.reason && (
                        <div className="text-sm text-muted-foreground">
                          {fileChange.reason}
                        </div>
                      )}
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {fileChange.changes.map((change, changeIndex) => {
                        const changeKey = `${fileIndex}-${changeIndex}`
                        const isExpanded = expandedChanges.has(changeKey)
                        
                        return (
                          <div key={changeIndex} className="border rounded-lg">
                            {/* Collapsible Header */}
                            <div 
                              className="flex items-center justify-between p-4 cursor-pointer hover:bg-muted/50"
                              onClick={() => {
                                const newSet = new Set(expandedChanges)
                                if (isExpanded) {
                                  newSet.delete(changeKey)
                                } else {
                                  newSet.add(changeKey)
                                }
                                setExpandedChanges(newSet)
                              }}
                            >
                              <div className="flex items-center gap-2">
                                {isExpanded ? 
                                  <ChevronDown className="h-4 w-4" /> : 
                                  <ChevronRight className="h-4 w-4" />
                                }
                                <div className="text-sm">
                                  <span className="font-medium">Lines {change.line_start}-{change.line_end}</span>
                                  {change.explanation && (
                                    <span className="text-muted-foreground ml-2">• {change.explanation}</span>
                                  )}
                                </div>
                              </div>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setActiveFileUpdate(
                                    activeFileUpdate?.fileIndex === fileIndex && activeFileUpdate?.changeIndex === changeIndex
                                      ? null
                                      : { fileIndex, changeIndex }
                                  )
                                }}
                              >
                                <Wand2 className="h-4 w-4 mr-1" />
                                Update with AI
                              </Button>
                            </div>

                            {/* Lazy-loaded Monaco Editor - only when expanded */}
                            {isExpanded && (
                              <div className="p-4 pt-0">
                                <CodeDiffEditor
                                  currentCode={change.current_content}
                                  suggestedCode={change.suggested_content}
                                  change={change}
                                  language={getLanguageFromFile(fileChange.file)}
                                  onCodeChange={(newCode) => {
                                    console.log('Code changed:', newCode)
                                    // TODO: Handle code changes - could store in state or call parent callback
                                  }}
                                  onSave={(finalCode) => {
                                    console.log('Code saved:', finalCode)
                                    // TODO: Handle code save - could update the change object or trigger a callback
                                  }}
                                  onReset={() => {
                                    console.log('Code reset to original')
                                    // TODO: Handle reset if needed
                                  }}
                                />

                                {activeFileUpdate?.fileIndex === fileIndex && activeFileUpdate?.changeIndex === changeIndex && (
                                  <div className="mt-4 p-3 bg-muted rounded">
                                    <textarea
                                      value={updateInstructions}
                                      onChange={(e) => setUpdateInstructions(e.target.value)}
                                      placeholder="Provide specific instructions to modify this change..."
                                      className="w-full min-h-16 p-2 rounded border border-border bg-background text-sm resize-none"
                                    />
                                    <div className="flex gap-2 mt-2">
                                      <Button 
                                        size="sm" 
                                        onClick={() => handleUpdateSpecificChange(fileIndex, changeIndex)}
                                      >
                                        Update This Change
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setActiveFileUpdate(null)}
                                      >
                                        Cancel
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )
                      })}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  )
}