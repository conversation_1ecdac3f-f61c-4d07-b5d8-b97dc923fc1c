import React, { useState, useEffect } from 'react'
import { 
  <PERSON>tings, 
  AlertCircle,
  CheckCircle2,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Label } from './ui/label'
import { jiraAuthService } from '../services/auth/JiraAuthService'

interface JiraUrlConfigProps {
  onConfigured?: (baseUrl: string) => void
  onStartAuth?: () => void
}

export const JiraUrlConfig: React.FC<JiraUrlConfigProps> = ({
  onConfigured,
  onStartAuth
}) => {
  const [baseUrl, setBaseUrl] = useState('')
  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // Load stored URL if available
    const storedUrl = jiraAuthService.getBaseUrl()
    if (storedUrl && storedUrl !== 'https://yourcompany.atlassian.net') {
      setBaseUrl(storedUrl)
      setIsValid(true)
    }
  }, [])

  const validateUrl = (url: string) => {
    if (!url.trim()) {
      setError('Please enter your Jira URL')
      setIsValid(false)
      return false
    }

    // Clean up the URL
    let cleanUrl = url.trim()
    if (!cleanUrl.startsWith('http')) {
      cleanUrl = `https://${cleanUrl}`
    }

    // Check if it's an Atlassian URL
    if (!cleanUrl.includes('atlassian.net')) {
      setError('Must be an Atlassian URL (e.g., yourcompany.atlassian.net)')
      setIsValid(false)
      return false
    }

    // Basic URL validation
    try {
      new URL(cleanUrl)
    } catch {
      setError('Invalid URL format')
      setIsValid(false)
      return false
    }

    setError(null)
    setIsValid(true)
    return true
  }

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const url = e.target.value
    setBaseUrl(url)
    validateUrl(url)
  }

  const handleSaveAndConnect = async () => {
    if (!validateUrl(baseUrl)) {
      return
    }

    setIsSubmitting(true)
    try {
      // Clean up the URL
      let cleanUrl = baseUrl.trim()
      if (!cleanUrl.startsWith('http')) {
        cleanUrl = `https://${cleanUrl}`
      }

      // Save the URL
      jiraAuthService.setBaseUrl(cleanUrl)
      onConfigured?.(cleanUrl)

      // Start OAuth flow
      jiraAuthService.startAuthFlow()
      onStartAuth?.()

    } catch (error) {
      console.error('Error configuring Jira URL:', error)
      setError(error instanceof Error ? error.message : 'Failed to configure Jira URL')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleTestUrl = () => {
    if (isValid && baseUrl) {
      let cleanUrl = baseUrl.trim()
      if (!cleanUrl.startsWith('http')) {
        cleanUrl = `https://${cleanUrl}`
      }
      window.open(cleanUrl, '_blank')
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          Configure Jira
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Enter your company's Jira URL to connect your account
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="jira-url">Jira URL</Label>
          <Input
            id="jira-url"
            type="text"
            placeholder="yourcompany.atlassian.net"
            value={baseUrl}
            onChange={handleUrlChange}
            className={error ? 'border-red-500' : isValid ? 'border-green-500' : ''}
          />
          <p className="text-xs text-muted-foreground">
            Enter your company's Atlassian domain (e.g., mycompany.atlassian.net)
          </p>
        </div>

        {error && (
          <div className="flex items-center gap-2 text-red-600 text-sm">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        {isValid && !error && (
          <div className="flex items-center gap-2 text-green-600 text-sm">
            <CheckCircle2 className="h-4 w-4" />
            <span>Valid Atlassian URL</span>
          </div>
        )}

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestUrl}
            disabled={!isValid}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-3 w-3" />
            Test URL
          </Button>
          <Button
            onClick={handleSaveAndConnect}
            disabled={!isValid || isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Connecting...' : 'Save & Connect to Jira'}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Examples:</strong></p>
          <p>• mycompany.atlassian.net</p>
          <p>• https://acme-corp.atlassian.net</p>
          <p>• example-org.atlassian.net</p>
        </div>
      </CardContent>
    </Card>
  )
}