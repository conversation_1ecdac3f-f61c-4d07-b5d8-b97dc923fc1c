import React from 'react'
import { GitBranch } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'
import { useCommentStore } from '../store/useCommentStore'

export const PRContextForm: React.FC = () => {
  const { prContext, updatePRContext } = useCommentStore()

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    updatePRContext({ [field]: e.target.value })
  }

  const handleTextareaChange = (field: string) => (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updatePRContext({ [field]: e.target.value })
  }

  return (
    <Card className="mb-8">
      <CardHeader className="relative">
        <CardTitle className="flex items-center gap-3 text-2xl">
          <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
            <GitBranch className="h-6 w-6 text-primary" />
          </div>
          <span className="text-foreground">
            PR Context
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* PR URL */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">PR URL</label>
            <div className="relative overflow-hidden rounded-lg">
              <Input
                placeholder="https://bitbucket.org/..."
                value={prContext.prUrl}
                onChange={handleInputChange('prUrl')}
                className="relative z-10"
              />
            </div>
          </div>
          
          {/* Branch Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">Branch Name</label>
            <div className="relative overflow-hidden rounded-lg">
              <Input
                placeholder="feature/CMS20-1166"
                value={prContext.branchName}
                onChange={handleInputChange('branchName')}
                className="relative z-10"
              />
            </div>
          </div>
          
          {/* Ticket Description - Full Width */}
          <div className="lg:col-span-2 space-y-2">
            <label className="text-sm font-medium text-muted-foreground">Jira Ticket Context</label>
            <div className="relative overflow-hidden rounded-lg">
              <Textarea
                placeholder="Paste your complete Jira ticket here including description, acceptance criteria, and any other relevant context..."
                value={prContext.ticketDescription}
                onChange={handleTextareaChange('ticketDescription')}
                rows={6}
                className="relative z-10 resize-none"
              />
            </div>
          </div>
          
          {/* Changes Summary */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">Changes Summary</label>
            <div className="relative overflow-hidden rounded-lg">
              <Input
                placeholder="Brief summary of changes..."
                value={prContext.changesSummary}
                onChange={handleInputChange('changesSummary')}
                className="relative z-10"
              />
            </div>
          </div>
          
          {/* Working Directory */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">Working Directory</label>
            <div className="relative overflow-hidden rounded-lg">
              <Input
                placeholder="Current directory (default)"
                value={prContext.workingDirectory}
                onChange={handleInputChange('workingDirectory')}
                className="relative z-10"
              />
            </div>
          </div>
          
          {/* Worktree Path - Full Width on Mobile */}
          <div className="lg:col-span-2 space-y-2">
            <label className="text-sm font-medium text-muted-foreground">Git Worktree Path</label>
            <div className="relative overflow-hidden rounded-lg">
              <Input
                placeholder="/tmp/pr_analysis_xyz (optional)"
                value={prContext.worktreePath}
                onChange={handleInputChange('worktreePath')}
                className="relative z-10"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}