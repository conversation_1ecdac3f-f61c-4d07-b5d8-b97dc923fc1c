import React, { use<PERSON>emo, useCallback } from 'react'
import {
  FileText,
  Download,
  AlertCircle
} from 'lucide-react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card'
import { But<PERSON> } from './ui/button'
import type { StructuredReviewResult } from '../services/codeReviewer/CodeReviewerService'

// Import the new Markdown renderer
import { MarkdownRenderer } from './markdown/MarkdownRenderer'

// New simplified interface for Markdown-based reports
interface MarkdownReviewResult {
  status: string
  review_type: string
  markdown_report: string
  metadata: {
    generated_at: string
    repository: string
    pull_request?: string
    branch_name?: string
    cost_usd: number
    duration_ms: number
  }
}

// Legacy interface for backward compatibility
interface LegacyReviewResult {
  session_id: string
  review_mode: string
  branch_name: string
  pr_url?: string
  timestamp: string
  raw_review: string
  enhanced_report?: any
  jira_ticket?: any
  metadata: any
  summary: any
}

// Helper functions to detect result types
const isMarkdownReviewResult = (results: any): results is MarkdownReviewResult => {
  return results && results.status === 'success' && results.markdown_report
}

const isLegacyReviewResult = (results: any): results is LegacyReviewResult => {
  return results && results.session_id && results.raw_review
}

const isStructuredReviewResult = (results: any): results is StructuredReviewResult => {
  return results && results.structured_data && results.structured_data.executive_summary
}

interface EnhancedReviewReportProps {
  results: MarkdownReviewResult | LegacyReviewResult | StructuredReviewResult
  className?: string
  onExportReport?: (results: any) => void
}

export const EnhancedReviewReport: React.FC<EnhancedReviewReportProps> = React.memo(({
  results,
  className = '',
  onExportReport
}) => {
  // Debug logging
  console.log('🔥 ENHANCED REVIEW REPORT - Input results:', results)
  console.log('🔥 ENHANCED REVIEW REPORT - Is Markdown?', isMarkdownReviewResult(results))
  console.log('🔥 ENHANCED REVIEW REPORT - Is Legacy?', isLegacyReviewResult(results))
  console.log('🔥 ENHANCED REVIEW REPORT - Is Structured?', isStructuredReviewResult(results))

  // Extract markdown content based on result type
  const markdownContent = useMemo(() => {
    if (isMarkdownReviewResult(results)) {
      // New API format with direct markdown
      return results.markdown_report
    } else if (isLegacyReviewResult(results)) {
      // Legacy format - use raw_review content
      return results.raw_review
    } else if (isStructuredReviewResult(results)) {
      // Structured format - use raw_content
      return results.raw_content || 'No content available'
    } else {
      // Fallback
      return 'No review content available'
    }
  }, [results])

  // Extract metadata for display
  const metadata = useMemo(() => {
    if (isMarkdownReviewResult(results)) {
      return {
        generated_at: results.metadata.generated_at,
        review_type: results.review_type,
        repository: results.metadata.repository,
        branch_name: results.metadata.branch_name,
        cost_usd: results.metadata.cost_usd,
        duration_ms: results.metadata.duration_ms
      }
    } else if (isLegacyReviewResult(results)) {
      return {
        generated_at: results.timestamp,
        review_type: results.review_mode,
        repository: 'Unknown',
        branch_name: results.branch_name,
        cost_usd: 0,
        duration_ms: 0
      }
    } else if (isStructuredReviewResult(results)) {
      return {
        generated_at: results.timestamp,
        review_type: results.review_type,
        repository: 'Unknown',
        branch_name: results.metadata?.branch_name,
        cost_usd: results.metadata?.cost_usd || 0,
        duration_ms: results.metadata?.duration_ms || 0
      }
    } else {
      return {
        generated_at: new Date().toISOString(),
        review_type: 'unknown',
        repository: 'Unknown',
        branch_name: 'Unknown',
        cost_usd: 0,
        duration_ms: 0
      }
    }
  }, [results])

  // Handle export functionality
  const handleExport = useCallback(() => {
    if (onExportReport) {
      onExportReport(results)
    } else {
      // Default export behavior - download as markdown file
      const blob = new Blob([markdownContent], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `code-review-report-${metadata.branch_name || 'unknown'}-${new Date().toISOString().split('T')[0]}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }, [markdownContent, metadata, onExportReport, results])

  if (!markdownContent) {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center text-gray-500 dark:text-gray-400">
            <AlertCircle className="mr-2" size={20} />
            No review content available
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`enhanced-review-report ${className}`}>
      {/* Header with metadata */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Enhanced Code Review Report
              </CardTitle>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {metadata.review_type.toUpperCase()} • {metadata.branch_name} • {new Date(metadata.generated_at).toLocaleString()}
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              className="flex items-center gap-2"
            >
              <Download size={16} />
              Export Report
            </Button>
          </div>
        </CardHeader>
        {metadata.cost_usd > 0 && (
          <CardContent className="pt-0">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Cost: ${metadata.cost_usd.toFixed(3)} • Duration: {(metadata.duration_ms / 1000).toFixed(1)}s
            </div>
          </CardContent>
        )}
      </Card>

      {/* Markdown content */}
      <Card>
        <CardContent className="p-8">
          <MarkdownRenderer
            content={markdownContent}
            enableCopy={true}
            enableAnchors={true}
            onExport={handleExport}
            title="Code Review Report"
          />
        </CardContent>
      </Card>
    </div>
  )
})

EnhancedReviewReport.displayName = 'EnhancedReviewReport'
