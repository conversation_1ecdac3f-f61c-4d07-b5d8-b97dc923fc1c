import React, { memo, useMemo } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import rehypeRaw from 'rehype-raw'
import { Button } from '../ui/button'
import { Download, ExternalLink } from 'lucide-react'

// Import custom components
import { CodeBlock } from './CodeBlock'
import { MarkdownTable } from './MarkdownTable'
import { MarkdownHeader } from './MarkdownHeader'
import { EmojiRenderer_Static } from './EmojiRenderer'
import { MermaidRenderer, isMermaidCode } from './MermaidRenderer'

// Import styles
import './styles/markdown.css'
import 'highlight.js/styles/github-dark.css'

interface MarkdownRendererProps {
  content: string
  className?: string
  enableCopy?: boolean
  enableAnchors?: boolean
  onExport?: () => void
  title?: string
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = memo(({
  content,
  className = '',
  enableCopy = true,
  enableAnchors = true,
  onExport,
  title = 'Code Review Report'
}) => {
  // Memoize the markdown components to prevent unnecessary re-renders
  const components = useMemo(() => ({
    // Headers with anchor support
    h1: (props: any) => <MarkdownHeader level={1} enableAnchors={enableAnchors} {...props} />,
    h2: (props: any) => <MarkdownHeader level={2} enableAnchors={enableAnchors} {...props} />,
    h3: (props: any) => <MarkdownHeader level={3} enableAnchors={enableAnchors} {...props} />,
    h4: (props: any) => <MarkdownHeader level={4} enableAnchors={enableAnchors} {...props} />,
    h5: (props: any) => <MarkdownHeader level={5} enableAnchors={enableAnchors} {...props} />,
    h6: (props: any) => <MarkdownHeader level={6} enableAnchors={enableAnchors} {...props} />,
    
    // Code blocks with syntax highlighting and copy functionality
    code: (props: any) => {
      const { children, className, inline, ...rest } = props
      const match = /language-(\w+)/.exec(className || '')
      
      // Convert children to string properly
      const codeContent = React.useMemo(() => {
        const extractText = (node: any): string => {
          // Handle simple types
          if (typeof node === 'string') {
            return node
          }
          if (typeof node === 'number') {
            return String(node)
          }
          
          // Handle arrays (multiple children)
          if (Array.isArray(node)) {
            return node.map(extractText).join('')
          }
          
          // Handle objects - this is where the [object Object] comes from
          if (node && typeof node === 'object') {
            // Handle React elements (has type, props, etc.)
            if (node.type && node.props) {
              if (node.props.children) {
                return extractText(node.props.children)
              }
              return ''
            }
            
            // Handle plain text nodes from markdown parser
            if (node.value && typeof node.value === 'string') {
              return node.value
            }
            
            // Handle DOM-like nodes
            if (node.nodeValue && typeof node.nodeValue === 'string') {
              return node.nodeValue
            }
            if (node.textContent && typeof node.textContent === 'string') {
              return node.textContent
            }
            if (node.data && typeof node.data === 'string') {
              return node.data
            }
            
            // Handle children property
            if (node.children) {
              return extractText(node.children)
            }
            
            // If it's a plain object, don't try to stringify - just return empty
            console.warn('Unknown object type in code block:', node)
            return ''
          }
          
          return String(node || '')
        }
        
        return extractText(children)
      }, [children])
      
      if (!inline && match) {
        const language = match[1]
        const cleanCode = codeContent.replace(/\n$/, '')
        
        // Check if this is a Mermaid diagram
        console.log('🐛 Debug CodeBlock:', { language, codePreview: cleanCode.substring(0, 100), isMermaid: isMermaidCode(language, cleanCode) })
        if (isMermaidCode(language, cleanCode)) {
          console.log('✅ Rendering as Mermaid diagram')
          return (
            <MermaidRenderer
              code={cleanCode}
              enableCopy={enableCopy}
            />
          )
        }
        
        return (
          <CodeBlock
            language={language}
            code={cleanCode}
            enableCopy={enableCopy}
            {...rest}
          />
        )
      }
      
      // Inline code
      return (
        <code className="inline-code" {...rest}>
          {codeContent}
        </code>
      )
    },
    
    // Tables with responsive design
    table: (props: any) => <MarkdownTable {...props} />,
    
    // Links with external link indicators
    a: (props: any) => {
      const { href, children, ...rest } = props
      const isExternal = href && (href.startsWith('http') || href.startsWith('https'))
      const isAnchor = href && href.startsWith('#')
      
      return (
        <a
          href={href}
          className={`markdown-link ${isExternal ? 'external-link' : ''} ${isAnchor ? 'anchor-link' : ''}`}
          target={isExternal ? '_blank' : undefined}
          rel={isExternal ? 'noopener noreferrer' : undefined}
          {...rest}
        >
          {children}
          {isExternal && <ExternalLink className="inline-icon" size={12} />}
        </a>
      )
    },
    
    // Blockquotes with styling
    blockquote: (props: any) => (
      <blockquote className="markdown-blockquote" {...props} />
    ),
    
    // Lists with proper spacing
    ul: (props: any) => <ul className="markdown-list" {...props} />,
    ol: (props: any) => <ol className="markdown-list ordered" {...props} />,
    li: (props: any) => <li className="markdown-list-item" {...props} />,
    
    // Paragraphs with proper spacing
    p: (props: any) => <p className="markdown-paragraph" {...props} />,
    
    // Horizontal rules
    hr: (props: any) => <hr className="markdown-divider" {...props} />,
    
    // Strong and emphasis
    strong: (props: any) => <strong className="markdown-strong" {...props} />,
    em: (props: any) => <em className="markdown-emphasis" {...props} />,
    
    // Images with responsive design
    img: (props: any) => (
      <img className="markdown-image" loading="lazy" {...props} />
    ),
  }), [enableCopy, enableAnchors])

  // Memoize the remark and rehype plugins
  const remarkPlugins = useMemo(() => [remarkGfm], [])
  const rehypePlugins = useMemo(() => [rehypeHighlight, rehypeRaw], [])

  // Process content to handle emojis
  const processedContent = useMemo(() => {
    return EmojiRenderer_Static.processContent(content)
  }, [content])

  return (
    <div className={`markdown-renderer ${className}`}>
      {/* Header with export functionality */}
      {onExport && (
        <div className="markdown-header-controls">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              className="flex items-center gap-2"
            >
              <Download size={16} />
              Export MD
            </Button>
          </div>
        </div>
      )}
      
      {/* Main markdown content */}
      <div className="markdown-content">
        <ReactMarkdown
          remarkPlugins={remarkPlugins}
          rehypePlugins={rehypePlugins}
          components={components}
          skipHtml={false}
        >
          {processedContent}
        </ReactMarkdown>
      </div>
    </div>
  )
})

MarkdownRenderer.displayName = 'MarkdownRenderer'
