import React from 'react'
// import { ChevronUp, ChevronDown, ArrowUpDown } from 'lucide-react' // Unused for now

interface MarkdownTableProps {
  children: React.ReactNode
  className?: string
}

// Interfaces for future sorting functionality - currently unused
// interface TableHeaderProps {
//   children: React.ReactNode
//   onClick?: () => void
//   sortDirection?: 'asc' | 'desc' | null
// }

// interface TableData {
//   headers: string[]
//   rows: string[][]
// }

// TableHeader component - currently unused but kept for future sorting functionality
// const TableHeader: React.FC<TableHeaderProps> = ({
//   children,
//   onClick,
//   sortDirection
// }) => (
//   <th
//     className={`markdown-table-header ${onClick ? 'sortable' : ''}`}
//     onClick={onClick}
//   >
//     <div className="flex items-center justify-between">
//       <span>{children}</span>
//       {onClick && (
//         <span className="sort-icon">
//           {sortDirection === 'asc' ? (
//             <ChevronUp size={14} />
//           ) : sortDirection === 'desc' ? (
//             <ChevronDown size={14} />
//           ) : (
//             <ArrowUpDown size={14} />
//           )}
//         </span>
//       )}
//     </div>
//   </th>
// )

export const MarkdownTable: React.FC<MarkdownTableProps> = ({
  children,
  className = ''
}) => {
  // Sorting functionality disabled for now - can be enabled later
  // const [sortColumn, setSortColumn] = useState<number | null>(null)
  // const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')

  // Extract table data from children for sorting - disabled for now
  // const tableData = useMemo((): TableData | null => {
  //   try {
  //     // This is a simplified extraction - in a real implementation,
  //     // you'd want more robust parsing of the table structure
  //     const tableElement = React.Children.toArray(children)[0] as React.ReactElement
  //     if (!tableElement || tableElement.type !== 'tbody') return null
  //
  //     // For now, return null to disable sorting until we implement proper parsing
  //     return null
  //   } catch {
  //     return null
  //   }
  // }, [children])

  // handleSort function - currently unused but kept for future sorting functionality
  // const handleSort = (columnIndex: number) => {
  //   if (!tableData) return
  //
  //   if (sortColumn === columnIndex) {
  //     setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
  //   } else {
  //     setSortColumn(columnIndex)
  //     setSortDirection('asc')
  //   }
  // }

  // For now, render the table without sorting functionality
  // In a full implementation, you'd parse and sort the data
  return (
    <div className={`markdown-table-container ${className}`}>
      <div className="table-wrapper">
        <table className="markdown-table">
          {children}
        </table>
      </div>
    </div>
  )
}

// Enhanced table components for better control
export const MarkdownTableHead: React.FC<{ children: React.ReactNode }> = ({ 
  children 
}) => (
  <thead className="markdown-table-head">
    {children}
  </thead>
)

export const MarkdownTableBody: React.FC<{ children: React.ReactNode }> = ({ 
  children 
}) => (
  <tbody className="markdown-table-body">
    {children}
  </tbody>
)

export const MarkdownTableRow: React.FC<{ 
  children: React.ReactNode
  isHeader?: boolean
}> = ({ 
  children, 
  isHeader = false 
}) => (
  <tr className={`markdown-table-row ${isHeader ? 'header-row' : ''}`}>
    {children}
  </tr>
)

export const MarkdownTableCell: React.FC<{ 
  children: React.ReactNode
  isHeader?: boolean
  align?: 'left' | 'center' | 'right'
}> = ({ 
  children, 
  isHeader = false,
  align = 'left'
}) => {
  const Tag = isHeader ? 'th' : 'td'
  
  return (
    <Tag className={`markdown-table-cell ${align}-align`}>
      {children}
    </Tag>
  )
}

// Utility function to parse table alignment from markdown
export const parseTableAlignment = (alignRow: string): ('left' | 'center' | 'right')[] => {
  return alignRow.split('|').map(cell => {
    const trimmed = cell.trim()
    if (trimmed.startsWith(':') && trimmed.endsWith(':')) return 'center'
    if (trimmed.endsWith(':')) return 'right'
    return 'left'
  }).filter(Boolean)
}

// Utility function to check if content looks like a table
export const isTableContent = (content: string): boolean => {
  const lines = content.trim().split('\n')
  if (lines.length < 2) return false
  
  // Check if we have pipe-separated content
  const hasPipes = lines.every(line => line.includes('|'))
  if (!hasPipes) return false
  
  // Check if second line looks like alignment row
  const secondLine = lines[1].trim()
  const isAlignmentRow = /^[\s]*\|?[\s]*:?-+:?[\s]*(\|[\s]*:?-+:?[\s]*)*\|?[\s]*$/.test(secondLine)
  
  return isAlignmentRow
}
