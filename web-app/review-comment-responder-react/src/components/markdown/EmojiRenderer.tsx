import React from 'react'

// Common emoji mappings used in code review reports
const EMOJI_MAP: Record<string, string> = {
  // Status indicators
  ':white_check_mark:': '✅',
  ':x:': '❌',
  ':warning:': '⚠️',
  ':heavy_check_mark:': '✅',
  ':cross_mark:': '❌',
  ':exclamation:': '❗',
  ':question:': '❓',
  
  // Review symbols
  ':bug:': '🐛',
  ':fire:': '🔥',
  ':rocket:': '🚀',
  ':sparkles:': '✨',
  ':zap:': '⚡',
  ':boom:': '💥',
  ':construction:': '🚧',
  ':wrench:': '🔧',
  ':hammer:': '🔨',
  ':gear:': '⚙️',
  ':shield:': '🛡️',
  ':lock:': '🔒',
  ':key:': '🔑',
  
  // Categories
  ':ticket:': '🎫',
  ':chart_with_upwards_trend:': '📈',
  ':bar_chart:': '📊',
  ':clipboard:': '📋',
  ':memo:': '📝',
  ':file_folder:': '📁',
  ':page_facing_up:': '📄',
  ':page_with_curl:': '📃',
  ':bookmark_tabs:': '📑',
  ':card_index:': '📇',
  
  // Actions
  ':arrow_right:': '➡️',
  ':arrow_left:': '⬅️',
  ':arrow_up:': '⬆️',
  ':arrow_down:': '⬇️',
  ':point_right:': '👉',
  ':point_left:': '👈',
  ':point_up:': '👆',
  ':point_down:': '👇',
  
  // Emotions/Reactions
  ':thumbsup:': '👍',
  ':thumbsdown:': '👎',
  ':clap:': '👏',
  ':raised_hands:': '🙌',
  ':ok_hand:': '👌',
  ':muscle:': '💪',
  ':brain:': '🧠',
  ':eyes:': '👀',
  ':thinking:': '🤔',
  
  // Objects
  ':computer:': '💻',
  ':keyboard:': '⌨️',
  ':mouse:': '🖱️',
  ':desktop_computer:': '🖥️',
  ':laptop:': '💻',
  ':phone:': '📱',
  ':iphone:': '📱',
  ':android:': '🤖',
  
  // Time
  ':clock1:': '🕐',
  ':clock2:': '🕑',
  ':clock3:': '🕒',
  ':clock4:': '🕓',
  ':clock5:': '🕔',
  ':clock6:': '🕕',
  ':clock7:': '🕖',
  ':clock8:': '🕗',
  ':clock9:': '🕘',
  ':clock10:': '🕙',
  ':clock11:': '🕚',
  ':clock12:': '🕛',
  ':hourglass:': '⏳',
  ':stopwatch:': '⏱️',
  ':timer_clock:': '⏲️',
  
  // Numbers
  ':zero:': '0️⃣',
  ':one:': '1️⃣',
  ':two:': '2️⃣',
  ':three:': '3️⃣',
  ':four:': '4️⃣',
  ':five:': '5️⃣',
  ':six:': '6️⃣',
  ':seven:': '7️⃣',
  ':eight:': '8️⃣',
  ':nine:': '9️⃣',
  ':ten:': '🔟',
  
  // Symbols
  ':heavy_plus_sign:': '➕',
  ':heavy_minus_sign:': '➖',
  ':heavy_multiplication_x:': '✖️',
  ':heavy_division_sign:': '➗',
  ':infinity:': '♾️',
  ':hash:': '#️⃣',
  ':asterisk:': '*️⃣',
  
  // Directional
  ':arrow_forward:': '▶️',
  ':arrow_backward:': '◀️',
  ':arrow_up_small:': '🔼',
  ':arrow_down_small:': '🔽',
  ':fast_forward:': '⏩',
  ':rewind:': '⏪',
  ':arrow_double_up:': '⏫',
  ':arrow_double_down:': '⏬',
  
  // Shapes
  ':red_circle:': '🔴',
  ':orange_circle:': '🟠',
  ':yellow_circle:': '🟡',
  ':green_circle:': '🟢',
  ':blue_circle:': '🔵',
  ':purple_circle:': '🟣',
  ':brown_circle:': '🟤',
  ':black_circle:': '⚫',
  ':white_circle:': '⚪',
  ':red_square:': '🟥',
  ':orange_square:': '🟧',
  ':yellow_square:': '🟨',
  ':green_square:': '🟩',
  ':blue_square:': '🟦',
  ':purple_square:': '🟪',
  ':brown_square:': '🟫',
  ':black_large_square:': '⬛',
  ':white_large_square:': '⬜'
}

interface EmojiRendererProps {
  children: React.ReactNode
  className?: string
}

export const EmojiRenderer: React.FC<EmojiRendererProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <span className={`emoji-renderer ${className}`}>
      {children}
    </span>
  )
}

// Static methods for processing emoji content
export const EmojiRenderer_Static = {
  // Process content to replace emoji codes with actual emojis
  processContent: (content: string): string => {
    let processedContent = content
    
    // Replace emoji codes with actual emojis
    Object.entries(EMOJI_MAP).forEach(([code, emoji]) => {
      const regex = new RegExp(code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')
      processedContent = processedContent.replace(regex, emoji)
    })
    
    return processedContent
  },

  // Extract all emojis from content
  extractEmojis: (content: string): string[] => {
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu
    return content.match(emojiRegex) || []
  },

  // Check if content contains emojis
  hasEmojis: (content: string): boolean => {
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu
    return emojiRegex.test(content)
  },

  // Get emoji by code
  getEmoji: (code: string): string | null => {
    return EMOJI_MAP[code] || null
  },

  // Get all available emoji codes
  getAvailableCodes: (): string[] => {
    return Object.keys(EMOJI_MAP)
  },

  // Add custom emoji mapping
  addEmojiMapping: (code: string, emoji: string): void => {
    EMOJI_MAP[code] = emoji
  },

  // Remove emoji mapping
  removeEmojiMapping: (code: string): void => {
    delete EMOJI_MAP[code]
  }
}

// Export static methods as properties of the component
Object.assign(EmojiRenderer, EmojiRenderer_Static)

// Component for rendering individual emojis with accessibility
export const Emoji: React.FC<{
  code?: string
  emoji?: string
  label?: string
  className?: string
}> = ({ 
  code, 
  emoji, 
  label, 
  className = '' 
}) => {
  const displayEmoji = emoji || (code ? EmojiRenderer_Static.getEmoji(code) : null)
  
  if (!displayEmoji) return null
  
  return (
    <span 
      className={`emoji ${className}`}
      role="img"
      aria-label={label || code || 'emoji'}
    >
      {displayEmoji}
    </span>
  )
}

export default EmojiRenderer
