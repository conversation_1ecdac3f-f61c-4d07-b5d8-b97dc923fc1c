import React, { useState, useCallback, useRef } from 'react'
import { Co<PERSON>, Check } from 'lucide-react'
import { Button } from '../ui/button'
import hljs from 'highlight.js'

interface CodeBlockProps {
  language: string
  code: string
  enableCopy?: boolean
  className?: string
}

export const CodeBlock: React.FC<CodeBlockProps> = ({
  language,
  code,
  enableCopy = true,
  className = ''
}) => {
  const [copied, setCopied] = useState(false)
  const codeRef = useRef<HTMLElement>(null)

  // Ensure code is a string and handle object conversion
  const codeString = React.useMemo(() => {
    if (typeof code === 'string') {
      return code
    }
    if (typeof code === 'number') {
      return String(code)
    }
    if (typeof code === 'object' && code !== null) {
      const codeObj = code as any;
      // Don't try to JSON.stringify React elements or DOM nodes
      if (codeObj.toString && typeof codeObj.toString === 'function') {
        const stringified = codeObj.toString()
        // Avoid [object Object] - return empty string instead
        if (stringified === '[object Object]') {
          return ''
        }
        return stringified
      }
      // Only try JSON.stringify for plain objects
      try {
        if (codeObj.constructor === Object) {
          return JSON.stringify(codeObj, null, 2)
        }
      } catch (e) {
        // Ignore JSON errors
      }
      return ''
    }
    return String(code || '')
  }, [code])

  // Apply syntax highlighting
  const highlightedCode = React.useMemo(() => {
    if (!codeString.trim()) return codeString

    try {
      // Check if the language is supported by highlight.js
      const validLanguage = hljs.getLanguage(language) ? language : 'plaintext'
      const result = hljs.highlight(codeString, { language: validLanguage })
      return result.value
    } catch (error) {
      console.warn('Failed to highlight code:', error)
      return codeString
    }
  }, [codeString, language])



  const handleCopy = useCallback(async () => {
    if (!enableCopy) return

    try {
      await navigator.clipboard.writeText(codeString)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy code:', err)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = codeString
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      } catch (fallbackErr) {
        console.error('Fallback copy failed:', fallbackErr)
      }
      document.body.removeChild(textArea)
    }
  }, [codeString, enableCopy])

  // Get language display name
  const getLanguageDisplayName = (lang: string): string => {
    const languageMap: Record<string, string> = {
      'js': 'JavaScript',
      'jsx': 'JSX',
      'ts': 'TypeScript',
      'tsx': 'TSX',
      'py': 'Python',
      'python': 'Python',
      'java': 'Java',
      'cpp': 'C++',
      'c': 'C',
      'cs': 'C#',
      'php': 'PHP',
      'rb': 'Ruby',
      'go': 'Go',
      'rs': 'Rust',
      'sh': 'Shell',
      'bash': 'Bash',
      'zsh': 'Zsh',
      'sql': 'SQL',
      'json': 'JSON',
      'xml': 'XML',
      'html': 'HTML',
      'css': 'CSS',
      'scss': 'SCSS',
      'sass': 'Sass',
      'yaml': 'YAML',
      'yml': 'YAML',
      'toml': 'TOML',
      'ini': 'INI',
      'dockerfile': 'Dockerfile',
      'md': 'Markdown',
      'markdown': 'Markdown'
    }
    
    return languageMap[lang.toLowerCase()] || lang.toUpperCase()
  }

  return (
    <div className={`code-block-container ${className}`}>
      {/* Code block header */}
      <div className="code-block-header">
        <div className="flex items-center justify-between">
          <span className="code-block-language">
            {getLanguageDisplayName(language)}
          </span>
          
          {enableCopy && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="code-block-copy-button"
              disabled={copied}
            >
              {copied ? (
                <>
                  <Check size={14} />
                  <span className="ml-1">Copied!</span>
                </>
              ) : (
                <>
                  <Copy size={14} />
                  <span className="ml-1">Copy</span>
                </>
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Code content */}
      <div className="code-block-content">
        <pre className={`language-${language}`}>
          <code
            ref={codeRef}
            className={`language-${language} hljs`}
            dangerouslySetInnerHTML={{ __html: highlightedCode }}
          />
        </pre>
      </div>
    </div>
  )
}

// Utility function to detect if content is likely code
export const isCodeContent = (content: string): boolean => {
  const codeIndicators = [
    /^[\s]*[{}\[\]();]/m,  // Brackets, braces, parentheses
    /^[\s]*[a-zA-Z_$][a-zA-Z0-9_$]*\s*[=:]/m,  // Variable assignments
    /^[\s]*(?:function|class|interface|type|const|let|var|def|public|private|protected)/m,  // Keywords
    /^[\s]*(?:import|export|from|require)/m,  // Import statements
    /^[\s]*(?:if|else|for|while|switch|case|try|catch|finally)/m,  // Control flow
    /^[\s]*\/\/|^[\s]*\/\*|^[\s]*#|^[\s]*<!--/m,  // Comments
    /^[\s]*<[a-zA-Z][^>]*>/m,  // HTML/XML tags
    /^[\s]*[.#][a-zA-Z-_]/m,  // CSS selectors
  ]
  
  return codeIndicators.some(pattern => pattern.test(content))
}

// Utility function to guess language from content
export const guessLanguage = (content: string): string => {
  // Check for specific patterns
  if (/<[a-zA-Z][^>]*>/.test(content)) {
    if (/className|jsx|tsx/.test(content)) return 'jsx'
    if (/<script|<style/.test(content)) return 'html'
    return 'xml'
  }
  
  if (/^[\s]*(?:function|const|let|var|=>)/.test(content)) {
    if (/interface|type|:.*=/.test(content)) return 'typescript'
    return 'javascript'
  }
  
  if (/^[\s]*(?:def|class|import|from)/.test(content)) return 'python'
  if (/^[\s]*(?:public|private|class|interface)/.test(content)) return 'java'
  if (/^[\s]*(?:#include|int main|std::)/.test(content)) return 'cpp'
  if (/^[\s]*(?:SELECT|INSERT|UPDATE|DELETE)/i.test(content)) return 'sql'
  if (/^[\s]*[.#][a-zA-Z-_]/.test(content)) return 'css'
  if (/^[\s]*\$/.test(content)) return 'bash'
  
  return 'text'
}
