/* Markdown Renderer Styles */
.markdown-renderer {
  @apply w-full max-w-none;
}

.markdown-header-controls {
  @apply border-b border-gray-200 dark:border-gray-700 pb-4 mb-6;
}

.markdown-content {
  @apply max-w-none text-gray-100 dark:text-gray-100;
  line-height: 1.7;
}

/* Ensure all text elements use light colors in dark mode */
.markdown-content p,
.markdown-content span,
.markdown-content div,
.markdown-content li,
.markdown-content td,
.markdown-content th {
  @apply text-gray-100 dark:text-gray-100;
}

/* Strong/bold text */
.markdown-content strong,
.markdown-content b {
  @apply text-white dark:text-white font-semibold;
}

/* Emphasis/italic text */
.markdown-content em,
.markdown-content i {
  @apply text-gray-200 dark:text-gray-200;
}

/* Headers */
.markdown-header {
  @apply relative;
}

.markdown-header:hover .header-anchor {
  opacity: 1;
}

.header-content-wrapper {
  @apply flex items-center gap-2;
}

.header-anchor {
  @apply opacity-0 transition-opacity duration-200;
  @apply text-gray-300 hover:text-gray-100 dark:text-gray-300 dark:hover:text-gray-100;
  @apply no-underline;
}

/* Header text styling */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  @apply text-white dark:text-white font-bold;
}

.header-text {
  @apply flex-1;
}

/* Links */
.markdown-link {
  @apply text-blue-400 dark:text-blue-400 hover:text-blue-200 dark:hover:text-blue-200;
  @apply underline decoration-1 underline-offset-2;
  @apply transition-colors duration-200;
}

.markdown-link.external-link {
  @apply inline-flex items-center gap-1;
}

.markdown-link.anchor-link {
  @apply text-gray-300 dark:text-gray-300;
}

.inline-icon {
  @apply inline-block ml-1;
}

/* Code Blocks */
.code-block-container {
  @apply my-4 rounded-lg border border-gray-700 dark:border-gray-700 overflow-hidden;
  @apply bg-gray-950 dark:bg-gray-950;
}

.code-block-header {
  @apply px-4 py-2 bg-gray-900 dark:bg-gray-900 border-b border-gray-700 dark:border-gray-700;
}

.code-block-language {
  @apply text-sm font-medium text-gray-300 dark:text-gray-300;
}

.code-block-copy-button {
  @apply text-xs;
}

.code-block-content {
  @apply overflow-x-auto;
  @apply bg-gray-950 dark:bg-gray-950;
}

.code-block-content pre {
  @apply m-0 p-4 bg-transparent;
  @apply text-sm leading-relaxed;
  @apply text-gray-200 dark:text-gray-200;
}

.code-block-content code {
  @apply bg-transparent text-gray-200 dark:text-gray-200;
  @apply font-mono;
  font-family: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
}

/* Inline Code */
.inline-code {
  @apply px-1.5 py-0.5 rounded text-sm;
  @apply bg-gray-800 dark:bg-gray-800;
  @apply text-gray-200 dark:text-gray-200;
  @apply font-mono;
}

/* Tables */
.markdown-table-container {
  @apply my-6 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700;
}

.table-wrapper {
  @apply overflow-x-auto;
}

.markdown-table {
  @apply w-full border-collapse;
  @apply bg-white dark:bg-gray-900;
}

.markdown-table-header {
  @apply px-4 py-3 text-left text-sm font-semibold;
  @apply text-gray-100 dark:text-gray-100;
  @apply bg-gray-800 dark:bg-gray-800;
  @apply border-b border-gray-600 dark:border-gray-600;
}

.markdown-table-header.sortable {
  @apply cursor-pointer hover:bg-gray-700 dark:hover:bg-gray-700;
  @apply transition-colors duration-200;
}

.sort-icon {
  @apply text-gray-400 dark:text-gray-500;
}

.markdown-table-cell {
  @apply px-4 py-3 text-sm;
  @apply text-gray-200 dark:text-gray-200;
  @apply border-b border-gray-600 dark:border-gray-600;
}

.markdown-table-cell.left-align {
  @apply text-left;
}

.markdown-table-cell.center-align {
  @apply text-center;
}

.markdown-table-cell.right-align {
  @apply text-right;
}

.markdown-table-row:last-child .markdown-table-cell {
  @apply border-b-0;
}

.markdown-table-row:hover {
  @apply bg-gray-50 dark:bg-gray-800;
}

/* Lists */
.markdown-list {
  @apply my-4 space-y-2;
}

.markdown-list.ordered {
  @apply list-decimal;
}

.markdown-list:not(.ordered) {
  @apply list-disc;
}

.markdown-list-item {
  @apply ml-6 text-gray-100 dark:text-gray-100;
}

/* List markers */
.markdown-content ul {
  @apply text-gray-100 dark:text-gray-100;
}

.markdown-content ol {
  @apply text-gray-100 dark:text-gray-100;
}

.markdown-content li {
  @apply text-gray-100 dark:text-gray-100;
}

/* Paragraphs */
.markdown-paragraph {
  @apply my-4 leading-relaxed;
  @apply text-gray-100 dark:text-gray-100;
}

/* Blockquotes */
.markdown-blockquote {
  @apply my-6 pl-4 border-l-4 border-gray-500 dark:border-gray-500;
  @apply italic text-gray-200 dark:text-gray-200;
  @apply bg-gray-800 dark:bg-gray-800 p-4 rounded-r-lg;
}

/* Horizontal Rules */
.markdown-divider {
  @apply my-8 border-0 border-t border-gray-200 dark:border-gray-700;
}

/* Text Formatting */
.markdown-strong {
  @apply font-semibold text-white dark:text-white;
}

.markdown-emphasis {
  @apply italic;
}

/* Images */
.markdown-image {
  @apply max-w-full h-auto rounded-lg;
  @apply shadow-sm border border-gray-200 dark:border-gray-700;
}

/* Emojis */
.emoji-renderer {
  @apply inline-block;
}

.emoji {
  @apply inline-block text-base leading-none;
  @apply select-none;
  filter: brightness(1.1) contrast(1.1); /* Make emojis more vibrant in dark mode */
}

/* Special markdown elements for better visibility */
.markdown-content hr {
  @apply border-gray-600 dark:border-gray-600;
}

/* Ensure all text content is visible */
.markdown-content * {
  @apply text-gray-100 dark:text-gray-100;
}

/* Override for specific elements that should be white */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6,
.markdown-content strong,
.markdown-content b {
  @apply text-white dark:text-white !important;
}

/* Table of Contents */
.table-of-contents {
  @apply bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-6;
  @apply border border-gray-200 dark:border-gray-700;
}

.toc-title {
  @apply text-lg font-semibold mb-3;
  @apply text-white dark:text-white;
}

.toc-list {
  @apply space-y-1;
}

.toc-item {
  @apply text-sm;
}

.toc-level-1 {
  @apply ml-0 font-medium;
}

.toc-level-2 {
  @apply ml-4;
}

.toc-level-3 {
  @apply ml-8;
}

.toc-level-4 {
  @apply ml-12;
}

.toc-level-5 {
  @apply ml-16;
}

.toc-level-6 {
  @apply ml-20;
}

.toc-link {
  @apply text-gray-300 dark:text-gray-300;
  @apply hover:text-gray-100 dark:hover:text-gray-100;
  @apply no-underline transition-colors duration-200;
}

.toc-link:hover {
  @apply underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .markdown-table-container {
    @apply text-xs;
  }
  
  .markdown-table-header,
  .markdown-table-cell {
    @apply px-2 py-2;
  }
  
  .code-block-content {
    @apply text-xs;
  }
  
  .header-anchor {
    @apply opacity-100;
  }
}

/* Syntax Highlighting Overrides for Dark Theme */
.hljs {
  @apply bg-gray-950 dark:bg-gray-950 !important;
  @apply text-gray-200 dark:text-gray-200 !important;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  @apply text-purple-400 dark:text-purple-400 !important;
}

.hljs-string,
.hljs-title,
.hljs-name,
.hljs-type,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-addition,
.hljs-variable,
.hljs-template-tag,
.hljs-template-variable {
  @apply text-green-400 dark:text-green-400 !important;
}

.hljs-comment,
.hljs-quote,
.hljs-deletion,
.hljs-meta {
  @apply text-gray-500 dark:text-gray-500 !important;
}

.hljs-keyword.hljs-selector-tag {
  @apply text-blue-400 dark:text-blue-400 !important;
}

.hljs-emphasis {
  @apply italic;
}

.hljs-strong {
  @apply font-bold;
}

/* Print Styles */
@media print {
  .markdown-renderer {
    @apply text-black bg-white;
  }
  
  .code-block-container {
    @apply border border-gray-400;
  }
  
  .code-block-header {
    @apply bg-gray-100;
  }
  
  .markdown-table {
    @apply border border-gray-400;
  }
  
  .markdown-table-header,
  .markdown-table-cell {
    @apply border border-gray-400;
  }
  
  .header-anchor {
    @apply hidden;
  }
  
  .code-block-copy-button {
    @apply hidden;
  }
}
