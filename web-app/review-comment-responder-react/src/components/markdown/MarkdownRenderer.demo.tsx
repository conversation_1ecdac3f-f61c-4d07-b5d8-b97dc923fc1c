import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './MarkdownRenderer'

/**
 * Demo component to showcase the MarkdownRenderer functionality
 * This demonstrates that the component can render the CLI-style markdown reports
 */
export const MarkdownRendererDemo: React.FC = () => {
  const sampleMarkdown = `# Enhanced Code Review Report mit Jira Integration

**Generated:** 2025-07-28 10:30:00  
**Review Type:** COMPREHENSIVE_WITH_AC  
**Repository:** /Users/<USER>/.devtools  
**Working Directory:** /Users/<USER>/.devtools/test  
**Pull Request:** https://github.com/test/repo/pull/123  
**Branch:** feature/test-branch  

---

## 🎫 Jira Ticket Information

- **Ticket ID:** [TEST-123](https://example.com/TEST-123)
- **Summary:** Test Feature Implementation
- **Type:** Story
- **Status:** In Progress
- **Priority:** High
- **Assignee:** Test User

### 📋 Acceptance Criteria (3 Items)
1. User can authenticate with valid credentials
2. System handles invalid credentials gracefully
3. Error messages are user-friendly

---

## 📊 Change Summary

\`\`\`
apps/service/file.ts     | 25 +++++
apps/auth/handler.ts     | 15 +++
apps/ui/component.tsx    | 8 +-
3 files changed, 48 insertions(+), 1 deletion(-)
\`\`\`

**Geänderte Dateien:** 3

- \`apps/service/file.ts\`
- \`apps/auth/handler.ts\`
- \`apps/ui/component.tsx\`

---

## 🎯 Enhanced Review Results

### Phase 1: 📋 ACCEPTANCE CRITERIA ANALYSIS

#### ✅ AC 1: User Authentication
**Status:** ERFÜLLT
**Implementiert:** Authentication logic correctly validates user credentials
**Code-Stelle:** \`apps/auth/handler.ts:15\`
\`\`\`typescript
const validateUser = (credentials: UserCredentials) => {
  return authService.validate(credentials)
}
\`\`\`

#### ❌ AC 2: Error Handling
**Status:** NICHT ERFÜLLT
**Problem:** Missing proper error handling for edge cases
**Code-Stelle:** \`apps/service/file.ts:42\`
\`\`\`typescript
// ❌ No error handling
const result = await apiCall()
return result.data
\`\`\`

#### ⚠️ AC 3: Error Messages
**Status:** TEILWEISE ERFÜLLT
**Implementiert:** Basic error messages present but not user-friendly
**Verbesserung:** Use more descriptive error messages

### Phase 2: 🔍 DETAILLIERTE CODE QUALITY & BUG ANALYSIS

#### 🐛 Bug Detection Results

##### 🚨 Critical Bugs (Must Fix)

###### Missing Error Handling for API Calls
**Location:** \`apps/service/file.ts:42\`
**Severity:** Critical
**Problem:** Unhandled API failures can crash the application
**Fix:**
\`\`\`typescript
try {
  const result = await apiCall()
  return result.data
} catch (error) {
  logger.error('API call failed', error)
  throw new ServiceError('Failed to fetch data', 503)
}
\`\`\`

#### Code Quality Analysis

| File | Issues | Severity | Type |
|------|--------|----------|------|
| file.ts | 2 | High | Error Handling |
| handler.ts | 1 | Medium | Code Style |
| component.tsx | 0 | Low | Clean |

### 🚨 CRITICAL (Must Fix Before Merge)
- [ ] Add error handling for API calls in \`file.ts\`
- [ ] Implement proper logging mechanism
- [ ] Add input validation for user credentials

### ⚠️ IMPORTANT (Should Fix)
- [ ] Improve error message clarity
- [ ] Add unit tests for authentication logic
- [ ] Consider using TypeScript strict mode

### 💡 SUGGESTIONS (Nice to Have)
- [ ] Extract authentication logic to separate service
- [ ] Add JSDoc comments for better documentation
- [ ] Consider using a state management library

---

## 📈 Report Information

- **Generated by:** Enhanced Claude Code PR Reviewer
- **Claude Code Version:** 1.0.59 (Claude Code)
- **Jira Integration:** ✅ Enabled
- **Ticket Loaded:** ✅ Yes
- **AC Analysis:** ✅ Included
- **Working Directory:** \`/Users/<USER>/.devtools/test\`

---

## 🔧 Next Steps

### If AC Issues Found:
1. **Address Critical AC Failures** before merging
2. **Clarify Requirements** with Product Owner if needed
3. **Update Implementation** to match AC
4. **Re-run Review** after fixes

### Standard Process:
1. Review and address feedback
2. Update tests as needed
3. Update documentation
4. Request re-review if major changes

---

*Dieser Enhanced Review Report wurde automatisch mit Jira Integration generiert. Bei Fragen zu Requirements kontaktieren Sie den Product Owner oder Business Analyst.*`

  const handleExport = () => {
    const blob = new Blob([sampleMarkdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'demo-review-report.md'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Markdown Renderer Demo</h1>
        <p className="text-gray-600 dark:text-gray-400">
          This demonstrates the new Markdown renderer that displays CLI-style code review reports.
        </p>
      </div>
      
      <MarkdownRenderer
        content={sampleMarkdown}
        enableCopy={true}
        enableAnchors={true}
        onExport={handleExport}
        title="Demo Code Review Report"
        className="border border-gray-200 dark:border-gray-700 rounded-lg p-6"
      />
    </div>
  )
}

export default MarkdownRendererDemo
