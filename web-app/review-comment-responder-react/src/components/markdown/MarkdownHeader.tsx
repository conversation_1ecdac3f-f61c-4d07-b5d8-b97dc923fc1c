import React, { useMemo } from 'react'
import { Link } from 'lucide-react'

interface MarkdownHeaderProps {
  level: 1 | 2 | 3 | 4 | 5 | 6
  children: React.ReactNode
  enableAnchors?: boolean
  className?: string
  id?: string
}

export const MarkdownHeader: React.FC<MarkdownHeaderProps> = ({
  level,
  children,
  enableAnchors = true,
  className = '',
  id: providedId
}) => {
  // Generate anchor ID from text content
  const anchorId = useMemo(() => {
    if (providedId) return providedId
    
    if (!enableAnchors) return undefined
    
    // Extract text content from children
    const textContent = React.Children.toArray(children)
      .map(child => {
        if (typeof child === 'string') return child
        if (React.isValidElement(child) && child.props && typeof child.props === 'object' && 'children' in child.props && typeof (child.props as any).children === 'string') {
          return (child.props as any).children
        }
        return ''
      })
      .join('')
      .trim()
    
    // Convert to URL-friendly anchor
    return textContent
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
  }, [children, enableAnchors, providedId])

  // Handle anchor click
  const handleAnchorClick = (e: React.MouseEvent) => {
    e.preventDefault()
    if (!anchorId) return
    
    // Update URL hash
    window.history.pushState(null, '', `#${anchorId}`)
    
    // Scroll to element
    const element = document.getElementById(anchorId)
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
    }
  }

  // Get header styling classes based on level
  const getHeaderClasses = (level: number): string => {
    const baseClasses = 'markdown-header'
    const levelClasses = {
      1: 'text-3xl font-bold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-4',
      2: 'text-2xl font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-2 mb-3',
      3: 'text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3',
      4: 'text-lg font-medium text-gray-900 dark:text-gray-100 mb-2',
      5: 'text-base font-medium text-gray-900 dark:text-gray-100 mb-2',
      6: 'text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'
    }
    
    return `${baseClasses} ${levelClasses[level as keyof typeof levelClasses]} ${className}`
  }

  // Create the appropriate header element
  const HeaderTag = `h${level}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'

  return (
    <HeaderTag
      id={anchorId}
      className={getHeaderClasses(level)}
    >
      <div className="header-content-wrapper">
        {/* Anchor link */}
        {enableAnchors && anchorId && (
          <a
            href={`#${anchorId}`}
            className="header-anchor"
            onClick={handleAnchorClick}
            aria-label={`Link to ${anchorId}`}
          >
            <Link size={16} />
          </a>
        )}
        
        {/* Header content */}
        <span className="header-text">
          {children}
        </span>
      </div>
    </HeaderTag>
  )
}

// Utility function to extract text content from React nodes
export const extractTextContent = (children: React.ReactNode): string => {
  return React.Children.toArray(children)
    .map(child => {
      if (typeof child === 'string') return child
      if (typeof child === 'number') return child.toString()
      if (React.isValidElement(child) && child.props && typeof child.props === 'object' && 'children' in child.props) {
        return extractTextContent((child.props as any).children)
      }
      return ''
    })
    .join('')
}

// Utility function to generate table of contents from headers
export const generateTableOfContents = (content: string): Array<{
  level: number
  text: string
  id: string
}> => {
  const headerRegex = /^(#{1,6})\s+(.+)$/gm
  const toc: Array<{ level: number; text: string; id: string }> = []
  
  let match
  while ((match = headerRegex.exec(content)) !== null) {
    const level = match[1].length
    const text = match[2].trim()
    const id = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
    
    toc.push({ level, text, id })
  }
  
  return toc
}

// Component for rendering table of contents
export const TableOfContents: React.FC<{
  items: Array<{ level: number; text: string; id: string }>
  className?: string
}> = ({ items, className = '' }) => {
  const handleTocClick = (id: string) => (e: React.MouseEvent) => {
    e.preventDefault()
    
    const element = document.getElementById(id)
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
      
      // Update URL hash
      window.history.pushState(null, '', `#${id}`)
    }
  }

  if (items.length === 0) return null

  return (
    <nav className={`table-of-contents ${className}`}>
      <h3 className="toc-title">Table of Contents</h3>
      <ul className="toc-list">
        {items.map((item, index) => (
          <li 
            key={index}
            className={`toc-item toc-level-${item.level}`}
          >
            <a
              href={`#${item.id}`}
              onClick={handleTocClick(item.id)}
              className="toc-link"
            >
              {item.text}
            </a>
          </li>
        ))}
      </ul>
    </nav>
  )
}
