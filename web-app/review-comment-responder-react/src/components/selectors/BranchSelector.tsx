import React, { useState, useEffect, useMemo } from 'react'
import { GitBranch, Loader2, RefreshC<PERSON>, Search, X } from 'lucide-react'
import { <PERSON><PERSON> } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { cn } from '../../lib/utils'
import type { BitbucketRepository, BitbucketBranch } from '../../types/bitbucket.types'
import { useRepositoryService } from '../../services/bitbucket/RepositoryService'

interface BranchSelectorProps {
  repository: BitbucketRepository | null
  selectedBranch: BitbucketBranch | null
  onBranchSelect: (branch: BitbucketBranch) => void
  className?: string
}

export const BranchSelector: React.FC<BranchSelectorProps> = ({
  repository,
  selectedBranch,
  onBranchSelect,
  className
}) => {
  const { getRepositoryWithBranches } = useRepositoryService()
  
  const [branches, setBranches] = useState<BitbucketBranch[]>([])
  const [defaultBranch, setDefaultBranch] = useState<BitbucketBranch | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showSearchResults, setShowSearchResults] = useState(false)
  const [loadingProgress, setLoadingProgress] = useState<string>('')

  // Load branches when repository changes
  useEffect(() => {
    if (repository) {
      loadBranches()
    } else {
      setBranches([])
      setDefaultBranch(null)
      setError(null)
      setSearchTerm('')
      setShowSearchResults(false)
      setLoadingProgress('')
    }
  }, [repository])

  // Filter branches based on search term
  const filteredBranches = useMemo(() => {
    if (!searchTerm.trim()) return branches

    const term = searchTerm.toLowerCase()
    return branches.filter(branch => 
      branch.name.toLowerCase().includes(term) ||
      branch.target.message?.toLowerCase().includes(term) ||
      branch.target.author?.user?.display_name?.toLowerCase().includes(term)
    )
  }, [branches, searchTerm])

  // Group filtered branches
  const groupedBranches = useMemo(() => {
    const filtered = filteredBranches
    const defaultFirst = defaultBranch && filtered.find(b => b.name === defaultBranch.name)
    const others = filtered
      .filter(b => b.name !== defaultBranch?.name)
      .sort((a, b) => new Date(b.target.date).getTime() - new Date(a.target.date).getTime())

    return {
      defaultBranch: defaultFirst,
      otherBranches: others,
      totalCount: filtered.length
    }
  }, [filteredBranches, defaultBranch])

  const loadBranches = async () => {
    if (!repository) return

    try {
      setIsLoading(true)
      setError(null)
      setLoadingProgress('Loading branches...')
      
      // Start loading
      const startTime = Date.now()
      
      const data = await getRepositoryWithBranches(
        repository.workspace.slug,
        repository.name
      )
      
      const loadTime = Date.now() - startTime
      setLoadingProgress(`Loaded ${data.branches.length} branches in ${(loadTime / 1000).toFixed(1)}s`)
      
      setBranches(data.branches)
      setDefaultBranch(data.defaultBranch || null)
      
      // Auto-select default branch if no branch is selected
      if (!selectedBranch && data.defaultBranch) {
        onBranchSelect(data.defaultBranch)
      }

      // Clear progress after a delay
      setTimeout(() => setLoadingProgress(''), 2000)
      
    } catch (error) {
      console.error('Failed to load branches:', error)
      setError(error instanceof Error ? error.message : 'Failed to load branches')
      setLoadingProgress('')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBranchClick = (branch: BitbucketBranch) => {
    onBranchSelect(branch)
    setSearchTerm('')
    setShowSearchResults(false)
  }

  const handleSearchChange = (value: string) => {
    setSearchTerm(value)
    setShowSearchResults(value.trim().length > 0)
  }

  const clearSearch = () => {
    setSearchTerm('')
    setShowSearchResults(false)
  }

  const renderBranchItem = (branch: BitbucketBranch) => {
    const isSelected = selectedBranch?.name === branch.name
    const isDefault = defaultBranch?.name === branch.name
    const lastCommitDate = new Date(branch.target.date).toLocaleDateString()

    return (
      <div
        key={branch.name}
        className={cn(
          'p-3 rounded-lg cursor-pointer transition-all duration-200 border',
          isSelected 
            ? 'border-primary bg-primary/10 shadow-md' 
            : 'border-border hover:border-primary/50 hover:bg-muted/30'
        )}
        onClick={() => handleBranchClick(branch)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <GitBranch className={cn(
              'h-4 w-4 shrink-0',
              isSelected ? 'text-primary' : 'text-muted-foreground'
            )} />
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className={cn(
                  'font-medium truncate',
                  isSelected ? 'text-primary' : 'text-foreground'
                )}>
                  {branch.name}
                </span>
                {isDefault && (
                  <span className="text-xs px-2 py-1 bg-primary/20 text-primary rounded-full">
                    default
                  </span>
                )}
              </div>
              
              <p className="text-xs text-muted-foreground truncate mt-1">
                {branch.target.message || 'No commit message'}
              </p>
            </div>
          </div>
          
          <div className="text-xs text-muted-foreground shrink-0 ml-2">
            {lastCommitDate}
          </div>
        </div>
        
        {branch.target.author && (
          <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
            <span>by {branch.target.author.user?.display_name || 'Unknown'}</span>
            <span>•</span>
            <span className="font-mono text-xs">
              {branch.target.hash.substring(0, 7)}
            </span>
          </div>
        )}
      </div>
    )
  }

  if (!repository) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Select Repository First
          </h3>
          <p className="text-muted-foreground">
            Please select a repository to view its branches.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-primary" />
            Select Branch
            {branches.length > 0 && (
              <span className="text-sm font-normal text-muted-foreground">
                ({showSearchResults ? groupedBranches.totalCount : branches.length} 
                {showSearchResults && ` of ${branches.length}`} available)
              </span>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={loadBranches}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn(
              'h-4 w-4',
              isLoading && 'animate-spin'
            )} />
            Refresh
          </Button>
        </CardTitle>
        
        <div className="space-y-3">
          <p className="text-sm text-muted-foreground">
            Repository: <span className="font-medium">{repository.full_name}</span>
          </p>

          {/* Search Input */}
          {branches.length > 5 && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search branches by name, commit message, or author..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-10 py-2 rounded-lg border border-border bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary transition-colors"
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {/* Search Results Summary */}
          {showSearchResults && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {groupedBranches.totalCount} results for "{searchTerm}"
              </Badge>
              {groupedBranches.totalCount === 0 && (
                <span className="text-xs text-muted-foreground">
                  Try a different search term
                </span>
              )}
            </div>
          )}

          {/* Loading Progress */}
          {loadingProgress && (
            <div className="flex items-center gap-2 text-xs text-primary">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              {loadingProgress}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {error && (
          <div className="p-4 bg-red-500/10 border-b border-red-500/20 text-red-400 text-sm">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={loadBranches}
              className="ml-2 text-red-400 hover:text-red-300"
            >
              Retry
            </Button>
          </div>
        )}

        <div className="max-h-80 overflow-y-auto custom-scrollbar">
          {isLoading ? (
            <div className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
              <p className="text-muted-foreground mb-2">Loading all branches...</p>
              <p className="text-xs text-muted-foreground">
                This may take a moment for repositories with many branches
              </p>
              {loadingProgress && (
                <p className="text-xs text-primary mt-2">{loadingProgress}</p>
              )}
            </div>
          ) : showSearchResults ? (
            /* Search Results */
            groupedBranches.totalCount > 0 ? (
              <div className="p-4 space-y-3">
                {/* Default branch in search results */}
                {groupedBranches.defaultBranch && (
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      Default Branch
                    </h4>
                    {renderBranchItem(groupedBranches.defaultBranch)}
                  </div>
                )}
                
                {/* Other matching branches */}
                {groupedBranches.otherBranches.length > 0 && (
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-2">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                      Other Branches ({groupedBranches.otherBranches.length})
                    </h4>
                    <div className="space-y-2">
                      {groupedBranches.otherBranches.map(branch => renderBranchItem(branch))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-8 text-center">
                <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground mb-1">No branches match your search</p>
                <p className="text-xs text-muted-foreground">Try searching for branch names, commit messages, or authors</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSearch}
                  className="mt-3"
                >
                  Clear Search
                </Button>
              </div>
            )
          ) : branches.length > 0 ? (
            /* Default View - All Branches */
            <div className="p-4 space-y-3">
              {/* Default branch section */}
              {defaultBranch && (
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    Default Branch
                  </h4>
                  {renderBranchItem(defaultBranch)}
                </div>
              )}
              
              {/* Recent branches section */}
              {branches.filter(branch => branch.name !== defaultBranch?.name).length > 0 && (
                <div>
                  <h4 className="text-xs font-medium text-muted-foreground mb-2 flex items-center gap-2">
                    <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                    Recent Branches ({branches.length - (defaultBranch ? 1 : 0)})
                    {branches.length > 10 && (
                      <span className="text-xs text-muted-foreground">
                        - Use search to find specific branches
                      </span>
                    )}
                  </h4>
                  <div className="space-y-2">
                    {branches
                      .filter(branch => branch.name !== defaultBranch?.name)
                      .sort((a, b) => new Date(b.target.date).getTime() - new Date(a.target.date).getTime())
                      .slice(0, 20) // Show max 20 branches initially
                      .map(branch => renderBranchItem(branch))
                    }
                    
                    {branches.length > 21 && (
                      <div className="p-3 text-center border-2 border-dashed border-border rounded-lg">
                        <p className="text-sm text-muted-foreground mb-2">
                          {branches.length - 20} more branches available
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Use the search above to find specific branches
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="p-8 text-center">
              <GitBranch className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No branches found</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}