import React, { useState, useEffect } from 'react'
import { Search, GitBranch, Lock, Unlock, Folder, Star, Clock } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { cn } from '../../lib/utils'
import type { BitbucketRepository } from '../../types/bitbucket.types'
import { useRepositoryService } from '../../services/bitbucket/RepositoryService'
import { useAuthStatus } from '../../hooks/useAuth'

interface RepositorySelectorProps {
  selectedRepository: BitbucketRepository | null
  onRepositorySelect: (repository: BitbucketRepository) => void
  className?: string
}

export const RepositorySelector: React.FC<RepositorySelectorProps> = ({
  selectedRepository,
  onRepositorySelect,
  className
}) => {
  const { isAuthenticated } = useAuthStatus()
  const { getAllRepositories, searchRepositories, service } = useRepositoryService()
  
  const [repositories, setRepositories] = useState<{
    byWorkspace: Record<string, BitbucketRepository[]>
    recent: BitbucketRepository[]
    total: number
  }>({ byWorkspace: {}, recent: [], total: 0 })
  
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<BitbucketRepository[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'recent' | 'workspaces' | 'search'>('recent')

  // Load repositories on mount
  useEffect(() => {
    if (isAuthenticated) {
      loadRepositories()
    }
  }, [isAuthenticated])

  // Handle search
  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      handleSearch()
    } else {
      setSearchResults([])
      if (activeTab === 'search') {
        setActiveTab('recent')
      }
    }
  }, [searchQuery])

  const loadRepositories = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const data = await getAllRepositories()
      setRepositories(data)
    } catch (error) {
      console.error('Failed to load repositories:', error)
      setError(error instanceof Error ? error.message : 'Failed to load repositories')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    try {
      setIsSearching(true)
      const results = await searchRepositories(searchQuery)
      setSearchResults(results)
      setActiveTab('search')
    } catch (error) {
      console.error('Search failed:', error)
      setError('Search failed. Please try again.')
    } finally {
      setIsSearching(false)
    }
  }

  const handleRepositoryClick = (repository: BitbucketRepository) => {
    onRepositorySelect(repository)
  }

  const renderRepositoryCard = (repository: BitbucketRepository) => {
    const isSelected = selectedRepository?.uuid === repository.uuid
    const display = service.formatRepositoryDisplay(repository)
    // const cloneUrls = service.getRepositoryCloneUrls(repository)

    return (
      <Card
        key={repository.uuid}
        className={cn(
          'cursor-pointer transition-all duration-200 hover:shadow-md border',
          isSelected 
            ? 'border-primary bg-primary/5 shadow-md' 
            : 'border-border hover:border-primary/50'
        )}
        onClick={() => handleRepositoryClick(repository)}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-lg bg-muted/50 shrink-0">
              <GitBranch className="h-4 w-4 text-muted-foreground" />
            </div>
            
            <div className="flex-1 min-w-0 space-y-2">
              {/* Title and Privacy */}
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-foreground truncate">
                  {display.title}
                </h3>
                {repository.is_private ? (
                  <Lock className="h-3 w-3 text-yellow-500 shrink-0" />
                ) : (
                  <Unlock className="h-3 w-3 text-green-500 shrink-0" />
                )}
              </div>

              {/* Full name and workspace */}
              <p className="text-sm text-muted-foreground truncate">
                {display.subtitle}
              </p>

              {/* Description */}
              {display.description && (
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {display.description}
                </p>
              )}

              {/* Badges and metadata */}
              <div className="flex items-center gap-2 flex-wrap">
                {display.badges.map((badge, index) => (
                  <span
                    key={index}
                    className="text-xs px-2 py-1 bg-muted rounded-full text-muted-foreground"
                  >
                    {badge}
                  </span>
                ))}
                <span className="text-xs text-muted-foreground ml-auto">
                  Updated {display.lastUpdated}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!isAuthenticated) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Login Required
          </h3>
          <p className="text-muted-foreground">
            Please login with your Bitbucket account to access repositories.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5 text-primary" />
          Select Repository
          {repositories.total > 0 && (
            <span className="text-sm font-normal text-muted-foreground">
              ({repositories.total} available)
            </span>
          )}
        </CardTitle>
        
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search repositories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="flex gap-2">
          <Button
            variant={activeTab === 'recent' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('recent')}
            className="flex items-center gap-2"
          >
            <Clock className="h-4 w-4" />
            Recent
          </Button>
          <Button
            variant={activeTab === 'workspaces' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setActiveTab('workspaces')}
            className="flex items-center gap-2"
          >
            <Folder className="h-4 w-4" />
            All Workspaces
          </Button>
          {searchResults.length > 0 && (
            <Button
              variant={activeTab === 'search' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('search')}
              className="flex items-center gap-2"
            >
              <Search className="h-4 w-4" />
              Search Results ({searchResults.length})
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {error && (
          <div className="p-4 bg-red-500/10 border-b border-red-500/20 text-red-400 text-sm">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={loadRepositories}
              className="ml-2 text-red-400 hover:text-red-300"
            >
              Retry
            </Button>
          </div>
        )}

        <div className="max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading repositories...</p>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {/* Recent Repositories */}
              {activeTab === 'recent' && (
                <>
                  {repositories.recent.length > 0 ? (
                    repositories.recent.map(repo => renderRepositoryCard(repo))
                  ) : (
                    <div className="text-center py-8">
                      <Star className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">No recent repositories found</p>
                    </div>
                  )}
                </>
              )}

              {/* All Workspaces */}
              {activeTab === 'workspaces' && (
                <>
                  {Object.entries(repositories.byWorkspace).map(([workspaceName, repos]) => (
                    <div key={workspaceName}>
                      <h4 className="font-medium text-foreground mb-2 px-2">
                        {workspaceName} ({repos.length})
                      </h4>
                      <div className="space-y-2 mb-4">
                        {repos.map(repo => renderRepositoryCard(repo))}
                      </div>
                    </div>
                  ))}
                  {Object.keys(repositories.byWorkspace).length === 0 && (
                    <div className="text-center py-8">
                      <Folder className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">No repositories found</p>
                    </div>
                  )}
                </>
              )}

              {/* Search Results */}
              {activeTab === 'search' && (
                <>
                  {searchResults.length > 0 ? (
                    searchResults.map(repo => renderRepositoryCard(repo))
                  ) : searchQuery.length > 2 ? (
                    <div className="text-center py-8">
                      <Search className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-muted-foreground">
                        No repositories found for "{searchQuery}"
                      </p>
                    </div>
                  ) : null}
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}