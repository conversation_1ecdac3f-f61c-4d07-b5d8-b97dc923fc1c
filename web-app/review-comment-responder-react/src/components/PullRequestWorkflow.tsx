import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { RepositorySelector } from './selectors/RepositorySelector'
import { BranchSelector } from './selectors/BranchSelector'
import { PullRequestSelector } from './selectors/PullRequestSelector'
import { InlineDiffViewer } from './InlineDiffViewer'
import { GitBranch, GitPullRequest, GitCommit } from 'lucide-react'
import type { 
  BitbucketRepository, 
  BitbucketBranch, 
  BitbucketPullRequest 
} from '../types/bitbucket.types'

interface PullRequestWorkflowProps {
  className?: string
}

export const PullRequestWorkflow: React.FC<PullRequestWorkflowProps> = ({ className }) => {
  const [selectedRepository, setSelectedRepository] = useState<BitbucketRepository | null>(null)
  const [selectedBranch, setSelectedBranch] = useState<BitbucketBranch | null>(null)
  const [selectedPR, setSelectedPR] = useState<BitbucketPullRequest | null>(null)

  const handleRepositorySelect = (repository: BitbucketRepository) => {
    setSelectedRepository(repository)
    // Reset dependent selections
    setSelectedBranch(null)
    setSelectedPR(null)
  }

  const handleBranchSelect = (branch: BitbucketBranch) => {
    setSelectedBranch(branch)
    // Reset dependent selections
    setSelectedPR(null)
  }

  const handlePRSelect = (pullRequest: BitbucketPullRequest) => {
    setSelectedPR(pullRequest)
  }

  return (
    <div className={className}>
      {/* Selection Flow */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <RepositorySelector
          selectedRepository={selectedRepository}
          onRepositorySelect={handleRepositorySelect}
        />
        
        <BranchSelector
          repository={selectedRepository}
          selectedBranch={selectedBranch}
          onBranchSelect={handleBranchSelect}
        />
        
        <PullRequestSelector
          repository={selectedRepository}
          branch={selectedBranch}
          selectedPullRequest={selectedPR}
          onPullRequestSelect={handlePRSelect}
        />
      </div>

      {/* Selection Summary */}
      {(selectedRepository || selectedBranch || selectedPR) && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <GitCommit className="h-5 w-5 text-primary" />
              Selection Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {selectedRepository && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="shrink-0">Repository</Badge>
                  <span className="text-sm font-mono">{selectedRepository.full_name}</span>
                  {selectedRepository.is_private && (
                    <Badge variant="secondary" className="text-xs">Private</Badge>
                  )}
                </div>
              )}
              
              {selectedBranch && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="shrink-0">Branch</Badge>
                  <GitBranch className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-mono">{selectedBranch.name}</span>
                  <span className="text-xs text-muted-foreground">
                    Last commit: {selectedBranch.target.hash.substring(0, 7)}
                  </span>
                </div>
              )}
              
              {selectedPR && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="shrink-0">Pull Request</Badge>
                  <GitPullRequest className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    #{selectedPR.id} {selectedPR.title}
                  </span>
                  <Badge 
                    variant={selectedPR.state === 'OPEN' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {selectedPR.state.toLowerCase()}
                  </Badge>
                </div>
              )}
            </div>

            {selectedPR && (
              <div className="mt-4 pt-4 border-t border-border">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-primary">{selectedPR.comment_count || 0}</p>
                    <p className="text-xs text-muted-foreground uppercase tracking-wide">Comments</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-500">{selectedPR.task_count || 0}</p>
                    <p className="text-xs text-muted-foreground uppercase tracking-wide">Tasks</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-500">{selectedPR.reviewers?.length || 0}</p>
                    <p className="text-xs text-muted-foreground uppercase tracking-wide">Reviewers</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-500">{selectedPR.participants?.length || 0}</p>
                    <p className="text-xs text-muted-foreground uppercase tracking-wide">Participants</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Inline Diff Viewer */}
      <InlineDiffViewer 
        pullRequest={selectedPR}
        repository={selectedRepository}
        className="mb-8" 
      />

      {/* Help/Instructions */}
      {!selectedPR && (
        <Card className="bg-gradient-to-br from-muted/30 to-muted/10">
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <div className="flex justify-center gap-4 text-muted-foreground">
                <div className="flex items-center gap-2">
                  <GitCommit className="h-5 w-5" />
                  <span className="text-sm">1. Select Repository</span>
                </div>
                <div className="flex items-center gap-2">
                  <GitBranch className="h-5 w-5" />
                  <span className="text-sm">2. Choose Branch</span>
                </div>
                <div className="flex items-center gap-2">
                  <GitPullRequest className="h-5 w-5" />
                  <span className="text-sm">3. Pick Pull Request</span>
                </div>
              </div>
              
              <h3 className="text-xl font-semibold text-foreground">
                Interactive Pull Request Review
              </h3>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Navigate through your repositories, branches, and pull requests. Once you select a PR, 
                you'll see the full diff with inline comments from Bitbucket. You can add new comments, 
                reply to existing ones, and generate Claude responses directly within the code view.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}