/**
 * Multi-Agent Results Aggregator Component
 * Displays comprehensive results from all 7 parallel agents
 */

import React, { useState } from 'react'
import { 
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Users,
  Brain,
  FileText,
  Zap,
  Target,
  Download,
  ExternalLink,
  ChevronDown,
  ChevronRight,
  Shield,
  Bug,
  Settings
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { Alert, AlertDescription } from './ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from './ui/collapsible'
import { FindingsCategorizer } from './FindingsCategorizer'
import type { 
  EnhancedReviewResults as MultiAgentEnhancedResults,
  AgentType
} from '../types/multi-agent'

interface MultiAgentResultsAggregatorProps {
  results: MultiAgentEnhancedResults
  onFileClick?: (filename: string, line?: number) => void
  onExportResults?: (results: MultiAgentEnhancedResults) => void
  className?: string
}

// Helper functions
const getAgentDisplayName = (agentType: AgentType): string => {
  const displayNames: Record<AgentType, string> = {
    'acceptance_criteria': 'Acceptance Criteria',
    'bug_detection': 'Bug Detection', 
    'security_analysis': 'Security Analysis',
    'logic_analysis': 'Logic Analysis',
    'quality_analysis': 'Quality Analysis',
    'architecture_analysis': 'Architecture Analysis',
    'summary': 'Summary & Recommendations'
  }
  return displayNames[agentType] || agentType
}

const getAgentIcon = (agentType: AgentType) => {
  const icons: Record<AgentType, React.ComponentType<{ className?: string }>> = {
    'acceptance_criteria': Target,
    'bug_detection': Bug,
    'security_analysis': Shield,
    'logic_analysis': Settings,
    'quality_analysis': BarChart3,
    'architecture_analysis': Users,
    'summary': FileText
  }
  return icons[agentType] || Brain
}

const getAgentColor = (agentType: AgentType): string => {
  const colors: Record<AgentType, string> = {
    'acceptance_criteria': 'text-green-600 border-green-200 bg-green-50',
    'bug_detection': 'text-red-600 border-red-200 bg-red-50',
    'security_analysis': 'text-orange-600 border-orange-200 bg-orange-50',
    'logic_analysis': 'text-blue-600 border-blue-200 bg-blue-50',
    'quality_analysis': 'text-purple-600 border-purple-200 bg-purple-50',
    'architecture_analysis': 'text-indigo-600 border-indigo-200 bg-indigo-50',
    'summary': 'text-gray-600 border-gray-200 bg-gray-50'
  }
  return colors[agentType] || 'text-gray-600 border-gray-200 bg-gray-50'
}

const getPriorityColor = (priority: 'high' | 'medium' | 'low'): string => {
  switch (priority) {
    case 'high': return 'text-red-700 bg-red-100 border-red-300'
    case 'medium': return 'text-yellow-700 bg-yellow-100 border-yellow-300'
    case 'low': return 'text-blue-700 bg-blue-100 border-blue-300'
    default: return 'text-gray-700 bg-gray-100 border-gray-300'
  }
}

export const MultiAgentResultsAggregator: React.FC<MultiAgentResultsAggregatorProps> = ({
  results,
  onFileClick,
  onExportResults,
  className = ""
}) => {
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set())
  const [selectedPriority, setSelectedPriority] = useState<'all' | 'high' | 'medium' | 'low'>('all')

  // Toggle agent expansion
  const toggleAgentExpansion = (agentType: string) => {
    const newExpanded = new Set(expandedAgents)
    if (newExpanded.has(agentType)) {
      newExpanded.delete(agentType)
    } else {
      newExpanded.add(agentType)
    }
    setExpandedAgents(newExpanded)
  }

  // Get execution metrics from results
  const executionMetrics = results.overall_results.execution_metrics
  const agentResults = results.agent_results || {}
  const priorityFindings = results.overall_results.priority_findings || []

  // Filter findings by priority
  const filteredFindings = selectedPriority === 'all' 
    ? priorityFindings
    : priorityFindings.filter(f => f.priority === selectedPriority)

  // Calculate parallel efficiency color
  const getEfficiencyColor = (efficiency: number): string => {
    if (efficiency >= 80) return 'text-green-600'
    if (efficiency >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Format execution time
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Performance Overview */}
      <Card className="border-violet-200 bg-gradient-to-r from-violet-50 to-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-violet-600" />
            Multi-Agent Performance Overview
            <Badge variant="secondary" className="ml-2">
              7 Parallel Agents
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-violet-600">
                {formatTime(executionMetrics.total_execution_time)}
              </div>
              <div className="text-sm text-muted-foreground">Total Time</div>
            </div>
            
            <div className="text-center">
              <div className={`text-2xl font-bold ${getEfficiencyColor(executionMetrics.parallel_efficiency)}`}>
                {executionMetrics.parallel_efficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-muted-foreground">Parallel Efficiency</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.values(executionMetrics.agent_performance).filter(p => p.success).length}
              </div>
              <div className="text-sm text-muted-foreground">Successful Agents</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {priorityFindings.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Findings</div>
            </div>
          </div>

          {/* Progress bars for individual agents */}
          <div className="mt-6">
            <h4 className="text-sm font-medium text-muted-foreground mb-3">Agent Execution Timeline</h4>
            <div className="space-y-2">
              {Object.entries(executionMetrics.agent_performance).map(([agentType, perf]) => {
                const IconComponent = getAgentIcon(agentType as AgentType)
                return (
                  <div key={agentType} className="flex items-center gap-3">
                    <div className="flex items-center gap-2 w-48">
                      <IconComponent className="h-4 w-4" />
                      <span className="text-sm">{getAgentDisplayName(agentType as AgentType)}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Progress 
                          value={perf.success ? 100 : 0} 
                          className="h-2 flex-1"
                        />
                        <span className="text-xs text-muted-foreground w-12">
                          {formatTime(perf.execution_time)}
                        </span>
                        {perf.success ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabbed Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="findings">Findings</TabsTrigger>
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Priority Findings Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Priority Findings ({filteredFindings.length})
                </div>
            
            <div className="flex items-center gap-2">
              {/* Priority Filter */}
              <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
                {(['all', 'high', 'medium', 'low'] as const).map((priority) => (
                  <Button
                    key={priority}
                    variant={selectedPriority === priority ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setSelectedPriority(priority)}
                    className="capitalize"
                  >
                    {priority}
                    {priority !== 'all' && (
                      <Badge variant="outline" className="ml-1 text-xs">
                        {priorityFindings.filter(f => f.priority === priority).length}
                      </Badge>
                    )}
                  </Button>
                ))}
              </div>
              
              {/* Export Button */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExportResults?.(results)}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {filteredFindings.map((finding, index) => {
              const IconComponent = getAgentIcon(finding.agent_type)
              return (
                <Alert key={index} className={getPriorityColor(finding.priority)}>
                  <div className="flex items-start gap-3">
                    <IconComponent className="h-4 w-4 mt-0.5" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {getAgentDisplayName(finding.agent_type)}
                        </Badge>
                        <Badge variant="outline" className="text-xs capitalize">
                          {finding.priority}
                        </Badge>
                        {finding.file && (
                          <Badge variant="secondary" className="text-xs">
                            {finding.file}
                            {finding.line && `:${finding.line}`}
                          </Badge>
                        )}
                      </div>
                      <AlertDescription className="mb-2">
                        <strong>{finding.type}:</strong> {finding.description}
                      </AlertDescription>
                      {finding.suggestion && (
                        <div className="text-sm bg-white/50 rounded p-2 mt-2">
                          <strong>Suggestion:</strong> {finding.suggestion}
                        </div>
                      )}
                      {finding.file && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onFileClick?.(finding.file!, finding.line)}
                          className="mt-2 h-7 text-xs"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          View in Code
                        </Button>
                      )}
                    </div>
                  </div>
                </Alert>
              )
            })}
            
            {filteredFindings.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Target className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>No findings for the selected priority level</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
        </TabsContent>

        {/* Findings Tab with Categorizer */}
        <TabsContent value="findings" className="space-y-4">
          <FindingsCategorizer
            findings={priorityFindings}
            onFileClick={onFileClick}
            className="max-w-none"
          />
        </TabsContent>

        {/* Agents Tab */}
        <TabsContent value="agents" className="space-y-4">
          {/* Individual Agent Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Individual Agent Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(agentResults).map(([agentType]) => {
                  const IconComponent = getAgentIcon(agentType as AgentType)
                  const isExpanded = expandedAgents.has(agentType)
                  const agentPerf = executionMetrics.agent_performance[agentType]
                  
                  return (
                    <Collapsible 
                      key={agentType} 
                      open={isExpanded}
                      onOpenChange={() => toggleAgentExpansion(agentType)}
                    >
                      <CollapsibleTrigger asChild>
                        <Card className={`cursor-pointer transition-all hover:shadow-md ${getAgentColor(agentType as AgentType)}`}>
                          <CardContent className="pt-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <IconComponent className="h-5 w-5" />
                                <div>
                                  <h4 className="font-medium">{getAgentDisplayName(agentType as AgentType)}</h4>
                                  <p className="text-sm text-muted-foreground">
                                    {agentPerf?.findings_count || 0} findings • {formatTime(agentPerf?.execution_time || 0)}
                                  </p>
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                {agentPerf?.success ? (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                ) : (
                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                )}
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </CollapsibleTrigger>
                      
                      <CollapsibleContent>
                        <Card className="mt-2 border-l-4 border-l-violet-500">
                          <CardContent className="pt-4">
                            {/* Agent-specific results would go here */}
                            <div className="text-sm text-muted-foreground">
                              <p>Agent analysis results and detailed findings would be displayed here.</p>
                              <p className="mt-2">This section would show the specific output from the {getAgentDisplayName(agentType as AgentType)} agent.</p>
                            </div>
                          </CardContent>
                        </Card>
                      </CollapsibleContent>
                    </Collapsible>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Compliance Tab */}
        <TabsContent value="compliance" className="space-y-4">
          {/* JIRA Compliance (if available) */}
          {results.jira_compliance ? (
            <Card className="border-green-200 bg-green-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  JIRA Acceptance Criteria Compliance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {results.jira_compliance.compliance_percentage.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Overall Compliance</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {results.jira_compliance.fulfilled_criteria}
                    </div>
                    <div className="text-sm text-muted-foreground">Fulfilled Criteria</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">
                      {results.jira_compliance.total_criteria}
                    </div>
                    <div className="text-sm text-muted-foreground">Total Criteria</div>
                  </div>
                </div>

                <Progress 
                  value={results.jira_compliance.compliance_percentage} 
                  className="h-3 mb-4"
                />

                {results.jira_compliance.unfulfilled_criteria.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Unfulfilled Criteria:</h4>
                    <div className="space-y-2">
                      {results.jira_compliance.unfulfilled_criteria.map((criterion, index) => (
                        <Alert key={index} className="border-yellow-200 bg-yellow-50">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>{criterion.criterion}:</strong> {criterion.explanation}
                            <Badge variant="outline" className="ml-2 text-xs">
                              {criterion.status}
                            </Badge>
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card className="border-gray-200 bg-gray-50">
              <CardContent className="pt-6">
                <div className="text-center">
                  <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-600 mb-2">
                    No JIRA Compliance Data Available
                  </h3>
                  <p className="text-gray-500 text-sm">
                    This review did not include JIRA ticket validation or the ticket did not have acceptance criteria.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}