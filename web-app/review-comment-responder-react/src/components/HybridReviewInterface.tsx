import React, { useEffect } from 'react'
import { ArrowLeftRight, Settings } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { ModeSelector, type ReviewMode } from './ModeSelector'
import { PullRequestWorkflow } from './PullRequestWorkflow'
import { CommentUploader } from './CommentUploader'
import { PRContextForm } from './PRContextForm'
import { GitDiffViewer } from './GitDiffViewer'
import { useReviewMode, useReviewModeHelpers } from '../hooks/useReviewMode'
import { useAuthStatus } from '../hooks/useAuth'
import { cn } from '../lib/utils'

interface HybridReviewInterfaceProps {
  className?: string
}

export const HybridReviewInterface: React.FC<HybridReviewInterfaceProps> = ({ className }) => {
  const { isAuthenticated } = useAuthStatus()
  const { 
    currentMode, 
    isTransitioning, 
    preferences, 
    setMode, 
    toggleMode, 
    updatePreferences 
  } = useReviewMode()
  const { shouldShowModeSelector, getRecommendedMode } = useReviewModeHelpers()

  const [showModeSelector, setShowModeSelector] = React.useState(false)
  const [isInitialized, setIsInitialized] = React.useState(false)

  // Initialize mode based on authentication state
  useEffect(() => {
    if (!isInitialized) {
      const recommendedMode = getRecommendedMode(isAuthenticated)
      if (currentMode !== recommendedMode && preferences.autoSwitchToApi) {
        setMode(recommendedMode)
      }
      setIsInitialized(true)
    }
  }, [isAuthenticated, isInitialized, currentMode, getRecommendedMode, setMode, preferences.autoSwitchToApi])

  const handleModeChange = (mode: ReviewMode) => {
    setMode(mode)
    setShowModeSelector(false)
    
    // Track manual mode changes to prevent auto-switching
    updatePreferences({
      lastManualModeChange: Date.now()
    } as any)
  }

  const canShowModeSelector = shouldShowModeSelector(isAuthenticated) || !isAuthenticated

  return (
    <div className={className}>
      {/* Mode Control Header */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <ArrowLeftRight className="h-5 w-5 text-primary" />
              <span>Review Interface</span>
              <Badge 
                variant={currentMode === 'api' ? 'default' : 'secondary'} 
                className={cn(
                  'transition-all duration-200',
                  isTransitioning && 'animate-pulse'
                )}
              >
                {currentMode === 'api' ? 'API Mode' : 'Screenshot Mode'}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              {/* Quick Toggle Button */}
              {isAuthenticated && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleMode}
                  className="flex items-center gap-2"
                  disabled={isTransitioning}
                >
                  <ArrowLeftRight className="h-4 w-4" />
                  Switch Mode
                </Button>
              )}

              {/* Mode Selector Toggle */}
              {canShowModeSelector && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowModeSelector(!showModeSelector)}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {showModeSelector ? 'Hide Options' : 'Mode Options'}
                </Button>
              )}
            </div>
          </CardTitle>

          {/* Mode Description */}
          <div className="text-sm text-muted-foreground">
            {currentMode === 'api' ? (
              <p>
                <strong>API Mode:</strong> Direct Bitbucket integration with live data, 
                inline comments, and real-time PR navigation.
              </p>
            ) : (
              <p>
                <strong>Screenshot Mode:</strong> Upload comment screenshots for AI analysis 
                and response generation. Works without authentication.
              </p>
            )}
          </div>
        </CardHeader>

        {/* Mode Selector (expandable) */}
        {showModeSelector && canShowModeSelector && (
          <CardContent>
            <ModeSelector
              currentMode={currentMode}
              onModeChange={handleModeChange}
              isAuthenticated={isAuthenticated}
            />
          </CardContent>
        )}
      </Card>

      {/* Mode-specific Content */}
      <div className={cn(
        'transition-all duration-300',
        isTransitioning && 'opacity-50'
      )}>
        {currentMode === 'api' ? (
          /* API Mode - Bitbucket Integration */
          <div className="space-y-6">
            {isAuthenticated ? (
              <PullRequestWorkflow />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    Authentication Required
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    API Mode requires Bitbucket authentication to access your repositories.
                  </p>
                  <Button 
                    onClick={() => setMode('screenshot')}
                    variant="outline"
                  >
                    Switch to Screenshot Mode
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          /* Screenshot Mode - Classic Upload Interface */
          <div className="space-y-6">
            {/* PR Context Form */}
            <PRContextForm />

            {/* Git Diff Viewer */}
            <GitDiffViewer />

            {/* Comment Uploader */}
            <CommentUploader />

            {/* API Mode Promotion (if authenticated) */}
            {isAuthenticated && (
              <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                      <ArrowLeftRight className="h-5 w-5 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-foreground mb-1">
                        Try API Mode
                      </h4>
                      <p className="text-sm text-muted-foreground mb-3">
                        Since you're authenticated, you can use API Mode to browse your 
                        Bitbucket repositories and view PR comments inline without uploading screenshots.
                      </p>
                      <Button 
                        size="sm"
                        onClick={() => setMode('api')}
                        className="flex items-center gap-2"
                      >
                        <ArrowLeftRight className="h-3 w-3" />
                        Switch to API Mode
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* Mode Preferences Footer */}
      {preferences.showModeSelector && (
        <Card className="mt-8 bg-muted/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                <strong>Tip:</strong> Your mode preference is automatically saved. 
                {preferences.autoSwitchToApi && ' Auto-switching to API mode when authenticated.'}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updatePreferences({ showModeSelector: false })}
                className="text-xs"
              >
                Hide Tips
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}