import React, { useState } from 'react'
import { 
  <PERSON>, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Copy, 
  RotateCcw, 
  Trash2,
  Eye,
  Clock
} from 'lucide-react'
import { Card, CardContent, CardHeader } from './ui/card'
import { Button } from './ui/button'
import type { Comment } from '../types'
import { useCommentStore } from '../store/useCommentStore'
import { getStatusColor, getConfidenceColor, formatResponse } from '../lib/utils'
import { PreviewChanges } from './PreviewChanges'

interface CommentCardProps {
  comment: Comment
  onProcess: (commentId: number) => void
  onRegenerate: (commentId: number) => void
}

export const CommentCard: React.FC<CommentCardProps> = ({ 
  comment, 
  onProcess, 
  onRegenerate 
}) => {
  const { removeComment } = useCommentStore()
  const [imageModalOpen, setImageModalOpen] = useState(false)

  const getStatusIcon = () => {
    switch (comment.status) {
      case 'pending':
        return <Play className="h-5 w-5" />
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin" />
      case 'completed':
        return <CheckCircle className="h-5 w-5" />
      case 'error':
        return <AlertCircle className="h-5 w-5" />
      default:
        return <Clock className="h-5 w-5" />
    }
  }

  const copyResponse = async () => {
    if (comment.response?.text || comment.response?.response) {
      await navigator.clipboard.writeText(comment.response.text || comment.response.response || '')
      // TODO: Show toast
    }
  }

  const handleRemove = () => {
    if (confirm('Are you sure you want to remove this comment?')) {
      removeComment(comment.id)
    }
  }

  return (
    <>
      <Card className="group hover:shadow-2xl transition-all duration-300 overflow-hidden relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg"></div>
        <CardHeader className="relative">
          <div className="flex justify-between items-start">
            <div className="flex-1 space-y-3">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                  <Eye className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-200">
                    {comment.fileName}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Uploaded: {new Date(comment.uploadTime).toLocaleString()}
                  </p>
                </div>
              </div>
              
              {comment.extractedData && (
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/50 backdrop-blur-sm border border-border/30">
                    <span className="text-sm font-medium text-muted-foreground">File:</span>
                    <code className="text-sm font-mono bg-background/80 px-2 py-1 rounded border">
                      {comment.extractedData.file || 'Unknown'}
                    </code>
                  </div>
                  <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-muted/50 backdrop-blur-sm border border-border/30">
                    <span className="text-sm font-medium text-muted-foreground">Line:</span>
                    <code className="text-sm font-mono bg-background/80 px-2 py-1 rounded border">
                      {comment.extractedData.line || 'Unknown'}
                    </code>
                  </div>
                </div>
              )}
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              className={`${getStatusColor(comment.status)} relative overflow-hidden transition-all duration-300 hover:scale-110 h-14 w-14`}
              onClick={() => onProcess(comment.id)}
              disabled={comment.status !== 'pending'}
            >
              <div className="relative">
                {getStatusIcon()}
              </div>
              {comment.status === 'processing' && (
                <div className="absolute inset-0 animate-pulse bg-primary/20 rounded-lg"></div>
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="relative">
          <div className="mb-6 group/image">
            <div className="relative overflow-hidden rounded-lg border-2 border-border bg-black/20 p-1">
              <img
                src={comment.dataUrl}
                alt="Review comment screenshot"
                className="w-full h-auto rounded-lg cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:brightness-110"
                onClick={() => setImageModalOpen(true)}
                style={{ maxHeight: '400px', objectFit: 'contain' }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"></div>
            </div>
          </div>

          {/* Response Display */}
          {comment.response && (
            <div className="border-t pt-4">
              <h4 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Generated Response
                {comment.response.confidence && (
                  <span className={`text-sm ${getConfidenceColor(comment.response.confidence)}`}>
                    ({Math.round(comment.response.confidence * 100)}% confidence)
                  </span>
                )}
              </h4>
              
              {(comment.response.commentText || comment.response.comment_text) && (
                <div className="mb-3 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-600">Extracted Comment:</p>
                  <p className="text-gray-800">
                    {comment.response.commentText || comment.response.comment_text}
                  </p>
                </div>
              )}
              
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: formatResponse(comment.response.text || comment.response.response || '') 
                }}
              />
            </div>
          )}

          {/* Preview Changes Component */}
          {comment.previewChanges && comment.needsApproval && (
            <PreviewChanges comment={comment} />
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 mt-6 pt-4 border-t border-border/30">
            {comment.response && (
              <>
                <Button variant="outline" size="sm" onClick={copyResponse} className="group/copy">
                  <Copy className="h-4 w-4 mr-2 group-hover/copy:scale-110 transition-transform" />
                  Copy Response
                </Button>
                <Button variant="outline" size="sm" onClick={() => onRegenerate(comment.id)} className="group/regen">
                  <RotateCcw className="h-4 w-4 mr-2 group-hover/regen:rotate-180 transition-transform duration-300" />
                  Regenerate
                </Button>
              </>
            )}
            <Button variant="destructive" size="sm" onClick={handleRemove} className="ml-auto group/remove">
              <Trash2 className="h-4 w-4 mr-2 group-hover/remove:scale-110 transition-transform" />
              Remove
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Image Modal */}
      {imageModalOpen && (
        <div 
          className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center z-50 animate-fade-in"
          onClick={() => setImageModalOpen(false)}
        >
          <div className="relative max-w-[95vw] max-h-[95vh] animate-scale-in">
            <button
              className="absolute -top-12 right-0 text-white/80 hover:text-white text-2xl font-bold hover:scale-110 transition-all duration-200 bg-black/30 backdrop-blur-sm rounded-full w-10 h-10 flex items-center justify-center"
              onClick={() => setImageModalOpen(false)}
            >
              ×
            </button>
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20">
              <img
                src={comment.dataUrl}
                alt="Review comment screenshot"
                className="max-w-full max-h-full object-contain rounded-xl shadow-2xl"
              />
            </div>
          </div>
        </div>
      )}
    </>
  )
}