import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../markdown/MarkdownRenderer'

const testMarkdown = `
# Mermaid Test

This should render as a Mermaid diagram:

\`\`\`mermaid
graph TB
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
    C --> E[End]
    D --> E
\`\`\`

This should also be detected as Mermaid (without explicit language):

\`\`\`
graph TB
    CMS --> Gateway
    Gateway --> Vertex
    Vertex --> VertexAI
\`\`\`

And a sequence diagram:

\`\`\`mermaid
sequenceDiagram
    participant A as Alice
    participant B as Bob
    A->>B: Hello <PERSON>, how are you?
    B-->>A: Great!
\`\`\`
`

export const MermaidTest: React.FC = () => {
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Mermaid Integration Test</h1>
      <MarkdownRenderer content={testMarkdown} />
    </div>
  )
}