import React from 'react'
import { 
  CheckCircle2, 
  AlertCircle, 
  Clock, 
  Shield,
  Bug,
  Microscope,
  Code,
  Building2,
  FileText,
  Target,
  Loader2
} from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { Progress } from '../ui/progress'
import { Badge } from '../ui/badge'

export interface AgentStatus {
  agent_type: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  progress: number
  started_at?: string
  completed_at?: string
  estimated_remaining_time?: number
  error_message?: string
}

interface AgentCardProps {
  agentStatus: AgentStatus
  className?: string
}

const agentConfig = {
  acceptance_criteria: {
    name: 'Acceptance Criteria',
    icon: Target,
    color: 'blue',
    description: 'Validates JIRA acceptance criteria compliance'
  },
  bug_detection: {
    name: 'Bug Detection',
    icon: Bug,
    color: 'red',
    description: 'Scans for potential bugs and errors'
  },
  security_analysis: {
    name: 'Security Analysis',
    icon: Shield,
    color: 'orange',
    description: 'Identifies security vulnerabilities'
  },
  logic_analysis: {
    name: 'Logic Analysis',
    icon: Microscope,
    color: 'purple',
    description: 'Analyzes code logic and flow'
  },
  code_quality: {
    name: 'Code Quality',
    icon: Code,
    color: 'green',
    description: 'Evaluates code quality and standards'
  },
  architectural: {
    name: 'Architecture',
    icon: Building2,
    color: 'indigo',
    description: 'Reviews architectural patterns'
  },
  summary: {
    name: 'Summary',
    icon: FileText,
    color: 'gray',
    description: 'Generates implementation summary'
  }
} as const

const getStatusIcon = (status: AgentStatus['status']) => {
  const iconProps = { className: "h-5 w-5" }
  
  switch (status) {
    case 'pending':
      return <Clock {...iconProps} className="h-5 w-5 text-muted-foreground" />
    case 'running':
      return <Loader2 {...iconProps} className="h-5 w-5 text-blue-500 animate-spin" />
    case 'completed':
      return <CheckCircle2 {...iconProps} className="h-5 w-5 text-green-500" />
    case 'failed':
      return <AlertCircle {...iconProps} className="h-5 w-5 text-red-500" />
    case 'skipped':
      return <div className="h-5 w-5 rounded-full bg-muted-foreground/20 flex items-center justify-center">
        <div className="h-2 w-2 bg-muted-foreground/40 rounded-full" />
      </div>
    default:
      return <Clock {...iconProps} className="h-5 w-5 text-muted-foreground" />
  }
}

const getStatusColor = (status: AgentStatus['status']) => {
  switch (status) {
    case 'pending':
      return 'bg-muted'
    case 'running':
      return 'bg-blue-50 border-blue-200'
    case 'completed':
      return 'bg-green-50 border-green-200'
    case 'failed':
      return 'bg-red-50 border-red-200'
    case 'skipped':
      return 'bg-muted/50'
    default:
      return 'bg-muted'
  }
}

const getStatusBadgeVariant = (status: AgentStatus['status']) => {
  switch (status) {
    case 'pending':
      return 'secondary'
    case 'running':
      return 'default'
    case 'completed':
      return 'default'
    case 'failed':
      return 'destructive'
    case 'skipped':
      return 'outline'
    default:
      return 'secondary'
  }
}

const formatTimeRemaining = (seconds?: number): string => {
  if (!seconds || seconds <= 0) return ''
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `~${minutes}m ${remainingSeconds}s`
  } else {
    return `~${remainingSeconds}s`
  }
}

export const AgentCard: React.FC<AgentCardProps> = ({ 
  agentStatus, 
  className = ""
}) => {
  const config = agentConfig[agentStatus.agent_type as keyof typeof agentConfig]
  
  if (!config) {
    console.warn(`Unknown agent type: ${agentStatus.agent_type}`)
    return null
  }
  
  const IconComponent = config.icon
  const statusColor = getStatusColor(agentStatus.status)
  const statusIcon = getStatusIcon(agentStatus.status)
  const badgeVariant = getStatusBadgeVariant(agentStatus.status)
  
  const showProgress = agentStatus.status === 'running' && agentStatus.progress > 0
  const isCompleted = agentStatus.status === 'completed'
  const hasFailed = agentStatus.status === 'failed'
  const isSkipped = agentStatus.status === 'skipped'
  
  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${statusColor} ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <div className={`
              p-2 rounded-lg
              ${config.color === 'blue' ? 'bg-blue-100 text-blue-600' : ''}
              ${config.color === 'red' ? 'bg-red-100 text-red-600' : ''}
              ${config.color === 'orange' ? 'bg-orange-100 text-orange-600' : ''}
              ${config.color === 'purple' ? 'bg-purple-100 text-purple-600' : ''}
              ${config.color === 'green' ? 'bg-green-100 text-green-600' : ''}
              ${config.color === 'indigo' ? 'bg-indigo-100 text-indigo-600' : ''}
              ${config.color === 'gray' ? 'bg-gray-100 text-gray-600' : ''}
              ${isSkipped ? 'bg-muted text-muted-foreground opacity-50' : ''}
            `}>
              <IconComponent className="h-5 w-5" />
            </div>
            
            <div className="flex-1">
              <h3 className={`font-medium text-sm ${isSkipped ? 'text-muted-foreground' : 'text-foreground'}`}>
                {config.name}
              </h3>
              <p className={`text-xs ${isSkipped ? 'text-muted-foreground' : 'text-muted-foreground'}`}>
                {config.description}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {statusIcon}
            <Badge variant={badgeVariant} className="text-xs">
              {isCompleted && (
                <CheckCircle2 className="h-3 w-3 mr-1 text-green-500" />
              )}
              {agentStatus.status.charAt(0).toUpperCase() + agentStatus.status.slice(1)}
            </Badge>
          </div>
        </div>
        
        {/* Progress Bar for Running Agent */}
        {showProgress && (
          <div className="space-y-2 mb-3">
            <div className="flex justify-between items-center text-xs">
              <span className="text-muted-foreground">Progress</span>
              <span className="font-medium">{Math.round(agentStatus.progress)}%</span>
            </div>
            <Progress 
              value={agentStatus.progress} 
              className="h-2"
            />
            {agentStatus.estimated_remaining_time && (
              <div className="text-xs text-muted-foreground text-right">
                {formatTimeRemaining(agentStatus.estimated_remaining_time)} remaining
              </div>
            )}
          </div>
        )}
        
        {/* Completed Indicator */}
        {isCompleted && (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 p-2 rounded-lg">
            <CheckCircle2 className="h-4 w-4" />
            <span className="text-xs font-medium">Analysis Complete</span>
            {agentStatus.completed_at && (
              <span className="text-xs text-green-600/70 ml-auto">
                {new Date(agentStatus.completed_at).toLocaleTimeString()}
              </span>
            )}
          </div>
        )}
        
        {/* Error Message */}
        {hasFailed && agentStatus.error_message && (
          <div className="flex items-start gap-2 text-red-600 bg-red-50 p-2 rounded-lg">
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <span className="text-xs font-medium block">Analysis Failed</span>
              <span className="text-xs text-red-600/80">{agentStatus.error_message}</span>
            </div>
          </div>
        )}
        
        {/* Skipped Indicator */}
        {isSkipped && (
          <div className="text-xs text-muted-foreground text-center py-2 opacity-60">
            Skipped for this review mode
          </div>
        )}
        
        {/* Timing Information */}
        {(agentStatus.started_at || agentStatus.completed_at) && !isSkipped && (
          <div className="text-xs text-muted-foreground mt-3 pt-2 border-t border-muted">
            {agentStatus.started_at && (
              <div>Started: {new Date(agentStatus.started_at).toLocaleTimeString()}</div>
            )}
            {agentStatus.completed_at && (
              <div>Completed: {new Date(agentStatus.completed_at).toLocaleTimeString()}</div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}