import React, { useState } from 'react'
import { 
  Brain, 
  <PERSON>ch, 
  Zap, 
  Search, 
  CheckCircle2, 
  AlertTriangle,
  Server,
  Clock,
  ChevronDown,
  ChevronRight,
  Cpu,
  MessageCircle,
  Play
} from 'lucide-react'
import { Card, CardContent } from './ui/card'

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: any
}

interface ClaudeProgressTimelineProps {
  events: ProgressEvent[]
  currentStep?: string
  className?: string
}

const getEventIcon = (type: string) => {
  switch (type) {
    case 'session_started':
      return <Play className="h-4 w-4 text-blue-400" />
    case 'tools_available':
      return <Wrench className="h-4 w-4 text-purple-400" />
    case 'mcp_servers':
      return <Server className="h-4 w-4 text-cyan-400" />
    case 'turn_started':
      return <Cpu className="h-4 w-4 text-orange-400" />
    case 'thinking':
      return <Brain className="h-4 w-4 text-pink-400 animate-pulse" />
    case 'tool_use':
      return <Search className="h-4 w-4 text-green-400" />
    case 'claude_response':
      return <MessageCircle className="h-4 w-4 text-blue-300" />
    case 'review_completed':
      return <CheckCircle2 className="h-4 w-4 text-emerald-400" />
    case 'review_error':
      return <AlertTriangle className="h-4 w-4 text-red-400" />
    default:
      return <Zap className="h-4 w-4 text-gray-400" />
  }
}

const getEventColor = (type: string) => {
  switch (type) {
    case 'session_started':
      return 'border-blue-400/50 bg-blue-400/10'
    case 'tools_available':
      return 'border-purple-400/50 bg-purple-400/10'
    case 'mcp_servers':
      return 'border-cyan-400/50 bg-cyan-400/10'
    case 'turn_started':
      return 'border-orange-400/50 bg-orange-400/10'
    case 'thinking':
      return 'border-pink-400/50 bg-pink-400/10'
    case 'tool_use':
      return 'border-green-400/50 bg-green-400/10'
    case 'claude_response':
      return 'border-blue-300/50 bg-blue-300/10'
    case 'review_completed':
      return 'border-emerald-400/50 bg-emerald-400/10'
    case 'review_error':
      return 'border-red-400/50 bg-red-400/10'
    default:
      return 'border-gray-400/50 bg-gray-400/10'
  }
}

const getEventTitle = (type: string, data?: any) => {
  switch (type) {
    case 'session_started':
      return 'Review Session Started'
    case 'tools_available':
      return `Tools Available${data?.tools ? ` (${data.tools.length})` : ''}`
    case 'mcp_servers':
      return `MCP Servers${data?.servers ? ` (${data.servers.length})` : ''}`
    case 'turn_started':
      return `Turn ${data?.turn || 'N/A'} Started`
    case 'thinking':
      return 'Claude is Thinking...'
    case 'tool_use':
      return `Using Tool: ${data?.tool || 'Unknown'}`
    case 'claude_response':
      return 'Claude Response'
    case 'review_completed':
      return 'Review Completed Successfully'
    case 'review_error':
      return 'Review Error'
    default:
      return 'Unknown Event'
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('de-DE', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

export const ClaudeProgressTimeline: React.FC<ClaudeProgressTimelineProps> = ({ 
  events, 
  currentStep,
  className = '' 
}) => {
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set())
  const [showAllEvents, setShowAllEvents] = useState(false)

  const toggleEventExpansion = (eventId: string) => {
    const newExpanded = new Set(expandedEvents)
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId)
    } else {
      newExpanded.add(eventId)
    }
    setExpandedEvents(newExpanded)
  }

  // Show last 10 events by default, or all if toggled
  const displayEvents = showAllEvents ? events : events.slice(-10)
  const hasMoreEvents = events.length > 10

  if (events.length === 0) {
    return (
      <Card className={`${className} border-dashed border-gray-600`}>
        <CardContent className="pt-6">
          <div className="text-center text-gray-400">
            <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Waiting for Claude progress updates...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`${className} border-gray-700 bg-gray-900/50`}>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-400" />
            Live Claude Progress
          </h3>
          {hasMoreEvents && (
            <button
              onClick={() => setShowAllEvents(!showAllEvents)}
              className="text-xs text-gray-400 hover:text-gray-300 transition-colors"
            >
              {showAllEvents ? `Hide older (${events.length - 10})` : `Show all (${events.length})`}
            </button>
          )}
        </div>

        <div className="space-y-3 max-h-80 overflow-y-auto custom-scrollbar">
          {displayEvents.map((event, index) => {
            const isExpanded = expandedEvents.has(event.id)
            const hasDetails = event.data && Object.keys(event.data).length > 0
            const isLast = index === displayEvents.length - 1
            
            return (
              <div key={event.id} className="relative">
                {/* Timeline line */}
                {!isLast && (
                  <div className="absolute left-5 top-10 bottom-0 w-px bg-gradient-to-b from-gray-600 to-transparent" />
                )}
                
                <div 
                  className={`
                    flex items-start gap-3 p-3 rounded-lg border transition-all duration-300
                    ${getEventColor(event.type)}
                    ${event.type === 'thinking' ? 'animate-pulse' : ''}
                    hover:scale-[1.01] cursor-pointer
                  `}
                  onClick={() => hasDetails && toggleEventExpansion(event.id)}
                >
                  {/* Icon */}
                  <div className="flex-shrink-0 mt-0.5">
                    {getEventIcon(event.type)}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-white">
                        {getEventTitle(event.type, event.data)}
                      </h4>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-400">
                          {formatTime(event.timestamp)}
                        </span>
                        {hasDetails && (
                          <div className="text-gray-400">
                            {isExpanded ? (
                              <ChevronDown className="h-3 w-3" />
                            ) : (
                              <ChevronRight className="h-3 w-3" />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-xs text-gray-300 mt-1 break-words">
                      {event.message}
                    </p>
                    
                    {/* Expanded details */}
                    {hasDetails && isExpanded && (
                      <div className="mt-3 pt-3 border-t border-gray-700/50">
                        <div className="grid grid-cols-1 gap-2 text-xs">
                          {event.data.preview && (
                            <div>
                              <span className="text-gray-400">Preview:</span>
                              <p className="text-gray-300 mt-1 font-mono bg-gray-800/50 p-2 rounded">
                                {event.data.preview}
                              </p>
                            </div>
                          )}
                          {event.data.tool && (
                            <div>
                              <span className="text-gray-400">Tool:</span>
                              <span className="text-green-400 ml-2 font-mono">{event.data.tool}</span>
                            </div>
                          )}
                          {event.data.turn && (
                            <div>
                              <span className="text-gray-400">Turn:</span>
                              <span className="text-orange-400 ml-2">{event.data.turn}</span>
                            </div>
                          )}
                          {event.data.cost && (
                            <div>
                              <span className="text-gray-400">Cost:</span>
                              <span className="text-yellow-400 ml-2">${event.data.cost}</span>
                            </div>
                          )}
                          {event.data.duration && (
                            <div>
                              <span className="text-gray-400">Duration:</span>
                              <span className="text-blue-400 ml-2">{event.data.duration}s</span>
                            </div>
                          )}
                          {event.data.tools && (
                            <div>
                              <span className="text-gray-400">Available Tools:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {event.data.tools.slice(0, 5).map((tool: string) => (
                                  <span 
                                    key={tool}
                                    className="px-2 py-1 bg-purple-400/20 text-purple-300 rounded text-xs"
                                  >
                                    {tool}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                          {event.data.servers && (
                            <div>
                              <span className="text-gray-400">MCP Servers:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {event.data.servers.slice(0, 3).map((server: string) => (
                                  <span 
                                    key={server}
                                    className="px-2 py-1 bg-cyan-400/20 text-cyan-300 rounded text-xs"
                                  >
                                    {server}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Status indicator */}
        {currentStep && (
          <div className="mt-4 pt-4 border-t border-gray-700">
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Clock className="h-4 w-4 text-blue-400 animate-pulse" />
              <span>Current: {currentStep}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}