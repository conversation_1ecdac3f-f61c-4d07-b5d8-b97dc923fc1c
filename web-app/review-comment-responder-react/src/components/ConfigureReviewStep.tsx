import React from 'react'
import { Card, CardContent } from './ui/card'
import { ReviewModeSelector, type ReviewMode } from './ReviewModeSelector'

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  acceptance_criteria_count: number
}

interface ConfigureReviewStepProps {
  selectedPR: AssignedPR | null
  selectedTicket: AssignedTicket | null
  mode: ReviewMode
  repositoryPath: string
  onModeSelect: (mode: ReviewMode) => void
  onStartReview: () => Promise<void>
  isStartingReview: boolean
}

export const ConfigureReviewStep: React.FC<ConfigureReviewStepProps> = ({
  selectedPR,
  selectedTicket,
  mode,
  repositoryPath,
  onModeSelect,
  onStartReview,
  isStartingReview
}) => {
  return (
    <div className="space-y-6">
      {/* Selected Work Summary */}
      <Card className="border-primary/20 bg-primary/5">
        <CardContent className="pt-6">
          <h3 className="font-medium text-foreground mb-3">Selected for Review</h3>
          <div className="space-y-2 text-sm">
            {selectedPR && (
              <div>
                <strong>Pull Request:</strong> #{selectedPR.id} {selectedPR.title}
                <div className="text-muted-foreground ml-4">
                  Branch: {selectedPR.branch} • Repository: {selectedPR.repository}
                </div>
              </div>
            )}
            {selectedTicket && (
              <div>
                <strong>Jira Ticket:</strong> {selectedTicket.ticket_id} - {selectedTicket.summary}
                <div className="text-muted-foreground ml-4">
                  {selectedTicket.acceptance_criteria_count} Acceptance Criteria
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card className="mb-4 border-orange-200 bg-orange-50">
        <CardContent className="pt-4">
          <h4 className="font-medium text-orange-800 mb-2">🔍 Debug Info:</h4>
          <div className="text-sm space-y-1 text-orange-700">
            <div><strong>Selected PR:</strong> {selectedPR ? `#${selectedPR.id} ${selectedPR.title}` : '❌ No PR selected'}</div>
            <div><strong>Selected Ticket:</strong> {selectedTicket ? `${selectedTicket.ticket_id} - ${selectedTicket.summary}` : '❌ No Ticket selected'}</div>
            <div><strong>Review Mode:</strong> {mode}</div>
            <div><strong>Repository:</strong> {repositoryPath}</div>
          </div>
          {(!selectedPR || !selectedTicket) && (
            <div className="mt-2 p-2 bg-orange-100 border border-orange-300 rounded text-orange-800 text-sm">
              <strong>⚠️ Warnung:</strong> Du musst sowohl einen PR als auch ein Ticket auswählen, um das Ticket im Review-Kontext zu haben!
            </div>
          )}
        </CardContent>
      </Card>

      <ReviewModeSelector
        selectedMode={mode}
        onModeSelect={onModeSelect}
        onStartReview={onStartReview}
        isStarting={isStartingReview}
        branchName={selectedPR?.branch}
        repositoryPath={repositoryPath}
      />
    </div>
  )
}