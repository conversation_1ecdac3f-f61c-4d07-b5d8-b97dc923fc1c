import React, { useState } from 'react'
import { 
  Zap, 
  CheckCircle, 
  Bug, 
  Shield, 
  Gauge, 
  Search, 
  Settings,
  Clock,
  Target,
  FileSearch
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

export type ReviewMode = 'quick' | 'comprehensive' | 'ac-only' | 'bug-analysis' | 'security' | 'performance'

interface ReviewModeOption {
  id: ReviewMode
  name: string
  description: string
  icon: React.ReactNode
  estimatedTime: string
  focusAreas: string[]
  color: string
  features: string[]
}

interface ReviewModeConfigPanelProps {
  selectedMode: ReviewMode
  onModeSelect: (mode: ReviewMode) => void
  onStartReview: () => void
  isStarting?: boolean
  branchName?: string
  repositoryPath?: string
  className?: string
}

const reviewModes: ReviewModeOption[] = [
  {
    id: 'quick',
    name: 'Quick Review',
    description: 'Fast review focusing on critical issues and obvious problems',
    icon: <Zap className="h-5 w-5" />,
    estimatedTime: '2-3 min',
    focusAreas: ['Critical Issues', 'Security', 'Code Quality'],
    color: 'from-blue-500 to-cyan-500',
    features: [
      'Obvious bugs and logic errors',
      'Security vulnerabilities',
      'Breaking changes',
      'Basic code quality'
    ]
  },
  {
    id: 'comprehensive',
    name: 'Comprehensive Review',
    description: 'Complete review including AC compliance, code quality, security, and testing',
    icon: <Search className="h-5 w-5" />,
    estimatedTime: '8-12 min',
    focusAreas: ['Acceptance Criteria', 'Code Quality', 'Security', 'Testing', 'Performance'],
    color: 'from-purple-500 to-pink-500',
    features: [
      'Full acceptance criteria compliance',
      'Detailed code quality analysis',
      'Security vulnerability scan',
      'Test coverage assessment',
      'Performance considerations'
    ]
  },
  {
    id: 'ac-only',
    name: 'AC Compliance Only',
    description: 'Focus exclusively on acceptance criteria compliance against Jira ticket',
    icon: <CheckCircle className="h-5 w-5" />,
    estimatedTime: '3-5 min',
    focusAreas: ['Acceptance Criteria', 'Business Requirements'],
    color: 'from-green-500 to-emerald-500',
    features: [
      'Jira ticket analysis',
      'AC point-by-point verification',
      'Business requirement compliance',
      'Feature completeness check'
    ]
  },
  {
    id: 'bug-analysis',
    name: 'Bug Detection',
    description: 'Deep analysis for potential bugs, edge cases, and error handling',
    icon: <Bug className="h-5 w-5" />,
    estimatedTime: '5-7 min',
    focusAreas: ['Bug Detection', 'Edge Cases', 'Error Handling', 'Testing'],
    color: 'from-orange-500 to-red-500',
    features: [
      'Logic error detection',
      'Edge case analysis',
      'Exception handling review',
      'Null pointer checks',
      'Race condition detection'
    ]
  },
  {
    id: 'security',
    name: 'Security Focus',
    description: 'Comprehensive security analysis and vulnerability assessment',
    icon: <Shield className="h-5 w-5" />,
    estimatedTime: '6-8 min',
    focusAreas: ['Security', 'Authentication', 'Data Protection', 'Input Validation'],
    color: 'from-red-500 to-rose-500',
    features: [
      'OWASP vulnerability scan',
      'Input validation checks',
      'Authentication/authorization',
      'Data exposure risks',
      'Injection attack prevention'
    ]
  },
  {
    id: 'performance',
    name: 'Performance Review',
    description: 'Performance optimization and scalability analysis',
    icon: <Gauge className="h-5 w-5" />,
    estimatedTime: '4-6 min',
    focusAreas: ['Performance', 'Scalability', 'Optimization', 'Resource Usage'],
    color: 'from-indigo-500 to-purple-500',
    features: [
      'Algorithm efficiency',
      'Database performance',
      'Memory usage optimization',
      'Caching strategies',
      'Load testing considerations'
    ]
  }
]

export const ReviewModeSelector: React.FC<ReviewModeConfigPanelProps> = ({
  selectedMode,
  onModeSelect,
  onStartReview,
  isStarting = false,
  branchName,
  repositoryPath,
  className
}) => {
  const [hoveredMode, setHoveredMode] = useState<ReviewMode | null>(null)

  const selectedModeConfig = reviewModes.find(mode => mode.id === selectedMode)

  const handleModeSelect = (mode: ReviewMode) => {
    onModeSelect(mode)
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="space-y-2">
        <h3 className="text-xl font-semibold text-foreground">Review Configuration</h3>
        <p className="text-sm text-muted-foreground">
          Choose the type of review you want to perform with Claude Code
        </p>
      </div>

      {/* Review Mode Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {reviewModes.map((mode) => {
          const isSelected = selectedMode === mode.id
          const isHovered = hoveredMode === mode.id

          return (
            <Card
              key={mode.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'border-primary shadow-md ring-1 ring-primary/20'
                  : 'border-border hover:border-primary/50 hover:shadow-sm'
              } ${isHovered ? 'scale-[1.02]' : ''}`}
              onClick={() => handleModeSelect(mode.id)}
              onMouseEnter={() => setHoveredMode(mode.id)}
              onMouseLeave={() => setHoveredMode(null)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${mode.color} text-white`}>
                    {mode.icon}
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {mode.estimatedTime}
                  </Badge>
                </div>
                <CardTitle className="text-sm font-medium text-foreground">
                  {mode.name}
                </CardTitle>
                <p className="text-xs text-muted-foreground leading-relaxed">
                  {mode.description}
                </p>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                {/* Focus Areas */}
                <div>
                  <h5 className="text-xs font-medium text-foreground mb-2">Focus Areas</h5>
                  <div className="flex flex-wrap gap-1">
                    {mode.focusAreas.map((area, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {area}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Features */}
                <div>
                  <h5 className="text-xs font-medium text-foreground mb-2">What it checks</h5>
                  <ul className="space-y-1">
                    {mode.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-start gap-1">
                        <span className="text-primary mt-1">•</span>
                        <span>{feature}</span>
                      </li>
                    ))}
                    {mode.features.length > 3 && (
                      <li className="text-xs text-muted-foreground">
                        + {mode.features.length - 3} more...
                      </li>
                    )}
                  </ul>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Selected Mode Details */}
      {selectedModeConfig && (
        <Card className="border-primary/20 bg-primary/5">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className={`p-2 rounded-lg bg-gradient-to-r ${selectedModeConfig.color} text-white`}>
                {selectedModeConfig.icon}
              </div>
              <div>
                <h4 className="text-lg font-medium text-foreground">
                  {selectedModeConfig.name} Selected
                </h4>
                <p className="text-sm text-muted-foreground font-normal">
                  {selectedModeConfig.description}
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Review Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <h5 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    All Features
                  </h5>
                  <ul className="space-y-1">
                    {selectedModeConfig.features.map((feature, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                        <CheckCircle className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <h5 className="text-sm font-medium text-foreground mb-2 flex items-center gap-2">
                    <Settings className="h-4 w-4" />
                    Review Settings
                  </h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="text-muted-foreground">Estimated time:</span>
                      <Badge variant="secondary">{selectedModeConfig.estimatedTime}</Badge>
                    </div>
                    {branchName && (
                      <div className="flex items-center gap-2">
                        <FileSearch className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">Branch:</span>
                        <span className="font-mono text-xs bg-muted px-1 py-0.5 rounded">
                          {branchName}
                        </span>
                      </div>
                    )}
                    {repositoryPath && (
                      <div className="flex items-center gap-2">
                        <Settings className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">Repository:</span>
                        <span className="font-mono text-xs bg-muted px-1 py-0.5 rounded truncate">
                          {repositoryPath.split('/').pop()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Start Review Button */}
            <div className="flex justify-end pt-4 border-t border-border">
              <Button
                onClick={onStartReview}
                disabled={isStarting || !branchName}
                className="flex items-center gap-2"
                size="lg"
              >
                {isStarting ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                    Starting Review...
                  </>
                ) : (
                  <>
                    {selectedModeConfig.icon}
                    Start {selectedModeConfig.name}
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      <div className="text-center text-sm text-muted-foreground">
        <p>
          💡 <strong>Tip:</strong> The comprehensive review is recommended for production code changes.
          Use quick review for draft PRs or urgent fixes.
        </p>
      </div>
    </div>
  )
}