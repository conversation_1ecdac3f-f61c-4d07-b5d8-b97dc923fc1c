import React, { Component } from 'react'
import type { ErrorInfo, ReactNode } from 'react'
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { <PERSON><PERSON> } from './ui/button'
import { Badge } from './ui/badge'
import { errorHandler } from '../services/errors/ErrorHandler'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to our error handler
    const enhancedError = errorHandler.handleError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      additionalData: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true
      }
    })

    this.setState({
      errorInfo,
      errorId: enhancedError.id
    })

    // Call optional error callback
    this.props.onError?.(error, errorInfo)
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    })
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  copyErrorDetails = () => {
    const { error, errorInfo, errorId } = this.state
    
    const errorDetails = {
      errorId,
      timestamp: new Date().toISOString(),
      error: {
        name: error?.name,
        message: error?.message,
        stack: error?.stack
      },
      componentStack: errorInfo?.componentStack,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => alert('Error details copied to clipboard'))
      .catch(() => alert('Failed to copy error details'))
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-destructive">
                <div className="p-2 rounded-lg bg-destructive/10 border border-destructive/20">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold">Something went wrong</h1>
                  <p className="text-sm text-muted-foreground font-normal">
                    An unexpected error occurred in the application
                  </p>
                </div>
              </CardTitle>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Error Summary */}
              <div className="p-4 bg-destructive/5 border border-destructive/20 rounded-lg">
                <div className="flex items-start gap-3 mb-3">
                  <Bug className="h-4 w-4 text-destructive shrink-0 mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-destructive text-sm">
                      {this.state.error?.name || 'Error'}
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      {this.state.error?.message || 'An unknown error occurred'}
                    </p>
                  </div>
                  {this.state.errorId && (
                    <Badge variant="outline" className="shrink-0 text-xs">
                      ID: {this.state.errorId.split('_')[1]?.substring(0, 8)}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Error Details (expandable) */}
              {this.props.showDetails && this.state.error && (
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                    Technical Details
                  </summary>
                  <div className="mt-3 p-4 bg-muted/50 rounded-lg">
                    <pre className="text-xs text-muted-foreground overflow-auto whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                    
                    {this.state.errorInfo?.componentStack && (
                      <div className="mt-4 pt-4 border-t border-border">
                        <h4 className="text-xs font-medium text-muted-foreground mb-2">
                          Component Stack:
                        </h4>
                        <pre className="text-xs text-muted-foreground overflow-auto whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              {/* Actions */}
              <div className="flex flex-wrap gap-3">
                <Button 
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={this.handleReload}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Reload Page
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>

                {this.props.showDetails && (
                  <Button 
                    variant="ghost"
                    onClick={this.copyErrorDetails}
                    className="flex items-center gap-2 text-xs"
                  >
                    <Bug className="h-3 w-3" />
                    Copy Error Details
                  </Button>
                )}
              </div>

              {/* Help Text */}
              <div className="text-sm text-muted-foreground space-y-2">
                <p>
                  If this error persists, try the following:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Clear your browser cache and cookies</li>
                  <li>Disable browser extensions temporarily</li>
                  <li>Try using an incognito/private browsing window</li>
                  <li>Check your internet connection</li>
                  {this.state.errorId && (
                    <li>
                      Report this issue with error ID: 
                      <code className="ml-1 px-1 py-0.5 bg-muted rounded text-xs">
                        {this.state.errorId.split('_')[1]?.substring(0, 8)}
                      </code>
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook for functional components to handle errors
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: any) => {
    errorHandler.handleError(error, {
      component: 'useErrorHandler',
      action: 'handleError',
      additionalData: context
    })
  }

  return { handleError }
}

// Higher-order component for error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}