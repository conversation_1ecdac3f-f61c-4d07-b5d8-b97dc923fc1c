import React, { Component } from 'react'
import type { ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug, Send } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { <PERSON><PERSON> } from './ui/button'
import { Badge } from './ui/badge'
import { errorHandler } from '../services/errors/ErrorHandler'
import { errorTracker, performanceMonitor } from '../utils/monitoring'
import { logger } from '../utils/logger'
import { env } from '../config/environment'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
  showDetails?: boolean
  isolateError?: boolean // Prevent error propagation to parent boundaries
  enableRetry?: boolean // Allow retry functionality
  maxRetries?: number // Maximum retry attempts
  component?: string // Component name for monitoring
  enableReporting?: boolean // Enable error reporting
  customActions?: Array<{
    label: string
    action: () => void
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  }>
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string | null
  retryCount: number
  isRetrying: boolean
  errorSeverity: 'low' | 'medium' | 'high' | 'critical'
  userAgent: string
  timestamp: number
  componentStack?: string
  errorReported: boolean
}

export class ErrorBoundary extends Component<Props, State> {
  private componentLogger = logger.child('ErrorBoundary')
  private retryTimeout?: NodeJS.Timeout

  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      isRetrying: false,
      errorSeverity: 'medium',
      userAgent: navigator.userAgent,
      timestamp: Date.now(),
      errorReported: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    const errorSeverity = ErrorBoundary.determineErrorSeverity(error)
    
    return {
      hasError: true,
      error,
      errorSeverity,
      timestamp: Date.now()
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const startTime = performance.now()
    
    try {
      // Generate unique error ID
      const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // Enhanced error tracking with monitoring integration
      const enhancedError = errorHandler.handleError(error, {
        component: this.props.component || 'ErrorBoundary',
        action: 'componentDidCatch',
        additionalData: {
          componentStack: errorInfo.componentStack,
          errorBoundary: true,
          props: this.props.component ? { component: this.props.component } : {},
          retryCount: this.state.retryCount,
          errorSeverity: this.state.errorSeverity
        }
      })

      // Track error in monitoring system
      errorTracker.trackError(error, {
        component: this.props.component || 'ErrorBoundary',
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        retryCount: this.state.retryCount,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now()
      }, this.state.errorSeverity)

      // Add breadcrumb for context
      errorTracker.addBreadcrumb(
        `Error caught by ErrorBoundary: ${error.message}`,
        'error_boundary',
        'error',
        {
          component: this.props.component,
          errorName: error.name,
          retryCount: this.state.retryCount
        }
      )

      // Performance tracking
      const processingTime = performance.now() - startTime
      performanceMonitor.recordMetric('error_boundary_processing_time', processingTime, {
        component: this.props.component || 'ErrorBoundary',
        errorType: error.name
      })

      // Structured logging
      this.componentLogger.error('Error caught by boundary', error, {
        component: this.props.component,
        componentStack: errorInfo.componentStack,
        retryCount: this.state.retryCount,
        errorSeverity: this.state.errorSeverity,
        processingTime: processingTime.toFixed(2)
      })

      // Update state with error details
      this.setState({
        errorInfo,
        errorId: enhancedError.id || errorId || 'unknown',
        componentStack: errorInfo.componentStack || '',
        errorReported: true
      })

      // Call optional error callback
      this.props.onError?.(error, errorInfo)

      // Critical errors - notify immediately in production
      if (this.state.errorSeverity === 'critical' && env.isProduction) {
        this.reportCriticalError(error, errorInfo)
      }

    } catch (handlingError) {
      // Fallback if error handling itself fails
      console.error('Error in ErrorBoundary componentDidCatch:', handlingError)
      console.error('Original error:', error)
      
      this.setState({
        errorInfo,
        errorId: `fallback_${Date.now()}`,
        errorReported: false
      })
    }
  }

  /**
   * Determine error severity based on error characteristics
   */
  static determineErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const errorMessage = error.message.toLowerCase()
    const errorName = error.name.toLowerCase()

    // Critical errors that break core functionality
    if (
      errorName.includes('chunk') ||
      errorMessage.includes('loading chunk') ||
      errorMessage.includes('network error') ||
      errorMessage.includes('script error') ||
      errorMessage.includes('out of memory')
    ) {
      return 'critical'
    }

    // High severity errors
    if (
      errorName.includes('reference') ||
      errorName.includes('type') ||
      errorMessage.includes('cannot read property') ||
      errorMessage.includes('is not a function') ||
      errorMessage.includes('permission denied')
    ) {
      return 'high'
    }

    // Medium severity (default)
    if (
      errorMessage.includes('fetch') ||
      errorMessage.includes('timeout') ||
      errorMessage.includes('abort')
    ) {
      return 'medium'
    }

    // Low severity for minor issues
    return 'low'
  }

  /**
   * Report critical errors immediately
   */
  private async reportCriticalError(error: Error, errorInfo: ErrorInfo): Promise<void> {
    try {
      // In production, send critical errors to monitoring service immediately
      const criticalErrorReport = {
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        errorInfo,
        component: this.props.component,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        severity: 'critical'
      }

      // Send to monitoring endpoint (implement actual endpoint)
      // await fetch('/api/critical-errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(criticalErrorReport)
      // })

      this.componentLogger.fatal('Critical error reported', error, criticalErrorReport)
    } catch (reportingError) {
      console.error('Failed to report critical error:', reportingError)
    }
  }

  /**
   * Enhanced retry handler with backoff and limits
   */
  handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3

    if (this.state.retryCount >= maxRetries) {
      this.componentLogger.warn('Maximum retry attempts reached', {
        retryCount: this.state.retryCount,
        maxRetries,
        component: this.props.component
      })
      return
    }

    this.setState({ isRetrying: true })

    // Add exponential backoff for retries
    const backoffDelay = Math.min(1000 * Math.pow(2, this.state.retryCount), 10000)

    this.retryTimeout = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: this.state.retryCount + 1,
        isRetrying: false,
        errorReported: false
      })

      this.componentLogger.info('Error boundary retry attempted', {
        retryCount: this.state.retryCount + 1,
        backoffDelay,
        component: this.props.component
      })

      // Track retry in monitoring
      performanceMonitor.recordMetric('error_boundary_retry', 1, {
        component: this.props.component || 'ErrorBoundary',
        retryCount: (this.state.retryCount + 1).toString()
      })
    }, backoffDelay)
  }

  handleReload = () => {
    this.componentLogger.info('Page reload requested by user', {
      component: this.props.component,
      errorId: this.state.errorId,
      retryCount: this.state.retryCount
    })
    
    // Track page reload
    performanceMonitor.recordMetric('error_boundary_page_reload', 1, {
      component: this.props.component || 'ErrorBoundary',
      errorSeverity: this.state.errorSeverity
    })
    
    window.location.reload()
  }

  handleGoHome = () => {
    this.componentLogger.info('Navigation to home requested by user', {
      component: this.props.component,
      errorId: this.state.errorId
    })
    
    // Track home navigation
    performanceMonitor.recordMetric('error_boundary_go_home', 1, {
      component: this.props.component || 'ErrorBoundary'
    })
    
    window.location.href = '/'
  }

  /**
   * Enhanced error details copying with more context
   */
  copyErrorDetails = () => {
    const { error, errorInfo, errorId, retryCount, errorSeverity, timestamp } = this.state
    
    const errorDetails = {
      // Basic error info
      errorId,
      timestamp: new Date(timestamp).toISOString(),
      severity: errorSeverity,
      retryCount,
      
      // Error details
      error: {
        name: error?.name,
        message: error?.message,
        stack: env.features.debugMode ? error?.stack : '[REDACTED IN PRODUCTION]'
      },
      
      // Component context
      component: this.props.component,
      componentStack: env.features.debugMode ? errorInfo?.componentStack : '[REDACTED IN PRODUCTION]',
      
      // Environment info
      environment: {
        userAgent: env.monitoring.anonymizeData ? '[ANONYMIZED]' : navigator.userAgent,
        url: env.monitoring.anonymizeData ? window.location.pathname : window.location.href,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        timestamp: Date.now(),
        buildVersion: env.deployment.version,
        buildTimestamp: env.deployment.buildTimestamp
      },
      
      // Feature flags context
      features: env.features.debugMode ? {
        multiAgentEnabled: env.features.multiAgentReviews,
        debugMode: env.features.debugMode,
        errorTracking: env.monitoring.enableErrorTracking
      } : '[REDACTED IN PRODUCTION]'
    }

    const errorText = JSON.stringify(errorDetails, null, 2)
    
    if (navigator.clipboard) {
      navigator.clipboard.writeText(errorText)
        .then(() => {
          alert('Error details copied to clipboard')
          this.componentLogger.info('Error details copied to clipboard', { errorId })
        })
        .catch(() => {
          this.fallbackCopyErrorDetails(errorText)
        })
    } else {
      this.fallbackCopyErrorDetails(errorText)
    }
  }

  /**
   * Fallback method for copying error details
   */
  private fallbackCopyErrorDetails = (errorText: string) => {
    try {
      const textArea = document.createElement('textarea')
      textArea.value = errorText
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('Error details copied to clipboard')
    } catch (fallbackError) {
      console.error('Failed to copy error details:', fallbackError)
      alert('Failed to copy error details. Please check the console for details.')
      console.log('Error Details:', errorText)
    }
  }

  /**
   * Send error report to support
   */
  sendErrorReport = async () => {
    if (!this.props.enableReporting || this.state.errorReported) return

    try {
      const errorReport = {
        errorId: this.state.errorId,
        error: {
          name: this.state.error?.name,
          message: this.state.error?.message,
          stack: this.state.error?.stack
        },
        component: this.props.component,
        severity: this.state.errorSeverity,
        retryCount: this.state.retryCount,
        timestamp: this.state.timestamp,
        userAgent: navigator.userAgent,
        url: window.location.href,
        buildVersion: env.deployment.version
      }

      // In a real implementation, send to error reporting service
      // const response = await fetch('/api/error-reports', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // })

      this.componentLogger.info('Error report sent', { 
        errorId: this.state.errorId,
        reportData: errorReport 
      })
      alert('Error report sent successfully')
      
    } catch (reportError) {
      this.componentLogger.error('Failed to send error report', reportError instanceof Error ? reportError : new Error(String(reportError)))
      alert('Failed to send error report')
    }
  }

  /**
   * Cleanup on unmount
   */
  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout)
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-destructive">
                <div className="p-2 rounded-lg bg-destructive/10 border border-destructive/20">
                  <AlertTriangle className="h-6 w-6 text-destructive" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold">Something went wrong</h1>
                  <p className="text-sm text-muted-foreground font-normal">
                    An unexpected error occurred in the application
                  </p>
                </div>
              </CardTitle>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* Error Summary with Enhanced Details */}
              <div className="p-4 bg-destructive/5 border border-destructive/20 rounded-lg">
                <div className="flex items-start gap-3 mb-3">
                  <Bug className="h-4 w-4 text-destructive shrink-0 mt-0.5" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-destructive text-sm">
                        {this.state.error?.name || 'Error'}
                      </h3>
                      <Badge 
                        variant={
                          this.state.errorSeverity === 'critical' ? 'destructive' :
                          this.state.errorSeverity === 'high' ? 'destructive' :
                          this.state.errorSeverity === 'medium' ? 'secondary' : 'outline'
                        }
                        className="text-xs"
                      >
                        {this.state.errorSeverity.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {this.state.error?.message || 'An unknown error occurred'}
                    </p>
                    {this.props.component && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Component: <code className="bg-muted px-1 rounded">{this.props.component}</code>
                      </p>
                    )}
                    {this.state.retryCount > 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Retry attempts: {this.state.retryCount} / {this.props.maxRetries || 3}
                      </p>
                    )}
                  </div>
                  {this.state.errorId && (
                    <div className="shrink-0 flex flex-col gap-1">
                      <Badge variant="outline" className="text-xs">
                        ID: {this.state.errorId.split('_')[1]?.substring(0, 8)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {new Date(this.state.timestamp).toLocaleTimeString()}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>

              {/* Error Details (expandable) */}
              {this.props.showDetails && this.state.error && (
                <details className="group">
                  <summary className="cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                    Technical Details
                  </summary>
                  <div className="mt-3 p-4 bg-muted/50 rounded-lg">
                    <pre className="text-xs text-muted-foreground overflow-auto whitespace-pre-wrap">
                      {this.state.error.stack}
                    </pre>
                    
                    {this.state.errorInfo?.componentStack && (
                      <div className="mt-4 pt-4 border-t border-border">
                        <h4 className="text-xs font-medium text-muted-foreground mb-2">
                          Component Stack:
                        </h4>
                        <pre className="text-xs text-muted-foreground overflow-auto whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}

              {/* Actions */}
              <div className="flex flex-wrap gap-3">
                {(this.props.enableRetry !== false) && (
                  <Button 
                    onClick={this.handleRetry}
                    disabled={this.state.isRetrying || this.state.retryCount >= (this.props.maxRetries || 3)}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${this.state.isRetrying ? 'animate-spin' : ''}`} />
                    {this.state.isRetrying ? 'Retrying...' : 'Try Again'}
                    {this.state.retryCount > 0 && (
                      <Badge variant="outline" className="ml-1 text-xs">
                        {this.state.retryCount}/{this.props.maxRetries || 3}
                      </Badge>
                    )}
                  </Button>
                )}
                
                <Button 
                  variant="outline"
                  onClick={this.handleReload}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Reload Page
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={this.handleGoHome}
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>

                {/* Custom Actions */}
                {this.props.customActions?.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant || 'outline'}
                    onClick={action.action}
                    className="flex items-center gap-2"
                  >
                    {action.label}
                  </Button>
                ))}

                {/* Error Reporting */}
                {this.props.enableReporting && env.monitoring.enableErrorTracking && (
                  <Button 
                    variant="ghost"
                    onClick={this.sendErrorReport}
                    disabled={this.state.errorReported}
                    className="flex items-center gap-2 text-xs"
                  >
                    <Send className="h-3 w-3" />
                    {this.state.errorReported ? 'Report Sent' : 'Send Report'}
                  </Button>
                )}

                {this.props.showDetails && (
                  <Button 
                    variant="ghost"
                    onClick={this.copyErrorDetails}
                    className="flex items-center gap-2 text-xs"
                  >
                    <Bug className="h-3 w-3" />
                    Copy Error Details
                  </Button>
                )}
              </div>

              {/* Help Text */}
              <div className="text-sm text-muted-foreground space-y-2">
                <p>
                  If this error persists, try the following:
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>Clear your browser cache and cookies</li>
                  <li>Disable browser extensions temporarily</li>
                  <li>Try using an incognito/private browsing window</li>
                  <li>Check your internet connection</li>
                  {this.state.errorId && (
                    <li>
                      Report this issue with error ID: 
                      <code className="ml-1 px-1 py-0.5 bg-muted rounded text-xs">
                        {this.state.errorId.split('_')[1]?.substring(0, 8)}
                      </code>
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook for functional components to handle errors
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: any) => {
    errorHandler.handleError(error, {
      component: 'useErrorHandler',
      action: 'handleError',
      additionalData: context
    })
  }

  return { handleError }
}

// Higher-order component for error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}