import React, { useState, useEffect } from 'react'
import Editor from '@monaco-editor/react'
import { Save, RotateCcw } from 'lucide-react'
import { Button } from './ui/button'

interface CodeChange {
  type: string
  line_start: number
  line_end: number
  current_content: string
  suggested_content: string
  explanation: string
}

interface CodeDiffEditorProps {
  currentCode: string
  suggestedCode: string
  change: CodeChange
  language?: string
  onCodeChange?: (newCode: string) => void
  onSave?: (finalCode: string) => void
  onReset?: () => void
}

export const CodeDiffEditor: React.FC<CodeDiffEditorProps> = ({
  currentCode,
  suggestedCode,
  change,
  language = 'typescript',
  onCodeChange,
  onSave,
  onReset
}) => {
  const [editedCode, setEditedCode] = useState(suggestedCode)
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    setEditedCode(suggestedCode)
    setHasChanges(false)
  }, [suggestedCode])

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setEditedCode(value)
      setHasChanges(value !== suggestedCode)
      onCodeChange?.(value)
    }
  }

  const handleSave = () => {
    onSave?.(editedCode)
    setHasChanges(false)
  }

  const handleReset = () => {
    setEditedCode(suggestedCode)
    setHasChanges(false)
    onReset?.()
  }

  return (
    <div className="space-y-4">
      {change.explanation && (
        <div className="text-sm text-muted-foreground italic p-2 bg-muted/50 rounded">
          💡 {change.explanation}
        </div>
      )}
      
      <div className="text-sm font-medium">
        Lines {change.line_start}-{change.line_end}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Current Code (Read-only) */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h5 className="text-xs font-medium text-red-600">Current</h5>
          </div>
          <div className="border rounded-lg overflow-hidden">
            <Editor
              height="200px"
              language={language}
              value={currentCode}
              theme="vs-dark"
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: 'on',
                fontSize: 12,
                wordWrap: 'on',
                automaticLayout: true
              }}
            />
          </div>
        </div>

        {/* Suggested Code (Editable) */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h5 className="text-xs font-medium text-green-600">
              Suggested {hasChanges && <span className="text-orange-600">(Modified)</span>}
            </h5>
            <div className="flex gap-1">
              {hasChanges && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleReset}
                    className="text-xs px-2 py-1 h-auto"
                  >
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    className="text-xs px-2 py-1 h-auto"
                  >
                    <Save className="h-3 w-3 mr-1" />
                    Save
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="border rounded-lg overflow-hidden">
            <Editor
              height="200px"
              language={language}
              value={editedCode}
              onChange={handleEditorChange}
              theme="vs-dark"
              options={{
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: 'on',
                fontSize: 12,
                wordWrap: 'on',
                automaticLayout: true,
                formatOnType: true,
                formatOnPaste: true
              }}
            />
          </div>
        </div>
      </div>

      {hasChanges && (
        <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
          ⚠️ You have unsaved changes. Click "Save" to apply your modifications.
        </div>
      )}
    </div>
  )
}