/**
 * Unit Tests for MultiAgentResultsAggregator Component
 * Testing comprehensive results display from all 7 parallel agents
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { MultiAgentResultsAggregator } from '../MultiAgentResultsAggregator'
import type { EnhancedReviewResults } from '../../types/multi-agent'

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  BarChart3: ({ className }: { className?: string }) => <div data-testid="bar-chart-icon" className={className} />,
  CheckCircle: ({ className }: { className?: string }) => <div data-testid="check-circle-icon" className={className} />,
  AlertTriangle: ({ className }: { className?: string }) => <div data-testid="alert-triangle-icon" className={className} />,
  Users: ({ className }: { className?: string }) => <div data-testid="users-icon" className={className} />,
  Brain: ({ className }: { className?: string }) => <div data-testid="brain-icon" className={className} />,
  FileText: ({ className }: { className?: string }) => <div data-testid="file-text-icon" className={className} />,
  Zap: ({ className }: { className?: string }) => <div data-testid="zap-icon" className={className} />,
  Target: ({ className }: { className?: string }) => <div data-testid="target-icon" className={className} />,
  Download: ({ className }: { className?: string }) => <div data-testid="download-icon" className={className} />,
  ExternalLink: ({ className }: { className?: string }) => <div data-testid="external-link-icon" className={className} />,
  ChevronDown: ({ className }: { className?: string }) => <div data-testid="chevron-down-icon" className={className} />,
  ChevronRight: ({ className }: { className?: string }) => <div data-testid="chevron-right-icon" className={className} />,
  Shield: ({ className }: { className?: string }) => <div data-testid="shield-icon" className={className} />,
  Bug: ({ className }: { className?: string }) => <div data-testid="bug-icon" className={className} />,
  Settings: ({ className }: { className?: string }) => <div data-testid="settings-icon" className={className} />
}))

// Mock UI Components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card" className={className}>{children}</div>,
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-content" className={className}>{children}</div>,
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-header" className={className}>{children}</div>,
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-title" className={className}>{children}</div>
}))

vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, variant, className }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    variant?: string; 
    className?: string 
  }) => 
    <button data-testid="button" data-variant={variant} className={className} onClick={onClick}>
      {children}
    </button>
}))

vi.mock('../ui/badge', () => ({
  Badge: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => 
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
}))

vi.mock('../ui/progress', () => ({
  Progress: ({ value, className }: { value: number; className?: string }) => 
    <div data-testid="progress" data-value={value} className={className} />
}))

vi.mock('../ui/alert', () => ({
  Alert: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="alert" className={className}>{children}</div>,
  AlertDescription: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="alert-description">{children}</div>
}))

vi.mock('../ui/tabs', () => ({
  Tabs: ({ children, defaultValue }: { children: React.ReactNode; defaultValue?: string }) => 
    <div data-testid="tabs" data-default-value={defaultValue}>{children}</div>,
  TabsContent: ({ children, value }: { children: React.ReactNode; value: string }) => 
    <div data-testid="tabs-content" data-value={value}>{children}</div>,
  TabsList: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value }: { children: React.ReactNode; value: string }) => 
    <button data-testid="tabs-trigger" data-value={value}>{children}</button>
}))

vi.mock('../ui/collapsible', () => ({
  Collapsible: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="collapsible">{children}</div>,
  CollapsibleContent: ({ children }: { children: React.ReactNode }) => 
    <div data-testid="collapsible-content">{children}</div>,
  CollapsibleTrigger: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => 
    <button data-testid="collapsible-trigger" onClick={onClick}>{children}</button>
}))

// Mock FindingsCategorizer component
vi.mock('../FindingsCategorizer', () => ({
  FindingsCategorizer: ({ findings, onFileClick }: { 
    findings: any[]; 
    onFileClick?: (filename: string, line?: number) => void 
  }) => 
    <div data-testid="findings-categorizer" data-findings-count={findings.length}>
      Findings Categorizer
      {onFileClick && <div data-testid="file-click-handler">File click handler available</div>}
    </div>
}))

describe('MultiAgentResultsAggregator', () => {
  let mockResults: EnhancedReviewResults
  let mockOnFileClick: ReturnType<typeof vi.fn>
  let mockOnExportResults: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockOnFileClick = vi.fn()
    mockOnExportResults = vi.fn()

    mockResults = {
      review_id: 'test-review-123',
      session_id: 'test-session-456',
      status: 'completed',
      overall_results: {
        summary: 'Multi-agent review completed successfully',
        priority_findings: [
          {
            id: 'finding-1',
            type: 'bug',
            severity: 'high',
            priority: 'high',
            agent_type: 'bug_detection',
            title: 'Potential null pointer exception',
            description: 'Variable may be null before use',
            file_path: 'src/components/test.ts',
            line_number: 42,
            code_snippet: 'const result = obj.property;',
            recommendation: 'Add null check before accessing property'
          },
          {
            id: 'finding-2',
            type: 'security',
            severity: 'medium',
            priority: 'medium',
            agent_type: 'security_analysis',
            title: 'Potential SQL injection',
            description: 'User input not properly sanitized',
            file_path: 'src/utils/database.ts',
            line_number: 15,
            code_snippet: 'query = "SELECT * FROM users WHERE id = " + userId;',
            recommendation: 'Use parameterized queries'
          }
        ],
        execution_metrics: {
          total_execution_time: 180.5,
          parallel_efficiency: 85.2,
          agent_performance: {
            acceptance_criteria: {
              execution_time: 45.3,
              success: true,
              findings_count: 3
            },
            bug_detection: {
              execution_time: 62.1,
              success: true,
              findings_count: 5
            },
            security_analysis: {
              execution_time: 55.8,
              success: true,
              findings_count: 2
            },
            logic_analysis: {
              execution_time: 38.9,
              success: true,
              findings_count: 1
            },
            quality_analysis: {
              execution_time: 51.2,
              success: true,
              findings_count: 4
            },
            architecture_analysis: {
              execution_time: 67.4,
              success: true,  
              findings_count: 2
            },
            summary: {
              execution_time: 25.1,
              success: true,
              findings_count: 0
            }
          }
        }
      },
      agent_results: {
        acceptance_criteria: {
          agent_type: 'acceptance_criteria',
          status: 'completed',
          started_at: '2024-01-01T10:00:00Z',
          completed_at: '2024-01-01T10:00:45Z',
          execution_time: 45.3,
          findings: [
            {
              id: 'ac-1',
              type: 'acceptance_criteria',
              severity: 'high',
              priority: 'high',
              title: 'Missing validation for user input',
              description: 'Input validation is not implemented',
              file_path: 'src/forms/UserForm.tsx',
              line_number: 25
            }
          ]
        },
        bug_detection: {
          agent_type: 'bug_detection',
          status: 'completed',
          started_at: '2024-01-01T10:00:00Z',
          completed_at: '2024-01-01T10:01:02Z',
          execution_time: 62.1,
          findings: [
            {
              id: 'bug-1',
              type: 'bug',
              severity: 'high',
              priority: 'high',
              title: 'Potential null pointer exception',
              description: 'Variable may be null before use',
              file_path: 'src/components/test.ts',
              line_number: 42
            }
          ]
        }
      },
      reports: {
        markdown: '# Multi-Agent Review Report\n\nResults from 7 parallel agents.',
        json: '{"agents": 7, "findings": 17}'
      },
      context_metadata: {
        files_analyzed: ['src/components/test.ts', 'src/utils/database.ts'],
        context_size: 15000,
        git_history_included: true,
        related_files_included: true
      },
      created_at: '2024-01-01T10:00:00Z',
      completed_at: '2024-01-01T10:03:00Z'
    }
  })

  describe('rendering', () => {
    it('should render performance overview correctly', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
      expect(screen.getByText('7 Parallel Agents')).toBeInTheDocument()
      expect(screen.getByText('3m 1s')).toBeInTheDocument() // Total Time
      expect(screen.getByText('85.2%')).toBeInTheDocument() // Parallel Efficiency
      expect(screen.getByText('7')).toBeInTheDocument() // Successful Agents
      expect(screen.getByText('2')).toBeInTheDocument() // Total Findings
    })

    it('should render agent execution timeline', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByText('Agent Execution Timeline')).toBeInTheDocument()
      expect(screen.getByText('Acceptance Criteria')).toBeInTheDocument()
      expect(screen.getByText('Bug Detection')).toBeInTheDocument()
      expect(screen.getByText('Security Analysis')).toBeInTheDocument()
    })

    it('should render priority findings section', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByTestId('findings-categorizer')).toBeInTheDocument()
      expect(screen.getByTestId('findings-categorizer')).toHaveAttribute('data-findings-count', '2')
    })

    it('should show file click handler availability', () => {
      render(
        <MultiAgentResultsAggregator 
          results={mockResults} 
          onFileClick={mockOnFileClick}
        />
      )
      
      expect(screen.getByTestId('file-click-handler')).toBeInTheDocument()
    })
  })

  describe('agent display helpers', () => {
    it('should return correct agent display names', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByText('Acceptance Criteria')).toBeInTheDocument()
      expect(screen.getByText('Bug Detection')).toBeInTheDocument()
      expect(screen.getByText('Security Analysis')).toBeInTheDocument()
      expect(screen.getByText('Logic Analysis')).toBeInTheDocument()
      expect(screen.getByText('Quality Analysis')).toBeInTheDocument()
      expect(screen.getByText('Architecture Analysis')).toBeInTheDocument()
      expect(screen.getByText('Summary & Recommendations')).toBeInTheDocument()
    })

    it('should render correct agent icons', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByTestId('target-icon')).toBeInTheDocument() // acceptance_criteria
      expect(screen.getByTestId('bug-icon')).toBeInTheDocument() // bug_detection
      expect(screen.getByTestId('shield-icon')).toBeInTheDocument() // security_analysis
      expect(screen.getByTestId('settings-icon')).toBeInTheDocument() // logic_analysis
      expect(screen.getByTestId('bar-chart-icon')).toBeInTheDocument() // quality_analysis
      expect(screen.getByTestId('users-icon')).toBeInTheDocument() // architecture_analysis
      expect(screen.getByTestId('file-text-icon')).toBeInTheDocument() // summary
    })
  })

  describe('performance metrics', () => {
    it('should format execution time correctly', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByText('3m 1s')).toBeInTheDocument() // 180.5 seconds
    })

    it('should format seconds-only time correctly', () => {
      const resultsWithShortTime = {
        ...mockResults,
        overall_results: {
          ...mockResults.overall_results,
          execution_metrics: {
            ...mockResults.overall_results.execution_metrics,
            total_execution_time: 45.2
          }
        }
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithShortTime} />)
      
      expect(screen.getByText('45.2s')).toBeInTheDocument()
    })

    it('should apply correct efficiency color for high efficiency', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      const efficiencyElement = screen.getByText('85.2%')
      expect(efficiencyElement).toHaveClass('text-green-600')
    })

    it('should apply correct efficiency color for medium efficiency', () => {
      const resultsWithMediumEfficiency = {
        ...mockResults,
        overall_results: {
          ...mockResults.overall_results,
          execution_metrics: {
            ...mockResults.overall_results.execution_metrics,
            parallel_efficiency: 65.0
          }
        }
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithMediumEfficiency} />)
      
      const efficiencyElement = screen.getByText('65.0%')
      expect(efficiencyElement).toHaveClass('text-yellow-600')
    })

    it('should apply correct efficiency color for low efficiency', () => {
      const resultsWithLowEfficiency = {
        ...mockResults,
        overall_results: {
          ...mockResults.overall_results,
          execution_metrics: {
            ...mockResults.overall_results.execution_metrics,
            parallel_efficiency: 45.0
          }
        }
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithLowEfficiency} />)
      
      const efficiencyElement = screen.getByText('45.0%')
      expect(efficiencyElement).toHaveClass('text-red-600')
    })
  })

  describe('user interactions', () => {
    it('should handle export results callback', () => {
      render(
        <MultiAgentResultsAggregator 
          results={mockResults} 
          onExportResults={mockOnExportResults}
        />
      )
      
      // Find and click export button (would need to extend component for this)
      // This test verifies the prop is passed correctly
      expect(mockOnExportResults).not.toHaveBeenCalled()
    })

    it('should handle file click callback', () => {
      render(
        <MultiAgentResultsAggregator 
          results={mockResults} 
          onFileClick={mockOnFileClick}
        />
      )
      
      // Verify file click handler is available in FindingsCategorizer
      expect(screen.getByTestId('file-click-handler')).toBeInTheDocument()
    })
  })

  describe('findings filtering', () => {
    it('should show all findings by default', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      const findingsComponent = screen.getByTestId('findings-categorizer')
      expect(findingsComponent).toHaveAttribute('data-findings-count', '2')
    })

    it('should handle empty findings array', () => {
      const resultsWithNoFindings = {
        ...mockResults,
        overall_results: {
          ...mockResults.overall_results,
          priority_findings: []
        }
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithNoFindings} />)
      
      const findingsComponent = screen.getByTestId('findings-categorizer')
      expect(findingsComponent).toHaveAttribute('data-findings-count', '0')
    })
  })

  describe('data validation', () => {
    it('should handle missing agent_results gracefully', () => {
      const resultsWithoutAgentResults = {
        ...mockResults,
        agent_results: undefined
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithoutAgentResults as any} />)
      
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
    })

    it('should handle missing priority_findings gracefully', () => {
      const resultsWithoutFindings = {
        ...mockResults,
        overall_results: {
          ...mockResults.overall_results,
          priority_findings: undefined
        }
      }
      
      render(<MultiAgentResultsAggregator results={resultsWithoutFindings as any} />)
      
      const findingsComponent = screen.getByTestId('findings-categorizer')
      expect(findingsComponent).toHaveAttribute('data-findings-count', '0')
    })
  })

  describe('accessibility', () => {
    it('should have proper semantic structure', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
      expect(screen.getByText('Agent Execution Timeline')).toBeInTheDocument()
      expect(screen.getByText('Total Time')).toBeInTheDocument()
      expect(screen.getByText('Parallel Efficiency')).toBeInTheDocument()
    })

    it('should provide meaningful progress indicators', () => {
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      const progressBars = screen.getAllByTestId('progress')
      expect(progressBars.length).toBeGreaterThan(0)
      
      // All successful agents should have 100% progress
      progressBars.forEach(progress => {
        expect(progress).toHaveAttribute('data-value', '100')
      })
    })
  })

  describe('custom className prop', () => {
    it('should apply custom className to root element', () => {
      const { container } = render(
        <MultiAgentResultsAggregator 
          results={mockResults} 
          className="custom-test-class" 
        />
      )
      
      expect(container.firstChild).toHaveClass('custom-test-class')
    })
  })

  describe('priority color coding', () => {
    it('should apply correct priority colors', () => {
      // Test is implicit through the priority_findings data structure
      // The component should handle high, medium, low priorities correctly
      render(<MultiAgentResultsAggregator results={mockResults} />)
      
      // Verify component renders without errors
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
    })
  })
})