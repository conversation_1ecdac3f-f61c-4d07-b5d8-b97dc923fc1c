/**
 * Component Integration Tests for Multi-Agent System
 * Testing complex component interactions and data flow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import '@testing-library/jest-dom'
import React from 'react'
import TestDataFactory, { FIXTURES } from '../../../test-utils/test-fixtures'
import { 
  MockMultiAgentReviewService, 
  createMockMultiAgentReviewHook,
  createMockServiceSuite 
} from '../../../test-utils/mock-services'
import { setupWebSocketMock } from '../../../test-utils/mock-websocket'

// Import components to test
import { CodeReviewer } from '../../../pages/CodeReviewer'
import { MultiAgentProgressTracker } from '../../MultiAgentProgressTracker'
import { MultiAgentResultsAggregator } from '../../MultiAgentResultsAggregator'
import { ReviewProgressStep } from '../../ReviewProgressStep'

// Mock all external dependencies
const mockServices = createMockServiceSuite('success')

// Mock hooks
vi.mock('../../../hooks/useAuth', () => ({
  useAuthStatus: () => mockServices.authStatus
}))

vi.mock('../../../contexts/WorktreeStatusContext', () => ({
  useWorktreeStatus: () => mockServices.worktreeStatus
}))

vi.mock('../../../store/useCodeReviewerStore', () => ({
  useCodeReviewerStore: () => ({
    selectPR: mockServices.store.selectPR,
    selectTicket: mockServices.store.selectTicket
  }),
  useCurrentSelection: () => ({
    selectedPR: mockServices.store.selectedPR,
    selectedTicket: mockServices.store.selectedTicket
  }),
  useReviewConfig: () => ({
    mode: mockServices.store.mode,
    repositoryPath: mockServices.store.repositoryPath,
    setMode: mockServices.store.setMode
  }),
  useReviewSession: () => ({
    activeSession: mockServices.store.activeSession,
    progress: mockServices.store.progress,
    addSession: mockServices.store.addSession,
    updateSession: mockServices.store.updateSession
  })
}))

vi.mock('../../../hooks/useMultiAgentReview', () => ({
  useMultiAgentReview: () => mockServices.multiAgentHook
}))

vi.mock('../../../hooks/useWebSocketConnection', () => ({
  useWebSocketConnection: () => mockServices.webSocketHook
}))

vi.mock('../../../hooks/useProgressPolling', () => ({
  useProgressPolling: () => ({})
}))

vi.mock('../../../hooks/useWorkflowAutoAdvance', () => ({
  useWorkflowAutoAdvance: () => ({})
}))

vi.mock('../../../services/multiAgent/MultiAgentReviewService', () => ({
  MultiAgentReviewService: vi.fn().mockImplementation(() => mockServices.multiAgentService)
}))

vi.mock('../../../services/codeReviewer/CodeReviewerService', () => ({
  codeReviewerService: mockServices.codeReviewerService
}))

vi.mock('../../../config/featureFlags', () => ({
  featureFlags: {
    isMultiAgentEnabled: () => true
  }
}))

// Mock UI components that aren't under test
vi.mock('../../CodeReviewerHeader', () => ({
  CodeReviewerHeader: () => <div data-testid="code-reviewer-header">Header</div>
}))

vi.mock('../../AuthenticationPrompt', () => ({
  AuthenticationPrompt: () => <div data-testid="auth-prompt">Please authenticate</div>
}))

vi.mock('../../WorkflowSteps', () => ({
  WorkflowSteps: ({ currentStep, onResetWorkflow }: any) => (
    <div data-testid="workflow-steps" data-current-step={currentStep}>
      <button onClick={onResetWorkflow}>Reset Workflow</button>
    </div>
  )
}))

vi.mock('../../AssignedWorkPanel', () => ({
  AssignedWorkPanel: ({ onStartReview }: any) => (
    <div data-testid="assigned-work-panel">
      <button onClick={() => onStartReview(FIXTURES.pr.basic, FIXTURES.ticket.withAC)}>
        Start Review
      </button>
    </div>
  )
}))

vi.mock('../../ConfigureReviewStep', () => ({
  ConfigureReviewStep: ({ onStartReview, onModeSelect }: any) => (
    <div data-testid="configure-review-step">
      <button onClick={() => onModeSelect('multi_agent')}>Multi-Agent Mode</button>
      <button onClick={onStartReview}>Start Review</button>
    </div>
  )
}))

vi.mock('../../ReviewResultsStep', () => ({
  ReviewResultsStep: () => <div data-testid="review-results-step">Results</div>
}))

vi.mock('../../worktree/WorktreeStatusIndicator', () => ({
  WorktreeStatusIndicator: () => <div data-testid="worktree-indicator">Worktree OK</div>
}))

describe('Component Integration Tests', () => {
  let cleanupWebSocket: () => void

  beforeEach(() => {
    cleanupWebSocket = setupWebSocketMock()
    vi.clearAllMocks()
    
    // Reset store state
    mockServices.store.selectedPR = null
    mockServices.store.selectedTicket = null
    mockServices.store.mode = 'quick'
    mockServices.store.activeSession = null
  })

  afterEach(() => {
    cleanupWebSocket()
  })

  describe('Multi-Agent Progress Tracker Integration', () => {
    it('should display progress tracker with real-time updates', async () => {
      const reviewStatus = FIXTURES.ui.progressTracker.running
      
      render(<MultiAgentProgressTracker reviewStatus={reviewStatus} />)

      // Verify basic rendering
      expect(screen.getByText('Multi-Agent Code Review')).toBeInTheDocument()
      expect(screen.getByText('Running')).toBeInTheDocument()
      expect(screen.getByText('1 agents running in parallel')).toBeInTheDocument()

      // Verify statistics
      expect(screen.getByText('2')).toBeInTheDocument() // Running agents
      expect(screen.getByText('4')).toBeInTheDocument() // Completed agents

      // Verify agent cards are rendered
      expect(screen.getByText('Acceptance Criteria')).toBeInTheDocument()
      expect(screen.getByText('Bug Detection')).toBeInTheDocument()
      expect(screen.getByText('Quality Analysis')).toBeInTheDocument()
    })

    it('should update progress tracker when review status changes', async () => {
      const { rerender } = render(
        <MultiAgentProgressTracker reviewStatus={FIXTURES.ui.progressTracker.running} />
      )

      expect(screen.getByText('Running')).toBeInTheDocument()

      // Update to completed status
      rerender(
        <MultiAgentProgressTracker reviewStatus={FIXTURES.ui.progressTracker.completed} />
      )

      await waitFor(() => {
        expect(screen.getByText('Completed')).toBeInTheDocument()
        expect(screen.getByText('All agents completed successfully')).toBeInTheDocument()
      })
    })

    it('should handle null status gracefully', () => {
      render(<MultiAgentProgressTracker reviewStatus={null} />)
      
      expect(screen.getByText('No multi-agent review in progress')).toBeInTheDocument()
    })
  })

  describe('Multi-Agent Results Aggregator Integration', () => {
    it('should display comprehensive results from all agents', () => {
      const results = FIXTURES.scenarios.successful.results
      const onFileClick = vi.fn()
      const onExportResults = vi.fn()

      render(
        <MultiAgentResultsAggregator 
          results={results}
          onFileClick={onFileClick}
          onExportResults={onExportResults}
        />
      )

      // Verify performance overview
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
      expect(screen.getByText('7 Parallel Agents')).toBeInTheDocument()

      // Verify metrics display
      expect(screen.getByText('Total Time')).toBeInTheDocument()
      expect(screen.getByText('Parallel Efficiency')).toBeInTheDocument()
      expect(screen.getByText('Successful Agents')).toBeInTheDocument()

      // Verify agent timeline is rendered
      expect(screen.getByText('Agent Execution Timeline')).toBeInTheDocument()
    })

    it('should handle large result sets efficiently', () => {
      const largeResults = FIXTURES.performance.largeResultSet
      
      const startTime = performance.now()
      render(<MultiAgentResultsAggregator results={largeResults} />)
      const endTime = performance.now()

      // Should render within reasonable time (< 100ms for large datasets)
      expect(endTime - startTime).toBeLessThan(100)

      // Verify it still renders correctly
      expect(screen.getByText('Multi-Agent Performance Overview')).toBeInTheDocument()
    })
  })

  describe('Review Progress Step Integration', () => {
    it('should switch between legacy and multi-agent progress displays', () => {
      const basicProps = {
        selectedPR: FIXTURES.pr.basic,
        progress: 50,
        progressEvents: [],
        currentProgressStep: 'In progress'
      }

      // Render with legacy mode
      const { rerender } = render(
        <ReviewProgressStep 
          {...basicProps}
          activeSession={TestDataFactory.createReviewSession()}
          mode="quick"
        />
      )

      expect(screen.getByText('Code Review Progress')).toBeInTheDocument()

      // Switch to multi-agent mode
      rerender(
        <ReviewProgressStep 
          {...basicProps}
          activeSession={TestDataFactory.createReviewSession()}
          mode="multi_agent"
        />
      )

      expect(screen.getByText('Multi-Agent Review Progress')).toBeInTheDocument()
    })

    it('should display progress events in chronological order', () => {
      const progressEvents = [
        {
          id: 'event-1',
          type: 'review_started',
          message: 'Review started',
          timestamp: new Date(Date.now() - 2000),
          data: {}
        },
        {
          id: 'event-2', 
          type: 'agent_started',
          message: 'Agent started',
          timestamp: new Date(Date.now() - 1000),
          data: {}
        },
        {
          id: 'event-3',
          type: 'agent_progress',
          message: 'Progress update',
          timestamp: new Date(),
          data: {}
        }
      ]

      render(
        <ReviewProgressStep
          activeSession={TestDataFactory.createReviewSession()}
          selectedPR={FIXTURES.pr.basic}
          progress={75}
          progressEvents={progressEvents}
          currentProgressStep="Processing..."
          mode="multi_agent"
        />
      )

      // Events should be displayed (exact implementation depends on ProgressEventsList component)
      expect(screen.getByText('Processing...')).toBeInTheDocument()
    })
  })

  describe('Full CodeReviewer Workflow Integration', () => {
    it('should handle complete multi-agent review workflow', async () => {
      // Setup successful multi-agent hook
      const successfulHook = createMockMultiAgentReviewHook('running')
      mockServices.multiAgentHook = successfulHook

      render(<CodeReviewer />)

      // Step 1: Select work
      expect(screen.getByTestId('assigned-work-panel')).toBeInTheDocument()
      
      fireEvent.click(screen.getByText('Start Review'))

      // Step 2: Configure review should appear
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })

      // Select multi-agent mode
      fireEvent.click(screen.getByText('Multi-Agent Mode'))
      expect(mockServices.store.setMode).toHaveBeenCalledWith('multi_agent')

      // Start the review
      fireEvent.click(screen.getByText('Start Review'))

      // Should start multi-agent review
      await waitFor(() => {
        expect(successfulHook.startReview).toHaveBeenCalled()
      })
    })

    it('should fallback to legacy mode when multi-agent fails', async () => {
      // Setup failing multi-agent service
      const failingService = new MockMultiAgentReviewService('failure')
      mockServices.multiAgentService = failingService

      render(<CodeReviewer />)

      fireEvent.click(screen.getByText('Start Review'))

      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Multi-Agent Mode'))
      fireEvent.click(screen.getByText('Start Review'))

      // Should eventually fallback to legacy service
      await waitFor(() => {
        expect(mockServices.codeReviewerService.startReview).toHaveBeenCalled()
      })
    })

    it('should maintain state consistency across component updates', async () => {
      // Start with running review
      mockServices.store.activeSession = TestDataFactory.createReviewSession({
        status: 'running',
        progress: 50
      })
      
      const { rerender } = render(<CodeReviewer />)

      // Update progress
      mockServices.store.progress = 75
      
      rerender(<CodeReviewer />)

      // State should be maintained
      expect(mockServices.store.activeSession?.progress).toBe(50) // Original session data
      expect(mockServices.store.progress).toBe(75) // Updated progress
    })
  })

  describe('Error Handling Integration', () => {
    it('should handle service errors gracefully across components', async () => {
      const errorHook = createMockMultiAgentReviewHook('failed')
      mockServices.multiAgentHook = errorHook

      render(<CodeReviewer />)

      // Navigate to configure step
      fireEvent.click(screen.getByText('Start Review'))

      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })

      // Start review that will fail
      fireEvent.click(screen.getByText('Start Review'))

      // Should handle error without crashing
      await waitFor(() => {
        expect(errorHook.startReview).toHaveBeenCalled()
      })

      // Component should still be responsive
      expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
    })

    it('should show appropriate error messages', () => {
      const errorStatus = {
        ...FIXTURES.ui.progressTracker.failed,
        failed_agents: ['security_analysis', 'logic_analysis']
      }

      render(<MultiAgentProgressTracker reviewStatus={errorStatus} />)

      expect(screen.getByText('Failed')).toBeInTheDocument()
      expect(screen.getByText('2 agents failed')).toBeInTheDocument()
    })
  })

  describe('Performance and Memory Integration', () => {
    it('should handle rapid state updates without memory leaks', async () => {
      let updateCount = 0
      const TestComponent = () => {
        const [status, setStatus] = React.useState(FIXTURES.ui.progressTracker.running)
        
        React.useEffect(() => {
          const interval = setInterval(() => {
            updateCount++
            setStatus(prev => ({
              ...prev!,
              progress: Math.min(100, prev!.progress + 1),
              agent_statuses: {
                ...prev!.agent_statuses,
                bug_detection: {
                  ...prev!.agent_statuses.bug_detection,
                  progress: Math.min(100, prev!.agent_statuses.bug_detection.progress + 1)
                }
              }
            }))
          }, 10)

          return () => clearInterval(interval)
        }, [])

        return <MultiAgentProgressTracker reviewStatus={status} />
      }

      const { unmount } = render(<TestComponent />)

      // Let it run for a short while
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200))
      })

      unmount()

      // Should have processed multiple updates
      expect(updateCount).toBeGreaterThan(10)
    })

    it('should render large datasets efficiently', () => {
      const manyAgentStatuses = FIXTURES.performance.manyAgentStatuses
      const status = TestDataFactory.createMultiAgentReviewStatus({
        agent_statuses: manyAgentStatuses
      })

      const startTime = performance.now()
      render(<MultiAgentProgressTracker reviewStatus={status} />)
      const endTime = performance.now()

      // Should render within reasonable time
      expect(endTime - startTime).toBeLessThan(50)
    })
  })

  describe('Accessibility Integration', () => {
    it('should maintain proper focus management during workflow transitions', async () => {
      render(<CodeReviewer />)

      const startButton = screen.getByText('Start Review')
      startButton.focus()
      expect(document.activeElement).toBe(startButton)

      fireEvent.click(startButton)

      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })

      // Focus should be maintained or moved appropriately
      expect(document.activeElement).toBeDefined()
    })

    it('should provide appropriate ARIA labels for complex components', () => {
      const results = FIXTURES.scenarios.successful.results
      
      render(<MultiAgentResultsAggregator results={results} />)

      // Check for ARIA attributes (implementation-dependent)
      const performanceSection = screen.getByText('Multi-Agent Performance Overview')
      expect(performanceSection).toBeInTheDocument()
    })
  })

  describe('Real-time Updates Integration', () => {
    it('should sync WebSocket events with component state', async () => {
      const webSocketHook = createMockMultiAgentReviewHook('running')
      mockServices.webSocketHook.isConnected = true
      
      render(
        <ReviewProgressStep
          activeSession={TestDataFactory.createReviewSession({ status: 'running' })}
          selectedPR={FIXTURES.pr.basic}
          progress={50}
          progressEvents={[]}
          currentProgressStep="Running"
          mode="multi_agent"
        />
      )

      expect(screen.getByText('Multi-Agent Review Progress')).toBeInTheDocument()
      
      // WebSocket connection status should be reflected
      // (exact implementation depends on WebSocketStatus component)
    })
  })
})