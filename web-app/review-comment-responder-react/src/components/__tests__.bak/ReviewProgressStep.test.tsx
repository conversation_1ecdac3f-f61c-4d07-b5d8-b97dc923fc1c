/**
 * Unit Tests for ReviewProgressStep Component - Multi-Agent Integration
 * Testing enhanced progress tracking with Multi-Agent Review capabilities
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ReviewProgressStep } from '../ReviewProgressStep'
import type { ReviewSession } from '../../types/enhanced-review'

// Mock components and hooks
const mockMultiAgentProgressTracker = vi.fn()
const mockProgressEventsList = vi.fn()
const mockWebSocketStatus = vi.fn()
const mockReviewProgressBar = vi.fn()

vi.mock('../MultiAgentProgressTracker', () => ({
  MultiAgentProgressTracker: mockMultiAgentProgressTracker
}))

vi.mock('../ProgressEventsList', () => ({
  ProgressEventsList: mockProgressEventsList
}))

vi.mock('../WebSocketStatus', () => ({
  WebSocketStatus: mockWebSocketStatus
}))

vi.mock('../ReviewProgressBar', () => ({
  ReviewProgressBar: mockReviewProgressBar
}))

// Mock UI components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content">{children}</div>
  ),
  CardHeader: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-header">{children}</div>
  ),
  CardTitle: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-title">{children}</div>
  )
}))

vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, variant, disabled }: { 
    children: React.ReactNode; 
    onClick?: () => void; 
    variant?: string;
    disabled?: boolean;
  }) => (
    <button 
      data-testid="button" 
      data-variant={variant} 
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  )
}))

vi.mock('../ui/badge', () => ({
  Badge: ({ children, variant }: { children: React.ReactNode; variant?: string }) => (
    <span data-testid="badge" data-variant={variant}>{children}</span>
  )
}))

vi.mock('../ui/alert', () => ({
  Alert: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="alert">{children}</div>
  ),
  AlertDescription: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="alert-description">{children}</div>
  )
}))

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Play: () => <div data-testid="play-icon" />,
  Square: () => <div data-testid="square-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Zap: () => <div data-testid="zap-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Wifi: () => <div data-testid="wifi-icon" />,
  WifiOff: () => <div data-testid="wifi-off-icon" />
}))

describe('ReviewProgressStep - Multi-Agent Integration', () => {
  let mockActiveSession: ReviewSession
  let mockProgressEvents: any[]
  let mockSelectedPR: any

  beforeEach(() => {
    mockActiveSession = {
      session_id: 'test-session-123',
      branch_name: 'feature/test-branch',
      status: 'running',
      progress: 45,
      progress_message: 'Review in progress...',
      created_at: '2024-01-01T10:00:00Z',
      worktree_path: '/test/repo'
    }

    mockProgressEvents = [
      {
        id: 'event-1',
        type: 'review_started',
        message: 'Review started successfully',
        timestamp: new Date('2024-01-01T10:00:00Z'),
        data: {}
      },
      {
        id: 'event-2',
        type: 'agent_started',
        message: 'Bug detection agent started',
        timestamp: new Date('2024-01-01T10:00:30Z'),
        data: { agent_type: 'bug_detection' }
      }
    ]

    mockSelectedPR = {
      id: 1,
      branch: 'feature/test-branch',
      title: 'Test PR',
      workspace: 'test-workspace',
      repository: 'test-repo'
    }

    // Reset all mocks
    mockMultiAgentProgressTracker.mockImplementation(() => (
      <div data-testid="multi-agent-progress-tracker">Multi-Agent Progress Tracker</div>
    ))
    
    mockProgressEventsList.mockImplementation(() => (
      <div data-testid="progress-events-list">Progress Events List</div>
    ))
    
    mockWebSocketStatus.mockImplementation(() => (
      <div data-testid="websocket-status">WebSocket Status</div>
    ))
    
    mockReviewProgressBar.mockImplementation(() => (
      <div data-testid="review-progress-bar">Review Progress Bar</div>
    ))
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('basic rendering', () => {
    it('should render review progress step with basic components', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Bug detection in progress"
          mode="quick"
        />
      )

      expect(screen.getByText('Code Review Progress')).toBeInTheDocument()
      expect(screen.getByTestId('review-progress-bar')).toBeInTheDocument()
      expect(screen.getByTestId('progress-events-list')).toBeInTheDocument()
      expect(screen.getByTestId('websocket-status')).toBeInTheDocument()
    })

    it('should render session information correctly', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Bug detection in progress"
          mode="quick"
        />
      )

      expect(screen.getByText('feature/test-branch')).toBeInTheDocument()
      expect(screen.getByText('Running')).toBeInTheDocument()
    })

    it('should handle null activeSession gracefully', () => {
      render(
        <ReviewProgressStep
          activeSession={null}
          selectedPR={mockSelectedPR}
          progress={0}
          progressEvents={[]}
          currentProgressStep=""
          mode="quick"
        />
      )

      expect(screen.getByText('No active review session')).toBeInTheDocument()
    })
  })

  describe('multi-agent mode detection', () => {
    it('should render multi-agent progress tracker for multi_agent mode', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Multi-agent orchestration in progress"
          mode="multi_agent"
        />
      )

      expect(screen.getByTestId('multi-agent-progress-tracker')).toBeInTheDocument()
      expect(screen.getByText('Multi-Agent Review Progress')).toBeInTheDocument()
    })

    it('should render multi-agent progress tracker for parallel mode', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Parallel agents running"
          mode="full"
        />
      )

      expect(screen.getByTestId('multi-agent-progress-tracker')).toBeInTheDocument()
      expect(screen.getByText('Multi-Agent Review Progress')).toBeInTheDocument()
    })

    it('should not render multi-agent progress tracker for legacy modes', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Legacy review in progress"
          mode="quick"
        />
      )

      expect(screen.queryByTestId('multi-agent-progress-tracker')).not.toBeInTheDocument()
      expect(screen.queryByText('Multi-Agent Review Progress')).not.toBeInTheDocument()
    })
  })

  describe('progress status indicators', () => {
    it('should show correct status badge for running session', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      const statusBadge = screen.getByTestId('badge')
      expect(statusBadge).toHaveTextContent('Running')
    })

    it('should show correct status badge for completed session', () => {
      const completedSession = {
        ...mockActiveSession,
        status: 'completed' as const,
        progress: 100,
        completed_at: '2024-01-01T10:03:00Z'
      }

      render(
        <ReviewProgressStep
          activeSession={completedSession}
          selectedPR={mockSelectedPR}
          progress={100}
          progressEvents={mockProgressEvents}
          currentProgressStep="Completed"
          mode="quick"
        />
      )

      const statusBadge = screen.getByTestId('badge')
      expect(statusBadge).toHaveTextContent('Completed')
    })

    it('should show correct status badge for failed session', () => {
      const failedSession = {
        ...mockActiveSession,
        status: 'error' as const,
        error: 'Review failed'
      }

      render(
        <ReviewProgressStep
          activeSession={failedSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Failed"
          mode="quick"
        />
      )

      const statusBadge = screen.getByTestId('badge')
      expect(statusBadge).toHaveTextContent('Error')
    })
  })

  describe('progress events handling', () => {
    it('should pass progress events to ProgressEventsList component', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(mockProgressEventsList).toHaveBeenCalledWith(
        expect.objectContaining({
          events: mockProgressEvents
        }),
        expect.anything()
      )
    })

    it('should handle empty progress events array', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={[]}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(mockProgressEventsList).toHaveBeenCalledWith(
        expect.objectContaining({
          events: []
        }),
        expect.anything()
      )
    })

    it('should show current progress step message', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Analyzing code quality patterns"
          mode="quick"
        />
      )

      expect(screen.getByText('Analyzing code quality patterns')).toBeInTheDocument()
    })
  })

  describe('PR information display', () => {
    it('should display PR title and branch information', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(screen.getByText('Test PR')).toBeInTheDocument()
      expect(screen.getByText('feature/test-branch')).toBeInTheDocument()
    })

    it('should handle null selectedPR gracefully', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={null}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      // Should still render without crashing
      expect(screen.getByText('Code Review Progress')).toBeInTheDocument()
    })
  })

  describe('time and duration display', () => {
    it('should show session start time', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      // Should format and display the created_at timestamp
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument()
    })

    it('should show duration for completed sessions', () => {
      const completedSession = {
        ...mockActiveSession,
        status: 'completed' as const,
        completed_at: '2024-01-01T10:03:00Z' // 3 minutes after start
      }

      render(
        <ReviewProgressStep
          activeSession={completedSession}
          selectedPR={mockSelectedPR}
          progress={100}
          progressEvents={mockProgressEvents}
          currentProgressStep="Completed"
          mode="quick"
        />
      )

      // Duration calculation should be handled by the component
      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument()
    })
  })

  describe('action buttons', () => {
    it('should show cancel button for running sessions', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(screen.getByText('Cancel Review')).toBeInTheDocument()
    })

    it('should show view results button for completed sessions', () => {
      const completedSession = {
        ...mockActiveSession,
        status: 'completed' as const,
        progress: 100
      }

      render(
        <ReviewProgressStep
          activeSession={completedSession}
          selectedPR={mockSelectedPR}
          progress={100}
          progressEvents={mockProgressEvents}
          currentProgressStep="Completed"
          mode="quick"
        />
      )

      expect(screen.getByText('View Results')).toBeInTheDocument()
    })

    it('should show retry button for failed sessions', () => {
      const failedSession = {
        ...mockActiveSession,
        status: 'error' as const,
        error: 'Review failed'
      }

      render(
        <ReviewProgressStep
          activeSession={failedSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Failed"
          mode="quick"
        />
      )

      expect(screen.getByText('Retry Review')).toBeInTheDocument()
    })
  })

  describe('error handling and display', () => {
    it('should display error message for failed sessions', () => {
      const failedSession = {
        ...mockActiveSession,
        status: 'error' as const,
        error: 'Service connection failed'
      }

      render(
        <ReviewProgressStep
          activeSession={failedSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Failed"
          mode="quick"
        />
      )

      expect(screen.getByText('Service connection failed')).toBeInTheDocument()
      expect(screen.getByTestId('alert')).toBeInTheDocument()
    })

    it('should show generic error message when error details not available', () => {
      const failedSession = {
        ...mockActiveSession,
        status: 'error' as const
      }

      render(
        <ReviewProgressStep
          activeSession={failedSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Failed"
          mode="quick"
        />
      )

      expect(screen.getByText('Review failed')).toBeInTheDocument()
    })
  })

  describe('websocket status integration', () => {
    it('should render WebSocket status component', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(screen.getByTestId('websocket-status')).toBeInTheDocument()
    })
  })

  describe('multi-agent specific features', () => {
    it('should pass correct props to MultiAgentProgressTracker', () => {
      const multiAgentSession = {
        ...mockActiveSession,
        multiAgentStatus: {
          review_id: 'multi-agent-123',
          status: 'running',
          progress: 65,
          active_agents: ['bug_detection', 'security_analysis'],
          completed_agents: ['acceptance_criteria'],
          failed_agents: []
        }
      }

      render(
        <ReviewProgressStep
          activeSession={multiAgentSession}
          selectedPR={mockSelectedPR}
          progress={65}
          progressEvents={mockProgressEvents}
          currentProgressStep="Multi-agent analysis in progress"
          mode="multi_agent"
        />
      )

      expect(mockMultiAgentProgressTracker).toHaveBeenCalledWith(
        expect.objectContaining({
          reviewStatus: expect.any(Object)
        }),
        expect.anything()
      )
    })

    it('should show agent-specific progress messages for multi-agent mode', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Bug detection and security analysis running in parallel"
          mode="multi_agent"
        />
      )

      expect(screen.getByText('Bug detection and security analysis running in parallel')).toBeInTheDocument()
    })
  })

  describe('progress bar integration', () => {
    it('should pass correct progress value to ReviewProgressBar', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={75}
          progressEvents={mockProgressEvents}
          currentProgressStep="In progress"
          mode="quick"
        />
      )

      expect(mockReviewProgressBar).toHaveBeenCalledWith(
        expect.objectContaining({
          progress: 75
        }),
        expect.anything()
      )
    })

    it('should handle progress values correctly', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={100}
          progressEvents={mockProgressEvents}
          currentProgressStep="Completed"
          mode="quick"
        />
      )

      expect(mockReviewProgressBar).toHaveBeenCalledWith(
        expect.objectContaining({
          progress: 100
        }),
        expect.anything()
      )
    })
  })

  describe('mode-specific styling and behavior', () => {
    it('should apply different styling for multi-agent mode', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Multi-agent analysis"
          mode="multi_agent"
        />
      )

      // Multi-agent specific title should be shown
      expect(screen.getByText('Multi-Agent Review Progress')).toBeInTheDocument()
    })

    it('should show legacy progress for non-multi-agent modes', () => {
      render(
        <ReviewProgressStep
          activeSession={mockActiveSession}
          selectedPR={mockSelectedPR}
          progress={45}
          progressEvents={mockProgressEvents}
          currentProgressStep="Sequential analysis"
          mode="standard"
        />
      )

      // Standard title should be shown
      expect(screen.getByText('Code Review Progress')).toBeInTheDocument()
    })
  })
})