/**
 * Unit Tests for MultiAgentProgressTracker Component
 * Testing comprehensive progress tracking for Multi-Agent Review system
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { MultiAgentProgressTracker, type MultiAgentReviewStatus } from '../MultiAgentProgressTracker'
import type { AgentStatus } from '../multi-agent/AgentCard'

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  Play: ({ className }: { className?: string }) => <div data-testid="play-icon" className={className} />,
  CheckCircle: ({ className }: { className?: string }) => <div data-testid="check-circle-icon" className={className} />,
  AlertTriangle: ({ className }: { className?: string }) => <div data-testid="alert-triangle-icon" className={className} />,
  Clock: ({ className }: { className?: string }) => <div data-testid="clock-icon" className={className} />,
  Zap: ({ className }: { className?: string }) => <div data-testid="zap-icon" className={className} />,
  TrendingUp: ({ className }: { className?: string }) => <div data-testid="trending-up-icon" className={className} />,
  Users: ({ className }: { className?: string }) => <div data-testid="users-icon" className={className} />
}))

// Mock UI Components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card" className={className}>{children}</div>,
  CardContent: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-content" className={className}>{children}</div>,
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-header" className={className}>{children}</div>,
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => 
    <div data-testid="card-title" className={className}>{children}</div>
}))

vi.mock('../ui/progress', () => ({
  Progress: ({ value, className }: { value: number; className?: string }) => 
    <div data-testid="progress" data-value={value} className={className} />
}))

vi.mock('../ui/badge', () => ({
  Badge: ({ children, variant, className }: { children: React.ReactNode; variant?: string; className?: string }) => 
    <span data-testid="badge" data-variant={variant} className={className}>{children}</span>
}))

// Mock AgentCard component
vi.mock('../multi-agent/AgentCard', () => ({
  AgentCard: ({ agentStatus, className }: { agentStatus: AgentStatus; className?: string }) => 
    <div data-testid="agent-card" data-agent-type={agentStatus.agent_type} className={className}>
      Agent: {agentStatus.agent_type} - Status: {agentStatus.status}
    </div>
}))

describe('MultiAgentProgressTracker', () => {
  let mockReviewStatus: MultiAgentReviewStatus

  beforeEach(() => {
    mockReviewStatus = {
      review_id: 'test-review-123',
      status: 'running',
      progress: 65,
      agent_statuses: {
        acceptance_criteria: {
          agent_type: 'acceptance_criteria',
          status: 'completed',
          progress: 100,
          started_at: '2024-01-01T10:00:00Z',
          completed_at: '2024-01-01T10:02:30Z'
        },
        bug_detection: {
          agent_type: 'bug_detection',
          status: 'running',
          progress: 75,
          started_at: '2024-01-01T10:00:30Z'
        },
        security_analysis: {
          agent_type: 'security_analysis',
          status: 'pending',
          progress: 0
        },
        logic_analysis: {
          agent_type: 'logic_analysis',
          status: 'failed',
          progress: 25,
          started_at: '2024-01-01T10:01:00Z',
          error_message: 'Analysis timeout'
        }
      },
      started_at: '2024-01-01T10:00:00Z',
      estimated_remaining_time: 120,
      active_agents: ['bug_detection'],
      completed_agents: ['acceptance_criteria'],
      failed_agents: ['logic_analysis'],
      context_status: {
        git_history: 'ready',
        file_analysis: 'preparing'
      }
    }
  })

  describe('rendering', () => {
    it('should render null state when no reviewStatus provided', () => {
      render(<MultiAgentProgressTracker reviewStatus={null} />)
      
      expect(screen.getByTestId('users-icon')).toBeInTheDocument()
      expect(screen.getByText('No multi-agent review in progress')).toBeInTheDocument()
    })

    it('should render progress tracker with running status', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('Multi-Agent Code Review')).toBeInTheDocument()
      expect(screen.getByText('Running')).toBeInTheDocument()
      expect(screen.getByText('1 agents running in parallel')).toBeInTheDocument()
      expect(screen.getByTestId('progress')).toHaveAttribute('data-value', '65')
    })

    it('should render completed status correctly', () => {
      const completedStatus = { 
        ...mockReviewStatus, 
        status: 'completed' as const, 
        progress: 100,
        completed_at: '2024-01-01T10:05:00Z',
        active_agents: [],
        completed_agents: ['acceptance_criteria', 'bug_detection', 'security_analysis', 'logic_analysis']
      }
      
      render(<MultiAgentProgressTracker reviewStatus={completedStatus} />)
      
      expect(screen.getByTestId('check-circle-icon')).toBeInTheDocument()
      expect(screen.getByText('Completed')).toBeInTheDocument()
      expect(screen.getByText('All agents completed successfully')).toBeInTheDocument()
    })

    it('should render failed status correctly', () => {
      const failedStatus = { 
        ...mockReviewStatus, 
        status: 'failed' as const,
        failed_agents: ['logic_analysis', 'security_analysis']
      }
      
      render(<MultiAgentProgressTracker reviewStatus={failedStatus} />)
      
      expect(screen.getByTestId('alert-triangle-icon')).toBeInTheDocument()
      expect(screen.getByText('Failed')).toBeInTheDocument()
      expect(screen.getByText('2 agents failed')).toBeInTheDocument()
    })
  })

  describe('statistics calculation', () => {
    it('should calculate and display correct agent statistics', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      // Check stats grid
      expect(screen.getByText('1')).toBeInTheDocument() // Running
      expect(screen.getByText('1')).toBeInTheDocument() // Completed
      expect(screen.getByText('1')).toBeInTheDocument() // Pending 
      expect(screen.getByText('1')).toBeInTheDocument() // Failed
      
      expect(screen.getByText('Running')).toBeInTheDocument()
      expect(screen.getByText('Completed')).toBeInTheDocument()
      expect(screen.getByText('Pending')).toBeInTheDocument()
      expect(screen.getByText('Failed')).toBeInTheDocument()
    })

    it('should show progress completion stats', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('1/4 completed')).toBeInTheDocument()
    })

    it('should show success rate for completed reviews', () => {
      const completedStatus = { 
        ...mockReviewStatus, 
        status: 'completed' as const,
        agent_statuses: {
          ...mockReviewStatus.agent_statuses,
          bug_detection: { ...mockReviewStatus.agent_statuses.bug_detection, status: 'completed' as const },
          security_analysis: { ...mockReviewStatus.agent_statuses.security_analysis, status: 'completed' as const }
        },
        completed_agents: ['acceptance_criteria', 'bug_detection', 'security_analysis'],
        failed_agents: ['logic_analysis']
      }
      
      render(<MultiAgentProgressTracker reviewStatus={completedStatus} />)
      
      expect(screen.getByText('75% success rate')).toBeInTheDocument()
    })
  })

  describe('time formatting', () => {
    it('should format duration correctly', () => {
      const statusWithCompletion = {
        ...mockReviewStatus,
        completed_at: '2024-01-01T10:03:45Z' // 3m 45s after start
      }
      
      render(<MultiAgentProgressTracker reviewStatus={statusWithCompletion} />)
      
      expect(screen.getByText('3m 45s')).toBeInTheDocument()
    })

    it('should format remaining time correctly', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('~2m 0s remaining')).toBeInTheDocument()
    })

    it('should handle seconds-only duration', () => {
      const statusWithShortDuration = {
        ...mockReviewStatus,
        completed_at: '2024-01-01T10:00:30Z' // 30s after start
      }
      
      render(<MultiAgentProgressTracker reviewStatus={statusWithShortDuration} />)
      
      expect(screen.getByText('30s')).toBeInTheDocument()
    })
  })

  describe('agent card rendering', () => {
    it('should render agent cards for all agents', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      const agentCards = screen.getAllByTestId('agent-card')
      expect(agentCards).toHaveLength(4)
      
      expect(screen.getByText(/Agent: acceptance_criteria - Status: completed/)).toBeInTheDocument()
      expect(screen.getByText(/Agent: bug_detection - Status: running/)).toBeInTheDocument()
      expect(screen.getByText(/Agent: security_analysis - Status: pending/)).toBeInTheDocument()
      expect(screen.getByText(/Agent: logic_analysis - Status: failed/)).toBeInTheDocument()
    })

    it('should sort agents in correct order', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      const agentCards = screen.getAllByTestId('agent-card')
      expect(agentCards[0]).toHaveAttribute('data-agent-type', 'acceptance_criteria')
      expect(agentCards[1]).toHaveAttribute('data-agent-type', 'bug_detection')
      expect(agentCards[2]).toHaveAttribute('data-agent-type', 'security_analysis')
      expect(agentCards[3]).toHaveAttribute('data-agent-type', 'logic_analysis')
    })
  })

  describe('context status display', () => {
    it('should render context status when available', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('Context Preparation Status')).toBeInTheDocument()
      expect(screen.getByText('Git history:')).toBeInTheDocument()
      expect(screen.getByText('File analysis:')).toBeInTheDocument()
    })

    it('should not render context status section when empty', () => {
      const statusWithoutContext = { ...mockReviewStatus, context_status: {} }
      render(<MultiAgentProgressTracker reviewStatus={statusWithoutContext} />)
      
      expect(screen.queryByText('Context Preparation Status')).not.toBeInTheDocument()
    })

    it('should not render context status section when undefined', () => {
      const statusWithoutContext = { ...mockReviewStatus, context_status: undefined }
      render(<MultiAgentProgressTracker reviewStatus={statusWithoutContext} />)
      
      expect(screen.queryByText('Context Preparation Status')).not.toBeInTheDocument()
    })
  })

  describe('development debug info', () => {
    const originalEnv = process.env.NODE_ENV

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('should show debug info in development mode', () => {
      process.env.NODE_ENV = 'development'
      
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('Debug Info (Dev Mode)')).toBeInTheDocument()
      expect(screen.getByText(/"review_id": "test-review-123"/)).toBeInTheDocument()
    })

    it('should not show debug info in production mode', () => {
      process.env.NODE_ENV = 'production'
      
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.queryByText('Debug Info (Dev Mode)')).not.toBeInTheDocument()
    })
  })

  describe('status color and icon handling', () => {
    it('should apply correct styling for running status', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByTestId('play-icon')).toHaveClass('text-blue-500')
      expect(screen.getByTestId('card')).toHaveClass('border-blue-200', 'bg-blue-50')
    })

    it('should apply correct styling for completed status', () => {
      const completedStatus = { ...mockReviewStatus, status: 'completed' as const }
      render(<MultiAgentProgressTracker reviewStatus={completedStatus} />)
      
      expect(screen.getByTestId('check-circle-icon')).toHaveClass('text-green-500')
      expect(screen.getByTestId('card')).toHaveClass('border-green-200', 'bg-green-50')
    })

    it('should apply correct styling for failed status', () => {
      const failedStatus = { ...mockReviewStatus, status: 'failed' as const }
      render(<MultiAgentProgressTracker reviewStatus={failedStatus} />)
      
      expect(screen.getByTestId('alert-triangle-icon')).toHaveClass('text-red-500')
      expect(screen.getByTestId('card')).toHaveClass('border-red-200', 'bg-red-50')
    })
  })

  describe('accessibility', () => {
    it('should have proper ARIA attributes and semantic structure', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('Multi-Agent Code Review')).toBeInTheDocument()
      expect(screen.getByText('Overall Progress')).toBeInTheDocument()
      expect(screen.getByText('65%')).toBeInTheDocument()
    })

    it('should provide meaningful text content for screen readers', () => {
      render(<MultiAgentProgressTracker reviewStatus={mockReviewStatus} />)
      
      expect(screen.getByText('1 agents running in parallel')).toBeInTheDocument()
      expect(screen.getByText('1/4 completed')).toBeInTheDocument()
      expect(screen.getByText('~2m 0s remaining')).toBeInTheDocument()
    })
  })

  describe('custom className prop', () => {
    it('should apply custom className to root element', () => {
      const { container } = render(
        <MultiAgentProgressTracker 
          reviewStatus={mockReviewStatus} 
          className="custom-test-class" 
        />
      )
      
      expect(container.firstChild).toHaveClass('custom-test-class')
    })
  })

  describe('edge cases', () => {
    it('should handle empty agent_statuses', () => {
      const emptyAgentsStatus = { 
        ...mockReviewStatus, 
        agent_statuses: {},
        active_agents: [],
        completed_agents: [],
        failed_agents: []
      }
      
      render(<MultiAgentProgressTracker reviewStatus={emptyAgentsStatus} />)
      
      expect(screen.getByText('0/0 completed')).toBeInTheDocument()
      expect(screen.queryByTestId('agent-card')).not.toBeInTheDocument()
    })

    it('should handle missing timestamps gracefully', () => {
      const statusWithoutTimestamps = { 
        ...mockReviewStatus, 
        started_at: undefined,
        estimated_remaining_time: undefined
      }
      
      render(<MultiAgentProgressTracker reviewStatus={statusWithoutTimestamps} />)
      
      expect(screen.queryByTestId('clock-icon')).not.toBeInTheDocument()
      expect(screen.queryByTestId('zap-icon')).not.toBeInTheDocument()
    })

    it('should handle zero progress correctly', () => {
      const zeroProgressStatus = { ...mockReviewStatus, progress: 0 }
      render(<MultiAgentProgressTracker reviewStatus={zeroProgressStatus} />)
      
      expect(screen.getByText('0%')).toBeInTheDocument()
      expect(screen.getByTestId('progress')).toHaveAttribute('data-value', '0')
    })
  })
})