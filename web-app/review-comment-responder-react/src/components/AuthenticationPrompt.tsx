import React from 'react'
import { Card, CardContent } from './ui/card'
import { MainLoginButton } from './auth/LoginButton'

export const AuthenticationPrompt: React.FC = () => {
  return (
    <Card className="mb-8">
      <CardContent className="p-8 text-center">
        <MainLoginButton />
        <div className="mt-6 p-4 bg-muted/30 rounded-lg">
          <p className="text-sm text-muted-foreground">
            <strong>Code Reviewer requires Bitbucket authentication</strong> to access your assigned pull requests. Once connected, you'll be able to:
          </p>
          <ul className="text-sm text-muted-foreground mt-2 space-y-1">
            <li>• See pull requests assigned to you for review</li>
            <li>• Connect with Jira to link tickets and acceptance criteria</li>
            <li>• Run comprehensive AI reviews with <PERSON> <PERSON></li>
            <li>• View structured findings and compliance reports</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}