import React, { useEffect } from 'react'
import { useAuthActions } from '../../hooks/useAuth'

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { checkAuthStatus } = useAuthActions()

  // Initialize auth state on app startup
  useEffect(() => {
    checkAuthStatus()
  }, [checkAuthStatus])

  return <>{children}</>
}