import React from 'react'
import { LogIn, Loader2 } from 'lucide-react'
import { Button } from '../ui/button'
import { useAuthActions, useAuthStatus } from '../../hooks/useAuth'

interface LoginButtonProps {
  variant?: 'default' | 'outline' | 'secondary'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  children?: React.ReactNode
}

export const LoginButton: React.FC<LoginButtonProps> = ({
  variant = 'default',
  size = 'default',
  className,
  children
}) => {
  const { login } = useAuthActions()
  const { isLoading, error } = useAuthStatus()

  const handleLogin = async () => {
    try {
      await login()
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  return (
    <div className="flex flex-col gap-2">
      <Button
        variant={variant}
        size={size}
        onClick={handleLogin}
        disabled={isLoading}
        className={className}
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Connecting...
          </>
        ) : (
          <>
            <LogIn className="h-4 w-4 mr-2" />
            {children || 'Login with Bitbucket'}
          </>
        )}
      </Button>
      {error && (
        <p className="text-sm text-red-400 bg-red-500/10 px-3 py-2 rounded-md border border-red-500/20">
          {error}
        </p>
      )}
    </div>
  )
}

// Specialized login button for navigation
export const NavLoginButton: React.FC = () => {
  return (
    <LoginButton 
      variant="outline" 
      size="sm"
      className="flex items-center gap-2 bg-primary/10 hover:bg-primary/20 border-primary/30 text-primary hover:text-primary"
    >
      <span className="hidden sm:inline">Login with Bitbucket</span>
      <span className="sm:hidden">Login</span>
    </LoginButton>
  )
}

// Large login button for main auth pages
export const MainLoginButton: React.FC = () => {
  return (
    <div className="text-center space-y-4">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-foreground">Connect to Bitbucket</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Login with your Bitbucket account to access your repositories, branches, and pull requests directly.
        </p>
      </div>
      <LoginButton 
        size="lg"
        className="px-8 py-3 text-lg font-medium min-w-[200px]"
      >
        Connect Bitbucket Account
      </LoginButton>
      <p className="text-xs text-muted-foreground">
        We only access repositories you have permission to view
      </p>
    </div>
  )
}