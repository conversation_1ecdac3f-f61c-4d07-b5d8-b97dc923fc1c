import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { jiraAuthService } from '../../services/auth/JiraAuthService'

export const JiraAuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing')
  const [message, setMessage] = useState('Processing Jira authentication...')

  useEffect(() => {
    let cancelled = false
    const handleCallback = async () => {
      if (cancelled) return
      try {
        const code = searchParams.get('code')
        const state = searchParams.get('state')
        const error = searchParams.get('error')
        const errorDescription = searchParams.get('error_description')

        if (error) {
          throw new Error(errorDescription || `Authentication error: ${error}`)
        }

        if (!code || !state) {
          throw new Error('Missing authorization code or state parameter')
        }

        setMessage('Exchanging authorization code for access token...')
        
        const success = await jiraAuthService.handleAuthCallback(code, state)
        
        if (success) {
          setStatus('success')
          setMessage('Successfully authenticated with Jira!')
          
          // Redirect back to the code reviewer page after a short delay
          setTimeout(() => {
            navigate('/reviewer')
          }, 2000)
        } else {
          // Check if we're already authenticated (from previous successful attempt)
          if (jiraAuthService.isAuthenticated()) {
            setStatus('success')
            setMessage('Already authenticated with Jira!')
            setTimeout(() => {
              navigate('/reviewer')
            }, 1000)
          } else {
            throw new Error('Failed to complete authentication')
          }
        }

      } catch (error) {
        console.error('Jira auth callback error:', error)
        setStatus('error')
        setMessage(error instanceof Error ? error.message : 'Authentication failed')
        
        // Redirect back to reviewer page after error display
        setTimeout(() => {
          navigate('/reviewer')
        }, 5000)
      }
    }

    handleCallback()
    
    return () => {
      cancelled = true
    }
  }, [searchParams, navigate])

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-primary" />
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case 'error':
        return <XCircle className="h-8 w-8 text-red-500" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-primary'
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <CardTitle className={`text-xl ${getStatusColor()}`}>
            {status === 'processing' && 'Authenticating...'}
            {status === 'success' && 'Authentication Successful!'}
            {status === 'error' && 'Authentication Failed'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              {message}
            </p>
            
            {status === 'success' && (
              <div className="space-y-2">
                <p className="text-sm text-green-600">
                  🎉 You can now access your Jira tickets and create automated code reviews!
                </p>
                <p className="text-xs text-muted-foreground">
                  Redirecting you to the Code Reviewer...
                </p>
              </div>
            )}
            
            {status === 'error' && (
              <div className="space-y-2">
                <p className="text-sm text-red-600">
                  Please try again or contact support if the problem persists.
                </p>
                <p className="text-xs text-muted-foreground">
                  Redirecting back in 5 seconds...
                </p>
              </div>
            )}
            
            {status === 'processing' && (
              <p className="text-xs text-muted-foreground">
                This may take a few moments...
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}