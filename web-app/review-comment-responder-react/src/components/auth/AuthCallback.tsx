import React, { useEffect, useRef } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Loader2, CheckCircle, XCircle } from 'lucide-react'
import { Card, CardContent } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { useAuthActions, useAuthStatus } from '../../hooks/useAuth'

export const AuthCallback: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { handleCallback } = useAuthActions()
  const { isLoading, error, isAuthenticated } = useAuthStatus()
  const processedRef = useRef(false)

  useEffect(() => {
    // Prevent multiple processing of the same callback
    if (processedRef.current) return

    const processCallback = async () => {
      const code = searchParams.get('code')
      const state = searchParams.get('state')
      const errorParam = searchParams.get('error')
      const errorDescription = searchParams.get('error_description')

      // Handle OAuth errors
      if (errorParam) {
        console.error('OAuth error:', errorParam, errorDescription)
        processedRef.current = true
        // Stay on callback page to show error
        return
      }

      // Handle successful authorization
      if (code && state) {
        processedRef.current = true
        try {
          await handleCallback(code, state)
          // Redirect will happen via useEffect below when isAuthenticated becomes true
        } catch (error) {
          console.error('Callback processing failed:', error)
          // Error state will be shown via useAuthStatus
        }
      } else {
        console.error('Missing authorization code or state parameter')
        processedRef.current = true
      }
    }

    processCallback()
  }, [searchParams, handleCallback])

  // Redirect to main page after successful authentication
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      // Small delay to show success state
      setTimeout(() => {
        navigate('/', { replace: true })
      }, 1500)
    }
  }, [isAuthenticated, isLoading, navigate])

  const errorParam = searchParams.get('error')
  const errorDescription = searchParams.get('error_description')

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center space-y-6">
          {/* Loading State */}
          {isLoading && (
            <>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-3xl"></div>
                <Loader2 className="relative mx-auto h-16 w-16 text-primary animate-spin" />
              </div>
              <div className="space-y-2">
                <h2 className="text-xl font-semibold text-foreground">
                  Connecting to Bitbucket
                </h2>
                <p className="text-muted-foreground">
                  Processing your authentication...
                </p>
              </div>
            </>
          )}

          {/* Success State */}
          {isAuthenticated && !isLoading && (
            <>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-transparent rounded-full blur-3xl"></div>
                <CheckCircle className="relative mx-auto h-16 w-16 text-green-400" />
              </div>
              <div className="space-y-2">
                <h2 className="text-xl font-semibold text-foreground">
                  Successfully Connected!
                </h2>
                <p className="text-muted-foreground">
                  Redirecting you to the main application...
                </p>
              </div>
            </>
          )}

          {/* Error State */}
          {(error || errorParam) && !isLoading && (
            <>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-transparent rounded-full blur-3xl"></div>
                <XCircle className="relative mx-auto h-16 w-16 text-red-400" />
              </div>
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">
                  Authentication Failed
                </h2>
                <div className="text-left space-y-2">
                  {errorParam && (
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                      <p className="font-medium text-red-400">
                        {errorParam === 'access_denied' 
                          ? 'Access Denied' 
                          : 'OAuth Error'
                        }
                      </p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {errorDescription || 
                         (errorParam === 'access_denied' 
                           ? 'You declined the authorization request.' 
                           : 'An error occurred during authentication.'
                         )
                        }
                      </p>
                    </div>
                  )}
                  {error && (
                    <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                      <p className="font-medium text-red-400">Error</p>
                      <p className="text-sm text-muted-foreground mt-1">
                        {error}
                      </p>
                    </div>
                  )}
                </div>
                <div className="flex gap-3">
                  <Button 
                    variant="outline" 
                    onClick={() => navigate('/')}
                    className="flex-1"
                  >
                    Go Back
                  </Button>
                  <Button 
                    onClick={() => window.location.reload()}
                    className="flex-1"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* No parameters - invalid callback */}
          {!searchParams.get('code') && !searchParams.get('error') && !isLoading && (
            <>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-transparent rounded-full blur-3xl"></div>
                <XCircle className="relative mx-auto h-16 w-16 text-yellow-400" />
              </div>
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground">
                  Invalid Callback
                </h2>
                <p className="text-muted-foreground">
                  This page should only be accessed via Bitbucket OAuth redirect.
                </p>
                <Button onClick={() => navigate('/')}>
                  Return to Main Page
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}