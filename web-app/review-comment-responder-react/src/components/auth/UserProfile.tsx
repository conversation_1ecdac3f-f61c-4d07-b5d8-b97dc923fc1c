import React, { useState } from 'react'
import { LogOut, User, Setting<PERSON>, ChevronDown } from 'lucide-react'
import { Link } from 'react-router-dom'
import { Button } from '../ui/button'
import { useAuthActions, useAuthStatus } from '../../hooks/useAuth'
import { cn } from '../../lib/utils'

export const UserProfile: React.FC = () => {
  const { logout } = useAuthActions()
  const { user, isLoading } = useAuthStatus()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  if (isLoading || !user) {
    return null
  }

  const handleLogout = () => {
    logout()
    setIsDropdownOpen(false)
  }

  return (
    <div className="relative">
      {/* Profile Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center gap-2 px-3 py-2 hover:bg-muted/50 relative"
      >
        <div className="flex items-center gap-2">
          {user.links?.avatar ? (
            <img
              src={user.links.avatar.href}
              alt={user.display_name}
              className="w-6 h-6 rounded-full border border-border"
              onError={(e) => {
                // Fallback to default avatar on error
                const target = e.target as HTMLImageElement
                target.style.display = 'none'
                target.nextElementSibling?.classList.remove('hidden')
              }}
            />
          ) : null}
          <div 
            className={cn(
              "w-6 h-6 rounded-full bg-primary/20 border border-primary/30 flex items-center justify-center",
              user.links?.avatar ? "hidden" : ""
            )}
          >
            <User className="h-3 w-3 text-primary" />
          </div>
          <div className="hidden sm:flex flex-col items-start">
            <span className="text-sm font-medium text-foreground">
              {user.display_name}
            </span>
            <span className="text-xs text-muted-foreground">
              @{user.username}
            </span>
          </div>
        </div>
        <ChevronDown className={cn(
          "h-4 w-4 text-muted-foreground transition-transform duration-200",
          isDropdownOpen && "rotate-180"
        )} />
      </Button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsDropdownOpen(false)}
          />
          
          {/* Dropdown Content */}
          <div className="absolute right-0 top-full mt-2 w-64 bg-card border border-border rounded-lg shadow-lg z-50 animate-fade-in">
            {/* User Info Header */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center gap-3">
                {user.links?.avatar ? (
                  <img
                    src={user.links.avatar.href}
                    alt={user.display_name}
                    className="w-10 h-10 rounded-full border border-border"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-primary/20 border border-primary/30 flex items-center justify-center">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-foreground truncate">
                    {user.display_name}
                  </h3>
                  <p className="text-sm text-muted-foreground truncate">
                    @{user.username}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    ID: {user.account_id}
                  </p>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="p-2 space-y-1">
              {user.links?.html && (
                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="w-full justify-start"
                >
                  <a 
                    href={user.links.html.href} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    View Bitbucket Profile
                  </a>
                </Button>
              )}
              
              <Link to="/settings" onClick={() => setIsDropdownOpen(false)}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </Link>
            </div>

            {/* Logout Section */}
            <div className="p-2 border-t border-border">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-500/10"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Compact version for mobile
export const UserProfileCompact: React.FC = () => {
  const { logout } = useAuthActions()
  const { user } = useAuthStatus()

  if (!user) return null

  return (
    <div className="flex items-center gap-2">
      {user.links?.avatar ? (
        <img
          src={user.links.avatar.href}
          alt={user.display_name}
          className="w-8 h-8 rounded-full border border-border"
        />
      ) : (
        <div className="w-8 h-8 rounded-full bg-primary/20 border border-primary/30 flex items-center justify-center">
          <User className="h-4 w-4 text-primary" />
        </div>
      )}
      <Button
        variant="ghost"
        size="sm"
        onClick={logout}
        className="p-1 hover:bg-red-500/10 text-red-400 hover:text-red-300"
      >
        <LogOut className="h-4 w-4" />
      </Button>
    </div>
  )
}