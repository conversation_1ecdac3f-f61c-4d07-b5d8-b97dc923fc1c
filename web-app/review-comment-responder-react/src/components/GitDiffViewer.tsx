import React, { useState, useEffect, useCallback } from 'react';
import {
  GitBranch,
  RefreshCw,
  Loader2,
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  FolderOpen,
  Plus,
  Minus,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { useCommentStore } from '../store/useCommentStore';
import { ApiService } from '../services/api';
import type { GitDiffResponse } from '../types';
import { cn } from '../lib/utils';

export const GitDiffViewer: React.FC = () => {
  const { prContext } = useCommentStore();
  const [diffData, setDiffData] = useState<GitDiffResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'unified' | 'split'>('unified');
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [fileDiffs, setFileDiffs] = useState<Record<string, string>>({});
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const shouldShow = Boolean(prContext.worktreePath?.trim());
    setIsVisible(shouldShow);
  }, [prContext.worktreePath]);

  const loadGitDiff = useCallback(async () => {
    if (!prContext.worktreePath?.trim()) {
      console.warn('No worktree path specified');
      return;
    }

    setIsLoading(true);
    try {
      const response = await ApiService.getGitDiff(prContext.worktreePath, prContext.branchName);
      setDiffData(response);
      if (response?.changedFiles?.length > 0) {
        setSelectedFile(response.changedFiles[0].filename);
        const parsedFileDiffs = parseFileDiffs(response.diff);
        setFileDiffs(parsedFileDiffs);
      }
    } catch (error) {
      console.error('Failed to load git diff:', error);
      setDiffData(null);
    } finally {
      setIsLoading(false);
    }
  }, [prContext.worktreePath, prContext.branchName]);

  useEffect(() => {
    if (prContext.worktreePath?.trim()) {
      loadGitDiff();
    }
  }, [prContext.worktreePath, prContext.branchName, loadGitDiff]);

  const parseFileDiffs = (fullDiff: string): Record<string, string> => {
    const fileDiffMap: Record<string, string> = {};
    const diffSections = fullDiff.split(/(?=diff --git)/);
    
    for (const section of diffSections) {
      if (!section.trim()) continue;
      
      const match = section.match(/diff --git a\/(.*?) b\//);
      if (match) {
        const filename = match[1];
        fileDiffMap[filename] = section;
      }
    }
    
    return fileDiffMap;
  };

  const selectFile = (filename: string) => {
    setSelectedFile(filename);
  };

  const buildFileTree = (files: GitDiffResponse['changedFiles']) => {
    const tree: Record<string, {
      type: 'file' | 'folder'
      data?: GitDiffResponse['changedFiles'][0]
      children?: Record<string, any>
    }> = {};
    
    files?.forEach(file => {
      const parts = file.filename.split('/');
      let current = tree;
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        const isFile = i === parts.length - 1;
        
        if (!current[part]) {
          current[part] = isFile 
            ? { type: 'file', data: file }
            : { type: 'folder', children: {} };
        }
        
        if (!isFile && current[part].children) {
          current = current[part].children;
        }
      }
    });
    
    return tree;
  };

  const toggleFolder = (folderPath: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderPath)) {
        newSet.delete(folderPath);
      } else {
        newSet.add(folderPath);
      }
      return newSet;
    });
  };

  const getFileStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'added': return { icon: 'A', class: 'bg-green-500 text-white' };
      case 'modified': return { icon: 'M', class: 'bg-orange-500 text-white' };
      case 'deleted': return { icon: 'D', class: 'bg-red-500 text-white' };
      case 'renamed': return { icon: 'R', class: 'bg-purple-500 text-white' };
      default: return { icon: 'M', class: 'bg-muted text-foreground' };
    }
  };

  const renderFileTreeNode = (node: {
    type: 'file' | 'folder'
    data?: GitDiffResponse['changedFiles'][0]
    children?: Record<string, any>
  }, name: string, path: string, level: number = 0) => {
    if (node.type === 'file') {
      const file = node.data;
      if (!file) return null;
      const statusInfo = getFileStatusIcon(file.status);
      
      return (
        <div
          key={path}
          className={cn(
            'flex items-center justify-between p-2 text-sm cursor-pointer hover:bg-muted/50 transition-colors rounded-md mx-1',
            selectedFile === file.filename && 'bg-primary/10 border-l-4 border-primary shadow-md'
          )}
          style={{ paddingLeft: `${16 + level * 20}px` }}
          onClick={() => selectFile(file.filename)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <div className={cn('w-5 h-5 rounded text-xs font-bold flex items-center justify-center', statusInfo.class)}>
              {statusInfo.icon}
            </div>
            <File className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="font-mono text-xs truncate text-foreground">{name}</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            {file.additions > 0 && (
              <span className="text-green-400 font-medium">+{file.additions}</span>
            )}
            {file.deletions > 0 && (
              <span className="text-red-400 font-medium">-{file.deletions}</span>
            )}
          </div>
        </div>
      );
    }

    const isExpanded = expandedFolders.has(path);
    
    return (
      <div key={path}>
        <div
          className="flex items-center gap-2 p-2 text-sm cursor-pointer hover:bg-muted/30 transition-colors rounded-md mx-1"
          style={{ paddingLeft: `${16 + level * 20}px` }}
          onClick={() => toggleFolder(path)}
        >
          {isExpanded ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}
          {isExpanded ? (
            <FolderOpen className="h-4 w-4 text-primary" />
          ) : (
            <Folder className="h-4 w-4 text-primary" />
          )}
          <span className="font-medium text-foreground">{name}</span>
        </div>
        {isExpanded && node.children && (
          <div>
            {Object.entries(node.children).map(([childName, childNode]) =>
              renderFileTreeNode(childNode, childName, `${path}/${childName}`, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  const parseAndRenderDiff = (diff: string) => {
    if (viewMode === 'split') {
      return renderSplitDiff(diff);
    }
    return renderUnifiedDiff(diff);
  };

  const renderUnifiedDiff = (diff: string) => {
    const lines = diff.split('\n');
    let oldLineNum = 1;
    let newLineNum = 1;
    
    return lines.map((line, index) => {
      let type = 'context';
      let content = line;
      
      if (line.startsWith('@@')) {
        const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);
        if (match) {
          oldLineNum = parseInt(match[1]);
          newLineNum = parseInt(match[2]);
        }
        type = 'hunk';
      } else if (line.startsWith('+') && !line.startsWith('+++')) {
        type = 'addition';
        content = line.substring(1);
        newLineNum++;
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        type = 'deletion'; 
        content = line.substring(1);
        oldLineNum++;
      } else if (!line.startsWith('+++') && !line.startsWith('---') && !line.startsWith('diff ') && !line.startsWith('index ')) {
        oldLineNum++;
        newLineNum++;
      }

      if (line.startsWith('+++') || line.startsWith('---') || line.startsWith('diff ') || line.startsWith('index ')) {
        return null;
      }

      return (
        <div
          key={index}
          className={cn(
            'flex text-sm font-mono hover:bg-muted/30 transition-colors',
            type === 'addition' && 'bg-green-500/10 border-l-2 border-green-400',
            type === 'deletion' && 'bg-red-500/10 border-l-2 border-red-400', 
            type === 'hunk' && 'bg-primary/10 border-l-2 border-primary',
            type === 'context' && 'hover:bg-muted/20'
          )}
        >
          <div className="w-12 px-2 text-right text-muted-foreground bg-muted/20 border-r border-border select-none shrink-0">
            {type === 'hunk' ? '' : type === 'deletion' ? oldLineNum - 1 : oldLineNum - 1}
          </div>
          <div className="w-12 px-2 text-right text-muted-foreground bg-muted/20 border-r border-border select-none shrink-0">
            {type === 'hunk' ? '' : type === 'addition' ? newLineNum - 1 : newLineNum - 1}
          </div>
          <div className="flex-1 px-4 whitespace-pre text-foreground min-w-0 overflow-x-auto">
            {type === 'addition' && <span className="text-green-400 mr-2 font-bold">+</span>}
            {type === 'deletion' && <span className="text-red-400 mr-2 font-bold">-</span>}
            {type === 'hunk' && <span className="text-primary mr-2 font-bold">@</span>}
            <span>{content}</span>
          </div>
        </div>
      );
    }).filter(Boolean);
  };

  const renderSplitDiff = (diff: string) => {
    const lines = diff.split('\n');
    let oldLineNum = 1;
    let newLineNum = 1;
    const splitLines: Array<{
      oldLine?: { num: number, content: string, type: string };
      newLine?: { num: number, content: string, type: string };
    }> = [];

    const parsedLines: Array<{ type: string, content: string, oldNum?: number, newNum?: number }> = [];
    
    for (const line of lines) {
      if (line.startsWith('@@')) {
        const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);
        if (match) {
          oldLineNum = parseInt(match[1]);
          newLineNum = parseInt(match[2]);
        }
        parsedLines.push({ type: 'hunk', content: line });
      } else if (line.startsWith('+') && !line.startsWith('+++')) {
        parsedLines.push({ 
          type: 'addition', 
          content: line.substring(1), 
          newNum: newLineNum++ 
        });
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        parsedLines.push({ 
          type: 'deletion', 
          content: line.substring(1), 
          oldNum: oldLineNum++ 
        });
      } else if (!line.startsWith('+++') && !line.startsWith('---') && !line.startsWith('diff ') && !line.startsWith('index ')) {
        parsedLines.push({ 
          type: 'context', 
          content: line, 
          oldNum: oldLineNum++, 
          newNum: newLineNum++ 
        });
      }
    }

    let i = 0;
    while (i < parsedLines.length) {
      const line = parsedLines[i];
      
      if (line.type === 'hunk') {
        splitLines.push({
          oldLine: { num: 0, content: line.content, type: 'hunk' },
          newLine: { num: 0, content: line.content, type: 'hunk' }
        });
        i++;
      } else if (line.type === 'deletion') {
        const deletions = [line];
        let j = i + 1;
        while (j < parsedLines.length && parsedLines[j].type === 'deletion') {
          deletions.push(parsedLines[j]);
          j++;
        }
        
        const additions = [];
        while (j < parsedLines.length && parsedLines[j].type === 'addition') {
          additions.push(parsedLines[j]);
          j++;
        }

        const maxLength = Math.max(deletions.length, additions.length);
        for (let k = 0; k < maxLength; k++) {
          const deletion = deletions[k];
          const addition = additions[k];
          
          splitLines.push({
            oldLine: deletion ? { 
              num: deletion.oldNum!,
              content: deletion.content, 
              type: 'deletion' 
            } : undefined,
            newLine: addition ? { 
              num: addition.newNum!,
              content: addition.content, 
              type: 'addition' 
            } : undefined
          });
        }
        
        i = j;
      } else if (line.type === 'addition') {
        splitLines.push({
          oldLine: undefined,
          newLine: { num: line.newNum!, content: line.content, type: 'addition' }
        });
        i++;
      } else {
        splitLines.push({
          oldLine: { num: line.oldNum!, content: line.content, type: 'context' },
          newLine: { num: line.newNum!, content: line.content, type: 'context' }
        });
        i++;
      }
    }

    return (
      <div>
        <div className="flex bg-muted/30 border-b border-border font-medium text-sm">
          <div className="flex-1 px-4 py-3 border-r border-border text-foreground">
            <span className="flex items-center gap-2">
              <Minus className="h-4 w-4 text-red-400" />
              Original
            </span>
          </div>
          <div className="flex-1 px-4 py-3 text-foreground">
            <span className="flex items-center gap-2">
              <Plus className="h-4 w-4 text-green-400" />
              Modified
            </span>
          </div>
        </div>
        
        {splitLines.map((splitLine, index) => (
          <div key={index} className="flex text-sm font-mono hover:bg-muted/20 transition-colors">
            <div className={cn(
              'flex-1 flex border-r border-border',
              splitLine.oldLine?.type === 'deletion' && 'bg-red-500/10',
              splitLine.oldLine?.type === 'hunk' && 'bg-primary/10',
              !splitLine.oldLine && 'bg-muted/10'
            )}>
              <div className="w-12 px-2 text-right text-muted-foreground bg-muted/20 border-r border-border select-none shrink-0">
                {splitLine.oldLine?.type === 'hunk' ? '' : splitLine.oldLine?.num || ''}
              </div>
              <div className="flex-1 px-4 whitespace-pre text-foreground min-w-0 overflow-x-auto">
                {splitLine.oldLine?.type === 'deletion' && (
                  <span className="text-red-400 mr-2 font-bold">-</span>
                )}
                {splitLine.oldLine?.type === 'hunk' && (
                  <span className="text-primary mr-2 font-bold">@</span>
                )}
                {splitLine.oldLine?.content || ''}
              </div>
            </div>
            
            <div className={cn(
              'flex-1 flex',
              splitLine.newLine?.type === 'addition' && 'bg-green-500/10',
              splitLine.newLine?.type === 'hunk' && 'bg-primary/10',
              !splitLine.newLine && 'bg-muted/10'
            )}>
              <div className="w-12 px-2 text-right text-muted-foreground bg-muted/20 border-r border-border select-none shrink-0">
                {splitLine.newLine?.type === 'hunk' ? '' : splitLine.newLine?.num || ''}
              </div>
              <div className="flex-1 px-4 whitespace-pre text-foreground min-w-0 overflow-x-auto">
                {splitLine.newLine?.type === 'addition' && (
                  <span className="text-green-400 mr-2 font-bold">+</span>
                )}
                {splitLine.newLine?.type === 'hunk' && (
                  <span className="text-primary mr-2 font-bold">@</span>
                )}
                {splitLine.newLine?.content || ''}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };


  if (!isVisible) {
    return null;
  }

  const fileTree = diffData ? buildFileTree(diffData.changedFiles) : {};

  return (
    <Card className="mb-6 bg-card border-border shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
              <GitBranch className="h-5 w-5 text-primary" />
            </div>
            <span className="text-foreground">PR Changes</span>
          </CardTitle>
          <div className="flex items-center gap-3">
            <Select value={viewMode} onValueChange={(value: 'unified' | 'split') => setViewMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unified">Unified</SelectItem>
                <SelectItem value="split">Split</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="sm"
              onClick={loadGitDiff}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Load Diff
            </Button>
          </div>
        </div>
        
        {diffData && (
          <div className="flex items-center gap-6 text-sm text-foreground bg-muted/30 p-4 rounded-lg border border-border/50">
            <span className="flex items-center gap-2">
              <File className="h-4 w-4 text-muted-foreground" />
              Files changed: <strong className="text-foreground">{diffData.stats?.files || 0}</strong>
            </span>
            <span className="flex items-center gap-2 text-green-400">
              <Plus className="h-4 w-4" />
              <strong>{diffData.stats?.additions || 0}</strong>
            </span>
            <span className="flex items-center gap-2 text-red-400">
              <Minus className="h-4 w-4" />
              <strong>{diffData.stats?.deletions || 0}</strong>
            </span>
          </div>
        )}
      </CardHeader>

      <CardContent className="p-0">
        {isLoading ? (
          <div className="flex items-center justify-center py-16 bg-muted/10">
            <Loader2 className="h-8 w-8 animate-spin mr-3" />
            <span className="text-muted-foreground">Loading diff...</span>
          </div>
        ) : diffData ? (
          <div className="flex border-t border-border" style={{ height: '600px' }}>
            <div className="w-80 border-r border-border overflow-y-auto bg-muted/10">
              <div className="bg-muted/30 p-4 border-b border-border font-medium sticky top-0 text-foreground">
                <span className="flex items-center gap-2">
                  <Folder className="h-4 w-4 text-primary" />
                  Files changed
                </span>
              </div>
              <div className="bg-muted/20 px-4 py-3 border-b border-border text-sm text-muted-foreground sticky top-[57px]">
                {diffData.changedFiles?.length || 0} files
              </div>
              <div className="p-2">
                {Object.entries(fileTree).map(([name, node]) =>
                  renderFileTreeNode(node, name, name, 0)
                )}
              </div>
            </div>

            <div className="flex-1 bg-card">
              {selectedFile && fileDiffs[selectedFile] ? (
                <div className="h-full overflow-auto">
                  {/* File Header */}
                  <div className="bg-muted/20 p-4 border-b border-border font-mono text-sm flex items-center justify-between sticky top-0 z-10">
                    <div className="flex items-center gap-3">
                      <File className="h-5 w-5 text-muted-foreground" />
                      <span className="font-medium text-foreground">{selectedFile}</span>
                      {(() => {
                        const file = diffData?.changedFiles.find(f => f.filename === selectedFile);
                        return file?.oldFilename && file.oldFilename !== file.filename ? (
                          <span className="text-muted-foreground">← {file.oldFilename}</span>
                        ) : null;
                      })()}
                    </div>
                    <div className="flex items-center gap-4 text-sm">
                      {(() => {
                        const file = diffData?.changedFiles.find(f => f.filename === selectedFile);
                        return (
                          <>
                            {file?.additions && file.additions > 0 && (
                              <span className="text-green-400 flex items-center gap-1 font-medium">
                                <Plus className="h-4 w-4" />
                                {file.additions}
                              </span>
                            )}
                            {file?.deletions && file.deletions > 0 && (
                              <span className="text-red-400 flex items-center gap-1 font-medium">
                                <Minus className="h-4 w-4" />
                                {file.deletions}
                              </span>
                            )}
                          </>
                        );
                      })()}
                    </div>
                  </div>
                  
                  {/* Diff Content */}
                  <div className="overflow-hidden">
                    {parseAndRenderDiff(fileDiffs[selectedFile])}
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground bg-muted/5">
                  {isLoading ? (
                    <>
                      <Loader2 className="h-8 w-8 animate-spin mr-3" />
                      Loading diff...
                    </>
                  ) : selectedFile ? (
                    <>
                      <AlertCircle className="h-8 w-8 mr-3" />
                      Diff not available for selected file
                    </>
                  ) : (
                    <>
                      <File className="h-8 w-8 mr-3" />
                      Select a file to view its diff
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center py-16 bg-muted/10">
            <div className="text-center">
              <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Enter a worktree path to load diff data</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};