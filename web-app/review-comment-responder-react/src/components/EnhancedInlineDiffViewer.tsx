import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { 
  MessageSquare, 
  Plus, 
  ChevronDown, 
  ChevronRight, 
  FileText,
  GitCommit,
  Loader2,
  AlertCircle,
  Reply,
  Bot,
  File,
  Folder,
  FolderOpen,
  Minus,
  CheckCircle,
  Shield,
  Bug,
  Gauge,
  Target,
  Lightbulb,
  Brain,
  Eye,
  Sparkles,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { cn } from '../lib/utils'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { usePullRequestComments } from '../services/bitbucket/PullRequestCommentsService'
import { ClaudeResponseModal } from './ClaudeResponseModal'
import type { 
  BitbucketPullRequest, 
  BitbucketRepository,
  DiffHunk, 
  DiffLine, 
  CommentWithPosition,
  BitbucketPRComment
} from '../types/bitbucket.types'

// AI Review Finding Types
interface AIReviewFinding {
  id: string
  text: string
  severity: 'high' | 'medium' | 'low'
  category: 'acceptance_criteria' | 'code_quality' | 'security_issues' | 'performance_issues' | 'bugs' | 'suggestions'
  file?: string
  line?: number
  suggestion?: string
  confidence: number
  auto_fixable: boolean
}

interface EnhancedReviewResults {
  session_id: string
  review_mode: string
  branch_name: string
  structured_findings: {
    acceptance_criteria: AIReviewFinding[]
    code_quality: AIReviewFinding[]
    security_issues: AIReviewFinding[]
    performance_issues: AIReviewFinding[]
    bugs: AIReviewFinding[]
    suggestions: AIReviewFinding[]
  }
  summary: {
    total_files_reviewed: number
    total_findings: number
    security_issues: number
    potential_bugs: number
    quality_suggestions: number
  }
}

interface EnhancedInlineDiffViewerProps {
  pullRequest: BitbucketPullRequest | null
  repository: BitbucketRepository | null
  aiReviewResults?: EnhancedReviewResults | null
  className?: string
  showAIFindings?: boolean
  onNavigateToFinding?: (finding: AIReviewFinding) => void
}

interface FileViewState {
  isExpanded: boolean
  showComments: boolean
  showAIFindings: boolean
}

const AI_CATEGORY_CONFIG = {
  acceptance_criteria: {
    label: 'AC Compliance',
    icon: Target,
    color: 'text-green-600',
    bgColor: 'bg-green-50 border-green-200',
    badgeColor: 'bg-green-100 text-green-800'
  },
  code_quality: {
    label: 'Code Quality',
    icon: CheckCircle,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50 border-blue-200',
    badgeColor: 'bg-blue-100 text-blue-800'
  },
  security_issues: {
    label: 'Security',
    icon: Shield,
    color: 'text-red-600',
    bgColor: 'bg-red-50 border-red-200',
    badgeColor: 'bg-red-100 text-red-800'
  },
  performance_issues: {
    label: 'Performance',
    icon: Gauge,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50 border-orange-200',
    badgeColor: 'bg-orange-100 text-orange-800'
  },
  bugs: {
    label: 'Potential Bug',
    icon: Bug,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50 border-purple-200',
    badgeColor: 'bg-purple-100 text-purple-800'
  },
  suggestions: {
    label: 'Suggestion',
    icon: Lightbulb,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50 border-yellow-200',
    badgeColor: 'bg-yellow-100 text-yellow-800'
  }
}

export const EnhancedInlineDiffViewer: React.FC<EnhancedInlineDiffViewerProps> = ({
  pullRequest,
  repository,
  aiReviewResults,
  className,
  showAIFindings = true,
  onNavigateToFinding
}) => {
  const { getCommentsWithDiff } = usePullRequestComments()
  
  const [diffData, setDiffData] = useState<{
    parsedDiff: DiffHunk[]
    commentsByFile: Record<string, CommentWithPosition[]>
    allComments: BitbucketPRComment[]
  } | null>(null)
  
  const [fileStates, setFileStates] = useState<Record<string, FileViewState>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [activeCommentForm, setActiveCommentForm] = useState<{
    filePath: string
    lineNumber: number
  } | null>(null)
  const [newCommentText, setNewCommentText] = useState('')
  const [replyForms, setReplyForms] = useState<Record<number, string>>({})
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [isClaudeModalOpen, setIsClaudeModalOpen] = useState(false)
  const [acceptedSuggestions] = useState<Set<string>>(new Set())
  const [aiSeverityFilter, setAISeverityFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all')
  const [, setSelectedAIFinding] = useState<AIReviewFinding | null>(null)
  const [selectedClaudeResponse] = useState<any | null>(null)

  // Load diff and comments when PR or repository changes
  useEffect(() => {
    if (pullRequest && repository) {
      loadDiffWithComments()
    } else {
      setDiffData(null)
      setFileStates({})
    }
  }, [pullRequest, repository])

  // Memoized AI findings by file and line
  const aiFindingsByFileAndLine = useMemo(() => {
    if (!aiReviewResults) return {}
    
    const findings: Record<string, Record<number, AIReviewFinding[]>> = {}
    
    Object.values(aiReviewResults.structured_findings).flat().forEach(finding => {
      if (finding.file && finding.line) {
        if (!findings[finding.file]) {
          findings[finding.file] = {}
        }
        if (!findings[finding.file][finding.line]) {
          findings[finding.file][finding.line] = []
        }
        findings[finding.file][finding.line].push(finding)
      }
    })
    
    return findings
  }, [aiReviewResults])

  // Memoized file grouping to prevent expensive recalculations
  const fileGroups = useMemo(() => {
    if (!diffData?.parsedDiff?.length) return {}
    
    return diffData.parsedDiff.reduce((acc, hunk) => {
      if (!acc[hunk.filePath]) {
        acc[hunk.filePath] = []
      }
      acc[hunk.filePath].push(hunk)
      return acc
    }, {} as Record<string, DiffHunk[]>)
  }, [diffData?.parsedDiff])

  // Memoized folder paths calculation
  const folderPaths = useMemo(() => {
    const paths = new Set<string>()
    const filePaths = Object.keys(fileGroups)
    
    filePaths.forEach(filePath => {
      const parts = filePath.split('/')
      let currentPath = ''
      for (let i = 0; i < parts.length - 1; i++) {
        currentPath = currentPath ? `${currentPath}/${parts[i]}` : parts[i]
        paths.add(currentPath)
      }
    })
    
    return paths
  }, [fileGroups])

  // Auto-expand folders and select first file
  useEffect(() => {
    if (folderPaths.size > 0) {
      setTimeout(() => {
        setExpandedFolders(folderPaths)
        
        const filePaths = Object.keys(fileGroups)
        if (filePaths.length > 0 && !selectedFile) {
          const firstFile = filePaths[0]
          setSelectedFile(firstFile)
          setFileStates(prev => ({
            ...prev,
            [firstFile]: {
              isExpanded: true,
              showComments: true,
              showAIFindings: showAIFindings
            }
          }))
        }
      }, 0)
    }
  }, [folderPaths, fileGroups, selectedFile, showAIFindings])

  const loadDiffWithComments = useCallback(async () => {
    if (!pullRequest || !repository) return

    try {
      setIsLoading(true)
      setError(null)

      const data = await getCommentsWithDiff(
        repository.workspace.slug,
        repository.name,
        pullRequest.id
      )

      setDiffData({
        parsedDiff: data.parsedDiff,
        commentsByFile: data.commentsData.commentsByFile,
        allComments: data.commentsData.allComments
      })

      // Initialize file states with AI findings enabled by default
      const newFileStates: Record<string, FileViewState> = {}
      data.parsedDiff.forEach(hunk => {
        if (!newFileStates[hunk.filePath]) {
          newFileStates[hunk.filePath] = {
            isExpanded: false,
            showComments: true,
            showAIFindings: showAIFindings
          }
        }
      })
      setFileStates(newFileStates)

    } catch (error) {
      console.error('Failed to load diff with comments:', error)
      setError(error instanceof Error ? error.message : 'Failed to load diff and comments')
    } finally {
      setIsLoading(false)
    }
  }, [pullRequest, repository, getCommentsWithDiff, showAIFindings])

  const getAIFindingsForLine = (filePath: string, lineNumber: number): AIReviewFinding[] => {
    return aiFindingsByFileAndLine[filePath]?.[lineNumber] || []
  }

  const getCommentsForLine = (filePath: string, lineNumber: number): CommentWithPosition[] => {
    if (!diffData) return []
    const fileComments = diffData.commentsByFile[filePath] || []
    return fileComments.filter(comment => 
      comment.position.line === lineNumber && !comment.parent
    )
  }

  const getAllFindingsForLine = (filePath: string, lineNumber: number) => {
    const manualComments = getCommentsForLine(filePath, lineNumber)
    const aiFindings = getAIFindingsForLine(filePath, lineNumber)
    return { manualComments, aiFindings }
  }

  const getFileStats = (filePath: string, hunks: DiffHunk[]) => {
    let additions = 0
    let deletions = 0
    
    hunks.forEach(hunk => {
      hunk.lines.forEach(line => {
        if (line.type === 'addition') additions++
        if (line.type === 'deletion') deletions++
      })
    })
    
    // Count AI findings for this file
    const aiFindings = aiReviewResults ? 
      Object.values(aiReviewResults.structured_findings).flat()
        .filter(finding => finding.file === filePath) : []
    
    return { 
      additions, 
      deletions, 
      aiFindings: aiFindings.length,
      highSeverityAI: aiFindings.filter(f => f.severity === 'high').length
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const toggleFileExpansion = (filePath: string) => {
    setFileStates(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        isExpanded: !prev[filePath]?.isExpanded
      }
    }))
  }

  const toggleFileAIFindings = (filePath: string) => {
    setFileStates(prev => ({
      ...prev,
      [filePath]: {
        ...prev[filePath],
        showAIFindings: !prev[filePath]?.showAIFindings
      }
    }))
  }

  const scrollToFile = (filePath: string) => {
    setSelectedFile(filePath)
    setFileStates(prev => {
      const newStates = { ...prev }
      Object.keys(newStates).forEach(key => {
        newStates[key] = { ...newStates[key], isExpanded: false }
      })
      newStates[filePath] = { ...newStates[filePath], isExpanded: true }
      return newStates
    })
    setTimeout(() => {
      const element = document.getElementById(`file-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    }, 100)
  }

  const renderAIFindingCard = (finding: AIReviewFinding) => {
    const config = AI_CATEGORY_CONFIG[finding.category]
    
    return (
      <div 
        key={finding.id}
        className={`p-3 rounded-lg border-l-4 ${config.bgColor} transition-all duration-200 hover:shadow-sm`}
      >
        <div className="flex items-start justify-between gap-3 mb-2">
          <div className="flex items-center gap-2">
            <config.icon className={`h-4 w-4 ${config.color}`} />
            <Badge variant="outline" className={`text-xs ${config.badgeColor}`}>
              {config.label}
            </Badge>
            <Badge 
              variant="outline" 
              className={`text-xs ${getSeverityColor(finding.severity)}`}
            >
              {finding.severity.toUpperCase()}
            </Badge>
            {finding.confidence && (
              <Badge variant="secondary" className="text-xs">
                {Math.round(finding.confidence * 100)}% confidence
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            <Brain className="h-4 w-4 text-primary" />
            <span className="text-xs text-primary font-medium">AI</span>
          </div>
        </div>

        <p className="text-sm text-foreground mb-2 leading-relaxed">{finding.text}</p>

        {finding.suggestion && (
          <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">AI Suggestion</span>
              {finding.auto_fixable && (
                <Badge variant="outline" className="text-xs bg-green-100 text-green-800">
                  Auto-fixable
                </Badge>
              )}
            </div>
            <p className="text-sm text-blue-700">{finding.suggestion}</p>
          </div>
        )}

        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Eye className="h-3 w-3" />
            <span>Line {finding.line}</span>
          </div>
          
          <div className="flex gap-1">
            {finding.auto_fixable && (
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-6 px-2"
                onClick={() => setSelectedAIFinding(finding)}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Apply Fix
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-6 px-2"
              onClick={() => onNavigateToFinding?.(finding)}
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    )
  }

  const renderCommentThread = (comment: BitbucketPRComment, isReply: boolean = false) => {
    const replies = diffData?.allComments.filter(c => c.parent?.id === comment.id) || []
    
    return (
      <div key={comment.id} className={cn(
        'border rounded-lg bg-card/50 backdrop-blur-sm transition-all duration-200 p-3',
        isReply && 'ml-6 mt-2 border-l-2 border-l-primary/30'
      )}>
        <div className="flex items-start gap-3">
          <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center shrink-0">
            <span className="text-xs font-medium text-primary">
              {comment.user.display_name.charAt(0).toUpperCase()}
            </span>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <span className="font-medium text-sm">{comment.user.display_name}</span>
              <span className="text-xs text-muted-foreground">
                {new Date(comment.created_on).toLocaleDateString()}
              </span>
              <Badge variant="outline" className="text-xs bg-gray-100 text-gray-800">
                Manual
              </Badge>
            </div>
            
            <div className="text-sm text-foreground prose prose-sm max-w-none">
              {comment.content.raw}
            </div>

            {!isReply && !comment.deleted && (
              <div className="mt-3">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyForms(prev => ({ 
                      ...prev, 
                      [comment.id]: prev[comment.id] || '' 
                    }))}
                    className="text-xs"
                  >
                    <Reply className="h-3 w-3 mr-1" />
                    Reply
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {}} // handleGenerateClaudeResponse
                    className="text-xs text-primary"
                  >
                    <Bot className="h-3 w-3 mr-1" />
                    Generate AI Response
                  </Button>
                </div>

                {replyForms[comment.id] !== undefined && (
                  <div className="mt-2 space-y-2">
                    <textarea
                      value={replyForms[comment.id]}
                      onChange={(e) => setReplyForms(prev => ({ 
                        ...prev, 
                        [comment.id]: e.target.value 
                      }))}
                      placeholder="Write a reply..."
                      className="w-full min-h-20 p-3 rounded-lg border border-border bg-background text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                    <div className="flex gap-2">
                      <Button size="sm" disabled={!replyForms[comment.id]?.trim()}>
                        Reply
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setReplyForms(prev => ({ 
                          ...prev, 
                          [comment.id]: '' 
                        }))}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {replies.map(reply => renderCommentThread(reply, true))}
      </div>
    )
  }

  const renderDiffLine = (line: DiffLine, lineIndex: number, hunk: DiffHunk) => {
    const lineNumber = line.newLineNumber || line.oldLineNumber || 0
    const { manualComments, aiFindings } = getAllFindingsForLine(hunk.filePath, lineNumber)
    const hasManualComments = manualComments.length > 0
    const hasAIFindings = aiFindings.length > 0
    const hasAnyFindings = hasManualComments || hasAIFindings
    const isActiveCommentForm = activeCommentForm?.filePath === hunk.filePath && 
                               activeCommentForm?.lineNumber === lineNumber

    // Filter AI findings by severity if needed
    const filteredAIFindings = aiSeverityFilter === 'all' ? 
      aiFindings : 
      aiFindings.filter(f => f.severity === aiSeverityFilter)

    const fileState = fileStates[hunk.filePath]

    return (
      <React.Fragment key={`${hunk.filePath}-${lineIndex}`}>
        {/* Diff line */}
        <div
          id={`line-${hunk.filePath.replace(/[^a-zA-Z0-9]/g, '-')}-${lineNumber}`}
          className={cn(
            'flex items-center hover:bg-muted/30 group transition-colors',
            line.type === 'addition' && 'bg-green-500/10 border-l-2 border-l-green-500',
            line.type === 'deletion' && 'bg-red-500/10 border-l-2 border-l-red-500',
            line.type === 'context' && 'bg-background'
          )}
        >
          {/* Line numbers */}
          <div className="flex shrink-0 text-xs text-muted-foreground font-mono">
            <span className="w-12 text-right px-2 py-1 border-r border-border">
              {line.oldLineNumber || ''}
            </span>
            <span className="w-12 text-right px-2 py-1 border-r border-border">
              {line.newLineNumber || ''}
            </span>
          </div>

          {/* Finding indicators and add comment button */}
          <div className="w-16 flex items-center justify-center shrink-0 gap-1">
            {line.newLineNumber && (
              <>
                {/* AI findings indicator */}
                {hasAIFindings && (
                  <div className="w-2 h-2 bg-primary rounded-full" title={`${aiFindings.length} AI findings`} />
                )}
                
                {/* Manual comment indicator / Add comment button */}
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'w-6 h-6 p-0 transition-opacity',
                    hasManualComments ? 'opacity-100 bg-blue-500/10 text-blue-500' : 'opacity-0 group-hover:opacity-100'
                  )}
                  onClick={() => {}} // handleAddComment
                >
                  {hasManualComments ? (
                    <MessageSquare className="h-3 w-3" />
                  ) : (
                    <Plus className="h-3 w-3" />
                  )}
                </Button>
              </>
            )}
          </div>

          {/* Line content */}
          <div className="flex-1 px-3 py-1 font-mono text-sm">
            <span className={cn(
              line.type === 'addition' && 'text-green-600 dark:text-green-400',
              line.type === 'deletion' && 'text-red-600 dark:text-red-400'
            )}>
              {line.type === 'addition' && '+'}
              {line.type === 'deletion' && '-'}
              {line.type === 'context' && ' '}
            </span>
            {line.content}
          </div>
        </div>

        {/* Comments and AI findings for this line */}
        {hasAnyFindings && (
          <div className="bg-muted/20 border-l-2 border-l-primary/30 pl-8 pr-4 py-3">
            <div className="space-y-3">
              {/* Manual Comments */}
              {hasManualComments && fileState?.showComments && 
                manualComments.map(comment => renderCommentThread(comment))
              }
              
              {/* AI Findings */}
              {hasAIFindings && fileState?.showAIFindings && 
                filteredAIFindings.map(finding => renderAIFindingCard(finding))
              }
            </div>
          </div>
        )}

        {/* Active comment form */}
        {isActiveCommentForm && (
          <div className="bg-primary/5 border-l-2 border-l-primary pl-8 pr-4 py-3">
            <div className="space-y-3">
              <textarea
                value={newCommentText}
                onChange={(e) => setNewCommentText(e.target.value)}
                placeholder="Add a comment..."
                className="w-full min-h-20 p-3 rounded-lg border border-border bg-background text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                autoFocus
              />
              <div className="flex gap-2">
                <Button size="sm" disabled={!newCommentText.trim()}>
                  Add Comment
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveCommentForm(null)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        )}
      </React.Fragment>
    )
  }

  const renderFileHunk = (hunk: DiffHunk, hunkIndex: number) => {
    const fileState = fileStates[hunk.filePath]
    if (!fileState?.isExpanded) return null

    return (
      <div key={`${hunk.filePath}-hunk-${hunkIndex}-${hunk.oldStart}-${hunk.newStart}`} className="border rounded-lg overflow-hidden">
        <div className="bg-muted/30 px-4 py-2 text-sm font-mono text-muted-foreground border-b">
          @@ -{hunk.oldStart},{hunk.oldLines} +{hunk.newStart},{hunk.newLines} @@ {hunk.header}
        </div>
        
        <div className="overflow-x-auto">
          {hunk.lines.map((line, lineIndex) => renderDiffLine(line, lineIndex, hunk))}
        </div>
      </div>
    )
  }

  const renderFileTreeNode = (node: any, name: string, path: string, level: number = 0): React.JSX.Element => {
    if (node.type === 'file') {
      const isSelected = selectedFile === node.path
      const stats = getFileStats(node.path, fileGroups[node.path] || [])
      const allFileComments = diffData?.allComments.filter(c => c.inline?.path === node.path && !c.parent) || []
      
      return (
        <div
          key={path}
          className={cn(
            'flex items-center justify-between px-2 py-1.5 text-sm cursor-pointer hover:bg-muted/50 transition-colors rounded-md mx-1',
            isSelected && 'bg-primary/10 border-l-2 border-primary'
          )}
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => scrollToFile(node.path)}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            <File className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="font-mono text-xs truncate">{name}</span>
            <div className="flex items-center gap-1">
              {allFileComments.length > 0 && (
                <Badge variant="outline" className="h-5 px-1.5 text-xs">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  {allFileComments.length}
                </Badge>
              )}
              {stats.aiFindings > 0 && (
                <Badge variant="outline" className="h-5 px-1.5 text-xs bg-primary/10 text-primary">
                  <Brain className="h-3 w-3 mr-1" />
                  {stats.aiFindings}
                </Badge>
              )}
              {stats.highSeverityAI > 0 && (
                <Badge variant="destructive" className="h-5 px-1.5 text-xs">
                  {stats.highSeverityAI} HIGH
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2 text-xs">
            {stats.additions > 0 && (
              <span className="text-green-500 font-medium">+{stats.additions}</span>
            )}
            {stats.deletions > 0 && (
              <span className="text-red-500 font-medium">-{stats.deletions}</span>
            )}
          </div>
        </div>
      )
    }

    const isExpanded = expandedFolders.has(path)
    
    return (
      <div key={path}>
        <div
          className="flex items-center gap-1.5 px-2 py-1.5 text-sm cursor-pointer hover:bg-muted/30 transition-colors rounded-md mx-1"
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => {}} // toggleFolder
        >
          {isExpanded ? (
            <ChevronDown className="h-3.5 w-3.5 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-3.5 w-3.5 text-muted-foreground" />
          )}
          {isExpanded ? (
            <FolderOpen className="h-4 w-4 text-primary" />
          ) : (
            <Folder className="h-4 w-4 text-muted-foreground" />
          )}
          <span className="font-medium">{name}</span>
        </div>
        {isExpanded && (
          <div>
            {Object.entries(node.children).map(([childName, childNode]) =>
              renderFileTreeNode(childNode, childName, `${path}/${childName}`, level + 1)
            )}
          </div>
        )}
      </div>
    )
  }

  // Return loading and error states early
  if (!pullRequest) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Select Pull Request</h3>
          <p className="text-muted-foreground">Choose a pull request to view its diff and AI review findings.</p>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-muted-foreground">Loading diff and AI review results...</p>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Error</h3>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={loadDiffWithComments} variant="outline">Retry</Button>
        </CardContent>
      </Card>
    )
  }

  if (!diffData || diffData.parsedDiff.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <GitCommit className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">No Changes</h3>
          <p className="text-muted-foreground">This pull request doesn't have any file changes.</p>
        </CardContent>
      </Card>
    )
  }

  const buildFileTree = (filePaths: string[]) => {
    const tree: Record<string, any> = {}
    
    filePaths.forEach(filePath => {
      const parts = filePath.split('/')
      let current = tree
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i]
        const isFile = i === parts.length - 1
        
        if (!current[part]) {
          current[part] = isFile 
            ? { type: 'file', path: filePath }
            : { type: 'folder', children: {} }
        }
        
        if (!isFile) {
          current = current[part].children
        }
      }
    })
    
    return tree
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Enhanced PR Diff & AI Review
            <Badge variant="outline" className="ml-2">
              {diffData.allComments.length} comments
            </Badge>
            {aiReviewResults && (
              <Badge variant="outline" className="bg-primary/10 text-primary">
                <Brain className="h-3 w-3 mr-1" />
                {Object.values(aiReviewResults.structured_findings).flat().length} AI findings
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {/* AI Severity Filter */}
            {aiReviewResults && (
              <Select value={aiSeverityFilter} onValueChange={(value: any) => setAISeverityFilter(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All AI</SelectItem>
                  <SelectItem value="high">High Only</SelectItem>
                  <SelectItem value="medium">Medium Only</SelectItem>
                  <SelectItem value="low">Low Only</SelectItem>
                </SelectContent>
              </Select>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={loadDiffWithComments}
              className="flex items-center gap-2"
            >
              <Loader2 className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardTitle>
        
        <div className="text-sm text-muted-foreground">
          <p>PR #{pullRequest.id}: {pullRequest.title}</p>
          <p>{Object.keys(fileGroups).length} files changed</p>
          {aiReviewResults && (
            <p className="text-primary">
              AI Review: {aiReviewResults.review_mode} mode • {aiReviewResults.summary.total_findings} total findings
            </p>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="flex border-t border-border" style={{ height: '700px' }}>
          {/* File Tree Sidebar */}
          <div className="w-80 border-r border-border overflow-y-auto bg-muted/5">
            <div className="bg-muted/20 p-3 border-b border-border font-medium sticky top-0 z-10">
              <span className="flex items-center gap-2 text-sm">
                <Folder className="h-4 w-4 text-primary" />
                Files changed ({Object.keys(fileGroups).length})
              </span>
            </div>
            
            {/* Enhanced Summary */}
            <div className="px-3 py-2 border-b border-border bg-muted/10 space-y-2">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-3.5 w-3.5 text-muted-foreground" />
                  <span className="font-medium">Manual Comments</span>
                </div>
                <Badge variant="secondary" className="h-5 text-xs">
                  {diffData.allComments.filter(c => !c.parent).length}
                </Badge>
              </div>
              
              {aiReviewResults && (
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-2">
                    <Brain className="h-3.5 w-3.5 text-primary" />
                    <span className="font-medium">AI Findings</span>
                  </div>
                  <Badge variant="outline" className="h-5 text-xs bg-primary/10 text-primary">
                    {Object.values(aiReviewResults.structured_findings).flat().length}
                  </Badge>
                </div>
              )}
            </div>
            
            <div className="py-2">
              {(() => {
                const fileTree = buildFileTree(Object.keys(fileGroups))
                return Object.entries(fileTree).map(([name, node]) =>
                  renderFileTreeNode(node, name, name, 0)
                )
              })()}
            </div>
          </div>

          {/* Enhanced Diff Content Area */}
          <div className="flex-1 overflow-y-auto">
            {selectedFile && fileGroups[selectedFile] ? (
              <div className="space-y-6">
                {(() => {
                  const filePath = selectedFile
                  const hunks = fileGroups[selectedFile]
                  const fileState = fileStates[filePath]
                  const fileStats = getFileStats(filePath, hunks)
                  
                  return (
                    <div 
                      key={filePath} 
                      id={`file-${filePath.replace(/[^a-zA-Z0-9]/g, '-')}`}
                      className="border-b border-border last:border-b-0"
                    >
                      {/* Enhanced File header */}
                      <div className="bg-muted/20 px-4 py-3 flex items-center justify-between sticky top-0 z-10 border-b border-border">
                        <div className="flex items-center gap-3">
                          <button onClick={() => toggleFileExpansion(filePath)}>
                            {fileState?.isExpanded ? (
                              <ChevronDown className="h-4 w-4 text-muted-foreground" />
                            ) : (
                              <ChevronRight className="h-4 w-4 text-muted-foreground" />
                            )}
                          </button>
                          
                          <File className="h-4 w-4 text-muted-foreground" />
                          <span className="font-mono text-sm font-medium">{filePath}</span>
                          
                          <div className="flex items-center gap-2">
                            {diffData.commentsByFile[filePath]?.length > 0 && (
                              <Badge variant="secondary" className="text-xs">
                                <MessageSquare className="h-3 w-3 mr-1" />
                                {diffData.commentsByFile[filePath].length}
                              </Badge>
                            )}
                            
                            {fileStats.aiFindings > 0 && (
                              <Badge variant="outline" className="text-xs bg-primary/10 text-primary">
                                <Brain className="h-3 w-3 mr-1" />
                                {fileStats.aiFindings}
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          {/* Show AI Findings toggle */}
                          {fileStats.aiFindings > 0 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleFileAIFindings(filePath)}
                              className={`text-xs ${fileState?.showAIFindings ? 'bg-primary/10 text-primary' : ''}`}
                            >
                              <Brain className="h-3 w-3 mr-1" />
                              AI Findings
                            </Button>
                          )}
                          
                          <div className="flex items-center gap-3 text-sm">
                            {fileStats.additions > 0 && (
                              <span className="text-green-500 flex items-center gap-1">
                                <Plus className="h-3 w-3" />
                                {fileStats.additions}
                              </span>
                            )}
                            {fileStats.deletions > 0 && (
                              <span className="text-red-500 flex items-center gap-1">
                                <Minus className="h-3 w-3" />
                                {fileStats.deletions}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* File content with enhanced rendering */}
                      {fileState?.isExpanded && (
                        <div className="space-y-0">
                          {hunks.map((hunk, hunkIndex) => renderFileHunk(hunk, hunkIndex))}
                        </div>
                      )}
                    </div>
                  )
                })()}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <File className="h-12 w-12 mx-auto mb-4" />
                  <p>Select a file to view its diff and AI review</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Enhanced Claude Response Modal */}
      <ClaudeResponseModal
        isOpen={isClaudeModalOpen}
        onClose={() => setIsClaudeModalOpen(false)}
        responseData={selectedClaudeResponse}
        isLoading={false}
        onAcceptChange={() => {}}
        onRejectChange={() => {}}
        onUpdateInstructions={() => {}}
        onGlobalUpdate={() => {}}
        acceptedSuggestions={acceptedSuggestions}
      />
    </Card>
  )
}