import React from 'react'
import { 
  BookOpen,
  Download
} from 'lucide-react'
import { But<PERSON> } from './ui/button'
import { <PERSON><PERSON><PERSON>enderer } from './markdown/MarkdownRenderer'
import type { StructuredReviewResult, TutorialData } from '../services/codeReviewer/CodeReviewerService'
import type { EnhancedReviewResults } from '../types/enhanced-review'

interface TutorialSummaryViewerProps {
  results: StructuredReviewResult | EnhancedReviewResults
  onExportTutorial?: (tutorial: TutorialData) => void
  className?: string
}

export const TutorialSummaryViewer: React.FC<TutorialSummaryViewerProps> = ({
  results,
  onExportTutorial,
  className = ''
}) => {
  // Extract tutorial data from results
  const tutorialData = React.useMemo(() => {
    console.log('🔍 TutorialSummaryViewer - Processing results:', results)

    if ('phase3_summary' in results && results.phase3_summary) {
      console.log('📚 Found phase3_summary:', results.phase3_summary)
      return results.phase3_summary as TutorialData
    }
    if ('tutorial' in results && results.tutorial) {
      console.log('📚 Found tutorial:', results.tutorial)
      return results.tutorial as TutorialData
    }

    console.log('⚠️ No tutorial data found in results')
    return null
  }, [results])

  // Extract raw markdown content
  const markdownContent = React.useMemo(() => {
    if (!tutorialData) {
      return `# Implementation Tutorial Not Available

This review session does not have tutorial data available yet.

## Available Options
- Generate a new tutorial using the tutorial generation feature
- View the main review results for detailed findings
- Export the current review data

## Tutorial Features
When available, the tutorial will include:
- **Business Context**: Understanding the impact and value of changes
- **Architecture Overview**: Technical design and implementation patterns  
- **Implementation Guide**: Step-by-step instructions for similar implementations
- **Testing Strategy**: Comprehensive testing approaches and recommendations
- **Deployment Guide**: Best practices for deploying these changes

*Generate a tutorial to see comprehensive implementation guidance.*`
    }

    // Use raw_content if available, otherwise fallback message
    if (tutorialData.raw_content && tutorialData.raw_content.trim().length > 0) {
      console.log('📋 Using raw_content for tutorial display')
      return tutorialData.raw_content
    }

    return `# Tutorial Generation in Progress

The comprehensive tutorial for this review is currently being processed.

## Tutorial Information
- **Tutorial ID**: ${tutorialData.tutorial_id || 'Unknown'}
- **Status**: Processing
- **Session**: ${tutorialData.metadata?.session_id || 'Unknown'}

## What's Being Generated
This tutorial will provide comprehensive implementation guidance including business context, architecture overview, detailed implementation steps, testing strategies, and deployment considerations.

*Please check back in a few moments or refresh the page to see the completed tutorial.*`
  }, [tutorialData])

  // Handle export functionality
  const handleExport = React.useCallback(() => {
    if (onExportTutorial && tutorialData) {
      onExportTutorial(tutorialData)
    } else {
      // Fallback: download as markdown file
      const blob = new Blob([markdownContent], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `implementation-tutorial-${new Date().toISOString().split('T')[0]}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }, [markdownContent, tutorialData, onExportTutorial])

  return (
    <div className={`tutorial-summary-viewer ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <BookOpen className="h-6 w-6 text-emerald-600" />
          <h2 className="text-2xl font-bold">Implementation Tutorial</h2>
        </div>
        
        {/* Export button */}
        <Button
          variant="outline"
          onClick={handleExport}
          className="flex items-center gap-2"
        >
          <Download size={16} />
          Export Tutorial
        </Button>
      </div>

      {/* Tutorial Content - Using MarkdownRenderer like Code Review */}
      <div className="tutorial-content rounded-lg border border-border bg-card text-card-foreground shadow-xl shadow-black/20 transition-all duration-300 hover:shadow-2xl hover:shadow-black/30 hover:border-primary/20 animate-fade-in">
        <MarkdownRenderer
          content={markdownContent}
          className="p-6"
          enableCopy={true}
          enableAnchors={true}
          title="Implementation Tutorial"
        />
      </div>
    </div>
  )
}