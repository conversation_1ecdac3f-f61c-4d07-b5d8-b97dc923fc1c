import React, { useCallback, useRef } from 'react'
import { Upload } from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { useCommentStore } from '../store/useCommentStore'

export const CommentUploader: React.FC = () => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { addComment } = useCommentStore()

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    
    const files = Array.from(e.dataTransfer.files)
    processFiles(files)
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      processFiles(Array.from(e.target.files))
    }
  }, [])

  const handlePaste = useCallback((e: ClipboardEvent) => {
    const items = Array.from(e.clipboardData?.items || [])
    const imageItems = items.filter(item => item.type.startsWith('image/'))
    
    imageItems.forEach(item => {
      const file = item.getAsFile()
      if (file) processFiles([file])
    })
  }, [])

  const processFiles = useCallback((files: File[]) => {
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          if (e.target?.result) {
            addComment({
              file: file,
              dataUrl: e.target.result as string,
              fileName: file.name,
              uploadTime: new Date().toISOString(),
              status: 'pending',
              response: null,
              extractedData: null
            })
          }
        }
        reader.readAsDataURL(file)
      }
    })
  }, [addComment])

  // Setup paste handler
  React.useEffect(() => {
    document.addEventListener('paste', handlePaste)
    return () => document.removeEventListener('paste', handlePaste)
  }, [handlePaste])

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <Card className="mb-8 group">
      <CardContent className="p-0">
        <div
          className="border-2 border-dashed border-primary/30 rounded-lg p-16 text-center cursor-pointer transition-all duration-300 group-hover:border-primary/60 hover:bg-primary/5 hover:shadow-lg hover:shadow-primary/10 active:scale-[0.98]"
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onClick={openFileDialog}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-primary/20 rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <Upload className="relative mx-auto h-20 w-20 text-primary/60 group-hover:text-primary transition-all duration-300 group-hover:scale-110" />
          </div>
          <h3 className="text-2xl font-bold text-foreground mb-2 group-hover:text-primary transition-colors duration-300">
            Drop Review Comment Screenshots Here
          </h3>
          <p className="text-muted-foreground mb-4 text-lg">
            or paste with{' '}
            <kbd className="px-3 py-2 bg-primary/10 rounded text-sm font-mono border border-primary/30 text-primary">
              Ctrl+V
            </kbd>
          </p>
          <p className="text-sm text-muted-foreground/70">
            Supports PNG, JPG, and other image formats
          </p>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={handleFileSelect}
          />
        </div>
      </CardContent>
    </Card>
  )
}