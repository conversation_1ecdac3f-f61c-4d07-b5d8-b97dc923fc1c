import React from 'react'
import { 
  Bug, 
  AlertTriangle, 
  Zap, 
  FileText,
  ChevronDown,
  ChevronRight,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Code2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

interface BugDetectionData {
  critical_bugs: Array<{
    text: string
    severity: string
    file?: string
    type: string
  }>
  logic_errors: Array<{
    text: string
    severity: string
    file?: string
    type: string
  }>
  runtime_risks: Array<{
    text: string
    severity: string
    file?: string
    type: string
  }>
}

interface BugDetectionSectionProps {
  data: BugDetectionData
  expanded: boolean
  onToggle: () => void
}

const getSeverityBadge = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'high':
    case 'critical':
      return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">🚨 CRITICAL</Badge>
    case 'medium':
      return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">⚠️ MEDIUM</Badge>
    case 'low':
      return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">ℹ️ LOW</Badge>
    default:
      return <Badge variant="outline" className="border-gray-500 text-gray-400">UNKNOWN</Badge>
  }
}

const getTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'critical':
      return <XCircle className="h-4 w-4 text-red-400" />
    case 'logic':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />
    case 'runtime':
      return <Zap className="h-4 w-4 text-orange-400" />
    default:
      return <Bug className="h-4 w-4 text-gray-400" />
  }
}

const BugCategory: React.FC<{
  title: string
  icon: React.ReactNode
  bugs: Array<{text: string, severity: string, file?: string, type: string}>
  color: string
  emptyMessage: string
}> = ({ title, icon, bugs, color, emptyMessage }) => {
  return (
    <div className="space-y-3">
      <h4 className={`text-md font-semibold ${color} flex items-center gap-2`}>
        {icon}
        {title} ({bugs.length})
      </h4>
      
      {bugs.length > 0 ? (
        <div className="space-y-3">
          {bugs.map((bug, index) => (
            <div 
              key={index} 
              className={`p-4 bg-${color.split('-')[1]}-500/10 border border-${color.split('-')[1]}-500/20 rounded-lg hover:bg-${color.split('-')[1]}-500/15 transition-colors`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    {getTypeIcon(bug.type)}
                  </div>
                  <div className="space-y-1">
                    <div className="text-white font-medium">
                      {bug.text}
                    </div>
                    {bug.file && (
                      <div className="flex items-center gap-2 text-sm">
                        <FileText className="h-3 w-3 text-gray-400" />
                        <span className="font-mono text-gray-400">{bug.file}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex-shrink-0">
                  {getSeverityBadge(bug.severity)}
                </div>
              </div>
              
              {/* Additional context based on type */}
              {bug.type === 'critical' && (
                <div className="mt-3 p-2 bg-red-900/30 border border-red-500/30 rounded text-sm text-red-200">
                  <strong>⚠️ Immediate Action Required:</strong> This bug could cause system failures or data corruption.
                </div>
              )}
              {bug.type === 'logic' && (
                <div className="mt-3 p-2 bg-yellow-900/30 border border-yellow-500/30 rounded text-sm text-yellow-200">
                  <strong>🧠 Logic Issue:</strong> Review business logic and edge case handling.
                </div>
              )}
              {bug.type === 'runtime' && (
                <div className="mt-3 p-2 bg-orange-900/30 border border-orange-500/30 rounded text-sm text-orange-200">
                  <strong>⚡ Runtime Risk:</strong> May cause unexpected behavior under certain conditions.
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
          <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
          <div className="text-green-400 font-medium">{emptyMessage}</div>
        </div>
      )}
    </div>
  )
}

export const BugDetectionSection: React.FC<BugDetectionSectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const { critical_bugs, logic_errors, runtime_risks } = data
  const totalBugs = critical_bugs.length + logic_errors.length + runtime_risks.length
  const criticalCount = critical_bugs.filter(bug => ['high', 'critical'].includes(bug.severity.toLowerCase())).length

  return (
    <Card className="border-red-500/30 bg-red-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-red-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Bug className="h-5 w-5 text-red-400" />
            <span className="text-red-400">🐛 BUG DETECTION RESULTS</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Total:</span>
                <span className={`font-bold ${totalBugs > 0 ? 'text-red-400' : 'text-green-400'}`}>
                  {totalBugs}
                </span>
              </div>
              {criticalCount > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Critical:</span>
                  <span className="font-bold text-red-400">{criticalCount}</span>
                </div>
              )}
            </div>
            {expanded ? (
              <ChevronDown className="h-5 w-5 text-red-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-red-400" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent className="space-y-6">
          {/* Bug Detection Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-400">{critical_bugs.length}</div>
              <div className="text-sm text-muted-foreground">Critical Bugs</div>
              <div className="text-xs text-red-300 mt-1">Must Fix Before Merge</div>
            </div>
            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-400">{logic_errors.length}</div>
              <div className="text-sm text-muted-foreground">Logic Errors</div>
              <div className="text-xs text-yellow-300 mt-1">Review Business Logic</div>
            </div>
            <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-orange-400">{runtime_risks.length}</div>
              <div className="text-sm text-muted-foreground">Runtime Risks</div>
              <div className="text-xs text-orange-300 mt-1">Potential Issues</div>
            </div>
          </div>

          {/* Overall Assessment */}
          {totalBugs === 0 ? (
            <div className="p-6 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
              <CheckCircle2 className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-400 mb-2">🎉 No Bugs Detected!</h3>
              <p className="text-green-300">
                The AI analysis found no critical bugs, logic errors, or runtime risks in this code.
                The implementation appears to be solid and well-structured.
              </p>
            </div>
          ) : (
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-red-400 mt-1" />
                <div>
                  <h4 className="font-semibold text-red-400 mb-1">Bug Detection Summary</h4>
                  <p className="text-red-300 text-sm">
                    Found {totalBugs} potential issue{totalBugs > 1 ? 's' : ''} that require attention.
                    {criticalCount > 0 && (
                      <span className="font-semibold"> {criticalCount} critical issue{criticalCount > 1 ? 's' : ''} must be fixed before merging.</span>
                    )}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Detailed Bug Categories */}
          <div className="space-y-6">
            <BugCategory
              title="Critical Bugs (Must Fix)"
              icon={<XCircle className="h-4 w-4" />}
              bugs={critical_bugs}
              color="text-red-400"
              emptyMessage="No critical bugs found - excellent!"
            />

            <BugCategory
              title="Logic Errors"
              icon={<AlertTriangle className="h-4 w-4" />}
              bugs={logic_errors}
              color="text-yellow-400"
              emptyMessage="No logic errors detected"
            />

            <BugCategory
              title="Runtime Risk Issues"
              icon={<Zap className="h-4 w-4" />}
              bugs={runtime_risks}
              color="text-orange-400"
              emptyMessage="No runtime risks identified"
            />
          </div>

          {/* Bug Prevention Recommendations */}
          {totalBugs > 0 && (
            <div className="p-4 bg-gradient-to-r from-red-500/10 to-orange-500/10 border border-red-500/30 rounded-lg">
              <h4 className="font-semibold text-red-400 mb-3 flex items-center gap-2">
                <Code2 className="h-4 w-4" />
                🛡️ Bug Prevention Recommendations
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="font-medium text-red-300">Immediate Actions:</div>
                  <ul className="space-y-1 text-gray-300">
                    {critical_bugs.length > 0 && (
                      <li className="flex items-center gap-2">
                        <XCircle className="h-3 w-3 text-red-400" />
                        Fix all {critical_bugs.length} critical bug{critical_bugs.length > 1 ? 's' : ''}
                      </li>
                    )}
                    {logic_errors.length > 0 && (
                      <li className="flex items-center gap-2">
                        <AlertTriangle className="h-3 w-3 text-yellow-400" />
                        Review business logic in {logic_errors.length} case{logic_errors.length > 1 ? 's' : ''}
                      </li>
                    )}
                    <li className="flex items-center gap-2">
                      <CheckCircle2 className="h-3 w-3 text-green-400" />
                      Add unit tests for edge cases
                    </li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <div className="font-medium text-red-300">Long-term Prevention:</div>
                  <ul className="space-y-1 text-gray-300">
                    <li className="flex items-center gap-2">
                      <Bug className="h-3 w-3 text-blue-400" />
                      Implement static code analysis
                    </li>
                    <li className="flex items-center gap-2">
                      <Code2 className="h-3 w-3 text-green-400" />
                      Add integration tests
                    </li>
                    <li className="flex items-center gap-2">
                      <AlertCircle className="h-3 w-3 text-purple-400" />
                      Set up error monitoring
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}