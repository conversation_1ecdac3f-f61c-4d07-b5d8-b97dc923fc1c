import React from 'react'
import { 
  Building2, 
  <PERSON><PERSON>, 
  Shield, 
  Gauge,
  ChevronDown,
  ChevronRight,
  CheckCircle2,
  AlertTriangle,
  XCircle,
  Network,
  Code2,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'

interface ArchitecturalData {
  design_patterns: Array<{
    pattern: string
    usage: string
    assessment: string
    recommendation?: string
  }>
  integration_quality: Array<{
    component: string
    quality: string
    issues?: string[]
    score: number
  }>
  violations: Array<{
    type: string
    description: string
    severity: string
    impact: string
  }>
}

interface ArchitecturalAssessmentSectionProps {
  data: ArchitecturalData
  expanded: boolean
  onToggle: () => void
}

const getAssessmentColor = (assessment: string) => {
  switch (assessment.toLowerCase()) {
    case 'excellent':
    case 'good':
      return 'text-green-400'
    case 'fair':
    case 'adequate':
      return 'text-yellow-400'
    case 'poor':
    case 'needs improvement':
      return 'text-red-400'
    default:
      return 'text-gray-400'
  }
}

const getAssessmentIcon = (assessment: string) => {
  switch (assessment.toLowerCase()) {
    case 'excellent':
    case 'good':
      return <CheckCircle2 className="h-4 w-4 text-green-400" />
    case 'fair':
    case 'adequate':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />
    case 'poor':
    case 'needs improvement':
      return <XCircle className="h-4 w-4 text-red-400" />
    default:
      return <AlertTriangle className="h-4 w-4 text-gray-400" />
  }
}

const getSeverityBadge = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'high':
    case 'critical':
      return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">HIGH</Badge>
    case 'medium':
      return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">MEDIUM</Badge>
    case 'low':
      return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">LOW</Badge>
    default:
      return <Badge variant="outline" className="border-gray-500 text-gray-400">UNKNOWN</Badge>
  }
}

const getQualityScore = (score: number) => {
  if (score >= 8) return { color: 'text-green-400', label: 'Excellent' }
  if (score >= 6) return { color: 'text-yellow-400', label: 'Good' }
  if (score >= 4) return { color: 'text-orange-400', label: 'Fair' }
  return { color: 'text-red-400', label: 'Poor' }
}

export const ArchitecturalAssessmentSection: React.FC<ArchitecturalAssessmentSectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const { design_patterns, integration_quality, violations } = data
  
  // Calculate overall scores
  const avgIntegrationScore = integration_quality.length > 0 
    ? integration_quality.reduce((sum, item) => sum + item.score, 0) / integration_quality.length 
    : 8

  const overallScore = getQualityScore(avgIntegrationScore)
  const hasViolations = violations.length > 0
  const criticalViolations = violations.filter(v => v.severity.toLowerCase() === 'high' || v.severity.toLowerCase() === 'critical').length

  return (
    <Card className="border-cyan-500/30 bg-cyan-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-cyan-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Building2 className="h-5 w-5 text-cyan-400" />
            <span className="text-cyan-400">🏗️ ARCHITECTURAL ASSESSMENT</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Quality:</span>
                <span className={`font-bold ${overallScore.color}`}>
                  {Math.round(avgIntegrationScore)}/10
                </span>
              </div>
              {hasViolations && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Violations:</span>
                  <span className="font-bold text-red-400">{violations.length}</span>
                </div>
              )}
            </div>
            {expanded ? (
              <ChevronDown className="h-5 w-5 text-cyan-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-cyan-400" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent className="space-y-6">
          {/* Architectural Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-cyan-500/10 border border-cyan-500/20 rounded-lg text-center">
              <div className={`text-2xl font-bold ${overallScore.color}`}>
                {Math.round(avgIntegrationScore)}/10
              </div>
              <div className="text-sm text-muted-foreground">Overall Architecture</div>
              <div className={`text-xs font-medium ${overallScore.color} mt-1`}>
                {overallScore.label}
              </div>
              <Progress 
                value={avgIntegrationScore * 10} 
                className="h-2 bg-cyan-900/50 mt-2" 
              />
            </div>
            <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-400">{design_patterns.length}</div>
              <div className="text-sm text-muted-foreground">Design Patterns</div>
              <div className="text-xs text-purple-300 mt-1">Identified & Analyzed</div>
            </div>
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-400">{violations.length}</div>
              <div className="text-sm text-muted-foreground">Violations</div>
              <div className="text-xs text-red-300 mt-1">
                {criticalViolations > 0 ? `${criticalViolations} Critical` : 'None Critical'}
              </div>
            </div>
          </div>

          {/* Design Patterns Analysis */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-cyan-400 flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Design Pattern Usage
            </h3>
            
            {design_patterns.length > 0 ? (
              <div className="space-y-3">
                {design_patterns.map((pattern, index) => (
                  <div key={index} className="p-4 bg-cyan-500/10 border border-cyan-500/20 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div className="space-y-1">
                        <div className="font-semibold text-cyan-300">
                          {pattern.pattern}
                        </div>
                        <div className="text-sm text-gray-300">
                          <span className="text-muted-foreground">Usage:</span> {pattern.usage}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getAssessmentIcon(pattern.assessment)}
                        <Badge className={`${getAssessmentColor(pattern.assessment)} border-current/30`}>
                          {pattern.assessment.toUpperCase()}
                        </Badge>
                      </div>
                    </div>
                    {pattern.recommendation && (
                      <div className="mt-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded">
                        <div className="text-sm">
                          <span className="text-blue-300 font-medium">💡 Recommendation:</span>
                          <div className="text-blue-200 mt-1">{pattern.recommendation}</div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 bg-gray-800/30 border border-gray-700 rounded-lg text-center">
                <Code2 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <div className="text-gray-400">No specific design patterns identified</div>
                <div className="text-sm text-gray-500 mt-1">
                  Code appears to follow standard patterns
                </div>
              </div>
            )}
          </div>

          {/* Integration Quality */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-cyan-400 flex items-center gap-2">
              <Network className="h-5 w-5" />
              Integration Quality Assessment
            </h3>
            
            {integration_quality.length > 0 ? (
              <div className="space-y-3">
                {integration_quality.map((integration, index) => {
                  const scoreInfo = getQualityScore(integration.score)
                  return (
                    <div key={index} className="p-4 bg-gray-800/30 border border-gray-700 rounded-lg">
                      <div className="flex items-start justify-between mb-2">
                        <div className="space-y-1">
                          <div className="font-semibold text-white">
                            {integration.component}
                          </div>
                          <div className="text-sm text-gray-300">
                            Quality Assessment: <span className={scoreInfo.color}>{integration.quality}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`text-sm font-semibold ${scoreInfo.color}`}>
                            {integration.score}/10
                          </span>
                          <Badge className={`${scoreInfo.color} border-current/30`}>
                            {scoreInfo.label.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                      
                      {integration.issues && integration.issues.length > 0 && (
                        <div className="mt-3 space-y-2">
                          <div className="text-sm font-medium text-yellow-400">Issues Identified:</div>
                          <div className="space-y-1">
                            {integration.issues.map((issue, issueIndex) => (
                              <div key={issueIndex} className="text-sm text-yellow-300 pl-4 border-l-2 border-yellow-500/50">
                                {issue}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-green-400 font-medium">Good integration quality</div>
                <div className="text-sm text-green-300 mt-1">
                  Components integrate well with existing architecture
                </div>
              </div>
            )}
          </div>

          {/* Architectural Violations */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-red-400 flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Architectural Violations
            </h3>
            
            {violations.length > 0 ? (
              <div className="space-y-3">
                {violations.map((violation, index) => (
                  <div key={index} className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                    <div className="flex items-start justify-between mb-2">
                      <div className="space-y-1">
                        <div className="font-semibold text-red-300">
                          {violation.type}
                        </div>
                        <div className="text-sm text-gray-300">
                          {violation.description}
                        </div>
                      </div>
                      {getSeverityBadge(violation.severity)}
                    </div>
                    
                    <div className="mt-3 p-3 bg-orange-500/10 border border-orange-500/20 rounded">
                      <div className="text-sm">
                        <span className="text-orange-300 font-medium">⚠️ Impact:</span>
                        <div className="text-orange-200 mt-1">{violation.impact}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-green-400 font-medium">No architectural violations detected</div>
                <div className="text-sm text-green-300 mt-1">
                  Code follows architectural best practices
                </div>
              </div>
            )}
          </div>

          {/* Security & Performance Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
              <h4 className="font-semibold text-green-400 mb-2 flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security Assessment
              </h4>
              <div className="text-sm text-green-300">
                No major security vulnerabilities identified in the architectural design.
                Components follow security best practices.
              </div>
            </div>
            
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <h4 className="font-semibold text-blue-400 mb-2 flex items-center gap-2">
                <Gauge className="h-4 w-4" />
                Performance Analysis
              </h4>
              <div className="text-sm text-blue-300">
                Architecture supports scalable performance patterns.
                No obvious performance bottlenecks in design.
              </div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="p-4 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 border border-cyan-500/30 rounded-lg">
            <h4 className="font-semibold text-cyan-400 mb-3 flex items-center gap-2">
              <Zap className="h-4 w-4" />
              🏗️ Architectural Recommendations
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="font-medium text-cyan-300">Best Practices:</div>
                <ul className="space-y-1 text-gray-300">
                  <li className="flex items-center gap-2">
                    <CheckCircle2 className="h-3 w-3 text-green-400" />
                    Follow SOLID principles consistently
                  </li>
                  <li className="flex items-center gap-2">
                    <Layers className="h-3 w-3 text-blue-400" />
                    Maintain clear separation of concerns
                  </li>
                  <li className="flex items-center gap-2">
                    <Network className="h-3 w-3 text-purple-400" />
                    Document integration patterns
                  </li>
                </ul>
              </div>
              <div className="space-y-2">
                <div className="font-medium text-cyan-300">Future Considerations:</div>
                <ul className="space-y-1 text-gray-300">
                  <li className="flex items-center gap-2">
                    <Gauge className="h-3 w-3 text-green-400" />
                    Monitor performance metrics
                  </li>
                  <li className="flex items-center gap-2">
                    <Shield className="h-3 w-3 text-yellow-400" />
                    Regular security reviews
                  </li>
                  <li className="flex items-center gap-2">
                    <Building2 className="h-3 w-3 text-blue-400" />
                    Plan for scalability
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}