import React from 'react'
import { 
  ArrowRight, 
  CheckCircle2, 
  AlertTriangle,
  Clock,
  Target,
  Zap,
  GitPullRequest,
  Bug,
  Shield,
  Code2
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'

interface NextStepsData {
  priority_assessment: {
    critical_blockers: number
    high_priority: number
    medium_priority: number
    can_merge: boolean
    estimated_effort: string
  }
  immediate_actions: Array<{
    action: string
    category: 'ac_compliance' | 'security' | 'bugs' | 'quality'
    priority: 'critical' | 'high' | 'medium'
    effort: string
    description: string
  }>
  follow_up_tasks: Array<{
    task: string
    category: string
    timeline: string
    assignee_suggestion?: string
    description: string
  }>
  merge_readiness: {
    status: 'ready' | 'blocked' | 'needs_review'
    blockers: string[]
    recommendations: string[]
  }
  post_merge_actions: Array<{
    action: string
    timeline: string
    importance: 'high' | 'medium' | 'low'
    description: string
  }>
}

interface NextStepsSectionProps {
  data: NextStepsData
  prUrl?: string
  jiraTicketId?: string
}

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'ac_compliance':
      return <Target className="h-4 w-4 text-purple-400" />
    case 'security':
      return <Shield className="h-4 w-4 text-red-400" />
    case 'bugs':
      return <Bug className="h-4 w-4 text-orange-400" />
    case 'quality':
      return <Code2 className="h-4 w-4 text-blue-400" />
    default:
      return <ArrowRight className="h-4 w-4 text-gray-400" />
  }
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'critical':
      return 'border-red-500 text-red-400 bg-red-500/10'
    case 'high':
      return 'border-orange-500 text-orange-400 bg-orange-500/10'
    case 'medium':
      return 'border-yellow-500 text-yellow-400 bg-yellow-500/10'
    default:
      return 'border-gray-500 text-gray-400 bg-gray-500/10'
  }
}

const getMergeStatusColor = (status: string) => {
  switch (status) {
    case 'ready':
      return 'border-green-500/50 bg-green-500/10 text-green-400'
    case 'blocked':
      return 'border-red-500/50 bg-red-500/10 text-red-400'
    case 'needs_review':
      return 'border-yellow-500/50 bg-yellow-500/10 text-yellow-400'
    default:
      return 'border-gray-500/50 bg-gray-500/10 text-gray-400'
  }
}

export const NextStepsSection: React.FC<NextStepsSectionProps> = ({ 
  data, 
  prUrl,
  jiraTicketId 
}) => {
  const hasData = data.immediate_actions.length > 0 || data.follow_up_tasks.length > 0

  if (!hasData) {
    return (
      <Card className="border-green-500/30 bg-green-500/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-400">
            <CheckCircle2 className="h-5 w-5" />
            🚀 Next Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle2 className="h-16 w-16 mx-auto mb-4 text-green-400" />
            <h3 className="text-lg font-semibold text-green-400 mb-2">Great Work!</h3>
            <p className="text-gray-300 mb-4">No critical issues found. This PR appears ready for merge.</p>
            <div className="flex justify-center gap-3">
              {prUrl && (
                <Button asChild variant="outline" size="sm">
                  <a href={prUrl} target="_blank" rel="noopener noreferrer">
                    <GitPullRequest className="h-4 w-4 mr-2" />
                    View PR
                  </a>
                </Button>
              )}
              {jiraTicketId && (
                <Button asChild variant="outline" size="sm">
                  <a href={`https://rmamedia.atlassian.net/browse/${jiraTicketId}`} target="_blank" rel="noopener noreferrer">
                    <Target className="h-4 w-4 mr-2" />
                    View Ticket
                  </a>
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-blue-500/30 bg-blue-500/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-400">
          <Zap className="h-5 w-5" />
          🚀 Next Steps & Action Plan
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Priority Assessment */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Merge Readiness */}
          <div className={`p-4 border rounded-lg ${getMergeStatusColor(data.merge_readiness.status)}`}>
            <div className="flex items-center gap-2 mb-3">
              <GitPullRequest className="h-5 w-5" />
              <h4 className="font-semibold">Merge Readiness</h4>
            </div>
            
            <div className="flex items-center gap-2 mb-3">
              {data.merge_readiness.status === 'ready' ? (
                <CheckCircle2 className="h-5 w-5 text-green-400" />
              ) : data.merge_readiness.status === 'blocked' ? (
                <AlertTriangle className="h-5 w-5 text-red-400" />
              ) : (
                <Clock className="h-5 w-5 text-yellow-400" />
              )}
              <Badge className={getPriorityColor(data.merge_readiness.status)}>
                {data.merge_readiness.status.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>

            {data.merge_readiness.blockers.length > 0 && (
              <div className="mb-3">
                <div className="text-sm font-medium mb-1">Blockers:</div>
                <ul className="text-sm space-y-1">
                  {data.merge_readiness.blockers.map((blocker, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-red-400 mt-1">•</span>
                      <span>{blocker}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {data.merge_readiness.recommendations.length > 0 && (
              <div>
                <div className="text-sm font-medium mb-1">Recommendations:</div>
                <ul className="text-sm space-y-1">
                  {data.merge_readiness.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-green-400 mt-1">•</span>
                      <span>{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Priority Overview */}
          <div className="p-4 bg-gray-900/50 border border-gray-600 rounded-lg">
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-400" />
              Priority Overview
            </h4>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-300">Critical Blockers</span>
                <Badge className={getPriorityColor('critical')}>
                  {data.priority_assessment.critical_blockers}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-300">High Priority</span>
                <Badge className={getPriorityColor('high')}>
                  {data.priority_assessment.high_priority}
                </Badge>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-300">Medium Priority</span>
                <Badge className={getPriorityColor('medium')}>
                  {data.priority_assessment.medium_priority}
                </Badge>
              </div>
              
              <div className="pt-2 border-t border-gray-700">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-300">Estimated Effort</span>
                  <span className="text-sm font-semibold text-blue-400">
                    {data.priority_assessment.estimated_effort}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Immediate Actions */}
        {data.immediate_actions.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-red-400 mb-4 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              ⚡ Immediate Actions Required
            </h4>
            
            <div className="space-y-3">
              {data.immediate_actions.map((action, index) => (
                <div key={index} className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(action.category)}
                      <span className="font-semibold text-white">{action.action}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(action.priority)}>
                        {action.priority}
                      </Badge>
                      <Badge variant="outline" className="border-gray-500 text-gray-400">
                        {action.effort}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-300">{action.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Follow-up Tasks */}
        {data.follow_up_tasks.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-blue-400 mb-4 flex items-center gap-2">
              <Clock className="h-5 w-5" />
              📋 Follow-up Tasks
            </h4>
            
            <div className="space-y-3">
              {data.follow_up_tasks.map((task, index) => (
                <div key={index} className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="font-semibold text-white mb-1">{task.task}</div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="outline" className="border-blue-500 text-blue-400">
                          {task.category}
                        </Badge>
                        <Badge variant="outline" className="border-gray-500 text-gray-400">
                          {task.timeline}
                        </Badge>
                        {task.assignee_suggestion && (
                          <Badge variant="outline" className="border-purple-500 text-purple-400">
                            → {task.assignee_suggestion}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-gray-300">{task.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Post-Merge Actions */}
        {data.post_merge_actions.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-green-400 mb-4 flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5" />
              ✅ Post-Merge Actions
            </h4>
            
            <div className="space-y-3">
              {data.post_merge_actions.map((action, index) => (
                <div key={index} className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="font-semibold text-white">{action.action}</div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={
                        action.importance === 'high' ? 'border-orange-500 text-orange-400' :
                        action.importance === 'medium' ? 'border-yellow-500 text-yellow-400' :
                        'border-gray-500 text-gray-400'
                      }>
                        {action.importance}
                      </Badge>
                      <Badge variant="outline" className="border-gray-500 text-gray-400">
                        {action.timeline}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-300">{action.description}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-4 border-t border-gray-700">
          <div className="flex flex-wrap gap-3 justify-center">
            {prUrl && (
              <Button asChild variant="outline" size="sm">
                <a href={prUrl} target="_blank" rel="noopener noreferrer">
                  <GitPullRequest className="h-4 w-4 mr-2" />
                  View Pull Request
                </a>
              </Button>
            )}
            {jiraTicketId && (
              <Button asChild variant="outline" size="sm">
                <a href={`https://rmamedia.atlassian.net/browse/${jiraTicketId}`} target="_blank" rel="noopener noreferrer">
                  <Target className="h-4 w-4 mr-2" />
                  Update Jira Ticket
                </a>
              </Button>
            )}
            <Button variant="outline" size="sm" onClick={() => window.print()}>
              📄 Print Report
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}