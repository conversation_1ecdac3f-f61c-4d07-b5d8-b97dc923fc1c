import React, { useState } from 'react'
import { 
  CheckSquare, 
  AlertTriangle, 
  Lightbulb, 
  Clock,
  ChevronDown,
  ChevronRight,
  CheckCircle2,
  Circle,
  Star,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'

interface ActionItem {
  text: string
  priority: string
  category: string
}

interface ActionItemsData {
  critical: ActionItem[]
  important: ActionItem[]
  suggestions: ActionItem[]
}

interface ActionItemsSectionProps {
  data: ActionItemsData
  expanded: boolean
  onToggle: () => void
}

const getPriorityIcon = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'critical':
      return <AlertTriangle className="h-4 w-4 text-red-400" />
    case 'important':
      return <Star className="h-4 w-4 text-yellow-400" />
    case 'suggestion':
      return <Lightbulb className="h-4 w-4 text-blue-400" />
    default:
      return <Circle className="h-4 w-4 text-gray-400" />
  }
}

const getPriorityBadge = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'critical':
      return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">🚨 CRITICAL</Badge>
    case 'important':
      return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">⚠️ IMPORTANT</Badge>
    case 'suggestion':
      return <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">💡 SUGGESTION</Badge>
    default:
      return <Badge variant="outline" className="border-gray-500 text-gray-400">UNKNOWN</Badge>
  }
}

const getCategoryDescription = (category: string) => {
  switch (category.toLowerCase()) {
    case 'must_fix':
      return 'Must be fixed before merging to prevent issues'
    case 'should_fix':
      return 'Should be addressed to improve code quality'
    case 'nice_to_have':
      return 'Optional improvements for better maintainability'
    default:
      return 'General improvement recommendation'
  }
}

const ActionItemCard: React.FC<{
  item: ActionItem
  index: number
  onToggleComplete?: (index: number) => void
  isCompleted?: boolean
}> = ({ item, index, onToggleComplete, isCompleted = false }) => {
  const handleToggle = () => {
    if (onToggleComplete) {
      onToggleComplete(index)
    }
  }

  return (
    <div className={`p-4 border rounded-lg transition-all duration-200 ${
      isCompleted 
        ? 'bg-green-500/10 border-green-500/20 opacity-75' 
        : item.priority.toLowerCase() === 'critical' 
          ? 'bg-red-500/10 border-red-500/20 hover:bg-red-500/15' 
          : item.priority.toLowerCase() === 'important'
            ? 'bg-yellow-500/10 border-yellow-500/20 hover:bg-yellow-500/15'
            : 'bg-blue-500/10 border-blue-500/20 hover:bg-blue-500/15'
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <button
            onClick={handleToggle}
            className="flex-shrink-0 mt-1 hover:scale-110 transition-transform"
          >
            {isCompleted ? (
              <CheckCircle2 className="h-5 w-5 text-green-400" />
            ) : (
              <Circle className="h-5 w-5 text-gray-400 hover:text-white" />
            )}
          </button>
          <div className="space-y-2 flex-1">
            <div className={`font-medium ${isCompleted ? 'line-through text-gray-400' : 'text-white'}`}>
              {item.text}
            </div>
            <div className="text-sm text-muted-foreground">
              {getCategoryDescription(item.category)}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          {getPriorityIcon(item.priority)}
          {getPriorityBadge(item.priority)}
        </div>
      </div>
    </div>
  )
}

const ActionCategory: React.FC<{
  title: string
  icon: React.ReactNode
  items: ActionItem[]
  color: string
  emptyMessage: string
  onToggleComplete?: (categoryIndex: number, itemIndex: number) => void
  completedItems?: boolean[]
}> = ({ title, icon, items, color, emptyMessage, onToggleComplete, completedItems = [] }) => {
  return (
    <div className="space-y-3">
      <h4 className={`text-md font-semibold ${color} flex items-center gap-2`}>
        {icon}
        {title} ({items.length})
      </h4>
      
      {items.length > 0 ? (
        <div className="space-y-3">
          {items.map((item, index) => (
            <ActionItemCard
              key={index}
              item={item}
              index={index}
              onToggleComplete={onToggleComplete ? (itemIndex) => onToggleComplete(0, itemIndex) : undefined}
              isCompleted={completedItems[index] || false}
            />
          ))}
        </div>
      ) : (
        <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
          <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
          <div className="text-green-400 font-medium">{emptyMessage}</div>
        </div>
      )}
    </div>
  )
}

export const ActionItemsSection: React.FC<ActionItemsSectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const { critical, important, suggestions } = data
  const [completedItems, setCompletedItems] = useState<{[key: string]: boolean[]}>({
    critical: new Array(critical.length).fill(false),
    important: new Array(important.length).fill(false),
    suggestions: new Array(suggestions.length).fill(false)
  })

  const totalItems = critical.length + important.length + suggestions.length
  const totalCompleted = Object.values(completedItems).flat().filter(Boolean).length
  const completionRate = totalItems > 0 ? Math.round((totalCompleted / totalItems) * 100) : 0

  const handleToggleComplete = (category: string, itemIndex: number) => {
    setCompletedItems(prev => ({
      ...prev,
      [category]: prev[category].map((completed, index) => 
        index === itemIndex ? !completed : completed
      )
    }))
  }

  const markAllAsComplete = () => {
    setCompletedItems({
      critical: new Array(critical.length).fill(true),
      important: new Array(important.length).fill(true),
      suggestions: new Array(suggestions.length).fill(true)
    })
  }

  const resetProgress = () => {
    setCompletedItems({
      critical: new Array(critical.length).fill(false),
      important: new Array(important.length).fill(false),
      suggestions: new Array(suggestions.length).fill(false)
    })
  }

  return (
    <Card className="border-purple-500/30 bg-purple-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-purple-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CheckSquare className="h-5 w-5 text-purple-400" />
            <span className="text-purple-400">🎯 COMBINED ACTION ITEMS</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Total:</span>
                <span className="font-bold text-purple-400">{totalItems}</span>
              </div>
              {totalItems > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-muted-foreground">Progress:</span>
                  <span className="font-bold text-green-400">{completionRate}%</span>
                </div>
              )}
            </div>
            {expanded ? (
              <ChevronDown className="h-5 w-5 text-purple-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-purple-400" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent className="space-y-6">
          {/* Progress Overview */}
          {totalItems > 0 && (
            <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-purple-400">Progress Tracking</h4>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetProgress}
                    className="border-gray-500 text-gray-400 hover:bg-gray-500/10"
                  >
                    Reset
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={markAllAsComplete}
                    className="border-green-500 text-green-400 hover:bg-green-500/10"
                  >
                    Mark All Complete
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Completion Progress</span>
                  <span className="font-semibold">{totalCompleted}/{totalItems} ({completionRate}%)</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-purple-600 to-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${completionRate}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Action Items Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-red-400">{critical.length}</div>
              <div className="text-sm text-muted-foreground">Critical Items</div>
              <div className="text-xs text-red-300 mt-1">Must Fix (Blockers)</div>
            </div>
            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-yellow-400">{important.length}</div>
              <div className="text-sm text-muted-foreground">Important Items</div>
              <div className="text-xs text-yellow-300 mt-1">Should Fix</div>
            </div>
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-400">{suggestions.length}</div>
              <div className="text-sm text-muted-foreground">Suggestions</div>
              <div className="text-xs text-blue-300 mt-1">Nice to Have</div>
            </div>
          </div>

          {/* No Action Items */}
          {totalItems === 0 && (
            <div className="p-6 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
              <CheckCircle2 className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-400 mb-2">🎉 No Action Items Required!</h3>
              <p className="text-green-300">
                The code review didn't identify any critical issues or required improvements.
                The implementation meets all quality standards.
              </p>
            </div>
          )}

          {/* Action Categories */}
          {totalItems > 0 && (
            <div className="space-y-6">
              <ActionCategory
                title="🚨 CRITICAL (Must Fix Before Merge)"
                icon={<AlertTriangle className="h-4 w-4" />}
                items={critical}
                color="text-red-400"
                emptyMessage="No critical issues - ready to merge!"
                onToggleComplete={(_, itemIndex) => handleToggleComplete('critical', itemIndex)}
                completedItems={completedItems.critical}
              />

              <ActionCategory
                title="⚠️ IMPORTANT (Should Fix)"
                icon={<Star className="h-4 w-4" />}
                items={important}
                color="text-yellow-400"
                emptyMessage="No important issues identified"
                onToggleComplete={(_, itemIndex) => handleToggleComplete('important', itemIndex)}
                completedItems={completedItems.important}
              />

              <ActionCategory
                title="💡 SUGGESTIONS (Nice to Have)"
                icon={<Lightbulb className="h-4 w-4" />}
                items={suggestions}
                color="text-blue-400"
                emptyMessage="No additional suggestions"
                onToggleComplete={(_, itemIndex) => handleToggleComplete('suggestions', itemIndex)}
                completedItems={completedItems.suggestions}
              />
            </div>
          )}

          {/* Next Steps Guidance */}
          {totalItems > 0 && (
            <div className="p-4 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/30 rounded-lg">
              <h4 className="font-semibold text-purple-400 mb-3 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                📋 Next Steps Guidance
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="font-medium text-purple-300">Immediate Actions:</div>
                  <ul className="space-y-1 text-gray-300">
                    {critical.length > 0 && (
                      <li className="flex items-center gap-2">
                        <AlertTriangle className="h-3 w-3 text-red-400" />
                        Address all {critical.length} critical item{critical.length > 1 ? 's' : ''}
                      </li>
                    )}
                    <li className="flex items-center gap-2">
                      <CheckCircle2 className="h-3 w-3 text-green-400" />
                      Test all changes thoroughly
                    </li>
                    <li className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-blue-400" />
                      Update documentation as needed
                    </li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <div className="font-medium text-purple-300">Follow-up Process:</div>
                  <ul className="space-y-1 text-gray-300">
                    <li className="flex items-center gap-2">
                      <Star className="h-3 w-3 text-yellow-400" />
                      Plan important improvements for next sprint
                    </li>
                    <li className="flex items-center gap-2">
                      <Lightbulb className="h-3 w-3 text-blue-400" />
                      Consider suggestions for technical debt
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckSquare className="h-3 w-3 text-purple-400" />
                      Request re-review after fixes
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}