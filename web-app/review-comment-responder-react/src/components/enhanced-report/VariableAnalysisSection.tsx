import React from 'react'
import { 
  Variable, 
  ChevronDown, 
  ChevronRight, 
  CheckCircle, 
  AlertTriangle,
  FileText,
  Code2
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

interface VariableAnalysisData {
  executive_summary: {
    total_variables: number
    naming_issues: number
    scope_issues: number
    consistency_score: number
  }
  naming_analysis: Array<{
    concept: string
    file: string
    variable_name: string
    pattern: string
    status: 'consistent' | 'inconsistent' | 'needs_review'
    suggestion?: string
    line_number?: number
  }>
  scope_analysis: Array<{
    issue_type: 'shadowing' | 'unused' | 'scope_pollution'
    file: string
    variable_name: string
    description: string
    recommendation: string
    line_number?: number
  }>
  type_consistency: Array<{
    concept: string
    files: string[]
    patterns: string[]
    consistency_status: 'good' | 'mixed' | 'poor'
    recommendation: string
  }>
}

interface VariableAnalysisSectionProps {
  data: VariableAnalysisData
  expanded: boolean
  onToggle: () => void
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'consistent':
    case 'good':
      return <CheckCircle className="h-4 w-4 text-green-400" />
    case 'inconsistent':
    case 'mixed':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />
    case 'needs_review':
    case 'poor':
      return <AlertTriangle className="h-4 w-4 text-red-400" />
    default:
      return <Variable className="h-4 w-4 text-gray-400" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'consistent':
    case 'good':
      return 'border-green-500/50 text-green-400'
    case 'inconsistent':
    case 'mixed':
      return 'border-yellow-500/50 text-yellow-400'
    case 'needs_review':
    case 'poor':
      return 'border-red-500/50 text-red-400'
    default:
      return 'border-gray-500/50 text-gray-400'
  }
}

export const VariableAnalysisSection: React.FC<VariableAnalysisSectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const hasData = data.naming_analysis.length > 0 || data.scope_analysis.length > 0 || data.type_consistency.length > 0

  return (
    <Card className="border-purple-500/30 bg-purple-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-purple-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between text-purple-400">
          <div className="flex items-center gap-2">
            <Variable className="h-5 w-5" />
            📊 Variable & Parameter Analysis
          </div>
          <div className="flex items-center gap-2">
            {hasData && (
              <Badge variant="outline" className="border-purple-500 text-purple-400">
                {data.executive_summary.total_variables} Variables
              </Badge>
            )}
            {expanded ? (
              <ChevronDown className="h-5 w-5" />
            ) : (
              <ChevronRight className="h-5 w-5" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent>
          {!hasData ? (
            <div className="text-center py-8 text-gray-400">
              <Variable className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No variable analysis data available</p>
              <p className="text-sm mt-2">
                This section will show naming patterns, scope issues, and type consistency analysis.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Executive Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-400">
                    {data.executive_summary.total_variables}
                  </div>
                  <div className="text-sm text-gray-400">Total Variables</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-400">
                    {data.executive_summary.naming_issues}
                  </div>
                  <div className="text-sm text-gray-400">Naming Issues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-400">
                    {data.executive_summary.scope_issues}
                  </div>
                  <div className="text-sm text-gray-400">Scope Issues</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {data.executive_summary.consistency_score}/10
                  </div>
                  <div className="text-sm text-gray-400">Consistency Score</div>
                </div>
              </div>

              {/* Naming Analysis Table */}
              {data.naming_analysis.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-purple-400 mb-4 flex items-center gap-2">
                    <Code2 className="h-5 w-5" />
                    Naming Consistency Analysis
                  </h4>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">Concept</th>
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">File</th>
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">Variable/Param</th>
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">Pattern</th>
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">Status</th>
                          <th className="text-left py-3 px-4 text-sm font-semibold text-gray-300">Suggestion</th>
                        </tr>
                      </thead>
                      <tbody>
                        {data.naming_analysis.map((item, index) => (
                          <tr key={index} className="border-b border-gray-800 hover:bg-gray-900/50">
                            <td className="py-3 px-4 text-sm text-white">{item.concept}</td>
                            <td className="py-3 px-4 text-sm">
                              <div className="flex items-center gap-1">
                                <FileText className="h-3 w-3 text-gray-400" />
                                <code className="text-xs text-blue-400">{item.file}</code>
                                {item.line_number && (
                                  <span className="text-xs text-gray-500">:{item.line_number}</span>
                                )}
                              </div>
                            </td>
                            <td className="py-3 px-4 text-sm">
                              <code className="bg-gray-800 px-2 py-1 rounded text-green-400">
                                {item.variable_name}
                              </code>
                            </td>
                            <td className="py-3 px-4 text-sm text-gray-300">{item.pattern}</td>
                            <td className="py-3 px-4 text-sm">
                              <div className="flex items-center gap-1">
                                {getStatusIcon(item.status)}
                                <Badge variant="outline" className={getStatusColor(item.status)}>
                                  {item.status.replace('_', ' ')}
                                </Badge>
                              </div>
                            </td>
                            <td className="py-3 px-4 text-sm text-gray-400">
                              {item.suggestion || '-'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Scope Issues */}
              {data.scope_analysis.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-red-400 mb-4 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Scope & Usage Issues
                  </h4>
                  
                  <div className="space-y-3">
                    {data.scope_analysis.map((issue, index) => (
                      <div key={index} className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline" className="border-red-500 text-red-400">
                                {issue.issue_type.replace('_', ' ')}
                              </Badge>
                              <code className="text-sm bg-gray-800 px-2 py-1 rounded text-yellow-400">
                                {issue.variable_name}
                              </code>
                            </div>
                            <div className="flex items-center gap-1 mb-2">
                              <FileText className="h-3 w-3 text-gray-400" />
                              <code className="text-xs text-blue-400">{issue.file}</code>
                              {issue.line_number && (
                                <span className="text-xs text-gray-500">:{issue.line_number}</span>
                              )}
                            </div>
                            <p className="text-sm text-gray-300 mb-2">{issue.description}</p>
                            <div className="text-sm text-green-400">
                              <strong>Recommendation:</strong> {issue.recommendation}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Type Consistency */}
              {data.type_consistency.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-blue-400 mb-4 flex items-center gap-2">
                    <Variable className="h-5 w-5" />
                    Type Consistency Analysis
                  </h4>
                  
                  <div className="space-y-3">
                    {data.type_consistency.map((item, index) => (
                      <div key={index} className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <div className="font-semibold text-white">{item.concept}</div>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(item.consistency_status)}
                            <Badge variant="outline" className={getStatusColor(item.consistency_status)}>
                              {item.consistency_status}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                          <div>
                            <div className="text-sm text-gray-400 mb-1">Files:</div>
                            <div className="space-y-1">
                              {item.files.map((file, i) => (
                                <div key={i} className="flex items-center gap-1">
                                  <FileText className="h-3 w-3 text-gray-400" />
                                  <code className="text-xs text-blue-400">{file}</code>
                                </div>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <div className="text-sm text-gray-400 mb-1">Patterns:</div>
                            <div className="space-y-1">
                              {item.patterns.map((pattern, i) => (
                                <code key={i} className="block text-xs bg-gray-800 px-2 py-1 rounded text-green-400">
                                  {pattern}
                                </code>
                              ))}
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-sm text-yellow-400">
                          <strong>Recommendation:</strong> {item.recommendation}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}