import React from 'react'
import { 
  Target, 
  CheckCircle2, 
  AlertTriangle, 
  XCircle, 
  Clock,
  ChevronDown,
  ChevronRight,
  TrendingUp,
  BarChart3
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'

interface ACAnalysisData {
  executive_summary: {
    total_ac: number
    fulfilled: number
    partially_fulfilled: number
    not_fulfilled: number
    compliance_rate: number
    business_alignment_score: number
  }
  detailed_results: Array<{
    id: string
    text: string
    status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending'
    implementation_evidence: string
    issues: string[]
  }>
}

interface ACAnalysisSectionProps {
  data: ACAnalysisData
  expanded: boolean
  onToggle: () => void
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'fulfilled':
      return <CheckCircle2 className="h-4 w-4 text-green-400" />
    case 'partially_fulfilled':
      return <AlertTriangle className="h-4 w-4 text-yellow-400" />
    case 'not_fulfilled':
      return <XCircle className="h-4 w-4 text-red-400" />
    default:
      return <Clock className="h-4 w-4 text-gray-400" />
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'fulfilled':
      return <Badge className="bg-green-500/20 text-green-400 border-green-500/30">✅ ERFÜLLT</Badge>
    case 'partially_fulfilled':
      return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">⚠️ TEILWEISE</Badge>
    case 'not_fulfilled':
      return <Badge className="bg-red-500/20 text-red-400 border-red-500/30">❌ NICHT ERFÜLLT</Badge>
    default:
      return <Badge variant="outline" className="border-gray-500 text-gray-400">🕒 PENDING</Badge>
  }
}

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-400'
  if (score >= 6) return 'text-yellow-400'
  return 'text-red-400'
}

export const ACAnalysisSection: React.FC<ACAnalysisSectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const { executive_summary, detailed_results } = data

  return (
    <Card className="border-blue-500/30 bg-blue-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-blue-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Target className="h-5 w-5 text-blue-400" />
            <span className="text-blue-400">PHASE 1: 📋 ACCEPTANCE CRITERIA ANALYSIS</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Compliance:</span>
              <span className={`font-bold ${getScoreColor(executive_summary.business_alignment_score)}`}>
                {executive_summary.compliance_rate}%
              </span>
            </div>
            {expanded ? (
              <ChevronDown className="h-5 w-5 text-blue-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-blue-400" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent className="space-y-6">
          {/* Executive Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-400 flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              AC Executive Summary
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Overall Compliance */}
              <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className="text-2xl font-bold text-blue-400">
                    {executive_summary.compliance_rate}%
                  </div>
                  <div className="text-sm text-muted-foreground">AC Compliance Rate</div>
                  <Progress 
                    value={executive_summary.compliance_rate} 
                    className="h-2 bg-blue-900/50" 
                  />
                </div>
              </div>

              {/* Business Alignment Score */}
              <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className={`text-2xl font-bold ${getScoreColor(executive_summary.business_alignment_score)}`}>
                    {executive_summary.business_alignment_score}/10
                  </div>
                  <div className="text-sm text-muted-foreground">Business Alignment Score</div>
                  <Progress 
                    value={executive_summary.business_alignment_score * 10} 
                    className="h-2 bg-purple-900/50" 
                  />
                </div>
              </div>

              {/* AC Breakdown */}
              <div className="p-4 bg-gray-800/50 border border-gray-700 rounded-lg">
                <div className="space-y-2">
                  <div className="text-sm font-medium text-gray-300 mb-3">AC Breakdown</div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-green-400">✅ Fulfilled:</span>
                      <span className="text-green-400 font-semibold">{executive_summary.fulfilled}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-yellow-400">⚠️ Partially:</span>
                      <span className="text-yellow-400 font-semibold">{executive_summary.partially_fulfilled}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-red-400">❌ Not Fulfilled:</span>
                      <span className="text-red-400 font-semibold">{executive_summary.not_fulfilled}</span>
                    </div>
                    <div className="border-t border-gray-600 pt-1 mt-2">
                      <div className="flex justify-between text-xs font-semibold">
                        <span className="text-gray-300">Total AC:</span>
                        <span className="text-gray-300">{executive_summary.total_ac}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Detailed AC Results */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-blue-400 flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Detailed AC Results
            </h3>
            
            <div className="space-y-3">
              {detailed_results.map((ac, index) => (
                <div 
                  key={ac.id}
                  className="p-4 bg-gray-800/30 border border-gray-700 rounded-lg hover:bg-gray-800/50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getStatusIcon(ac.status)}
                      </div>
                      <div className="space-y-1">
                        <div className="font-medium text-white">
                          <span className="text-blue-400 font-semibold">AC {index + 1}:</span> {ac.text}
                        </div>
                      </div>
                    </div>
                    <div className="flex-shrink-0">
                      {getStatusBadge(ac.status)}
                    </div>
                  </div>

                  {/* Implementation Evidence */}
                  {ac.implementation_evidence && (
                    <div className="mt-3 p-3 bg-gray-900/50 border border-gray-600 rounded">
                      <div className="text-sm font-medium text-gray-300 mb-1">
                        Code-Implementierung:
                      </div>
                      <div className="text-sm text-gray-400 font-mono">
                        {ac.implementation_evidence}
                      </div>
                    </div>
                  )}

                  {/* Issues */}
                  {ac.issues.length > 0 && (
                    <div className="mt-3 space-y-2">
                      <div className="text-sm font-medium text-red-400">Probleme:</div>
                      <div className="space-y-1">
                        {ac.issues.map((issue, issueIndex) => (
                          <div key={issueIndex} className="text-sm text-red-300 pl-4 border-l-2 border-red-500/50">
                            {issue}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Business Impact Summary */}
          <div className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-lg">
            <h4 className="font-semibold text-blue-400 mb-2">💼 Business Impact Assessment</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">User Story Fulfillment:</span>
                <div className={`font-semibold ${getScoreColor(executive_summary.business_alignment_score)}`}>
                  {executive_summary.compliance_rate >= 80 ? 'Excellent' : 
                   executive_summary.compliance_rate >= 60 ? 'Good' : 
                   executive_summary.compliance_rate >= 40 ? 'Needs Improvement' : 'Critical Issues'}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">Business Risk Level:</span>
                <div className={`font-semibold ${
                  executive_summary.compliance_rate >= 80 ? 'text-green-400' : 
                  executive_summary.compliance_rate >= 60 ? 'text-yellow-400' : 'text-red-400'
                }`}>
                  {executive_summary.compliance_rate >= 80 ? 'Low Risk' : 
                   executive_summary.compliance_rate >= 60 ? 'Medium Risk' : 'High Risk'}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}