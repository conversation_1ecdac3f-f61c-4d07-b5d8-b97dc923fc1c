import React from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Layers, 
  Type,
  ChevronDown,
  ChevronRight,
  BarChart3,
  AlertTriangle,
  CheckCircle2,
  TrendingDown,
  Code2
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'

interface CodeQualityData {
  executive_summary: {
    overall_score: number
    critical_issues: number
    code_smells: number
    duplication_level: number
  }
  code_duplication: any[]
  complexity_issues: any[]
  naming_consistency: any[]
}

interface CodeQualitySectionProps {
  data: CodeQualityData
  expanded: boolean
  onToggle: () => void
}

const getScoreColor = (score: number) => {
  if (score >= 8) return 'text-green-400'
  if (score >= 6) return 'text-yellow-400'
  return 'text-red-400'
}

const getScoreLabel = (score: number) => {
  if (score >= 9) return 'Excellent'
  if (score >= 8) return 'Very Good'
  if (score >= 6) return 'Good'
  if (score >= 4) return 'Needs Improvement'
  return 'Poor'
}

const getDuplicationLevel = (level: number) => {
  if (level <= 5) return { color: 'text-green-400', label: 'Low' }
  if (level <= 15) return { color: 'text-yellow-400', label: 'Medium' }
  return { color: 'text-red-400', label: 'High' }
}

export const CodeQualitySection: React.FC<CodeQualitySectionProps> = ({ 
  data, 
  expanded, 
  onToggle 
}) => {
  const { executive_summary, code_duplication, complexity_issues, naming_consistency } = data
  const duplicationInfo = getDuplicationLevel(executive_summary.duplication_level)

  return (
    <Card className="border-orange-500/30 bg-orange-500/5">
      <CardHeader 
        className="cursor-pointer hover:bg-orange-500/10 transition-colors"
        onClick={onToggle}
      >
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Gauge className="h-5 w-5 text-orange-400" />
            <span className="text-orange-400">PHASE 2: 🔍 CODE QUALITY & STRUCTURE ANALYSIS</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Score:</span>
              <span className={`font-bold ${getScoreColor(executive_summary.overall_score)}`}>
                {executive_summary.overall_score}/10
              </span>
            </div>
            {expanded ? (
              <ChevronDown className="h-5 w-5 text-orange-400" />
            ) : (
              <ChevronRight className="h-5 w-5 text-orange-400" />
            )}
          </div>
        </CardTitle>
      </CardHeader>

      {expanded && (
        <CardContent className="space-y-6">
          {/* Executive Summary */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-orange-400 flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Code Quality Executive Summary
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Overall Score */}
              <div className="p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className={`text-2xl font-bold ${getScoreColor(executive_summary.overall_score)}`}>
                    {executive_summary.overall_score}/10
                  </div>
                  <div className="text-sm text-muted-foreground">Overall Code Quality</div>
                  <div className={`text-xs font-medium ${getScoreColor(executive_summary.overall_score)}`}>
                    {getScoreLabel(executive_summary.overall_score)}
                  </div>
                  <Progress 
                    value={executive_summary.overall_score * 10} 
                    className="h-2 bg-orange-900/50" 
                  />
                </div>
              </div>

              {/* Critical Issues */}
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className="text-2xl font-bold text-red-400">
                    {executive_summary.critical_issues}
                  </div>
                  <div className="text-sm text-muted-foreground">Critical Issues</div>
                  <div className="text-xs">
                    {executive_summary.critical_issues === 0 ? (
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                        ✅ None Found
                      </Badge>
                    ) : (
                      <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                        ⚠️ Action Required
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Code Smells */}
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className="text-2xl font-bold text-yellow-400">
                    {executive_summary.code_smells}
                  </div>
                  <div className="text-sm text-muted-foreground">Code Smells</div>
                  <div className="text-xs">
                    {executive_summary.code_smells <= 5 ? (
                      <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                        ✅ Good
                      </Badge>
                    ) : executive_summary.code_smells <= 15 ? (
                      <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                        ⚠️ Moderate
                      </Badge>
                    ) : (
                      <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                        ❌ High
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Code Duplication */}
              <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <div className="text-center space-y-2">
                  <div className={`text-2xl font-bold ${duplicationInfo.color}`}>
                    {executive_summary.duplication_level}%
                  </div>
                  <div className="text-sm text-muted-foreground">Code Duplication</div>
                  <div className={`text-xs font-medium ${duplicationInfo.color}`}>
                    {duplicationInfo.label} Level
                  </div>
                  <Progress 
                    value={Math.min(executive_summary.duplication_level, 50) * 2} 
                    className="h-2 bg-purple-900/50" 
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Detailed Analysis Sections */}
          <div className="space-y-6">
            {/* Code Duplication Analysis */}
            <div className="space-y-3">
              <h4 className="text-md font-semibold text-purple-400 flex items-center gap-2">
                <Copy className="h-4 w-4" />
                Code Duplication Analysis
              </h4>
              
              {code_duplication.length > 0 ? (
                <div className="space-y-3">
                  {code_duplication.slice(0, 3).map((dup, index) => (
                    <div key={index} className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="font-mono text-sm text-purple-300">
                            {dup.files ? `${dup.files.join(', ')}` : 'Multiple files'}
                          </div>
                          <div className="text-sm text-gray-300">
                            {dup.description || `Duplicated block found (${dup.lines || 'unknown'} lines)`}
                          </div>
                        </div>
                        <Badge className="bg-purple-500/20 text-purple-400 border-purple-500/30">
                          {dup.severity || 'Medium'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {code_duplication.length > 3 && (
                    <div className="text-sm text-muted-foreground text-center">
                      ... and {code_duplication.length - 3} more duplicated blocks
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                  <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
                  <div className="text-green-400 font-medium">No significant code duplication detected</div>
                  <div className="text-sm text-green-300 mt-1">Code follows DRY principles well</div>
                </div>
              )}
            </div>

            {/* Complexity Issues */}
            <div className="space-y-3">
              <h4 className="text-md font-semibold text-red-400 flex items-center gap-2">
                <Layers className="h-4 w-4" />
                Complexity Issues
              </h4>
              
              {complexity_issues.length > 0 ? (
                <div className="space-y-3">
                  {complexity_issues.slice(0, 3).map((issue, index) => (
                    <div key={index} className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="font-mono text-sm text-red-300">
                            {issue.function || issue.file || 'Complex code structure'}
                          </div>
                          <div className="text-sm text-gray-300">
                            {issue.description || 'High cyclomatic complexity detected'}
                          </div>
                          {issue.suggestion && (
                            <div className="text-xs text-blue-300 mt-2 p-2 bg-blue-500/10 rounded">
                              💡 Suggestion: {issue.suggestion}
                            </div>
                          )}
                        </div>
                        <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                          {issue.complexity_score || 'High'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {complexity_issues.length > 3 && (
                    <div className="text-sm text-muted-foreground text-center">
                      ... and {complexity_issues.length - 3} more complexity issues
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                  <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
                  <div className="text-green-400 font-medium">No excessive complexity found</div>
                  <div className="text-sm text-green-300 mt-1">Functions and methods are well-structured</div>
                </div>
              )}
            </div>

            {/* Naming & Consistency */}
            <div className="space-y-3">
              <h4 className="text-md font-semibold text-blue-400 flex items-center gap-2">
                <Type className="h-4 w-4" />
                Naming & Consistency Analysis
              </h4>
              
              {naming_consistency.length > 0 ? (
                <div className="space-y-3">
                  {naming_consistency.slice(0, 3).map((issue, index) => (
                    <div key={index} className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="font-mono text-sm text-blue-300">
                            {issue.variable || issue.function || 'Naming inconsistency'}
                          </div>
                          <div className="text-sm text-gray-300">
                            {issue.description || 'Inconsistent naming pattern detected'}
                          </div>
                          {issue.suggestion && (
                            <div className="text-xs text-green-300 mt-2 p-2 bg-green-500/10 rounded">
                              ✨ Suggestion: {issue.suggestion}
                            </div>
                          )}
                        </div>
                        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                          {issue.severity || 'Low'}
                        </Badge>
                      </div>
                    </div>
                  ))}
                  {naming_consistency.length > 3 && (
                    <div className="text-sm text-muted-foreground text-center">
                      ... and {naming_consistency.length - 3} more naming issues
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                  <CheckCircle2 className="h-8 w-8 text-green-400 mx-auto mb-2" />
                  <div className="text-green-400 font-medium">Consistent naming conventions</div>
                  <div className="text-sm text-green-300 mt-1">Variables and functions follow proper patterns</div>
                </div>
              )}
            </div>
          </div>

          {/* Quality Recommendations */}
          <div className="p-4 bg-gradient-to-r from-orange-500/10 to-yellow-500/10 border border-orange-500/30 rounded-lg">
            <h4 className="font-semibold text-orange-400 mb-3 flex items-center gap-2">
              <Code2 className="h-4 w-4" />
              📈 Quality Improvement Recommendations
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="font-medium text-orange-300">Short-term Actions:</div>
                <ul className="space-y-1 text-gray-300">
                  {executive_summary.critical_issues > 0 && (
                    <li className="flex items-center gap-2">
                      <AlertTriangle className="h-3 w-3 text-red-400" />
                      Address {executive_summary.critical_issues} critical issues
                    </li>
                  )}
                  {executive_summary.duplication_level > 15 && (
                    <li className="flex items-center gap-2">
                      <Copy className="h-3 w-3 text-purple-400" />
                      Refactor duplicated code blocks
                    </li>
                  )}
                  <li className="flex items-center gap-2">
                    <CheckCircle2 className="h-3 w-3 text-green-400" />
                    Run automated code quality tools
                  </li>
                </ul>
              </div>
              <div className="space-y-2">
                <div className="font-medium text-orange-300">Long-term Strategy:</div>
                <ul className="space-y-1 text-gray-300">
                  <li className="flex items-center gap-2">
                    <TrendingDown className="h-3 w-3 text-blue-400" />
                    Implement complexity monitoring
                  </li>
                  <li className="flex items-center gap-2">
                    <Gauge className="h-3 w-3 text-green-400" />
                    Set up quality gates in CI/CD
                  </li>
                  <li className="flex items-center gap-2">
                    <Type className="h-3 w-3 text-purple-400" />
                    Establish coding standards
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}