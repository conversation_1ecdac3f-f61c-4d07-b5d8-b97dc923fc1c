import React from 'react'
import { Eye } from 'lucide-react'
import { Card, CardHeader, CardTitle } from './ui/card'

export const CodeReviewerHeader: React.FC = () => {
  return (
    <Card className="mb-8 overflow-hidden relative">
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5"></div>
      <CardHeader className="relative">
        <CardTitle className="flex items-center gap-4">
          <div className="p-3 rounded-lg bg-primary shadow-lg shadow-primary/30">
            <Eye className="h-10 w-10 text-white" />
          </div>
          <div className="flex flex-col">
            <h1 className="text-4xl text-white font-black uppercase tracking-tighter" style={{ letterSpacing: '-0.05em', fontWeight: 900 }}>
              Code
              <span className="text-primary ml-3">Reviewer</span>
            </h1>
            <span className="text-base font-normal text-muted-foreground mt-1">
              AI-Powered Pull Request Review with Acceptance Criteria Compliance
            </span>
          </div>
        </CardTitle>
      </CardHeader>
    </Card>
  )
}