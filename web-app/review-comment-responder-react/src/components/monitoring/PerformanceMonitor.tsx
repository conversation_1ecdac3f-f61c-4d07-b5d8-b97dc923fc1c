import React, { useState, useEffect } from 'react'
import { 
  Activity, 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  Trash2,
  Bar<PERSON>hart3
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { cacheManager } from '../../services/cache/CacheManager'
import { errorHandler } from '../../services/errors/ErrorHandler'

interface PerformanceMonitorProps {
  className?: string
  autoRefresh?: boolean
  refreshInterval?: number
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className,
  autoRefresh = true,
  refreshInterval = 5000
}) => {
  const [cacheStats, setCacheStats] = useState(cacheManager.getStats())
  const [errorMetrics, setErrorMetrics] = useState(errorHandler.getMetrics())
  const [performanceData, setPerformanceData] = useState<{
    memoryUsage?: number
    loadTime?: number
    apiResponseTime?: number
  }>({})

  // Refresh data
  const refreshData = () => {
    setCacheStats(cacheManager.getStats())
    setErrorMetrics(errorHandler.getMetrics())
    
    // Get performance data
    if ('memory' in performance) {
      const memory = (performance as any).memory
      setPerformanceData(prev => ({
        ...prev,
        memoryUsage: memory.usedJSHeapSize / (1024 * 1024) // MB
      }))
    }
  }

  // Auto-refresh effect
  useEffect(() => {
    refreshData()
    
    if (autoRefresh) {
      const interval = setInterval(refreshData, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // Clear cache
  const handleClearCache = () => {
    if (confirm('Are you sure you want to clear all cache data?')) {
      cacheManager.clear()
      refreshData()
    }
  }

  // Clear error history
  const handleClearErrors = () => {
    if (confirm('Are you sure you want to clear error history?')) {
      errorHandler.clearHistory()
      refreshData()
    }
  }

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`
  const formatMemory = (mb: number) => `${mb.toFixed(1)} MB`
  const formatNumber = (num: number) => num.toLocaleString()

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-primary" />
              Performance Monitor
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={refreshData}
                className="flex items-center gap-2"
              >
                <TrendingUp className="h-4 w-4" />
                Refresh
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearCache}
                className="flex items-center gap-2 text-orange-600"
              >
                <Database className="h-4 w-4" />
                Clear Cache
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearErrors}
                className="flex items-center gap-2 text-red-600"
              >
                <Trash2 className="h-4 w-4" />
                Clear Errors
              </Button>
            </div>
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Cache Statistics */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center gap-2">
              <Database className="h-4 w-4 text-blue-500" />
              Cache Performance
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">
                  {formatNumber(cacheStats.totalEntries)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Cached Items
                </p>
              </div>
              
              <div className="text-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {formatPercentage(cacheStats.hitRate)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Hit Rate
                </p>
              </div>
              
              <div className="text-center p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <p className="text-2xl font-bold text-purple-600">
                  {formatMemory(cacheStats.memoryUsage)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Memory Usage
                </p>
              </div>
              
              <div className="text-center p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                <p className="text-2xl font-bold text-orange-600">
                  {formatNumber(cacheStats.totalRequests)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Total Requests
                </p>
              </div>
            </div>

            {/* Cache Health Status */}
            <div className="mt-4 flex items-center gap-2">
              {cacheStats.hitRate > 70 ? (
                <Badge variant="default" className="bg-green-500">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Excellent Cache Performance
                </Badge>
              ) : cacheStats.hitRate > 50 ? (
                <Badge variant="secondary">
                  <Clock className="h-3 w-3 mr-1" />
                  Good Cache Performance
                </Badge>
              ) : (
                <Badge variant="destructive">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Poor Cache Performance
                </Badge>
              )}
              
              {cacheStats.memoryUsage > 40 && (
                <Badge variant="outline" className="text-orange-600 border-orange-600">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  High Memory Usage
                </Badge>
              )}
            </div>
          </div>

          {/* Error Statistics */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              Error Tracking
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <p className="text-2xl font-bold text-red-600">
                  {formatNumber(errorMetrics.totalErrors)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Total Errors
                </p>
              </div>
              
              <div className="text-center p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">
                  {formatNumber(errorMetrics.errorsBySeverity.high + errorMetrics.errorsBySeverity.critical)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Critical/High
                </p>
              </div>
              
              <div className="text-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <p className="text-2xl font-bold text-blue-600">
                  {formatNumber(errorMetrics.retryableErrors)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Retryable
                </p>
              </div>
              
              <div className="text-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {formatNumber(errorMetrics.resolvedErrors)}
                </p>
                <p className="text-xs text-muted-foreground uppercase tracking-wide">
                  Resolved
                </p>
              </div>
            </div>

            {/* Error Health Status */}
            <div className="mt-4 flex items-center gap-2">
              {errorMetrics.totalErrors === 0 ? (
                <Badge variant="default" className="bg-green-500">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  No Errors
                </Badge>
              ) : errorMetrics.errorsBySeverity.critical > 0 ? (
                <Badge variant="destructive">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  Critical Errors Present
                </Badge>
              ) : errorMetrics.errorsBySeverity.high > 0 ? (
                <Badge variant="secondary" className="text-orange-600 border-orange-600">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  High Severity Errors
                </Badge>
              ) : (
                <Badge variant="outline">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Low Severity Only
                </Badge>
              )}
            </div>
          </div>

          {/* Error Breakdown */}
          {errorMetrics.totalErrors > 0 && (
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">
                Error Categories
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                {Object.entries(errorMetrics.errorsByCategory).map(([category, count]) => {
                  if (count === 0) return null
                  return (
                    <div key={category} className="text-center p-2 bg-muted/30 rounded">
                      <p className="text-sm font-medium">{count}</p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {category}
                      </p>
                    </div>
                  )
                })}
              </div>
            </div>
          )}

          {/* System Performance */}
          {performanceData.memoryUsage && (
            <div>
              <h3 className="text-sm font-medium text-foreground mb-3 flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-purple-500" />
                System Performance
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">
                    {formatMemory(performanceData.memoryUsage)}
                  </p>
                  <p className="text-xs text-muted-foreground uppercase tracking-wide">
                    JS Heap Size
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Last Updated */}
          <div className="text-xs text-muted-foreground text-center pt-4 border-t border-border">
            Last updated: {new Date().toLocaleTimeString()}
            {autoRefresh && (
              <span className="ml-2">
                (Auto-refresh: {refreshInterval / 1000}s)
              </span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}