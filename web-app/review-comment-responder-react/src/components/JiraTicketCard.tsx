import React, { useState } from 'react'
import { 
  Ticket, 
  ChevronDown, 
  ChevronRight, 
  CheckCircle, 
  Circle, 
  Calendar,
  User,
  ExternalLink,
  AlertTriangle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

interface JiraTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  acceptance_criteria?: string[]
  related_prs?: number[]
  url?: string
}

interface JiraTicketCardProps {
  ticket: JiraTicket
  isSelected?: boolean
  isCompact?: boolean
  showAcceptanceCriteria?: boolean
  onSelect?: (ticket: JiraTicket) => void
  onViewTicket?: (ticket: JiraTicket) => void
  className?: string
}

export const JiraTicketCard: React.FC<JiraTicketCardProps> = ({
  ticket,
  isSelected = false,
  isCompact = false,
  showAcceptanceCriteria = true,
  onSelect,
  onViewTicket,
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'to do':
      case 'open':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      case 'in progress':
      case 'in development':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'code review':
      case 'review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'testing':
      case 'qa':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'done':
      case 'closed':
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'highest':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 border-green-200'
      case 'lowest':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 border-gray-200'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'highest':
      case 'high':
        return <AlertTriangle className="h-3 w-3" />
      case 'medium':
        return <Circle className="h-3 w-3" />
      case 'low':
      case 'lowest':
        return <CheckCircle className="h-3 w-3" />
      default:
        return <Circle className="h-3 w-3" />
    }
  }

  const handleCardClick = () => {
    if (onSelect && !isCompact) {
      onSelect(ticket)
    }
  }

  const handleViewTicket = (e: React.MouseEvent) => {
    e.stopPropagation()
    onViewTicket?.(ticket)
  }

  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  const getAcceptanceCriteriaStatus = () => {
    if (!ticket.acceptance_criteria || ticket.acceptance_criteria.length === 0) {
      return { completed: 0, total: ticket.acceptance_criteria_count || 0 }
    }

    const completed = ticket.acceptance_criteria.filter(ac => 
      ac.includes('✅') || 
      ac.includes('[x]') || 
      ac.toLowerCase().includes('done') ||
      ac.toLowerCase().includes('completed')
    ).length

    return { completed, total: ticket.acceptance_criteria.length }
  }

  const acStatus = getAcceptanceCriteriaStatus()
  const acCompletionPercentage = acStatus.total > 0 ? Math.round((acStatus.completed / acStatus.total) * 100) : 0

  return (
    <Card 
      className={`transition-all duration-200 ${
        isSelected 
          ? 'border-primary bg-primary/5 shadow-md' 
          : 'border-border hover:border-primary/50 hover:shadow-sm'
      } ${onSelect && !isCompact ? 'cursor-pointer' : ''} ${className}`}
      onClick={handleCardClick}
    >
      <CardHeader className={`pb-3 ${isCompact ? 'pb-2' : ''}`}>
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <Ticket className="h-4 w-4 text-primary flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <CardTitle className={`text-sm font-medium truncate text-foreground ${isCompact ? 'text-xs' : ''}`}>
                {ticket.ticket_id}
              </CardTitle>
              <p className={`text-muted-foreground mt-1 line-clamp-2 ${isCompact ? 'text-xs' : 'text-sm'}`}>
                {ticket.summary}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge variant="outline" className={`text-xs ${getStatusColor(ticket.status)}`}>
              {ticket.status}
            </Badge>
            
            <Badge 
              variant="outline" 
              className={`text-xs flex items-center gap-1 ${getPriorityColor(ticket.priority)}`}
            >
              {getPriorityIcon(ticket.priority)}
              {ticket.priority}
            </Badge>
            
            {onViewTicket && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewTicket}
                className="h-6 w-6 p-0"
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={`pt-0 ${isCompact ? 'pb-2' : ''}`}>
        {/* Metadata Row */}
        <div className={`flex items-center gap-4 text-muted-foreground ${isCompact ? 'text-xs' : 'text-xs'} mb-3`}>
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>{ticket.assignee}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{formatDate(ticket.updated_date)}</span>
          </div>
          
          {ticket.related_prs && ticket.related_prs.length > 0 && (
            <div className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              <span>{ticket.related_prs.length} PR(s)</span>
            </div>
          )}
        </div>

        {/* Acceptance Criteria Summary */}
        {showAcceptanceCriteria && acStatus.total > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <button
                onClick={toggleExpanded}
                className="flex items-center gap-2 text-sm font-medium text-foreground hover:text-primary transition-colors"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
                Acceptance Criteria
                <Badge variant="secondary" className="text-xs">
                  {acStatus.completed}/{acStatus.total}
                </Badge>
              </button>
              
              <div className="flex items-center gap-2">
                <div className="text-xs text-muted-foreground">
                  {acCompletionPercentage}%
                </div>
                <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all duration-300"
                    style={{ width: `${acCompletionPercentage}%` }}
                  />
                </div>
              </div>
            </div>

            {/* Detailed AC List */}
            {isExpanded && ticket.acceptance_criteria && (
              <div className="space-y-2 pl-6 border-l-2 border-muted">
                {ticket.acceptance_criteria.map((criterion, index) => {
                  const isCompleted = criterion.includes('✅') || 
                                    criterion.includes('[x]') || 
                                    criterion.toLowerCase().includes('done') ||
                                    criterion.toLowerCase().includes('completed')
                  
                  return (
                    <div key={index} className="flex items-start gap-2">
                      {isCompleted ? (
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      ) : (
                        <Circle className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                      )}
                      <span 
                        className={`text-xs leading-relaxed ${
                          isCompleted 
                            ? 'text-muted-foreground line-through' 
                            : 'text-foreground'
                        }`}
                      >
                        {criterion.replace(/^[✅\[x\]\s]*/, '').trim()}
                      </span>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        )}

        {/* Description Preview (only when not compact) */}
        {!isCompact && !isExpanded && ticket.description && (
          <div className="mt-3 pt-3 border-t border-border">
            <p className="text-xs text-muted-foreground line-clamp-2">
              {ticket.description}
            </p>
          </div>
        )}

        {/* Additional Info when expanded */}
        {isExpanded && ticket.description && (
          <div className="mt-4 pt-3 border-t border-border">
            <h5 className="text-xs font-medium text-foreground mb-2">Description</h5>
            <p className="text-xs text-muted-foreground whitespace-pre-wrap">
              {ticket.description}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}