import React, { useState, useEffect } from 'react'
import { 
  GitPullRequest, 
  Ticket, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  User,
  Calendar,
  MessageSquare,
  Eye,
  LogOut,
  UserCheck
} from 'lucide-react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
// import { codeReviewerService } from '../services/codeReviewer/CodeReviewerService' // Not used - using direct Bitbucket API instead
import { jiraAuthService } from '../services/auth/JiraAuthService'
import { bitbucketApiService } from '../services/bitbucket/BitbucketApiService'
import { useAuthStatus } from '../hooks/useAuth'
import { JiraTicketModal } from './JiraTicketModal'

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
  author: string
  created_date: string
  updated_date: string
  comments: number
  state: string
  reviewers: string[]
  jira_ticket?: string
  userRole?: 'reviewer' | 'author' | 'participant'
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  related_prs: number[]
}

interface AssignedWorkPanelProps {
  onPRSelect?: (pr: AssignedPR) => void
  onTicketSelect?: (ticket: AssignedTicket | null) => void
  onStartReview?: (pr: AssignedPR, ticket?: AssignedTicket) => void
}

export const AssignedWorkPanel: React.FC<AssignedWorkPanelProps> = ({
  onPRSelect,
  onTicketSelect,
  onStartReview
}) => {
  const { isAuthenticated, accessToken, user } = useAuthStatus()
  const [assignedPRs, setAssignedPRs] = useState<AssignedPR[]>([])
  const [assignedTickets, setAssignedTickets] = useState<AssignedTicket[]>([])
  const [isLoadingPRs, setIsLoadingPRs] = useState(false)
  const [isLoadingTickets, setIsLoadingTickets] = useState(false)
  const [selectedPR, setSelectedPR] = useState<AssignedPR | null>(null)
  const [selectedTicket, setSelectedTicket] = useState<AssignedTicket | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [ticketModalOpen, setTicketModalOpen] = useState(false)
  const [modalTicket, setModalTicket] = useState<AssignedTicket | null>(null)

  const [isJiraAuthenticated, setIsJiraAuthenticated] = useState(jiraAuthService.isAuthenticated())

  useEffect(() => {
    console.log('🔍 AssignedWorkPanel - Auth state changed:', { 
      isAuthenticated, 
      hasAccessToken: !!accessToken, 
      userUuid: user?.uuid,
      userName: user?.display_name 
    })
    loadAssignedWork()
  }, [isAuthenticated, accessToken])

  const loadAssignedWork = async () => {
    await Promise.all([
      loadAssignedPRs(),
      isJiraAuthenticated ? loadAssignedTickets() : Promise.resolve()
    ])
  }

  const loadAssignedPRs = async () => {
    setIsLoadingPRs(true)
    setError(null)
    
    try {
      // Use Frontend Bitbucket integration instead of backend mock data
      if (!isAuthenticated || !accessToken) {
        console.log('User not authenticated with Bitbucket - skipping PR loading')
        setAssignedPRs([])
        return
      }

      console.log('Loading PRs from Bitbucket API...')
      
      // Get user workspaces first
      const workspaces = await bitbucketApiService.getUserWorkspaces(accessToken)
      
      let allPRs: AssignedPR[] = []
      
      // Get PRs from each workspace where user has access
      console.log(`🏢 Found ${workspaces.length} workspaces:`, workspaces.map(w => w.slug))
      
      for (const workspace of workspaces.slice(0, 5)) { // Check first 5 workspaces
        try {
          const repos = await bitbucketApiService.getWorkspaceRepositories(
            workspace.slug, 
            accessToken,
            1,
            10 // Get first 10 repos per workspace
          )
          
          // Get PRs from each repository 
          console.log(`📁 Workspace ${workspace.slug} has ${repos.repositories.length} repos`)
          
          for (const repo of repos.repositories.slice(0, 10)) { // Check first 10 repos per workspace
            try {
              const { pullRequests } = await bitbucketApiService.getRepositoryPullRequests(
                workspace.slug,
                repo.name,
                accessToken,
                { state: 'OPEN', pagelen: 20 }
              )
              
              // Fetch full PR details for each PR to get reviewers
              const pullRequestsWithDetails = await Promise.all(
                pullRequests.slice(0, 10).map(async (pr) => { // Limit to 10 PRs per repo for performance
                  try {
                    const fullPR = await bitbucketApiService.getPullRequest(
                      workspace.slug,
                      repo.name,
                      pr.id,
                      accessToken
                    )
                    return fullPR
                  } catch (error) {
                    console.warn(`Failed to fetch full PR details for PR #${pr.id}:`, error)
                    return pr // Return original PR if detail fetch fails
                  }
                })
              )
              
              // Filter PRs where current user is a reviewer
              const currentUser = user?.uuid || user?.account_id
              
              // Enhanced debugging for first PR with details
              if (pullRequestsWithDetails.length > 0 && repo.name === 'rma-mono') {
                console.log('🔍 Detailed PR structure for rma-mono:', {
                  pr: pullRequestsWithDetails[0],
                  hasReviewers: !!pullRequestsWithDetails[0].reviewers,
                  reviewersCount: pullRequestsWithDetails[0].reviewers?.length || 0,
                  hasParticipants: !!pullRequestsWithDetails[0].participants,
                  participantsCount: pullRequestsWithDetails[0].participants?.length || 0
                })
              }
              
              console.log('🔍 Debug PR filtering:', {
                workspace: workspace.slug,
                repo: repo.name,
                totalPRs: pullRequestsWithDetails.length,
                currentUser,
                samplePR: pullRequestsWithDetails[0] ? {
                  id: pullRequestsWithDetails[0].id,
                  title: pullRequestsWithDetails[0].title,
                  author: pullRequestsWithDetails[0].author?.display_name,
                  reviewers: pullRequestsWithDetails[0].reviewers?.map(r => ({
                    uuid: r.uuid,
                    account_id: r.account_id,
                    display_name: r.display_name
                  })),
                  participants: pullRequestsWithDetails[0].participants?.map(p => ({
                    role: p.role,
                    user: p.user ? {
                      uuid: p.user.uuid,
                      account_id: p.user.account_id,
                      display_name: p.user.display_name
                    } : null
                  }))
                } : null
              })
              
              const assignedPRs = pullRequestsWithDetails.filter(pr => {
                // Check reviewers field
                const isReviewer = pr.reviewers?.some(reviewer => 
                  reviewer.uuid === currentUser || reviewer.account_id === currentUser
                )
                
                // Also check participants field (sometimes reviewers are there)
                const isParticipant = pr.participants?.some(participant => 
                  participant.role === 'REVIEWER' && 
                  (participant.user?.uuid === currentUser || participant.user?.account_id === currentUser)
                )
                
                // Check if user is the author (they might need to see their own PRs)
                const isAuthor = pr.author?.uuid === currentUser || pr.author?.account_id === currentUser
                
                // Check ALL participants regardless of role
                const isAnyParticipant = pr.participants?.some(participant => 
                  participant.user?.uuid === currentUser || participant.user?.account_id === currentUser
                )
                
                const isAssigned = isReviewer || isParticipant
                
                if (isReviewer) {
                  console.log('✅ Found PR where user is REVIEWER:', {
                    id: pr.id,
                    title: pr.title,
                    author: pr.author?.display_name,
                    reviewers: pr.reviewers?.map(r => r.display_name),
                    myRole: 'REVIEWER'
                  })
                } else if (isParticipant) {
                  console.log('✅ Found PR where user is PARTICIPANT REVIEWER:', {
                    id: pr.id,
                    title: pr.title,
                    author: pr.author?.display_name,
                    participants: pr.participants?.filter(p => p.role === 'REVIEWER').map(p => p.user?.display_name),
                    myRole: 'PARTICIPANT_REVIEWER'
                  })
                } else if (isAuthor) {
                  console.log('👤 Found PR where user is AUTHOR:', {
                    id: pr.id,
                    title: pr.title,
                    author: pr.author?.display_name,
                    reviewers: pr.reviewers?.map(r => r.display_name),
                    myRole: 'AUTHOR'
                  })
                } else if (isAnyParticipant) {
                  console.log('👤 Found PR where user is PARTICIPANT:', {
                    id: pr.id,
                    title: pr.title,
                    author: pr.author?.display_name,
                    allParticipants: pr.participants?.map(p => ({
                      role: p.role,
                      user: p.user?.display_name
                    })),
                    myRole: 'PARTICIPANT_OTHER'
                  })
                }
                
                // Show PRs where user is reviewer OR author
                return isAssigned || isAuthor
              })
              
              // Convert to our format
              const formattedPRs: AssignedPR[] = assignedPRs.map(pr => {
                // Determine user's role in the PR
                const isUserReviewer = pr.reviewers?.some(reviewer => 
                  reviewer.uuid === currentUser || reviewer.account_id === currentUser
                )
                const isUserAuthor = pr.author?.uuid === currentUser || pr.author?.account_id === currentUser
                
                const branchName = pr.source?.branch?.name || 'unknown'
                const extractedTicket = extractJiraTicketFromBranch(branchName)
                
                // Debug ticket extraction for specific PRs
                if (pr.id === 406) {
                  console.log('🎫 Ticket extraction for PR #406:', {
                    branch: branchName,
                    extractedTicket: extractedTicket
                  })
                }
                
                return {
                  id: pr.id,
                  title: pr.title,
                  branch: branchName,
                  repository: `${workspace.slug}/${repo.name}`,
                  workspace: workspace.slug,
                  author: pr.author?.display_name || 'Unknown',
                  created_date: pr.created_on,
                  updated_date: pr.updated_on,
                  comments: pr.comment_count || 0,
                  state: pr.state,
                  reviewers: pr.reviewers?.map(r => r.display_name) || [],
                  jira_ticket: extractedTicket,
                  userRole: isUserReviewer ? 'reviewer' : isUserAuthor ? 'author' : 'participant'
                }
              })
              
              allPRs = [...allPRs, ...formattedPRs]
              
            } catch (repoError) {
              console.warn(`Failed to load PRs from ${workspace.slug}/${repo.name}:`, repoError)
            }
          }
        } catch (workspaceError) {
          console.warn(`Failed to load repos from ${workspace.slug}:`, workspaceError)
        }
      }
      
      console.log(`Loaded ${allPRs.length} assigned PRs from Bitbucket`)
      setAssignedPRs(allPRs)
      
    } catch (error) {
      console.error('Failed to load assigned PRs from Bitbucket:', error)
      setError('Failed to load PRs from Bitbucket. Please check your authentication.')
    } finally {
      setIsLoadingPRs(false)
    }
  }

  // Helper function to extract Jira ticket from branch name
  const extractJiraTicketFromBranch = (branchName: string): string | undefined => {
    // Match patterns like CMS20-1251 or ABC-123
    const match = branchName.match(/([A-Z]+\d*-\d+)/i)
    return match ? match[1].toUpperCase() : undefined
  }

  const loadAssignedTickets = async () => {
    setIsLoadingTickets(true)
    
    try {
      // Use Jira Auth Service to get real tickets
      const tickets = await jiraAuthService.getAssignedTickets(50)
      
      // The backend already returns tickets in AssignedTicket format
      setAssignedTickets(tickets as any as AssignedTicket[])
    } catch (error) {
      console.error('Failed to load assigned tickets:', error)
      // Don't show error for tickets as it's not critical
    } finally {
      setIsLoadingTickets(false)
    }
  }

  const handlePRSelect = (pr: AssignedPR) => {
    console.log('🎯 PR Selected:', {
      id: pr.id,
      branch: pr.branch,
      jira_ticket: pr.jira_ticket,
      availableTickets: assignedTickets.map(t => t.ticket_id)
    })
    
    setSelectedPR(pr)
    onPRSelect?.(pr)
    
    // Clear ticket selection when PR changes - user must select ticket manually
    setSelectedTicket(null)
    onTicketSelect?.(null)
  }

  const handleTicketSelect = (ticket: AssignedTicket) => {
    setSelectedTicket(ticket)
    onTicketSelect?.(ticket)

    // Auto-select related PR if available
    if (ticket.related_prs.length > 0) {
      const relatedPR = assignedPRs.find(pr => ticket.related_prs.includes(pr.id))
      if (relatedPR) {
        setSelectedPR(relatedPR)
        onPRSelect?.(relatedPR)
      }
    }
  }

  // handleStartReview function removed - not used since action panel at bottom handles this directly

  const handleJiraConnect = async () => {
    console.log('🔗 Starting Jira OAuth flow...')
    jiraAuthService.startAuthFlow()
  }

  const handleJiraLogout = () => {
    console.log('🔓 Logging out from Jira...')
    jiraAuthService.logout()
    setIsJiraAuthenticated(false)
    setAssignedTickets([])
  }

  const handleSwitchAccount = async () => {
    console.log('🔄 Switching Jira account...')
    jiraAuthService.logout()
    setIsJiraAuthenticated(false)
    setAssignedTickets([])
    
    // Use force account selection to clear Atlassian session
    await jiraAuthService.forceAccountSelection()
  }

  const handleTicketClick = (ticket: AssignedTicket) => {
    setModalTicket(ticket)
    setTicketModalOpen(true)
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('de-DE', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      })
    } catch {
      return 'Invalid Date'
    }
  }

  const getStatusColor = (status: string | undefined) => {
    if (!status) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'in progress':
      case 'in arbeit':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'code review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'done':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority: string | undefined) => {
    if (!priority) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    
    switch (priority.toLowerCase()) {
      case 'high':
      case 'highest':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low':
      case 'lowest':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Assigned Work</h2>
          <p className="text-muted-foreground">
            Review pull requests and tickets assigned to you
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadAssignedWork}
            disabled={isLoadingPRs || isLoadingTickets}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${(isLoadingPRs || isLoadingTickets) ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          {!isJiraAuthenticated ? (
            <Button
              variant="outline"
              size="sm"
              onClick={handleJiraConnect}
            >
              <Ticket className="h-4 w-4 mr-2" />
              Connect Jira
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSwitchAccount}
                className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
              >
                <UserCheck className="h-4 w-4 mr-2" />
                Switch Account
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleJiraLogout}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Disconnect
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800 dark:text-red-200">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Assigned Pull Requests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitPullRequest className="h-5 w-5 text-primary" />
              Pull Requests for Review
              <Badge variant="secondary" className="ml-auto">
                {assignedPRs.length}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingPRs ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                <span className="ml-2 text-sm text-muted-foreground">Loading PRs...</span>
              </div>
            ) : assignedPRs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <GitPullRequest className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">No pull requests assigned for review</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {assignedPRs.map((pr) => (
                  <div
                    key={pr.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors hover:bg-muted/50 ${
                      selectedPR?.id === pr.id ? 'border-primary bg-primary/5' : 'border-border'
                    }`}
                    onClick={() => handlePRSelect(pr)}
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate text-foreground">
                          #{pr.id} {pr.title}
                        </h4>
                        <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                          <span className="font-mono">{pr.branch}</span>
                          <span>•</span>
                          <span>{pr.repository}</span>
                        </div>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span className="text-xs">{pr.author}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            <span className="text-xs">{pr.comments}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span className="text-xs">{formatDate(pr.updated_date)}</span>
                          </div>
                        </div>
                        {pr.userRole && (
                          <div className="flex items-center gap-1 mt-1">
                            <span className={`text-xs font-medium ${
                              pr.userRole === 'reviewer' ? 'text-purple-600 dark:text-purple-400' :
                              pr.userRole === 'author' ? 'text-blue-600 dark:text-blue-400' :
                              'text-gray-600 dark:text-gray-400'
                            }`}>
                              {pr.userRole === 'reviewer' ? '👁️ You are reviewing' :
                               pr.userRole === 'author' ? '✍️ You authored' :
                               '👥 You are participating'}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline" className={getStatusColor(pr.state)}>
                          {pr.state.toLowerCase()}
                        </Badge>
                        {pr.jira_ticket && (
                          <Badge variant="secondary" className="text-xs">
                            {pr.jira_ticket}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Assigned Jira Tickets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Ticket className="h-5 w-5 text-primary" />
              Assigned Tickets
              {selectedPR?.jira_ticket && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  (filtered for {selectedPR.jira_ticket})
                </span>
              )}
              <Badge variant="secondary" className="ml-auto">
                {selectedPR && selectedPR.jira_ticket 
                  ? assignedTickets.filter(t => t.ticket_id === selectedPR.jira_ticket).length
                  : assignedTickets.length
                }
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {!isJiraAuthenticated ? (
              <div className="text-center py-8">
                <Ticket className="h-12 w-12 mx-auto mb-4 opacity-50 text-muted-foreground" />
                <p className="text-sm text-muted-foreground mb-4">
                  Connect to Jira to see your assigned tickets
                </p>
                <Button variant="outline" size="sm" onClick={handleJiraConnect}>
                  <Ticket className="h-4 w-4 mr-2" />
                  Connect Jira
                </Button>
              </div>
            ) : isLoadingTickets ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                <span className="ml-2 text-sm text-muted-foreground">Loading tickets...</span>
              </div>
            ) : assignedTickets.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Ticket className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-sm">No tickets assigned</p>
              </div>
            ) : selectedPR && selectedPR.jira_ticket && assignedTickets.filter(t => t.ticket_id === selectedPR.jira_ticket).length === 0 ? (
              <div className="text-center py-8">
                <AlertCircle className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
                <p className="text-sm text-muted-foreground mb-2">
                  No Jira ticket found for <strong>{selectedPR.jira_ticket}</strong>
                </p>
                <p className="text-xs text-muted-foreground">
                  The ticket might not be assigned to you or doesn't exist.
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {/* Filter tickets based on selected PR's Jira ticket */}
                {(selectedPR && selectedPR.jira_ticket 
                  ? assignedTickets.filter(ticket => ticket.ticket_id === selectedPR.jira_ticket)
                  : assignedTickets
                ).map((ticket) => (
                  <div
                    key={ticket.ticket_id}
                    className={`p-4 rounded-lg border cursor-pointer transition-all hover:bg-muted/50 ${
                      selectedTicket?.ticket_id === ticket.ticket_id 
                        ? 'border-primary bg-primary/5 ring-2 ring-primary ring-opacity-50' 
                        : selectedPR?.jira_ticket === ticket.ticket_id 
                          ? 'border-primary/50 bg-primary/5 animate-pulse'
                          : 'border-border'
                    }`}
                    onClick={() => handleTicketSelect(ticket)}
                    onDoubleClick={() => handleTicketClick(ticket)}
                  >
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm truncate text-foreground">
                            {ticket.ticket_id}: {ticket.summary}
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleTicketClick(ticket)
                            }}
                            className="text-xs px-2 py-1 h-auto"
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {ticket.description}
                        </p>
                        <div className="flex items-center gap-4 mt-2">
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            <span className="text-xs">{ticket.acceptance_criteria_count || 0} AC</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span className="text-xs">{formatDate(ticket.updated_date || '')}</span>
                          </div>
                          {ticket.related_prs && ticket.related_prs.length > 0 && (
                            <div className="flex items-center gap-1">
                              <GitPullRequest className="h-3 w-3" />
                              <span className="text-xs">{ticket.related_prs.length} PR(s)</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline" className={getStatusColor(ticket.status)}>
                          {ticket.status || 'Unknown'}
                        </Badge>
                        <Badge variant="outline" className={getPriorityColor(ticket.priority)}>
                          {ticket.priority || 'Unknown'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Action Panel */}
      {(selectedPR || selectedTicket) && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-foreground">
                  {selectedPR && selectedTicket ? 'Ready to Start Review' : 'Select Work to Review'}
                </h3>
                <div className="text-sm text-muted-foreground mt-1 space-y-1">
                  {selectedPR && (
                    <p>• PR: #{selectedPR.id} {selectedPR.title}</p>
                  )}
                  {selectedTicket && (
                    <p>• Ticket: {selectedTicket.ticket_id} - {selectedTicket.summary}</p>
                  )}
                  {selectedPR && !selectedTicket && selectedPR.jira_ticket && (
                    <p className="text-yellow-600">⚠️ Please select the related Jira ticket</p>
                  )}
                  {selectedPR && selectedTicket && (
                    <p className="text-green-600 dark:text-green-400">✅ PR and Ticket linked - AC compliance will be checked</p>
                  )}
                </div>
              </div>
              <Button 
                onClick={() => {
                  if (selectedPR && selectedTicket) {
                    onStartReview?.(selectedPR, selectedTicket)
                  }
                }}
                disabled={!selectedPR || !selectedTicket}
                className="flex items-center gap-2"
              >
                <Eye className="h-4 w-4" />
                {selectedPR && selectedTicket ? 'Continue to Configure' : 'Select Both PR & Ticket'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Jira Ticket Modal */}
      <JiraTicketModal
        ticket={modalTicket}
        isOpen={ticketModalOpen}
        onClose={() => setTicketModalOpen(false)}
      />

    </div>
  )
}