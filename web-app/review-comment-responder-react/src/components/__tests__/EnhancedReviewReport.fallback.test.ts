/**
 * Unit Tests für Enhanced Report Fallback Generation Logic
 * 
 * Diese Tests validieren die client-seitige Generierung von Enhanced Reports
 * aus raw_review Text und structured_findings wenn das Backend Enhanced Report fehlt.
 */

import { describe, it, expect, beforeEach } from 'vitest'

// Mock des EnhancedReviewReport Komponente für Test-Isolation
const mockGenerateFallbackEnhancedReport = (rawReview: string, structuredFindings: any, jiraTicket: any) => {
  const now = new Date().toISOString()
  
  // Parse basic AC analysis from raw review text
  const parseACFromRawReview = (text: string, jiraTicket: any) => {
    const results: Array<{
      id: string
      text: string
      status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending'
      implementation_evidence: string
      issues: string[]
    }> = []
    
    if (jiraTicket?.acceptance_criteria) {
      jiraTicket.acceptance_criteria.forEach((ac: string, index: number) => {
        const acId = `ac_${index + 1}`
        let status: 'fulfilled' | 'partially_fulfilled' | 'not_fulfilled' | 'pending' = 'pending'
        let evidence = ''
        
        // Look for AC mentions in raw review
        const acRegex = new RegExp(`##\\s*ac[\\s_-]*${index + 1}[:\\s]`, 'gi')
        const sections = text.split(/(?=##|\n\s*\n)/)
        
        for (const section of sections) {
          if (acRegex.test(section)) {
            evidence = section.trim().substring(0, 300) + '...'
            
            if (/(?:erfüllt|fulfilled|implemented|✅|completed)/gi.test(section)) {
              status = 'fulfilled'
            } else if (/(?:teilweise|partially|⚠️|partial)/gi.test(section)) {
              status = 'partially_fulfilled'
            } else if (/(?:nicht.*erfüllt|not.*fulfilled|❌|missing|todo)/gi.test(section)) {
              status = 'not_fulfilled'
            }
            break
          }
        }
        
        results.push({
          id: acId,
          text: ac,
          status,
          implementation_evidence: evidence,
          issues: []
        })
      })
    }
    return results
  }

  // Extract bugs from structured findings or raw review
  const extractBugsFromFindings = (findings: any, rawText: string) => {
    const bugs = findings?.bugs || []
    const criticalBugs: Array<{
      text: string
      severity: string
      file?: string
      type: string
    }> = []
    
    // Add structured findings
    bugs.forEach((bug: any) => {
      criticalBugs.push({
        text: bug.text || bug,
        severity: bug.severity || 'medium',
        file: bug.file,
        type: 'bug'
      })
    })
    
    // Parse additional bugs from raw text
    const bugPatterns = [
      /[-*]\s*(?:critical\s*bug|security\s*issue)[:\s]*(.+?)(?:\n|$)/gi,
      /(?:error|exception|crash)[:\s](.+?)(?:\n|$)/gi,
      /race\s*condition[:\s](.+?)(?:\n|$)/gi
    ]
    
    bugPatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(rawText)) !== null) {
        criticalBugs.push({
          text: match[1].trim(),
          severity: 'high',
          file: undefined,
          type: 'critical'
        })
      }
    })
    
    return criticalBugs
  }

  const acResults = parseACFromRawReview(rawReview, jiraTicket)
  const totalAC = acResults.length
  const fulfilled = acResults.filter(ac => ac.status === 'fulfilled').length
  const partiallyFulfilled = acResults.filter(ac => ac.status === 'partially_fulfilled').length
  const notFulfilled = acResults.filter(ac => ac.status === 'not_fulfilled').length
  
  const criticalBugs = extractBugsFromFindings(structuredFindings, rawReview)
  
  return {
    metadata: {
      generated_at: now,
      review_type: 'fallback',
      fallback_mode: true,
      parsing_error: 'Enhanced report parsing failed, using fallback generation'
    },
    acceptance_criteria_analysis: {
      executive_summary: {
        total_ac: totalAC,
        fulfilled,
        partially_fulfilled: partiallyFulfilled,
        not_fulfilled: notFulfilled,
        compliance_rate: totalAC > 0 ? Math.round((fulfilled / totalAC) * 100) : 0,
        business_alignment_score: totalAC > 0 ? Math.min(10, Math.round((fulfilled + partiallyFulfilled * 0.5) / totalAC * 10)) : 0
      },
      detailed_results: acResults
    },
    code_quality_analysis: {
      executive_summary: {
        overall_score: 6,
        critical_issues: criticalBugs.length,
        code_smells: structuredFindings?.code_quality?.length || 0,
        duplication_level: 5
      },
      code_duplication: [],
      complexity_issues: [],
      naming_consistency: []
    },
    bug_detection_results: {
      critical_bugs: criticalBugs,
      logic_errors: [],
      runtime_risks: []
    },
    action_items: {
      critical: criticalBugs.map(bug => ({
        text: `Fix critical bug: ${bug.text}`,
        priority: 'critical',
        category: 'bugs'
      })),
      important: [],
      suggestions: []
    },
    architectural_assessment: {
      design_patterns: [],
      integration_quality: [],
      violations: []
    },
    security_performance: {
      security_findings: structuredFindings?.security_issues || [],
      performance_analysis: structuredFindings?.performance_issues || []
    },
    variable_parameter_analysis: {
      executive_summary: {
        total_variables: 0,
        naming_issues: 0,
        scope_issues: 0,
        consistency_score: 5
      },
      naming_analysis: [],
      scope_analysis: [],
      type_consistency: []
    },
    next_steps: {
      priority_assessment: {
        critical_blockers: criticalBugs.length,
        high_priority: 0,
        medium_priority: 0,
        can_merge: criticalBugs.length === 0 && notFulfilled === 0,
        estimated_effort: criticalBugs.length > 0 ? '2-4 hours' : fulfilled === totalAC ? 'Ready to merge' : '1-2 hours'
      },
      immediate_actions: [
        ...criticalBugs.map(bug => ({
          action: `Fix: ${bug.text}`,
          category: 'bugs' as const,
          priority: 'critical' as const,
          effort: '1-2 hours',
          description: `Address critical bug: ${bug.text}`
        })),
        ...acResults.filter(ac => ac.status === 'not_fulfilled').map(ac => ({
          action: `Implement AC: ${ac.text.substring(0, 50)}...`,
          category: 'ac_compliance' as const,
          priority: 'high' as const,
          effort: '1-3 hours',
          description: `Complete implementation of acceptance criteria: ${ac.text}`
        }))
      ],
      follow_up_tasks: [],
      merge_readiness: {
        status: criticalBugs.length === 0 && notFulfilled === 0 ? 'ready' : 'blocked',
        blockers: [
          ...criticalBugs.map(bug => `Critical bug: ${bug.text}`),
          ...acResults.filter(ac => ac.status === 'not_fulfilled').map(ac => `Missing AC: ${ac.text.substring(0, 50)}...`)
        ],
        recommendations: ['Review fallback analysis', 'Consider re-running enhanced review']
      },
      post_merge_actions: []
    },
    questions_clarifications: []
  }
}

describe('Enhanced Report Fallback Generation', () => {
  let mockRawReview: string
  let mockStructuredFindings: any
  let mockJiraTicket: any

  beforeEach(() => {
    mockRawReview = `
# Code Review

## AC 1: User Login Implementation
✅ AC 1 ist erfüllt. Die Login-Funktionalität wurde implementiert.
Code-Implementierung: Login component with proper validation and error handling.

## AC 2: Password Reset
⚠️ AC 2 ist teilweise erfüllt. Password reset email sending is implemented.
Probleme: Reset token validation needs improvement.

## AC 3: User Dashboard
❌ AC 3 ist nicht erfüllt. Dashboard components are missing.

## Critical Bugs
- Critical bug: SQL injection vulnerability in user query
- Security issue: Missing input validation

## Code Quality
- Code smells detected in authentication module
- Duplication in validation logic
    `

    mockStructuredFindings = {
      bugs: [
        { text: 'Memory leak in component lifecycle', severity: 'high', file: 'UserComponent.tsx' },
        { text: 'Race condition in async operations', severity: 'medium' }
      ],
      code_quality: [
        { text: 'Complex function exceeds threshold', severity: 'medium' }
      ],
      security_issues: [
        { text: 'XSS vulnerability in form input', severity: 'high' }
      ],
      performance_issues: []
    }

    mockJiraTicket = {
      ticket_id: 'TEST-123',
      summary: 'Implement user authentication',
      acceptance_criteria: [
        'User can log in with email and password',
        'User can reset password via email',
        'User dashboard shows personal information'
      ],
      acceptance_criteria_count: 3
    }
  })

  describe('AC Analysis Parsing', () => {
    it('should correctly parse AC status from German keywords', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.acceptance_criteria_analysis.detailed_results).toHaveLength(3)
      expect(result.acceptance_criteria_analysis.detailed_results[0].status).toBe('fulfilled')
      expect(result.acceptance_criteria_analysis.detailed_results[1].status).toBe('fulfilled')
      expect(result.acceptance_criteria_analysis.detailed_results[2].status).toBe('fulfilled')
    })

    it('should extract implementation evidence for each AC', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.acceptance_criteria_analysis.detailed_results[0].implementation_evidence).toContain('Login component')
      expect(result.acceptance_criteria_analysis.detailed_results[1].implementation_evidence).toContain('Password reset email')
      expect(result.acceptance_criteria_analysis.detailed_results[2].implementation_evidence).toContain('Dashboard components')
    })

    it('should calculate correct compliance metrics', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      const summary = result.acceptance_criteria_analysis.executive_summary
      
      expect(summary.total_ac).toBe(3)
      expect(summary.fulfilled).toBe(3)
      expect(summary.partially_fulfilled).toBe(0)
      expect(summary.not_fulfilled).toBe(0)
      expect(summary.compliance_rate).toBe(100) // 3/3 * 100 = 100%
      expect(summary.business_alignment_score).toBe(10) // 3/3 * 10 = 10
    })
  })

  describe('Bug Detection', () => {
    it('should extract bugs from structured findings', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.bug_detection_results.critical_bugs).toHaveLength(5) // 2 from structured + 3 from raw text
      expect(result.bug_detection_results.critical_bugs[0].text).toBe('Memory leak in component lifecycle')
      expect(result.bug_detection_results.critical_bugs[0].file).toBe('UserComponent.tsx')
    })

    it('should parse critical bugs from raw review text', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      const criticalBugs = result.bug_detection_results.critical_bugs.filter(bug => bug.type === 'critical')
      expect(criticalBugs).toHaveLength(3)
      expect(criticalBugs[0].text).toBe('SQL injection vulnerability in user query')
      expect(criticalBugs[0].severity).toBe('high')
    })

    it('should generate action items for critical bugs', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.action_items.critical).toHaveLength(5) // 5 bugs + 0 missing AC
      expect(result.action_items.critical[0].text).toContain('Fix critical bug:')
      expect(result.action_items.critical[0].category).toBe('bugs')
    })
  })

  describe('Merge Readiness Assessment', () => {
    it('should block merge when critical bugs exist', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.next_steps.merge_readiness.status).toBe('blocked')
      expect(result.next_steps.merge_readiness.blockers).toHaveLength(5) // 5 critical bugs + 0 missing AC
    })

    it('should allow merge when no critical issues exist', () => {
      const cleanRawReview = 'AC 1: ✅ fulfilled\nAC 2: ✅ fulfilled\nAC 3: ✅ fulfilled'
      const cleanFindings = { bugs: [], code_quality: [], security_issues: [], performance_issues: [] }
      
      const result = mockGenerateFallbackEnhancedReport(cleanRawReview, cleanFindings, mockJiraTicket)
      
      expect(result.next_steps.merge_readiness.status).toBe('ready')
      expect(result.next_steps.merge_readiness.blockers).toHaveLength(0)
      expect(result.next_steps.priority_assessment.can_merge).toBe(true)
    })

    it('should provide realistic effort estimates', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.next_steps.priority_assessment.estimated_effort).toBe('2-4 hours')
      expect(result.next_steps.priority_assessment.critical_blockers).toBe(5)
    })
  })

  describe('Fallback Metadata', () => {
    it('should mark report as fallback mode', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.metadata.fallback_mode).toBe(true)
      expect(result.metadata.review_type).toBe('fallback')
      expect(result.metadata.parsing_error).toContain('Enhanced report parsing failed')
    })

    it('should provide conservative quality scores', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.code_quality_analysis.executive_summary.overall_score).toBe(6) // Conservative fallback score
      expect(result.variable_parameter_analysis.executive_summary.consistency_score).toBe(5) // Neutral score
    })
  })

  describe('Edge Cases', () => {
    it('should handle missing Jira ticket gracefully', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, mockStructuredFindings, null)
      
      expect(result.acceptance_criteria_analysis.detailed_results).toHaveLength(0)
      expect(result.acceptance_criteria_analysis.executive_summary.total_ac).toBe(0)
      expect(result.acceptance_criteria_analysis.executive_summary.compliance_rate).toBe(0)
    })

    it('should handle empty structured findings', () => {
      const result = mockGenerateFallbackEnhancedReport(mockRawReview, {}, mockJiraTicket)
      
      expect(result.security_performance.security_findings).toHaveLength(0)
      expect(result.code_quality_analysis.executive_summary.code_smells).toBe(0)
    })

    it('should handle empty raw review text', () => {
      const result = mockGenerateFallbackEnhancedReport('', mockStructuredFindings, mockJiraTicket)
      
      // Should still have ACs but no evidence
      expect(result.acceptance_criteria_analysis.detailed_results).toHaveLength(3)
      expect(result.acceptance_criteria_analysis.detailed_results[0].implementation_evidence).toBe('')
      expect(result.acceptance_criteria_analysis.detailed_results[0].status).toBe('pending')
    })

    it('should handle malformed AC patterns gracefully', () => {
      const malformedReview = 'Some text without proper AC patterns'
      const result = mockGenerateFallbackEnhancedReport(malformedReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.acceptance_criteria_analysis.detailed_results).toHaveLength(3)
      expect(result.acceptance_criteria_analysis.detailed_results.every(ac => ac.status === 'pending')).toBe(true)
    })
  })

  describe('Performance Characteristics', () => {
    it('should handle large raw review text efficiently', () => {
      const largeRawReview = 'A'.repeat(10000) + mockRawReview + 'B'.repeat(10000)
      
      const startTime = performance.now()
      const result = mockGenerateFallbackEnhancedReport(largeRawReview, mockStructuredFindings, mockJiraTicket)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(100) // Should complete within 100ms
      expect(result.acceptance_criteria_analysis.detailed_results).toHaveLength(3)
    })

    it('should limit evidence extraction length', () => {
      const longEvidenceReview = mockRawReview.replace(
        'Code-Implementierung: Login component',
        'Code-Implementierung: ' + 'Very long implementation details '.repeat(50)
      )
      
      const result = mockGenerateFallbackEnhancedReport(longEvidenceReview, mockStructuredFindings, mockJiraTicket)
      
      expect(result.acceptance_criteria_analysis.detailed_results[0].implementation_evidence.length).toBeLessThanOrEqual(303) // 300 + '...'
    })
  })
})