/**
 * Performance Tests für Client-side Enhanced Report Parsing
 * 
 * Diese Tests validieren die Performance-Charakteristika der Fallback-Generierung,
 * insbesondere bei großen raw_review Texten und komplexen structured_findings.
 */

import { describe, it, expect } from 'vitest'
import { performance } from 'perf_hooks'

// Mock der Parsing-Funktionen aus EnhancedReviewReport
const createLargeDataset = (size: 'small' | 'medium' | 'large' | 'xlarge') => {
  const baseSections = {
    small: 10,
    medium: 50,
    large: 200,
    xlarge: 1000
  }
  
  const sectionCount = baseSections[size]
  const sections = []
  
  for (let i = 0; i < sectionCount; i++) {
    sections.push(`
## AC ${i + 1}: Feature Implementation ${i + 1}
${i % 3 === 0 ? '✅' : i % 3 === 1 ? '⚠️' : '❌'} AC ${i + 1} ist ${i % 3 === 0 ? 'erfüllt' : i % 3 === 1 ? 'teilweise erfüllt' : 'nicht erfüllt'}.
Code-Implementierung: Implementation details for feature ${i + 1} with extensive documentation and code examples.
This section contains multiple paragraphs of detailed analysis and code review feedback.
${'Additional analysis content '.repeat(size === 'xlarge' ? 100 : size === 'large' ? 50 : 10)}

## Issues Found in AC ${i + 1}
- Critical bug: Issue ${i + 1} in component
- Security concern: Vulnerability ${i + 1} detected
- Performance issue: Optimization needed for ${i + 1}
    `)
  }
  
  return sections.join('\n')
}

const createLargeStructuredFindings = (size: 'small' | 'medium' | 'large' | 'xlarge') => {
  const itemCounts = {
    small: 10,
    medium: 50,
    large: 200,
    xlarge: 1000
  }
  
  const count = itemCounts[size]
  
  return {
    bugs: Array.from({ length: count }, (_, i) => ({
      text: `Bug ${i + 1}: Critical issue in component ${i + 1}`,
      severity: i % 3 === 0 ? 'high' : 'medium',
      file: `Component${i + 1}.tsx`,
      line: (i + 1) * 10
    })),
    code_quality: Array.from({ length: count }, (_, i) => ({
      text: `Code quality issue ${i + 1}: Complexity detected`,
      severity: 'medium',
      file: `Module${i + 1}.ts`
    })),
    security_issues: Array.from({ length: count }, (_, i) => ({
      text: `Security issue ${i + 1}: Vulnerability detected`,
      severity: i % 2 === 0 ? 'high' : 'medium',
      file: `Service${i + 1}.ts`
    })),
    performance_issues: Array.from({ length: count }, (_, i) => ({
      text: `Performance issue ${i + 1}: Optimization needed`,
      severity: 'medium',
      file: `Handler${i + 1}.ts`
    }))
  }
}

const createLargeJiraTicket = (acCount: number) => ({
  ticket_id: 'PERF-TEST-123',
  summary: 'Large scale feature implementation',
  acceptance_criteria: Array.from({ length: acCount }, (_, i) => 
    `Acceptance criteria ${i + 1}: Feature ${i + 1} must be implemented with proper validation and error handling`
  ),
  acceptance_criteria_count: acCount
})

// Mock implementation of the parsing logic for performance testing
const mockPerformanceFallbackGeneration = (rawReview: string, structuredFindings: any, jiraTicket: any) => {
  const startTime = performance.now()
  
  // Parse AC analysis - simulate the actual parsing logic
  const parseACResults = () => {
    if (!jiraTicket?.acceptance_criteria) return []
    
    return jiraTicket.acceptance_criteria.map((ac: string, index: number) => {
      const acId = `ac_${index + 1}`
      let status = 'pending'
      let evidence = ''
      
      // Simulate regex search through large text
      const acRegex = new RegExp(`(ac[\\s_-]*${index + 1}|acceptance[\\s_-]*criteria[\\s_-]*${index + 1})`, 'gi')
      const sections = rawReview.split(/\n\s*\n/)
      
      for (const section of sections) {
        if (acRegex.test(section)) {
          evidence = section.trim().substring(0, 300) + '...'
          
          if (/(?:erfüllt|fulfilled|implemented|✅|completed)/gi.test(section)) {
            status = 'fulfilled'
          } else if (/(?:teilweise|partially|⚠️|partial)/gi.test(section)) {
            status = 'partially_fulfilled'
          } else if (/(?:nicht.*erfüllt|not.*fulfilled|❌|missing|todo)/gi.test(section)) {
            status = 'not_fulfilled'
          }
          break
        }
      }
      
      return {
        id: acId,
        text: ac,
        status,
        implementation_evidence: evidence,
        issues: []
      }
    })
  }
  
  // Parse bugs - simulate structured findings processing
  const parseBugs = () => {
    const bugs = structuredFindings?.bugs || []
    const criticalBugs: Array<{
      text: string
      severity: string
      file?: string
      type: string
    }> = []
    
    // Process structured findings
    bugs.forEach((bug: any) => {
      if (!bug || typeof bug !== 'object' && typeof bug !== 'string') return
      const text = typeof bug === 'string' ? bug : (bug.text || String(bug))
      if (!text || text === '[object Object]') return
      
      criticalBugs.push({
        text,
        severity: bug.severity || 'medium',
        file: bug.file,
        type: 'bug'
      })
    })
    
    // Parse additional bugs from raw text using regex
    const bugPatterns = [
      /(?:critical|severe|major)\s*bug[:\s](.+?)(?:\n|$)/gi,
      /(?:error|exception|crash)[:\s](.+?)(?:\n|$)/gi,
      /race\s*condition[:\s](.+?)(?:\n|$)/gi
    ]
    
    bugPatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(rawReview)) !== null) {
        criticalBugs.push({
          text: match[1].trim(),
          severity: 'high',
          file: undefined,
          type: 'critical'
        })
      }
    })
    
    return criticalBugs
  }
  
  const acResults = parseACResults()
  const criticalBugs = parseBugs()
  const parsingTime = performance.now() - startTime
  
  return {
    acResults,
    criticalBugs,
    parsingTime,
    totalAC: acResults.length,
    totalBugs: criticalBugs.length
  }
}

describe('Enhanced Report Fallback Performance Tests', () => {
  
  describe('AC Parsing Performance', () => {
    it('should parse small dataset (10 ACs) within 50ms', () => {
      const rawReview = createLargeDataset('small')
      const structuredFindings = createLargeStructuredFindings('small')
      const jiraTicket = createLargeJiraTicket(10)
      
      const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      
      expect(result.parsingTime).toBeLessThan(50)
      expect(result.totalAC).toBe(10)
      expect(result.acResults.length).toBe(10)
    })
    
    it('should parse medium dataset (50 ACs) within 200ms', () => {
      const rawReview = createLargeDataset('medium')
      const structuredFindings = createLargeStructuredFindings('medium')
      const jiraTicket = createLargeJiraTicket(50)
      
      const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      
      expect(result.parsingTime).toBeLessThan(200)
      expect(result.totalAC).toBe(50)
      expect(result.acResults.length).toBe(50)
    })
    
    it('should parse large dataset (200 ACs) within 800ms', () => {
      const rawReview = createLargeDataset('large')
      const structuredFindings = createLargeStructuredFindings('large')
      const jiraTicket = createLargeJiraTicket(200)
      
      const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      
      expect(result.parsingTime).toBeLessThan(800)
      expect(result.totalAC).toBe(200)
      expect(result.acResults.length).toBe(200)
    })
    
    it('should handle xlarge dataset (1000 ACs) within 3 seconds', () => {
      const rawReview = createLargeDataset('xlarge')
      const structuredFindings = createLargeStructuredFindings('xlarge')
      const jiraTicket = createLargeJiraTicket(1000)
      
      const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      
      expect(result.parsingTime).toBeLessThan(3000)
      expect(result.totalAC).toBe(1000)
      expect(result.acResults.length).toBe(1000)
    })
  })
  
  describe('Memory Usage Performance', () => {
    it('should not cause memory leaks with repeated parsing', () => {
      const rawReview = createLargeDataset('medium')
      const structuredFindings = createLargeStructuredFindings('medium')
      const jiraTicket = createLargeJiraTicket(50)
      
      const initialMemory = process.memoryUsage().heapUsed
      
      // Run parsing 100 times
      for (let i = 0; i < 100; i++) {
        mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = process.memoryUsage().heapUsed
      const memoryGrowth = finalMemory - initialMemory
      
      // Memory growth should be minimal (less than 10MB)
      expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024)
    })
    
    it('should handle large structured findings without memory issues', () => {
      const rawReview = createLargeDataset('small')
      const structuredFindings = createLargeStructuredFindings('xlarge')
      const jiraTicket = createLargeJiraTicket(10)
      
      const initialMemory = process.memoryUsage().heapUsed
      
      const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
      
      const finalMemory = process.memoryUsage().heapUsed
      const memoryUsed = finalMemory - initialMemory
      
      // Should process large findings without excessive memory usage (less than 50MB)
      expect(memoryUsed).toBeLessThan(50 * 1024 * 1024)
      expect(result.totalBugs).toBeGreaterThan(1000) // Should process all bugs
    })
  })
  
  describe('Regex Performance', () => {
    it('should perform AC regex matching efficiently on large text', () => {
      const largeText = 'Background text '.repeat(10000) + 
                       createLargeDataset('medium') + 
                       'Additional content '.repeat(10000)
      
      const jiraTicket = createLargeJiraTicket(10)
      const structuredFindings = { bugs: [] }
      
      const startTime = performance.now()
      const result = mockPerformanceFallbackGeneration(largeText, structuredFindings, jiraTicket)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(500) // Should complete within 500ms
      expect(result.acResults.length).toBe(10)
    })
    
    it('should handle pathological regex cases gracefully', () => {
      // Create text with many potential matches to test regex efficiency
      const pathologicalText = Array.from({ length: 1000 }, (_, i) => 
        `ac ${i % 50} some text ac_${i % 50} more text acceptance criteria ${i % 50}`
      ).join('\n')
      
      const jiraTicket = createLargeJiraTicket(50)
      const structuredFindings = { bugs: [] }
      
      const startTime = performance.now()
      const result = mockPerformanceFallbackGeneration(pathologicalText, structuredFindings, jiraTicket)
      const endTime = performance.now()
      
      // Should not take excessive time even with many potential matches
      expect(endTime - startTime).toBeLessThan(1000)
      expect(result.acResults.length).toBe(50)
    })
  })
  
  describe('Concurrent Processing Performance', () => {
    it('should handle multiple simultaneous parsing operations', async () => {
      const rawReview = createLargeDataset('medium')
      const structuredFindings = createLargeStructuredFindings('medium')
      const jiraTicket = createLargeJiraTicket(50)
      
      const startTime = performance.now()
      
      // Run 10 concurrent parsing operations
      const promises = Array.from({ length: 10 }, () => 
        new Promise(resolve => {
          const result = mockPerformanceFallbackGeneration(rawReview, structuredFindings, jiraTicket)
          resolve(result)
        })
      )
      
      const results = await Promise.all(promises)
      const endTime = performance.now()
      
      // All should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(2000)
      
      // All should produce consistent results
      results.forEach(result => {
        expect((result as any).totalAC).toBe(50)
        expect((result as any).acResults.length).toBe(50)
      })
    })
  })
  
  describe('Edge Case Performance', () => {
    it('should handle empty inputs efficiently', () => {
      const startTime = performance.now()
      
      const result = mockPerformanceFallbackGeneration('', {}, { acceptance_criteria: [] })
      
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(10) // Should be very fast for empty input
      expect(result.acResults.length).toBe(0)
      expect(result.totalBugs).toBe(0)
    })
    
    it('should handle malformed data without performance degradation', () => {
      const malformedRawReview = 'Malformed data '.repeat(1000) + '\x00\x01\x02invalid chars'
      const malformedFindings = {
        bugs: [null, undefined, { malformed: true }, 'string instead of object'],
        code_quality: 'not an array'
      }
      const malformedTicket = {
        acceptance_criteria: ['valid criteria']
      }
      
      const startTime = performance.now()
      
      // Should not throw and should complete quickly
      expect(() => {
        const result = mockPerformanceFallbackGeneration(malformedRawReview, malformedFindings, malformedTicket)
        expect(result).toBeDefined()
      }).not.toThrow()
      
      const endTime = performance.now()
      expect(endTime - startTime).toBeLessThan(200)
    })
  })
  
  describe('Text Processing Performance Benchmarks', () => {
    const benchmarkSizes = [
      { name: '1KB', size: 1024 },
      { name: '10KB', size: 10 * 1024 },
      { name: '100KB', size: 100 * 1024 },
      { name: '1MB', size: 1024 * 1024 }
    ]
    
    benchmarkSizes.forEach(({ name, size }) => {
      it(`should process ${name} of raw review text efficiently`, () => {
        const largeText = 'Sample review content with AC analysis. '.repeat(Math.floor(size / 40))
        const structuredFindings = createLargeStructuredFindings('small')
        const jiraTicket = createLargeJiraTicket(10)
        
        const startTime = performance.now()
        const result = mockPerformanceFallbackGeneration(largeText, structuredFindings, jiraTicket)
        const endTime = performance.now()
        
        // Performance thresholds based on text size
        const thresholds = {
          '1KB': 50,
          '10KB': 100,
          '100KB': 500,
          '1MB': 2000
        }
        
        expect(endTime - startTime).toBeLessThan(thresholds[name as keyof typeof thresholds])
        expect(result.acResults.length).toBe(10)
        
        console.log(`${name} processing time: ${(endTime - startTime).toFixed(2)}ms`)
      })
    })
  })
  
  describe('Real-world Performance Scenarios', () => {
    it('should handle typical code review size efficiently', () => {
      // Simulate a typical code review: 20 files, 500 lines each, 5 ACs
      const typicalReview = Array.from({ length: 20 }, (_, fileIndex) => 
        Array.from({ length: 25 }, (_, sectionIndex) => 
          `File ${fileIndex + 1} Section ${sectionIndex + 1}: Analysis content with code examples and findings.`
        ).join('\n')
      ).join('\n\n') + '\n\n' + createLargeDataset('small')
      
      const typicalFindings = createLargeStructuredFindings('small')
      const typicalTicket = createLargeJiraTicket(5)
      
      const startTime = performance.now()
      const result = mockPerformanceFallbackGeneration(typicalReview, typicalFindings, typicalTicket)
      const endTime = performance.now()
      
      // Should complete within 200ms for typical review
      expect(endTime - startTime).toBeLessThan(200)
      expect(result.totalAC).toBe(5)
      
      console.log(`Typical review processing time: ${(endTime - startTime).toFixed(2)}ms`)
    })
    
    it('should handle large enterprise review efficiently', () => {
      // Simulate large enterprise review: 100 files, multiple teams, 20 ACs
      const enterpriseReview = createLargeDataset('large')
      const enterpriseFindings = createLargeStructuredFindings('medium')
      const enterpriseTicket = createLargeJiraTicket(20)
      
      const startTime = performance.now()
      const result = mockPerformanceFallbackGeneration(enterpriseReview, enterpriseFindings, enterpriseTicket)
      const endTime = performance.now()
      
      // Should complete within 1 second for large enterprise review
      expect(endTime - startTime).toBeLessThan(1000)
      expect(result.totalAC).toBe(20)
      
      console.log(`Enterprise review processing time: ${(endTime - startTime).toFixed(2)}ms`)
    })
  })
})