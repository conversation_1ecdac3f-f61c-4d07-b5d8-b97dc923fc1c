/* Jira HTML Content Styling */
.jira-description {
  /* Base styles */
  color: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Headers */
.jira-description h1,
.jira-description h2,
.jira-description h3,
.jira-description h4,
.jira-description h5,
.jira-description h6 {
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.jira-description h1 { font-size: 1.5rem; }
.jira-description h2 { font-size: 1.25rem; }
.jira-description h3 { font-size: 1.125rem; }
.jira-description h4 { font-size: 1rem; }
.jira-description h5 { font-size: 0.875rem; }
.jira-description h6 { font-size: 0.75rem; }

/* Paragraphs */
.jira-description p {
  margin-bottom: 0.75rem;
}

/* Lists */
.jira-description ul,
.jira-description ol {
  margin-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.jira-description ul {
  list-style-type: disc;
}

.jira-description ol {
  list-style-type: decimal;
}

.jira-description li {
  margin-bottom: 0.25rem;
}

/* Task lists (checkboxes) */
.jira-description .task-list-item {
  list-style: none;
  margin-left: -1.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.jira-description .task-list-item input[type="checkbox"] {
  position: absolute;
  left: 0;
  top: 0.25rem;
}

/* Blockquotes */
.jira-description blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1rem;
  margin-left: 0;
  margin-bottom: 0.75rem;
  color: #6b7280;
}

/* Code */
.jira-description code {
  background-color: rgba(107, 114, 128, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: monospace;
  font-size: 0.875em;
}

.jira-description pre {
  background-color: rgba(107, 114, 128, 0.1);
  padding: 0.75rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-bottom: 0.75rem;
}

.jira-description pre code {
  background-color: transparent;
  padding: 0;
}

/* Tables */
.jira-description table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 0.75rem;
}

.jira-description th,
.jira-description td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.jira-description th {
  background-color: rgba(107, 114, 128, 0.1);
  font-weight: 600;
}

/* Links */
.jira-description a {
  color: #3b82f6;
  text-decoration: underline;
}

.jira-description a:hover {
  color: #2563eb;
}

/* Dark mode adjustments */
.dark .jira-description blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.dark .jira-description code {
  background-color: rgba(156, 163, 175, 0.2);
}

.dark .jira-description pre {
  background-color: rgba(156, 163, 175, 0.2);
}

.dark .jira-description th,
.dark .jira-description td {
  border-color: #4b5563;
}

.dark .jira-description th {
  background-color: rgba(156, 163, 175, 0.2);
}

/* Special Jira elements */
.jira-description .aui-lozenge {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e5e7eb;
  color: #374151;
}

.dark .jira-description .aui-lozenge {
  background-color: #4b5563;
  color: #e5e7eb;
}