import React, { useMemo } from 'react'
import { 
  <PERSON>, 
  Check<PERSON><PERSON>cle, 
  Alert<PERSON>riangle, 
  Clock,
  Zap,
  TrendingUp,
  Users
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Progress } from './ui/progress'
import { Badge } from './ui/badge'
import { AgentCard, type AgentStatus } from './multi-agent/AgentCard'

export interface MultiAgentReviewStatus {
  review_id: string
  status: 'started' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  agent_statuses: Record<string, AgentStatus>
  started_at?: string
  completed_at?: string
  estimated_remaining_time?: number
  active_agents: string[]
  completed_agents: string[]
  failed_agents: string[]
  context_status?: Record<string, string>
}

interface MultiAgentProgressTrackerProps {
  reviewStatus: MultiAgentReviewStatus | null
  className?: string
}

const getOverallStatusIcon = (status: MultiAgentReviewStatus['status']) => {
  const iconProps = { className: "h-5 w-5" }
  
  switch (status) {
    case 'started':
      return <Clock {...iconProps} className="h-5 w-5 text-yellow-500" />
    case 'running':
      return <Play {...iconProps} className="h-5 w-5 text-blue-500" />
    case 'completed':
      return <CheckCircle {...iconProps} className="h-5 w-5 text-green-500" />
    case 'failed':
      return <AlertTriangle {...iconProps} className="h-5 w-5 text-red-500" />
    case 'cancelled':
      return <AlertTriangle {...iconProps} className="h-5 w-5 text-orange-500" />
    default:
      return <Clock {...iconProps} className="h-5 w-5 text-muted-foreground" />
  }
}

const getOverallStatusColor = (status: MultiAgentReviewStatus['status']) => {
  switch (status) {
    case 'started':
      return 'border-yellow-200 bg-yellow-50'
    case 'running':
      return 'border-blue-200 bg-blue-50'
    case 'completed':
      return 'border-green-200 bg-green-50'
    case 'failed':
      return 'border-red-200 bg-red-50'
    case 'cancelled':
      return 'border-orange-200 bg-orange-50'
    default:
      return 'border-muted bg-muted/50'
  }
}

const formatDuration = (startTime?: string, endTime?: string): string => {
  if (!startTime) return ''
  
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date()
  const durationMs = end.getTime() - start.getTime()
  const durationSeconds = Math.floor(durationMs / 1000)
  
  const minutes = Math.floor(durationSeconds / 60)
  const seconds = durationSeconds % 60
  
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`
  } else {
    return `${seconds}s`
  }
}

const formatTimeRemaining = (seconds?: number): string => {
  if (!seconds || seconds <= 0) return ''
  
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `~${minutes}m ${remainingSeconds}s remaining`
  } else {
    return `~${remainingSeconds}s remaining`
  }
}

export const MultiAgentProgressTracker: React.FC<MultiAgentProgressTrackerProps> = ({
  reviewStatus,
  className = ""
}) => {
  const stats = useMemo(() => {
    if (!reviewStatus) return null
    
    const agents = Object.values(reviewStatus.agent_statuses)
    const totalAgents = agents.filter(agent => agent.status !== 'skipped').length
    const completedAgents = agents.filter(agent => agent.status === 'completed').length
    const failedAgents = agents.filter(agent => agent.status === 'failed').length
    const runningAgents = agents.filter(agent => agent.status === 'running').length
    const pendingAgents = agents.filter(agent => agent.status === 'pending').length
    
    return {
      total: totalAgents,
      completed: completedAgents,
      failed: failedAgents,
      running: runningAgents,
      pending: pendingAgents,
      successRate: totalAgents > 0 ? (completedAgents / totalAgents) * 100 : 0
    }
  }, [reviewStatus])
  
  if (!reviewStatus) {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No multi-agent review in progress</p>
          </div>
        </CardContent>
      </Card>
    )
  }
  
  const statusIcon = getOverallStatusIcon(reviewStatus.status)
  const statusColor = getOverallStatusColor(reviewStatus.status)
  const duration = formatDuration(reviewStatus.started_at, reviewStatus.completed_at)
  const timeRemaining = formatTimeRemaining(reviewStatus.estimated_remaining_time)
  
  // Sort agents for consistent display order
  const sortedAgents = Object.entries(reviewStatus.agent_statuses).sort(([a], [b]) => {
    const order = [
      'acceptance_criteria',
      'bug_detection', 
      'security_analysis',
      'logic_analysis',
      'code_quality',
      'architectural',
      'summary'
    ]
    return order.indexOf(a) - order.indexOf(b)
  })
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Progress Header */}
      <Card className={`${statusColor} border-2`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-3">
            {statusIcon}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Multi-Agent Code Review
                </h3>
                <Badge variant="outline" className="ml-2">
                  {reviewStatus.status.charAt(0).toUpperCase() + reviewStatus.status.slice(1)}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground font-normal mt-1">
                {reviewStatus.status === 'running' && `${reviewStatus.active_agents.length} agents running in parallel`}
                {reviewStatus.status === 'completed' && `All agents completed successfully`}
                {reviewStatus.status === 'failed' && `${stats?.failed || 0} agents failed`}
                {reviewStatus.status === 'started' && `Initializing multi-agent orchestration...`}
                {reviewStatus.status === 'cancelled' && `Review cancelled by user`}
              </p>
            </div>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="pt-0">
          {/* Overall Progress Bar */}
          <div className="space-y-3 mb-4">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium">Overall Progress</span>
              <span className="font-semibold">{Math.round(reviewStatus.progress)}%</span>
            </div>
            <Progress value={reviewStatus.progress} className="h-3" />
            
            {/* Timing and Stats */}
            <div className="flex justify-between items-center text-xs text-muted-foreground">
              <div className="flex items-center gap-4">
                {duration && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{duration}</span>
                  </div>
                )}
                {stats && (
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    <span>{stats.completed}/{stats.total} completed</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-4">
                {timeRemaining && (
                  <div className="flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    <span>{timeRemaining}</span>
                  </div>
                )}
                {stats && stats.successRate > 0 && reviewStatus.status === 'completed' && (
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>{Math.round(stats.successRate)}% success rate</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Quick Stats */}
          {stats && (
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 text-center">
              <div className="bg-white/60 rounded-lg p-2 border border-white/40">
                <div className="text-lg font-bold text-blue-600">{stats.running}</div>
                <div className="text-xs text-muted-foreground">Running</div>
              </div>
              <div className="bg-white/60 rounded-lg p-2 border border-white/40">
                <div className="text-lg font-bold text-green-600">{stats.completed}</div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>
              <div className="bg-white/60 rounded-lg p-2 border border-white/40">
                <div className="text-lg font-bold text-yellow-600">{stats.pending}</div>
                <div className="text-xs text-muted-foreground">Pending</div>
              </div>
              <div className="bg-white/60 rounded-lg p-2 border border-white/40">
                <div className="text-lg font-bold text-red-600">{stats.failed}</div>
                <div className="text-xs text-muted-foreground">Failed</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Agent Cards Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {sortedAgents.map(([agentType, agentStatus]) => (
          <AgentCard
            key={agentType}
            agentStatus={agentStatus}
            className="h-full"
          />
        ))}
      </div>
      
      {/* Context Status (if available) */}
      {reviewStatus.context_status && Object.keys(reviewStatus.context_status).length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <div className="h-4 w-4 bg-blue-100 rounded flex items-center justify-center">
                <div className="h-2 w-2 bg-blue-500 rounded" />
              </div>
              Context Preparation Status
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
              {Object.entries(reviewStatus.context_status).map(([contextType, status]) => (
                <div key={contextType} className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground capitalize">
                    {contextType.replace('_', ' ')}:
                  </span>
                  <Badge variant={status === 'ready' ? 'default' : 'secondary'} className="text-xs">
                    {status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Debug Info (Development Only) */}
      {import.meta.env.NODE_ENV === 'development' && (
        <Card className="border-dashed border-muted-foreground/30">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs font-mono text-muted-foreground">
              Debug Info (Dev Mode)
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <pre className="text-xs text-muted-foreground bg-muted/30 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify({
                review_id: reviewStatus.review_id,
                status: reviewStatus.status,
                progress: reviewStatus.progress,
                active_agents: reviewStatus.active_agents,
                completed_agents: reviewStatus.completed_agents,
                failed_agents: reviewStatus.failed_agents
              }, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}