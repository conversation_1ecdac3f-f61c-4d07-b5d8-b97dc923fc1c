import React from 'react'
import { Camera, GitPullRequest, Zap, Upload } from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { cn } from '../lib/utils'

export type ReviewMode = 'screenshot' | 'api'

interface ModeSelectorProps {
  currentMode: ReviewMode
  onModeChange: (mode: ReviewMode) => void
  isAuthenticated: boolean
  className?: string
}

export const ModeSelector: React.FC<ModeSelectorProps> = ({
  currentMode,
  onModeChange,
  isAuthenticated,
  className
}) => {
  const modes = [
    {
      id: 'api' as ReviewMode,
      title: 'API Mode',
      subtitle: 'Direct Bitbucket Integration',
      description: 'Browse repositories, select PRs, and view inline comments directly from Bitbucket',
      icon: GitPullRequest,
      color: 'primary',
      features: [
        'Live PR data from Bitbucket',
        'Inline comments display',
        'Real-time comment threading',
        'Direct comment creation',
        'Branch and commit navigation'
      ],
      requirements: 'Requires Bitbucket authentication',
      disabled: !isAuthenticated
    },
    {
      id: 'screenshot' as ReviewMode,
      title: 'Screenshot Mode',
      subtitle: 'Upload Comment Screenshots',
      description: 'Upload screenshots of review comments and get AI-powered responses',
      icon: Camera,
      color: 'secondary',
      features: [
        'Works without authentication',
        'Upload multiple screenshots',
        'AI comment analysis',
        'Batch processing',
        'Export responses to markdown'
      ],
      requirements: 'No authentication required',
      disabled: false
    }
  ]

  const handleModeSelect = (mode: ReviewMode) => {
    if (mode === 'api' && !isAuthenticated) {
      return // Don't allow API mode without authentication
    }
    onModeChange(mode)
  }

  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-foreground mb-2 flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            Choose Review Mode
          </h3>
          <p className="text-sm text-muted-foreground">
            Select how you want to work with code review comments
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {modes.map((mode) => {
            const isSelected = currentMode === mode.id
            const Icon = mode.icon
            
            return (
              <div
                key={mode.id}
                className={cn(
                  'relative border rounded-lg p-4 cursor-pointer transition-all duration-200',
                  isSelected 
                    ? 'border-primary bg-primary/5 shadow-md' 
                    : 'border-border hover:border-primary/50 hover:bg-muted/30',
                  mode.disabled && 'opacity-50 cursor-not-allowed'
                )}
                onClick={() => !mode.disabled && handleModeSelect(mode.id)}
              >
                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <div className="w-3 h-3 bg-primary rounded-full"></div>
                  </div>
                )}

                {/* Header */}
                <div className="flex items-start gap-3 mb-3">
                  <div className={cn(
                    'p-2 rounded-lg shrink-0',
                    mode.color === 'primary' 
                      ? 'bg-primary/10 border border-primary/20' 
                      : 'bg-secondary/10 border border-secondary/20'
                  )}>
                    <Icon className={cn(
                      'h-5 w-5',
                      mode.color === 'primary' ? 'text-primary' : 'text-secondary-foreground'
                    )} />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className={cn(
                        'font-medium',
                        isSelected ? 'text-primary' : 'text-foreground'
                      )}>
                        {mode.title}
                      </h4>
                      {mode.id === 'api' && (
                        <Badge variant="outline" className="text-xs">
                          New
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground font-medium">
                      {mode.subtitle}
                    </p>
                  </div>
                </div>

                {/* Description */}
                <p className="text-sm text-muted-foreground mb-4">
                  {mode.description}
                </p>

                {/* Features */}
                <div className="space-y-2 mb-4">
                  <h5 className="text-xs font-medium text-foreground uppercase tracking-wide">
                    Features
                  </h5>
                  <ul className="space-y-1">
                    {mode.features.map((feature, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                        <div className="w-1 h-1 bg-primary rounded-full shrink-0"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Requirements */}
                <div className={cn(
                  'text-xs p-2 rounded border text-center',
                  mode.disabled 
                    ? 'bg-red-500/10 border-red-500/20 text-red-400'
                    : 'bg-muted/30 border-border text-muted-foreground'
                )}>
                  {mode.requirements}
                </div>

                {/* Action button */}
                <div className="mt-4">
                  <Button
                    variant={isSelected ? 'default' : 'outline'}
                    size="sm"
                    className="w-full"
                    disabled={mode.disabled}
                    onClick={(e) => {
                      e.stopPropagation()
                      !mode.disabled && handleModeSelect(mode.id)
                    }}
                  >
                    {isSelected ? (
                      <>
                        <Zap className="h-3 w-3 mr-1" />
                        Current Mode
                      </>
                    ) : mode.disabled ? (
                      <>
                        <Upload className="h-3 w-3 mr-1" />
                        Authentication Required
                      </>
                    ) : (
                      <>
                        Switch to {mode.title}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )
          })}
        </div>

        {/* Mode-specific info */}
        <div className="mt-6 p-4 bg-muted/20 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-primary rounded-full mt-2 shrink-0"></div>
            <div>
              <h5 className="text-sm font-medium text-foreground mb-1">
                {currentMode === 'api' ? 'API Mode Active' : 'Screenshot Mode Active'}
              </h5>
              <p className="text-xs text-muted-foreground">
                {currentMode === 'api' 
                  ? 'You can browse your Bitbucket repositories and view PR comments inline. Switch to Screenshot Mode if you want to upload comment images instead.'
                  : 'Upload screenshots of review comments to get AI responses. Switch to API Mode if you have Bitbucket authentication for direct integration.'
                }
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}