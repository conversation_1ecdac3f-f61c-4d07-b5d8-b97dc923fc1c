import React from 'react'
import { 
  X, 
  Calendar, 
  User, 
  AlertCircle, 
  CheckCircle2,
  ExternalLink,
  Flag
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { But<PERSON> } from './ui/button'
import { Badge } from './ui/badge'
import { jiraAuthService } from '../services/auth/JiraAuthService'
import './JiraTicketModal.css'

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  acceptance_criteria?: string[]
  related_prs: number[]
}

interface JiraTicketModalProps {
  ticket: AssignedTicket | null
  isOpen: boolean
  onClose: () => void
}

export const JiraTicketModal: React.FC<JiraTicketModalProps> = ({
  ticket,
  isOpen,
  onClose
}) => {
  const [detailedTicket, setDetailedTicket] = React.useState<AssignedTicket | null>(null)
  const [isLoading, setIsLoading] = React.useState(false)

  // Load detailed ticket data when modal opens
  React.useEffect(() => {
    if (isOpen && ticket && ticket.ticket_id) {
      loadTicketDetails(ticket.ticket_id)
    }
  }, [isOpen, ticket])

  const loadTicketDetails = async (ticketId: string) => {
    setIsLoading(true)
    setDetailedTicket(null) // Clear previous data for better UX
    
    try {
      // Check if user is authenticated with Jira
      if (!jiraAuthService.isAuthenticated()) {
        console.warn('Not authenticated with Jira')
        setDetailedTicket(ticket)
        return
      }

      // Use the JiraAuthService to make the request (handles token refresh, headers, etc.)
      const response = await jiraAuthService.getTicketDetails(ticketId)
      
      if (response) {
        // LOG RAW BACKEND RESPONSE
        console.log('🔍 RAW BACKEND RESPONSE for', ticketId, ':', response)
        console.log('🔍 DESCRIPTION FIELD:', response.description)
        console.log('🔍 DESCRIPTION TYPE:', typeof response.description)
        console.log('🔍 DESCRIPTION LENGTH:', response.description?.length || 0)
        console.log('🔍 ACCEPTANCE CRITERIA:', (response as any).acceptanceCriteria || (response as any).acceptance_criteria)
        console.log('🔍 AC COUNT:', (response as any).acceptance_criteria_count || 0)
        
        // The backend already returns the ticket in AssignedTicket format
        setDetailedTicket(response as any as AssignedTicket)
      } else {
        console.error('Ticket not found')
        setDetailedTicket(ticket)
      }
    } catch (error) {
      console.error('Error loading ticket details:', error)
      setDetailedTicket(ticket)
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen || !ticket) return null

  // Show skeleton while loading and no detailed ticket yet
  if (isLoading && !detailedTicket) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
          <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-4">
            <div className="flex-1">
              <div className="h-6 bg-gray-200 rounded animate-pulse mb-2 w-32"></div>
              <div className="h-5 bg-gray-200 rounded animate-pulse w-3/4"></div>
              <div className="flex gap-2 mt-2">
                <div className="h-6 bg-gray-200 rounded animate-pulse w-20"></div>
                <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </CardHeader>
          <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]">
            <div className="space-y-6">
              {/* Skeleton content */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-24"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-28"></div>
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                </div>
              </div>
              <div>
                <div className="h-6 bg-gray-200 rounded animate-pulse w-32 mb-3"></div>
                <div className="bg-muted/30 rounded-lg p-4">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-full"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse w-4/5"></div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const displayTicket = detailedTicket || ticket

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string | undefined) => {
    if (!status) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'in progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'code review':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'done':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const getPriorityColor = (priority: string | undefined) => {
    if (!priority) return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    
    switch (priority.toLowerCase()) {
      case 'high':
      case 'highest':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'low':
      case 'lowest':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const jiraBaseUrl = jiraAuthService.getBaseUrl()
  const jiraUrl = `${jiraBaseUrl}/browse/${displayTicket.ticket_id}`

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-4">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-primary" />
              {displayTicket.ticket_id}
              {isLoading && <span className="text-sm text-muted-foreground">(Loading...)</span>}
            </CardTitle>
            <h2 className="text-lg text-foreground mt-1">{displayTicket.summary}</h2>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={getStatusColor(displayTicket.status)}>
                {displayTicket.status || 'Unknown'}
              </Badge>
              <Badge className={getPriorityColor(displayTicket.priority)}>
                <Flag className="h-3 w-3 mr-1" />
                {displayTicket.priority || 'Unknown'}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(jiraUrl, '_blank')}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Open in Jira
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            
            {/* Ticket Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Assignee:</span>
                  <span>{displayTicket.assignee}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Created:</span>
                  <span>{formatDate(displayTicket.created_date)}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Acceptance Criteria:</span>
                  <span>{displayTicket.acceptance_criteria_count}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Updated:</span>
                  <span>{formatDate(displayTicket.updated_date)}</span>
                </div>
              </div>
              
              <div className="space-y-2">
                {displayTicket.related_prs && displayTicket.related_prs.length > 0 && (
                  <div className="flex items-center gap-2 text-sm">
                    <span className="font-medium">Related PRs:</span>
                    <div className="flex gap-1">
                      {displayTicket.related_prs.map(prId => (
                        <Badge key={prId} variant="outline" className="text-xs">
                          #{prId}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Description
              </h3>
              <div className="bg-muted/30 rounded-lg p-4">
                {displayTicket.description ? (
                  <>
                    {/* DEBUG LOGGING */}
                    {console.log('🎨 RENDERING DESCRIPTION:', {
                      hasHTML: displayTicket.description.includes('<'),
                      length: displayTicket.description.length,
                      preview: displayTicket.description.substring(0, 200),
                      full: displayTicket.description
                    })}
                    <div className="prose prose-sm max-w-none dark:prose-invert">
                      {displayTicket.description.includes('<') ? (
                        // If it contains HTML, render it
                        <div 
                          className="jira-description"
                          dangerouslySetInnerHTML={{ __html: displayTicket.description }}
                        />
                      ) : (
                        // Otherwise render as plain text
                        <pre className="whitespace-pre-wrap text-sm text-foreground font-sans">
                          {displayTicket.description}
                        </pre>
                      )}
                    </div>
                  </>
                ) : (
                  <p className="text-muted-foreground italic">
                    {isLoading ? 'Loading description...' : 'No description provided'}
                  </p>
                )}
              </div>
            </div>

            {/* Acceptance Criteria */}
            {displayTicket.acceptance_criteria_count > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5" />
                  Acceptance Criteria ({displayTicket.acceptance_criteria_count})
                </h3>
                <div className="bg-muted/30 rounded-lg p-4">
                  {displayTicket.acceptance_criteria && displayTicket.acceptance_criteria.length > 0 ? (
                    <div className="space-y-2">
                      {displayTicket.acceptance_criteria.map((criterion, index) => (
                        <div key={index} className="flex items-start gap-2 text-sm">
                          <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-foreground">{criterion}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div>
                      <p className="text-muted-foreground text-sm mb-3">
                        {isLoading ? 'Loading acceptance criteria from Jira...' : 'Acceptance criteria will be loaded from Jira for review compliance checking.'}
                      </p>
                      {!isLoading && (
                        <div className="space-y-2">
                          {Array.from({ length: displayTicket.acceptance_criteria_count }).map((_, index) => (
                            <div key={index} className="flex items-start gap-2 text-sm">
                              <CheckCircle2 className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-muted-foreground">
                                Acceptance Criterion {index + 1} (Could not load from Jira)
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Code Review Context */}
            <div>
              <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Code Review Context
              </h3>
              <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                <p className="text-sm text-foreground mb-2">
                  <strong>This ticket will be used for:</strong>
                </p>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Automated compliance checking against acceptance criteria</li>
                  <li>• Context-aware code review with business requirements</li>
                  <li>• Verification that all acceptance criteria are met in the PR</li>
                  <li>• Enhanced AI review with ticket-specific context</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}