import React from 'react'
import { Camera, GitPullRequest, ArrowLeftRight } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { cn } from '../lib/utils'
import { useCurrentReviewMode, useReviewModeActions } from '../hooks/useReviewMode'
import { useAuthStatus } from '../hooks/useAuth'

interface ModeIndicatorProps {
  variant?: 'full' | 'compact' | 'badge-only'
  showToggle?: boolean
  className?: string
}

export const ModeIndicator: React.FC<ModeIndicatorProps> = ({
  variant = 'compact',
  showToggle = true,
  className
}) => {
  const { currentMode, isTransitioning } = useCurrentReviewMode()
  const { toggleMode } = useReviewModeActions()
  const { isAuthenticated } = useAuthStatus()

  const modeConfig = {
    api: {
      label: 'API Mode',
      shortLabel: 'API',
      icon: GitPullRequest,
      color: 'primary',
      description: 'Bitbucket Integration'
    },
    screenshot: {
      label: 'Screenshot Mode', 
      shortLabel: 'Screenshot',
      icon: Camera,
      color: 'secondary',
      description: 'Upload Images'
    }
  }

  const config = modeConfig[currentMode]
  const Icon = config.icon
  const canToggle = showToggle && isAuthenticated && !isTransitioning

  if (variant === 'badge-only') {
    return (
      <Badge 
        variant={config.color === 'primary' ? 'default' : 'secondary'}
        className={cn(
          'transition-all duration-200',
          isTransitioning && 'animate-pulse',
          className
        )}
      >
        <Icon className="h-3 w-3 mr-1" />
        {config.shortLabel}
      </Badge>
    )
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Badge 
          variant={config.color === 'primary' ? 'default' : 'secondary'}
          className={cn(
            'transition-all duration-200',
            isTransitioning && 'animate-pulse'
          )}
        >
          <Icon className="h-3 w-3 mr-1" />
          {config.shortLabel}
        </Badge>
        
        {canToggle && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMode}
            className="h-6 px-2 text-xs"
            disabled={isTransitioning}
          >
            <ArrowLeftRight className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  // Full variant
  return (
    <div className={cn('flex items-center gap-3', className)}>
      <div className="flex items-center gap-2">
        <div className={cn(
          'p-1.5 rounded border',
          config.color === 'primary' 
            ? 'bg-primary/10 border-primary/20' 
            : 'bg-secondary/10 border-secondary/20'
        )}>
          <Icon className={cn(
            'h-4 w-4',
            config.color === 'primary' ? 'text-primary' : 'text-secondary-foreground'
          )} />
        </div>
        
        <div>
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{config.label}</span>
            <Badge 
              variant="outline" 
              className={cn(
                'text-xs',
                isTransitioning && 'animate-pulse'
              )}
            >
              {isTransitioning ? 'Switching...' : 'Active'}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground">{config.description}</p>
        </div>
      </div>

      {canToggle && (
        <Button
          variant="outline"
          size="sm"
          onClick={toggleMode}
          className="flex items-center gap-2"
          disabled={isTransitioning}
        >
          <ArrowLeftRight className="h-3 w-3" />
          Switch
        </Button>
      )}
    </div>
  )
}

// Quick toggle button component
export const ModeToggleButton: React.FC<{ className?: string }> = ({ className }) => {
  const { currentMode, isTransitioning } = useCurrentReviewMode()
  const { toggleMode } = useReviewModeActions()
  const { isAuthenticated } = useAuthStatus()

  if (!isAuthenticated) return null

  const nextMode = currentMode === 'api' ? 'screenshot' : 'api'
  const Icon = nextMode === 'api' ? GitPullRequest : Camera

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={toggleMode}
      disabled={isTransitioning}
      className={cn('flex items-center gap-2', className)}
    >
      <Icon className="h-4 w-4" />
      Switch to {nextMode === 'api' ? 'API' : 'Screenshot'} Mode
    </Button>
  )
}