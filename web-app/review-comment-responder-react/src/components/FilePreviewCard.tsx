import React, { useState } from 'react'
import { Check, X, Edit, FileCode, Loader2 } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader } from './ui/card'
import type { Comment, PreviewChange, CommentResponse } from '../types'
import { useCommentStore } from '../store/useCommentStore'
import { ApiService } from '../services/api'
import { cn } from '../lib/utils'

interface FilePreviewCardProps {
  comment: Comment
  fileChange: PreviewChange
  // Props for file-specific responses
  hasFileSpecificResponse?: boolean
  fileSpecificResponse?: CommentResponse
  fileSpecificPreviewChanges?: PreviewChange[]
}

export const FilePreviewCard: React.FC<FilePreviewCardProps> = ({ 
  comment, 
  fileChange,
  hasFileSpecificResponse = false,
  fileSpecificResponse,
  fileSpecificPreviewChanges
}) => {
  const { updateComment, updateFileSpecificResponse } = useCommentStore()
  const [isApplying, setIsApplying] = useState(false)
  const [fileModification, setFileModification] = useState('')
  const [showModificationInput, setShowModificationInput] = useState(false)
  const [isModifying, setIsModifying] = useState(false)

  // Use file-specific data if available, otherwise use original data
  const currentResponse = hasFileSpecificResponse ? fileSpecificResponse : null
  const currentPreviewChanges = hasFileSpecificResponse ? fileSpecificPreviewChanges : [fileChange]
  const currentChanges = currentPreviewChanges?.[0]?.changes || fileChange.changes

  const status = comment.fileStatus?.[fileChange.file] || 'pending'

  const acceptFileChange = async () => {
    if (!comment.session_id) return

    const newFileStatus = { ...comment.fileStatus, [fileChange.file]: 'accepted' as const }
    
    try {
      setIsApplying(true)
      
      // Apply this specific file change immediately
      await ApiService.executeApprovedChanges(comment.session_id, [fileChange])
      
      updateComment(comment.id, { fileStatus: newFileStatus })
      
      console.log(`Changes applied to ${fileChange.file}`)
    } catch (error) {
      console.error('Error applying file change:', error)
    } finally {
      setIsApplying(false)
    }
  }

  const rejectFileChange = () => {
    const newFileStatus = { ...comment.fileStatus, [fileChange.file]: 'rejected' as const }
    updateComment(comment.id, { fileStatus: newFileStatus })
    console.log(`Rejected changes for ${fileChange.file}`)
  }

  const submitFileModification = async () => {
    const modification = fileModification.trim()
    if (!modification || !comment.session_id) return

    try {
      setIsModifying(true)
      
      // Use the new file-specific API with full context
      const result = await ApiService.modifyFileSpecific(
        comment.session_id,
        fileChange.file,
        modification,
        currentChanges,
        fileChange.reason
      )
      
      if (result.success && result.response) {
        // Extract only the file-specific changes
        const fileSpecificData = ApiService.extractFileSpecificChanges(
          result.response, 
          fileChange.file
        )
        
        if (fileSpecificData) {
          // Update only this file's response, keep others intact
          updateFileSpecificResponse(
            comment.id,
            fileChange.file,
            fileSpecificData.response,
            fileSpecificData.previewChanges
          )
        }
        
        // Clear the modification input
        setFileModification('')
        setShowModificationInput(false)
        
        console.log(`File ${fileChange.file} updated with file-specific response`)
      }
    } catch (error) {
      console.error(`Error modifying file ${fileChange.file}:`, error)
    } finally {
      setIsModifying(false)
    }
  }

  return (
    <Card className="mb-4 bg-gray-900 text-gray-100">
      <CardHeader className="bg-gray-800">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FileCode className="h-4 w-4" />
            <span className="font-mono text-sm">{fileChange.file}</span>
            <span className={cn(
              'px-2 py-1 rounded text-xs font-semibold uppercase',
              status === 'pending' ? 'bg-yellow-500 text-black' :
              status === 'accepted' ? 'bg-green-500 text-white' :
              'bg-red-500 text-white'
            )}>
              {status}
            </span>
            {fileChange.reason && (
              <span className="text-xs text-gray-400">// {fileChange.reason}</span>
            )}
            {hasFileSpecificResponse && (
              <span className="px-2 py-1 rounded text-xs font-semibold bg-blue-500 text-white">
                UPDATED
              </span>
            )}
          </div>
          
          {status === 'pending' && (
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                className="bg-green-600 text-white hover:bg-green-700"
                onClick={acceptFileChange}
                disabled={isApplying || isModifying}
              >
                <Check className="h-4 w-4" />
                Accept
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="bg-red-600 text-white hover:bg-red-700"
                onClick={rejectFileChange}
                disabled={isApplying || isModifying}
              >
                <X className="h-4 w-4" />
                Reject
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="bg-blue-600 text-white hover:bg-blue-700"
                onClick={() => setShowModificationInput(!showModificationInput)}
                disabled={isApplying || isModifying}
              >
                <Edit className="h-4 w-4" />
                Modify
              </Button>
            </div>
          )}
          
          {/* Loading state for individual file modification */}
          {isModifying && (
            <div className="flex items-center gap-2 text-blue-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Modifying {fileChange.file}...</span>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {/* File-specific response display */}
        {hasFileSpecificResponse && currentResponse && (
          <div className="p-4 bg-blue-900/30 border-b border-blue-500">
            <div className="text-sm font-medium text-blue-300 mb-2">
              File-specific response for {fileChange.file}:
            </div>
            <div className="text-sm text-blue-100">
              {currentResponse.text || currentResponse.response}
            </div>
          </div>
        )}

        <div className="max-h-96 overflow-y-auto">
          {currentChanges.map((change, changeIndex) => (
            <div key={changeIndex}>
              {change.explanation && (
                <div className="px-4 py-2 text-green-400 italic text-sm">
                  // {change.explanation}
                </div>
              )}
              
              {/* Context line */}
              <div className="px-4 py-1 text-gray-500 text-sm">
                // Context: Lines {change.line_start - 3} to {change.line_start - 1}
              </div>
              
              {/* Removed lines */}
              {change.current_content && change.current_content.split('\n').map((line, i) => (
                <div key={`remove-${i}`} className="flex bg-red-900/30">
                  <span className="w-12 px-2 py-1 text-right text-gray-500 bg-gray-800 text-xs">
                    {change.line_start + i}
                  </span>
                  <span className="flex-1 px-4 py-1 font-mono text-sm">
                    - {line}
                  </span>
                </div>
              ))}
              
              {/* Added lines */}
              {change.suggested_content && change.suggested_content.split('\n').map((line, i) => (
                <div key={`add-${i}`} className="flex bg-green-900/30">
                  <span className="w-12 px-2 py-1 text-right text-gray-500 bg-gray-800 text-xs">
                    {change.line_start + i}
                  </span>
                  <span className="flex-1 px-4 py-1 font-mono text-sm">
                    + {line}
                  </span>
                </div>
              ))}
              
              {/* Context line */}
              <div className="px-4 py-1 text-gray-500 text-sm">
                // Context: Lines {change.line_end + 1} to {change.line_end + 3}
              </div>
            </div>
          ))}
        </div>
        
        {/* File-specific modification input */}
        {showModificationInput && (
          <div className="p-4 bg-blue-50 border-t border-blue-200">
            <div className="mb-2">
              <label className="text-sm font-medium text-blue-800">
                Modify instructions for {fileChange.file}:
              </label>
            </div>
            <textarea
              className="w-full p-3 border border-blue-300 rounded-lg font-mono text-sm resize-vertical bg-white text-gray-900 placeholder-gray-500"
              rows={3}
              placeholder={`Tell Claude how to modify ${fileChange.file} specifically...`}
              value={fileModification}
              onChange={(e) => setFileModification(e.target.value)}
            />
            <div className="flex gap-2 mt-2">
              <Button 
                size="sm"
                onClick={submitFileModification}
                disabled={!fileModification.trim() || isModifying}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isModifying ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-1" />
                    Processing...
                  </>
                ) : (
                  'Send File Modification'
                )}
              </Button>
              <Button 
                size="sm"
                variant="outline"
                onClick={() => setShowModificationInput(false)}
                disabled={isModifying}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}