import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { ChevronDown, ChevronRight, Bug, AlertTriangle, Eye } from 'lucide-react';

interface DataFlowDebuggerProps {
  backendData: any;
  frontendData: any;
  transformationSteps?: Array<{
    step: string;
    input: any;
    output: any;
    transformation: string;
  }>;
  className?: string;
}

export const DataFlowDebugger: React.FC<DataFlowDebuggerProps> = ({
  backendData,
  frontendData,
  transformationSteps = [],
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState<'backend' | 'frontend' | 'diff' | 'steps'>('diff');
  
  const findDataDifferences = (backend: any, frontend: any): Array<{
    path: string;
    backendValue: any;
    frontendValue: any;
    type: 'missing' | 'added' | 'modified' | 'type_mismatch';
  }> => {
    const differences: Array<{
      path: string;
      backendValue: any;
      frontendValue: any;
      type: 'missing' | 'added' | 'modified' | 'type_mismatch';
    }> = [];
    
    const compareObjects = (obj1: any, obj2: any, path: string = '') => {
      if (obj1 === null || obj1 === undefined) {
        if (obj2 !== null && obj2 !== undefined) {
          differences.push({
            path,
            backendValue: obj1,
            frontendValue: obj2,
            type: 'added'
          });
        }
        return;
      }
      
      if (obj2 === null || obj2 === undefined) {
        differences.push({
          path,
          backendValue: obj1,
          frontendValue: obj2,
          type: 'missing'
        });
        return;
      }
      
      if (typeof obj1 !== typeof obj2) {
        differences.push({
          path,
          backendValue: obj1,
          frontendValue: obj2,
          type: 'type_mismatch'
        });
        return;
      }
      
      if (typeof obj1 === 'object' && !Array.isArray(obj1)) {
        const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
        allKeys.forEach(key => {
          compareObjects(obj1[key], obj2[key], path ? `${path}.${key}` : key);
        });
      } else if (Array.isArray(obj1) && Array.isArray(obj2)) {
        const maxLength = Math.max(obj1.length, obj2.length);
        for (let i = 0; i < maxLength; i++) {
          compareObjects(obj1[i], obj2[i], `${path}[${i}]`);
        }
      } else if (obj1 !== obj2) {
        differences.push({
          path,
          backendValue: obj1,
          frontendValue: obj2,
          type: 'modified'
        });
      }
    };
    
    compareObjects(backend, frontend);
    return differences;
  };
  
  const differences = findDataDifferences(backendData, frontendData);
  const hasCriticalIssues = differences.some(diff => 
    diff.type === 'type_mismatch' || 
    (diff.type === 'missing' && diff.path.includes('structured_findings'))
  );
  
  if (!backendData && !frontendData) return null;
  
  return (
    <Card className={`border-blue-500/50 bg-blue-500/5 ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-blue-400">
            <Bug className="h-5 w-5" />
            Data Flow Debugger
            {hasCriticalIssues && (
              <AlertTriangle className="h-4 w-4 text-red-400 ml-2" />
            )}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="border-blue-500 text-blue-400 hover:bg-blue-500/10"
          >
            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            {isExpanded ? 'Hide' : 'Show'} Debugging
          </Button>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent>
          <div className="space-y-4">
            {/* Status Summary */}
            <div className="bg-blue-950/50 border border-blue-800 rounded-lg p-3">
              <h4 className="font-medium text-blue-300 mb-2">Analysis Summary</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-blue-400">Backend Data:</span>
                  <div className="text-blue-200">
                    {backendData ? 'Available' : 'Missing'} 
                    {backendData?.structured_findings && (
                      <span className="ml-2">
                        ({Object.keys(backendData.structured_findings).length} categories)
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-blue-400">Frontend Data:</span>
                  <div className="text-blue-200">
                    {frontendData ? 'Available' : 'Missing'}
                    {frontendData?.structured_findings && (
                      <span className="ml-2">
                        ({Object.keys(frontendData.structured_findings).length} categories)
                      </span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-blue-400">Differences Found:</span>
                  <div className={`${differences.length > 0 ? 'text-orange-300' : 'text-green-300'}`}>
                    {differences.length} issues
                  </div>
                </div>
                <div>
                  <span className="text-blue-400">Critical Issues:</span>
                  <div className={`${hasCriticalIssues ? 'text-red-300' : 'text-green-300'}`}>
                    {hasCriticalIssues ? 'Yes' : 'None'}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Tab Navigation */}
            <div className="flex items-center gap-1 bg-blue-950/30 rounded-lg p-1">
              {[
                { id: 'diff', label: 'Differences' },
                { id: 'backend', label: 'Backend Data' },
                { id: 'frontend', label: 'Frontend Data' },
                { id: 'steps', label: 'Transformation Steps' }
              ].map(tab => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab(tab.id as any)}
                  className="flex-1"
                >
                  {tab.label}
                  {tab.id === 'diff' && differences.length > 0 && (
                    <span className="ml-2 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {differences.length}
                    </span>
                  )}
                </Button>
              ))}
            </div>
            
            {/* Tab Content */}
            <div className="bg-gray-950 border border-gray-800 rounded-lg p-4 max-h-96 overflow-auto">
              {activeTab === 'diff' && (
                <div className="space-y-3">
                  <h4 className="font-medium text-white mb-3">Data Differences Analysis</h4>
                  {differences.length === 0 ? (
                    <p className="text-green-400">✅ No differences found - data is consistent</p>
                  ) : (
                    differences.map((diff, index) => (
                      <div key={index} className="border-l-2 border-orange-500 pl-3 py-2">
                        <div className="flex items-center gap-2 mb-1">
                          <span className={`px-2 py-1 text-xs rounded font-mono ${
                            diff.type === 'missing' ? 'bg-red-900 text-red-200' :
                            diff.type === 'added' ? 'bg-green-900 text-green-200' :
                            diff.type === 'type_mismatch' ? 'bg-purple-900 text-purple-200' :
                            'bg-orange-900 text-orange-200'
                          }`}>
                            {diff.type.toUpperCase()}
                          </span>
                          <code className="text-gray-300 text-sm">{diff.path}</code>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-400">Backend:</span>
                            <pre className="text-gray-200 mt-1 bg-gray-800 p-2 rounded">
                              {JSON.stringify(diff.backendValue, null, 2)}
                            </pre>
                          </div>
                          <div>
                            <span className="text-gray-400">Frontend:</span>
                            <pre className="text-gray-200 mt-1 bg-gray-800 p-2 rounded">
                              {JSON.stringify(diff.frontendValue, null, 2)}
                            </pre>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
              
              {activeTab === 'backend' && (
                <div>
                  <h4 className="font-medium text-white mb-3">Backend Raw Data</h4>
                  <pre className="text-gray-200 text-xs whitespace-pre-wrap">
                    {JSON.stringify(backendData, null, 2)}
                  </pre>
                </div>
              )}
              
              {activeTab === 'frontend' && (
                <div>
                  <h4 className="font-medium text-white mb-3">Frontend Processed Data</h4>
                  <pre className="text-gray-200 text-xs whitespace-pre-wrap">
                    {JSON.stringify(frontendData, null, 2)}
                  </pre>
                </div>
              )}
              
              {activeTab === 'steps' && (
                <div className="space-y-3">
                  <h4 className="font-medium text-white mb-3">Transformation Steps</h4>
                  {transformationSteps.length === 0 ? (
                    <p className="text-gray-400">No transformation steps recorded</p>
                  ) : (
                    transformationSteps.map((step, index) => (
                      <div key={index} className="border border-gray-700 rounded p-3">
                        <h5 className="font-medium text-blue-300 mb-2">{step.step}</h5>
                        <p className="text-gray-400 text-sm mb-2">{step.transformation}</p>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-gray-400">Input:</span>
                            <pre className="text-gray-200 mt-1 bg-gray-800 p-2 rounded max-h-32 overflow-auto">
                              {JSON.stringify(step.input, null, 2)}
                            </pre>
                          </div>
                          <div>
                            <span className="text-gray-400">Output:</span>
                            <pre className="text-gray-200 mt-1 bg-gray-800 p-2 rounded max-h-32 overflow-auto">
                              {JSON.stringify(step.output, null, 2)}
                            </pre>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-2 pt-2 border-t border-blue-800">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('🐛 MANUAL DEBUG - Backend Data:', backendData);
                  console.log('🐛 MANUAL DEBUG - Frontend Data:', frontendData);
                  console.log('🐛 MANUAL DEBUG - Differences:', differences);
                }}
                className="border-blue-500 text-blue-400 hover:bg-blue-500/10"
              >
                <Eye className="h-4 w-4 mr-2" />
                Log to Console
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const debugData = {
                    backend: backendData,
                    frontend: frontendData,
                    differences,
                    timestamp: new Date().toISOString()
                  };
                  const blob = new Blob([JSON.stringify(debugData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `data-flow-debug-${Date.now()}.json`;
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  URL.revokeObjectURL(url);
                }}
                className="border-blue-500 text-blue-400 hover:bg-blue-500/10"
              >
                Export Debug Data
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default DataFlowDebugger;