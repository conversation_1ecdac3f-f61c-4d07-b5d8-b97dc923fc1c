import React, { useState, useEffect } from 'react'
import { Folder, GitBranch, Home, ChevronRight } from 'lucide-react'
import { Button } from '../ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

interface DirectoryItem {
  name: string
  path: string
  isDirectory: boolean
  isGitRepo?: boolean
  children?: DirectoryItem[]
  expanded?: boolean
}

interface DirectoryBrowserProps {
  onMasterRepoSelected: (masterRepoPath: string) => void
  className?: string
}

export const DirectoryBrowser: React.FC<DirectoryBrowserProps> = ({ 
  onMasterRepoSelected,
  className 
}) => {
  const [currentPath, setCurrentPath] = useState<string>('/Users/<USER>')
  const [directories, setDirectories] = useState<DirectoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedMasterRepo, setSelectedMasterRepo] = useState<string | null>(null)

  // Load directories for current path
  const loadDirectories = async (path: string) => {
    setLoading(true)
    try {
      // Try to get directories from backend
      try {
        const response = await fetch('http://localhost:5002/api/worktree/browse-directories', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          mode: 'cors',
          body: JSON.stringify({ path })
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setDirectories(data.directories)
            return
          }
        }
      } catch (backendError) {
        console.warn('Backend not available for directory browsing:', backendError)
      }

      // Fallback to mock directories for development
      const mockDirectories: DirectoryItem[] = [
        {
          name: 'rma',
          path: '/Users/<USER>/rma',
          isDirectory: true,
          isGitRepo: true
        },
        {
          name: '.devtools',
          path: '/Users/<USER>/.devtools',
          isDirectory: true,
          isGitRepo: true
        },
        {
          name: 'projects',
          path: '/Users/<USER>/projects',
          isDirectory: true
        },
        {
          name: 'dev',
          path: '/Users/<USER>/dev',
          isDirectory: true
        },
        {
          name: 'Documents',
          path: '/Users/<USER>/Documents',
          isDirectory: true
        }
      ]
      
      setDirectories(mockDirectories)
    } catch (error) {
      console.error('Failed to load directories:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadDirectories(currentPath)
  }, [currentPath])

  const handleDirectoryClick = (directory: DirectoryItem) => {
    if (directory.isGitRepo) {
      setSelectedMasterRepo(directory.path)
    } else {
      setCurrentPath(directory.path)
    }
  }

  const handleSelectMasterRepo = () => {
    if (selectedMasterRepo) {
      onMasterRepoSelected(selectedMasterRepo)
    }
  }

  const goToParent = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/'
    setCurrentPath(parentPath)
  }

  const goToHome = () => {
    setCurrentPath('/Users/<USER>')
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Folder className="h-5 w-5" />
          Master Repository auswählen
        </CardTitle>
        <div className="text-sm text-muted-foreground">
          Wähle den Ordner deines Master Branch Repository aus. 
          Worktrees werden in <code>../worktrees/</code> erstellt.
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Path Navigation */}
        <div className="flex items-center gap-2 p-2 bg-muted rounded-md">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={goToHome}
            className="h-8 w-8 p-0"
          >
            <Home className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={goToParent}
            disabled={currentPath === '/'}
            className="h-8"
          >
            ..
          </Button>
          <div className="font-mono text-sm truncate">
            {currentPath}
          </div>
        </div>

        {/* Directory List */}
        <div className="space-y-1 max-h-64 overflow-y-auto">
          {loading ? (
            <div className="text-center py-4 text-muted-foreground">
              Lade Verzeichnisse...
            </div>
          ) : (
            directories.map((dir) => (
              <div
                key={dir.path}
                className={`flex items-center gap-2 p-2 rounded-md cursor-pointer hover:bg-muted ${
                  selectedMasterRepo === dir.path ? 'bg-primary/10 border border-primary/20' : ''
                }`}
                onClick={() => handleDirectoryClick(dir)}
              >
                <div className="flex items-center gap-2 flex-1">
                  {dir.isGitRepo ? (
                    <GitBranch className="h-4 w-4 text-green-600" />
                  ) : (
                    <Folder className="h-4 w-4 text-blue-600" />
                  )}
                  <span className="font-medium">{dir.name}</span>
                  {dir.isGitRepo && (
                    <Badge variant="secondary" className="text-xs">
                      Git Repository
                    </Badge>
                  )}
                </div>
                {!dir.isGitRepo && (
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
            ))
          )}
        </div>

        {/* Selected Repository Info */}
        {selectedMasterRepo && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="text-sm font-medium text-green-800">
              Ausgewählter Master Repository:
            </div>
            <div className="font-mono text-sm text-green-700">
              {selectedMasterRepo}
            </div>
            <div className="text-xs text-green-600 mt-1">
              Worktrees werden erstellt in: <code>{selectedMasterRepo}/../worktrees/</code>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button 
          onClick={handleSelectMasterRepo}
          disabled={!selectedMasterRepo}
          className="w-full"
        >
          {selectedMasterRepo ? 'Repository auswählen' : 'Wähle ein Git Repository aus'}
        </Button>
      </CardContent>
    </Card>
  )
}