import React from 'react'
import { GitBranch, Circle, Settings, AlertCircle } from 'lucide-react'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent } from '../ui/card'
import { useWorktreeStatus } from '../../contexts/WorktreeStatusContext'
import { Link } from 'react-router-dom'

interface WorktreeStatusIndicatorProps {
  showFullCard?: boolean
  className?: string
}

export const WorktreeStatusIndicator: React.FC<WorktreeStatusIndicatorProps> = ({ 
  showFullCard = false,
  className 
}) => {
  const { status } = useWorktreeStatus()

  const getStatusColor = () => {
    if (status.loading) return 'text-blue-500'
    if (status.isConfigured && status.isValid) return 'text-green-500'
    return 'text-red-500'
  }

  const getStatusText = () => {
    if (status.loading) return 'Checking...'
    if (status.isConfigured && status.isValid) return 'Ready'
    if (status.error) return 'Error'
    return 'Not Configured'
  }

  const getStatusBadgeVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    if (status.loading) return 'secondary'
    if (status.isConfigured && status.isValid) return 'default'
    return 'destructive'
  }

  // Compact indicator for navigation/header
  if (!showFullCard) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center gap-1">
          <Circle className={`h-2 w-2 fill-current ${getStatusColor()}`} />
          <GitBranch className="h-4 w-4 text-muted-foreground" />
        </div>
        <span className="text-sm text-muted-foreground">
          Git Worktree
        </span>
        <Badge variant={getStatusBadgeVariant()} className="text-xs">
          {getStatusText()}
        </Badge>
      </div>
    )
  }

  // Full card for detailed view
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Circle className={`h-3 w-3 fill-current ${getStatusColor()}`} />
              <h3 className="font-medium">Git Worktree Configuration</h3>
            </div>
            <Badge variant={getStatusBadgeVariant()}>
              {getStatusText()}
            </Badge>
          </div>

          {/* Status Details */}
          {status.isConfigured && status.isValid ? (
            <div className="space-y-2">
              <div className="text-sm text-muted-foreground">
                Master Repository:
              </div>
              <div className="font-mono text-sm bg-muted px-2 py-1 rounded">
                {status.masterRepoPath}
              </div>
              <div className="text-xs text-muted-foreground">
                ✅ System ready for code reviews
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-amber-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">
                  Git Worktree not configured
                </span>
              </div>
              <div className="text-xs text-muted-foreground">
                Configure your master repository to enable code reviews
              </div>
              {status.error && (
                <div className="text-xs text-red-600">
                  Error: {status.error}
                </div>
              )}
            </div>
          )}

          {/* Action Button */}
          {(!status.isConfigured || !status.isValid) && (
            <Link to="/settings?tab=git">
              <Button size="sm" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Configure Git Worktree
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  )
}