import React, { useState } from 'react';
import { ChevronDown, ChevronRight, AlertTriangle, Clock, TrendingDown } from 'lucide-react';
import type { AIAnalysisMetadata, QualityScoreAnalysis, DuplicationAnalysis, EffortEstimationAnalysis } from '../types/fallback-monitoring';

interface DetailedFallbackReportProps {
  aiMetadata?: AIAnalysisMetadata;
  effortAnalysis?: EffortEstimationAnalysis;
  className?: string;
}

export const DetailedFallbackReport: React.FC<DetailedFallbackReportProps> = ({
  aiMetadata,
  effortAnalysis,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  if (!aiMetadata?.has_fallback_calculations && effortAnalysis?.claude_analysis !== false) {
    return null;
  }

  const renderQualityScoreDetails = (analysis: QualityScoreAnalysis) => (
    <div className="bg-red-50 border border-red-200 rounded p-3">
      <h4 className="font-medium text-red-800 flex items-center">
        <TrendingDown className="w-4 h-4 mr-2" />
        Quality Score Fallback Details
      </h4>
      <div className="mt-2 space-y-2 text-sm text-red-700">
        <div className="flex justify-between">
          <span>Berechneter Score:</span>
          <span className="font-mono">{analysis.quality_score}/10</span>
        </div>
        <div className="flex justify-between">
          <span>Methode:</span>
          <span className="font-mono text-xs bg-red-100 px-2 py-1 rounded">
            {analysis.calculation_method}
          </span>
        </div>
        {analysis.calculation_details && (
          <div className="bg-red-100 p-2 rounded text-xs">
            <div className="font-medium mb-1">Fallback-Formel:</div>
            <code className="text-red-800">{analysis.calculation_details.formula}</code>
            <div className="mt-2 grid grid-cols-3 gap-2">
              <div>High: {analysis.calculation_details.high_issues}</div>
              <div>Medium: {analysis.calculation_details.medium_issues}</div>
              <div>Low: {analysis.calculation_details.low_issues}</div>
            </div>
          </div>
        )}
        <div className="flex items-center text-xs text-red-600">
          <Clock className="w-3 h-3 mr-1" />
          {analysis.fallback_timestamp && new Date(analysis.fallback_timestamp).toLocaleString()}
        </div>
      </div>
    </div>
  );

  const renderDuplicationDetails = (analysis: DuplicationAnalysis) => (
    <div className="bg-orange-50 border border-orange-200 rounded p-3">
      <h4 className="font-medium text-orange-800 flex items-center">
        <AlertTriangle className="w-4 h-4 mr-2" />
        Code Duplication Fallback Details
      </h4>
      <div className="mt-2 space-y-2 text-sm text-orange-700">
        <div className="flex justify-between">
          <span>Duplikation:</span>
          <span className="font-mono">{analysis.duplication_percentage}%</span>
        </div>
        <div className="flex justify-between">
          <span>Methode:</span>
          <span className="font-mono text-xs bg-orange-100 px-2 py-1 rounded">
            {analysis.calculation_method}
          </span>
        </div>
        {analysis.detected_patterns && analysis.detected_patterns.length > 0 && (
          <div className="bg-orange-100 p-2 rounded text-xs">
            <div className="font-medium mb-1">Erkannte Pattern:</div>
            <ul className="list-disc list-inside space-y-1">
              {analysis.detected_patterns.map((pattern, idx) => (
                <li key={idx}><code className="text-orange-800">{pattern}</code></li>
              ))}
            </ul>
          </div>
        )}
        <div className="text-xs">{analysis.analysis_summary}</div>
        <div className="flex items-center text-xs text-orange-600">
          <Clock className="w-3 h-3 mr-1" />
          {analysis.fallback_timestamp && new Date(analysis.fallback_timestamp).toLocaleString()}
        </div>
      </div>
    </div>
  );

  const renderEffortEstimationDetails = (analysis: EffortEstimationAnalysis) => (
    <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
      <h4 className="font-medium text-yellow-800 flex items-center">
        <Clock className="w-4 h-4 mr-2" />
        Effort Estimation Fallback Details
      </h4>
      <div className="mt-2 space-y-2 text-sm text-yellow-700">
        <div className="flex justify-between">
          <span>Geschätzter Aufwand:</span>
          <span className="font-mono">{analysis.estimated_effort}</span>
        </div>
        <div className="flex justify-between">
          <span>Stunden:</span>
          <span className="font-mono">{analysis.estimated_hours}h</span>
        </div>
        <div className="flex justify-between">
          <span>Confidence:</span>
          <span className="font-mono text-xs bg-yellow-100 px-2 py-1 rounded">
            {analysis.confidence_level} ({analysis.confidence_range})
          </span>
        </div>
        {analysis.calculation_breakdown && (
          <div className="bg-yellow-100 p-2 rounded text-xs space-y-1">
            <div className="font-medium">Fallback-Berechnung:</div>
            <div>• {analysis.calculation_breakdown.base_estimation}</div>
            <div>• {analysis.calculation_breakdown.high_severity_impact}</div>
            <div>• {analysis.calculation_breakdown.complexity_adjustment}</div>
            <div>• {analysis.calculation_breakdown.final_result}</div>
          </div>
        )}
        <div className="flex items-center text-xs text-yellow-600">
          <Clock className="w-3 h-3 mr-1" />
          {analysis.fallback_timestamp && new Date(analysis.fallback_timestamp).toLocaleString()}
        </div>
      </div>
    </div>
  );

  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-100"
      >
        <div className="flex items-center">
          {isExpanded ? <ChevronDown className="w-4 h-4 mr-2" /> : <ChevronRight className="w-4 h-4 mr-2" />}
          <span className="font-medium text-gray-800">
            🔍 Detaillierte Fallback-Analyse anzeigen
          </span>
        </div>
        <span className="text-xs bg-gray-200 px-2 py-1 rounded">
          Entwickler-Tools
        </span>
      </button>
      
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4">
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded border border-blue-200">
            <div className="font-medium text-blue-800 mb-2">📊 Fallback-Monitoring</div>
            <p>Diese Analyse zeigt detaillierte Informationen über Berechnungen, die ohne Claude Code AI durchgeführt wurden. 
            Dies hilft bei der Diagnose von CLI-Problemen und der Verbesserung der Robustheit des Systems.</p>
          </div>

          {aiMetadata?.quality_score_analysis?.claude_analysis === false && (
            renderQualityScoreDetails(aiMetadata.quality_score_analysis)
          )}

          {aiMetadata?.duplication_analysis?.claude_analysis === false && (
            renderDuplicationDetails(aiMetadata.duplication_analysis)
          )}

          {effortAnalysis?.claude_analysis === false && (
            renderEffortEstimationDetails(effortAnalysis)
          )}

          <div className="text-xs text-gray-500 bg-gray-100 p-2 rounded">
            <strong>Troubleshooting-Tipps:</strong>
            <ul className="mt-1 list-disc list-inside space-y-1">
              <li>Überprüfen Sie die Claude Code CLI Installation: <code>claude --version</code></li>
              <li>Prüfen Sie die Netzwerkverbindung zu Anthropic's API</li>
              <li>Kontrollieren Sie die API-Key Konfiguration</li>
              <li>Bei wiederholten Timeouts: Erhöhen Sie die Timeout-Werte</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default DetailedFallbackReport;