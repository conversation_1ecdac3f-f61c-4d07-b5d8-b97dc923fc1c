/**
 * Service Health Indicator Component
 * Monitors and displays health status of Multi-Agent and Legacy services
 */

import React, { useState, useEffect, useCallback } from 'react'
import { 
  HeartOff,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Server,
  Activity
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { ServiceEndpoints, ServiceAvailabilityChecker } from '../config/serviceEndpoints'
import { MultiAgentReviewService } from '../services/multiAgent/MultiAgentReviewService'
import type { ServiceHealthResponse } from '../types/multi-agent'

export interface ServiceHealth {
  service_name: string
  status: 'healthy' | 'unhealthy' | 'checking' | 'unknown'
  response_time?: number
  last_check: Date
  error_message?: string
  uptime?: number
  version?: string
  active_reviews?: number
}

interface ServiceHealthIndicatorProps {
  showDetails?: boolean
  autoRefresh?: boolean
  refreshInterval?: number
  onHealthChange?: (services: Record<string, ServiceHealth>) => void
  compact?: boolean
  className?: string
}

export const ServiceHealthIndicator: React.FC<ServiceHealthIndicatorProps> = ({
  showDetails = true,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  onHealthChange,
  compact = false,
  className = ""
}) => {
  const [services, setServices] = useState<Record<string, ServiceHealth>>({
    'multi-agent': {
      service_name: 'Multi-Agent Service',
      status: 'unknown',
      last_check: new Date()
    },
    'legacy-code-reviewer': {
      service_name: 'Legacy Code Reviewer',
      status: 'unknown', 
      last_check: new Date()
    },
    'legacy-claude': {
      service_name: 'Legacy Claude Service',
      status: 'unknown',
      last_check: new Date()
    }
  })

  const [isChecking, setIsChecking] = useState(false)
  const [lastFullCheck, setLastFullCheck] = useState<Date | null>(null)

  // Service checker instance
  const [serviceChecker] = useState(() => new ServiceAvailabilityChecker())
  const [_multiAgentService] = useState(() => new MultiAgentReviewService())

  // Check Multi-Agent service health
  const checkMultiAgentHealth = useCallback(async (): Promise<ServiceHealth> => {
    const startTime = Date.now()
    
    try {
      // Try to get service health from the Multi-Agent API
      // Create an AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)
      
      const healthResponse = await fetch(`${ServiceEndpoints.MULTI_AGENT.BASE_URL}/health`, {
        method: 'GET',
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      const responseTime = Date.now() - startTime
      
      if (healthResponse.ok) {
        const healthData: ServiceHealthResponse = await healthResponse.json()
        
        return {
          service_name: 'Multi-Agent Service',
          status: healthData.status === 'healthy' ? 'healthy' : 'unhealthy',
          response_time: responseTime,
          last_check: new Date(),
          uptime: healthData.service_info?.uptime,
          version: healthData.service_info?.version,
          active_reviews: healthData.service_info?.active_reviews
        }
      } else {
        return {
          service_name: 'Multi-Agent Service',
          status: 'unhealthy',
          response_time: responseTime,
          last_check: new Date(),
          error_message: `HTTP ${healthResponse.status}`
        }
      }
    } catch (error) {
      return {
        service_name: 'Multi-Agent Service',
        status: 'unhealthy',
        response_time: Date.now() - startTime,
        last_check: new Date(),
        error_message: error instanceof Error ? error.message : 'Network error'
      }
    }
  }, [])

  // Check legacy service health
  const checkLegacyServiceHealth = useCallback(async (serviceName: string): Promise<ServiceHealth> => {
    const startTime = Date.now()
    
    try {
      const isHealthy = await serviceChecker.checkServiceHealth(serviceName as any)
      const responseTime = Date.now() - startTime
      
      return {
        service_name: serviceName === 'LEGACY_CODE_REVIEWER' ? 'Legacy Code Reviewer' : 'Legacy Claude Service',
        status: isHealthy ? 'healthy' : 'unhealthy',
        response_time: responseTime,
        last_check: new Date(),
        error_message: isHealthy ? undefined : 'Service unreachable'
      }
    } catch (error) {
      return {
        service_name: serviceName === 'LEGACY_CODE_REVIEWER' ? 'Legacy Code Reviewer' : 'Legacy Claude Service',
        status: 'unhealthy',
        response_time: Date.now() - startTime,
        last_check: new Date(),
        error_message: error instanceof Error ? error.message : 'Check failed'
      }
    }
  }, [serviceChecker])

  // Perform health check for all services
  const checkAllServices = useCallback(async () => {
    setIsChecking(true)
    
    try {
      const [
        multiAgentHealth,
        codeReviewerHealth,
        claudeHealth
      ] = await Promise.all([
        checkMultiAgentHealth(),
        checkLegacyServiceHealth('LEGACY_CODE_REVIEWER'),
        checkLegacyServiceHealth('LEGACY_CLAUDE')
      ])

      const newServices = {
        'multi-agent': multiAgentHealth,
        'legacy-code-reviewer': codeReviewerHealth,
        'legacy-claude': claudeHealth
      }

      setServices(newServices)
      setLastFullCheck(new Date())
      onHealthChange?.(newServices)
      
    } catch (error) {
      console.error('Failed to check service health:', error)
    } finally {
      setIsChecking(false)
    }
  }, [checkMultiAgentHealth, checkLegacyServiceHealth, onHealthChange])

  // Auto-refresh effect
  useEffect(() => {
    // Initial check
    checkAllServices()

    if (!autoRefresh) return

    const interval = setInterval(checkAllServices, refreshInterval)
    return () => clearInterval(interval)
  }, [checkAllServices, autoRefresh, refreshInterval])

  // Get overall health status
  const getOverallStatus = () => {
    const healthyServices = Object.values(services).filter(s => s.status === 'healthy').length
    const totalServices = Object.values(services).length
    
    if (healthyServices === totalServices) return 'healthy'
    if (healthyServices === 0) return 'unhealthy'
    return 'degraded'
  }

  // Status icon component
  const StatusIcon: React.FC<{ status: ServiceHealth['status'] }> = ({ status }) => {
    const iconProps = { className: "h-4 w-4" }
    
    switch (status) {
      case 'healthy':
        return <CheckCircle {...iconProps} className="h-4 w-4 text-green-500" />
      case 'unhealthy':
        return <HeartOff {...iconProps} className="h-4 w-4 text-red-500" />
      case 'checking':
        return <RefreshCw {...iconProps} className="h-4 w-4 text-blue-500 animate-spin" />
      default:
        return <AlertTriangle {...iconProps} className="h-4 w-4 text-yellow-500" />
    }
  }

  // Service status badge
  const getStatusBadge = (status: ServiceHealth['status']) => {
    switch (status) {
      case 'healthy':
        return <Badge variant="default" className="text-xs bg-green-100 text-green-800">Healthy</Badge>
      case 'unhealthy':
        return <Badge variant="destructive" className="text-xs">Unhealthy</Badge>
      case 'checking':
        return <Badge variant="secondary" className="text-xs">Checking...</Badge>
      default:
        return <Badge variant="outline" className="text-xs">Unknown</Badge>
    }
  }

  // Compact view
  if (compact) {
    const overallStatus = getOverallStatus()
    const healthyCount = Object.values(services).filter(s => s.status === 'healthy').length
    const totalCount = Object.values(services).length

    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Activity className={`h-4 w-4 ${
          overallStatus === 'healthy' ? 'text-green-500' : 
          overallStatus === 'unhealthy' ? 'text-red-500' : 'text-yellow-500'
        }`} />
        <span className="text-sm">
          Services: {healthyCount}/{totalCount}
        </span>
        {isChecking && (
          <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
        )}
      </div>
    )
  }

  // Full view
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            <span>Service Health</span>
          </div>
          <div className="flex items-center gap-2">
            {lastFullCheck && (
              <span className="text-xs text-muted-foreground">
                Last check: {lastFullCheck.toLocaleTimeString()}
              </span>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={checkAllServices}
              disabled={isChecking}
              className="h-8 px-2"
            >
              {isChecking ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {Object.entries(services).map(([key, service]) => (
            <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <StatusIcon status={service.status} />
                <div>
                  <div className="font-medium text-sm">{service.service_name}</div>
                  {showDetails && (
                    <div className="text-xs text-muted-foreground space-y-1">
                      {service.response_time && (
                        <div>Response: {service.response_time}ms</div>
                      )}
                      {service.version && (
                        <div>Version: {service.version}</div>
                      )}
                      {service.active_reviews !== undefined && (
                        <div>Active reviews: {service.active_reviews}</div>
                      )}
                      {service.error_message && (
                        <div className="text-red-600">Error: {service.error_message}</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {getStatusBadge(service.status)}
                {showDetails && service.response_time && (
                  <Badge variant="outline" className="text-xs">
                    {service.response_time}ms
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Overall Status Summary */}
        <div className="mt-4 p-3 border-t">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Overall Status:</span>
            <Badge variant={getOverallStatus() === 'healthy' ? 'default' : 'destructive'}>
              {getOverallStatus().charAt(0).toUpperCase() + getOverallStatus().slice(1)}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}