/**
 * Connection Status Indicator Component
 * Shows WebSocket connection status for Multi-Agent services
 */

import React from 'react'
import { Wifi, WifiOff, Loader2, Al<PERSON><PERSON>riangle } from 'lucide-react'
import { Badge } from './ui/badge'

export interface ConnectionStatus {
  connected: boolean
  connecting?: boolean
  quality: 'excellent' | 'good' | 'poor' | 'unknown'
  error?: string
  lastHeartbeat?: Date
  reconnectAttempts?: number
  serviceType?: 'multi-agent' | 'legacy'
}

interface ConnectionStatusIndicatorProps {
  connectionStatus: ConnectionStatus | null
  showDetails?: boolean
  className?: string
  compact?: boolean
}

export const ConnectionStatusIndicator: React.FC<ConnectionStatusIndicatorProps> = ({
  connectionStatus,
  showDetails = true,
  className = "",
  compact = false
}) => {
  if (!connectionStatus) return null

  const getStatusIcon = () => {
    const iconProps = { className: "h-3 w-3" }
    
    if (connectionStatus.connecting) {
      return <Loader2 {...iconProps} className="h-3 w-3 animate-spin text-blue-500" />
    }
    
    if (connectionStatus.error) {
      return <AlertTriangle {...iconProps} className="h-3 w-3 text-orange-500" />
    }
    
    if (connectionStatus.connected) {
      switch (connectionStatus.quality) {
        case 'excellent':
          return <Wifi {...iconProps} className="h-3 w-3 text-green-500" />
        case 'good':
          return <Wifi {...iconProps} className="h-3 w-3 text-green-400" />
        case 'poor':
          return <Wifi {...iconProps} className="h-3 w-3 text-yellow-500" />
        default:
          return <Wifi {...iconProps} className="h-3 w-3 text-gray-500" />
      }
    }
    
    return <WifiOff {...iconProps} className="h-3 w-3 text-red-500" />
  }

  const getStatusText = () => {
    if (connectionStatus.connecting) {
      return 'Connecting...'
    }
    
    if (connectionStatus.error) {
      return compact ? 'Error' : `Connection Error: ${connectionStatus.error}`
    }
    
    if (connectionStatus.connected) {
      const serviceText = connectionStatus.serviceType === 'multi-agent' ? 
        'Multi-Agent Service' : 'Legacy Service'
      return compact ? 'Connected' : `${serviceText} Connected`
    }
    
    if (connectionStatus.reconnectAttempts && connectionStatus.reconnectAttempts > 0) {
      return compact ? 'Reconnecting...' : `Reconnecting... (${connectionStatus.reconnectAttempts})`
    }
    
    return compact ? 'Disconnected' : 'Connection Lost'
  }

  const getStatusColor = () => {
    if (connectionStatus.connecting) return 'text-blue-600'
    if (connectionStatus.error) return 'text-orange-600'
    if (connectionStatus.connected) {
      switch (connectionStatus.quality) {
        case 'excellent':
        case 'good':
          return 'text-green-600'
        case 'poor':
          return 'text-yellow-600'
        default:
          return 'text-gray-600'
      }
    }
    return 'text-red-600'
  }

  const getBadgeVariant = () => {
    if (connectionStatus.connected) {
      switch (connectionStatus.quality) {
        case 'excellent':
          return 'default'
        case 'good':
          return 'secondary'
        case 'poor':
          return 'outline'
        default:
          return 'secondary'
      }
    }
    return 'destructive'
  }

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {getStatusIcon()}
        <span className={`text-xs ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>
    )
  }

  return (
    <div className={`flex items-center gap-2 text-xs ${className}`}>
      {getStatusIcon()}
      <span className={getStatusColor()}>
        {getStatusText()}
      </span>
      
      {showDetails && connectionStatus.connected && (
        <Badge variant={getBadgeVariant()} className="text-xs px-1 py-0">
          {connectionStatus.quality}
        </Badge>
      )}
      
      {showDetails && connectionStatus.lastHeartbeat && (
        <span className="text-muted-foreground text-xs">
          Last ping: {connectionStatus.lastHeartbeat.toLocaleTimeString()}
        </span>
      )}
    </div>
  )
}