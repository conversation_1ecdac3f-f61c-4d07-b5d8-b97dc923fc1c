/**
 * Service Mode Selector Component
 * Allows users to choose between Multi-Agent and Legacy services
 */

import React, { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>,
  User,
  <PERSON><PERSON><PERSON>,
  Clock,
  AlertTriangle,
  CheckCircle,
  Activity
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Switch } from './ui/switch'
import { Label } from './ui/label'
import { featureFlags } from '../config/featureFlags'
import { ServiceHealthIndicator, type ServiceHealth } from './ServiceHealthIndicator'

export type ServiceMode = 'multi_agent' | 'legacy' | 'auto'

interface ServiceModeOption {
  id: ServiceMode
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  features: string[]
  estimatedTime: string
  pros: string[]
  cons: string[]
  requiresHealthyService?: string
  agentCount?: number
}

interface ServiceModeSelectorProps {
  selectedMode: ServiceMode
  onModeChange: (mode: ServiceMode) => void
  serviceHealth?: Record<string, ServiceHealth>
  disabled?: boolean
  showAdvanced?: boolean
  className?: string
}

const SERVICE_MODE_OPTIONS: ServiceModeOption[] = [
  {
    id: 'multi_agent',
    name: 'Multi-Agent Service',
    description: 'Parallel analysis by 7 specialized AI agents for comprehensive review',
    icon: Cpu,
    features: [
      'Parallel execution (7 agents)',
      'Specialized analysis per domain',
      'JIRA acceptance criteria validation',
      'Enhanced security scanning',
      'Architectural pattern analysis',
      'Real-time progress tracking'
    ],
    estimatedTime: '2-4 minutes',
    pros: [
      '8.2x faster than sequential',
      'More comprehensive analysis',
      'Better JIRA integration',
      'Real-time WebSocket updates'
    ],
    cons: [
      'Requires Multi-Agent service',
      'Higher resource usage',
      'More complex error handling'
    ],
    requiresHealthyService: 'multi-agent',
    agentCount: 7
  },
  {
    id: 'legacy',
    name: 'Legacy Service',
    description: 'Sequential Claude-based analysis with traditional workflow',
    icon: User,
    features: [
      'Sequential analysis',
      'Traditional Claude workflow',
      'Proven reliability',
      'Lower resource usage',
      'Simpler error handling'
    ],
    estimatedTime: '15-30 minutes',
    pros: [
      'Battle-tested reliability',
      'Lower system requirements',
      'Simpler debugging',
      'Works without Multi-Agent service'
    ],
    cons: [
      'Slower execution time',
      'Less specialized analysis',
      'Limited parallel processing',
      'Basic JIRA integration'
    ],
    requiresHealthyService: 'legacy-claude',
    agentCount: 1
  },
  {
    id: 'auto',
    name: 'Automatic Selection',
    description: 'Automatically choose the best available service based on health',
    icon: Settings,
    features: [
      'Intelligent service selection',
      'Automatic fallback',
      'Health-based routing',
      'Zero configuration'
    ],
    estimatedTime: 'Variable',
    pros: [
      'No manual configuration',
      'Automatic fallback',
      'Best available performance',
      'Resilient to service issues'
    ],
    cons: [
      'Less predictable timing',
      'Mode may change unexpectedly',
      'Debugging complexity'
    ]
  }
]

export const ServiceModeSelector: React.FC<ServiceModeSelectorProps> = ({
  selectedMode,
  onModeChange,
  serviceHealth = {},
  disabled = false,
  showAdvanced = false,
  className = ""
}) => {
  const [isMultiAgentEnabled, setIsMultiAgentEnabled] = useState(featureFlags.isMultiAgentEnabled())
  const [showServiceHealth, setShowServiceHealth] = useState(false)

  // Update feature flag state
  useEffect(() => {
    setIsMultiAgentEnabled(featureFlags.isMultiAgentEnabled())
  }, [])

  // Check if a mode is available based on service health
  const isModeAvailable = (mode: ServiceModeOption): boolean => {
    if (disabled) return false
    
    if (mode.id === 'auto') return true
    
    if (mode.requiresHealthyService) {
      const service = serviceHealth[mode.requiresHealthyService]
      return service?.status === 'healthy'
    }
    
    return true
  }

  // Get recommendation badge
  const getRecommendationBadge = (mode: ServiceModeOption) => {
    if (mode.id === 'multi_agent' && isMultiAgentEnabled && isModeAvailable(mode)) {
      return <Badge variant="default" className="text-xs">Recommended</Badge>
    }
    
    if (mode.id === 'auto') {
      return <Badge variant="secondary" className="text-xs">Smart</Badge>
    }
    
    if (mode.id === 'legacy' && !isModeAvailable(SERVICE_MODE_OPTIONS[0])) {
      return <Badge variant="outline" className="text-xs">Fallback</Badge>
    }
    
    return null
  }

  // Get service status indicator
  const getServiceStatusIndicator = (mode: ServiceModeOption) => {
    if (!mode.requiresHealthyService) return null
    
    const service = serviceHealth[mode.requiresHealthyService]
    if (!service) return null
    
    switch (service.status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'unhealthy':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with Service Health Toggle */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Service Selection</h3>
        
        {showAdvanced && (
          <div className="flex items-center gap-2">
            <Label htmlFor="show-health" className="text-sm">
              Show Service Health
            </Label>
            <Switch
              id="show-health"
              checked={showServiceHealth}
              onCheckedChange={setShowServiceHealth}
            />
          </div>
        )}
      </div>

      {/* Service Health Indicator */}
      {showServiceHealth && (
        <ServiceHealthIndicator 
          compact={true}
          autoRefresh={true}
          className="mb-4"
        />
      )}

      {/* Mode Selection Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {SERVICE_MODE_OPTIONS.map((mode) => {
          const isAvailable = isModeAvailable(mode)
          const isSelected = selectedMode === mode.id
          const IconComponent = mode.icon
          
          return (
            <Card 
              key={mode.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected 
                  ? 'ring-2 ring-primary border-primary' 
                  : 'hover:shadow-md border-border'
              } ${
                !isAvailable 
                  ? 'opacity-50 cursor-not-allowed' 
                  : ''
              }`}
              onClick={() => isAvailable && onModeChange(mode.id)}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <IconComponent className="h-5 w-5" />
                    <span className="text-base">{mode.name}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {getServiceStatusIndicator(mode)}
                    {getRecommendationBadge(mode)}
                  </div>
                </CardTitle>
                
                <p className="text-sm text-muted-foreground">
                  {mode.description}
                </p>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Key Stats */}
                <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{mode.estimatedTime}</span>
                  </div>
                  {mode.agentCount && (
                    <div className="flex items-center gap-1">
                      <Cpu className="h-3 w-3" />
                      <span>{mode.agentCount} agent{mode.agentCount > 1 ? 's' : ''}</span>
                    </div>
                  )}
                </div>

                {/* Features */}
                <div className="space-y-2">
                  <h4 className="text-xs font-medium text-muted-foreground">Key Features:</h4>
                  <ul className="text-xs space-y-1">
                    {mode.features.slice(0, 3).map((feature, index) => (
                      <li key={index} className="flex items-start gap-1">
                        <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                    {mode.features.length > 3 && (
                      <li className="text-muted-foreground">
                        +{mode.features.length - 3} more features
                      </li>
                    )}
                  </ul>
                </div>

                {/* Selection Button */}
                <Button
                  variant={isSelected ? "default" : "outline"}
                  size="sm"
                  className="w-full mt-3"
                  disabled={!isAvailable}
                  onClick={(e) => {
                    e.stopPropagation()
                    if (isAvailable) onModeChange(mode.id)
                  }}
                >
                  {isSelected ? 'Selected' : 'Select'}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Advanced Details */}
      {showAdvanced && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Advanced Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">Feature Flags:</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>Multi-Agent Enabled: {isMultiAgentEnabled ? '✅ Yes' : '❌ No'}</li>
                  <li>Debug Mode: {import.meta.env.NODE_ENV === 'development' ? '✅ Yes' : '❌ No'}</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Current Selection:</h4>
                <div className="space-y-1 text-muted-foreground">
                  <div>Mode: <Badge variant="outline">{selectedMode}</Badge></div>
                  <div>Available: {isModeAvailable(SERVICE_MODE_OPTIONS.find(m => m.id === selectedMode)!) ? '✅ Yes' : '❌ No'}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Warning for unavailable modes */}
      {!isMultiAgentEnabled && selectedMode === 'multi_agent' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-sm text-yellow-800 font-medium">
                  Multi-Agent service is not available
                </p>
                <p className="text-xs text-yellow-700 mt-1">
                  The Multi-Agent service is either disabled or unhealthy. 
                  The system will automatically fall back to Legacy service.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}