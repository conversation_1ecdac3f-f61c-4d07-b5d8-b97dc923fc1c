import React, { useState, useEffect } from 'react'
import { 
  Search, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  RefreshCw,
  <PERSON><PERSON><PERSON>,
  User,
  Co<PERSON>,
  Eye
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { But<PERSON> } from './ui/button'
import { Badge } from './ui/badge'
import { Input } from './ui/input'
import { JiraAuthService } from '../services/auth/JiraAuthService'
import { 
  analyzeJiraFields, 
  getBestReviewerField, 
  formatFieldAnalysis,
  type JiraFieldAnalysis 
} from '../utils/jiraFieldDetector'

interface JiraFieldDetectorProps {
  onFieldSelected?: (field: string) => void
  currentField?: string
  className?: string
}

export const JiraFieldDetector: React.FC<JiraFieldDetectorProps> = ({
  onFieldSelected,
  currentField,
  className = ''
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState<JiraFieldAnalysis | null>(null)
  const [sampleTickets, setSampleTickets] = useState<any[]>([])
  const [selectedField, setSelectedField] = useState<string>(currentField || '')
  const [testTicketId, setTestTicketId] = useState('')
  const [testResult, setTestResult] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  const jiraService = new JiraAuthService()

  const analyzeFields = async () => {
    setIsAnalyzing(true)
    setError(null)
    
    try {
      // Fetch recent tickets from current user's assignments
      const userTickets = await jiraService.getAssignedTickets()
      
      if (!userTickets || userTickets.length === 0) {
        throw new Error('No tickets found to analyze. Make sure you have assigned tickets in Jira.')
      }

      setSampleTickets(userTickets.slice(0, 10)) // Use first 10 for analysis
      
      // Analyze the fields
      const fieldAnalysis = analyzeJiraFields(userTickets)
      setAnalysis(fieldAnalysis)

      // Auto-select best field if found
      const bestField = getBestReviewerField(fieldAnalysis)
      if (bestField && !selectedField) {
        setSelectedField(bestField)
        onFieldSelected?.(bestField)
      }

      console.log(formatFieldAnalysis(fieldAnalysis))
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to analyze fields')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const testField = async () => {
    if (!testTicketId || !selectedField) return

    try {
      // Note: getTicket method needs to be implemented in JiraAuthService
      // const ticket = await jiraService.getTicket(testTicketId)
      // const reviewer = extractReviewerFromTicket(ticket, selectedField)
      
      // For now, show a mock result
      setTestResult(`Testing field ${selectedField} with ticket ${testTicketId} - Feature not yet implemented`)
    } catch (err) {
      setTestResult(`Error: ${err instanceof Error ? err.message : 'Failed to test field'}`)
    }
  }

  const selectField = (field: string) => {
    setSelectedField(field)
    onFieldSelected?.(field)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'border-green-500 text-green-400 bg-green-500/10'
      case 'medium': return 'border-yellow-500 text-yellow-400 bg-yellow-500/10'
      case 'low': return 'border-red-500 text-red-400 bg-red-500/10'
      default: return 'border-gray-500 text-gray-400 bg-gray-500/10'
    }
  }

  const getConfidenceIcon = (confidence: string) => {
    switch (confidence) {
      case 'high': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'low': return <XCircle className="h-4 w-4 text-red-400" />
      default: return <Search className="h-4 w-4 text-gray-400" />
    }
  }

  useEffect(() => {
    if (currentField) {
      setSelectedField(currentField)
    }
  }, [currentField])

  return (
    <Card className={`${className} border-blue-500/30 bg-blue-500/5`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-400">
          <Settings className="h-5 w-5" />
          🔍 Jira Reviewer Field Detection
        </CardTitle>
        <p className="text-sm text-gray-400">
          Automatically detect which custom field is used for code reviewers in your Jira instance.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Analysis Controls */}
        <div className="flex items-center gap-3">
          <Button 
            onClick={analyzeFields}
            disabled={isAnalyzing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {isAnalyzing ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="h-4 w-4" />
            )}
            {isAnalyzing ? 'Analyzing...' : 'Analyze Jira Fields'}
          </Button>
          
          {analysis && (
            <Badge variant="outline" className="border-blue-500 text-blue-400">
              Found {analysis.possibleReviewerFields.length} candidates
            </Badge>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-center gap-2 text-red-400">
              <XCircle className="h-4 w-4" />
              <span className="font-medium">Analysis Failed</span>
            </div>
            <p className="text-sm text-gray-300 mt-1">{error}</p>
          </div>
        )}

        {/* Field Suggestions */}
        {analysis && analysis.fieldSuggestions.length > 0 && (
          <div>
            <h4 className="text-sm font-semibold text-gray-300 mb-3">
              Detected Reviewer Fields (sorted by confidence):
            </h4>
            
            <div className="space-y-2">
              {analysis.fieldSuggestions.map((suggestion) => (
                <div 
                  key={suggestion.field}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors hover:bg-gray-800/50 ${
                    selectedField === suggestion.field ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => selectField(suggestion.field)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getConfidenceIcon(suggestion.confidence)}
                      <code className="text-sm bg-gray-800 px-2 py-1 rounded font-mono text-blue-400">
                        {suggestion.field}
                      </code>
                      <Badge className={getConfidenceColor(suggestion.confidence)}>
                        {suggestion.confidence}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          copyToClipboard(suggestion.field)
                        }}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      
                      {selectedField === suggestion.field && (
                        <CheckCircle className="h-4 w-4 text-green-400" />
                      )}
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-400 mt-2 ml-7">
                    {suggestion.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Manual Field Input */}
        <div>
          <h4 className="text-sm font-semibold text-gray-300 mb-3">
            Manual Field Configuration:
          </h4>
          
          <div className="flex items-center gap-2">
            <Input
              placeholder="e.g., customfield_10009"
              value={selectedField}
              onChange={(e) => setSelectedField(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={() => selectField(selectedField)}
              disabled={!selectedField}
              variant="outline"
              size="sm"
            >
              Use Field
            </Button>
          </div>
        </div>

        {/* Field Testing */}
        {selectedField && (
          <div className="p-4 bg-gray-900/50 border border-gray-700 rounded-lg">
            <h4 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Test Selected Field: <code className="text-blue-400">{selectedField}</code>
            </h4>
            
            <div className="flex items-center gap-2 mb-3">
              <Input
                placeholder="Enter Jira ticket ID (e.g., CMS20-1234)"
                value={testTicketId}
                onChange={(e) => setTestTicketId(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={testField}
                disabled={!testTicketId || !selectedField}
                variant="outline"
                size="sm"
              >
                Test
              </Button>
            </div>

            {testResult && (
              <div className="p-3 bg-gray-800 border border-gray-600 rounded">
                <div className="flex items-center gap-2 mb-1">
                  <User className="h-4 w-4 text-green-400" />
                  <span className="text-sm font-medium text-gray-300">Test Result:</span>
                </div>
                <p className="text-sm text-white font-mono">{testResult}</p>
              </div>
            )}
          </div>
        )}

        {/* Sample Data Display */}
        {sampleTickets.length > 0 && (
          <div>
            <h4 className="text-sm font-semibold text-gray-300 mb-3">
              Analysis based on {sampleTickets.length} sample tickets:
            </h4>
            
            <div className="text-xs text-gray-400 space-y-1">
              {sampleTickets.slice(0, 5).map(ticket => (
                <div key={ticket.key} className="flex items-center gap-2">
                  <code className="text-blue-400">{ticket.key}</code>
                  <span>-</span>
                  <span className="truncate">{ticket.fields?.summary || 'No title'}</span>
                </div>
              ))}
              {sampleTickets.length > 5 && (
                <div className="text-gray-500">... and {sampleTickets.length - 5} more</div>
              )}
            </div>
          </div>
        )}

        {/* Success Message */}
        {selectedField && onFieldSelected && (
          <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="flex items-center gap-2 text-green-400">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">Field Selected</span>
            </div>
            <p className="text-sm text-gray-300 mt-1">
              Using <code className="text-green-400">{selectedField}</code> for reviewer detection.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}