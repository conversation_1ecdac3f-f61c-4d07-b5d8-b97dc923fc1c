import React from 'react';
import type { AIAnalysisMetadata, FallbackWarning, EffortEstimationAnalysis } from '../types/fallback-monitoring';

interface FallbackMonitoringAlertProps {
  aiMetadata?: AIAnalysisMetadata;
  effortAnalysis?: EffortEstimationAnalysis;
  className?: string;
}

export const FallbackMonitoringAlert: React.FC<FallbackMonitoringAlertProps> = ({
  aiMetadata,
  effortAnalysis,
  className = ''
}) => {
  if (!aiMetadata?.has_fallback_calculations && effortAnalysis?.claude_analysis !== false) {
    return null; // No fallbacks detected
  }

  const generateFallbackWarnings = (): FallbackWarning[] => {
    const warnings: FallbackWarning[] = [];

    // Check Quality Score
    if (aiMetadata?.quality_score_analysis?.claude_analysis === false) {
      warnings.push({
        type: 'quality_score',
        title: 'Quality Score Fallback',
        message: `Quality Score wurde mit mathematischen Algorithmen berechnet (Score: ${aiMetadata.quality_score_analysis.quality_score})`,
        fallback_reason: aiMetadata.quality_score_analysis.fallback_reason || 'Unknown reason',
        timestamp: aiMetadata.quality_score_analysis.fallback_timestamp || '',
        calculation_method: aiMetadata.quality_score_analysis.calculation_method
      });
    }

    // Check Duplication Detection
    if (aiMetadata?.duplication_analysis?.claude_analysis === false) {
      warnings.push({
        type: 'duplication_detection',
        title: 'Code Duplication Fallback',
        message: `Code-Duplikation wurde mit Text-Pattern-Analyse erkannt (${aiMetadata.duplication_analysis.duplication_percentage}%)`,
        fallback_reason: aiMetadata.duplication_analysis.fallback_reason || 'Unknown reason',
        timestamp: aiMetadata.duplication_analysis.fallback_timestamp || '',
        calculation_method: aiMetadata.duplication_analysis.calculation_method
      });
    }

    // Check Effort Estimation
    if (effortAnalysis?.claude_analysis === false) {
      warnings.push({
        type: 'effort_estimation',
        title: 'Effort Estimation Fallback',
        message: `Aufwandsschätzung wurde mit Fallback-Algorithmen berechnet (${effortAnalysis.estimated_effort})`,
        fallback_reason: effortAnalysis.fallback_reason || 'Unknown reason',
        timestamp: effortAnalysis.fallback_timestamp || '',
        calculation_method: effortAnalysis.calculation_method
      });
    }

    return warnings;
  };

  const warnings = generateFallbackWarnings();

  if (warnings.length === 0) {
    return null;
  }

  return (
    <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            ⚠️ Claude Code CLI Fallback Detected ({warnings.length} Berechnungen)
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p className="mb-2">
              Einige Berechnungen wurden ohne Claude Code AI durchgeführt. 
              Dies kann auf temporäre CLI-Probleme oder Timeout-Errors hinweisen.
            </p>
            <div className="space-y-2">
              {warnings.map((warning, index) => (
                <div key={index} className="border-l-2 border-yellow-300 pl-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{warning.title}</span>
                    <span className="text-xs bg-yellow-200 px-2 py-1 rounded">
                      {warning.calculation_method}
                    </span>
                  </div>
                  <p className="mt-1 text-xs">{warning.message}</p>
                  <div className="mt-1 flex items-center text-xs text-yellow-600">
                    <span className="mr-2">Grund: {warning.fallback_reason}</span>
                    {warning.timestamp && (
                      <span>• {new Date(warning.timestamp).toLocaleTimeString()}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-3 text-xs bg-yellow-100 p-2 rounded">
              <strong>Entwickler-Info:</strong> Fallback-Berechnungen verwenden mathematische Algorithmen 
              anstatt KI-basierter Analysen. Prüfen Sie die Claude Code CLI Installation und Netzwerkverbindung.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FallbackMonitoringAlert;