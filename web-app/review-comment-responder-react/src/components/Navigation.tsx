import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { MessageSquareText, Eye, Bot } from 'lucide-react'
import { cn } from '../lib/utils'
import { UserProfile } from './auth/UserProfile'
import { NavLoginButton } from './auth/LoginButton'
import { ModeIndicator } from './ModeIndicator'
import { WorktreeStatusIndicator } from './worktree/WorktreeStatusIndicator'
import { useAuthStatus } from '../hooks/useAuth'

export const Navigation: React.FC = () => {
  const location = useLocation()
  const { isAuthenticated } = useAuthStatus()

  const navItems = [
    {
      path: '/',
      label: 'Code Review Responder',
      icon: MessageSquareText,
      description: 'Generate intelligent responses to code review comments'
    },
    {
      path: '/reviewer',
      label: 'Code Reviewer',
      icon: Eye,
      description: 'Analyze and review code for quality and best practices'
    }
  ]

  return (
    <nav className="sticky top-0 z-50 bg-card/80 backdrop-blur-xl border-b border-border/50 shadow-lg shadow-black/5">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3 group">
            <div className="p-2 rounded-lg bg-primary shadow-lg shadow-primary/30 group-hover:shadow-primary/50 transition-all duration-300">
              <Bot className="h-6 w-6 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-black uppercase tracking-tighter text-white" style={{ letterSpacing: '-0.05em' }}>
                AI Code
                <span className="text-primary ml-1">Tools</span>
              </h1>
            </div>
          </Link>

          {/* Navigation Items */}
          <div className="flex items-center gap-2">
            {navItems.map((item) => {
              const isActive = location.pathname === item.path
              const Icon = item.icon

              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={cn(
                    "flex items-center gap-3 px-4 py-2 rounded-lg transition-all duration-300 group relative overflow-hidden",
                    "hover:bg-primary/10 hover:border-primary/20 border-2",
                    isActive 
                      ? "bg-primary/15 border-primary/30 text-primary shadow-lg shadow-primary/20" 
                      : "border-transparent text-muted-foreground hover:text-foreground"
                  )}
                >
                  {/* Active indicator */}
                  {isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 rounded-lg"></div>
                  )}
                  
                  <div className="relative flex items-center gap-3">
                    <Icon className={cn(
                      "h-5 w-5 transition-all duration-300",
                      isActive ? "text-primary" : "text-muted-foreground group-hover:text-primary"
                    )} />
                    <div className="hidden md:block">
                      <span className={cn(
                        "font-medium text-sm",
                        isActive ? "text-primary" : "text-foreground"
                      )}>
                        {item.label}
                      </span>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>

          {/* Status Indicators & Auth Section */}
          <div className="flex items-center gap-4">
            {/* Worktree Status - show on reviewer page */}
            {location.pathname === '/reviewer' && (
              <WorktreeStatusIndicator 
                showFullCard={false}
                className="hidden sm:flex"
              />
            )}
            
            {/* Mode Indicator - only show on Code Review Responder page */}
            {location.pathname === '/' && (
              <ModeIndicator 
                variant="compact" 
                showToggle={false}
                className="hidden sm:flex"
              />
            )}
            
            {isAuthenticated ? (
              <UserProfile />
            ) : (
              <NavLoginButton />
            )}
          </div>
        </div>

        {/* Mobile Navigation Description */}
        <div className="md:hidden pb-3">
          {navItems.map((item) => {
            if (location.pathname === item.path) {
              return (
                <p key={item.path} className="text-xs text-muted-foreground mt-2 animate-fade-in">
                  {item.description}
                </p>
              )
            }
            return null
          })}
        </div>
      </div>
    </nav>
  )
}