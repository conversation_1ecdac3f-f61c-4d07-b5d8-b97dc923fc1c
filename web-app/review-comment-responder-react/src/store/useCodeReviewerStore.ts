import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { useShallow } from 'zustand/react/shallow'
import type { ReviewMode } from '../components/ReviewModeSelector'
import type { EnhancedReviewResults, ReviewSession } from '../types/enhanced-review'

// Types
interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
  author: string
  created_date: string
  updated_date: string
  comments: number
  state: string
  reviewers: string[]
  jira_ticket?: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  related_prs: number[]
}


interface ReviewProgress {
  session_id: string
  status: string
  progress: number
  message: string
  worktree_path?: string
}

// Store State
interface CodeReviewerState {
  // Assigned Work
  assignedPRs: AssignedPR[]
  assignedTickets: AssignedTicket[]
  isLoadingPRs: boolean
  isLoadingTickets: boolean
  assignedWorkError: string | null

  // Current Selection
  selectedPR: AssignedPR | null
  selectedTicket: AssignedTicket | null
  
  // Review Configuration
  reviewMode: ReviewMode
  repositoryPath: string
  
  // Review Sessions
  reviewSessions: Record<string, ReviewSession>
  activeSessionId: string | null
  currentProgress: ReviewProgress | null
  
  // UI State
  isReviewPanelExpanded: boolean
  showCompletedSessions: boolean
  
  // Actions
  setAssignedPRs: (prs: AssignedPR[]) => void
  setAssignedTickets: (tickets: AssignedTicket[]) => void
  setLoadingPRs: (loading: boolean) => void
  setLoadingTickets: (loading: boolean) => void
  setAssignedWorkError: (error: string | null) => void
  
  selectPR: (pr: AssignedPR | null) => void
  selectTicket: (ticket: AssignedTicket | null) => void
  
  setReviewMode: (mode: ReviewMode) => void
  setRepositoryPath: (path: string) => void
  
  addReviewSession: (session: ReviewSession) => void
  updateReviewSession: (sessionId: string, updates: Partial<ReviewSession>) => void
  setActiveSession: (sessionId: string | null) => void
  updateProgress: (progress: ReviewProgress) => void
  clearProgress: () => void
  
  setReviewPanelExpanded: (expanded: boolean) => void
  setShowCompletedSessions: (show: boolean) => void
  
  // Computed/Helper Methods
  getRelatedTicketForPR: (pr: AssignedPR) => AssignedTicket | null
  getRelatedPRsForTicket: (ticket: AssignedTicket) => AssignedPR[]
  getActiveSession: () => ReviewSession | null
  getCompletedSessions: () => ReviewSession[]
  getSessionsForPR: (prId: number) => ReviewSession[]
  
  // Reset/Clear Methods
  clearSelection: () => void
  clearSessions: () => void
  reset: () => void
}

// Default Values
const defaultRepositoryPath = '/Users/<USER>/dev/rma-mono'

// Create Store
export const useCodeReviewerStore = create<CodeReviewerState>()(
  devtools(
    (set, get) => ({
      // Initial State
      assignedPRs: [],
      assignedTickets: [],
      isLoadingPRs: false,
      isLoadingTickets: false,
      assignedWorkError: null,
      
      selectedPR: null,
      selectedTicket: null,
      
      reviewMode: 'comprehensive',
      repositoryPath: defaultRepositoryPath,
      
      reviewSessions: {},
      activeSessionId: null,
      currentProgress: null,
      
      isReviewPanelExpanded: true,
      showCompletedSessions: false,
      
      // Actions
      setAssignedPRs: (prs) => set({ assignedPRs: prs }),
      setAssignedTickets: (tickets) => set({ assignedTickets: tickets }),
      setLoadingPRs: (loading) => set({ isLoadingPRs: loading }),
      setLoadingTickets: (loading) => set({ isLoadingTickets: loading }),
      setAssignedWorkError: (error) => set({ assignedWorkError: error }),
      
      selectPR: (pr) => {
        const currentState = get()
        
        // Only update if different to prevent infinite loops
        if (currentState.selectedPR?.id !== pr?.id) {
          set({ selectedPR: pr })
          
          // Auto-select related ticket if available and not already selected
          if (pr && pr.jira_ticket) {
            const relatedTicket = currentState.assignedTickets.find(
              ticket => ticket.ticket_id === pr.jira_ticket
            )
            if (relatedTicket && currentState.selectedTicket?.ticket_id !== relatedTicket.ticket_id) {
              set({ selectedTicket: relatedTicket })
            }
          } else if (pr === null) {
            // Clear ticket selection when PR is cleared
            set({ selectedTicket: null })
          }
        }
      },
      
      selectTicket: (ticket) => {
        const currentState = get()
        
        // Only update if different to prevent infinite loops
        if (currentState.selectedTicket?.ticket_id !== ticket?.ticket_id) {
          set({ selectedTicket: ticket })
          
          // Auto-select related PR if available and not already selected
          if (ticket && ticket.related_prs && ticket.related_prs.length > 0) {
            const relatedPR = currentState.assignedPRs.find(
              pr => ticket.related_prs.includes(pr.id)
            )
            if (relatedPR && currentState.selectedPR?.id !== relatedPR.id) {
              set({ selectedPR: relatedPR })
            }
          } else if (ticket === null) {
            // Clear PR selection when ticket is cleared
            set({ selectedPR: null })
          }
        }
      },
      
      setReviewMode: (mode) => set({ reviewMode: mode }),
      setRepositoryPath: (path) => set({ repositoryPath: path }),
      
      addReviewSession: (session) => 
        set(state => ({
          reviewSessions: {
            ...state.reviewSessions,
            [session.session_id]: session
          },
          activeSessionId: session.session_id
        })),
      
      updateReviewSession: (sessionId, updates) =>
        set(state => ({
          reviewSessions: {
            ...state.reviewSessions,
            [sessionId]: {
              ...state.reviewSessions[sessionId],
              ...updates
            }
          }
        })),
      
      setActiveSession: (sessionId) => set({ activeSessionId: sessionId }),
      
      updateProgress: (progress) => set({ currentProgress: progress }),
      clearProgress: () => set({ currentProgress: null }),
      
      setReviewPanelExpanded: (expanded) => set({ isReviewPanelExpanded: expanded }),
      setShowCompletedSessions: (show) => set({ showCompletedSessions: show }),
      
      // Helper Methods
      getRelatedTicketForPR: (pr) => {
        const { assignedTickets } = get()
        if (!pr.jira_ticket) return null
        return assignedTickets.find(ticket => ticket.ticket_id === pr.jira_ticket) || null
      },
      
      getRelatedPRsForTicket: (ticket) => {
        const { assignedPRs } = get()
        return assignedPRs.filter(pr => ticket.related_prs.includes(pr.id))
      },
      
      getActiveSession: () => {
        const { reviewSessions, activeSessionId } = get()
        return activeSessionId ? reviewSessions[activeSessionId] || null : null
      },
      
      getCompletedSessions: () => {
        const { reviewSessions } = get()
        return Object.values(reviewSessions).filter(
          session => session.status === 'completed'
        )
      },
      
      getSessionsForPR: (prId) => {
        const { reviewSessions, assignedPRs } = get()
        const pr = assignedPRs.find(p => p.id === prId)
        if (!pr) return []
        
        return Object.values(reviewSessions).filter(
          session => session.branch_name === pr.branch
        )
      },
      
      // Reset Methods
      clearSelection: () => set({ 
        selectedPR: null, 
        selectedTicket: null 
      }),
      
      clearSessions: () => set({ 
        reviewSessions: {}, 
        activeSessionId: null, 
        currentProgress: null 
      }),
      
      reset: () => set({
        assignedPRs: [],
        assignedTickets: [],
        isLoadingPRs: false,
        isLoadingTickets: false,
        assignedWorkError: null,
        selectedPR: null,
        selectedTicket: null,
        reviewMode: 'comprehensive',
        repositoryPath: defaultRepositoryPath,
        reviewSessions: {},
        activeSessionId: null,
        currentProgress: null,
        isReviewPanelExpanded: true,
        showCompletedSessions: false
      })
    }),
    {
      name: 'code-reviewer-store',
      partialize: (state: CodeReviewerState) => ({
        // Persist only configuration and completed sessions
        reviewMode: state.reviewMode,
        repositoryPath: state.repositoryPath,
        isReviewPanelExpanded: state.isReviewPanelExpanded,
        showCompletedSessions: state.showCompletedSessions,
        reviewSessions: Object.fromEntries(
          Object.entries(state.reviewSessions).filter(
            ([, session]: [string, ReviewSession]) => session.status === 'completed'
          )
        )
      })
    }
  )
)

// Selectors for common use cases
export const useAssignedWork = () => useCodeReviewerStore(
  useShallow(state => ({
    prs: state.assignedPRs,
    tickets: state.assignedTickets,
    isLoadingPRs: state.isLoadingPRs,
    isLoadingTickets: state.isLoadingTickets,
    error: state.assignedWorkError
  }))
)

export const useCurrentSelection = () => useCodeReviewerStore(
  useShallow(state => ({
    selectedPR: state.selectedPR,
    selectedTicket: state.selectedTicket
  }))
)

export const useRelatedWork = () => useCodeReviewerStore(state => {
  const selectedPR = state.selectedPR
  const selectedTicket = state.selectedTicket
  const assignedTickets = state.assignedTickets
  const assignedPRs = state.assignedPRs
  
  const relatedTicket = selectedPR && selectedPR.jira_ticket 
    ? assignedTickets.find(ticket => ticket.ticket_id === selectedPR.jira_ticket) || null
    : null
    
  const relatedPRs = selectedTicket 
    ? assignedPRs.filter(pr => selectedTicket.related_prs.includes(pr.id))
    : []
  
  return {
    relatedTicket,
    relatedPRs
  }
})

export const useReviewConfig = () => useCodeReviewerStore(
  useShallow(state => ({
    mode: state.reviewMode,
    repositoryPath: state.repositoryPath,
    setMode: state.setReviewMode,
    setRepositoryPath: state.setRepositoryPath
  }))
)

export const useActiveSession = () => useCodeReviewerStore(
  state => {
    const activeSessionId = state.activeSessionId
    return activeSessionId ? state.reviewSessions[activeSessionId] || null : null
  }
)

export const useReviewSessionActions = () => useCodeReviewerStore(
  useShallow(state => ({
    addSession: state.addReviewSession,
    updateSession: state.updateReviewSession,
    setActiveSession: state.setActiveSession,
    updateProgress: state.updateProgress,
    clearProgress: state.clearProgress
  }))
)

export const useReviewProgress = () => useCodeReviewerStore(
  state => state.currentProgress
)

export const useReviewSession = () => {
  const activeSession = useActiveSession()
  const progress = useReviewProgress()
  const { addSession, updateSession, setActiveSession, updateProgress, clearProgress } = useReviewSessionActions()
  
  return {
    activeSession,
    progress,
    addSession,
    updateSession, 
    setActiveSession,
    updateProgress,
    clearProgress
  }
}

export const useReviewUI = () => useCodeReviewerStore(
  useShallow(state => ({
    isReviewPanelExpanded: state.isReviewPanelExpanded,
    showCompletedSessions: state.showCompletedSessions,
    setReviewPanelExpanded: state.setReviewPanelExpanded,
    setShowCompletedSessions: state.setShowCompletedSessions
  }))
)

// Action creators for complex operations
export const useCodeReviewerActions = () => {
  const store = useCodeReviewerStore()
  
  return {
    // Load assigned work
    loadAssignedWork: async () => {
      // This would typically call the service and update the store
      // Implementation would be in the components using this store
    },
    
    // Start a review
    startReview: async () => {
      // Implementation would be in the components
    },
    
    // Handle review completion
    handleReviewCompletion: (sessionId: string, results: EnhancedReviewResults) => {
      store.updateReviewSession(sessionId, {
        status: 'completed',
        results,
        completed_at: new Date().toISOString()
      })
      store.clearProgress()
    },
    
    // Handle review error
    handleReviewError: (sessionId: string, error: string) => {
      store.updateReviewSession(sessionId, {
        status: 'error',
        error
      })
      store.clearProgress()
    }
  }
}