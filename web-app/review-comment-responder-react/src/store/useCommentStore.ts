import { create } from 'zustand'
import type { Comment, PRContext, CommentResponse, PreviewChange } from '../types'

interface CommentStore {
  comments: Comment[]
  commentIdCounter: number
  prContext: PRContext
  isBackendAvailable: boolean
  
  // Actions
  addComment: (comment: Omit<Comment, 'id'>) => void
  updateComment: (id: number, updates: Partial<Comment>) => void
  updateFileSpecificResponse: (id: number, filename: string, response: CommentResponse, previewChanges: PreviewChange[]) => void
  removeComment: (id: number) => void
  clearAllComments: () => void
  updatePRContext: (context: Partial<PRContext>) => void
  setBackendAvailable: (available: boolean) => void
  
  // Computed
  getPendingComments: () => Comment[]
  getCompletedComments: () => Comment[]
  getStats: () => { total: number; pending: number; completed: number; error: number }
}

const defaultPRContext: PRContext = {
  prUrl: '',
  branchName: '',
  ticketDescription: '',
  changesSummary: '',
  workingDirectory: '.',
  worktreePath: ''
}

export const useCommentStore = create<CommentStore>((set, get) => ({
  comments: [],
  commentIdCounter: 1,
  prContext: defaultPRContext,
  isBackendAvailable: false,

  addComment: (comment) => 
    set((state) => ({
      comments: [...state.comments, { ...comment, id: state.commentIdCounter }],
      commentIdCounter: state.commentIdCounter + 1
    })),

  updateComment: (id, updates) =>
    set((state) => ({
      comments: state.comments.map(comment => 
        comment.id === id ? { ...comment, ...updates } : comment
      )
    })),

  updateFileSpecificResponse: (id, filename, response, previewChanges) =>
    set((state) => ({
      comments: state.comments.map(comment => {
        if (comment.id !== id) return comment
        
        const currentFileSpecificResponses = comment.fileSpecificResponses || {}
        
        return {
          ...comment,
          fileSpecificResponses: {
            ...currentFileSpecificResponses,
            [filename]: {
              response,
              previewChanges,
              timestamp: new Date().toISOString()
            }
          }
        }
      })
    })),

  removeComment: (id) =>
    set((state) => ({
      comments: state.comments.filter(comment => comment.id !== id)
    })),

  clearAllComments: () =>
    set(() => ({
      comments: [],
      commentIdCounter: 1
    })),

  updatePRContext: (context) =>
    set((state) => ({
      prContext: { ...state.prContext, ...context }
    })),

  setBackendAvailable: (available) =>
    set(() => ({ isBackendAvailable: available })),

  getPendingComments: () => 
    get().comments.filter(c => c.status === 'pending'),

  getCompletedComments: () =>
    get().comments.filter(c => c.status === 'completed'),

  getStats: () => {
    const comments = get().comments
    return {
      total: comments.length,
      pending: comments.filter(c => c.status === 'pending').length,
      completed: comments.filter(c => c.status === 'completed').length,
      error: comments.filter(c => c.status === 'error').length
    }
  }
}))