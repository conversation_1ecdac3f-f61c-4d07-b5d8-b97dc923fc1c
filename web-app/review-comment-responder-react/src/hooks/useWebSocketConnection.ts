import { useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import type { ReviewSession } from '../types/enhanced-review'

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

interface UseWebSocketConnectionProps {
  activeSession: ReviewSession | null
  onProgressEvent: (event: ProgressEvent) => void
  onProgressStepUpdate: (step: string) => void
  onSessionUpdate: (sessionId: string, updates: Partial<ReviewSession>) => void
}

export const useWebSocketConnection = ({
  activeSession,
  onProgressEvent,
  onProgressStepUpdate,
  onSessionUpdate
}: UseWebSocketConnectionProps) => {
  const socketRef = useRef<Socket | null>(null)

  useEffect(() => {
    console.log('🔌 Setting up WebSocket connection...')
    const newSocket = io('http://localhost:5002', {
      transports: ['polling', 'websocket'],
      forceNew: true,
      upgrade: true
    })
    socketRef.current = newSocket
    
    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('✅ WebSocket connected successfully')
    })
    
    newSocket.on('disconnect', () => {
      console.log('❌ WebSocket disconnected')
    })
    
    newSocket.on('connect_error', (error) => {
      console.error('❌ WebSocket connection error:', error)
    })
    
    // Listen for enhanced WebSocket events from the new backend
    
    // Session lifecycle events
    newSocket.on('session_started', (data) => {
      console.log('🚀 Session Started:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `session_started_${Date.now()}`,
          type: 'session_started',
          message: 'Review session started',
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          progress: 10,
          progress_message: 'Session initialized'
        })
      }
    })
    
    // Claude thinking events
    newSocket.on('claude_thinking', (data) => {
      console.log('🤔 Claude Thinking:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `claude_thinking_${Date.now()}`,
          type: 'claude_thinking',
          message: data.message || 'Claude is analyzing...',
          timestamp: new Date(),
          data
        })
        onProgressStepUpdate(data.message || 'Analyzing code')
      }
    })
    
    // Tool usage events
    newSocket.on('tool_usage', (data) => {
      console.log('🔧 Tool Usage:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `tool_usage_${Date.now()}`,
          type: 'tool_usage',
          message: `${data.tool_name}: ${data.action || 'Processing'}`,
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          progress: Math.min(data.progress || 30, 85),
          progress_message: `Using ${data.tool_name}`
        })
      }
    })
    
    // Claude response streaming
    newSocket.on('claude_response_stream', (data) => {
      console.log('💬 Claude Response Stream:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `claude_response_${Date.now()}`,
          type: 'claude_response',
          message: data.chunk || 'Generating response...',
          timestamp: new Date(),
          data
        })
      }
    })
    
    // Structured result ready
    newSocket.on('structured_result', (data) => {
      console.log('📊 Structured Result:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `structured_result_${Date.now()}`,
          type: 'structured_result',
          message: 'Structured analysis complete',
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          progress: 90,
          progress_message: 'Finalizing review'
        })
      }
    })
    
    // Phase 3 events
    newSocket.on('phase3_started', (data) => {
      console.log('📚 Phase 3 Started:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `phase3_started_${Date.now()}`,
          type: 'phase3_started',
          message: 'Generating implementation summary...',
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          progress: 85,
          progress_message: 'Creating tutorial documentation'
        })
      }
    })
    
    newSocket.on('phase3_completed', (data) => {
      console.log('✅ Phase 3 Completed:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `phase3_completed_${Date.now()}`,
          type: 'phase3_completed',
          message: 'Tutorial generation complete',
          timestamp: new Date(),
          data
        })
      }
    })
    
    // Review metadata
    newSocket.on('review_metadata', (data) => {
      console.log('📋 Review Metadata:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `review_metadata_${Date.now()}`,
          type: 'review_metadata',
          message: 'Review statistics updated',
          timestamp: new Date(),
          data
        })
      }
    })
    
    // Review completed
    newSocket.on('review_completed', (data) => {
      console.log('🎉 Review Completed:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `review_completed_${Date.now()}`,
          type: 'review_completed',
          message: 'Review completed successfully!',
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          status: 'completed',
          progress: 100,
          progress_message: 'Review completed!'
        })
      }
    })
    
    // Error handling
    newSocket.on('review_error', (data) => {
      console.error('❌ Review Error:', data)
      if (data.session_id === activeSession?.session_id) {
        onProgressEvent({
          id: `review_error_${Date.now()}`,
          type: 'review_error',
          message: data.error || 'An error occurred',
          timestamp: new Date(),
          data
        })
        onSessionUpdate(data.session_id, {
          status: 'error',
          progress_message: `Error: ${data.error}`
        })
      }
    })
    
    // Legacy support for old claude_progress event
    newSocket.on('claude_progress', (data) => {
      console.log('🔴 Legacy Claude Progress:', data)
      
      if (data.session_id && activeSession?.session_id === data.session_id) {
        console.log('✅ Adding legacy progress event:', data.type, data.message)
        
        // Create new progress event
        const progressEvent: ProgressEvent = {
          id: `${data.type}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          type: data.type,
          message: data.message || 'Update received',
          timestamp: new Date(),
          data: {
            ...data,
            session_id: undefined // Remove session_id from data to avoid duplication
          }
        }
        
        onProgressEvent(progressEvent)
        onProgressStepUpdate(data.message || data.type)
        
        // Update progress based on message type
        if (data.type === 'session_started') {
          onSessionUpdate(data.session_id, { 
            progress_message: data.message,
            progress: 15
          })
        } else if (data.type === 'turn_started') {
          onSessionUpdate(data.session_id, { 
            progress_message: `Turn ${data.turn}: Analyzing...`,
            progress: Math.min(20 + (data.turn * 5), 80)
          })
        } else if (data.type === 'tool_use') {
          onSessionUpdate(data.session_id, { 
            progress_message: `Using ${data.tool}: ${data.message}`,
            progress: Math.min(30 + (Math.random() * 20), 85)
          })
        } else if (data.type === 'claude_response') {
          onSessionUpdate(data.session_id, { 
            progress_message: data.preview || 'Claude is analyzing...',
            progress: Math.min(60 + (Math.random() * 20), 90)
          })
        } else if (data.type === 'review_completed') {
          console.log('🎉 Review completed - updating session status')
          onSessionUpdate(data.session_id, { 
            status: 'completed',
            progress: 100,
            progress_message: 'Review completed!'
          })
        }
      }
    })
    
    // Cleanup on unmount
    return () => {
      console.log('🔌 Closing WebSocket connection...')
      newSocket.close()
      socketRef.current = null
    }
  }, [activeSession?.session_id, onProgressEvent, onProgressStepUpdate, onSessionUpdate])

  return socketRef.current
}