import { useCallback } from 'react'
import { useEnhancedWebSocket } from './useEnhancedWebSocket'
import type { ReviewSession } from '../types/enhanced-review'
import type { MultiAgentReviewStatus } from '../components/MultiAgentProgressTracker'
import type { 
  AllWebSocketEvents, 
  MultiAgentWebSocketEvent, 
  LegacyWebSocketEvent, 
  WebSocketEventHandlers 
} from '../types/websocket-events'
import { featureFlags } from '../config/featureFlags'

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

interface UseWebSocketConnectionProps {
  activeSession: ReviewSession | null
  onProgressEvent: (event: ProgressEvent) => void
  onProgressStepUpdate: (step: string) => void
  onSessionUpdate: (sessionId: string, updates: Partial<ReviewSession>) => void
  // Multi-agent specific callbacks
  onMultiAgentStatusUpdate?: (status: MultiAgentReviewStatus) => void
  reviewMode?: string
}

export const useWebSocketConnection = ({
  activeSession,
  onProgressEvent,
  onProgressStepUpdate,
  onSessionUpdate,
  onMultiAgentStatusUpdate,
  reviewMode
}: UseWebSocketConnectionProps) => {
  
  /**
   * Convert WebSocket events to legacy ProgressEvent format
   */
  const convertToProgressEvent = useCallback((event: AllWebSocketEvents): ProgressEvent => {
    const reviewId = 'review_id' in event ? event.review_id : 
                    'session_id' in event ? event.session_id : 'unknown'
    
    return {
      id: `${event.event_type}_${reviewId}_${Date.now()}`,
      type: event.event_type,
      message: event.message || event.event_type.replace(/_/g, ' '),
      timestamp: new Date(event.timestamp),
      data: event.data
    }
  }, [])

  /**
   * Handle Multi-Agent Events
   */
  const handleMultiAgentEvent = useCallback((event: MultiAgentWebSocketEvent) => {
    if (!activeSession || 
        !('review_id' in event) || 
        event.review_id !== activeSession.session_id) {
      return
    }

    const progressEvent = convertToProgressEvent(event)
    onProgressEvent(progressEvent)

    // Handle specific Multi-Agent events
    switch (event.event_type) {
      case 'review_started':
        onSessionUpdate(event.review_id, {
          status: 'running',
          progress: 5,
          progress_message: 'Multi-agent orchestration started'
        })
        onProgressStepUpdate('Multi-agent review orchestration started')
        break

      case 'agent_started':
        onProgressStepUpdate(`${event.agent_type} agent started`)
        break

      case 'agent_progress':
        const progress = event.data?.progress as number || 0
        onProgressStepUpdate(`${event.agent_type}: ${progress}% complete`)
        // Update overall session progress based on agent progress
        const overallProgress = Math.min(20 + (progress * 0.6), 95)
        onSessionUpdate(event.review_id, {
          progress: overallProgress,
          progress_message: `${event.agent_type}: ${event.data?.current_step || 'Processing'}`
        })
        break

      case 'agent_completed':
        onProgressStepUpdate(`${event.agent_type} agent completed successfully`)
        break

      case 'agent_failed':
        onProgressStepUpdate(`${event.agent_type} agent failed: ${event.data?.error_message || 'Unknown error'}`)
        break

      case 'review_completed':
        onSessionUpdate(event.review_id, {
          status: 'completed',
          progress: 100,
          progress_message: 'Multi-agent review completed!'
        })
        onProgressStepUpdate('Multi-agent review completed successfully!')
        break

      case 'review_failed':
        onSessionUpdate(event.review_id, {
          status: 'error',
          progress_message: `Review failed: ${event.data?.error_message || 'Unknown error'}`
        })
        onProgressStepUpdate(`Review failed: ${event.data?.error_message || 'Unknown error'}`)
        break

      case 'review_cancelled':
        onSessionUpdate(event.review_id, {
          status: 'error',
          progress_message: 'Review cancelled'
        })
        onProgressStepUpdate('Multi-agent review cancelled by user')
        break

      case 'status_update':
        if (onMultiAgentStatusUpdate && event.data) {
          onMultiAgentStatusUpdate(event.data as MultiAgentReviewStatus)
        }
        const statusProgress = event.data?.progress as number || 0
        onSessionUpdate(event.review_id, {
          progress: statusProgress,
          progress_message: `Status: ${event.data?.status || 'Processing'}`
        })
        break
    }
  }, [activeSession, convertToProgressEvent, onProgressEvent, onSessionUpdate, onProgressStepUpdate, onMultiAgentStatusUpdate])

  /**
   * Handle Legacy Events
   */
  const handleLegacyEvent = useCallback((event: LegacyWebSocketEvent) => {
    if (!activeSession || 
        !('session_id' in event) || 
        event.session_id !== activeSession.session_id) {
      return
    }

    const progressEvent = convertToProgressEvent(event)
    onProgressEvent(progressEvent)

    // Handle specific Legacy events
    switch (event.event_type) {
      case 'session_started':
        onSessionUpdate(event.session_id, {
          progress: 10,
          progress_message: 'Session initialized'
        })
        onProgressStepUpdate('Review session started')
        break

      case 'claude_thinking':
        onProgressStepUpdate(event.data?.thinking_step as string || 'Claude is analyzing...')
        break

      case 'tool_usage':
        const toolName = event.data?.tool_name as string || 'Unknown tool'
        const action = event.data?.action as string || 'Processing'
        const toolProgress = event.data?.progress as number || 30
        
        onSessionUpdate(event.session_id, {
          progress: Math.min(toolProgress, 85),
          progress_message: `Using ${toolName}`
        })
        onProgressStepUpdate(`${toolName}: ${action}`)
        break

      case 'claude_response_stream':
        onProgressStepUpdate(event.data?.chunk as string || 'Generating response...')
        break

      case 'structured_result':
        onSessionUpdate(event.session_id, {
          progress: 90,
          progress_message: 'Finalizing review'
        })
        onProgressStepUpdate('Structured analysis complete')
        break

      case 'phase3_started':
        onSessionUpdate(event.session_id, {
          progress: 85,
          progress_message: 'Creating tutorial documentation'
        })
        onProgressStepUpdate('Generating implementation summary...')
        break

      case 'phase3_completed':
        onProgressStepUpdate('Tutorial generation complete')
        break

      case 'review_metadata':
        onProgressStepUpdate('Review statistics updated')
        break

      case 'review_completed':
        onSessionUpdate(event.session_id, {
          status: 'completed',
          progress: 100,
          progress_message: 'Review completed!'
        })
        onProgressStepUpdate('Review completed successfully!')
        break

      case 'review_error':
        onSessionUpdate(event.session_id, {
          status: 'error',
          progress_message: `Error: ${event.data?.error_message || 'Unknown error'}`
        })
        onProgressStepUpdate(`Error: ${event.data?.error_message || 'Unknown error'}`)
        break

      case 'claude_progress':
        // Handle legacy claude_progress event for backward compatibility
        const subType = event.data?.type as string || 'unknown'
        const message = event.message || event.data?.message as string || 'Update received'
        
        onProgressStepUpdate(message)
        
        if (subType === 'session_started') {
          onSessionUpdate(event.session_id, { 
            progress_message: message,
            progress: 15
          })
        } else if (subType === 'turn_started') {
          const turn = event.data?.turn as number || 1
          onSessionUpdate(event.session_id, { 
            progress_message: `Turn ${turn}: Analyzing...`,
            progress: Math.min(20 + (turn * 5), 80)
          })
        } else if (subType === 'tool_use') {
          const tool = event.data?.tool as string || 'Unknown'
          onSessionUpdate(event.session_id, { 
            progress_message: `Using ${tool}: ${message}`,
            progress: Math.min(30 + (Math.random() * 20), 85)
          })
        } else if (subType === 'claude_response') {
          const preview = event.data?.preview as string || 'Claude is analyzing...'
          onSessionUpdate(event.session_id, { 
            progress_message: preview,
            progress: Math.min(60 + (Math.random() * 20), 90)
          })
        } else if (subType === 'review_completed') {
          onSessionUpdate(event.session_id, { 
            status: 'completed',
            progress: 100,
            progress_message: 'Review completed!'
          })
        }
        break
    }
  }, [activeSession, convertToProgressEvent, onProgressEvent, onSessionUpdate, onProgressStepUpdate])

  /**
   * Determine service type based on review mode and feature flags
   */
  const getServiceType = useCallback((): 'multi-agent' | 'legacy' | 'auto' => {
    // Check if multi-agent is explicitly requested
    if (reviewMode === 'multi_agent' || reviewMode === 'full') {
      return 'multi-agent'
    }
    
    // Use feature flags for automatic selection
    if (featureFlags.isMultiAgentEnabled()) {
      return 'auto' // Let enhanced hook decide
    }
    
    // Default to legacy
    return 'legacy'
  }, [reviewMode])

  /**
   * WebSocket Event Handlers
   */
  const eventHandlers: WebSocketEventHandlers = {
    onMultiAgentEvent: handleMultiAgentEvent,
    onLegacyEvent: handleLegacyEvent,
    onAnyEvent: (event) => {
      console.log('📡 WebSocket Event:', event.event_type, event)
    },
    onError: (error, event) => {
      console.error('❌ WebSocket Event Error:', error, event)
      if (activeSession) {
        onProgressEvent({
          id: `error_${Date.now()}`,
          type: 'error',
          message: `WebSocket error: ${error.message}`,
          timestamp: new Date(),
          data: { error: error.message, event }
        })
      }
    },
    onConnected: () => {
      console.log('✅ Enhanced WebSocket connected successfully')
    },
    onDisconnected: () => {
      console.log('❌ Enhanced WebSocket disconnected')
    }
  }

  /**
   * Use Enhanced WebSocket Hook
   */
  const enhancedWebSocket = useEnhancedWebSocket({
    serviceType: getServiceType(),
    reviewId: activeSession?.session_id,
    sessionId: activeSession?.session_id,
    reviewMode,
    handlers: eventHandlers,
    autoConnect: true,
    enableEventLogging: true,
    enableLatencyTracking: false
  })

  // Return socket-like interface for backward compatibility
  return {
    current: enhancedWebSocket.isConnected ? {
      connected: enhancedWebSocket.isConnected,
      close: enhancedWebSocket.disconnect,
      disconnect: enhancedWebSocket.disconnect
    } : null,
    // Additional enhanced features
    ...enhancedWebSocket
  }
}