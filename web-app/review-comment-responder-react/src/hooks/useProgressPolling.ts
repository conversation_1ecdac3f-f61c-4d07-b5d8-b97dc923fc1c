import { useEffect, useRef } from 'react'
import { codeReviewerService } from '../services/codeReviewer/CodeReviewerService'
import type { ReviewSession } from '../types/enhanced-review'

interface UseProgressPollingProps {
  activeSession: ReviewSession | null
  onSessionUpdate: (sessionId: string, updates: Partial<ReviewSession>) => void
}

export const useProgressPolling = ({
  activeSession,
  onSessionUpdate
}: UseProgressPollingProps) => {
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (activeSession && (
      activeSession.status === 'initializing' || 
      activeSession.status === 'running' ||
      (activeSession.status === 'completed' && !activeSession.results)
    )) {
      // Polling function
      const pollProgress = async () => {
        try {
          console.log('🔄 Polling review status for session:', activeSession.session_id)
          const response = await codeReviewerService.getReviewStatus(activeSession.session_id)
          console.log('📥 Poll response:', response)
          
          if (response.success && response.session) {
            console.log('✅ Poll successful - updating session')
            console.log('📊 Session status:', response.session.status)
            console.log('📊 Session results:', response.session.results ? 'Available' : 'Not available')
            
            onSessionUpdate(activeSession.session_id, {
              status: response.session.status,
              progress: response.session.progress,
              progress_message: response.session.progress_message,
              results: response.session.results
            })
            
            // Stop polling if we have results
            if (response.session.results && response.session.status === 'completed') {
              console.log('🛑 Results available - stopping polling')
              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            }
          } else {
            console.log('❌ Poll failed or no session data')
          }
        } catch (error) {
          console.error('Failed to poll progress:', error)
        }
      }

      // Initial poll
      pollProgress()

      // Set up interval polling every 2 seconds
      pollIntervalRef.current = setInterval(pollProgress, 2000)

      // Cleanup on unmount or when session completes
      return () => {
        if (pollIntervalRef.current) {
          clearInterval(pollIntervalRef.current)
          pollIntervalRef.current = null
        }
      }
    }
  }, [activeSession?.session_id, activeSession?.status, activeSession?.results, onSessionUpdate])
}