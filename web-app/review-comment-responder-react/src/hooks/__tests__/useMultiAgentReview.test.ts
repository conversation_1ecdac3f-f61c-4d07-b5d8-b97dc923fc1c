/**
 * Unit Tests for useMultiAgentReview Hook
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useMultiAgentReview } from '../useMultiAgentReview'
import type { MultiAgentReviewRequest, MultiAgentReviewResponse, EnhancedReviewResults } from '../../types/multi-agent'

// Mock the MultiAgentReviewService
const mockMultiAgentService = {
  startParallelReview: vi.fn(),
  getReviewStatus: vi.fn(),
  getReviewResults: vi.fn(),
  cancelReview: vi.fn()
}

vi.mock('../../services/multiAgent/MultiAgentReviewService', () => ({
  MultiAgentReviewService: vi.fn(() => mockMultiAgentService)
}))

// Mock the Enhanced WebSocket
const mockEnhancedWebSocket = {
  isConnected: false,
  isConnecting: false,
  hasError: false,
  connect: vi.fn(),
  disconnect: vi.fn(),
  sendMessage: vi.fn(),
  connectionStatus: {
    connected: false,
    connecting: false,
    error: null,
    lastHeartbeat: null,
    reconnectAttempts: 0,
    connectionQuality: 'unknown' as const
  },
  currentService: null as 'multi-agent' | 'legacy' | null,
  eventHistory: [],
  reconnectCount: 0,
  lastEventTime: null,
  getConnectionQuality: vi.fn(() => 'unknown' as const),
  currentServiceUrl: null,
  recentEvents: []
}

vi.mock('../useEnhancedWebSocket', () => ({
  useEnhancedWebSocket: vi.fn(() => mockEnhancedWebSocket)
}))

// Mock feature flags
vi.mock('../../config/featureFlags', () => ({
  featureFlags: {
    isMultiAgentEnabled: vi.fn(() => true),
    getWebSocketConfig: vi.fn(() => ({
      autoReconnect: true,
      maxReconnectAttempts: 3,
      reconnectDelay: 1000,
      heartbeatInterval: 30000
    }))
  }
}))

describe('useMultiAgentReview', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    vi.clearAllTimers()
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      expect(result.current.state.review_id).toBeNull()
      expect(result.current.state.session_id).toBeNull()
      expect(result.current.state.status).toBe('started')
      expect(result.current.state.progress).toBe(0)
      expect(result.current.state.is_loading).toBe(false)
      expect(result.current.state.is_starting).toBe(false)
      expect(result.current.isActive).toBe(false)
      expect(result.current.isCompleted).toBe(false)
      expect(result.current.isFailed).toBe(false)
    })

    it('should initialize with custom config', () => {
      const config = {
        enable_debug_logs: true,
        polling_interval: 3000,
        auto_fetch_results: false
      }

      const { result } = renderHook(() => useMultiAgentReview(config))

      expect(result.current.state).toBeDefined()
      // Config affects behavior, not initial state
    })
  })

  describe('startReview', () => {
    it('should start a review successfully', async () => {
      const mockResponse: MultiAgentReviewResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'started',
        initial_agent_statuses: {
          acceptance_criteria: {
            agent_type: 'acceptance_criteria',
            status: 'pending',
            progress: 0
          },
          bug_detection: {
            agent_type: 'bug_detection',
            status: 'pending',
            progress: 0
          }
        },
        estimated_completion_time: 300,
        websocket_session_id: 'ws-789',
        created_at: '2024-01-01T10:00:00Z'
      }

      mockMultiAgentService.startParallelReview.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => useMultiAgentReview())

      const request: MultiAgentReviewRequest = {
        branch_name: 'feature/test',
        repository_path: '/test/repo',
        pr_url: 'https://github.com/test/repo/pull/1',
        review_mode: 'parallel',
        agent_config: {
          enabled_agents: ['acceptance_criteria', 'bug_detection'],
          timeout_seconds: 300
        }
      }

      await act(async () => {
        await result.current.startReview(request)
      })

      expect(mockMultiAgentService.startParallelReview).toHaveBeenCalledWith(request)
      expect(result.current.state.review_id).toBe('review-123')
      expect(result.current.state.session_id).toBe('session-456')
      expect(result.current.state.status).toBe('started')
      expect(result.current.state.is_starting).toBe(false)
      expect(result.current.state.is_loading).toBe(false)
    })

    it('should handle start review failure', async () => {
      const error = new Error('Network error')
      mockMultiAgentService.startParallelReview.mockRejectedValue(error)

      const { result } = renderHook(() => useMultiAgentReview())

      const request: MultiAgentReviewRequest = {
        branch_name: 'feature/test',
        repository_path: '/test/repo',
        pr_url: 'https://github.com/test/repo/pull/1',
        review_mode: 'parallel'
      }

      await act(async () => {
        await result.current.startReview(request)
      })

      expect(result.current.state.status).toBe('failed')
      expect(result.current.state.error).toBeDefined()
      expect(result.current.state.error?.error_message).toBe('Network error')
      expect(result.current.isFailed).toBe(true)
    })

    it('should call onReviewStarted callback', async () => {
      const onReviewStarted = vi.fn()
      const mockResponse: MultiAgentReviewResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'started',
        initial_agent_statuses: {},
        estimated_completion_time: 300,
        websocket_session_id: 'ws-789',
        created_at: '2024-01-01T10:00:00Z'
      }

      mockMultiAgentService.startParallelReview.mockResolvedValue(mockResponse)

      const { result } = renderHook(() => 
        useMultiAgentReview({ on_review_started: onReviewStarted })
      )

      const request: MultiAgentReviewRequest = {
        branch_name: 'feature/test',
        repository_path: '/test/repo',
        pr_url: 'https://github.com/test/repo/pull/1',
        review_mode: 'parallel'
      }

      await act(async () => {
        await result.current.startReview(request)
      })

      expect(onReviewStarted).toHaveBeenCalledWith(mockResponse)
    })
  })

  describe('refreshStatus', () => {
    it('should refresh review status', async () => {
      const mockStatus = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'running' as const,
        progress: 45,
        agent_statuses: {
          acceptance_criteria: {
            agent_type: 'acceptance_criteria',
            status: 'completed' as const,
            progress: 100
          },
          bug_detection: {
            agent_type: 'bug_detection',
            status: 'running' as const,
            progress: 75
          }
        },
        active_agents: ['bug_detection'],
        completed_agents: ['acceptance_criteria'],
        failed_agents: [],
        started_at: '2024-01-01T10:00:00Z'
      }

      mockMultiAgentService.getReviewStatus.mockResolvedValue(mockStatus)

      const { result } = renderHook(() => useMultiAgentReview())

      // Set initial state with review_id
      act(() => {
        result.current.state.review_id = 'review-123'
      })

      await act(async () => {
        await result.current.refreshStatus()
      })

      expect(mockMultiAgentService.getReviewStatus).toHaveBeenCalledWith('review-123')
      expect(result.current.state.status).toBe('running')
      expect(result.current.state.progress).toBe(45)
      expect(result.current.state.active_agents).toEqual(['bug_detection'])
      expect(result.current.state.completed_agents).toEqual(['acceptance_criteria'])
    })

    it('should handle refresh status failure', async () => {
      const error = new Error('API error')
      mockMultiAgentService.getReviewStatus.mockRejectedValue(error)

      const { result } = renderHook(() => useMultiAgentReview())

      // Set initial state with review_id
      act(() => {
        result.current.state.review_id = 'review-123'
      })

      await act(async () => {
        await result.current.refreshStatus()
      })

      expect(result.current.state.last_error).toContain('Failed to refresh status')
      expect(result.current.hasError).toBe(true)
    })
  })

  describe('fetchResults', () => {
    it('should fetch review results successfully', async () => {
      const mockResults: EnhancedReviewResults = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'completed',
        overall_results: {
          summary: 'Review completed successfully',
          priority_findings: [],
          execution_metrics: {
            total_execution_time: 120,
            parallel_efficiency: 0.85,
            agent_performance: {}
          }
        },
        agent_results: {},
        reports: {
          markdown: '# Review Report',
          json: '{}'
        },
        context_metadata: {
          files_analyzed: [],
          context_size: 1000,
          git_history_included: true,
          related_files_included: false
        },
        created_at: '2024-01-01T10:00:00Z',
        completed_at: '2024-01-01T10:02:00Z'
      }

      mockMultiAgentService.getReviewResults.mockResolvedValue(mockResults)

      const { result } = renderHook(() => useMultiAgentReview())

      // Set initial state with review_id
      act(() => {
        result.current.state.review_id = 'review-123'
      })

      await act(async () => {
        await result.current.fetchResults()
      })

      expect(mockMultiAgentService.getReviewResults).toHaveBeenCalledWith('review-123')
      expect(result.current.state.results).toEqual(mockResults)
      expect(result.current.state.status).toBe('completed')
      expect(result.current.isCompleted).toBe(true)
    })
  })

  describe('cancelReview', () => {
    it('should cancel review successfully', async () => {
      mockMultiAgentService.cancelReview.mockResolvedValue(undefined)

      const { result } = renderHook(() => useMultiAgentReview())

      // Set initial state with review_id
      act(() => {
        result.current.state.review_id = 'review-123'
      })

      await act(async () => {
        await result.current.cancelReview()
      })

      expect(mockMultiAgentService.cancelReview).toHaveBeenCalledWith('review-123')
      expect(result.current.state.status).toBe('cancelled')
    })
  })

  describe('agent utilities', () => {
    it('should provide correct agent status information', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      // Set up some agent statuses
      act(() => {
        result.current.state.agent_statuses = {
          acceptance_criteria: {
            agent_type: 'acceptance_criteria',
            status: 'completed',
            progress: 100
          },
          bug_detection: {
            agent_type: 'bug_detection',
            status: 'running',
            progress: 75
          },
          security_analysis: {
            agent_type: 'security_analysis',
            status: 'failed',
            progress: 30
          },
          summary: {
            agent_type: 'summary',
            status: 'pending',
            progress: 0
          },
          logic_analysis: {
            agent_type: 'logic_analysis',
            status: 'pending',
            progress: 0
          },
          quality_analysis: {
            agent_type: 'quality_analysis',
            status: 'pending',
            progress: 0
          },
          architecture_analysis: {
            agent_type: 'architecture_analysis',
            status: 'pending',
            progress: 0
          }
        }
        result.current.state.active_agents = ['bug_detection']
        result.current.state.completed_agents = ['acceptance_criteria']
        result.current.state.failed_agents = ['security_analysis']
      })

      expect(result.current.getAgentStatus('acceptance_criteria')?.status).toBe('completed')
      expect(result.current.getAgentProgress('bug_detection')).toBe(75)
      expect(result.current.isAgentActive('bug_detection')).toBe(true)
      expect(result.current.isAgentCompleted('acceptance_criteria')).toBe(true)
      expect(result.current.isAgentFailed('security_analysis')).toBe(true)
      
      expect(result.current.activeAgentCount).toBe(1)
      expect(result.current.completedAgentCount).toBe(1)
      expect(result.current.failedAgentCount).toBe(1)
    })
  })

  describe('performance utilities', () => {
    it('should calculate execution time correctly', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      const startTime = '2024-01-01T10:00:00Z'
      const endTime = '2024-01-01T10:02:00Z'

      act(() => {
        result.current.state.started_at = startTime
        result.current.state.completed_at = endTime
      })

      expect(result.current.getExecutionTime()).toBe(120) // 2 minutes in seconds
    })

    it('should return null for execution time when no start time', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      expect(result.current.getExecutionTime()).toBeNull()
    })
  })

  describe('error handling', () => {
    it('should clear errors', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      // Set an error
      act(() => {
        result.current.state.last_error = 'Test error'
      })

      expect(result.current.hasError).toBe(true)
      expect(result.current.getLastError()).toBe('Test error')

      act(() => {
        result.current.clearError()
      })

      expect(result.current.hasError).toBe(false)
      expect(result.current.getLastError()).toBeNull()
    })
  })

  describe('reset functionality', () => {
    it('should reset state to default', () => {
      const { result } = renderHook(() => useMultiAgentReview())

      // Modify state
      act(() => {
        result.current.state.review_id = 'review-123'
        result.current.state.status = 'running'
        result.current.state.progress = 50
      })

      expect(result.current.state.review_id).toBe('review-123')

      act(() => {
        result.current.reset()
      })

      expect(result.current.state.review_id).toBeNull()
      expect(result.current.state.status).toBe('started')
      expect(result.current.state.progress).toBe(0)
    })
  })

  describe('polling functionality', () => {
    it('should start polling when enabled', async () => {
      const { result } = renderHook(() => 
        useMultiAgentReview({ polling_interval: 1000 })
      )

      const mockResponse: MultiAgentReviewResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'running',
        initial_agent_statuses: {},
        estimated_completion_time: 300,
        websocket_session_id: 'ws-789',
        created_at: '2024-01-01T10:00:00Z'
      }

      mockMultiAgentService.startParallelReview.mockResolvedValue(mockResponse)
      mockMultiAgentService.getReviewStatus.mockResolvedValue({
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'running',
        progress: 50,
        agent_statuses: {},
        active_agents: [],
        completed_agents: [],
        failed_agents: [],
        started_at: '2024-01-01T10:00:00Z'
      })

      const request: MultiAgentReviewRequest = {
        branch_name: 'feature/test',
        repository_path: '/test/repo',
        pr_url: 'https://github.com/test/repo/pull/1',
        review_mode: 'parallel'
      }

      await act(async () => {
        await result.current.startReview(request)
      })

      // Fast-forward timers to trigger polling
      act(() => {
        vi.advanceTimersByTime(1000)
      })

      expect(mockMultiAgentService.getReviewStatus).toHaveBeenCalled()
    })
  })

  describe('WebSocket integration', () => {
    it('should handle WebSocket events correctly', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')
      
      renderHook(() => useMultiAgentReview())

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'multi-agent',
          reviewMode: 'multi_agent',
          handlers: expect.objectContaining({
            onMultiAgentEvent: expect.any(Function),
            onLegacyEvent: expect.any(Function),
            onAnyEvent: expect.any(Function),
            onError: expect.any(Function),
            onConnected: expect.any(Function),
            onDisconnected: expect.any(Function)
          }),
          autoConnect: true
        })
      )
    })
  })
})