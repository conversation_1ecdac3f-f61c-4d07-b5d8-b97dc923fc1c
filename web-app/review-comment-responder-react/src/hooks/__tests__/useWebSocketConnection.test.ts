/**
 * Unit Tests for useWebSocketConnection Hook (Refactored)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useWebSocketConnection } from '../useWebSocketConnection'
import type { ReviewSession } from '../../types/enhanced-review'
// import type { MultiAgentReviewStatus } from '../../components/MultiAgentProgressTracker'

// Mock useEnhancedWebSocket
const mockEnhancedWebSocket = {
  isConnected: false,
  isConnecting: false,
  hasError: false,
  connect: vi.fn(),
  disconnect: vi.fn(),
  sendMessage: vi.fn(),
  connectionStatus: {
    connected: false,
    connecting: false,
    error: null,
    lastHeartbeat: null,
    reconnectAttempts: 0,
    connectionQuality: 'unknown' as const
  },
  currentService: null as 'multi-agent' | 'legacy' | null,
  eventHistory: [],
  reconnectCount: 0,
  lastEventTime: null,
  getConnectionQuality: vi.fn(() => 'unknown' as const),
  currentServiceUrl: null,
  recentEvents: []
}

vi.mock('../useEnhancedWebSocket', () => ({
  useEnhancedWebSocket: vi.fn(() => mockEnhancedWebSocket)
}))

// Mock feature flags
vi.mock('../../config/featureFlags', () => ({
  featureFlags: {
    isMultiAgentEnabled: vi.fn(() => true)
  }
}))

describe('useWebSocketConnection (Refactored)', () => {
  let mockActiveSession: ReviewSession
  let mockProgressEvent: ReturnType<typeof vi.fn>
  let mockProgressStepUpdate: ReturnType<typeof vi.fn>
  let mockSessionUpdate: ReturnType<typeof vi.fn>
  let mockMultiAgentStatusUpdate: ReturnType<typeof vi.fn>

  beforeEach(() => {
    mockActiveSession = {
      session_id: 'test-session-123',
      branch_name: 'test-branch',
      status: 'initializing' as const,
      progress: 0,
      progress_message: '',
      created_at: '2024-01-01T10:00:00Z'
    } as ReviewSession

    mockProgressEvent = vi.fn()
    mockProgressStepUpdate = vi.fn()
    mockSessionUpdate = vi.fn()
    mockMultiAgentStatusUpdate = vi.fn()

    // Reset enhanced WebSocket mock
    mockEnhancedWebSocket.isConnected = false
    mockEnhancedWebSocket.isConnecting = false
    mockEnhancedWebSocket.hasError = false
    mockEnhancedWebSocket.currentService = null
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('initialization', () => {
    it('should initialize with enhanced WebSocket hook', () => {
      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          onMultiAgentStatusUpdate: mockMultiAgentStatusUpdate,
          reviewMode: 'multi_agent'
        })
      )

      expect(result.current.isConnected).toBe(false)
      expect(result.current.isConnecting).toBe(false)
      expect(result.current.hasError).toBe(false)
    })

    it('should return null socket reference when disconnected', () => {
      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      expect(result.current.current).toBeNull()
    })

    it('should return socket-like interface when connected', () => {
      mockEnhancedWebSocket.isConnected = true

      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      expect(result.current.current).toBeTruthy()
      expect(result.current.current?.connected).toBe(true)
      expect(typeof result.current.current?.close).toBe('function')
      expect(typeof result.current.current?.disconnect).toBe('function')
    })
  })

  describe('service type selection', () => {
    it('should select multi-agent service for multi_agent reviewMode', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          reviewMode: 'multi_agent'
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'multi-agent'
        })
      )
    })

    it('should select multi-agent service for parallel reviewMode', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          reviewMode: 'parallel'
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'multi-agent'
        })
      )
    })

    it('should use auto selection when feature flags are enabled', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          reviewMode: 'standard'
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'auto'
        })
      )
    })

    it('should use legacy service when multi-agent is disabled', () => {
      const { featureFlags } = require('../../config/featureFlags')
      featureFlags.isMultiAgentEnabled.mockReturnValue(false)

      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          reviewMode: 'standard'
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          serviceType: 'legacy'
        })
      )
    })
  })

  describe('event handling configuration', () => {
    it('should configure enhanced WebSocket with correct parameters', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate,
          reviewMode: 'multi_agent'
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith({
        serviceType: 'multi-agent',
        reviewId: mockActiveSession.session_id,
        sessionId: mockActiveSession.session_id,
        reviewMode: 'multi_agent',
        handlers: expect.objectContaining({
          onMultiAgentEvent: expect.any(Function),
          onLegacyEvent: expect.any(Function),
          onAnyEvent: expect.any(Function),
          onError: expect.any(Function),
          onConnected: expect.any(Function),
          onDisconnected: expect.any(Function)
        }),
        autoConnect: true,
        enableEventLogging: true,
        enableLatencyTracking: false
      })
    })

    it('should handle null activeSession gracefully', () => {
      const { useEnhancedWebSocket } = require('../useEnhancedWebSocket')

      renderHook(() =>
        useWebSocketConnection({
          activeSession: null,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      expect(useEnhancedWebSocket).toHaveBeenCalledWith(
        expect.objectContaining({
          reviewId: undefined,
          sessionId: undefined
        })
      )
    })
  })

  describe('backward compatibility', () => {
    it('should expose enhanced WebSocket properties for backward compatibility', () => {
      mockEnhancedWebSocket.connectionStatus.connected = true
      mockEnhancedWebSocket.isConnected = true
      mockEnhancedWebSocket.currentService = 'multi-agent'

      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      // Should expose all enhanced WebSocket properties
      expect(result.current.connectionStatus).toBe(mockEnhancedWebSocket.connectionStatus)
      expect(result.current.currentService).toBe(mockEnhancedWebSocket.currentService)
      expect(result.current.isConnected).toBe(mockEnhancedWebSocket.isConnected)
      expect(result.current.connect).toBe(mockEnhancedWebSocket.connect)
      expect(result.current.disconnect).toBe(mockEnhancedWebSocket.disconnect)
    })

    it('should maintain socket-like interface for legacy components', () => {
      mockEnhancedWebSocket.isConnected = true

      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      const socketInterface = result.current.current
      expect(socketInterface).toBeTruthy()
      expect(socketInterface?.connected).toBe(true)
      
      // Test methods
      act(() => {
        socketInterface?.close()
      })
      expect(mockEnhancedWebSocket.disconnect).toHaveBeenCalled()

      act(() => {
        socketInterface?.disconnect()
      })
      expect(mockEnhancedWebSocket.disconnect).toHaveBeenCalledTimes(2)
    })
  })

  describe('error handling', () => {
    it('should handle connection errors gracefully', () => {
      mockEnhancedWebSocket.hasError = true
      mockEnhancedWebSocket.connectionStatus.error = null

      const { result } = renderHook(() =>
        useWebSocketConnection({
          activeSession: mockActiveSession,
          onProgressEvent: mockProgressEvent,
          onProgressStepUpdate: mockProgressStepUpdate,
          onSessionUpdate: mockSessionUpdate
        })
      )

      expect(result.current.hasError).toBe(true)
      expect(result.current.connectionStatus.error).toBe('Connection failed')
    })
  })
})