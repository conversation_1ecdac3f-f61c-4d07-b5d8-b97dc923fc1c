/**
 * Unit Tests for useEnhancedWebSocket Hook
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useEnhancedWebSocket } from '../useEnhancedWebSocket'
import type { WebSocketEventHandlers } from '../../types/websocket-events'

// Mock socket.io-client
const mockSocket = {
  connected: false,
  connect: vi.fn(),
  disconnect: vi.fn(),
  close: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn()
}

const mockIo = vi.fn(() => mockSocket)

vi.mock('socket.io-client', () => ({
  io: mockIo
}))

// Mock feature flags
vi.mock('../../config/featureFlags', () => ({
  featureFlags: {
    isMultiAgentEnabled: vi.fn(() => true),
    getWebSocketConfig: vi.fn(() => ({
      autoReconnect: true,
      maxReconnectAttempts: 5,
      reconnectDelay: 1000,
      heartbeatInterval: 30000
    }))
  }
}))

// Mock service endpoints
vi.mock('../../config/serviceEndpoints', () => ({
  ServiceEndpoints: {
    MULTI_AGENT: {
      WEBSOCKET_URL: 'ws://localhost:5000',
      TIMEOUT: 10000
    },
    LEGACY_CODE_REVIEWER: {
      BASE_URL: 'http://localhost:5002',
      TIMEOUT: 15000
    }
  }
}))

describe('useEnhancedWebSocket', () => {
  let mockHandlers: WebSocketEventHandlers

  beforeEach(() => {
    mockHandlers = {
      onMultiAgentEvent: vi.fn(),
      onLegacyEvent: vi.fn(),
      onAnyEvent: vi.fn(),
      onError: vi.fn(),
      onConnected: vi.fn(),
      onDisconnected: vi.fn(),
      onReviewStarted: vi.fn(),
      onReviewCompleted: vi.fn(),
      onAgentStarted: vi.fn(),
      onAgentProgress: vi.fn(),
      onAgentCompleted: vi.fn()
    }

    // Reset mocks
    mockSocket.connected = false
    mockSocket.on.mockReset()
    mockSocket.emit.mockReset()
    mockSocket.close.mockReset()
    mockIo.mockReset()
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('initialization', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      expect(result.current.connectionStatus.connected).toBe(false)
      expect(result.current.connectionStatus.connecting).toBe(false)
      expect(result.current.connectionStatus.error).toBeNull()
      expect(result.current.currentService).toBeNull()
      expect(result.current.isConnected).toBe(false)
      expect(result.current.isConnecting).toBe(false)
      expect(result.current.hasError).toBe(false)
    })

    it('should auto-connect when autoConnect is true', () => {
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: true
        })
      )

      expect(mockIo).toHaveBeenCalledWith('ws://localhost:5000', expect.any(Object))
    })

    it('should not auto-connect when autoConnect is false', () => {
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      expect(mockIo).not.toHaveBeenCalled()
    })
  })

  describe('service selection', () => {
    it('should connect to multi-agent service when specified', () => {
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: true
        })
      )

      expect(mockIo).toHaveBeenCalledWith('ws://localhost:5000', expect.any(Object))
    })

    it('should connect to legacy service when specified', () => {
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'legacy',
          handlers: mockHandlers,
          autoConnect: true
        })
      )

      expect(mockIo).toHaveBeenCalledWith('http://localhost:5002', expect.any(Object))
    })

    it('should auto-select multi-agent service for parallel review mode', () => {
      renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'auto',
          reviewMode: 'parallel',
          handlers: mockHandlers,
          autoConnect: true
        })
      )

      expect(mockIo).toHaveBeenCalledWith('ws://localhost:5000', expect.any(Object))
    })
  })

  describe('connection management', () => {
    it('should handle successful connection', async () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      // Get the connect event handler
      let connectHandler: () => void
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'connect') {
          connectHandler = handler
        }
      })

      act(() => {
        result.current.connect()
      })

      // Simulate successful connection
      mockSocket.connected = true
      act(() => {
        connectHandler!()
      })

      expect(result.current.connectionStatus.connected).toBe(true)
      expect(result.current.connectionStatus.connecting).toBe(false)
      expect(result.current.connectionStatus.error).toBeNull()
      expect(result.current.currentService).toBe('multi-agent')
      expect(mockHandlers.onConnected).not.toHaveBeenCalled() // Only called for 'connected' event
    })

    it('should handle connection error', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let errorHandler: (error: Error) => void
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'connect_error') {
          errorHandler = handler
        }
      })

      act(() => {
        result.current.connect()
      })

      const testError = new Error('Connection failed')
      act(() => {
        errorHandler!(testError)
      })

      expect(result.current.connectionStatus.connected).toBe(false)
      expect(result.current.connectionStatus.error).toBe('Connection failed')
      expect(result.current.hasError).toBe(true)
    })

    it('should handle disconnection', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let disconnectHandler: (reason: string) => void
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'disconnect') {
          disconnectHandler = handler
        }
      })

      act(() => {
        result.current.connect()
      })

      act(() => {
        disconnectHandler!('transport close')
      })

      expect(result.current.connectionStatus.connected).toBe(false)
      expect(result.current.connectionStatus.error).toBe('Disconnected: transport close')
    })

    it('should manually disconnect', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      act(() => {
        result.current.connect()
      })

      act(() => {
        result.current.disconnect()
      })

      expect(mockSocket.close).toHaveBeenCalled()
      expect(result.current.currentService).toBeNull()
    })
  })

  describe('event handling', () => {
    it('should process multi-agent events correctly', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'review-123',
          handlers: mockHandlers,
          autoConnect: false,
          enableEventLogging: true
        })
      )

      let eventHandlers: Record<string, (data: any) => void> = {}
      mockSocket.on.mockImplementation((event, handler) => {
        eventHandlers[event] = handler
      })

      act(() => {
        result.current.connect()
      })

      // Simulate review started event
      const reviewStartedData = {
        review_id: 'review-123',
        session_id: 'session-456',
        data: {
          agents_count: 7,
          estimated_duration: 300,
          enabled_agents: ['bug_detection', 'security_analysis']
        }
      }

      act(() => {
        eventHandlers['review_started'](reviewStartedData)
      })

      expect(mockHandlers.onMultiAgentEvent).toHaveBeenCalled()
      expect(mockHandlers.onReviewStarted).toHaveBeenCalled()
      expect(mockHandlers.onAnyEvent).toHaveBeenCalled()
      expect(result.current.eventHistory).toHaveLength(1)
    })

    it('should process agent progress events', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'review-123',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let eventHandlers: Record<string, (data: any) => void> = {}
      mockSocket.on.mockImplementation((event, handler) => {
        eventHandlers[event] = handler
      })

      act(() => {
        result.current.connect()
      })

      // Simulate agent progress event
      const agentProgressData = {
        review_id: 'review-123',
        agent_type: 'bug_detection',
        data: {
          progress: 45,
          current_step: 'Analyzing code patterns',
          estimated_remaining_time: 120
        }
      }

      act(() => {
        eventHandlers['agent_progress'](agentProgressData)
      })

      expect(mockHandlers.onAgentProgress).toHaveBeenCalledWith(
        expect.objectContaining({
          event_type: 'agent_progress',
          review_id: 'review-123',
          agent_type: 'bug_detection'
        })
      )
    })

    it('should process legacy events correctly', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'legacy',
          sessionId: 'session-123',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let eventHandlers: Record<string, (data: any) => void> = {}
      mockSocket.on.mockImplementation((event, handler) => {
        eventHandlers[event] = handler
      })

      act(() => {
        result.current.connect()
      })

      // Simulate claude thinking event
      const claudeThinkingData = {
        session_id: 'session-123',
        data: {
          thinking_step: 'Analyzing code structure',
          complexity_level: 'medium'
        }
      }

      act(() => {
        eventHandlers['claude_thinking'](claudeThinkingData)
      })

      expect(mockHandlers.onLegacyEvent).toHaveBeenCalled()
      expect(mockHandlers.onAnyEvent).toHaveBeenCalled()
    })

    it('should handle event processing errors gracefully', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: {
            ...mockHandlers,
            onMultiAgentEvent: vi.fn(() => {
              throw new Error('Handler error')
            })
          },
          autoConnect: false
        })
      )

      let eventHandlers: Record<string, (data: any) => void> = {}
      mockSocket.on.mockImplementation((event, handler) => {
        eventHandlers[event] = handler
      })

      act(() => {
        result.current.connect()
      })

      // Simulate event that will cause handler to throw
      act(() => {
        eventHandlers['review_started']({
          review_id: 'test',
          session_id: 'test'
        })
      })

      expect(mockHandlers.onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.any(Object)
      )
    })
  })

  describe('message sending', () => {
    it('should send messages when connected', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      mockSocket.connected = true

      act(() => {
        result.current.connect()
      })

      const success = result.current.sendMessage('test_event', { test: 'data' })

      expect(success).toBe(true)
      expect(mockSocket.emit).toHaveBeenCalledWith('test_event', { test: 'data' })
    })

    it('should not send messages when disconnected', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      mockSocket.connected = false

      const success = result.current.sendMessage('test_event', { test: 'data' })

      expect(success).toBe(false)
      expect(mockSocket.emit).not.toHaveBeenCalled()
    })
  })

  describe('connection quality assessment', () => {
    it('should assess connection quality based on latency', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false,
          enableLatencyTracking: true
        })
      )

      // Simulate connected state with low latency
      act(() => {
        result.current.connect()
      })

      // Update connection status with latency
      act(() => {
        // This would normally be done internally by the hook
        // For testing, we'll simulate the effect
      })

      expect(result.current.getConnectionQuality()).toBe('unknown') // No latency data yet
    })

    it('should return poor quality when disconnected', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      expect(result.current.getConnectionQuality()).toBe('poor')
    })
  })

  describe('room management', () => {
    it('should join multi-agent review room when connected', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          reviewId: 'review-123',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let connectHandler: () => void
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'connect') {
          connectHandler = handler
        }
      })

      act(() => {
        result.current.connect()
      })

      mockSocket.connected = true
      act(() => {
        connectHandler!()
      })

      expect(mockSocket.emit).toHaveBeenCalledWith('join_review_room', {
        review_id: 'review-123'
      })
    })

    it('should join legacy session room when connected', () => {
      const { result } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'legacy',
          sessionId: 'session-123',
          handlers: mockHandlers,
          autoConnect: false
        })
      )

      let connectHandler: () => void
      mockSocket.on.mockImplementation((event, handler) => {
        if (event === 'connect') {
          connectHandler = handler
        }
      })

      act(() => {
        result.current.connect()
      })

      mockSocket.connected = true
      act(() => {
        connectHandler!()
      })

      expect(mockSocket.emit).toHaveBeenCalledWith('join_session_room', {
        session_id: 'session-123'
      })
    })
  })

  describe('cleanup', () => {
    it('should cleanup on unmount', () => {
      const { unmount } = renderHook(() => 
        useEnhancedWebSocket({
          serviceType: 'multi-agent',
          handlers: mockHandlers,
          autoConnect: true
        })
      )

      unmount()

      expect(mockSocket.close).toHaveBeenCalled()
    })
  })
})