/**
 * Enhanced WebSocket Hook
 * Unified WebSocket management for both Multi-Agent and Legacy services
 */

import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import type {
  AllWebSocketEvents,
  MultiAgentWebSocketEvent,
  LegacyWebSocketEvent,
  WebSocketEventHandlers,
  WebSocketConfig,
  WebSocketConnectionStatus
} from '../types/websocket-events'
import { 
  isMultiAgentEvent, 
  isLegacyEvent, 
  isConnectionEvent,
  WebSocketEventUtils
} from '../types/websocket-events'
import { featureFlags } from '../config/featureFlags'
import { ServiceEndpoints } from '../config/serviceEndpoints'

/**
 * Enhanced WebSocket Hook Configuration
 */
interface UseEnhancedWebSocketConfig {
  // Service selection
  serviceType: 'multi-agent' | 'legacy' | 'auto'
  
  // Review context
  reviewId?: string
  sessionId?: string
  reviewMode?: string
  
  // Event handlers
  handlers: WebSocketEventHandlers
  
  // Connection options
  autoConnect?: boolean
  reconnectOnFailure?: boolean
  
  // Feature flags
  enableHeartbeat?: boolean
  enableEventLogging?: boolean
  enableLatencyTracking?: boolean
}

/**
 * Enhanced WebSocket State
 */
interface EnhancedWebSocketState {
  connectionStatus: WebSocketConnectionStatus
  currentService: 'multi-agent' | 'legacy' | null
  eventHistory: AllWebSocketEvents[]
  reconnectCount: number
  lastEventTime: number | null
}

/**
 * Enhanced WebSocket Hook
 */
export const useEnhancedWebSocket = ({
  serviceType,
  reviewId,
  sessionId,
  reviewMode,
  handlers,
  autoConnect = true,
  reconnectOnFailure: _reconnectOnFailure = true,
  enableHeartbeat = true,
  enableEventLogging = false,
  enableLatencyTracking = false
}: UseEnhancedWebSocketConfig) => {
  
  const socketRef = useRef<Socket | null>(null)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const latencyTrackingRef = useRef<{ startTime: number; eventType: string } | null>(null)
  
  const [state, setState] = useState<EnhancedWebSocketState>({
    connectionStatus: {
      connected: false,
      connecting: false,
      error: null,
      lastHeartbeat: null,
      reconnectAttempts: 0,
      connectionQuality: 'unknown'
    },
    currentService: null,
    eventHistory: [],
    reconnectCount: 0,
    lastEventTime: null
  })

  /**
   * Determine which service to connect to
   */
  const determineService = useCallback((): 'multi-agent' | 'legacy' => {
    if (serviceType === 'auto') {
      // Use feature flags and service availability to determine
      if (featureFlags.isMultiAgentEnabled() && 
          (reviewMode === 'multi_agent' || reviewMode === 'full')) {
        return 'multi-agent'
      }
      return 'legacy'
    }
    return serviceType === 'multi-agent' ? 'multi-agent' : 'legacy'
  }, [serviceType, reviewMode])

  /**
   * Get WebSocket configuration for selected service
   */
  const getWebSocketConfig = useCallback((service: 'multi-agent' | 'legacy'): WebSocketConfig => {
    if (service === 'multi-agent') {
      const wsConfig = featureFlags.getWebSocketConfig()
      return {
        url: ServiceEndpoints.MULTI_AGENT.WEBSOCKET_URL,
        transports: ['polling', 'websocket'],
        forceNew: true,
        upgrade: true,
        timeout: ServiceEndpoints.MULTI_AGENT.TIMEOUT,
        reconnection: wsConfig.autoReconnect,
        reconnectionDelay: wsConfig.reconnectDelay,
        reconnectionDelayMax: wsConfig.reconnectDelay * 5,
        reconnectionAttempts: wsConfig.maxReconnectAttempts,
        heartbeatInterval: wsConfig.heartbeatInterval
      }
    } else {
      // Legacy service does not support WebSocket - return null config
      throw new Error('Legacy service does not support WebSocket connections')
    }
  }, [])

  /**
   * Update connection status
   */
  const updateConnectionStatus = useCallback((updates: Partial<WebSocketConnectionStatus>) => {
    setState(prev => ({
      ...prev,
      connectionStatus: { ...prev.connectionStatus, ...updates }
    }))
  }, [])

  /**
   * Add event to history (for debugging/replay)
   */
  const addEventToHistory = useCallback((event: AllWebSocketEvents) => {
    if (enableEventLogging) {
      setState(prev => ({
        ...prev,
        eventHistory: [...prev.eventHistory.slice(-99), event], // Keep last 100 events
        lastEventTime: Date.now()
      }))
    }
  }, [enableEventLogging])

  /**
   * Process incoming WebSocket event
   */
  const processWebSocketEvent = useCallback((rawEvent: any) => {
    try {
      // Normalize event structure
      const event: AllWebSocketEvents = {
        event_type: rawEvent.event_type || rawEvent.type,
        timestamp: rawEvent.timestamp || Date.now(),
        message: rawEvent.message,
        data: rawEvent.data || {},
        ...rawEvent
      }

      // Add to history
      addEventToHistory(event)

      // Log event if enabled
      if (enableEventLogging) {
        console.log('📡 WebSocket Event:', WebSocketEventUtils.formatForLogging(event))
      }

      // Track latency if enabled
      if (enableLatencyTracking && latencyTrackingRef.current) {
        const latency = Date.now() - latencyTrackingRef.current.startTime
        updateConnectionStatus({ latency })
        latencyTrackingRef.current = null
      }

      // Route event to appropriate handlers
      if (isMultiAgentEvent(event)) {
        const multiAgentEvent = event as MultiAgentWebSocketEvent
        handlers.onMultiAgentEvent?.(multiAgentEvent)
        
        // Specific multi-agent event handlers
        switch (multiAgentEvent.event_type) {
          case 'review_started':
            handlers.onReviewStarted?.(multiAgentEvent)
            break
          case 'review_completed':
            handlers.onReviewCompleted?.(multiAgentEvent)
            break
          case 'review_failed':
            handlers.onReviewFailed?.(multiAgentEvent)
            break
          case 'agent_started':
            handlers.onAgentStarted?.(multiAgentEvent)
            break
          case 'agent_progress':
            handlers.onAgentProgress?.(multiAgentEvent)
            break
          case 'agent_completed':
            handlers.onAgentCompleted?.(multiAgentEvent)
            break
          case 'agent_failed':
            handlers.onAgentFailed?.(multiAgentEvent)
            break
          case 'status_update':
            handlers.onStatusUpdate?.(multiAgentEvent)
            break
        }
      } else if (isLegacyEvent(event)) {
        const legacyEvent = event as LegacyWebSocketEvent
        handlers.onLegacyEvent?.(legacyEvent)
        
        // Specific legacy event handlers
        switch (legacyEvent.event_type) {
          case 'session_started':
            handlers.onSessionStarted?.(legacyEvent)
            break
          case 'claude_thinking':
            handlers.onClaudeThinking?.(legacyEvent)
            break
          case 'tool_usage':
            handlers.onToolUsage?.(legacyEvent)
            break
          case 'review_completed':
            handlers.onLegacyReviewCompleted?.(legacyEvent)
            break
        }
      } else if (isConnectionEvent(event)) {
        // Handle connection events
        switch (event.event_type) {
          case 'connected':
            handlers.onConnected?.(event)
            updateConnectionStatus({
              connected: true,
              connecting: false,
              error: null,
              connectionQuality: 'good'
            })
            break
          case 'disconnected':
            handlers.onDisconnected?.(event)
            updateConnectionStatus({
              connected: false,
              connecting: false,
              error: event.data?.reason as string || 'Disconnected'
            })
            break
          case 'reconnected':
            handlers.onReconnected?.(event)
            updateConnectionStatus({
              connected: true,
              connecting: false,
              error: null,
              reconnectAttempts: 0
            })
            setState(prev => ({ ...prev, reconnectCount: prev.reconnectCount + 1 }))
            break
          case 'reconnect_failed':
            handlers.onReconnectFailed?.(event)
            updateConnectionStatus({
              connected: false,
              connecting: false,
              error: 'Reconnection failed'
            })
            break
        }
      }

      // Call generic handler
      handlers.onAnyEvent?.(event)

    } catch (error) {
      console.error('Error processing WebSocket event:', error, rawEvent)
      handlers.onError?.(error as Error, rawEvent)
    }
  }, [handlers, addEventToHistory, enableEventLogging, enableLatencyTracking, updateConnectionStatus])

  /**
   * Setup heartbeat
   */
  const setupHeartbeat = useCallback((socket: Socket, config: WebSocketConfig) => {
    if (!enableHeartbeat || !config.heartbeatInterval) return

    heartbeatIntervalRef.current = setInterval(() => {
      if (socket.connected) {
        if (enableLatencyTracking) {
          latencyTrackingRef.current = {
            startTime: Date.now(),
            eventType: 'heartbeat'
          }
        }
        
        socket.emit('ping', { timestamp: Date.now() })
        updateConnectionStatus({ lastHeartbeat: Date.now() })
      }
    }, config.heartbeatInterval)
  }, [enableHeartbeat, enableLatencyTracking, updateConnectionStatus])

  /**
   * Join review/session room
   */
  const joinRoom = useCallback((socket: Socket, service: 'multi-agent' | 'legacy') => {
    if (service === 'multi-agent' && reviewId) {
      console.log(`🔗 Joining multi-agent review room: ${reviewId}`)
      socket.emit('join_review_room', { review_id: reviewId })
    } else if (service === 'legacy' && sessionId) {
      console.log(`🔗 Joining legacy session room: ${sessionId}`)
      socket.emit('join_session_room', { session_id: sessionId })
    }
  }, [reviewId, sessionId])

  /**
   * Connect to Multi-Agent service using native WebSocket
   */
  const connectNativeWebSocket = useCallback(async (service: 'multi-agent') => {
    try {
      // Close existing connection if any
      if (socketRef.current) {
        // Check if it's a native WebSocket (has readyState) or Socket.IO (has connected)
        if ('readyState' in socketRef.current && socketRef.current.readyState !== WebSocket.CLOSED) {
          console.log('🔄 Closing existing native WebSocket connection before creating new one')
          socketRef.current.close()
        } else if ('connected' in socketRef.current && socketRef.current.connected) {
          console.log('🔄 Closing existing Socket.IO connection before creating new one')
          socketRef.current.disconnect()
        }
        socketRef.current = null
      }
      
      const config = getWebSocketConfig(service)
      const wsUrl = config.url.replace('ws://', '').replace('http://', '')
      // Add session_id query parameter that backend expects
      const websocketUrl = `ws://${wsUrl}?session_id=${sessionId || 'default_session'}`
      
      console.log(`🔌 Connecting to native WebSocket:`, websocketUrl)
      
      updateConnectionStatus({
        connecting: true,
        connected: false,
        error: null
      })
      
      const ws = new WebSocket(websocketUrl)
      socketRef.current = ws as any // Type compatibility hack
      
      ws.onopen = () => {
        console.log('✅ Native WebSocket connected successfully')
        updateConnectionStatus({
          connected: true,
          connecting: false,
          error: null,
          connectionQuality: 'excellent'
        })

        // Wait a bit before sending messages to ensure server is ready
        setTimeout(() => {
          // Join review room if reviewId is provided
          if (reviewId && ws.readyState === WebSocket.OPEN) {
            try {
              ws.send(JSON.stringify({
                action: 'subscribe_review',
                data: { review_id: reviewId }
              }))
              console.log(`📡 Subscribed to review room: ${reviewId}`)
            } catch (error) {
              console.error('Failed to subscribe to review room:', error)
            }
          }
        }, 100) // Small delay to ensure server is ready

        handlers.onConnected?.({
          event_type: 'connected',
          timestamp: Date.now(),
          message: 'Native WebSocket connected',
          data: {
            client_id: 'web-client',
            server_version: 'unknown',
            supported_features: ['native-websocket']
          }
        })
      }
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          processWebSocketEvent(data)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }
      
      ws.onclose = () => {
        console.log('❌ Native WebSocket disconnected')
        updateConnectionStatus({
          connected: false,
          connecting: false,
          error: 'WebSocket connection closed'
        })
        
        handlers.onDisconnected?.({
          event_type: 'disconnected',
          timestamp: Date.now(),
          message: 'Native WebSocket disconnected',
          data: {
            reason: 'connection_closed',
            code: 1000,
            reconnect_possible: true
          }
        })
      }
      
      ws.onerror = (error) => {
        console.error('❌ Native WebSocket error:', error)
        updateConnectionStatus({
          connected: false,
          connecting: false,
          error: 'WebSocket connection failed',
          connectionQuality: 'poor'
        })
      }
      
    } catch (error) {
      console.error('Error creating native WebSocket connection:', error)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: `WebSocket connection failed: ${error}`,
        connectionQuality: 'poor'
      })
    }
  }, [getWebSocketConfig, updateConnectionStatus, reviewId, processWebSocketEvent, handlers])

  /**
   * Connect to WebSocket service
   */
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      console.log('Already connected to WebSocket')
      return
    }

    const service = determineService()
    
    // Multi-agent service uses native WebSocket, not Socket.IO
    if (service === 'multi-agent') {
      console.log('🔄 Multi-agent service: connecting via native WebSocket')
      connectNativeWebSocket(service)
      return
    }
    
    if (service === 'legacy') {
      console.log('🚫 Legacy service does not support WebSocket connections')
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: 'Legacy service does not support WebSocket',
        connectionQuality: 'poor'
      })
      return
    }
    
    const config = getWebSocketConfig(service)

    console.log(`🔌 Connecting to ${service} WebSocket:`, config.url)

    updateConnectionStatus({
      connecting: true,
      error: null
    })

    const socket = io(config.url, {
      transports: config.transports,
      forceNew: config.forceNew,
      upgrade: config.upgrade,
      timeout: config.timeout,
      reconnection: config.reconnection,
      reconnectionDelay: config.reconnectionDelay,
      reconnectionDelayMax: config.reconnectionDelayMax,
      reconnectionAttempts: config.reconnectionAttempts
    })

    socketRef.current = socket
    setState(prev => ({ ...prev, currentService: service }))

    // Connection event handlers
    socket.on('connect', () => {
      console.log(`✅ Connected to ${service} WebSocket`)
      updateConnectionStatus({
        connected: true,
        connecting: false,
        error: null,
        reconnectAttempts: 0,
        connectionQuality: 'excellent'
      })
      
      // Join appropriate room
      joinRoom(socket, service)
      
      // Setup heartbeat
      setupHeartbeat(socket, config)
    })

    socket.on('disconnect', (reason) => {
      console.log(`❌ Disconnected from ${service} WebSocket:`, reason)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: `Disconnected: ${reason}`,
        connectionQuality: 'poor'
      })
      
      // Clear heartbeat
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }
    })

    socket.on('connect_error', (error) => {
      console.error(`❌ Connection error for ${service} WebSocket:`, error)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: error.message || 'Connection failed',
        connectionQuality: 'poor'
      })
    })

    socket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 Reconnected to ${service} WebSocket (attempt ${attemptNumber})`)
      setState(prev => ({ ...prev, reconnectCount: prev.reconnectCount + 1 }))
      joinRoom(socket, service)
    })

    socket.on('reconnect_failed', () => {
      console.error(`💀 Failed to reconnect to ${service} WebSocket`)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: 'Reconnection failed - maximum attempts exceeded',
        connectionQuality: 'poor'
      })
    })

    // Listen for all possible events
    const eventTypes = [
      // Multi-agent events
      'review_started', 'review_completed', 'review_failed', 'review_cancelled',
      'agent_started', 'agent_progress', 'agent_completed', 'agent_failed',
      'context_ready', 'status_update', 'heartbeat', 'error',
      
      // Legacy events  
      'session_started', 'claude_thinking', 'tool_usage', 'claude_response_stream',
      'structured_result', 'phase3_started', 'phase3_completed', 'review_metadata',
      'review_completed', 'review_error', 'claude_progress',
      
      // Connection events
      'connected', 'disconnected', 'reconnected', 'reconnect_failed',
      
      // Generic events
      'pong' // Response to ping for latency tracking
    ]

    eventTypes.forEach(eventType => {
      socket.on(eventType, (data) => {
        processWebSocketEvent({
          event_type: eventType,
          timestamp: Date.now(),
          ...data
        })
      })
    })

  }, [
    determineService, 
    getWebSocketConfig, 
    updateConnectionStatus, 
    joinRoom, 
    setupHeartbeat, 
    processWebSocketEvent
  ])

  /**
   * Disconnect from WebSocket
   */
  const disconnect = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
      heartbeatIntervalRef.current = null
    }

    if (socketRef.current) {
      console.log('🔌 Disconnecting from WebSocket...')
      socketRef.current.close()
      socketRef.current = null
    }

    setState(prev => ({
      ...prev,
      currentService: null,
      connectionStatus: {
        ...prev.connectionStatus,
        connected: false,
        connecting: false,
        error: null
      }
    }))
  }, [])

  /**
   * Send message to WebSocket
   */
  const sendMessage = useCallback((eventType: string, data: Record<string, unknown> = {}) => {
    const socket = socketRef.current
    if (!socket || !socket.connected) {
      console.warn('Cannot send message: WebSocket not connected')
      return false
    }

    if (enableLatencyTracking) {
      latencyTrackingRef.current = {
        startTime: Date.now(),
        eventType
      }
    }

    socket.emit(eventType, data)
    return true
  }, [enableLatencyTracking])

  /**
   * Get connection quality assessment
   */
  const getConnectionQuality = useCallback((): 'excellent' | 'good' | 'poor' | 'unknown' => {
    const { connected, latency, lastHeartbeat } = state.connectionStatus
    
    if (!connected) return 'poor'
    
    if (latency !== undefined) {
      if (latency < 100) return 'excellent'
      if (latency < 500) return 'good'
      return 'poor'
    }
    
    if (lastHeartbeat) {
      const timeSinceHeartbeat = Date.now() - lastHeartbeat
      if (timeSinceHeartbeat < 60000) return 'good' // Less than 1 minute
      if (timeSinceHeartbeat < 300000) return 'poor' // Less than 5 minutes
    }
    
    return 'unknown'
  }, [state.connectionStatus])

  // Auto-connect effect
  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    // Only disconnect on unmount, not on every re-render
    return () => {
      if (socketRef.current) {
        console.log('🔄 Component unmounting: disconnecting WebSocket')
        disconnect()
      }
    }
  }, [autoConnect]) // Removed connect/disconnect from dependencies to prevent re-render loops

  // Update connection quality
  useEffect(() => {
    const quality = getConnectionQuality()
    if (quality !== state.connectionStatus.connectionQuality) {
      updateConnectionStatus({ connectionQuality: quality })
    }
  }, [state.connectionStatus.latency, state.connectionStatus.lastHeartbeat, getConnectionQuality, updateConnectionStatus])

  return {
    // Connection state
    ...state,
    
    // Connection methods
    connect,
    disconnect,
    sendMessage,
    
    // Utility methods
    isConnected: state.connectionStatus.connected,
    isConnecting: state.connectionStatus.connecting,
    hasError: !!state.connectionStatus.error,
    getConnectionQuality,
    
    // Service info
    currentServiceUrl: state.currentService ? getWebSocketConfig(state.currentService).url : null,
    
    // Event history (for debugging)
    recentEvents: state.eventHistory.slice(-10)
  }
}