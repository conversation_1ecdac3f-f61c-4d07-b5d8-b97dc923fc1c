import { useEffect, useRef } from 'react'
import type { ReviewSession } from '../types/enhanced-review'

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  acceptance_criteria_count: number
}

type ReviewStep = 'select-work' | 'configure-review' | 'review-progress' | 'review-results'

interface UseWorkflowAutoAdvanceProps {
  selectedPR: AssignedPR | null
  selectedTicket: AssignedTicket | null
  activeSession: ReviewSession | null
  currentStep: ReviewStep
  onStepChange: (step: ReviewStep) => void
}

export const useWorkflowAutoAdvance = ({
  selectedPR,
  selectedTicket,
  activeSession,
  currentStep,
  onStepChange
}: UseWorkflowAutoAdvanceProps) => {
  // Use refs to avoid infinite loops
  const prevSelectedPRId = useRef<number | null>(null)
  const prevActiveSessionId = useRef<string | null>(null)
  const prevSessionStatus = useRef<string | null>(null)

  // Auto-advance to configure step when both PR and Ticket are selected
  useEffect(() => {
    if (selectedPR && selectedTicket && selectedPR.id !== prevSelectedPRId.current) {
      prevSelectedPRId.current = selectedPR.id
      if (currentStep === 'select-work') {
        onStepChange('configure-review')
      }
    }
  }, [selectedPR, selectedTicket, currentStep, onStepChange])

  // Auto-advance to progress when review starts
  useEffect(() => {
    if (activeSession && activeSession.session_id !== prevActiveSessionId.current) {
      prevActiveSessionId.current = activeSession.session_id
      if (currentStep === 'configure-review') {
        onStepChange('review-progress')
      }
    }
  }, [activeSession, currentStep, onStepChange])

  // Auto-advance to results when review completes
  useEffect(() => {
    if (activeSession && activeSession.status !== prevSessionStatus.current) {
      prevSessionStatus.current = activeSession.status
      if (activeSession.status === 'completed' && currentStep === 'review-progress') {
        onStepChange('review-results')
      }
    }
  }, [activeSession, currentStep, onStepChange])
}