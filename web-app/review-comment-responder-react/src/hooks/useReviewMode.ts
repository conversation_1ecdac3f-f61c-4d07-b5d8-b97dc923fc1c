import React from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { ReviewMode } from '../components/ModeSelector'

interface ReviewModeState {
  currentMode: ReviewMode
  isTransitioning: boolean
  lastUsedMode: ReviewMode | null
  preferences: {
    autoSwitchToApi: boolean
    rememberModeChoice: boolean
    showModeSelector: boolean
    lastManualModeChange?: number
  }
}

interface ReviewModeStore extends ReviewModeState {
  // Actions
  setMode: (mode: ReviewMode) => void
  toggleMode: () => void
  setTransitioning: (transitioning: boolean) => void
  updatePreferences: (preferences: Partial<ReviewModeState['preferences']>) => void
  
  // Helper methods
  canUseApiMode: (isAuthenticated: boolean) => boolean
  getRecommendedMode: (isAuthenticated: boolean) => ReviewMode
  shouldShowModeSelector: (isAuthenticated: boolean) => boolean
}

export const useReviewMode = create<ReviewModeStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentMode: 'screenshot', // Default to screenshot mode for backward compatibility
      isTransitioning: false,
      lastUsedMode: null,
      preferences: {
        autoSwitchToApi: true, // Automatically switch to API mode when authenticated
        rememberModeChoice: true,
        showModeSelector: true,
        lastManualModeChange: undefined
      },

      // Actions
      setMode: (mode: ReviewMode) => {
        const currentState = get()
        
        set({
          lastUsedMode: currentState.currentMode,
          currentMode: mode,
          isTransitioning: true
        })

        // Reset transitioning state after a short delay
        setTimeout(() => {
          set({ isTransitioning: false })
        }, 300)
      },

      toggleMode: () => {
        const { currentMode, canUseApiMode } = get()
        const isAuthenticated = true // We'll get this from auth context
        
        if (currentMode === 'screenshot' && canUseApiMode(isAuthenticated)) {
          get().setMode('api')
        } else {
          get().setMode('screenshot')
        }
      },

      setTransitioning: (transitioning: boolean) => {
        set({ isTransitioning: transitioning })
      },

      updatePreferences: (newPreferences) => {
        set(state => ({
          preferences: {
            ...state.preferences,
            ...newPreferences
          }
        }))
      },

      // Helper methods
      canUseApiMode: (isAuthenticated: boolean) => {
        return isAuthenticated
      },

      getRecommendedMode: (isAuthenticated: boolean) => {
        const { preferences, lastUsedMode } = get()
        
        // If user has a preference and it's valid, use it
        if (preferences.rememberModeChoice && lastUsedMode) {
          if (lastUsedMode === 'api' && isAuthenticated) {
            return 'api'
          }
          if (lastUsedMode === 'screenshot') {
            return 'screenshot'
          }
        }
        
        // Auto-switch logic
        if (preferences.autoSwitchToApi && isAuthenticated) {
          return 'api'
        }
        
        // Default fallback
        return 'screenshot'
      },

      shouldShowModeSelector: (isAuthenticated: boolean) => {
        const { preferences } = get()
        return preferences.showModeSelector && isAuthenticated
      }
    }),
    {
      name: 'review-mode-preferences',
      // Only persist user preferences and mode choice
      partialize: (state) => ({
        currentMode: state.currentMode,
        lastUsedMode: state.lastUsedMode,
        preferences: state.preferences
      })
    }
  )
)

// Hook for components that only need to read the current mode
export const useCurrentReviewMode = () => {
  const { currentMode, isTransitioning } = useReviewMode()
  return { currentMode, isTransitioning }
}

// Hook for mode management actions
export const useReviewModeActions = () => {
  const { setMode, toggleMode, setTransitioning, updatePreferences } = useReviewMode()
  return { setMode, toggleMode, setTransitioning, updatePreferences }
}

// Hook for mode recommendations and validation
export const useReviewModeHelpers = () => {
  const { canUseApiMode, getRecommendedMode, shouldShowModeSelector } = useReviewMode()
  return { canUseApiMode, getRecommendedMode, shouldShowModeSelector }
}

// Hook that automatically manages mode based on authentication state
export const useAutoReviewMode = (isAuthenticated: boolean) => {
  const { currentMode, preferences, getRecommendedMode, setMode } = useReviewMode()

  // Auto-switch to recommended mode when authentication state changes
  React.useEffect(() => {
    if (preferences.autoSwitchToApi) {
      const recommendedMode = getRecommendedMode(isAuthenticated)
      if (recommendedMode !== currentMode) {
        // Only auto-switch if user hasn't manually chosen a mode recently
        const timeSinceLastManualChange = Date.now() - (preferences.lastManualModeChange || 0)
        if (timeSinceLastManualChange > 30000) { // 30 seconds cooldown
          setMode(recommendedMode)
        }
      }
    }
  }, [isAuthenticated, preferences.autoSwitchToApi, currentMode, getRecommendedMode, setMode])

  return { currentMode }
}

// Type exports for external use
export type { ReviewMode, ReviewModeState }