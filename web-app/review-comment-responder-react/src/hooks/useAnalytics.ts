/**
 * Analytics Hook
 * React hook for user action tracking and analytics integration
 */

import { useCallback, useRef, useEffect } from 'react'
import { useFeatureFlags } from './useFeatureFlags'
import { userActionTracker, performanceMonitor } from '../utils/monitoring'
import { logger } from '../utils/logger'
import { env } from '../config/environment'

/**
 * Analytics event interface
 */
export interface AnalyticsEvent {
  action: string
  category: string
  label?: string
  value?: number
  properties?: Record<string, any>
}

/**
 * Multi-agent specific events
 */
export interface MultiAgentEvent extends AnalyticsEvent {
  reviewId?: string
  agentType?: string
  phase?: 'start' | 'progress' | 'complete' | 'error'
  duration?: number
}

/**
 * Performance event interface
 */
export interface PerformanceEvent {
  metric: string
  value: number
  unit: string
  component?: string
  tags?: Record<string, string>
}

/**
 * User interaction event
 */
export interface InteractionEvent extends AnalyticsEvent {
  element: string
  screen: string
  timestamp?: number
}

/**
 * Analytics hook return type
 */
export interface UseAnalyticsReturn {
  // Basic tracking
  trackEvent: (event: AnalyticsEvent) => void
  trackPageView: (page: string, title?: string) => void
  trackUserAction: (action: string, element: string, properties?: Record<string, any>) => void
  
  // Multi-agent specific tracking
  trackMultiAgentEvent: (event: MultiAgentEvent) => void
  trackReviewStart: (reviewId: string, mode: string) => void
  trackReviewComplete: (reviewId: string, duration: number, agentsCount: number) => void
  trackAgentProgress: (reviewId: string, agentType: string, progress: number) => void
  trackAgentComplete: (reviewId: string, agentType: string, duration: number, findingsCount: number) => void
  trackAgentError: (reviewId: string, agentType: string, error: string) => void
  
  // Performance tracking
  trackPerformance: (event: PerformanceEvent) => void
  trackComponentRender: (componentName: string, duration: number) => void
  trackApiCall: (endpoint: string, method: string, duration: number, status: number) => void
  trackWebSocketEvent: (event: string, duration?: number) => void
  
  // User experience tracking
  trackInteraction: (event: InteractionEvent) => void
  trackButtonClick: (buttonId: string, screen: string, context?: Record<string, any>) => void
  trackFormSubmission: (formName: string, success: boolean, errors?: string[]) => void
  trackSearch: (query: string, resultsCount?: number, filters?: Record<string, any>) => void
  trackNavigation: (from: string, to: string, method: 'click' | 'keyboard' | 'programmatic') => void
  
  // Error tracking
  trackError: (error: Error | string, context?: Record<string, any>, severity?: 'low' | 'medium' | 'high' | 'critical') => void
  trackJavaScriptError: (error: Error, component?: string) => void
  trackNetworkError: (url: string, status: number, message: string) => void
  
  // Conversion tracking
  trackConversion: (event: string, value?: number, currency?: string) => void
  trackFeatureUsage: (feature: string, action: string, context?: Record<string, any>) => void
  
  // Session tracking
  startSession: (userId?: string) => void
  endSession: () => void
  updateUserProperties: (properties: Record<string, any>) => void
  
  // A/B Testing
  trackExperiment: (experimentName: string, variant: string, outcome?: string) => void
  
  // Utility functions
  isTrackingEnabled: boolean
  getSessionId: () => string
  flush: () => Promise<void>
}

/**
 * Session manager for analytics
 */
class AnalyticsSession {
  private sessionId: string
  private userId?: string
  private startTime: number
  private pageViews: number = 0
  private interactions: number = 0
  private errors: number = 0

  constructor() {
    this.sessionId = this.generateSessionId()
    this.startTime = Date.now()
    this.loadSessionFromStorage()
  }

  getSessionId(): string {
    return this.sessionId
  }

  setUserId(userId?: string): void {
    this.userId = userId
    this.saveSessionToStorage()
  }

  getUserId(): string | undefined {
    return this.userId
  }

  incrementPageViews(): void {
    this.pageViews++
    this.saveSessionToStorage()
  }

  incrementInteractions(): void {
    this.interactions++
    this.saveSessionToStorage()
  }

  incrementErrors(): void {
    this.errors++
    this.saveSessionToStorage()
  }

  getSessionData(): {
    sessionId: string
    userId?: string
    duration: number
    pageViews: number
    interactions: number
    errors: number
  } {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      duration: Date.now() - this.startTime,
      pageViews: this.pageViews,
      interactions: this.interactions,
      errors: this.errors
    }
  }

  private generateSessionId(): string {
    return `analytics-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private saveSessionToStorage(): void {
    try {
      sessionStorage.setItem('analytics_session', JSON.stringify({
        sessionId: this.sessionId,
        userId: this.userId,
        startTime: this.startTime,
        pageViews: this.pageViews,
        interactions: this.interactions,
        errors: this.errors
      }))
    } catch (error) {
      console.warn('Failed to save analytics session:', error)
    }
  }

  private loadSessionFromStorage(): void {
    try {
      const stored = sessionStorage.getItem('analytics_session')
      if (stored) {
        const data = JSON.parse(stored)
        this.sessionId = data.sessionId
        this.userId = data.userId
        this.startTime = data.startTime
        this.pageViews = data.pageViews || 0
        this.interactions = data.interactions || 0
        this.errors = data.errors || 0
      }
    } catch (error) {
      console.warn('Failed to load analytics session:', error)
    }
  }
}

/**
 * Global analytics session
 */
const analyticsSession = new AnalyticsSession()

/**
 * Analytics hook
 */
export const useAnalytics = (): UseAnalyticsReturn => {
  const { shouldTrackUsage, shouldTrackErrors, shouldTrackPerformance } = useFeatureFlags()
  const componentLogger = useRef(logger.child('useAnalytics'))
  const lastPageView = useRef<string>('')
  const sessionStarted = useRef(false)

  // Check if tracking is enabled
  const isTrackingEnabled = shouldTrackUsage && env.monitoring.enableUsageTracking

  /**
   * Basic event tracking
   */
  const trackEvent = useCallback((event: AnalyticsEvent) => {
    if (!isTrackingEnabled) return

    try {
      userActionTracker.trackAction(
        event.action,
        event.category,
        event.label,
        event.value,
        event.properties
      )

      componentLogger.current.debug('Event tracked', {
        action: event.action,
        category: event.category,
        label: event.label
      })

      analyticsSession.incrementInteractions()
    } catch (error) {
      componentLogger.current.error('Failed to track event', error instanceof Error ? error : new Error(String(error)))
    }
  }, [isTrackingEnabled])

  /**
   * Page view tracking
   */
  const trackPageView = useCallback((page: string, title?: string) => {
    if (!isTrackingEnabled) return

    try {
      userActionTracker.trackPageView(page)
      
      componentLogger.current.info('Page view tracked', { page, title })
      
      analyticsSession.incrementPageViews()
      lastPageView.current = page
    } catch (error) {
      componentLogger.current.error('Failed to track page view', error instanceof Error ? error : new Error(String(error)))
    }
  }, [isTrackingEnabled])

  /**
   * User action tracking
   */
  const trackUserAction = useCallback((action: string, element: string, properties?: Record<string, any>) => {
    if (!isTrackingEnabled) return

    trackEvent({
      action,
      category: 'user_interaction',
      label: element,
      properties: {
        ...properties,
        element,
        screen: lastPageView.current
      }
    })
  }, [isTrackingEnabled, trackEvent])

  /**
   * Multi-agent event tracking
   */
  const trackMultiAgentEvent = useCallback((event: MultiAgentEvent) => {
    if (!isTrackingEnabled) return

    userActionTracker.trackMultiAgentEvent(
      event.action,
      event.reviewId,
      event.agentType
    )

    componentLogger.current.info('Multi-agent event tracked', {
      action: event.action,
      reviewId: event.reviewId,
      agentType: event.agentType,
      phase: event.phase
    })
  }, [isTrackingEnabled])

  /**
   * Review start tracking
   */
  const trackReviewStart = useCallback((reviewId: string, mode: string) => {
    trackMultiAgentEvent({
      action: 'review_started',
      category: 'multi_agent_review',
      reviewId,
      phase: 'start',
      properties: { mode }
    })
  }, [trackMultiAgentEvent])

  /**
   * Review complete tracking
   */
  const trackReviewComplete = useCallback((reviewId: string, duration: number, agentsCount: number) => {
    trackMultiAgentEvent({
      action: 'review_completed',
      category: 'multi_agent_review',
      reviewId,
      phase: 'complete',
      duration,
      properties: { agentsCount }
    })
  }, [trackMultiAgentEvent])

  /**
   * Agent progress tracking
   */
  const trackAgentProgress = useCallback((reviewId: string, agentType: string, progress: number) => {
    trackMultiAgentEvent({
      action: 'agent_progress',
      category: 'multi_agent_review',
      reviewId,
      agentType,
      phase: 'progress',
      value: progress
    })
  }, [trackMultiAgentEvent])

  /**
   * Agent complete tracking
   */
  const trackAgentComplete = useCallback((reviewId: string, agentType: string, duration: number, findingsCount: number) => {
    trackMultiAgentEvent({
      action: 'agent_completed',
      category: 'multi_agent_review',
      reviewId,
      agentType,
      phase: 'complete',
      duration,
      properties: { findingsCount }
    })
  }, [trackMultiAgentEvent])

  /**
   * Agent error tracking
   */
  const trackAgentError = useCallback((reviewId: string, agentType: string, error: string) => {
    trackMultiAgentEvent({
      action: 'agent_error',
      category: 'multi_agent_review',
      reviewId,
      agentType,
      phase: 'error',
      properties: { error }
    })
  }, [trackMultiAgentEvent])

  /**
   * Performance tracking
   */
  const trackPerformance = useCallback((event: PerformanceEvent) => {
    if (!shouldTrackPerformance) return

    performanceMonitor.recordMetric(event.metric, event.value, event.tags)
    
    componentLogger.current.debug('Performance tracked', {
      metric: event.metric,
      value: event.value,
      unit: event.unit,
      component: event.component
    })
  }, [shouldTrackPerformance])

  /**
   * Component render tracking
   */
  const trackComponentRender = useCallback((componentName: string, duration: number) => {
    trackPerformance({
      metric: 'component_render',
      value: duration,
      unit: 'ms',
      component: componentName,
      tags: { component: componentName }
    })
  }, [trackPerformance])

  /**
   * API call tracking
   */
  const trackApiCall = useCallback((endpoint: string, method: string, duration: number, status: number) => {
    performanceMonitor.recordApiCall(endpoint, duration, status)
    
    trackPerformance({
      metric: 'api_call',
      value: duration,
      unit: 'ms',
      tags: { endpoint, method, status: status.toString() }
    })
  }, [trackPerformance])

  /**
   * WebSocket event tracking
   */
  const trackWebSocketEvent = useCallback((event: string, duration?: number) => {
    performanceMonitor.recordWebSocketEvent(event, duration)
    
    trackEvent({
      action: 'websocket_event',
      category: 'websocket',
      label: event,
      value: duration
    })
  }, [trackEvent])

  /**
   * User interaction tracking
   */
  const trackInteraction = useCallback((event: InteractionEvent) => {
    trackEvent({
      ...event,
      properties: {
        ...event.properties,
        element: event.element,
        screen: event.screen,
        timestamp: event.timestamp || Date.now()
      }
    })
  }, [trackEvent])

  /**
   * Button click tracking
   */
  const trackButtonClick = useCallback((buttonId: string, screen: string, context?: Record<string, any>) => {
    trackInteraction({
      action: 'button_click',
      category: 'interaction',
      element: buttonId,
      screen,
      properties: context
    })
  }, [trackInteraction])

  /**
   * Form submission tracking
   */
  const trackFormSubmission = useCallback((formName: string, success: boolean, errors?: string[]) => {
    trackEvent({
      action: 'form_submit',
      category: 'form',
      label: formName,
      value: success ? 1 : 0,
      properties: {
        success,
        errors: errors?.length || 0,
        errorDetails: errors
      }
    })
  }, [trackEvent])

  /**
   * Search tracking
   */
  const trackSearch = useCallback((query: string, resultsCount?: number, filters?: Record<string, any>) => {
    userActionTracker.trackSearch(query, resultsCount)
    
    trackEvent({
      action: 'search',
      category: 'search',
      label: env.monitoring.anonymizeData ? '[SEARCH_QUERY]' : query,
      value: resultsCount,
      properties: { filters }
    })
  }, [trackEvent])

  /**
   * Navigation tracking
   */
  const trackNavigation = useCallback((from: string, to: string, method: 'click' | 'keyboard' | 'programmatic') => {
    trackEvent({
      action: 'navigation',
      category: 'navigation',
      label: `${from} -> ${to}`,
      properties: { from, to, method }
    })
  }, [trackEvent])

  /**
   * Error tracking
   */
  const trackError = useCallback((error: Error | string, context?: Record<string, any>, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') => {
    if (!shouldTrackErrors) return

    const errorMessage = typeof error === 'string' ? error : error.message
    
    componentLogger.current.error('Error tracked', typeof error === 'object' ? error : new Error(error), context)
    
    trackEvent({
      action: 'error_occurred',
      category: 'error',
      label: errorMessage,
      properties: {
        ...context,
        severity,
        stack: typeof error === 'object' ? error.stack : undefined
      }
    })

    analyticsSession.incrementErrors()
  }, [shouldTrackErrors, trackEvent])

  /**
   * JavaScript error tracking
   */
  const trackJavaScriptError = useCallback((error: Error, component?: string) => {
    trackError(error, { component, type: 'javascript_error' }, 'high')
  }, [trackError])

  /**
   * Network error tracking
   */
  const trackNetworkError = useCallback((url: string, status: number, message: string) => {
    trackError(message, { url, status, type: 'network_error' }, 'medium')
  }, [trackError])

  /**
   * Conversion tracking
   */
  const trackConversion = useCallback((event: string, value?: number, currency?: string) => {
    trackEvent({
      action: 'conversion',
      category: 'conversion',
      label: event,
      value,
      properties: { currency }
    })
  }, [trackEvent])

  /**
   * Feature usage tracking
   */
  const trackFeatureUsage = useCallback((feature: string, action: string, context?: Record<string, any>) => {
    trackEvent({
      action: `feature_${action}`,
      category: 'feature_usage',
      label: feature,
      properties: context
    })
  }, [trackEvent])

  /**
   * Session management
   */
  const startSession = useCallback((userId?: string) => {
    if (!isTrackingEnabled) return

    analyticsSession.setUserId(userId)
    sessionStarted.current = true
    
    trackEvent({
      action: 'session_start',
      category: 'session',
      properties: {
        userId: env.monitoring.anonymizeData && userId ? '[USER_ID]' : userId,
        userAgent: navigator.userAgent,
        viewport: `${window.innerWidth}x${window.innerHeight}`,
        timestamp: Date.now()
      }
    })

    componentLogger.current.info('Analytics session started', { userId })
  }, [isTrackingEnabled, trackEvent])

  const endSession = useCallback(() => {
    if (!isTrackingEnabled || !sessionStarted.current) return

    const sessionData = analyticsSession.getSessionData()
    
    trackEvent({
      action: 'session_end',
      category: 'session',
      properties: sessionData
    })

    componentLogger.current.info('Analytics session ended', sessionData)
    sessionStarted.current = false
  }, [isTrackingEnabled, trackEvent])

  const updateUserProperties = useCallback((properties: Record<string, any>) => {
    if (!isTrackingEnabled) return

    const anonymizedProperties = env.monitoring.anonymizeData 
      ? Object.keys(properties).reduce((acc, key) => ({
          ...acc,
          [key]: '[ANONYMIZED]'
        }), {})
      : properties

    componentLogger.current.debug('User properties updated', anonymizedProperties)
  }, [isTrackingEnabled])

  /**
   * A/B Testing tracking
   */
  const trackExperiment = useCallback((experimentName: string, variant: string, outcome?: string) => {
    trackEvent({
      action: 'experiment_exposure',
      category: 'experiment',
      label: experimentName,
      properties: { variant, outcome }
    })
  }, [trackEvent])

  /**
   * Utility functions
   */
  const getSessionId = useCallback(() => {
    return analyticsSession.getSessionId()
  }, [])

  const flush = useCallback(async () => {
    // Force flush of pending analytics data
    try {
      // Implementation would depend on analytics service
      componentLogger.current.debug('Analytics data flushed')
    } catch (error) {
      componentLogger.current.error('Failed to flush analytics data', error instanceof Error ? error : new Error(String(error)))
    }
  }, [])

  /**
   * Initialize session on mount
   */
  useEffect(() => {
    if (isTrackingEnabled && !sessionStarted.current) {
      startSession()
    }

    // Cleanup on unmount
    return () => {
      if (sessionStarted.current) {
        endSession()
      }
    }
  }, [isTrackingEnabled, startSession, endSession])

  /**
   * Track page visibility changes
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      trackEvent({
        action: document.hidden ? 'page_hidden' : 'page_visible',
        category: 'page_visibility'
      })
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [trackEvent])

  return {
    // Basic tracking
    trackEvent,
    trackPageView,
    trackUserAction,
    
    // Multi-agent specific tracking
    trackMultiAgentEvent,
    trackReviewStart,
    trackReviewComplete,
    trackAgentProgress,
    trackAgentComplete,
    trackAgentError,
    
    // Performance tracking
    trackPerformance,
    trackComponentRender,
    trackApiCall,
    trackWebSocketEvent,
    
    // User experience tracking
    trackInteraction,
    trackButtonClick,
    trackFormSubmission,
    trackSearch,
    trackNavigation,
    
    // Error tracking
    trackError,
    trackJavaScriptError,
    trackNetworkError,
    
    // Conversion tracking
    trackConversion,
    trackFeatureUsage,
    
    // Session tracking
    startSession,
    endSession,
    updateUserProperties,
    
    // A/B Testing
    trackExperiment,
    
    // Utility functions
    isTrackingEnabled,
    getSessionId,
    flush
  }
}

export default useAnalytics