import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import type { MultiAgentReviewStatus } from '../components/MultiAgentProgressTracker'

export interface AgentEvent {
  event_type: 'agent_started' | 'agent_progress' | 'agent_completed' | 'agent_failed'
  timestamp: number
  review_id: string
  agent_type: string
  data?: Record<string, unknown>
  message?: string
}

export interface ReviewEvent {
  event_type: 'review_started' | 'review_completed' | 'review_failed' | 'review_cancelled'
  timestamp: number
  review_id: string
  data?: Record<string, unknown>
  message?: string
}

export interface WebSocketConnectionStatus {
  connected: boolean
  connecting: boolean
  error: string | null
  lastHeartbeat: number | null
}

interface UseMultiAgentWebSocketProps {
  reviewId?: string
  onAgentEvent?: (event: AgentEvent) => void
  onReviewEvent?: (event: ReviewEvent) => void
  onReviewStatusUpdate?: (status: MultiAgentReviewStatus) => void
  onConnectionStatusChange?: (status: WebSocketConnectionStatus) => void
}

interface WebSocketMessage {
  event_type: string
  timestamp: number
  review_id?: string
  agent_type?: string
  data?: Record<string, unknown>
  message?: string
}

export const useMultiAgentWebSocket = ({
  reviewId,
  onAgentEvent,
  onReviewEvent,
  onReviewStatusUpdate,
  onConnectionStatusChange
}: UseMultiAgentWebSocketProps) => {
  const socketRef = useRef<Socket | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<WebSocketConnectionStatus>({
    connected: false,
    connecting: false,
    error: null,
    lastHeartbeat: null
  })
  const [currentReviewStatus, setCurrentReviewStatus] = useState<MultiAgentReviewStatus | null>(null)
  
  // Update connection status and notify parent
  const updateConnectionStatus = useCallback((updates: Partial<WebSocketConnectionStatus>) => {
    setConnectionStatus(prev => {
      const newStatus = { ...prev, ...updates }
      onConnectionStatusChange?.(newStatus)
      return newStatus
    })
  }, [onConnectionStatusChange])
  
  // Join/leave review room
  const joinReviewRoom = useCallback((socket: Socket, reviewId: string) => {
    console.log(`🔗 Joining review room: ${reviewId}`)
    socket.emit('join_review_room', { review_id: reviewId })
  }, [])
  
  const leaveReviewRoom = useCallback((socket: Socket, reviewId: string) => {
    console.log(`🔌 Leaving review room: ${reviewId}`)
    socket.emit('leave_review_room', { review_id: reviewId })
  }, [])
  
  // Process incoming WebSocket messages
  const processWebSocketMessage = useCallback((message: WebSocketMessage) => {
    const { event_type, review_id, agent_type } = message
    
    // Only process messages for the current review (or system messages)
    if (review_id && reviewId && review_id !== reviewId) {
      return
    }
    
    console.log(`📡 WebSocket Event: ${event_type}`, message)
    
    // Agent-specific events
    if (agent_type && ['agent_started', 'agent_progress', 'agent_completed', 'agent_failed'].includes(event_type)) {
      const agentEvent: AgentEvent = {
        event_type: event_type as AgentEvent['event_type'],
        timestamp: message.timestamp,
        review_id: review_id || '',
        agent_type,
        data: message.data,
        message: message.message
      }
      
      onAgentEvent?.(agentEvent)
      
      // Update current review status based on agent events
      if (currentReviewStatus && review_id === currentReviewStatus.review_id) {
        setCurrentReviewStatus(prev => {
          if (!prev) return prev
          
          const updatedAgentStatuses = { ...prev.agent_statuses }
          
          if (updatedAgentStatuses[agent_type]) {
            const agentStatus = updatedAgentStatuses[agent_type]
            
            switch (event_type) {
              case 'agent_started':
                agentStatus.status = 'running'
                agentStatus.started_at = new Date().toISOString()
                break
              case 'agent_progress':
                agentStatus.progress = (message.data?.progress as number) || 0
                break
              case 'agent_completed':
                agentStatus.status = 'completed'
                agentStatus.progress = 100
                agentStatus.completed_at = new Date().toISOString()
                break
              case 'agent_failed':
                agentStatus.status = 'failed'
                agentStatus.completed_at = new Date().toISOString()
                agentStatus.error_message = message.data?.error as string || message.message || 'Unknown error'
                break
            }
          }
          
          // Calculate overall progress
          const agents = Object.values(updatedAgentStatuses).filter(agent => agent.status !== 'skipped')
          const totalProgress = agents.reduce((sum, agent) => sum + agent.progress, 0)
          const overallProgress = agents.length > 0 ? totalProgress / agents.length : 0
          
          // Update active/completed/failed agent lists
          const activeAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'running')
            .map(([type, _]) => type)
          
          const completedAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'completed')
            .map(([type, _]) => type)
          
          const failedAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'failed')
            .map(([type, _]) => type)
          
          const updatedStatus = {
            ...prev,
            agent_statuses: updatedAgentStatuses,
            progress: overallProgress,
            active_agents: activeAgents,
            completed_agents: completedAgents,
            failed_agents: failedAgents
          }
          
          onReviewStatusUpdate?.(updatedStatus)
          return updatedStatus
        })
      }
    }
    
    // Review-level events
    else if (['review_started', 'review_completed', 'review_failed', 'review_cancelled'].includes(event_type)) {
      const reviewEvent: ReviewEvent = {
        event_type: event_type as ReviewEvent['event_type'],
        timestamp: message.timestamp,
        review_id: review_id || '',
        data: message.data,
        message: message.message
      }
      
      onReviewEvent?.(reviewEvent)
      
      // Update overall review status
      if (currentReviewStatus && review_id === currentReviewStatus.review_id) {
        setCurrentReviewStatus(prev => {
          if (!prev) return prev
          
          const updatedStatus = { ...prev }
          
          switch (event_type) {
            case 'review_started':
              updatedStatus.status = 'running'
              break
            case 'review_completed':
              updatedStatus.status = 'completed'
              updatedStatus.progress = 100
              updatedStatus.completed_at = new Date().toISOString()
              break
            case 'review_failed':
              updatedStatus.status = 'failed'
              updatedStatus.completed_at = new Date().toISOString()
              break
            case 'review_cancelled':
              updatedStatus.status = 'cancelled'
              updatedStatus.completed_at = new Date().toISOString()
              break
          }
          
          onReviewStatusUpdate?.(updatedStatus)
          return updatedStatus
        })
      }
    }
    
    // Connection/system events
    else if (event_type === 'connected') {
      updateConnectionStatus({
        connected: true,
        connecting: false,
        error: null
      })
    }
    else if (event_type === 'heartbeat') {
      updateConnectionStatus({
        lastHeartbeat: Date.now()
      })
    }
  }, [reviewId, currentReviewStatus, onAgentEvent, onReviewEvent, onReviewStatusUpdate, updateConnectionStatus])
  
  // Setup WebSocket connection
  useEffect(() => {
    console.log('🔌 Setting up Multi-Agent WebSocket connection...')

    // DEPRECATED: This hook is deprecated in favor of useEnhancedWebSocket
    // The Multi-Agent service uses native WebSockets, not Socket.IO
    console.warn('⚠️ useMultiAgentWebSocket is deprecated. Use useEnhancedWebSocket instead.')

    // Return early to prevent conflicting WebSocket connections
    updateConnectionStatus({
      connected: false,
      connecting: false,
      error: 'This hook is deprecated. Use useEnhancedWebSocket instead.'
    })

    return () => {
      console.log('🔌 useMultiAgentWebSocket cleanup (deprecated)')
    }
  }, [reviewId, processWebSocketMessage, updateConnectionStatus, joinReviewRoom, leaveReviewRoom])

  // Handle review ID changes - DEPRECATED
  useEffect(() => {
    console.warn('⚠️ useMultiAgentWebSocket review ID effect is deprecated')
    // No-op since this hook is deprecated
  }, [reviewId, joinReviewRoom])

  // Public API - DEPRECATED but maintained for compatibility
  const sendMessage = useCallback((eventType: string, data: Record<string, unknown> = {}) => {
    console.warn('⚠️ useMultiAgentWebSocket.sendMessage is deprecated. Use useEnhancedWebSocket instead.')
    return false
  }, [])

  const subscribeToReview = useCallback((newReviewId: string) => {
    console.warn('⚠️ useMultiAgentWebSocket.subscribeToReview is deprecated. Use useEnhancedWebSocket instead.')
    return false
  }, [])

  const unsubscribeFromReview = useCallback((reviewIdToLeave: string) => {
    console.warn('⚠️ useMultiAgentWebSocket.unsubscribeFromReview is deprecated. Use useEnhancedWebSocket instead.')
    return false
  }, [])

  return {
    connectionStatus,
    currentReviewStatus,
    sendMessage,
    subscribeToReview,
    unsubscribeFromReview,
    isConnected: false, // Always false since deprecated
    socket: null // Always null since deprecated
  }
}

// DEPRECATED COMMENT - keeping for reference but this hook should not be used
/*
OLD IMPLEMENTATION REMOVED - This hook attempted to use Socket.IO to connect to port 5000,
but the Multi-Agent service uses native WebSockets on port 8000. This caused connection
conflicts and the "WebSocket is not connected. Need to call 'accept' first." error.

Use useEnhancedWebSocket instead, which properly handles native WebSocket connections
to the Multi-Agent service.
*/