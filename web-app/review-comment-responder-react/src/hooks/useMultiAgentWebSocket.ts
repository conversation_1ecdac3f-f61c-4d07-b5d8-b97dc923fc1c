import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import type { MultiAgentReviewStatus } from '../components/MultiAgentProgressTracker'

export interface AgentEvent {
  event_type: 'agent_started' | 'agent_progress' | 'agent_completed' | 'agent_failed'
  timestamp: number
  review_id: string
  agent_type: string
  data?: Record<string, unknown>
  message?: string
}

export interface ReviewEvent {
  event_type: 'review_started' | 'review_completed' | 'review_failed' | 'review_cancelled'
  timestamp: number
  review_id: string
  data?: Record<string, unknown>
  message?: string
}

export interface WebSocketConnectionStatus {
  connected: boolean
  connecting: boolean
  error: string | null
  lastHeartbeat: number | null
}

interface UseMultiAgentWebSocketProps {
  reviewId?: string
  onAgentEvent?: (event: AgentEvent) => void
  onReviewEvent?: (event: ReviewEvent) => void
  onReviewStatusUpdate?: (status: MultiAgentReviewStatus) => void
  onConnectionStatusChange?: (status: WebSocketConnectionStatus) => void
}

interface WebSocketMessage {
  event_type: string
  timestamp: number
  review_id?: string
  agent_type?: string
  data?: Record<string, unknown>
  message?: string
}

export const useMultiAgentWebSocket = ({
  reviewId,
  onAgentEvent,
  onReviewEvent,
  onReviewStatusUpdate,
  onConnectionStatusChange
}: UseMultiAgentWebSocketProps) => {
  const socketRef = useRef<Socket | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<WebSocketConnectionStatus>({
    connected: false,
    connecting: false,
    error: null,
    lastHeartbeat: null
  })
  const [currentReviewStatus, setCurrentReviewStatus] = useState<MultiAgentReviewStatus | null>(null)
  
  // Update connection status and notify parent
  const updateConnectionStatus = useCallback((updates: Partial<WebSocketConnectionStatus>) => {
    setConnectionStatus(prev => {
      const newStatus = { ...prev, ...updates }
      onConnectionStatusChange?.(newStatus)
      return newStatus
    })
  }, [onConnectionStatusChange])
  
  // Join/leave review room
  const joinReviewRoom = useCallback((socket: Socket, reviewId: string) => {
    console.log(`🔗 Joining review room: ${reviewId}`)
    socket.emit('join_review_room', { review_id: reviewId })
  }, [])
  
  const leaveReviewRoom = useCallback((socket: Socket, reviewId: string) => {
    console.log(`🔌 Leaving review room: ${reviewId}`)
    socket.emit('leave_review_room', { review_id: reviewId })
  }, [])
  
  // Process incoming WebSocket messages
  const processWebSocketMessage = useCallback((message: WebSocketMessage) => {
    const { event_type, review_id, agent_type } = message
    
    // Only process messages for the current review (or system messages)
    if (review_id && reviewId && review_id !== reviewId) {
      return
    }
    
    console.log(`📡 WebSocket Event: ${event_type}`, message)
    
    // Agent-specific events
    if (agent_type && ['agent_started', 'agent_progress', 'agent_completed', 'agent_failed'].includes(event_type)) {
      const agentEvent: AgentEvent = {
        event_type: event_type as AgentEvent['event_type'],
        timestamp: message.timestamp,
        review_id: review_id || '',
        agent_type,
        data: message.data,
        message: message.message
      }
      
      onAgentEvent?.(agentEvent)
      
      // Update current review status based on agent events
      if (currentReviewStatus && review_id === currentReviewStatus.review_id) {
        setCurrentReviewStatus(prev => {
          if (!prev) return prev
          
          const updatedAgentStatuses = { ...prev.agent_statuses }
          
          if (updatedAgentStatuses[agent_type]) {
            const agentStatus = updatedAgentStatuses[agent_type]
            
            switch (event_type) {
              case 'agent_started':
                agentStatus.status = 'running'
                agentStatus.started_at = new Date().toISOString()
                break
              case 'agent_progress':
                agentStatus.progress = (message.data?.progress as number) || 0
                break
              case 'agent_completed':
                agentStatus.status = 'completed'
                agentStatus.progress = 100
                agentStatus.completed_at = new Date().toISOString()
                break
              case 'agent_failed':
                agentStatus.status = 'failed'
                agentStatus.completed_at = new Date().toISOString()
                agentStatus.error_message = message.data?.error as string || message.message || 'Unknown error'
                break
            }
          }
          
          // Calculate overall progress
          const agents = Object.values(updatedAgentStatuses).filter(agent => agent.status !== 'skipped')
          const totalProgress = agents.reduce((sum, agent) => sum + agent.progress, 0)
          const overallProgress = agents.length > 0 ? totalProgress / agents.length : 0
          
          // Update active/completed/failed agent lists
          const activeAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'running')
            .map(([type, _]) => type)
          
          const completedAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'completed')
            .map(([type, _]) => type)
          
          const failedAgents = Object.entries(updatedAgentStatuses)
            .filter(([_, status]) => status.status === 'failed')
            .map(([type, _]) => type)
          
          const updatedStatus = {
            ...prev,
            agent_statuses: updatedAgentStatuses,
            progress: overallProgress,
            active_agents: activeAgents,
            completed_agents: completedAgents,
            failed_agents: failedAgents
          }
          
          onReviewStatusUpdate?.(updatedStatus)
          return updatedStatus
        })
      }
    }
    
    // Review-level events
    else if (['review_started', 'review_completed', 'review_failed', 'review_cancelled'].includes(event_type)) {
      const reviewEvent: ReviewEvent = {
        event_type: event_type as ReviewEvent['event_type'],
        timestamp: message.timestamp,
        review_id: review_id || '',
        data: message.data,
        message: message.message
      }
      
      onReviewEvent?.(reviewEvent)
      
      // Update overall review status
      if (currentReviewStatus && review_id === currentReviewStatus.review_id) {
        setCurrentReviewStatus(prev => {
          if (!prev) return prev
          
          let updatedStatus = { ...prev }
          
          switch (event_type) {
            case 'review_started':
              updatedStatus.status = 'running'
              break
            case 'review_completed':
              updatedStatus.status = 'completed'
              updatedStatus.progress = 100
              updatedStatus.completed_at = new Date().toISOString()
              break
            case 'review_failed':
              updatedStatus.status = 'failed'
              updatedStatus.completed_at = new Date().toISOString()
              break
            case 'review_cancelled':
              updatedStatus.status = 'cancelled'
              updatedStatus.completed_at = new Date().toISOString()
              break
          }
          
          onReviewStatusUpdate?.(updatedStatus)
          return updatedStatus
        })
      }
    }
    
    // Connection/system events
    else if (event_type === 'connected') {
      updateConnectionStatus({
        connected: true,
        connecting: false,
        error: null
      })
    }
    else if (event_type === 'heartbeat') {
      updateConnectionStatus({
        lastHeartbeat: Date.now()
      })
    }
  }, [reviewId, currentReviewStatus, onAgentEvent, onReviewEvent, onReviewStatusUpdate, updateConnectionStatus])
  
  // Setup WebSocket connection
  useEffect(() => {
    console.log('🔌 Setting up Multi-Agent WebSocket connection...')
    
    updateConnectionStatus({
      connecting: true,
      error: null
    })
    
    const newSocket = io('http://localhost:5000', {
      transports: ['polling', 'websocket'],
      forceNew: true,
      upgrade: true,
      timeout: 10000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: 5
    })
    
    socketRef.current = newSocket
    
    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('✅ Multi-Agent WebSocket connected successfully')
      updateConnectionStatus({
        connected: true,
        connecting: false,
        error: null
      })
      
      // Join review room if we have a review ID
      if (reviewId) {
        joinReviewRoom(newSocket, reviewId)
      }
    })
    
    newSocket.on('disconnect', (reason) => {
      console.log('❌ Multi-Agent WebSocket disconnected:', reason)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: `Disconnected: ${reason}`
      })
    })
    
    newSocket.on('connect_error', (error) => {
      console.error('❌ Multi-Agent WebSocket connection error:', error)
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: error.message || 'Connection failed'
      })
    })
    
    newSocket.on('reconnect', (attemptNumber) => {
      console.log(`🔄 Multi-Agent WebSocket reconnected (attempt ${attemptNumber})`)
      updateConnectionStatus({
        connected: true,
        connecting: false,
        error: null
      })
      
      // Rejoin review room on reconnection
      if (reviewId) {
        joinReviewRoom(newSocket, reviewId)
      }
    })
    
    newSocket.on('reconnect_failed', () => {
      console.error('💀 Multi-Agent WebSocket reconnection failed')
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: 'Reconnection failed - maximum attempts exceeded'
      })
    })
    
    // Listen for all multi-agent WebSocket events
    const eventTypes = [
      'connected', 'disconnected',
      'review_started', 'review_completed', 'review_failed', 'review_cancelled',
      'agent_started', 'agent_progress', 'agent_completed', 'agent_failed',
      'error', 'heartbeat'
    ]
    
    eventTypes.forEach(eventType => {
      newSocket.on(eventType, (data) => {
        processWebSocketMessage({
          event_type: eventType,
          timestamp: Date.now(),
          ...data
        })
      })
    })
    
    // Cleanup on unmount
    return () => {
      console.log('🔌 Closing Multi-Agent WebSocket connection...')
      
      // Leave review room before disconnecting
      if (reviewId) {
        leaveReviewRoom(newSocket, reviewId)
      }
      
      newSocket.close()
      socketRef.current = null
      
      updateConnectionStatus({
        connected: false,
        connecting: false,
        error: null
      })
    }
  }, [reviewId, processWebSocketMessage, updateConnectionStatus, joinReviewRoom, leaveReviewRoom])
  
  // Handle review ID changes
  useEffect(() => {
    const socket = socketRef.current
    if (!socket || !socket.connected) return
    
    if (reviewId) {
      joinReviewRoom(socket, reviewId)
    }
    
    // Note: We don't leave the previous room here because the effect cleanup will handle it
  }, [reviewId, joinReviewRoom])
  
  // Public API
  const sendMessage = useCallback((eventType: string, data: Record<string, unknown> = {}) => {
    const socket = socketRef.current
    if (!socket || !socket.connected) {
      console.warn('Cannot send message: WebSocket not connected')
      return false
    }
    
    socket.emit(eventType, data)
    return true
  }, [])
  
  const subscribeToReview = useCallback((newReviewId: string) => {
    const socket = socketRef.current
    if (!socket || !socket.connected) {
      console.warn('Cannot subscribe to review: WebSocket not connected')
      return false
    }
    
    joinReviewRoom(socket, newReviewId)
    return true
  }, [joinReviewRoom])
  
  const unsubscribeFromReview = useCallback((oldReviewId: string) => {
    const socket = socketRef.current
    if (!socket || !socket.connected) {
      console.warn('Cannot unsubscribe from review: WebSocket not connected')
      return false
    }
    
    leaveReviewRoom(socket, oldReviewId)
    return true
  }, [leaveReviewRoom])
  
  return {
    connectionStatus,
    currentReviewStatus,
    sendMessage,
    subscribeToReview,
    unsubscribeFromReview,
    isConnected: connectionStatus.connected,
    isConnecting: connectionStatus.connecting,
    error: connectionStatus.error
  }
}