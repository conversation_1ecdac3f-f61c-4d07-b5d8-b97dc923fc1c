/**
 * Feature Flags Hook
 * Advanced React hook for feature flag management with caching and performance optimizations
 */

import { useMemo, useCallback, useState, useEffect, useRef } from 'react'
import { useFeatureFlags as useFeatureFlagsContext } from '../components/FeatureFlagProvider'
import { FeatureFlags } from '../config/featureFlags'
import { env } from '../config/environment'

/**
 * Feature flag hook return type
 */
export interface UseFeatureFlagsReturn {
  // Basic flag checks
  isEnabled: (flag: string) => boolean
  isMultiAgentEnabled: boolean
  isRealtimeEnabled: boolean
  isFallbackEnabled: boolean
  isEnhancedReportsEnabled: boolean
  
  // Environment checks
  isDevelopment: boolean
  isProduction: boolean
  isStaging: boolean
  
  // Advanced features
  getExperimentGroup: (experiment: string) => string
  isInExperimentGroup: (experiment: string, group: string) => boolean
  
  // Performance features
  shouldLazyLoad: boolean
  shouldPreloadRoutes: boolean
  shouldUseServiceWorker: boolean
  
  // Configuration getters
  getWebSocketConfig: () => {
    enabled: boolean
    autoReconnect: boolean
    maxReconnectAttempts: number
    reconnectDelay: number
    heartbeatInterval: number
  }
  getFallbackConfig: () => {
    enabled: boolean
    fallbackOnTimeout: boolean
    fallbackOnError: boolean
    maxAttempts: number
    showNotifications: boolean
  }
  
  // Monitoring and analytics
  shouldTrackUsage: boolean
  shouldTrackErrors: boolean
  shouldTrackPerformance: boolean
  
  // UI features
  shouldShowDebugInfo: boolean
  shouldUseModernUI: boolean
  shouldShowBetaBadges: boolean
  
  // Remote configuration
  refreshFlags: () => Promise<void>
  isLoading: boolean
  lastUpdate: Date | null
  
  // Development utilities
  getAllFlags: () => Record<string, boolean>
  exportDebugInfo: () => any
}

/**
 * Cache for feature flag evaluations
 */
class FeatureFlagCache {
  private cache = new Map<string, { value: boolean; timestamp: number }>()
  private ttl = 5 * 60 * 1000 // 5 minutes

  get(key: string): boolean | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.value
  }

  set(key: string, value: boolean): void {
    this.cache.set(key, { value, timestamp: Date.now() })
  }

  clear(): void {
    this.cache.clear()
  }

  size(): number {
    return this.cache.size
  }
}

// Global cache instance
const featureFlagCache = new FeatureFlagCache()

/**
 * Performance-optimized feature flag evaluation
 */
const evaluateFeatureFlag = (flagPath: string): boolean => {
  // Check cache first
  const cached = featureFlagCache.get(flagPath)
  if (cached !== null) {
    return cached
  }

  try {
    const pathParts = flagPath.split('.')
    let current: any = FeatureFlags
    
    for (const part of pathParts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part]
      } else {
        featureFlagCache.set(flagPath, false)
        return false
      }
    }
    
    const result = typeof current === 'boolean' ? current : false
    featureFlagCache.set(flagPath, result)
    return result
  } catch {
    featureFlagCache.set(flagPath, false)
    return false
  }
}

/**
 * Hook for feature flags with performance optimizations
 */
export const useFeatureFlags = (): UseFeatureFlagsReturn => {
  const context = useFeatureFlagsContext()
  const [cacheVersion, setCacheVersion] = useState(0)
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Memoized flag evaluations
  const flags = useMemo(() => ({
    isMultiAgentEnabled: context.isMultiAgentEnabled(),
    isRealtimeEnabled: context.isRealtimeEnabled(),
    isFallbackEnabled: context.isFallbackEnabled(),
    isEnhancedReportsEnabled: evaluateFeatureFlag('ENHANCED_REPORTS.enabled'),
    
    // Environment flags
    isDevelopment: env.isDevelopment,
    isProduction: env.isProduction,
    isStaging: env.isStaging,
    
    // Performance flags
    shouldLazyLoad: evaluateFeatureFlag('PERFORMANCE.lazyLoadComponents'),
    shouldPreloadRoutes: evaluateFeatureFlag('PERFORMANCE.preloadCriticalRoutes'),
    shouldUseServiceWorker: evaluateFeatureFlag('PERFORMANCE.enableServiceWorker'),
    
    // Monitoring flags
    shouldTrackUsage: evaluateFeatureFlag('ANALYTICS.enableUsageTracking'),
    shouldTrackErrors: evaluateFeatureFlag('ANALYTICS.enableErrorTracking'),
    shouldTrackPerformance: evaluateFeatureFlag('ANALYTICS.enablePerformanceTracking'),
    
    // UI flags
    shouldShowDebugInfo: evaluateFeatureFlag('DEVELOPMENT.enableDebugMode'),
    shouldUseModernUI: evaluateFeatureFlag('UI_ENHANCEMENTS.modernProgressTracker'),
    shouldShowBetaBadges: evaluateFeatureFlag('MULTI_AGENT_REVIEWS.betaMode')
  }), [context, cacheVersion])

  // Optimized flag checker with caching
  const isEnabled = useCallback((flag: string): boolean => {
    return evaluateFeatureFlag(flag)
  }, [cacheVersion])

  // Experiment methods
  const getExperimentGroup = useCallback((experiment: string): string => {
    return context.getExperimentGroup(experiment)
  }, [context])

  const isInExperimentGroup = useCallback((experiment: string, group: string): boolean => {
    return context.isInExperimentGroup(experiment, group)
  }, [context])

  // Configuration getters
  const getWebSocketConfig = useCallback(() => {
    return context.getWebSocketConfig()
  }, [context])

  const getFallbackConfig = useCallback(() => {
    return context.getFallbackConfig()
  }, [context])

  // Remote configuration management
  const refreshFlags = useCallback(async (): Promise<void> => {
    featureFlagCache.clear()
    setCacheVersion(prev => prev + 1)
    
    await context.refreshFlags()
    
    // Clear timeout if exists
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current)
    }
    
    // Set up next refresh
    refreshTimeoutRef.current = setTimeout(() => {
      refreshFlags()
    }, 5 * 60 * 1000) // 5 minutes
  }, [context])

  // Development utilities
  const getAllFlags = useCallback((): Record<string, boolean> => {
    const allFlags: Record<string, boolean> = { ...flags }
    const customFlags = context.getAllEnabledFeatures()
    
    // Convert custom flags to boolean values if needed
    if (typeof customFlags === 'object') {
      Object.assign(allFlags, customFlags)
    }
    
    return allFlags
  }, [flags, context])

  const exportDebugInfo = useCallback(() => {
    if (!flags.shouldShowDebugInfo) return null
    
    return {
      flags,
      context: context.exportDebugInfo(),
      cache: {
        size: featureFlagCache.size(),
        version: cacheVersion
      },
      environment: {
        mode: env.NODE_ENV,
        version: env.deployment.version,
        buildTimestamp: env.deployment.buildTimestamp
      }
    }
  }, [flags, context, cacheVersion])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current)
      }
    }
  }, [])

  // Auto-refresh in production
  useEffect(() => {
    if (env.isProduction && flags.shouldTrackUsage) {
      refreshFlags()
    }
  }, [refreshFlags, flags.shouldTrackUsage])

  return {
    // Basic flag checks
    isEnabled,
    isMultiAgentEnabled: flags.isMultiAgentEnabled,
    isRealtimeEnabled: flags.isRealtimeEnabled,
    isFallbackEnabled: flags.isFallbackEnabled,
    isEnhancedReportsEnabled: flags.isEnhancedReportsEnabled,
    
    // Environment checks
    isDevelopment: flags.isDevelopment,
    isProduction: flags.isProduction,
    isStaging: flags.isStaging,
    
    // Advanced features
    getExperimentGroup,
    isInExperimentGroup,
    
    // Performance features
    shouldLazyLoad: flags.shouldLazyLoad,
    shouldPreloadRoutes: flags.shouldPreloadRoutes,
    shouldUseServiceWorker: flags.shouldUseServiceWorker,
    
    // Configuration getters
    getWebSocketConfig,
    getFallbackConfig,
    
    // Monitoring and analytics
    shouldTrackUsage: flags.shouldTrackUsage,
    shouldTrackErrors: flags.shouldTrackErrors,
    shouldTrackPerformance: flags.shouldTrackPerformance,
    
    // UI features
    shouldShowDebugInfo: flags.shouldShowDebugInfo,
    shouldUseModernUI: flags.shouldUseModernUI,
    shouldShowBetaBadges: flags.shouldShowBetaBadges,
    
    // Remote configuration
    refreshFlags,
    isLoading: context.isLoading,
    lastUpdate: context.lastUpdate,
    
    // Development utilities
    getAllFlags,
    exportDebugInfo
  }
}

/**
 * Specialized hooks for specific feature areas
 */

/**
 * Multi-Agent specific feature flags
 */
export const useMultiAgentFeatures = () => {
  const { isMultiAgentEnabled, getExperimentGroup, shouldShowBetaBadges } = useFeatureFlags()
  
  return useMemo(() => ({
    enabled: isMultiAgentEnabled,
    showBetaBadge: shouldShowBetaBadges,
    rolloutGroup: getExperimentGroup('multi_agent_rollout'),
    
    // Feature-specific checks
    isFullyEnabled: isMultiAgentEnabled && getExperimentGroup('multi_agent_rollout') === 'treatment',
    isTestGroup: getExperimentGroup('multi_agent_rollout') === 'treatment',
    isControlGroup: getExperimentGroup('multi_agent_rollout') === 'control'
  }), [isMultiAgentEnabled, getExperimentGroup, shouldShowBetaBadges])
}

/**
 * Performance optimization feature flags
 */
export const usePerformanceFeatures = () => {
  const { 
    shouldLazyLoad, 
    shouldPreloadRoutes, 
    shouldUseServiceWorker,
    isEnabled
  } = useFeatureFlags()
  
  return useMemo(() => ({
    lazyLoading: shouldLazyLoad,
    routePreloading: shouldPreloadRoutes,
    serviceWorker: shouldUseServiceWorker,
    
    // Advanced optimizations
    bundleSplitting: isEnabled('PERFORMANCE.enableCodeSplitting'),
    imageOptimization: isEnabled('PERFORMANCE.optimizeImageLoading'),
    resultCaching: isEnabled('PERFORMANCE.cacheReviewResults')
  }), [shouldLazyLoad, shouldPreloadRoutes, shouldUseServiceWorker, isEnabled])
}

/**
 * Monitoring and analytics feature flags
 */
export const useMonitoringFeatures = () => {
  const { 
    shouldTrackUsage, 
    shouldTrackErrors, 
    shouldTrackPerformance,
    isEnabled 
  } = useFeatureFlags()
  
  return useMemo(() => ({
    usageTracking: shouldTrackUsage,
    errorTracking: shouldTrackErrors,
    performanceTracking: shouldTrackPerformance,
    
    // Advanced monitoring
    anonymizeData: isEnabled('ANALYTICS.anonymizeUserData'),
    realTimeMetrics: isEnabled('MONITORING.enableRealTimeMetrics'),
    alerting: isEnabled('MONITORING.enableAlerting')
  }), [shouldTrackUsage, shouldTrackErrors, shouldTrackPerformance, isEnabled])
}

/**
 * Development and debugging feature flags
 */
export const useDebugFeatures = () => {
  const { shouldShowDebugInfo, isDevelopment, isEnabled, exportDebugInfo } = useFeatureFlags()
  
  return useMemo(() => ({
    debugMode: shouldShowDebugInfo,
    isDev: isDevelopment,
    
    // Debug features
    apiLogs: isEnabled('DEVELOPMENT.showApiLogs'),
    performancePanel: isEnabled('DEVELOPMENT.showPerformancePanel'),
    mockServices: isEnabled('DEVELOPMENT.enableMockServices'),
    errorBoundaryInfo: isEnabled('DEVELOPMENT.enableErrorBoundaryInfo'),
    
    // Debug utilities
    exportInfo: exportDebugInfo
  }), [shouldShowDebugInfo, isDevelopment, isEnabled, exportDebugInfo])
}

export default useFeatureFlags