/**
 * useMultiAgentReview Hook
 * React Hook for managing Multi-Agent Review state and integration with MultiAgentReviewService
 */

import { useReducer, useCallback, useEffect, useRef } from 'react'
import { MultiAgentReviewService } from '../services/multiAgent/MultiAgentReviewService'
import { useEnhancedWebSocket } from './useEnhancedWebSocket'
import type {
  MultiAgentReviewState,
  MultiAgentAction,
  UseMultiAgentReviewConfig,
  UseMultiAgentReviewResult,
  // AgentStatusStats,
} from '../types/multi-agent-status'
import type {
  AgentType,
  MultiAgentReviewRequest,
  MultiAgentWebSocketEvent,
  MultiAgentError
} from '../types/multi-agent'
import type { AgentStatus, AgentResult } from '../types/enhanced-review'
import { DEFAULT_MULTI_AGENT_STATE } from '../types/multi-agent-status'
// import { featureFlags } from '../config/featureFlags'

// State reducer
function multiAgentReducer(state: MultiAgentReviewState, action: MultiAgentAction): MultiAgentReviewState {
  switch (action.type) {
    case 'START_REVIEW':
      return {
        ...state,
        is_starting: true,
        is_loading: true,
        error: undefined,
        last_error: undefined
      }

    case 'REVIEW_STARTED':
      return {
        ...state,
        review_id: action.payload.review_id,
        session_id: action.payload.session_id,
        status: action.payload.status,
        agent_statuses: action.payload.initial_agent_statuses,
        started_at: action.payload.created_at,
        context_status: action.payload.context_preparation_status,
        is_starting: false,
        is_loading: false
      }

    case 'STATUS_UPDATE':
      return {
        ...state,
        status: action.payload.status,
        progress: action.payload.progress,
        agent_statuses: action.payload.agent_statuses,
        active_agents: action.payload.active_agents as AgentType[],
        completed_agents: action.payload.completed_agents as AgentType[],
        failed_agents: action.payload.failed_agents as AgentType[],
        estimated_remaining_time: action.payload.estimated_remaining_time,
        completed_at: action.payload.completed_at,
        context_status: action.payload.context_status
      }

    case 'AGENT_STARTED':
      return {
        ...state,
        agent_statuses: {
          ...state.agent_statuses,
          [action.payload.agent_type]: {
            ...state.agent_statuses[action.payload.agent_type],
            status: 'running',
            started_at: action.payload.started_at
          }
        },
        active_agents: state.active_agents.includes(action.payload.agent_type)
          ? state.active_agents
          : [...state.active_agents, action.payload.agent_type]
      }

    case 'AGENT_PROGRESS':
      return {
        ...state,
        agent_statuses: {
          ...state.agent_statuses,
          [action.payload.agent_type]: {
            ...state.agent_statuses[action.payload.agent_type],
            progress: action.payload.progress,
            current_step: action.payload.message
          }
        }
      }

    case 'AGENT_COMPLETED':
      return {
        ...state,
        agent_statuses: {
          ...state.agent_statuses,
          [action.payload.agent_type]: {
            ...state.agent_statuses[action.payload.agent_type],
            status: 'completed',
            progress: 100,
            completed_at: action.payload.completed_at,
            result: action.payload.result
          }
        },
        completed_agents: state.completed_agents.includes(action.payload.agent_type)
          ? state.completed_agents
          : [...state.completed_agents, action.payload.agent_type],
        active_agents: state.active_agents.filter(agent => agent !== action.payload.agent_type)
      }

    case 'AGENT_FAILED':
      return {
        ...state,
        agent_statuses: {
          ...state.agent_statuses,
          [action.payload.agent_type]: {
            ...state.agent_statuses[action.payload.agent_type],
            status: 'failed',
            error_message: action.payload.error
          }
        },
        failed_agents: state.failed_agents.includes(action.payload.agent_type)
          ? state.failed_agents
          : [...state.failed_agents, action.payload.agent_type],
        active_agents: state.active_agents.filter(agent => agent !== action.payload.agent_type)
      }

    case 'REVIEW_COMPLETED':
      return {
        ...state,
        status: 'completed',
        progress: 100,
        completed_at: action.payload.completed_at,
        results: action.payload,
        is_loading: false,
        performance_metrics: {
          parallel_efficiency: action.payload.overall_results.execution_metrics.parallel_efficiency,
          total_execution_time: action.payload.overall_results.execution_metrics.total_execution_time,
          agent_completion_order: state.completed_agents
        }
      }

    case 'REVIEW_FAILED':
      return {
        ...state,
        status: 'failed',
        error: action.payload.error,
        last_error: action.payload.error.error_message,
        is_loading: false,
        is_starting: false
      }

    case 'REVIEW_CANCELLED':
      return {
        ...state,
        status: 'cancelled',
        is_loading: false,
        is_starting: false,
        is_cancelling: false
      }

    case 'SET_LOADING':
      return {
        ...state,
        is_loading: action.payload.loading
      }

    case 'SET_ERROR':
      return {
        ...state,
        last_error: action.payload.error,
        is_loading: false,
        is_starting: false
      }

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: undefined,
        last_error: undefined
      }

    case 'RESET_STATE':
      return DEFAULT_MULTI_AGENT_STATE

    default:
      return state
  }
}

// Main hook
export const useMultiAgentReview = (config: UseMultiAgentReviewConfig = {}): UseMultiAgentReviewResult => {
  const [state, dispatch] = useReducer(multiAgentReducer, DEFAULT_MULTI_AGENT_STATE)
  const serviceRef = useRef<MultiAgentReviewService | null>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize service
  if (!serviceRef.current) {
    serviceRef.current = new MultiAgentReviewService()
  }

  // WebSocket event handlers
  const handleMultiAgentEvent = useCallback((event: MultiAgentWebSocketEvent) => {
    if (!state.review_id || event.review_id !== state.review_id) {
      return // Ignore events for other reviews
    }

    if (config.enable_debug_logs) {
      console.log('📡 Multi-Agent WebSocket Event:', event)
    }

    switch (event.event_type) {
      case 'review_started':
        // This should be handled by the startReview method
        break

      case 'agent_started':
        if (event.agent_type) {
          dispatch({
            type: 'AGENT_STARTED',
            payload: {
              agent_type: event.agent_type,
              started_at: new Date(event.timestamp).toISOString()
            }
          })
        }
        break

      case 'agent_progress':
        if (event.agent_type && event.data?.progress !== undefined) {
          dispatch({
            type: 'AGENT_PROGRESS',
            payload: {
              agent_type: event.agent_type,
              progress: event.data.progress as number,
              message: event.data.current_step as string
            }
          })
        }
        break

      case 'agent_completed':
        if (event.agent_type) {
          dispatch({
            type: 'AGENT_COMPLETED',
            payload: {
              agent_type: event.agent_type,
              completed_at: new Date(event.timestamp).toISOString(),
              result: event.data?.result as AgentResult | undefined
            }
          })
          config.on_agent_completed?.(event.agent_type, event.data?.result as AgentResult | undefined)
        }
        break

      case 'agent_failed':
        if (event.agent_type) {
          dispatch({
            type: 'AGENT_FAILED',
            payload: {
              agent_type: event.agent_type,
              error: event.data?.error_message as string || 'Agent failed'
            }
          })
        }
        break

      case 'review_completed':
        if (state.review_id) {
          // Fetch complete results
          fetchResults()
        }
        break

      case 'review_failed':
        dispatch({
          type: 'REVIEW_FAILED',
          payload: {
            error: {
              error_code: 'REVIEW_FAILED',
              error_message: event.data?.error_message as string || 'Review failed',
              review_id: event.review_id,
              timestamp: new Date(event.timestamp).toISOString(),
              retry_possible: true
            }
          }
        })
        config.on_error?.({
          error_code: 'REVIEW_FAILED',
          error_message: event.data?.error_message as string || 'Review failed',
          review_id: event.review_id,
          timestamp: new Date(event.timestamp).toISOString(),
          retry_possible: true
        })
        break

      case 'review_cancelled':
        dispatch({
          type: 'REVIEW_CANCELLED',
          payload: {
            reason: event.data?.reason as string
          }
        })
        break
    }
  }, [state.review_id, config])

  // WebSocket connection
  // WebSocket connection (currently unused but ready for future implementation)
  // @ts-ignore - WebSocket unused but ready for future implementation
  const _webSocket = useEnhancedWebSocket({
    serviceType: 'multi-agent',
    reviewId: state.review_id || undefined,
    sessionId: state.session_id || undefined,
    reviewMode: 'multi_agent',
    handlers: {
      onMultiAgentEvent: (event: any) => handleMultiAgentEvent(event),
      onLegacyEvent: () => {}, // Not used for multi-agent
      onAnyEvent: (event) => {
        if (config.enable_debug_logs) {
          console.log('📡 Any WebSocket Event:', event)
        }
      },
      onError: (error) => {
        console.error('❌ WebSocket Error:', error)
        dispatch({
          type: 'SET_ERROR',
          payload: { error: `WebSocket error: ${error.message}` }
        })
      },
      onConnected: () => {
        if (config.enable_debug_logs) {
          console.log('✅ Multi-Agent WebSocket connected')
        }
      },
      onDisconnected: () => {
        if (config.enable_debug_logs) {
          console.log('❌ Multi-Agent WebSocket disconnected')
        }
      }
    },
    autoConnect: config.enable_websocket !== false,
    enableEventLogging: config.enable_debug_logs,
    enableLatencyTracking: config.track_performance
  })

  // Start review
  const startReview = useCallback(async (request: MultiAgentReviewRequest) => {
    if (!serviceRef.current) return

    dispatch({ type: 'START_REVIEW', payload: { request } })

    try {
      const response = await serviceRef.current.startParallelReview(request)
      
      if (response.success && response.data) {
        dispatch({
          type: 'REVIEW_STARTED',
          payload: response.data
        })

        config.on_review_started?.(response.data)
      } else {
        throw new Error(response.error?.error_message || 'Failed to start review')
      }

      // Start status polling if enabled
      if (config.polling_interval && config.polling_interval > 0) {
        startStatusPolling()
      }

    } catch (error) {
      const multiAgentError: MultiAgentError = {
        error_code: 'START_REVIEW_FAILED',
        error_message: error instanceof Error ? error.message : 'Failed to start review',
        timestamp: new Date().toISOString(),
        retry_possible: true
      }
      
      dispatch({
        type: 'REVIEW_FAILED',
        payload: { error: multiAgentError }
      })

      config.on_error?.(multiAgentError)
    }
  }, [config])

  // Cancel review
  const cancelReview = useCallback(async () => {
    if (!serviceRef.current || !state.review_id) return

    try {
      state.is_cancelling = true
      await serviceRef.current.cancelReview(state.review_id)
      
      dispatch({
        type: 'REVIEW_CANCELLED',
        payload: { reason: 'Cancelled by user' }
      })
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: { error: `Failed to cancel review: ${error instanceof Error ? error.message : 'Unknown error'}` }
      })
    }
  }, [state.review_id])

  // Retry review
  const retryReview = useCallback(async () => {
    if (!state.review_id) return
    
    // Reset state and restart (implementation depends on stored request)
    dispatch({ type: 'RESET_STATE' })
    // Note: This would need the original request to be stored
  }, [state.review_id])

  // Refresh status
  const refreshStatus = useCallback(async () => {
    if (!serviceRef.current || !state.review_id) return

    try {
      const response = await serviceRef.current.getReviewStatus(state.review_id)
      if (response.success && response.data) {
        dispatch({
          type: 'STATUS_UPDATE',
          payload: response.data
        })
        config.on_status_update?.(response.data)
      }
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: { error: `Failed to refresh status: ${error instanceof Error ? error.message : 'Unknown error'}` }
      })
    }
  }, [state.review_id, config])

  // Fetch results
  const fetchResults = useCallback(async () => {
    if (!serviceRef.current || !state.review_id) return

    try {
      dispatch({ type: 'SET_LOADING', payload: { loading: true } })
      const response = await serviceRef.current.getReviewResults(state.review_id)
      
      if (response.success && response.data) {
        dispatch({
          type: 'REVIEW_COMPLETED',
          payload: response.data
        })

        config.on_review_completed?.(response.data)
      } else {
        throw new Error(response.error?.error_message || 'Failed to fetch results')
      }
    } catch (error) {
      dispatch({
        type: 'SET_ERROR',
        payload: { error: `Failed to fetch results: ${error instanceof Error ? error.message : 'Unknown error'}` }
      })
    }
  }, [state.review_id, config])

  // Status polling
  const startStatusPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
    }

    pollingIntervalRef.current = setInterval(() => {
      if (state.status === 'running' && state.review_id) {
        refreshStatus()
      }
    }, config.polling_interval || 5000)
  }, [state.status, state.review_id, config.polling_interval, refreshStatus])

  // Stop polling
  const stopStatusPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
  }, [])

  // Cleanup
  useEffect(() => {
    return () => {
      stopStatusPolling()
    }
  }, [stopStatusPolling])

  // Auto-fetch results when completed
  useEffect(() => {
    if (state.status === 'completed' && !state.results && config.auto_fetch_results !== false) {
      fetchResults()
    }
  }, [state.status, state.results, config.auto_fetch_results, fetchResults])

  // Computed values
  const isActive = state.status === 'running' || state.status === 'started'
  const isCompleted = state.status === 'completed'
  const isFailed = state.status === 'failed'
  const hasError = !!state.error || !!state.last_error

  const overallProgress = state.progress
  const activeAgentCount = state.active_agents.length
  const completedAgentCount = state.completed_agents.length
  const failedAgentCount = state.failed_agents.length

  // Agent utilities
  const getAgentStatus = (agent_type: AgentType): AgentStatus | undefined => {
    return state.agent_statuses[agent_type]
  }

  const getAgentProgress = (agent_type: AgentType): number => {
    return state.agent_statuses[agent_type]?.progress || 0
  }

  const isAgentActive = (agent_type: AgentType): boolean => {
    return state.active_agents.includes(agent_type)
  }

  const isAgentCompleted = (agent_type: AgentType): boolean => {
    return state.completed_agents.includes(agent_type)
  }

  const isAgentFailed = (agent_type: AgentType): boolean => {
    return state.failed_agents.includes(agent_type)
  }

  // Performance utilities
  const getExecutionTime = (): number | null => {
    if (!state.started_at) return null
    const endTime = state.completed_at ? new Date(state.completed_at) : new Date()
    const startTime = new Date(state.started_at)
    return Math.floor((endTime.getTime() - startTime.getTime()) / 1000)
  }

  const getParallelEfficiency = (): number | null => {
    return state.performance_metrics?.parallel_efficiency || null
  }

  const getAgentExecutionTime = (agent_type: AgentType): number | null => {
    const agent = state.agent_statuses[agent_type]
    if (!agent?.started_at || !agent?.completed_at) return null
    
    const start = new Date(agent.started_at)
    const end = new Date(agent.completed_at)
    return Math.floor((end.getTime() - start.getTime()) / 1000)
  }

  // Error utilities
  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' })
  }, [])

  const getLastError = (): string | null => {
    return state.last_error || null
  }

  // Reset utilities
  const reset = useCallback(() => {
    stopStatusPolling()
    dispatch({ type: 'RESET_STATE' })
  }, [stopStatusPolling])

  return {
    state,
    startReview,
    cancelReview,
    retryReview,
    refreshStatus,
    fetchResults,
    isActive,
    isCompleted,
    isFailed,
    hasError,
    overallProgress,
    activeAgentCount,
    completedAgentCount,
    failedAgentCount,
    getAgentStatus,
    getAgentProgress,
    isAgentActive,
    isAgentCompleted,
    isAgentFailed,
    getExecutionTime,
    getParallelEfficiency,
    getAgentExecutionTime,
    clearError,
    getLastError,
    reset
  }
}