import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { AuthState } from '../types/bitbucket.types'
import { bitbucketAuthService } from '../services/auth/BitbucketAuthService'

interface AuthStore extends AuthState {
  // Actions
  login: () => Promise<void>
  handleCallback: (code: string, state: string) => Promise<void>
  logout: () => void
  refreshAccessToken: () => Promise<void>
  checkAuthStatus: () => void
  
  // Helper methods
  isTokenValid: () => boolean
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
}

export const useAuth = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
      expiresAt: null,
      isLoading: false,
      error: null,

      // Actions
      login: async () => {
        try {
          set({ isLoading: true, error: null })
          
          if (!bitbucketAuthService.isConfigured()) {
            throw new Error('Bitbucket OAuth is not configured. Please check your environment variables.')
          }

          await bitbucketAuthService.initiateLogin()
          // User will be redirected, so loading state will persist until callback
        } catch (error) {
          console.error('Login failed:', error)
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Login failed' 
          })
        }
      },

      handleCallback: async (code: string, state: string) => {
        try {
          set({ isLoading: true, error: null })
          
          const authState = await bitbucketAuthService.handleCallback(code, state)
          
          set({
            isAuthenticated: authState.isAuthenticated,
            user: authState.user,
            accessToken: authState.accessToken,
            refreshToken: authState.refreshToken,
            expiresAt: authState.expiresAt,
            isLoading: false,
            error: null
          })
        } catch (error) {
          console.error('OAuth callback failed:', error)
          set({
            isAuthenticated: false,
            user: null,
            accessToken: null,
            refreshToken: null,
            expiresAt: null,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Authentication failed'
          })
        }
      },

      logout: () => {
        bitbucketAuthService.logout()
        set({
          isAuthenticated: false,
          user: null,
          accessToken: null,
          refreshToken: null,
          expiresAt: null,
          isLoading: false,
          error: null
        })
      },

      refreshAccessToken: async () => {
        const state = get()
        if (!state.refreshToken) {
          get().logout()
          return
        }

        try {
          set({ isLoading: true })
          
          const newAuthState = await bitbucketAuthService.refreshAccessToken(state.refreshToken)
          
          set({
            isAuthenticated: newAuthState.isAuthenticated,
            user: newAuthState.user,
            accessToken: newAuthState.accessToken,
            refreshToken: newAuthState.refreshToken,
            expiresAt: newAuthState.expiresAt,
            isLoading: false,
            error: null
          })
        } catch (error) {
          console.error('Token refresh failed:', error)
          get().logout()
        }
      },

      checkAuthStatus: () => {
        try {
          const storedAuth = bitbucketAuthService.getStoredAuthState()
          
          if (storedAuth) {
            set({
              isAuthenticated: storedAuth.isAuthenticated,
              user: storedAuth.user,
              accessToken: storedAuth.accessToken,
              refreshToken: storedAuth.refreshToken,
              expiresAt: storedAuth.expiresAt,
              isLoading: false,
              error: null
            })

            // Check if token needs refresh
            const state = get()
            if (state.expiresAt && bitbucketAuthService.isTokenExpired(state.expiresAt)) {
              state.refreshAccessToken()
            }
          } else {
            // No valid auth state found
            set({
              isAuthenticated: false,
              user: null,
              accessToken: null,
              refreshToken: null,
              expiresAt: null,
              isLoading: false,
              error: null
            })
          }
        } catch (error) {
          console.error('Auth status check failed:', error)
          get().logout()
        }
      },

      // Helper methods
      isTokenValid: (): boolean => {
        const state = get()
        return Boolean(state.isAuthenticated && 
               state.accessToken && 
               state.expiresAt && 
               !bitbucketAuthService.isTokenExpired(state.expiresAt))
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      setError: (error: string | null) => {
        set({ error })
      }
    }),
    {
      name: 'bitbucket-auth',
      // Only persist non-sensitive data
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        expiresAt: state.expiresAt
        // Note: Don't persist tokens in Zustand persist - they're handled by AuthService
      })
    }
  )
)

// Hook for easy access to auth methods
export const useAuthActions = () => {
  const { login, logout, handleCallback, refreshAccessToken, checkAuthStatus } = useAuth()
  return { login, logout, handleCallback, refreshAccessToken, checkAuthStatus }
}

// Hook for auth status only (prevents unnecessary re-renders)
export const useAuthStatus = () => {
  const { isAuthenticated, user, accessToken, isLoading, error, isTokenValid } = useAuth()
  return { 
    isAuthenticated, 
    user, 
    accessToken, 
    isLoading, 
    error, 
    isTokenValid: isTokenValid(),
    // Create a combined authState object for easier consumption
    authState: {
      isAuthenticated,
      user,
      accessToken,
      isLoading,
      error
    }
  }
}

// Hook for making authenticated API calls
export const useAuthenticatedRequest = () => {
  const { isTokenValid, refreshAccessToken } = useAuth()

  const makeRequest = async <T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> => {
    // Check if token is valid
    if (!isTokenValid()) {
      try {
        await refreshAccessToken()
      } catch (error) {
        throw new Error('Authentication required. Please login again.')
      }
    }

    const state = useAuth.getState()
    if (!state.accessToken) {
      throw new Error('No access token available')
    }

    return bitbucketAuthService.makeAuthenticatedRequest<T>(endpoint, state.accessToken, options)
  }

  return { makeRequest }
}