/**
 * Environment Configuration Tests
 * Test suite for production-ready environment management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { getCurrentEnvironment, env, type Environment } from '../environment'

// Mock import.meta.env
const mockImportMeta = {
  env: {} as Record<string, string>,
  MODE: 'test'
}

// Mock process.env
const originalProcessEnv = process.env

describe('Environment Configuration', () => {
  beforeEach(() => {
    // Reset environment variables
    process.env = { ...originalProcessEnv }
    mockImportMeta.env = {}
    
    // Mock import.meta
    vi.stubGlobal('import.meta', mockImportMeta)
  })

  afterEach(() => {
    process.env = originalProcessEnv
    vi.unstubAllGlobals()
  })

  describe('getCurrentEnvironment', () => {
    it('should return development by default', () => {
      expect(getCurrentEnvironment()).toBe('development')
    })

    it('should return environment from import.meta.env.MODE', () => {
      mockImportMeta.MODE = 'production'
      expect(getCurrentEnvironment()).toBe('production')
    })

    it('should return environment from process.env.NODE_ENV', () => {
      process.env.NODE_ENV = 'staging'
      expect(getCurrentEnvironment()).toBe('staging')
    })

    it('should handle unknown environments gracefully', () => {
      mockImportMeta.MODE = 'unknown'
      const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      expect(getCurrentEnvironment()).toBe('development')
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'Unknown environment: unknown, defaulting to development'
      )
      
      consoleWarnSpy.mockRestore()
    })
  })

  describe('Environment Configuration Building', () => {
    it('should build valid development configuration', () => {
      mockImportMeta.MODE = 'development'
      mockImportMeta.env = {
        VITE_MULTI_AGENT_URL: 'http://localhost:5000',
        VITE_FEATURE_DEBUG_MODE: 'true'
      }

      // Re-import to get fresh config
      expect(env.NODE_ENV).toBe('development')
      expect(env.isDevelopment).toBe(true)
      expect(env.isProduction).toBe(false)
      expect(env.features.debugMode).toBe(true)
    })

    it('should build valid production configuration', () => {
      mockImportMeta.MODE = 'production'
      mockImportMeta.env = {
        VITE_MULTI_AGENT_URL: 'https://api.example.com',
        VITE_FEATURE_DEBUG_MODE: 'false',
        VITE_FEATURE_ERROR_TRACKING: 'true'
      }

      // Mock the environment config creation for production
      const mockConfig = {
        NODE_ENV: 'production' as Environment,
        isProduction: true,
        isDevelopment: false,
        isStaging: false,
        isTest: false,
        api: {
          multiAgent: {
            baseUrl: 'https://api.example.com',
            wsUrl: 'wss://api.example.com',
            timeout: 10000,
            retries: 3
          },
          legacyCodeReviewer: {
            baseUrl: 'http://localhost:5002',
            timeout: 15000
          },
          legacyClaude: {
            baseUrl: 'http://localhost:5001',
            timeout: 30000
          },
          bitbucket: {
            baseUrl: 'https://api.bitbucket.org/2.0',
            timeout: 10000
          },
          jira: {
            baseUrl: 'https://rma.atlassian.net',
            timeout: 10000
          }
        },
        features: {
          multiAgentReviews: false,
          realtimeUpdates: true,
          enhancedReports: true,
          serviceWorker: true,
          analytics: true,
          debugMode: false
        },
        performance: {
          enableCodeSplitting: true,
          enableLazyLoading: true,
          enableCaching: true,
          bundleAnalyzer: false,
          preloadRoutes: true
        },
        monitoring: {
          enableErrorTracking: true,
          enablePerformanceTracking: true,
          enableUsageTracking: false,
          anonymizeData: true,
          sampleRate: 10
        },
        security: {
          enableCSP: true,
          enableHSTS: true,
          enableXSSProtection: true,
          trustedDomains: ['localhost', '127.0.0.1']
        },
        deployment: {
          version: '0.0.0',
          buildTimestamp: expect.any(String),
          commitHash: 'unknown',
          deploymentId: expect.stringMatching(/^local-\d+$/)
        }
      }

      expect(mockConfig.NODE_ENV).toBe('production')
      expect(mockConfig.isProduction).toBe(true)
      expect(mockConfig.features.debugMode).toBe(false)
      expect(mockConfig.features.analytics).toBe(true)
      expect(mockConfig.security.enableCSP).toBe(true)
    })
  })

  describe('Environment Variable Validation', () => {
    it('should throw error for missing required variables', () => {
      // Test would need to be implemented with actual validation
      expect(() => {
        // This would trigger validation in actual implementation
        new URL('invalid-url')
      }).toThrow()
    })

    it('should validate URL formats', () => {
      const validUrls = [
        'http://localhost:5000',
        'https://api.example.com',
        'wss://websocket.example.com'
      ]

      validUrls.forEach(url => {
        expect(() => new URL(url)).not.toThrow()
      })

      const invalidUrls = [
        'not-a-url',
        'ftp://invalid-protocol',
        ''
      ]

      invalidUrls.forEach(url => {
        if (url) {
          expect(() => new URL(url)).toThrow()
        }
      })
    })

    it('should validate timeout values', () => {
      const validTimeouts = [1000, 5000, 30000, 300000]
      const invalidTimeouts = [0, -1000, 400000]

      validTimeouts.forEach(timeout => {
        expect(timeout).toBeGreaterThan(0)
        expect(timeout).toBeLessThanOrEqual(300000)
      })

      invalidTimeouts.forEach(timeout => {
        expect(timeout <= 0 || timeout > 300000).toBe(true)
      })
    })

    it('should validate sample rates', () => {
      const validSampleRates = [0, 10, 50, 100]
      const invalidSampleRates = [-1, 101, 150]

      validSampleRates.forEach(rate => {
        expect(rate).toBeGreaterThanOrEqual(0)
        expect(rate).toBeLessThanOrEqual(100)
      })

      invalidSampleRates.forEach(rate => {
        expect(rate < 0 || rate > 100).toBe(true)
      })
    })
  })

  describe('EnvUtils', () => {
    beforeEach(() => {
      // Mock a basic environment config
      vi.doMock('../environment', () => ({
        env: {
          features: {
            multiAgentReviews: true,
            debugMode: false,
            analytics: true
          },
          api: {
            multiAgent: {
              baseUrl: 'http://localhost:5000',
              timeout: 10000
            }
          },
          deployment: {
            version: '1.0.0',
            buildTimestamp: '2024-01-01T00:00:00Z',
            commitHash: 'abc123'
          },
          monitoring: {
            enableErrorTracking: true,
            sampleRate: 10
          },
          security: {
            enableCSP: true,
            trustedDomains: ['localhost']
          },
          isDevelopment: false
        },
        EnvUtils: {
          isFeatureEnabled: (feature: string) => {
            const features: Record<string, boolean> = {
              'multiAgentReviews': true,
              'debugMode': false,
              'analytics': true
            }
            return features[feature] || false
          },
          getApiConfig: (_service: string) => ({
            baseUrl: 'http://localhost:5000',
            timeout: 10000
          }),
          getServiceUrl: (_service: string, endpoint: string = '') => 
            `http://localhost:5000${endpoint}`,
          supportsFeature: (_feature: string) => true,
          getDeploymentInfo: () => ({
            version: '1.0.0',
            buildTimestamp: '2024-01-01T00:00:00Z',
            commitHash: 'abc123',
            deploymentId: 'test-deployment'
          }),
          getMonitoringConfig: () => ({
            enableErrorTracking: true,
            sampleRate: 10
          }),
          isDevelopmentMode: () => false,
          getSecurityConfig: () => ({
            enableCSP: true,
            trustedDomains: ['localhost']
          }),
          exportConfig: () => ({
            environment: 'test',
            version: '1.0.0',
            features: { multiAgentReviews: true }
          })
        }
      }))
    })

    it('should check feature enablement', () => {
      const mockEnvUtils = {
        isFeatureEnabled: (feature: string) => feature === 'multiAgentReviews'
      }
      
      expect(mockEnvUtils.isFeatureEnabled('multiAgentReviews')).toBe(true)
      expect(mockEnvUtils.isFeatureEnabled('nonExistentFeature')).toBe(false)
    })

    it('should get API configuration', () => {
      const mockEnvUtils = {
        getApiConfig: (_service: string) => ({
          baseUrl: 'http://localhost:5000',
          timeout: 10000
        })
      }
      
      const config = mockEnvUtils.getApiConfig('multiAgent')
      expect(config.baseUrl).toBe('http://localhost:5000')
      expect(config.timeout).toBe(10000)
    })

    it('should build service URLs', () => {
      const mockEnvUtils = {
        getServiceUrl: (_service: string, endpoint: string = '') => 
          `http://localhost:5000${endpoint}`
      }
      
      expect(mockEnvUtils.getServiceUrl('multiAgent', '/health')).toBe('http://localhost:5000/health')
      expect(mockEnvUtils.getServiceUrl('multiAgent')).toBe('http://localhost:5000')
    })

    it('should get deployment info', () => {
      const mockEnvUtils = {
        getDeploymentInfo: () => ({
          version: '1.0.0',
          buildTimestamp: '2024-01-01T00:00:00Z',
          commitHash: 'abc123',
          deploymentId: 'test-deployment'
        })
      }
      
      const info = mockEnvUtils.getDeploymentInfo()
      expect(info.version).toBe('1.0.0')
      expect(info.commitHash).toBe('abc123')
    })

    it('should get monitoring configuration', () => {
      const mockEnvUtils = {
        getMonitoringConfig: () => ({
          enableErrorTracking: true,
          enablePerformanceTracking: false,
          sampleRate: 10
        })
      }
      
      const config = mockEnvUtils.getMonitoringConfig()
      expect(config.enableErrorTracking).toBe(true)
      expect(config.sampleRate).toBe(10)
    })

    it('should check development mode', () => {
      const mockEnvUtils = {
        isDevelopmentMode: () => false
      }
      
      expect(mockEnvUtils.isDevelopmentMode()).toBe(false)
    })

    it('should get security configuration', () => {
      const mockEnvUtils = {
        getSecurityConfig: () => ({
          enableCSP: true,
          enableHSTS: false,
          trustedDomains: ['localhost', 'example.com']
        })
      }
      
      const config = mockEnvUtils.getSecurityConfig()
      expect(config.enableCSP).toBe(true)
      expect(config.trustedDomains).toContain('localhost')
    })

    it('should export config for debugging', () => {
      const mockEnvUtils = {
        exportConfig: () => ({
          environment: 'test',
          version: '1.0.0',
          features: {
            multiAgentReviews: true,
            debugMode: false
          },
          apis: {
            multiAgent: {
              baseUrl: 'http://localhost:5000',
              timeout: 10000
            }
          }
        })
      }
      
      const config = mockEnvUtils.exportConfig()
      expect(config?.environment).toBe('test')
      expect(config?.features.multiAgentReviews).toBe(true)
    })
  })

  describe('Environment-Specific Overrides', () => {
    it('should apply development overrides', () => {
      const devOverrides = {
        features: {
          multiAgentReviews: true,
          debugMode: true,
          analytics: false
        },
        monitoring: {
          enableErrorTracking: false,
          sampleRate: 100
        },
        security: {
          enableCSP: false,
          trustedDomains: ['localhost', '127.0.0.1']
        }
      }

      expect(devOverrides.features.debugMode).toBe(true)
      expect(devOverrides.features.analytics).toBe(false)
      expect(devOverrides.monitoring.enableErrorTracking).toBe(false)
      expect(devOverrides.security.enableCSP).toBe(false)
    })

    it('should apply production overrides', () => {
      const prodOverrides = {
        features: {
          debugMode: false,
          analytics: true,
          serviceWorker: true
        },
        monitoring: {
          enableErrorTracking: true,
          enablePerformanceTracking: true,
          sampleRate: 10
        },
        security: {
          enableCSP: true,
          enableHSTS: true
        }
      }

      expect(prodOverrides.features.debugMode).toBe(false)
      expect(prodOverrides.features.analytics).toBe(true)
      expect(prodOverrides.monitoring.enableErrorTracking).toBe(true)
      expect(prodOverrides.security.enableCSP).toBe(true)
    })

    it('should apply staging overrides', () => {
      const stagingOverrides = {
        features: {
          multiAgentReviews: true,
          analytics: true,
          debugMode: false
        },
        monitoring: {
          enableErrorTracking: true,
          sampleRate: 50
        },
        performance: {
          enableCodeSplitting: true,
          preloadRoutes: true
        }
      }

      expect(stagingOverrides.features.multiAgentReviews).toBe(true)
      expect(stagingOverrides.monitoring.sampleRate).toBe(50)
      expect(stagingOverrides.performance.enableCodeSplitting).toBe(true)
    })

    it('should apply test overrides', () => {
      const testOverrides = {
        features: {
          multiAgentReviews: false,
          analytics: false,
          debugMode: false
        },
        monitoring: {
          enableErrorTracking: false,
          enablePerformanceTracking: false,
          enableUsageTracking: false
        }
      }

      expect(testOverrides.features.multiAgentReviews).toBe(false)
      expect(testOverrides.features.analytics).toBe(false)
      expect(testOverrides.monitoring.enableErrorTracking).toBe(false)
    })
  })

  describe('Configuration Validation', () => {
    it('should pass validation for valid configuration', () => {
      const validConfig = {
        api: {
          multiAgent: {
            baseUrl: 'https://api.example.com',
            timeout: 10000
          },
          legacyCodeReviewer: {
            baseUrl: 'https://legacy.example.com',
            timeout: 15000
          }
        },
        monitoring: {
          sampleRate: 50
        }
      }

      // Validate URLs
      expect(() => new URL(validConfig.api.multiAgent.baseUrl)).not.toThrow()
      expect(() => new URL(validConfig.api.legacyCodeReviewer.baseUrl)).not.toThrow()

      // Validate timeouts
      expect(validConfig.api.multiAgent.timeout).toBeGreaterThan(0)
      expect(validConfig.api.multiAgent.timeout).toBeLessThanOrEqual(300000)

      // Validate sample rate
      expect(validConfig.monitoring.sampleRate).toBeGreaterThanOrEqual(0)
      expect(validConfig.monitoring.sampleRate).toBeLessThanOrEqual(100)
    })

    it('should collect validation errors for invalid configuration', () => {
      const invalidConfig = {
        api: {
          multiAgent: {
            baseUrl: 'invalid-url',
            timeout: -1000
          }
        },
        monitoring: {
          sampleRate: 150
        }
      }

      const errors: string[] = []

      // URL validation
      try {
        new URL(invalidConfig.api.multiAgent.baseUrl)
      } catch {
        errors.push(`Invalid URL for api.multiAgent.baseUrl: ${invalidConfig.api.multiAgent.baseUrl}`)
      }

      // Timeout validation
      if (invalidConfig.api.multiAgent.timeout <= 0 || invalidConfig.api.multiAgent.timeout > 300000) {
        errors.push(`Invalid timeout for api.multiAgent.timeout: ${invalidConfig.api.multiAgent.timeout}ms`)
      }

      // Sample rate validation
      if (invalidConfig.monitoring.sampleRate < 0 || invalidConfig.monitoring.sampleRate > 100) {
        errors.push(`Invalid sample rate: ${invalidConfig.monitoring.sampleRate}`)
      }

      expect(errors).toHaveLength(3)
      expect(errors[0]).toContain('Invalid URL')
      expect(errors[1]).toContain('Invalid timeout')
      expect(errors[2]).toContain('Invalid sample rate')
    })
  })
})