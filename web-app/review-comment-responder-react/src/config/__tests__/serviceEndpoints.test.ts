/**
 * Unit Tests for Service Endpoints Configuration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import {
  ServiceEndpoints,
  HealthCheckConfig,
  ServicePriority,
  buildEndpointUrl,
  getHealthCheckUrl,
  ServiceAvailabilityChecker,
  getCurrentEnvironment,
  getEnvironmentConfig
} from '../serviceEndpoints'

// Mock environment variables
const mockEnv = {
  VITE_MULTI_AGENT_URL: 'http://test:5000',
  VITE_CODE_REVIEWER_URL: 'http://test:5002',
  VITE_CLAUDE_URL: 'http://test:5001',
  NODE_ENV: 'test'
}

describe('ServiceEndpoints', () => {
  beforeEach(() => {
    // Mock import.meta.env
    vi.stubGlobal('import', {
      meta: {
        env: mockEnv
      }
    })
  })

  afterEach(() => {
    vi.unstubAllGlobals()
  })

  describe('ServiceEndpoints configuration', () => {
    it('should provide correct Multi-Agent service configuration', () => {
      expect(ServiceEndpoints.MULTI_AGENT).toEqual({
        BASE_URL: 'http://localhost:5000', // Default value
        API_VERSION: 'v1',
        ENDPOINTS: {
          HEALTH: '/health',
          START_REVIEW: '/api/v1/review/start',
          REVIEW_STATUS: '/api/v1/review/:reviewId/status',
          REVIEW_RESULTS: '/api/v1/review/:reviewId/results',
          CANCEL_REVIEW: '/api/v1/review/:reviewId/cancel',
          AGENTS: '/api/v1/agents',
          ACTIVE_REVIEWS: '/api/v1/reviews'
        },
        WEBSOCKET_URL: 'ws://localhost:5000',
        TIMEOUT: 10000,
        RETRY_CONFIG: {
          MAX_RETRIES: 3,
          RETRY_DELAY: 1000,
          EXPONENTIAL_BACKOFF: true
        }
      })
    })

    it('should provide correct Legacy Code Reviewer configuration', () => {
      expect(ServiceEndpoints.LEGACY_CODE_REVIEWER.BASE_URL).toBe('http://localhost:5002')
      expect(ServiceEndpoints.LEGACY_CODE_REVIEWER.API_VERSION).toBe('code-reviewer')
      expect(ServiceEndpoints.LEGACY_CODE_REVIEWER.ENDPOINTS.HEALTH).toBe('/api/health')
      expect(ServiceEndpoints.LEGACY_CODE_REVIEWER.TIMEOUT).toBe(15000)
    })

    it('should provide correct Legacy Claude configuration', () => {
      expect(ServiceEndpoints.LEGACY_CLAUDE.BASE_URL).toBe('http://localhost:5001')
      expect(ServiceEndpoints.LEGACY_CLAUDE.TIMEOUT).toBe(30000)
      expect(ServiceEndpoints.LEGACY_CLAUDE.ENDPOINTS.HEALTH).toBe('/api/health')
    })

    it('should provide correct Bitbucket configuration', () => {
      expect(ServiceEndpoints.BITBUCKET.BASE_URL).toBe('https://api.bitbucket.org/2.0')
      expect(ServiceEndpoints.BITBUCKET.TIMEOUT).toBe(10000)
    })

    it('should provide correct Jira configuration', () => {
      expect(ServiceEndpoints.JIRA.BASE_URL).toBe('https://rma.atlassian.net')
      expect(ServiceEndpoints.JIRA.API_VERSION).toBe('3')
      expect(ServiceEndpoints.JIRA.TIMEOUT).toBe(10000)
    })
  })

  describe('HealthCheckConfig', () => {
    it('should provide correct health check intervals', () => {
      expect(HealthCheckConfig.INTERVALS.MULTI_AGENT).toBe(30000)
      expect(HealthCheckConfig.INTERVALS.LEGACY_SERVICES).toBe(60000)
      expect(HealthCheckConfig.INTERVALS.EXTERNAL_APIS).toBe(300000)
      expect(HealthCheckConfig.TIMEOUT).toBe(5000)
      expect(HealthCheckConfig.MAX_FAILURES).toBe(3)
    })
  })

  describe('ServicePriority', () => {
    it('should define correct service priority order', () => {
      expect(ServicePriority.CODE_REVIEW).toEqual([
        'MULTI_AGENT',
        'LEGACY_CODE_REVIEWER'
      ])
      expect(ServicePriority.COMMENT_PROCESSING).toEqual([
        'LEGACY_CLAUDE'
      ])
    })
  })

  describe('getCurrentEnvironment', () => {
    it('should return development by default', () => {
      expect(getCurrentEnvironment()).toBe('development')
    })

    it('should return production when NODE_ENV is production', () => {
      vi.stubGlobal('import', {
        meta: {
          env: { ...mockEnv, MODE: 'production' }
        }
      })
      
      expect(getCurrentEnvironment()).toBe('production')
    })
  })

  describe('getEnvironmentConfig', () => {
    it('should return development config by default', () => {
      const config = getEnvironmentConfig()
      expect(config.enableMockServices).toBe(false)
      expect(config.logRequests).toBe(true)
      expect(config.enableServiceDebugging).toBe(true)
    })
  })

  describe('buildEndpointUrl', () => {
    it('should build URL without parameters', () => {
      const url = buildEndpointUrl('http://localhost:5000', '/api/v1/health')
      expect(url).toBe('http://localhost:5000/api/v1/health')
    })

    it('should build URL with parameters', () => {
      const url = buildEndpointUrl(
        'http://localhost:5000', 
        '/api/v1/review/:reviewId/status',
        { reviewId: 'review-123' }
      )
      expect(url).toBe('http://localhost:5000/api/v1/review/review-123/status')
    })

    it('should URL encode parameters', () => {
      const url = buildEndpointUrl(
        'http://localhost:5000',
        '/api/v1/review/:reviewId/status',
        { reviewId: 'review with spaces' }
      )
      expect(url).toBe('http://localhost:5000/api/v1/review/review%20with%20spaces/status')
    })

    it('should handle multiple parameters', () => {
      const url = buildEndpointUrl(
        'http://localhost:5000',
        '/api/v1/workspace/:workspaceId/repo/:repoId',
        { workspaceId: 'my-workspace', repoId: 'my-repo' }
      )
      expect(url).toBe('http://localhost:5000/api/v1/workspace/my-workspace/repo/my-repo')
    })
  })

  describe('getHealthCheckUrl', () => {
    it('should return correct health check URL for Multi-Agent service', () => {
      const url = getHealthCheckUrl('MULTI_AGENT')
      expect(url).toBe('http://localhost:5000/health')
    })

    it('should return correct health check URL for Legacy Code Reviewer', () => {
      const url = getHealthCheckUrl('LEGACY_CODE_REVIEWER')
      expect(url).toBe('http://localhost:5002/api/health')
    })

    it('should return correct health check URL for Legacy Claude', () => {
      const url = getHealthCheckUrl('LEGACY_CLAUDE')
      expect(url).toBe('http://localhost:5001/api/health')
    })

    it('should throw error for invalid service name', () => {
      expect(() => getHealthCheckUrl('INVALID_SERVICE' as any))
        .toThrow('Health check endpoint not defined for service: INVALID_SERVICE')
    })
  })
})

describe('ServiceAvailabilityChecker', () => {
  let checker: ServiceAvailabilityChecker
  let mockFetch: ReturnType<typeof vi.fn>

  beforeEach(() => {
    checker = new ServiceAvailabilityChecker()
    mockFetch = vi.fn()
    global.fetch = mockFetch
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('checkServiceHealth', () => {
    it('should return true for healthy service', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK'
      })

      const isHealthy = await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(isHealthy).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:5000/health',
        {
          method: 'GET',
          signal: expect.any(AbortSignal)
        }
      )
    })

    it('should return false for unhealthy service', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable'
      })

      const isHealthy = await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(isHealthy).toBe(false)
    })

    it('should return false on network error', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const isHealthy = await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(isHealthy).toBe(false)
    })

    it('should return false on timeout', async () => {
      // Mock AbortSignal.timeout to throw immediately
      const mockAbortSignal = {
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        aborted: false,
        reason: undefined,
        throwIfAborted: vi.fn()
      }
      
      vi.stubGlobal('AbortSignal', {
        timeout: vi.fn().mockReturnValue(mockAbortSignal)
      })

      mockFetch.mockRejectedValue(new DOMException('AbortError', 'AbortError'))

      const isHealthy = await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(isHealthy).toBe(false)
    })
  })

  describe('health status tracking', () => {
    it('should track health status for successful checks', async () => {
      mockFetch.mockResolvedValue({ ok: true })

      await checker.checkServiceHealth('MULTI_AGENT')
      
      const health = checker.getServiceHealth('MULTI_AGENT')
      expect(health).toBeTruthy()
      expect(health?.isHealthy).toBe(true)
      expect(health?.failures).toBe(0)
      expect(health?.lastCheck).toBeCloseTo(Date.now(), -2) // Within 100ms
    })

    it('should track health status for failed checks', async () => {
      mockFetch.mockResolvedValue({ ok: false })

      await checker.checkServiceHealth('MULTI_AGENT')
      
      const health = checker.getServiceHealth('MULTI_AGENT')
      expect(health).toBeTruthy()
      expect(health?.isHealthy).toBe(false)
      expect(health?.failures).toBe(1)
    })

    it('should increment failure count on consecutive failures', async () => {
      mockFetch.mockResolvedValue({ ok: false })

      // First failure
      await checker.checkServiceHealth('MULTI_AGENT')
      let health = checker.getServiceHealth('MULTI_AGENT')
      expect(health?.failures).toBe(1)

      // Second failure
      await checker.checkServiceHealth('MULTI_AGENT')
      health = checker.getServiceHealth('MULTI_AGENT')
      expect(health?.failures).toBe(2)
    })

    it('should reset failure count on successful check after failures', async () => {
      // First failure
      mockFetch.mockResolvedValueOnce({ ok: false })
      await checker.checkServiceHealth('MULTI_AGENT')
      let health = checker.getServiceHealth('MULTI_AGENT')
      expect(health?.failures).toBe(1)

      // Success - should reset failures
      mockFetch.mockResolvedValueOnce({ ok: true })
      await checker.checkServiceHealth('MULTI_AGENT')
      health = checker.getServiceHealth('MULTI_AGENT')
      expect(health?.failures).toBe(0)
      expect(health?.isHealthy).toBe(true)
    })

    it('should return null for service that has never been checked', () => {
      const health = checker.getServiceHealth('NEVER_CHECKED')
      expect(health).toBeNull()
    })
  })

  describe('isServiceAvailable', () => {
    it('should return true for healthy service with no failures', async () => {
      mockFetch.mockResolvedValue({ ok: true })
      await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(checker.isServiceAvailable('MULTI_AGENT')).toBe(true)
    })

    it('should return false for unhealthy service', async () => {
      mockFetch.mockResolvedValue({ ok: false })
      await checker.checkServiceHealth('MULTI_AGENT')
      
      expect(checker.isServiceAvailable('MULTI_AGENT')).toBe(false)
    })

    it('should return false for service with too many failures', async () => {
      mockFetch.mockResolvedValue({ ok: false })
      
      // Exceed max failures (3)
      for (let i = 0; i < 4; i++) {
        await checker.checkServiceHealth('MULTI_AGENT')
      }
      
      expect(checker.isServiceAvailable('MULTI_AGENT')).toBe(false)
    })

    it('should return true for service that has never been checked', () => {
      // Default to available if never checked
      expect(checker.isServiceAvailable('NEVER_CHECKED')).toBe(true)
    })
  })
})