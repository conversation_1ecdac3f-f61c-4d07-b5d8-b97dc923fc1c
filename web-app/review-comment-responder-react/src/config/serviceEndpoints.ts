/**
 * Service Endpoints Configuration
 * Central configuration for all service URLs and endpoints
 */

/**
 * Environment variables with fallback defaults
 */
const getEnvVar = (key: string, defaultValue: string): string => {
  return import.meta.env[key] || defaultValue
}

const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  return value === 'true' || value === '1'
}

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  const parsed = parseInt(value, 10)
  return isNaN(parsed) ? defaultValue : parsed
}

/**
 * Service endpoint configurations
 */
export const ServiceEndpoints = {
  /**
   * Multi-Agent Code Reviewer Service (NEW - Port 8000)
   * High-performance parallel review service
   */
  MULTI_AGENT: {
    BASE_URL: getEnvVar('VITE_MULTI_AGENT_URL', 'http://localhost:8000'),
    API_VERSION: 'v1',
    ENDPOINTS: {
      HEALTH: '/health',
      START_REVIEW: '/api/v1/review/start',
      REVIEW_STATUS: '/api/v1/review/:reviewId/status',
      REVIEW_RESULTS: '/api/v1/review/:reviewId/results',
      CANCEL_REVIEW: '/api/v1/review/:reviewId/cancel',
      AGENTS: '/api/v1/agents',
      ACTIVE_REVIEWS: '/api/v1/reviews'
    },
    WEBSOCKET_URL: getEnvVar('VITE_MULTI_AGENT_WS_URL', 'ws://localhost:8000/api/v1/websocket/ws'),
    TIMEOUT: getEnvNumber('VITE_MULTI_AGENT_TIMEOUT', 10000),
    RETRY_CONFIG: {
      MAX_RETRIES: getEnvNumber('VITE_MULTI_AGENT_MAX_RETRIES', 3),
      RETRY_DELAY: getEnvNumber('VITE_MULTI_AGENT_RETRY_DELAY', 1000),
      EXPONENTIAL_BACKOFF: getEnvBoolean('VITE_MULTI_AGENT_EXPONENTIAL_BACKOFF', true)
    }
  },

  /**
   * Legacy Code Reviewer Service (Port 5002)
   * Sequential review service for fallback
   */
  LEGACY_CODE_REVIEWER: {
    BASE_URL: getEnvVar('VITE_CODE_REVIEWER_URL', 'http://localhost:5002'),
    API_VERSION: 'code-reviewer',
    ENDPOINTS: {
      HEALTH: '/api/health',
      START_REVIEW: '/api/code-reviewer/start-structured-review',
      REVIEW_STATUS: '/api/code-reviewer/review-status/:sessionId',
      REVIEW_RESULTS: '/api/code-reviewer/review-results/:sessionId',
      GENERATE_TUTORIAL: '/api/code-reviewer/generate-tutorial/:sessionId',
      ASSIGNED_PRS: '/api/code-reviewer/assigned-prs',
      ASSIGNED_TICKETS: '/api/code-reviewer/assigned-tickets',
      PROGRESS_STREAM: '/api/code-reviewer/progress-stream/:sessionId'
    },
    TIMEOUT: getEnvNumber('VITE_CODE_REVIEWER_TIMEOUT', 15000)
  },

  /**
   * Legacy Claude Service (Port 5001)
   * Original Claude integration service
   */
  LEGACY_CLAUDE: {
    BASE_URL: getEnvVar('VITE_CLAUDE_URL', 'http://localhost:5001'),
    ENDPOINTS: {
      HEALTH: '/api/health',
      PROCESS_COMMENT: '/api/process-comment',
      PREVIEW_CHANGES: '/api/preview-changes',
      APPLY_CHANGES: '/api/apply-changes'
    },
    TIMEOUT: getEnvNumber('VITE_CLAUDE_TIMEOUT', 30000)
  },

  /**
   * Bitbucket API Integration
   */
  BITBUCKET: {
    BASE_URL: getEnvVar('VITE_BITBUCKET_API_URL', 'https://api.bitbucket.org/2.0'),
    ENDPOINTS: {
      REPOSITORIES: '/repositories/:workspace',
      PULL_REQUESTS: '/repositories/:workspace/:repo_slug/pullrequests',
      PULL_REQUEST: '/repositories/:workspace/:repo_slug/pullrequests/:pull_request_id',
      COMMITS: '/repositories/:workspace/:repo_slug/commits',
      DIFF: '/repositories/:workspace/:repo_slug/diff/:spec'
    },
    TIMEOUT: getEnvNumber('VITE_BITBUCKET_TIMEOUT', 10000)
  },

  /**
   * Jira API Integration
   */
  JIRA: {
    BASE_URL: getEnvVar('VITE_JIRA_URL', 'https://rma.atlassian.net'),
    API_VERSION: '3',
    ENDPOINTS: {
      SEARCH: '/rest/api/3/search',
      ISSUE: '/rest/api/3/issue/:issueIdOrKey',
      FIELDS: '/rest/api/3/field',
      PROJECT: '/rest/api/3/project/:projectIdOrKey'
    },
    TIMEOUT: getEnvNumber('VITE_JIRA_TIMEOUT', 10000)
  }
} as const

/**
 * Service health check configuration
 */
export const HealthCheckConfig = {
  INTERVALS: {
    MULTI_AGENT: getEnvNumber('VITE_HEALTH_CHECK_MULTI_AGENT_INTERVAL', 30000), // 30 seconds
    LEGACY_SERVICES: getEnvNumber('VITE_HEALTH_CHECK_LEGACY_INTERVAL', 60000), // 1 minute
    EXTERNAL_APIS: getEnvNumber('VITE_HEALTH_CHECK_EXTERNAL_INTERVAL', 300000) // 5 minutes
  },
  TIMEOUT: getEnvNumber('VITE_HEALTH_CHECK_TIMEOUT', 5000),
  MAX_FAILURES: getEnvNumber('VITE_HEALTH_CHECK_MAX_FAILURES', 3)
}

/**
 * Service priority configuration for fallback scenarios
 */
export const ServicePriority = {
  CODE_REVIEW: [
    'MULTI_AGENT',        // Preferred: New parallel service
    'LEGACY_CODE_REVIEWER' // Fallback: Sequential service
  ],
  COMMENT_PROCESSING: [
    'LEGACY_CLAUDE'       // Only option currently
  ],
  WEBSOCKET_CONNECTIONS: [
    'MULTI_AGENT'         // Only Multi-Agent WebSocket - avoid Legacy loops
  ]
} as const

/**
 * WebSocket service filtering - prevents problematic Legacy WebSocket loops
 */
export const WebSocketServiceFilter = {
  isAllowedForWebSocket: (serviceName: keyof typeof ServiceEndpoints): boolean => {
    // Only allow Multi-Agent WebSocket connections to prevent loops
    return serviceName === 'MULTI_AGENT'
  },
  
  getWebSocketUrl: (serviceName: keyof typeof ServiceEndpoints): string | null => {
    if (!WebSocketServiceFilter.isAllowedForWebSocket(serviceName)) {
      console.warn(`WebSocket disabled for ${serviceName} to prevent connection loops`)
      return null
    }
    
    const service = ServiceEndpoints[serviceName]
    return 'WEBSOCKET_URL' in service ? (service as any).WEBSOCKET_URL : null
  }
}

/**
 * Development vs Production endpoint overrides
 */
export const EnvironmentConfig = {
  development: {
    // Use local services in development
    enableMockServices: getEnvBoolean('VITE_ENABLE_MOCK_SERVICES', false),
    logRequests: getEnvBoolean('VITE_LOG_SERVICE_REQUESTS', true),
    enableServiceDebugging: getEnvBoolean('VITE_ENABLE_SERVICE_DEBUGGING', true)
  },
  production: {
    // Production configurations
    enableMockServices: false,
    logRequests: getEnvBoolean('VITE_LOG_SERVICE_REQUESTS', false),
    enableServiceDebugging: false,
    // Override URLs for production if needed
    MULTI_AGENT_BASE_URL: getEnvVar('VITE_PROD_MULTI_AGENT_URL', ServiceEndpoints.MULTI_AGENT.BASE_URL),
    CODE_REVIEWER_BASE_URL: getEnvVar('VITE_PROD_CODE_REVIEWER_URL', ServiceEndpoints.LEGACY_CODE_REVIEWER.BASE_URL)
  }
}

/**
 * Get current environment
 */
export const getCurrentEnvironment = (): 'development' | 'production' => {
  return (import.meta.env.MODE || 'development') === 'production' 
    ? 'production' 
    : 'development'
}

/**
 * Get environment-specific configuration
 */
export const getEnvironmentConfig = () => {
  return EnvironmentConfig[getCurrentEnvironment()]
}

/**
 * Utility function to build endpoint URLs with parameter substitution
 */
export const buildEndpointUrl = (
  baseUrl: string, 
  endpoint: string, 
  params: Record<string, string> = {}
): string => {
  let url = `${baseUrl}${endpoint}`
  
  // Replace URL parameters (e.g., :reviewId with actual value)
  Object.entries(params).forEach(([key, value]) => {
    url = url.replace(`:${key}`, encodeURIComponent(value))
  })
  
  return url
}

/**
 * Utility function to get service health check URL
 */
export const getHealthCheckUrl = (serviceName: keyof typeof ServiceEndpoints): string => {
  const service = ServiceEndpoints[serviceName]
  if (!service || !service.ENDPOINTS || !('HEALTH' in service.ENDPOINTS)) {
    throw new Error(`Health check endpoint not defined for service: ${serviceName}`)
  }
  
  return `${service.BASE_URL}${(service.ENDPOINTS as any).HEALTH}`
}

/**
 * Service availability checker
 */
export class ServiceAvailabilityChecker {
  private healthStatus: Map<string, { isHealthy: boolean; lastCheck: number; failures: number }> = new Map()

  async checkServiceHealth(serviceName: keyof typeof ServiceEndpoints): Promise<boolean> {
    try {
      const healthUrl = getHealthCheckUrl(serviceName)
      const response = await fetch(healthUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(HealthCheckConfig.TIMEOUT)
      })
      
      const isHealthy = response.ok
      this.updateHealthStatus(serviceName, isHealthy)
      
      return isHealthy
    } catch (error) {
      console.warn(`Health check failed for ${serviceName}:`, error)
      this.updateHealthStatus(serviceName, false)
      return false
    }
  }

  private updateHealthStatus(serviceName: string, isHealthy: boolean): void {
    const current = this.healthStatus.get(serviceName) || { isHealthy: true, lastCheck: 0, failures: 0 }
    
    this.healthStatus.set(serviceName, {
      isHealthy,
      lastCheck: Date.now(),
      failures: isHealthy ? 0 : current.failures + 1
    })
  }

  getServiceHealth(serviceName: string): { isHealthy: boolean; lastCheck: number; failures: number } | null {
    return this.healthStatus.get(serviceName) || null
  }

  isServiceAvailable(serviceName: string): boolean {
    const health = this.getServiceHealth(serviceName)
    return health ? health.isHealthy && health.failures < HealthCheckConfig.MAX_FAILURES : true
  }
}

// Export singleton instance
export const serviceAvailabilityChecker = new ServiceAvailabilityChecker()