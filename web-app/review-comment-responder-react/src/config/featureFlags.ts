/**
 * Feature Flags Configuration
 * Central management of feature toggles for safe deployment and A/B testing
 */

/**
 * Environment variable helpers
 */
const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  return value === 'true' || value === '1'
}

const getEnvString = (key: string, defaultValue: string): string => {
  return import.meta.env[key] || defaultValue
}

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  const parsed = parseFloat(value)
  return isNaN(parsed) ? defaultValue : parsed
}

/**
 * Feature flag definitions
 */
export const FeatureFlags = {
  /**
   * Multi-Agent Review System
   * Controls access to the new parallel review service
   */
  MULTI_AGENT_REVIEWS: {
    enabled: getEnvBoolean('VITE_FEATURE_MULTI_AGENT_REVIEWS', true),
    rolloutPercentage: getEnvNumber('VITE_MULTI_AGENT_ROLLOUT_PERCENTAGE', 0), // 0-100
    allowedUserIds: getEnvString('VITE_MULTI_AGENT_ALLOWED_USERS', '').split(',').filter(Boolean),
    allowedWorkspaces: getEnvString('VITE_MULTI_AGENT_ALLOWED_WORKSPACES', '').split(',').filter(Boolean),
    betaMode: getEnvBoolean('VITE_MULTI_AGENT_BETA_MODE', true) // Show beta badges/warnings
  },

  /**
   * Service Fallback Behavior
   * Controls automatic fallback from Multi-Agent to Legacy services
   */
  AUTOMATIC_SERVICE_FALLBACK: {
    enabled: getEnvBoolean('VITE_FEATURE_AUTO_FALLBACK', true),
    fallbackOnTimeout: getEnvBoolean('VITE_FALLBACK_ON_TIMEOUT', true),
    fallbackOnError: getEnvBoolean('VITE_FALLBACK_ON_ERROR', true),
    maxFallbackAttempts: getEnvNumber('VITE_MAX_FALLBACK_ATTEMPTS', 2),
    showFallbackNotifications: getEnvBoolean('VITE_SHOW_FALLBACK_NOTIFICATIONS', true)
  },

  /**
   * WebSocket Real-Time Updates
   * Controls real-time progress updates via WebSocket
   */
  REALTIME_UPDATES: {
    enabled: getEnvBoolean('VITE_FEATURE_REALTIME_UPDATES', true), // RE-ENABLED for Multi-Agent
    autoReconnect: getEnvBoolean('VITE_WEBSOCKET_AUTO_RECONNECT', true), 
    maxReconnectAttempts: getEnvNumber('VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS', 3), // Reduced
    reconnectDelay: getEnvNumber('VITE_WEBSOCKET_RECONNECT_DELAY', 2000), // Slower reconnect
    heartbeatInterval: getEnvNumber('VITE_WEBSOCKET_HEARTBEAT_INTERVAL', 30000)
  },

  /**
   * Enhanced Review Reports
   * Controls new report features and formats
   */
  ENHANCED_REPORTS: {
    enabled: getEnvBoolean('VITE_FEATURE_ENHANCED_REPORTS', true),
    showAgentBreakdown: getEnvBoolean('VITE_SHOW_AGENT_BREAKDOWN', true),
    enableMermaidDiagrams: getEnvBoolean('VITE_ENABLE_MERMAID_DIAGRAMS', true),
    enablePdfExport: getEnvBoolean('VITE_ENABLE_PDF_EXPORT', false),
    enableJsonExport: getEnvBoolean('VITE_ENABLE_JSON_EXPORT', true),
    showPerformanceMetrics: getEnvBoolean('VITE_SHOW_PERFORMANCE_METRICS', true)
  },

  /**
   * User Interface Enhancements
   * Controls new UI features and improvements
   */
  UI_ENHANCEMENTS: {
    modernProgressTracker: getEnvBoolean('VITE_FEATURE_MODERN_PROGRESS_TRACKER', true),
    darkModeSupport: getEnvBoolean('VITE_FEATURE_DARK_MODE', false),
    accessibilityEnhancements: getEnvBoolean('VITE_FEATURE_A11Y_ENHANCEMENTS', true),
    keyboardShortcuts: getEnvBoolean('VITE_FEATURE_KEYBOARD_SHORTCUTS', false),
    compactMode: getEnvBoolean('VITE_FEATURE_COMPACT_MODE', false)
  },

  /**
   * Performance Optimizations
   * Controls performance-related features
   */
  PERFORMANCE: {
    lazyLoadComponents: getEnvBoolean('VITE_FEATURE_LAZY_LOADING', true),
    enableServiceWorker: getEnvBoolean('VITE_FEATURE_SERVICE_WORKER', false),
    cacheReviewResults: getEnvBoolean('VITE_FEATURE_CACHE_RESULTS', true),
    preloadCriticalRoutes: getEnvBoolean('VITE_FEATURE_PRELOAD_ROUTES', true),
    optimizeImageLoading: getEnvBoolean('VITE_FEATURE_OPTIMIZE_IMAGES', true)
  },

  /**
   * Development and Debugging
   * Controls debugging and development features
   */
  DEVELOPMENT: {
    enableDebugMode: getEnvBoolean('VITE_FEATURE_DEBUG_MODE', false),
    showApiLogs: getEnvBoolean('VITE_FEATURE_API_LOGS', false),
    enableMockServices: getEnvBoolean('VITE_FEATURE_MOCK_SERVICES', false),
    showPerformancePanel: getEnvBoolean('VITE_FEATURE_PERFORMANCE_PANEL', false),
    enableErrorBoundaryInfo: getEnvBoolean('VITE_FEATURE_ERROR_BOUNDARY_INFO', true)
  },

  /**
   * External Integrations
   * Controls third-party service integrations
   */
  INTEGRATIONS: {
    jiraIntegration: getEnvBoolean('VITE_FEATURE_JIRA_INTEGRATION', true),
    bitbucketIntegration: getEnvBoolean('VITE_FEATURE_BITBUCKET_INTEGRATION', true),
    slackNotifications: getEnvBoolean('VITE_FEATURE_SLACK_NOTIFICATIONS', false),
    emailNotifications: getEnvBoolean('VITE_FEATURE_EMAIL_NOTIFICATIONS', false),
    webhookSupport: getEnvBoolean('VITE_FEATURE_WEBHOOK_SUPPORT', false)
  },

  /**
   * Analytics and Monitoring
   * Controls telemetry and usage analytics
   */
  ANALYTICS: {
    enableUsageTracking: getEnvBoolean('VITE_FEATURE_USAGE_TRACKING', false),
    enableErrorTracking: getEnvBoolean('VITE_FEATURE_ERROR_TRACKING', true),
    enablePerformanceTracking: getEnvBoolean('VITE_FEATURE_PERFORMANCE_TRACKING', false),
    anonymizeUserData: getEnvBoolean('VITE_ANONYMIZE_USER_DATA', true)
  }
} as const

/**
 * Feature flag evaluation context
 */
export interface FeatureFlagContext {
  userId?: string
  workspaceId?: string
  userAgent?: string
  timestamp?: number
  sessionId?: string
  experimentGroup?: string
}

/**
 * Feature flag evaluator class
 */
export class FeatureFlagEvaluator {
  private context: FeatureFlagContext = {}

  /**
   * Set the evaluation context
   */
  setContext(context: Partial<FeatureFlagContext>): void {
    this.context = { ...this.context, ...context }
  }

  /**
   * Check if Multi-Agent reviews are enabled for current user
   */
  isMultiAgentEnabled(): boolean {
    const flag = FeatureFlags.MULTI_AGENT_REVIEWS
    
    // Always disabled if feature flag is off
    if (!flag.enabled) return false

    // Always enabled for allowed users
    if (this.context.userId && flag.allowedUserIds.includes(this.context.userId)) {
      return true
    }

    // Always enabled for allowed workspaces
    if (this.context.workspaceId && flag.allowedWorkspaces.includes(this.context.workspaceId)) {
      return true
    }

    // Check rollout percentage
    if (flag.rolloutPercentage > 0) {
      const userId = this.context.userId || 'anonymous'
      const hash = this.hashString(userId)
      const userPercentile = (hash % 100) + 1
      return userPercentile <= flag.rolloutPercentage
    }

    return false
  }

  /**
   * Check if real-time updates are enabled
   */
  isRealtimeUpdatesEnabled(): boolean {
    return FeatureFlags.REALTIME_UPDATES.enabled
  }

  /**
   * Check if automatic service fallback is enabled
   */
  isAutoFallbackEnabled(): boolean {
    return FeatureFlags.AUTOMATIC_SERVICE_FALLBACK.enabled
  }

  /**
   * Check if enhanced reports are enabled
   */
  isEnhancedReportsEnabled(): boolean {
    return FeatureFlags.ENHANCED_REPORTS.enabled
  }

  /**
   * Get WebSocket configuration based on feature flags
   */
  getWebSocketConfig(): {
    enabled: boolean
    autoReconnect: boolean
    maxReconnectAttempts: number
    reconnectDelay: number
    heartbeatInterval: number
  } {
    const flags = FeatureFlags.REALTIME_UPDATES
    return {
      enabled: flags.enabled,
      autoReconnect: flags.autoReconnect,
      maxReconnectAttempts: flags.maxReconnectAttempts,
      reconnectDelay: flags.reconnectDelay,
      heartbeatInterval: flags.heartbeatInterval
    }
  }

  /**
   * Get fallback configuration
   */
  getFallbackConfig(): {
    enabled: boolean
    fallbackOnTimeout: boolean
    fallbackOnError: boolean
    maxAttempts: number
    showNotifications: boolean
  } {
    const flags = FeatureFlags.AUTOMATIC_SERVICE_FALLBACK
    return {
      enabled: flags.enabled,
      fallbackOnTimeout: flags.fallbackOnTimeout,
      fallbackOnError: flags.fallbackOnError,
      maxAttempts: flags.maxFallbackAttempts,
      showNotifications: flags.showFallbackNotifications
    }
  }

  /**
   * Simple hash function for consistent user bucketing
   */
  private hashString(str: string): number {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash)
  }

  /**
   * Get all enabled features for debugging
   */
  getEnabledFeatures(): Record<string, boolean> {
    return {
      multiAgentReviews: this.isMultiAgentEnabled(),
      realtimeUpdates: this.isRealtimeUpdatesEnabled(),
      autoFallback: this.isAutoFallbackEnabled(),
      enhancedReports: this.isEnhancedReportsEnabled(),
      modernProgressTracker: FeatureFlags.UI_ENHANCEMENTS.modernProgressTracker,
      lazyLoading: FeatureFlags.PERFORMANCE.lazyLoadComponents,
      jiraIntegration: FeatureFlags.INTEGRATIONS.jiraIntegration,
      debugMode: FeatureFlags.DEVELOPMENT.enableDebugMode
    }
  }
}

/**
 * Global feature flag evaluator instance
 */
export const featureFlags = new FeatureFlagEvaluator()

/**
 * React hook for feature flags (if needed)
 */
export const useFeatureFlag = (flagPath: string): boolean => {
  // This would be implemented as a proper React hook
  // For now, just return the static value
  const pathParts = flagPath.split('.')
  let current: any = FeatureFlags
  
  for (const part of pathParts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part]
    } else {
      return false
    }
  }
  
  return typeof current === 'boolean' ? current : false
}

/**
 * Environment-specific feature flag overrides
 */
export const getEnvironmentFeatureFlags = (): Partial<typeof FeatureFlags> => {
  const env = import.meta.env.MODE || 'development'
  
  switch (env) {
    case 'development':
      return {
        DEVELOPMENT: {
          ...FeatureFlags.DEVELOPMENT,
          enableDebugMode: true,
          showApiLogs: true,
          enableErrorBoundaryInfo: true
        },
        MULTI_AGENT_REVIEWS: {
          ...FeatureFlags.MULTI_AGENT_REVIEWS,
          enabled: true, // Always enabled in development
          betaMode: true
        }
      }
    
    case 'staging':
      return {
        MULTI_AGENT_REVIEWS: {
          ...FeatureFlags.MULTI_AGENT_REVIEWS,
          enabled: true,
          rolloutPercentage: 100, // Full rollout in staging
          betaMode: true
        },
        ANALYTICS: {
          ...FeatureFlags.ANALYTICS,
          enableErrorTracking: true,
          enablePerformanceTracking: true
        }
      }
    
    case 'production':
      return {
        DEVELOPMENT: {
          ...FeatureFlags.DEVELOPMENT,
          enableDebugMode: false,
          showApiLogs: false,
          enableMockServices: false,
          showPerformancePanel: false
        },
        ANALYTICS: {
          ...FeatureFlags.ANALYTICS,
          enableErrorTracking: true,
          enableUsageTracking: getEnvBoolean('VITE_PROD_USAGE_TRACKING', false)
        }
      }
    
    default:
      return {}
  }
}

/**
 * Initialize feature flags with environment context
 */
export const initializeFeatureFlags = (context: Partial<FeatureFlagContext> = {}): void => {
  featureFlags.setContext({
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    ...context
  })

  // Log enabled features in development
  if (FeatureFlags.DEVELOPMENT.enableDebugMode) {
    console.log('🚩 Feature Flags Initialized:', featureFlags.getEnabledFeatures())
  }
}

/**
 * Export utility functions
 */
export const FeatureFlagUtils = {
  isMultiAgentEnabled: () => featureFlags.isMultiAgentEnabled(),
  isRealtimeEnabled: () => featureFlags.isRealtimeUpdatesEnabled(),
  isFallbackEnabled: () => featureFlags.isAutoFallbackEnabled(),
  getWebSocketConfig: () => featureFlags.getWebSocketConfig(),
  getFallbackConfig: () => featureFlags.getFallbackConfig()
}