/**
 * Environment Configuration
 * Production-ready environment management with validation and type safety
 */

/**
 * Environment types
 */
export type Environment = 'development' | 'staging' | 'production' | 'test'

/**
 * Environment configuration interface
 */
export interface EnvironmentConfig {
  NODE_ENV: Environment
  isProduction: boolean
  isDevelopment: boolean
  isStaging: boolean
  isTest: boolean
  
  // API Configuration
  api: {
    multiAgent: {
      baseUrl: string
      wsUrl: string
      timeout: number
      retries: number
    }
    legacyCodeReviewer: {
      baseUrl: string
      timeout: number
    }
    legacyClaude: {
      baseUrl: string
      timeout: number
    }
    bitbucket: {
      baseUrl: string
      timeout: number
    }
    jira: {
      baseUrl: string
      timeout: number
    }
  }
  
  // Feature Flags
  features: {
    multiAgentReviews: boolean
    realtimeUpdates: boolean
    enhancedReports: boolean
    serviceWorker: boolean
    analytics: boolean
    debugMode: boolean
  }
  
  // Performance Configuration
  performance: {
    enableCodeSplitting: boolean
    enableLazyLoading: boolean
    enableCaching: boolean
    bundleAnalyzer: boolean
    preloadRoutes: boolean
  }
  
  // Monitoring Configuration
  monitoring: {
    enableErrorTracking: boolean
    enablePerformanceTracking: boolean
    enableUsageTracking: boolean
    anonymizeData: boolean
    sampleRate: number
  }
  
  // Security Configuration
  security: {
    enableCSP: boolean
    enableHSTS: boolean
    enableXSSProtection: boolean
    trustedDomains: string[]
  }
  
  // Deployment Configuration
  deployment: {
    version: string
    buildTimestamp: string
    commitHash: string
    deploymentId: string
  }
}

/**
 * Environment variable helpers with validation
 */
const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = import.meta.env[key]
  if (value === undefined && defaultValue === undefined) {
    throw new Error(`Missing required environment variable: ${key}`)
  }
  return value || defaultValue!
}

const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  return value === 'true' || value === '1'
}

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key]
  if (value === undefined) return defaultValue
  const parsed = Number(value)
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number, got: ${value}`)
  }
  return parsed
}

/**
 * Get current environment
 */
export const getCurrentEnvironment = (): Environment => {
  const env = import.meta.env.MODE || 'development'
  
  if (!['development', 'staging', 'production', 'test'].includes(env)) {
    console.warn(`Unknown environment: ${env}, defaulting to development`)
    return 'development'
  }
  
  return env as Environment
}

/**
 * Build environment configuration
 */
const buildEnvironmentConfig = (): EnvironmentConfig => {
  const currentEnv = getCurrentEnvironment()
  
  const config: EnvironmentConfig = {
    NODE_ENV: currentEnv,
    isProduction: currentEnv === 'production',
    isDevelopment: currentEnv === 'development',
    isStaging: currentEnv === 'staging',
    isTest: currentEnv === 'test',
    
    api: {
      multiAgent: {
        baseUrl: getEnvVar('VITE_MULTI_AGENT_URL', 'http://localhost:5000'),
        wsUrl: getEnvVar('VITE_MULTI_AGENT_WS_URL', 'ws://localhost:5000'),
        timeout: getEnvNumber('VITE_MULTI_AGENT_TIMEOUT', 10000),
        retries: getEnvNumber('VITE_MULTI_AGENT_MAX_RETRIES', 3)
      },
      legacyCodeReviewer: {
        baseUrl: getEnvVar('VITE_CODE_REVIEWER_URL', 'http://localhost:5002'),
        timeout: getEnvNumber('VITE_CODE_REVIEWER_TIMEOUT', 15000)
      },
      legacyClaude: {
        baseUrl: getEnvVar('VITE_CLAUDE_URL', 'http://localhost:5001'),
        timeout: getEnvNumber('VITE_CLAUDE_TIMEOUT', 30000)
      },
      bitbucket: {
        baseUrl: getEnvVar('VITE_BITBUCKET_API_URL', 'https://api.bitbucket.org/2.0'),
        timeout: getEnvNumber('VITE_BITBUCKET_TIMEOUT', 10000)
      },
      jira: {
        baseUrl: getEnvVar('VITE_JIRA_URL', 'https://rma.atlassian.net'),
        timeout: getEnvNumber('VITE_JIRA_TIMEOUT', 10000)
      }
    },
    
    features: {
      multiAgentReviews: getEnvBoolean('VITE_FEATURE_MULTI_AGENT_REVIEWS', currentEnv !== 'production'),
      realtimeUpdates: getEnvBoolean('VITE_FEATURE_REALTIME_UPDATES', true),
      enhancedReports: getEnvBoolean('VITE_FEATURE_ENHANCED_REPORTS', true),
      serviceWorker: getEnvBoolean('VITE_FEATURE_SERVICE_WORKER', currentEnv === 'production'),
      analytics: getEnvBoolean('VITE_FEATURE_ANALYTICS', currentEnv === 'production'),
      debugMode: getEnvBoolean('VITE_FEATURE_DEBUG_MODE', currentEnv === 'development')
    },
    
    performance: {
      enableCodeSplitting: getEnvBoolean('VITE_ENABLE_CODE_SPLITTING', currentEnv === 'production'),
      enableLazyLoading: getEnvBoolean('VITE_FEATURE_LAZY_LOADING', true),
      enableCaching: getEnvBoolean('VITE_FEATURE_CACHE_RESULTS', true),
      bundleAnalyzer: getEnvBoolean('VITE_BUNDLE_ANALYZER', false),
      preloadRoutes: getEnvBoolean('VITE_FEATURE_PRELOAD_ROUTES', currentEnv === 'production')
    },
    
    monitoring: {
      enableErrorTracking: getEnvBoolean('VITE_FEATURE_ERROR_TRACKING', currentEnv !== 'development'),
      enablePerformanceTracking: getEnvBoolean('VITE_FEATURE_PERFORMANCE_TRACKING', currentEnv === 'production'),
      enableUsageTracking: getEnvBoolean('VITE_FEATURE_USAGE_TRACKING', false),
      anonymizeData: getEnvBoolean('VITE_ANONYMIZE_USER_DATA', true),
      sampleRate: getEnvNumber('VITE_MONITORING_SAMPLE_RATE', currentEnv === 'production' ? 10 : 100)
    },
    
    security: {
      enableCSP: getEnvBoolean('VITE_ENABLE_CSP', currentEnv === 'production'),
      enableHSTS: getEnvBoolean('VITE_ENABLE_HSTS', currentEnv === 'production'),
      enableXSSProtection: getEnvBoolean('VITE_ENABLE_XSS_PROTECTION', true),
      trustedDomains: getEnvVar('VITE_TRUSTED_DOMAINS', 'localhost,127.0.0.1').split(',')
    },
    
    deployment: {
      version: getEnvVar('VITE_APP_VERSION', '0.0.0'),
      buildTimestamp: getEnvVar('VITE_BUILD_TIMESTAMP', new Date().toISOString()),
      commitHash: getEnvVar('VITE_COMMIT_HASH', 'unknown'),
      deploymentId: getEnvVar('VITE_DEPLOYMENT_ID', `local-${Date.now()}`)
    }
  }
  
  return config
}

/**
 * Environment-specific overrides
 */
const getEnvironmentOverrides = (env: Environment): Partial<EnvironmentConfig> => {
  switch (env) {
    case 'development':
      return {
        features: {
          multiAgentReviews: true,
          realtimeUpdates: true,
          enhancedReports: true,
          serviceWorker: true,
          analytics: false,
          debugMode: true
        },
        monitoring: {
          enableErrorTracking: false,
          enablePerformanceTracking: false,
          enableUsageTracking: false,
          anonymizeData: true,
          sampleRate: 100
        },
        security: {
          enableCSP: false,
          enableHSTS: false,
          enableXSSProtection: false,
          trustedDomains: ['localhost', '127.0.0.1', '0.0.0.0']
        }
      }
    
    case 'staging':
      return {
        features: {
          multiAgentReviews: true,
          realtimeUpdates: true,
          enhancedReports: true,
          serviceWorker: true,
          analytics: true,
          debugMode: false
        },
        monitoring: {
          enableErrorTracking: true,
          enablePerformanceTracking: true,
          enableUsageTracking: true,
          anonymizeData: true,
          sampleRate: 50
        },
        performance: {
          enableCodeSplitting: true,
          enableLazyLoading: true,
          enableCaching: true,
          bundleAnalyzer: false,
          preloadRoutes: true
        }
      }
    
    case 'production':
      return {
        features: {
          multiAgentReviews: true,
          realtimeUpdates: true,
          enhancedReports: true,
          serviceWorker: true,
          analytics: true,
          debugMode: false
        },
        monitoring: {
          enableErrorTracking: true,
          enablePerformanceTracking: true,
          enableUsageTracking: true,
          anonymizeData: true,
          sampleRate: 10
        },
        performance: {
          enableCodeSplitting: true,
          enableLazyLoading: true,
          enableCaching: true,
          bundleAnalyzer: false,
          preloadRoutes: true
        },
        security: {
          enableCSP: true,
          enableHSTS: true,
          enableXSSProtection: true,
          trustedDomains: ['review.rma.local', 'api.rma.local']
        }
      }
    
    case 'test':
      return {
        features: {
          multiAgentReviews: false,
          realtimeUpdates: false,
          enhancedReports: false,
          serviceWorker: false,
          analytics: false,
          debugMode: false
        },
        monitoring: {
          enableErrorTracking: false,
          enablePerformanceTracking: false,
          enableUsageTracking: false,
          anonymizeData: true,
          sampleRate: 0
        }
      }
    
    default:
      return {}
  }
}

/**
 * Validate environment configuration
 */
const validateEnvironmentConfig = (config: EnvironmentConfig): void => {
  const errors: string[] = []
  
  // Validate URLs
  const urlFields = [
    { key: 'api.multiAgent.baseUrl', value: config.api.multiAgent.baseUrl },
    { key: 'api.legacyCodeReviewer.baseUrl', value: config.api.legacyCodeReviewer.baseUrl },
    { key: 'api.legacyClaude.baseUrl', value: config.api.legacyClaude.baseUrl },
    { key: 'api.bitbucket.baseUrl', value: config.api.bitbucket.baseUrl },
    { key: 'api.jira.baseUrl', value: config.api.jira.baseUrl }
  ]
  
  urlFields.forEach(({ key, value }) => {
    try {
      new URL(value)
    } catch {
      errors.push(`Invalid URL for ${key}: ${value}`)
    }
  })
  
  // Validate timeouts
  const timeoutFields = [
    { key: 'api.multiAgent.timeout', value: config.api.multiAgent.timeout },
    { key: 'api.legacyCodeReviewer.timeout', value: config.api.legacyCodeReviewer.timeout },
    { key: 'api.legacyClaude.timeout', value: config.api.legacyClaude.timeout }
  ]
  
  timeoutFields.forEach(({ key, value }) => {
    if (value <= 0 || value > 300000) { // Max 5 minutes
      errors.push(`Invalid timeout for ${key}: ${value}ms (must be between 1-300000ms)`)
    }
  })
  
  // Validate sample rate
  if (config.monitoring.sampleRate < 0 || config.monitoring.sampleRate > 100) {
    errors.push(`Invalid sample rate: ${config.monitoring.sampleRate} (must be between 0-100)`)
  }
  
  if (errors.length > 0) {
    console.error('Environment configuration validation errors:', errors)
    if (config.isProduction) {
      throw new Error(`Environment validation failed: ${errors.join(', ')}`)
    }
  }
}

/**
 * Create and validate environment configuration
 */
const createEnvironmentConfig = (): EnvironmentConfig => {
  const baseConfig = buildEnvironmentConfig()
  const overrides = getEnvironmentOverrides(baseConfig.NODE_ENV)
  
  // Deep merge configuration
  const config: EnvironmentConfig = {
    ...baseConfig,
    ...overrides,
    api: { ...baseConfig.api, ...overrides.api },
    features: { ...baseConfig.features, ...overrides.features },
    performance: { ...baseConfig.performance, ...overrides.performance },
    monitoring: { ...baseConfig.monitoring, ...overrides.monitoring },
    security: { ...baseConfig.security, ...overrides.security },
    deployment: { ...baseConfig.deployment, ...overrides.deployment }
  }
  
  // Validate configuration
  validateEnvironmentConfig(config)
  
  return config
}

/**
 * Global environment configuration instance
 */
export const env = createEnvironmentConfig()

/**
 * Environment utilities
 */
export const EnvUtils = {
  /**
   * Check if feature is enabled
   */
  isFeatureEnabled: (feature: keyof EnvironmentConfig['features']): boolean => {
    return env.features[feature]
  },

  /**
   * Get API configuration for service
   */
  getApiConfig: (service: keyof EnvironmentConfig['api']) => {
    return env.api[service]
  },

  /**
   * Get full service URL
   */
  getServiceUrl: (service: keyof EnvironmentConfig['api'], endpoint: string = ''): string => {
    const apiConfig = env.api[service]
    const baseUrl = 'baseUrl' in apiConfig ? apiConfig.baseUrl : ''
    return `${baseUrl}${endpoint}`
  },

  /**
   * Check if environment supports feature
   */
  supportsFeature: (feature: string): boolean => {
    // Advanced feature checking logic can be added here
    // For now, check if feature exists in our features config
    return feature in env.features || true
  },

  /**
   * Get deployment info
   */
  getDeploymentInfo: () => {
    return env.deployment
  },

  /**
   * Get monitoring configuration
   */
  getMonitoringConfig: () => {
    return env.monitoring
  },

  /**
   * Check if we should enable development features
   */
  isDevelopmentMode: (): boolean => {
    return env.isDevelopment || env.features.debugMode
  },

  /**
   * Get security headers configuration
   */
  getSecurityConfig: () => {
    return env.security
  },

  /**
   * Export configuration for debugging
   */
  exportConfig: () => {
    if (env.features.debugMode) {
      return {
        environment: env.NODE_ENV,
        version: env.deployment.version,
        buildTimestamp: env.deployment.buildTimestamp,
        features: env.features,
        apis: Object.keys(env.api).reduce((acc, key) => ({
          ...acc,
          [key]: {
            baseUrl: env.api[key as keyof EnvironmentConfig['api']].baseUrl,
            timeout: 'timeout' in env.api[key as keyof EnvironmentConfig['api']] 
              ? env.api[key as keyof EnvironmentConfig['api']].timeout 
              : 'N/A'
          }
        }), {})
      }
    }
    return null
  }
}

/**
 * Log environment configuration on startup
 */
if (env.features.debugMode) {
  console.group('🌍 Environment Configuration')
  console.log('Environment:', env.NODE_ENV)
  console.log('Version:', env.deployment.version)
  console.log('Build Timestamp:', env.deployment.buildTimestamp)
  console.log('Commit Hash:', env.deployment.commitHash)
  console.log('Features:', env.features)
  console.log('API Endpoints:', Object.fromEntries(
    Object.entries(env.api).map(([key, config]) => [
      key, 
      'baseUrl' in config ? config.baseUrl : 'Unknown'
    ])
  ))
  console.groupEnd()
}

export default env