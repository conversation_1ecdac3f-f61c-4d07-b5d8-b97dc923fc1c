import type {
  
  BitbucketPRComment,
  BitbucketDiff,
  CommentWithPosition,
  InlineCommentGroup,
  DiffHunk
} from '../../types/bitbucket.types'
import { bitbucketApiService } from './BitbucketApiService'
// import { useAuthenticatedRequest } from '../../hooks/useAuth'

export class PullRequestCommentsService {
  private apiService = bitbucketApiService

  /**
   * Get all comments for a pull request with enhanced metadata
   */
  async getPullRequestComments(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string
  ): Promise<{
    allComments: BitbucketPRComment[]
    inlineComments: CommentWithPosition[]
    generalComments: BitbucketPRComment[]
    commentsByFile: Record<string, CommentWithPosition[]>
  }> {
    try {
      // Get ALL comments (handle pagination)
      let allComments: BitbucketPRComment[] = []
      let page = 1
      let hasMore = true
      
      while (hasMore) {
        const { comments, hasNext } = await this.apiService.getPullRequestComments(
          workspace,
          repoSlug,
          prId,
          accessToken,
          page,
          100 // Get 100 per page for efficiency
        )
        
        allComments = [...allComments, ...comments]
        hasMore = hasNext
        page++
        
        // Safety limit to prevent infinite loops
        if (page > 20) {
          console.warn('Reached maximum page limit for comments')
          break
        }

        // Add small delay to prevent overwhelming the API and allow UI updates
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 10))
        }
      }
      
      console.log(`Loaded ${allComments.length} total comments for PR #${prId}`)

      // Separate inline and general comments
      const inlineComments: CommentWithPosition[] = []
      const generalComments: BitbucketPRComment[] = []

      allComments.forEach(comment => {
        if (comment.inline) {
          inlineComments.push({
            ...comment,
            position: {
              path: comment.inline.path,
              line: comment.inline.to,
              from: comment.inline.from
            }
          })
        } else {
          generalComments.push(comment)
        }
      })

      // Group inline comments by file
      const commentsByFile = inlineComments.reduce((acc, comment) => {
        const filePath = comment.position.path
        if (!acc[filePath]) {
          acc[filePath] = []
        }
        acc[filePath].push(comment)
        return acc
      }, {} as Record<string, CommentWithPosition[]>)

      return {
        allComments,
        inlineComments,
        generalComments,
        commentsByFile
      }
    } catch (error) {
      console.error('Failed to fetch PR comments:', error)
      throw new Error('Failed to load pull request comments.')
    }
  }

  /**
   * Get pull request diff with comments mapped to lines
   */
  async getPullRequestDiffWithComments(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string
  ): Promise<{
    diffText: string
    diffStat: BitbucketDiff[]
    commentsData: {
      allComments: BitbucketPRComment[]
      commentsByFile: Record<string, CommentWithPosition[]>
    }
    parsedDiff: DiffHunk[]
  }> {
    try {
      const [diffText, diffStat, commentsData] = await Promise.all([
        this.apiService.getPullRequestDiff(workspace, repoSlug, prId, accessToken),
        this.apiService.getPullRequestDiffstat(workspace, repoSlug, prId, accessToken),
        this.getPullRequestComments(workspace, repoSlug, prId, accessToken)
      ])

      console.log('PR Comments fetch result:', {
        workspace,
        repoSlug,
        prId,
        totalComments: commentsData.allComments.length,
        inlineComments: commentsData.inlineComments.length,
        commentsByFile: Object.keys(commentsData.commentsByFile),
        diffLength: diffText.length
      })

      // Parse diff into structured format - now async
      const parsedDiff = await this.parseDiffText(diffText)

      return {
        diffText,
        diffStat,
        commentsData,
        parsedDiff
      }
    } catch (error) {
      console.error('Failed to fetch PR diff with comments:', error)
      throw new Error('Failed to load pull request diff and comments.')
    }
  }

  /**
   * Parse diff text into structured hunks - ASYNC to prevent UI blocking
   */
  private async parseDiffText(diffText: string): Promise<DiffHunk[]> {
    return new Promise((resolve) => {
      // Use setTimeout to make parsing async and prevent UI blocking
      setTimeout(() => {
        const lines = diffText.split('\n')
        const hunks: DiffHunk[] = []
        let currentHunk: DiffHunk | null = null
        let currentFile = ''

        // Process in chunks to allow other tasks to run
        const processChunk = (startIndex: number) => {
          const chunkSize = 1000 // Process 1000 lines at a time
          const endIndex = Math.min(startIndex + chunkSize, lines.length)

          for (let i = startIndex; i < endIndex; i++) {
            const line = lines[i]
            
            // File header
            if (line.startsWith('diff --git')) {
              const match = line.match(/diff --git a\/(.+) b\/(.+)/)
              if (match) {
                currentFile = match[2] // Use the "b/" (new) file path
              }
            }
            
            // Hunk header
            else if (line.startsWith('@@')) {
              if (currentHunk) {
                hunks.push(currentHunk)
              }
              
              const match = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@(.*)/)
              if (match) {
                currentHunk = {
                  filePath: currentFile,
                  oldStart: parseInt(match[1]),
                  oldLines: parseInt(match[2] || '1'),
                  newStart: parseInt(match[3]),
                  newLines: parseInt(match[4] || '1'),
                  header: match[5].trim(),
                  lines: []
                }
              }
            }
            
            // Content lines
            else if (currentHunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-'))) {
              const type = line.startsWith('+') ? 'addition' : 
                          line.startsWith('-') ? 'deletion' : 'context'
              
              currentHunk.lines.push({
                type,
                content: line.slice(1), // Remove the +/- prefix
                oldLineNumber: type === 'addition' ? null : currentHunk.oldStart + currentHunk.lines.filter(l => l.type !== 'addition').length,
                newLineNumber: type === 'deletion' ? null : currentHunk.newStart + currentHunk.lines.filter(l => l.type !== 'deletion').length
              })
            }
          }

          if (endIndex < lines.length) {
            // More lines to process, continue asynchronously
            setTimeout(() => processChunk(endIndex), 0)
          } else {
            // Finished processing
            if (currentHunk) {
              hunks.push(currentHunk)
            }
            resolve(hunks)
          }
        }

        // Start processing
        processChunk(0)
      }, 0)
    })
  }

  /**
   * Get comments for a specific file and line
   */
  getCommentsForLine(
    commentsByFile: Record<string, CommentWithPosition[]>,
    filePath: string,
    lineNumber: number
  ): CommentWithPosition[] {
    const fileComments = commentsByFile[filePath] || []
    return fileComments.filter(comment => comment.position.line === lineNumber)
  }

  /**
   * Group comments by thread (parent-child relationships)
   */
  groupCommentsByThread(comments: BitbucketPRComment[]): InlineCommentGroup[] {
    const commentMap = new Map<number, BitbucketPRComment>()
    const threads: InlineCommentGroup[] = []

    // Index all comments
    comments.forEach(comment => {
      commentMap.set(comment.id, comment)
    })

    // Find root comments (no parent) and build threads
    comments.forEach(comment => {
      if (!comment.parent) {
        const thread: InlineCommentGroup = {
          rootComment: comment,
          replies: [],
          position: comment.inline ? {
            path: comment.inline.path,
            line: comment.inline.to,
            from: comment.inline.from
          } : null
        }

        // Find all replies to this comment
        comments.forEach(potentialReply => {
          if (potentialReply.parent?.id === comment.id) {
            thread.replies.push(potentialReply)
          }
        })

        // Sort replies by creation date
        thread.replies.sort((a, b) => 
          new Date(a.created_on).getTime() - new Date(b.created_on).getTime()
        )

        threads.push(thread)
      }
    })

    return threads
  }

  /**
   * Create a new comment on a PR
   */
  async createComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    content: string,
    accessToken: string,
    options: {
      inline?: {
        path: string
        line: number
        from?: number
      }
      parentId?: number
    } = {}
  ): Promise<BitbucketPRComment> {
    try {
      const createOptions: any = {}

      if (options.inline) {
        createOptions.inline = {
          path: options.inline.path,
          to: options.inline.line,
          ...(options.inline.from && { from: options.inline.from })
        }
      }

      if (options.parentId) {
        createOptions.parent = options.parentId
      }

      return await this.apiService.createPullRequestComment(
        workspace,
        repoSlug,
        prId,
        content,
        accessToken,
        createOptions
      )
    } catch (error) {
      console.error('Failed to create comment:', error)
      throw new Error('Failed to create comment.')
    }
  }

  /**
   * Reply to an existing comment
   */
  async replyToComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    parentCommentId: number,
    content: string,
    accessToken: string
  ): Promise<BitbucketPRComment> {
    return this.createComment(workspace, repoSlug, prId, content, accessToken, {
      parentId: parentCommentId
    })
  }

  /**
   * Update an existing comment
   */
  async updateComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    commentId: number,
    content: string,
    accessToken: string
  ): Promise<BitbucketPRComment> {
    try {
      return await this.apiService.updatePullRequestComment(
        workspace,
        repoSlug,
        prId,
        commentId,
        content,
        accessToken
      )
    } catch (error) {
      console.error('Failed to update comment:', error)
      throw new Error('Failed to update comment.')
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    commentId: number,
    accessToken: string
  ): Promise<void> {
    try {
      await this.apiService.deletePullRequestComment(
        workspace,
        repoSlug,
        prId,
        commentId,
        accessToken
      )
    } catch (error) {
      console.error('Failed to delete comment:', error)
      throw new Error('Failed to delete comment.')
    }
  }

  /**
   * Get statistics about PR comments
   */
  getCommentStatistics(comments: BitbucketPRComment[]): {
    total: number
    inline: number
    general: number
    resolved: number
    pending: number
    participants: number
  } {
    const inline = comments.filter(c => c.inline).length
    const general = comments.length - inline
    const resolved = comments.filter(c => c.deleted).length
    const pending = comments.length - resolved
    
    const participants = new Set(comments.map(c => c.user.uuid)).size

    return {
      total: comments.length,
      inline,
      general,
      resolved,
      pending,
      participants
    }
  }
}

export const pullRequestCommentsService = new PullRequestCommentsService()

// React hook for PR comments operations
export const usePullRequestComments = () => {
  // const { makeRequest } = useAuthenticatedRequest()

  const getCommentsWithDiff = async (workspace: string, repoSlug: string, prId: number) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    
    return pullRequestCommentsService.getPullRequestDiffWithComments(
      workspace,
      repoSlug,
      prId,
      accessToken
    )
  }

  const getComments = async (workspace: string, repoSlug: string, prId: number) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    
    return pullRequestCommentsService.getPullRequestComments(
      workspace,
      repoSlug,
      prId,
      accessToken
    )
  }

  const createComment = async (
    workspace: string,
    repoSlug: string,
    prId: number,
    content: string,
    options?: {
      inline?: { path: string; line: number; from?: number }
      parentId?: number
    }
  ) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    
    return pullRequestCommentsService.createComment(
      workspace,
      repoSlug,
      prId,
      content,
      accessToken,
      options
    )
  }

  const replyToComment = async (
    workspace: string,
    repoSlug: string,
    prId: number,
    parentCommentId: number,
    content: string
  ) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    
    return pullRequestCommentsService.replyToComment(
      workspace,
      repoSlug,
      prId,
      parentCommentId,
      content,
      accessToken
    )
  }

  return {
    getCommentsWithDiff,
    getComments,
    createComment,
    replyToComment,
    service: pullRequestCommentsService
  }
}