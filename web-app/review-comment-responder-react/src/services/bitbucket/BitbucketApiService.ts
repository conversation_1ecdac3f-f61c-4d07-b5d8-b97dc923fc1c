import type {
  BitbucketApiResponse,
  <PERSON>bucket<PERSON>ser,
  BitbucketWorkspace,
  BitbucketRepository,
  BitbucketBranch,
  BitbucketPullRequest,
  BitbucketPRComment,
  BitbucketDiff
} from '../../types/bitbucket.types'
import { bitbucketAuthService } from '../auth/BitbucketAuthService'
import { cacheManager, withCache } from '../cache/CacheManager'
import { withErrorHandling, withRetry } from '../errors/ErrorHandler'

class BitbucketApiService {
  private readonly API_BASE = 'https://api.bitbucket.org/2.0'

  /**
   * Get current authenticated user
   */
  async getCurrentUser(accessToken: string): Promise<BitbucketUser> {
    return withErrorHandling(
      () => withCache(
        'USER_PROFILE',
        'current',
        () => bitbucketAuthService.makeAuthenticatedRequest<BitbucketUser>(
          '/user',
          accessToken
        )
      ),
      { component: 'BitbucketApiService', action: 'getCurrentUser' }
    )
  }

  /**
   * Get user's workspaces
   */
  async getUserWorkspaces(accessToken: string): Promise<BitbucketWorkspace[]> {
    return withErrorHandling(
      () => withCache(
        'WORKSPACES',
        'user',
        async () => {
          const response = await bitbucketAuthService.makeAuthenticatedRequest<
            BitbucketApiResponse<BitbucketWorkspace>
          >('/workspaces?role=member', accessToken)
          return response.values
        }
      ),
      { component: 'BitbucketApiService', action: 'getUserWorkspaces' }
    )
  }

  /**
   * Get repositories for a workspace
   */
  async getWorkspaceRepositories(
    workspace: string, 
    accessToken: string,
    page?: number,
    pagelen: number = 50
  ): Promise<{ repositories: BitbucketRepository[], hasNext: boolean }> {
    const cacheParams = { page, pagelen }
    
    return withErrorHandling(
      () => withRetry(
        () => withCache(
          'REPOSITORIES',
          workspace,
          async () => {
            const params = new URLSearchParams({
              pagelen: pagelen.toString(),
              ...(page && { page: page.toString() })
            })

            const response = await bitbucketAuthService.makeAuthenticatedRequest<
              BitbucketApiResponse<BitbucketRepository>
            >(`/repositories/${workspace}?${params.toString()}`, accessToken)
            
            return {
              repositories: response.values,
              hasNext: !!response.next
            }
          },
          cacheParams
        ),
        { component: 'BitbucketApiService', action: 'getWorkspaceRepositories' },
        2
      ),
      { component: 'BitbucketApiService', action: 'getWorkspaceRepositories' }
    )
  }

  /**
   * Get specific repository
   */
  async getRepository(
    workspace: string, 
    repoSlug: string, 
    accessToken: string
  ): Promise<BitbucketRepository> {
    return bitbucketAuthService.makeAuthenticatedRequest<BitbucketRepository>(
      `/repositories/${workspace}/${repoSlug}`,
      accessToken
    )
  }

  /**
   * Get branches for a repository
   */
  async getRepositoryBranches(
    workspace: string,
    repoSlug: string,
    accessToken: string,
    page?: number,
    pagelen: number = 50
  ): Promise<{ branches: BitbucketBranch[], hasNext: boolean }> {
    const params = new URLSearchParams({
      pagelen: pagelen.toString(),
      ...(page && { page: page.toString() })
    })

    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<BitbucketBranch>
    >(`/repositories/${workspace}/${repoSlug}/refs/branches?${params.toString()}`, accessToken)
    
    return {
      branches: response.values,
      hasNext: !!response.next
    }
  }

  /**
   * Get ALL branches for a repository (handles pagination automatically)
   */
  async getAllRepositoryBranches(
    workspace: string,
    repoSlug: string,
    accessToken: string
  ): Promise<BitbucketBranch[]> {
    return withErrorHandling(
      async () => {
        let allBranches: BitbucketBranch[] = []
        let page = 1
        let hasNext = true
        const pageSize = 100 // Use larger page size for efficiency

        while (hasNext) {
          const { branches, hasNext: hasMorePages } = await this.getRepositoryBranches(
            workspace,
            repoSlug,
            accessToken,
            page,
            pageSize
          )

          allBranches = [...allBranches, ...branches]
          hasNext = hasMorePages
          page++

          // Safety break to prevent infinite loops (max 50 pages = 5000 branches)
          if (page > 50) {
            console.warn(`Reached maximum page limit (50) for repository ${workspace}/${repoSlug}`)
            break
          }
        }

        console.log(`Loaded ${allBranches.length} branches from ${workspace}/${repoSlug}`)
        return allBranches
      },
      { 
        component: 'BitbucketApiService', 
        action: 'getAllRepositoryBranches',
        additionalData: {
          workspace,
          repository: repoSlug
        }
      }
    )
  }

  /**
   * Get pull requests for a repository
   */
  async getRepositoryPullRequests(
    workspace: string,
    repoSlug: string,
    accessToken: string,
    options: {
      state?: 'OPEN' | 'MERGED' | 'DECLINED' | 'SUPERSEDED'
      page?: number
      pagelen?: number
      sort?: string
    } = {}
  ): Promise<{ pullRequests: BitbucketPullRequest[], hasNext: boolean }> {
    const params = new URLSearchParams({
      pagelen: (options.pagelen || 50).toString(),
      ...(options.page && { page: options.page.toString() }),
      ...(options.state && { state: options.state }),
      ...(options.sort && { sort: options.sort })
    })

    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<BitbucketPullRequest>
    >(`/repositories/${workspace}/${repoSlug}/pullrequests?${params.toString()}`, accessToken)
    
    return {
      pullRequests: response.values,
      hasNext: !!response.next
    }
  }

  /**
   * Get specific pull request
   */
  async getPullRequest(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string
  ): Promise<BitbucketPullRequest> {
    return bitbucketAuthService.makeAuthenticatedRequest<BitbucketPullRequest>(
      `/repositories/${workspace}/${repoSlug}/pullrequests/${prId}`,
      accessToken
    )
  }

  /**
   * Get pull request comments
   */
  async getPullRequestComments(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string,
    page?: number,
    pagelen: number = 50
  ): Promise<{ comments: BitbucketPRComment[], hasNext: boolean }> {
    const cacheParams = { page, pagelen }
    const identifier = `${workspace}/${repoSlug}/${prId}`
    
    return withErrorHandling(
      () => withRetry(
        () => withCache(
          'PR_COMMENTS',
          identifier,
          async () => {
            const params = new URLSearchParams({
              pagelen: pagelen.toString(),
              ...(page && { page: page.toString() })
            })

            const response = await bitbucketAuthService.makeAuthenticatedRequest<
              BitbucketApiResponse<BitbucketPRComment>
            >(`/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/comments?${params.toString()}`, accessToken)
            
            return {
              comments: response.values,
              hasNext: !!response.next
            }
          },
          cacheParams
        ),
        { 
          component: 'BitbucketApiService', 
          action: 'getPullRequestComments',
          additionalData: {
            workspace,
            repository: repoSlug,
            pullRequest: prId.toString()
          }
        },
        2
      ),
      { 
        component: 'BitbucketApiService', 
        action: 'getPullRequestComments',
        additionalData: {
          workspace,
          repository: repoSlug,
          pullRequest: prId.toString()
        }
      }
    )
  }

  /**
   * Get pull request diff
   */
  async getPullRequestDiff(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string,
    options: {
      context?: number
      ignoreWhitespace?: boolean
      binary?: boolean
    } = {}
  ): Promise<string> {
    const params = new URLSearchParams()
    
    if (options.context !== undefined) {
      params.append('context', options.context.toString())
    }
    if (options.ignoreWhitespace) {
      params.append('ignore_whitespace', 'true')
    }
    if (options.binary !== undefined) {
      params.append('binary', options.binary.toString())
    }

    const queryString = params.toString()
    const url = `/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/diff${queryString ? `?${queryString}` : ''}`

    // Get diff as plain text
    const response = await fetch(`${this.API_BASE}${url}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'text/plain'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch diff: ${response.statusText}`)
    }

    return response.text()
  }

  /**
   * Get pull request diffstat
   */
  async getPullRequestDiffstat(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string
  ): Promise<BitbucketDiff[]> {
    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<BitbucketDiff>
    >(`/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/diffstat`, accessToken)
    
    return response.values
  }

  /**
   * Get pull request commits
   */
  async getPullRequestCommits(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string,
    page?: number,
    pagelen: number = 50
  ): Promise<{ commits: any[], hasNext: boolean }> {
    const params = new URLSearchParams({
      pagelen: pagelen.toString(),
      ...(page && { page: page.toString() })
    })

    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<any>
    >(`/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/commits?${params.toString()}`, accessToken)
    
    return {
      commits: response.values,
      hasNext: !!response.next
    }
  }

  /**
   * Search repositories across workspaces
   */
  async searchRepositories(
    query: string,
    accessToken: string,
    page?: number,
    pagelen: number = 25
  ): Promise<{ repositories: BitbucketRepository[], hasNext: boolean }> {
    const params = new URLSearchParams({
      q: `name ~ "${query}"`,
      pagelen: pagelen.toString(),
      ...(page && { page: page.toString() })
    })

    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<BitbucketRepository>
    >(`/repositories?${params.toString()}`, accessToken)
    
    return {
      repositories: response.values,
      hasNext: !!response.next
    }
  }

  /**
   * Get user's recent repositories
   */
  async getUserRecentRepositories(
    accessToken: string,
    pagelen: number = 10
  ): Promise<BitbucketRepository[]> {
    const response = await bitbucketAuthService.makeAuthenticatedRequest<
      BitbucketApiResponse<BitbucketRepository>
    >(`/repositories?role=contributor&sort=-updated_on&pagelen=${pagelen}`, accessToken)
    
    return response.values
  }

  /**
   * Create a new pull request comment
   */
  async createPullRequestComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    content: string,
    accessToken: string,
    options: {
      inline?: {
        path: string
        to: number
        from?: number
      }
      parent?: number
    } = {}
  ): Promise<BitbucketPRComment> {
    const result = await withErrorHandling(
      async () => {
        const body: any = {
          content: {
            raw: content
          }
        }

        if (options.inline) {
          body.inline = options.inline
        }

        if (options.parent) {
          body.parent = { id: options.parent }
        }

        const comment = await bitbucketAuthService.makeAuthenticatedRequest<BitbucketPRComment>(
          `/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/comments`,
          accessToken,
          {
            method: 'POST',
            body: JSON.stringify(body)
          }
        )

        // Invalidate related cache entries
        cacheManager.invalidateRelated({
          workspace,
          repository: repoSlug,
          pullRequest: prId
        })

        return comment
      },
      { 
        component: 'BitbucketApiService', 
        action: 'createPullRequestComment',
        additionalData: {
          workspace,
          repository: repoSlug,
          pullRequest: prId.toString()
        }
      }
    )

    return result
  }

  /**
   * Update a pull request comment
   */
  async updatePullRequestComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    commentId: number,
    content: string,
    accessToken: string
  ): Promise<BitbucketPRComment> {
    const body = {
      content: {
        raw: content
      }
    }

    return bitbucketAuthService.makeAuthenticatedRequest<BitbucketPRComment>(
      `/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/comments/${commentId}`,
      accessToken,
      {
        method: 'PUT',
        body: JSON.stringify(body)
      }
    )
  }

  /**
   * Delete a pull request comment
   */
  async deletePullRequestComment(
    workspace: string,
    repoSlug: string,
    prId: number,
    commentId: number,
    accessToken: string
  ): Promise<void> {
    await bitbucketAuthService.makeAuthenticatedRequest(
      `/repositories/${workspace}/${repoSlug}/pullrequests/${prId}/comments/${commentId}`,
      accessToken,
      {
        method: 'DELETE'
      }
    )
  }
}

export const bitbucketApiService = new BitbucketApiService()
export default BitbucketApiService