import type {
  BitbucketPullRequest,
  <PERSON>bu<PERSON><PERSON><PERSON>om<PERSON>,
  BitbucketDiff,
  EnhancedPRComment
} from '../../types/bitbucket.types'
import { bitbucketApiService } from './BitbucketApiService'
// import { useAuthenticatedRequest } from '../../hooks/useAuth'

export class PullRequestService {
  private apiService = bitbucketApiService

  /**
   * Get pull requests assigned to current user for review
   */
  async getAssignedPullRequests(
    accessToken: string,
    options: {
      limit?: number
      includeWorkspaces?: string[]
    } = {}
  ): Promise<{
    pullRequests: (BitbucketPullRequest & { workspace: string; repository: string })[]
    total: number
  }> {
    try {
      // Get current user to filter by reviewer
      const currentUser = await this.apiService.getCurrentUser(accessToken)
      
      if (!currentUser) {
        throw new Error('Unable to get current user information')
      }

      // Get user's workspaces/repositories
      const workspaces = options.includeWorkspaces || ['rma-digital'] // Default to RMA workspace
      const allPRs: (BitbucketPullRequest & { workspace: string; repository: string })[] = []

      for (const workspace of workspaces) {
        try {
          // Get repositories for this workspace
          const { repositories } = await this.apiService.getWorkspaceRepositories(workspace, accessToken)
          
          for (const repo of repositories) {
            try {
              // Get PRs where current user is a reviewer
              const { pullRequests } = await this.apiService.getRepositoryPullRequests(
                workspace,
                repo.name,
                accessToken,
                {
                  state: 'OPEN',
                  pagelen: options.limit || 25
                }
              )

              // Filter PRs where current user is assigned as reviewer
              const assignedPRs = pullRequests.filter(pr => 
                pr.reviewers?.some(reviewer => 
                  reviewer.uuid === currentUser.uuid || 
                  reviewer.username === currentUser.username ||
                  reviewer.account_id === currentUser.account_id
                )
              )

              // Add workspace and repository context
              const enrichedPRs = assignedPRs.map(pr => ({
                ...pr,
                workspace,
                repository: repo.name
              }))

              allPRs.push(...enrichedPRs)

            } catch (repoError) {
              console.warn(`Failed to fetch PRs for ${workspace}/${repo.name}:`, repoError)
            }
          }
        } catch (workspaceError) {
          console.warn(`Failed to fetch repositories for ${workspace}:`, workspaceError)
        }
      }

      // Sort by most recently updated
      allPRs.sort((a, b) => new Date(b.updated_on).getTime() - new Date(a.updated_on).getTime())

      return {
        pullRequests: allPRs,
        total: allPRs.length
      }

    } catch (error) {
      console.error('Failed to fetch assigned pull requests:', error)
      throw error
    }
  }

  /**
   * Get pull requests with enhanced filtering and sorting
   */
  async getPullRequests(
    workspace: string,
    repoSlug: string,
    accessToken: string,
    options: {
      state?: 'OPEN' | 'MERGED' | 'DECLINED' | 'SUPERSEDED' | 'ALL'
      author?: string
      reviewers?: string[]
      sort?: 'created_on' | 'updated_on' | 'activity'
      direction?: 'asc' | 'desc'
      limit?: number
    } = {}
  ): Promise<{
    pullRequests: BitbucketPullRequest[]
    hasMore: boolean
    stats: {
      open: number
      merged: number
      declined: number
      total: number
    }
  }> {
    try {
      const fetchOptions: any = {
        pagelen: options.limit || 50,
        sort: options.sort ? `${options.direction === 'asc' ? '' : '-'}${options.sort}` : '-updated_on'
      }

      if (options.state && options.state !== 'ALL') {
        fetchOptions.state = options.state
      }

      const { pullRequests, hasNext } = await this.apiService.getRepositoryPullRequests(
        workspace,
        repoSlug,
        accessToken,
        fetchOptions
      )

      // Get stats for all PRs if not filtering by state
      let stats = { open: 0, merged: 0, declined: 0, total: 0 }
      
      if (options.state === 'ALL' || !options.state) {
        try {
          const [openResult, mergedResult, declinedResult] = await Promise.all([
            this.apiService.getRepositoryPullRequests(workspace, repoSlug, accessToken, { 
              state: 'OPEN', 
              pagelen: 1 
            }),
            this.apiService.getRepositoryPullRequests(workspace, repoSlug, accessToken, { 
              state: 'MERGED', 
              pagelen: 1 
            }),
            this.apiService.getRepositoryPullRequests(workspace, repoSlug, accessToken, { 
              state: 'DECLINED', 
              pagelen: 1 
            })
          ])

          stats = {
            open: openResult.pullRequests.length,
            merged: mergedResult.pullRequests.length,
            declined: declinedResult.pullRequests.length,
            total: openResult.pullRequests.length + mergedResult.pullRequests.length + declinedResult.pullRequests.length
          }
        } catch (error) {
          console.warn('Failed to fetch PR stats:', error)
        }
      }

      return {
        pullRequests,
        hasMore: hasNext,
        stats
      }
    } catch (error) {
      console.error('Failed to fetch pull requests:', error)
      throw new Error('Failed to load pull requests. Please check your permissions.')
    }
  }

  /**
   * Get pull request with all related data
   */
  async getPullRequestWithDetails(
    workspace: string,
    repoSlug: string,
    prId: number,
    accessToken: string
  ): Promise<{
    pullRequest: BitbucketPullRequest
    comments: EnhancedPRComment[]
    diff: string
    diffStats: BitbucketDiff[]
    commits: any[]
  }> {
    try {
      const [pullRequest, { comments }, diff, diffStats] = await Promise.all([
        this.apiService.getPullRequest(workspace, repoSlug, prId, accessToken),
        this.apiService.getPullRequestComments(workspace, repoSlug, prId, accessToken, 1, 100),
        this.apiService.getPullRequestDiff(workspace, repoSlug, prId, accessToken),
        this.apiService.getPullRequestDiffstat(workspace, repoSlug, prId, accessToken)
      ])

      // Get commits separately as it might be large
      let commits: any[] = []
      try {
        const commitsResult = await this.apiService.getPullRequestCommits(
          workspace, 
          repoSlug, 
          prId, 
          accessToken, 
          1, 
          20
        )
        commits = commitsResult.commits
      } catch (error) {
        console.warn('Failed to fetch PR commits:', error)
      }

      // Enhance comments with threading and app-specific data
      const enhancedComments = this.enhanceComments(comments)

      return {
        pullRequest,
        comments: enhancedComments,
        diff,
        diffStats,
        commits
      }
    } catch (error) {
      console.error('Failed to fetch pull request details:', error)
      throw new Error('Failed to load pull request details.')
    }
  }

  /**
   * Enhance comments with threading and app-specific features
   */
  private enhanceComments(comments: BitbucketPRComment[]): EnhancedPRComment[] {
    const commentMap = new Map<number, EnhancedPRComment>()
    const rootComments: EnhancedPRComment[] = []

    // First pass: create enhanced comments
    comments.forEach(comment => {
      const enhanced: EnhancedPRComment = {
        ...comment,
        isVisible: true,
        threadReplies: [],
        claudeResponse: undefined
      }
      commentMap.set(comment.id, enhanced)
    })

    // Second pass: build threading
    comments.forEach(comment => {
      const enhanced = commentMap.get(comment.id)!
      
      if (comment.parent?.id) {
        const parent = commentMap.get(comment.parent.id)
        if (parent) {
          parent.threadReplies.push(enhanced)
        } else {
          rootComments.push(enhanced)
        }
      } else {
        rootComments.push(enhanced)
      }
    })

    return rootComments
  }

  /**
   * Get inline comments by file and line
   */
  getInlineCommentsByLocation(comments: EnhancedPRComment[]): Map<string, Map<number, EnhancedPRComment[]>> {
    const locationMap = new Map<string, Map<number, EnhancedPRComment[]>>()

    const processComments = (commentList: EnhancedPRComment[]) => {
      commentList.forEach(comment => {
        if (comment.inline) {
          const filePath = comment.inline.path
          const lineNumber = comment.inline.to

          if (!locationMap.has(filePath)) {
            locationMap.set(filePath, new Map())
          }

          const fileMap = locationMap.get(filePath)!
          if (!fileMap.has(lineNumber)) {
            fileMap.set(lineNumber, [])
          }

          fileMap.get(lineNumber)!.push(comment)
        }

        // Process replies recursively
        if (comment.threadReplies.length > 0) {
          processComments(comment.threadReplies)
        }
      })
    }

    processComments(comments)
    return locationMap
  }

  /**
   * Parse diff for code viewer integration
   */
  parseDiffForViewer(diff: string, diffStats: BitbucketDiff[]): {
    files: Array<{
      path: string
      oldPath?: string
      status: string
      additions: number
      deletions: number
      chunks: Array<{
        oldStart: number
        oldLines: number
        newStart: number
        newLines: number
        header: string
        lines: Array<{
          type: 'context' | 'addition' | 'deletion'
          content: string
          oldLineNumber?: number
          newLineNumber?: number
        }>
      }>
    }>
  } {
    const files: any[] = []
    const diffSections = diff.split(/(?=diff --git)/)

    diffSections.forEach((section) => {
      if (!section.trim()) return

      const lines = section.split('\n')
      const fileMatch = lines[0]?.match(/diff --git a\/(.*?) b\/(.*)/)
      
      if (!fileMatch) return

      const oldPath = fileMatch[1]
      const newPath = fileMatch[2]
      const filePath = newPath

      // Find corresponding diffstat
      const stat = diffStats.find(s => 
        s.new?.path === filePath || 
        s.old?.path === filePath
      )

      const file = {
        path: filePath,
        oldPath: oldPath !== newPath ? oldPath : undefined,
        status: stat?.status || 'modified',
        additions: stat?.lines_added || 0,
        deletions: stat?.lines_removed || 0,
        chunks: [] as any[]
      }

      let currentChunk: any = null
      let oldLineNum = 0
      let newLineNum = 0

      lines.forEach(line => {
        if (line.startsWith('@@')) {
          // New chunk
          const match = line.match(/@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@(.*)/)
          if (match) {
            oldLineNum = parseInt(match[1])
            newLineNum = parseInt(match[3])
            
            currentChunk = {
              oldStart: oldLineNum,
              oldLines: parseInt(match[2]) || 1,
              newStart: newLineNum,
              newLines: parseInt(match[4]) || 1,
              header: match[5]?.trim() || '',
              lines: []
            }
            file.chunks.push(currentChunk)
          }
        } else if (currentChunk && !line.startsWith('diff') && !line.startsWith('index') && !line.startsWith('+++') && !line.startsWith('---')) {
          if (line.startsWith('+')) {
            currentChunk.lines.push({
              type: 'addition',
              content: line.substring(1),
              newLineNumber: newLineNum++
            })
          } else if (line.startsWith('-')) {
            currentChunk.lines.push({
              type: 'deletion',
              content: line.substring(1),
              oldLineNumber: oldLineNum++
            })
          } else {
            currentChunk.lines.push({
              type: 'context',
              content: line.substring(1),
              oldLineNumber: oldLineNum++,
              newLineNumber: newLineNum++
            })
          }
        }
      })

      files.push(file)
    })

    return { files }
  }

  /**
   * Format PR for display
   */
  formatPullRequestDisplay(pr: BitbucketPullRequest): {
    title: string
    description: string
    author: string
    status: {
      state: string
      color: string
      icon: string
    }
    branch: {
      source: string
      destination: string
    }
    stats: {
      comments: number
      tasks: number
      approvals: number
    }
    dates: {
      created: string
      updated: string
      timeAgo: string
    }
  } {
    const statusConfig = {
      'OPEN': { color: 'text-green-400', icon: '○', label: 'Open' },
      'MERGED': { color: 'text-purple-400', icon: '✓', label: 'Merged' },
      'DECLINED': { color: 'text-red-400', icon: '✗', label: 'Declined' },
      'SUPERSEDED': { color: 'text-yellow-400', icon: '◐', label: 'Superseded' }
    }

    const status = statusConfig[pr.state] || statusConfig['OPEN']
    const approvals = pr.participants?.filter(p => p.approved).length || 0

    return {
      title: pr.title,
      description: pr.description || 'No description provided',
      author: pr.author.display_name,
      status: {
        state: status.label,
        color: status.color,
        icon: status.icon
      },
      branch: {
        source: pr.source.branch.name,
        destination: pr.destination.branch.name
      },
      stats: {
        comments: pr.comment_count,
        tasks: pr.task_count,
        approvals
      },
      dates: {
        created: new Date(pr.created_on).toLocaleDateString(),
        updated: new Date(pr.updated_on).toLocaleDateString(),
        timeAgo: this.getTimeAgo(pr.updated_on)
      }
    }
  }

  /**
   * Get human-readable time ago
   */
  private getTimeAgo(dateString: string): string {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffMinutes = Math.floor(diffMs / (1000 * 60))

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
    return 'Just now'
  }
}

export const pullRequestService = new PullRequestService()

// React hook for pull request operations
export const usePullRequestService = () => {
  // const { makeRequest } = useAuthenticatedRequest()

  const getPullRequests = async (workspace: string, repoSlug: string, options?: any) => {
    const accessToken = '' // Handled by makeRequest
    return pullRequestService.getPullRequests(workspace, repoSlug, accessToken, options)
  }

  const getPullRequestWithDetails = async (workspace: string, repoSlug: string, prId: number) => {
    const accessToken = ''
    return pullRequestService.getPullRequestWithDetails(workspace, repoSlug, prId, accessToken)
  }

  return {
    getPullRequests,
    getPullRequestWithDetails,
    service: pullRequestService
  }
}