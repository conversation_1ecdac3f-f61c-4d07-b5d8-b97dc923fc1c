import type {
  BitbucketRepository,
  BitbucketBranch,
  BitbucketPullRequest
} from '../../types/bitbucket.types'
import { bitbucketApiService } from './BitbucketApiService'
// import { useAuthenticatedRequest } from '../../hooks/useAuth'

export class RepositoryService {
  private apiService = bitbucketApiService

  /**
   * Get all accessible repositories for the authenticated user
   */
  async getAllUserRepositories(accessToken: string): Promise<{
    byWorkspace: Record<string, BitbucketRepository[]>
    recent: BitbucketRepository[]
    total: number
  }> {
    try {
      // Get user's workspaces first
      const workspaces = await this.apiService.getUserWorkspaces(accessToken)
      
      // Get recent repositories
      const recent = await this.apiService.getUserRecentRepositories(accessToken, 10)
      
      // Fetch repositories for each workspace
      const byWorkspace: Record<string, BitbucketRepository[]> = {}
      let total = 0

      for (const workspace of workspaces) {
        try {
          const { repositories } = await this.apiService.getWorkspaceRepositories(
            workspace.slug,
            accessToken,
            1,
            100 // Get more repos per workspace
          )
          
          byWorkspace[workspace.slug] = repositories
          total += repositories.length
        } catch (error) {
          console.warn(`Failed to fetch repositories for workspace ${workspace.slug}:`, error)
          byWorkspace[workspace.slug] = []
        }
      }

      return {
        byWorkspace,
        recent,
        total
      }
    } catch (error) {
      console.error('Failed to fetch user repositories:', error)
      throw new Error('Failed to load repositories. Please check your permissions.')
    }
  }

  /**
   * Search repositories with intelligent filtering
   */
  async searchRepositories(
    query: string,
    accessToken: string,
    options: {
      workspace?: string
      includePrivate?: boolean
      language?: string
      limit?: number
    } = {}
  ): Promise<BitbucketRepository[]> {
    try {
      if (options.workspace) {
        // Search within specific workspace
        const { repositories } = await this.apiService.getWorkspaceRepositories(
          options.workspace,
          accessToken,
          1,
          options.limit || 50
        )
        
        // Filter by query
        return repositories.filter(repo => 
          repo.name.toLowerCase().includes(query.toLowerCase()) ||
          repo.full_name.toLowerCase().includes(query.toLowerCase()) ||
          (repo.description && repo.description.toLowerCase().includes(query.toLowerCase()))
        )
      } else {
        // Global search
        const { repositories } = await this.apiService.searchRepositories(
          query,
          accessToken,
          1,
          options.limit || 25
        )
        
        return repositories
      }
    } catch (error) {
      console.error('Repository search failed:', error)
      throw new Error('Search failed. Please try again.')
    }
  }

  /**
   * Get repository with branches
   */
  async getRepositoryWithBranches(
    workspace: string,
    repoSlug: string,
    accessToken: string
  ): Promise<{
    repository: BitbucketRepository
    branches: BitbucketBranch[]
    defaultBranch?: BitbucketBranch
  }> {
    try {
      const [repository, branches] = await Promise.all([
        this.apiService.getRepository(workspace, repoSlug, accessToken),
        this.apiService.getAllRepositoryBranches(workspace, repoSlug, accessToken)
      ])

      // Find default branch (usually main or master)
      const defaultBranch = branches.find(branch => 
        branch.name === 'main' || 
        branch.name === 'master' ||
        branch.name === 'develop'
      ) || branches[0]

      return {
        repository,
        branches,
        defaultBranch
      }
    } catch (error) {
      console.error('Failed to fetch repository with branches:', error)
      throw new Error('Failed to load repository details.')
    }
  }

  /**
   * Get repository statistics
   */
  async getRepositoryStats(
    workspace: string,
    repoSlug: string,
    accessToken: string
  ): Promise<{
    totalBranches: number
    openPRs: number
    totalPRs: number
    lastActivity: string
  }> {
    try {
      const [{ branches }, { pullRequests: openPRs }, { pullRequests: allPRs }] = await Promise.all([
        this.apiService.getRepositoryBranches(workspace, repoSlug, accessToken, 1, 1),
        this.apiService.getRepositoryPullRequests(workspace, repoSlug, accessToken, { 
          state: 'OPEN',
          pagelen: 1 
        }),
        this.apiService.getRepositoryPullRequests(workspace, repoSlug, accessToken, { 
          pagelen: 1 
        })
      ])

      const repository = await this.apiService.getRepository(workspace, repoSlug, accessToken)

      return {
        totalBranches: branches.length,
        openPRs: openPRs.length,
        totalPRs: allPRs.length,
        lastActivity: repository.updated_on
      }
    } catch (error) {
      console.error('Failed to fetch repository stats:', error)
      return {
        totalBranches: 0,
        openPRs: 0,
        totalPRs: 0,
        lastActivity: new Date().toISOString()
      }
    }
  }

  /**
   * Get favorite/starred repositories
   */
  async getFavoriteRepositories(accessToken: string): Promise<BitbucketRepository[]> {
    try {
      // Bitbucket doesn't have a direct "favorites" API, so we use recent + high activity
      const recent = await this.apiService.getUserRecentRepositories(accessToken, 20)
      
      // Sort by activity and filter for repos user has contributed to recently
      return recent
        .filter(repo => new Date(repo.updated_on) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) // Last 30 days
        .slice(0, 10)
    } catch (error) {
      console.error('Failed to fetch favorite repositories:', error)
      return []
    }
  }

  /**
   * Get repository clone URLs
   */
  getRepositoryCloneUrls(repository: BitbucketRepository): {
    ssh?: string
    https?: string
  } {
    const cloneLinks = repository.links?.clone || []
    
    return {
      ssh: cloneLinks.find(link => link.name === 'ssh')?.href,
      https: cloneLinks.find(link => link.name === 'https')?.href
    }
  }

  /**
   * Get repository pull requests
   */
  async getRepositoryPullRequests(
    workspace: string,
    repoSlug: string,
    accessToken: string,
    branchName?: string
  ): Promise<BitbucketPullRequest[]> {
    try {
      const options: any = { 
        pagelen: 50,
        sort: '-updated_on' // Most recently updated first
      }
      
      // Filter by branch if specified
      if (branchName) {
        options.q = `source.branch.name="${branchName}" OR destination.branch.name="${branchName}"`
      }

      const { pullRequests } = await this.apiService.getRepositoryPullRequests(
        workspace,
        repoSlug,
        accessToken,
        options
      )

      return pullRequests
    } catch (error) {
      console.error('Failed to fetch repository pull requests:', error)
      throw new Error('Failed to load pull requests.')
    }
  }

  /**
   * Format repository for display
   */
  formatRepositoryDisplay(repository: BitbucketRepository): {
    title: string
    subtitle: string
    description: string
    badges: string[]
    lastUpdated: string
  } {
    const badges = []
    
    if (repository.is_private) badges.push('Private')
    if (repository.language) badges.push(repository.language)
    if (repository.project) badges.push(`Project: ${repository.project.name}`)

    return {
      title: repository.name,
      subtitle: repository.full_name,
      description: repository.description || 'No description available',
      badges,
      lastUpdated: new Date(repository.updated_on).toLocaleDateString()
    }
  }
}

export const repositoryService = new RepositoryService()

// React hook for repository operations
export const useRepositoryService = () => {
  // const { makeRequest } = useAuthenticatedRequest()

  const getAllRepositories = async () => {
    // Get token from auth store and call service directly
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    return repositoryService.getAllUserRepositories(accessToken)
  }

  const searchRepositories = async (query: string, options?: any) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    return repositoryService.searchRepositories(query, accessToken, options)
  }

  const getRepositoryWithBranches = async (workspace: string, repoSlug: string) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    return repositoryService.getRepositoryWithBranches(workspace, repoSlug, accessToken)
  }

  const getRepositoryPullRequests = async (workspace: string, repoSlug: string, branchName?: string) => {
    const { useAuth } = await import('../../hooks/useAuth')
    const { accessToken } = useAuth.getState()
    if (!accessToken) throw new Error('No access token available')
    return repositoryService.getRepositoryPullRequests(workspace, repoSlug, accessToken, branchName)
  }

  return {
    getAllRepositories,
    searchRepositories,
    getRepositoryWithBranches,
    getRepositoryPullRequests,
    service: repositoryService
  }
}