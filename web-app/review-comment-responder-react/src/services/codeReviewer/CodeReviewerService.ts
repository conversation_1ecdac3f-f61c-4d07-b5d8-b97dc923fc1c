interface ReviewSession {
  session_id: string
  branch_name: string
  pr_url?: string
  status: 'initializing' | 'running' | 'completed' | 'error'
  progress: number
  progress_message?: string
  created_at: string
  completed_at?: string
  results?: StructuredReviewResult
  tutorial?: TutorialData
  error?: string
  worktree_path?: string
  structured_summary?: ExecutiveSummary
}

// Import the centralized interface
import type { EnhancedReviewResults, ReviewFinding } from '../../types/enhanced-review'

// New enhanced structured result from backend
export interface StructuredReviewResult {
  session_id: string
  review_type: string
  timestamp: string
  success: boolean
  
  // Raw content for fallback
  raw_content: string
  
  // Enhanced structured data
  structured_data: {
    executive_summary: ExecutiveSummary
    acceptance_criteria: AcceptanceCriteriaItem[]
    code_quality_findings: CodeQualityFinding[]
    action_items: ActionItems
    questions: string[]
  }
  
  // Enhanced metadata from Claude Code SDK
  metadata: {
    cost_usd: number
    duration_ms: number
    api_duration_ms: number
    num_turns: number
    session_id: string
    is_error: boolean
    success: boolean
    worktree_path?: string
    branch_name?: string
    pr_url?: string
    changed_files: string[]
    diff_summary: string
  }
  
  // Jira integration
  jira_ticket?: {
    ticket_id: string
    summary: string
    status: string
    priority: string
    acceptance_criteria_count: number
    acceptance_criteria: string[]
  }
  
  // Phase 3 tutorial (if included)
  phase3_summary?: TutorialData
}

export interface ExecutiveSummary {
  ac_compliance?: {
    fulfilled: number
    total: number
    percentage: number
  }
  code_quality_score?: number
  critical_issues: number
  warning_issues: number
  has_ac_analysis: boolean
  has_code_analysis: boolean
}

export interface AcceptanceCriteriaItem {
  id: string
  title: string
  status: 'FULFILLED' | 'NOT_FULFILLED' | 'PARTIAL'
  icon: string
}

export interface CodeQualityFinding {
  type: string
  severity: 'critical' | 'high' | 'medium' | 'low'
  description: string
  full_text: string
}

export interface ActionItems {
  critical: string[]
  important: string[]
  suggestions: string[]
}

export interface TutorialData {
  tutorial_id: string
  raw_content: string
  structured_sections: {
    business_context?: string
    architecture_overview?: string
    implementation_guide?: string
    testing?: string
    deployment?: string
  }
  mermaid_diagrams: string[]
  code_examples: Array<{
    language: string
    code: string
  }>
  metadata?: {
    cost_usd: number
    duration_ms: number
    session_id: string
  }
}

// Re-export for backward compatibility
export type { EnhancedReviewResults, ReviewFinding }

interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
  author: string
  created_date: string
  updated_date: string
  comments: number
  state: string
  reviewers: string[]
  jira_ticket?: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  related_prs: number[]
}

export class CodeReviewerService {
  private readonly API_BASE = 'http://localhost:5002/api/code-reviewer'
  private eventSource: EventSource | null = null

  /**
   * Health check for the code reviewer API
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:5002/api/health')
      return response.ok
    } catch (error) {
      console.error('Code Reviewer API health check failed:', error)
      return false
    }
  }

  /**
   * Get assigned pull requests for the current user
   */
  async getAssignedPullRequests(): Promise<{
    success: boolean
    prs: AssignedPR[]
    total: number
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/assigned-prs`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch assigned PRs: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to fetch assigned PRs:', error)
      return {
        success: false,
        prs: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get assigned Jira tickets for the current user
   */
  async getAssignedTickets(): Promise<{
    success: boolean
    tickets: AssignedTicket[]
    total: number
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/assigned-tickets`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch assigned tickets: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to fetch assigned tickets:', error)
      return {
        success: false,
        tickets: [],
        total: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Start an enhanced code review session with structured output
   */
  async startReview(options: {
    branch_name: string
    repository_path: string
    pr_url?: string
    review_mode?: 'quick' | 'comprehensive' | 'ac-only' | 'bug-analysis' | 'security' | 'performance'
    config?: Record<string, unknown>
    jira_ticket?: {
      ticket_id: string
      summary: string
      description?: string
      acceptance_criteria?: string[]
      acceptance_criteria_count?: number
    } | null
  }): Promise<{
    success: boolean
    session?: {
      session_id: string
      branch_name: string
      status: string
      progress: number
      progress_message?: string
      created_at: string
      sdk_enabled?: boolean
      structured_output?: boolean
      include_summary?: boolean
    }
    error?: string
  }> {
    try {
      // Map frontend review modes to backend modes
      const mappedMode = this.mapReviewMode(options.review_mode || 'comprehensive')
      
      const response = await fetch(`${this.API_BASE}/start-structured-review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          branch_name: options.branch_name,
          repository_path: options.repository_path,
          pr_url: options.pr_url,
          review_mode: mappedMode,
          include_summary: true, // Enable Phase 3 by default
          config: options.config,
          jira_ticket: options.jira_ticket
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to start structured review: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to start structured review:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Map frontend review modes to backend review types
   */
  private mapReviewMode(frontendMode: string): string {
    const mapping: Record<string, string> = {
      'quick': 'comprehensive',
      'comprehensive': 'comprehensive_with_ac',
      'ac-only': 'ac_focused',
      'bug-analysis': 'bug_analysis',
      'security': 'bug_analysis',
      'performance': 'bug_analysis'
    }
    return mapping[frontendMode] || 'comprehensive_with_ac'
  }

  /**
   * Get the status of a review session
   */
  async getReviewStatus(sessionId: string): Promise<{
    success: boolean
    session?: ReviewSession
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/review-status/${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 404) {
          return {
            success: false,
            error: 'Review session not found'
          }
        }
        throw new Error(`Failed to get review status: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to get review status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get the results of a completed review session (enhanced structured format)
   */
  async getReviewResults(sessionId: string): Promise<{
    success: boolean
    session_id?: string
    results?: StructuredReviewResult
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/review-results/${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 404) {
          return {
            success: false,
            error: 'Review session not found'
          }
        }
        if (response.status === 400) {
          const errorData = await response.json()
          return {
            success: false,
            error: errorData.error || 'Review not completed yet'
          }
        }
        throw new Error(`Failed to get review results: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to get review results:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate Phase 3 tutorial for a completed review session
   */
  async generateTutorial(sessionId: string): Promise<{
    success: boolean
    tutorial?: TutorialData
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/generate-tutorial/${sessionId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 404) {
          return {
            success: false,
            error: 'Review session not found'
          }
        }
        throw new Error(`Failed to generate tutorial: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to generate tutorial:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Generate acceptance criteria for a Jira ticket using Claude AI
   */
  async generateAcceptanceCriteria(ticket: {
    ticket_id: string
    summary: string
    description: string
  }): Promise<{
    success: boolean
    acceptance_criteria?: string[]
    acceptance_criteria_count?: number
    source?: string
    method?: string
    error?: string
  }> {
    try {
      console.log('🤖 Generating AC for ticket:', ticket.ticket_id)
      
      const response = await fetch('http://localhost:5002/api/jira/generate-acceptance-criteria', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          ticket_key: ticket.ticket_id,
          summary: ticket.summary,
          description: ticket.description
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to generate AC: ${response.status}`)
      }

      const result = await response.json()
      console.log('✅ AC generation result:', result)
      
      return result

    } catch (error) {
      console.error('Failed to generate acceptance criteria:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Check if tutorial is available for a session
   */
  async getTutorialStatus(sessionId: string): Promise<{
    success: boolean
    tutorial_available?: boolean
    tutorial_id?: string
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/tutorial-status/${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 404) {
          return {
            success: false,
            error: 'Review session not found'
          }
        }
        throw new Error(`Failed to get tutorial status: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to get tutorial status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get tutorial data for a session
   */
  async getTutorial(sessionId: string): Promise<{
    success: boolean
    tutorial?: TutorialData
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/tutorial/${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        if (response.status === 404) {
          return {
            success: false,
            error: 'Tutorial not found'
          }
        }
        throw new Error(`Failed to get tutorial: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to get tutorial:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * List all review sessions
   */
  async listReviewSessions(): Promise<{
    success: boolean
    sessions?: ReviewSession[]
    total?: number
    error?: string
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/sessions`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`Failed to list sessions: ${response.status}`)
      }

      return await response.json()

    } catch (error) {
      console.error('Failed to list sessions:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Subscribe to real-time review progress updates
   */
  subscribeToReviewProgress(
    sessionId: string,
    callbacks: {
      onProgress?: (data: { session_id: string; status: string; progress: number; message: string; worktree_path?: string }) => void
      onCompleted?: (data: { session_id: string; status: string; progress: number; message: string; results_available: boolean }) => void
      onError?: (data: { session_id: string; status: string; error: string; message: string }) => void
      onConnectionError?: (error: Error) => void
    }
  ): () => void {
    // Close existing connection if any
    this.closeProgressConnection()

    try {
      // Use Server-Sent Events for progress updates
      this.eventSource = new EventSource(`${this.API_BASE}/progress-stream/${sessionId}`)
      
      this.eventSource.onopen = () => {
        console.log(`📡 Connected to review progress stream for session ${sessionId}`)
      }

      this.eventSource.addEventListener('review_progress', (event) => {
        try {
          const data = JSON.parse(event.data)
          callbacks.onProgress?.(data)
        } catch (error) {
          console.error('Failed to parse progress event:', error)
        }
      })

      this.eventSource.addEventListener('review_completed', (event) => {
        try {
          const data = JSON.parse(event.data)
          callbacks.onCompleted?.(data)
        } catch (error) {
          console.error('Failed to parse completed event:', error)
        }
      })

      this.eventSource.addEventListener('review_error', (event) => {
        try {
          const data = JSON.parse(event.data)
          callbacks.onError?.(data)
        } catch (error) {
          console.error('Failed to parse error event:', error)
        }
      })

      this.eventSource.onerror = (error) => {
        console.error('EventSource error:', error)
        callbacks.onConnectionError?.(new Error('Connection to progress stream failed'))
      }

      // Return cleanup function
      return () => this.closeProgressConnection()

    } catch (error) {
      console.error('Failed to setup progress stream:', error)
      callbacks.onConnectionError?.(error instanceof Error ? error : new Error('Unknown error'))
      return () => {} // Return no-op cleanup function
    }
  }

  /**
   * Close the progress connection
   */
  closeProgressConnection(): void {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
      console.log('📡 Closed review progress stream connection')
    }
  }

  /**
   * Extract Jira ticket ID from branch name
   */
  extractJiraTicketFromBranch(branchName: string): string | null {
    const patterns = [
      /([A-Z]+-\d+)/,           // Standard format: ABC-123
      /([A-Z]+\d+-\d+)/,        // Alternative: ABC123-456
    ]

    for (const pattern of patterns) {
      const match = branchName.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  }

  /**
   * Generate default repository path for RMA mono repo
   */
  getDefaultRepositoryPath(): string {
    return '/Users/<USER>/dev/rma-mono'
  }

  /**
   * Validate review options before starting
   */
  validateReviewOptions(options: {
    branch_name: string
    repository_path: string
    review_mode?: string
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!options.branch_name || options.branch_name.trim().length === 0) {
      errors.push('Branch name is required')
    }

    if (!options.repository_path || options.repository_path.trim().length === 0) {
      errors.push('Repository path is required')
    }

    const validModes = ['quick', 'comprehensive', 'ac-only', 'bug-analysis', 'security', 'performance']
    if (options.review_mode && !validModes.includes(options.review_mode)) {
      errors.push(`Invalid review mode. Must be one of: ${validModes.join(', ')}`)
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * Format structured review results for display
   */
  formatStructuredReviewSummary(results: StructuredReviewResult): {
    overview: string
    keyFindings: string[]
    recommendations: string[]
    acCompliance?: {
      total: number
      completed: number
      percentage: number
    }
    qualityScore?: number
    metadata: {
      cost: number
      duration: string
      turns: number
    }
  } {
    const structuredData = results.structured_data
    const metadata = results.metadata
    
    // Build overview from executive summary
    const executive = structuredData.executive_summary
    const overview = `Review completed with ${executive.critical_issues} critical and ${executive.warning_issues} warning issues found. ` +
                    `${executive.has_ac_analysis ? 'Includes AC compliance analysis. ' : ''}` +
                    `${executive.has_code_analysis ? 'Includes code quality analysis.' : ''}`

    const keyFindings: string[] = []
    
    // Add critical findings
    structuredData.code_quality_findings
      .filter(finding => finding.severity === 'critical')
      .forEach(finding => {
        keyFindings.push(`🚨 ${finding.description}`)
      })

    // Add high severity findings
    structuredData.code_quality_findings
      .filter(finding => finding.severity === 'high')
      .slice(0, 3)
      .forEach(finding => {
        keyFindings.push(`🔴 ${finding.description}`)
      })

    // Build recommendations from action items
    const recommendations: string[] = []
    if (structuredData.action_items.critical.length > 0) {
      recommendations.push(`${structuredData.action_items.critical.length} critical issues require immediate attention`)
    }
    if (structuredData.action_items.important.length > 0) {
      recommendations.push(`${structuredData.action_items.important.length} important improvements recommended`)
    }
    if (metadata.changed_files.length > 0) {
      recommendations.push(`Review covers ${metadata.changed_files.length} changed files`)
    }

    // Enhanced AC Compliance from executive summary
    let acCompliance
    if (executive.ac_compliance) {
      acCompliance = {
        total: executive.ac_compliance.total,
        completed: executive.ac_compliance.fulfilled,
        percentage: executive.ac_compliance.percentage
      }
    }

    return {
      overview,
      keyFindings,
      recommendations,
      acCompliance,
      qualityScore: executive.code_quality_score,
      metadata: {
        cost: metadata.cost_usd,
        duration: `${Math.round(metadata.duration_ms / 1000)}s`,
        turns: metadata.num_turns
      }
    }
  }

  /**
   * Legacy format method for backward compatibility
   */
  formatReviewSummary(results: EnhancedReviewResults): {
    overview: string
    keyFindings: string[]
    recommendations: string[]
    acCompliance?: {
      total: number
      completed: number
      percentage: number
    }
  } {
    const summary = results.summary
    const findings = results.structured_findings
    
    const overview = `Reviewed ${summary.files_changed} files with ${summary.total_findings} findings. ` +
                    `Found ${summary.categories.security_issues || 0} security issues, ${summary.categories.bugs || 0} potential bugs, ` +
                    `and ${summary.categories.suggestions || 0} quality suggestions.`

    const keyFindings: string[] = []
    
    // Add high-severity findings
    Object.values(findings).flat().forEach(finding => {
      if (finding.severity === 'high') {
        keyFindings.push(`🔴 ${finding.text}`)
      }
    })

    // Add medium-severity findings (limit to top 3)
    const mediumFindings = Object.values(findings).flat()
      .filter(finding => finding.severity === 'medium')
      .slice(0, 3)
    
    mediumFindings.forEach(finding => {
      keyFindings.push(`🟡 ${finding.text}`)
    })

    const recommendations = [
      `Review contains ${summary.total_findings} findings`,
      'Address high-severity issues first',
      'Consider automated testing for critical paths'
    ]

    if ((summary.categories.security_issues || 0) > 0) {
      recommendations.push('Security review recommended before deployment')
    }

    // AC Compliance if available
    let acCompliance
    if (results.jira_ticket && results.structured_findings.acceptance_criteria.length > 0) {
      const total = results.jira_ticket.acceptance_criteria_count
      const completed = results.structured_findings.acceptance_criteria.filter(ac => 
        ac.text.includes('✅') || ac.text.includes('completed') || ac.text.includes('implemented')
      ).length
      
      acCompliance = {
        total,
        completed,
        percentage: total > 0 ? Math.round((completed / total) * 100) : 0
      }
    }

    return {
      overview,
      keyFindings,
      recommendations,
      acCompliance
    }
  }
}

export const codeReviewerService = new CodeReviewerService()