/**
 * Multi-Agent Service Error Handling
 * Custom error classes for Multi-Agent Review Service integration
 */

import type { MultiAgentError, AgentType } from '../../types/multi-agent'

/**
 * Base error class for Multi-Agent Service errors
 */
export class MultiAgentServiceError extends Error {
  public readonly errorCode: string
  public readonly statusCode: number
  public readonly reviewId?: string
  public readonly agentType?: AgentType
  public readonly retryable: boolean
  public readonly timestamp: string
  public readonly details?: Record<string, unknown>
  public readonly cause?: Error

  constructor(
    message: string,
    errorCode: string,
    statusCode: number = 500,
    options: {
      reviewId?: string
      agentType?: AgentType
      retryable?: boolean
      details?: Record<string, unknown>
      cause?: Error
    } = {}
  ) {
    super(message)
    this.name = 'MultiAgentServiceError'
    this.errorCode = errorCode
    this.statusCode = statusCode
    this.reviewId = options.reviewId
    this.agentType = options.agentType
    this.retryable = options.retryable ?? false
    this.timestamp = new Date().toISOString()
    this.details = options.details
    
    // Maintain proper stack trace for debugging
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, MultiAgentServiceError)
    }
    
    // Chain the original error if provided
    if (options.cause) {
      this.cause = options.cause
    }
  }

  /**
   * Convert to MultiAgentError interface for API responses
   */
  toApiError(): MultiAgentError {
    return {
      error_code: this.errorCode,
      error_message: this.message,
      error_details: this.details,
      review_id: this.reviewId,
      agent_type: this.agentType,
      timestamp: this.timestamp,
      retry_possible: this.retryable
    }
  }

  /**
   * Get user-friendly error message
   */
  getUserFriendlyMessage(): string {
    switch (this.errorCode) {
      case 'SERVICE_UNAVAILABLE':
        return 'The Multi-Agent review service is currently unavailable. Please try again later.'
      case 'REVIEW_NOT_FOUND':
        return 'The requested review could not be found. It may have been cancelled or expired.'
      case 'INVALID_REQUEST':
        return 'The review request contains invalid data. Please check your configuration.'
      case 'TIMEOUT':
        return 'The review took longer than expected to complete. This may be due to high system load.'
      case 'AGENT_FAILURE':
        return `The ${this.agentType || 'analysis'} agent encountered an error during processing.`
      case 'RESOURCE_EXHAUSTED':
        return 'The system is currently at capacity. Please try again in a few minutes.'
      case 'NETWORK_ERROR':
        return 'Unable to connect to the review service. Please check your network connection.'
      case 'AUTHENTICATION_ERROR':
        return 'Authentication failed. Please refresh the page and try again.'
      case 'RATE_LIMIT_EXCEEDED':
        return 'Too many requests. Please wait a moment before trying again.'
      default:
        return this.message || 'An unexpected error occurred during the review process.'
    }
  }
}

/**
 * Network-related errors (connection, timeout, etc.)
 */
export class MultiAgentNetworkError extends MultiAgentServiceError {
  constructor(message: string, options: {
    url?: string
    method?: string
    timeout?: number
    cause?: Error
  } = {}) {
    super(
      message,
      'NETWORK_ERROR',
      0, // No HTTP status for network errors
      {
        retryable: true,
        details: {
          url: options.url,
          method: options.method,
          timeout: options.timeout
        },
        cause: options.cause
      }
    )
    this.name = 'MultiAgentNetworkError'
  }
}

/**
 * HTTP response errors (4xx, 5xx status codes)
 */
export class MultiAgentHttpError extends MultiAgentServiceError {
  public readonly response?: Response

  constructor(
    message: string,
    statusCode: number,
    response?: Response,
    options: {
      reviewId?: string
      agentType?: AgentType
      details?: Record<string, unknown>
    } = {}
  ) {
    const errorCode = MultiAgentHttpError.getErrorCodeFromStatus(statusCode)
    const retryable = MultiAgentHttpError.isRetryableStatus(statusCode)
    
    super(message, errorCode, statusCode, {
      ...options,
      retryable,
      details: {
        ...options.details,
        statusCode,
        statusText: response?.statusText
      }
    })
    
    this.name = 'MultiAgentHttpError'
    this.response = response
  }

  private static getErrorCodeFromStatus(statusCode: number): string {
    if (statusCode >= 400 && statusCode < 500) {
      switch (statusCode) {
        case 400: return 'INVALID_REQUEST'
        case 401: return 'AUTHENTICATION_ERROR'
        case 403: return 'AUTHORIZATION_ERROR'
        case 404: return 'REVIEW_NOT_FOUND'
        case 409: return 'CONFLICT'
        case 429: return 'RATE_LIMIT_EXCEEDED'
        default: return 'CLIENT_ERROR'
      }
    } else if (statusCode >= 500) {
      switch (statusCode) {
        case 502: return 'BAD_GATEWAY'
        case 503: return 'SERVICE_UNAVAILABLE'
        case 504: return 'TIMEOUT'
        default: return 'SERVER_ERROR'
      }
    }
    return 'HTTP_ERROR'
  }

  private static isRetryableStatus(statusCode: number): boolean {
    // Retry on 5xx errors and specific 4xx errors
    return statusCode >= 500 || [408, 429].includes(statusCode)
  }
}

/**
 * Agent-specific errors
 */
export class MultiAgentAgentError extends MultiAgentServiceError {
  constructor(
    agentType: AgentType,
    message: string,
    options: {
      reviewId?: string
      retryable?: boolean
      details?: Record<string, unknown>
      cause?: Error
    } = {}
  ) {
    super(
      message,
      'AGENT_FAILURE',
      500,
      {
        ...options,
        agentType,
        retryable: options.retryable ?? true
      }
    )
    this.name = 'MultiAgentAgentError'
  }
}

/**
 * Timeout errors
 */
export class MultiAgentTimeoutError extends MultiAgentServiceError {
  constructor(
    timeoutMs: number,
    operation: string,
    options: {
      reviewId?: string
      agentType?: AgentType
    } = {}
  ) {
    super(
      `Operation '${operation}' timed out after ${timeoutMs}ms`,
      'TIMEOUT',
      408,
      {
        ...options,
        retryable: true,
        details: {
          timeoutMs,
          operation
        }
      }
    )
    this.name = 'MultiAgentTimeoutError'
  }
}

/**
 * Resource exhaustion errors
 */
export class MultiAgentResourceError extends MultiAgentServiceError {
  constructor(
    resource: string,
    message: string,
    options: {
      reviewId?: string
      details?: Record<string, unknown>
    } = {}
  ) {
    super(
      message,
      'RESOURCE_EXHAUSTED',
      503,
      {
        ...options,
        retryable: true,
        details: {
          ...options.details,
          resource
        }
      }
    )
    this.name = 'MultiAgentResourceError'
  }
}

/**
 * Validation errors
 */
export class MultiAgentValidationError extends MultiAgentServiceError {
  constructor(
    field: string,
    value: unknown,
    expectedType: string,
    options: {
      details?: Record<string, unknown>
    } = {}
  ) {
    super(
      `Invalid value for field '${field}': expected ${expectedType}, got ${typeof value}`,
      'INVALID_REQUEST',
      400,
      {
        retryable: false,
        details: {
          ...options.details,
          field,
          value,
          expectedType
        }
      }
    )
    this.name = 'MultiAgentValidationError'
  }
}

/**
 * Error factory for creating errors from HTTP responses
 */
export class MultiAgentErrorFactory {
  /**
   * Create error from fetch response
   */
  static async fromResponse(
    response: Response,
    operation: string,
    options: {
      reviewId?: string
      agentType?: AgentType
    } = {}
  ): Promise<MultiAgentServiceError> {
    let errorMessage = `${operation} failed with status ${response.status}`
    let details: Record<string, unknown> = {}

    try {
      const responseText = await response.text()
      if (responseText) {
        try {
          const errorData = JSON.parse(responseText)
          errorMessage = errorData.message || errorData.error || errorMessage
          details = errorData.details || {}
        } catch {
          // If not JSON, use the text as details
          details.responseText = responseText
        }
      }
    } catch {
      // Ignore errors when reading response body
    }

    return new MultiAgentHttpError(errorMessage, response.status, response, {
      ...options,
      details
    })
  }

  /**
   * Create error from network/fetch error
   */
  static fromNetworkError(
    error: Error,
    operation: string,
    options: {
      url?: string
      method?: string
      timeout?: number
    } = {}
  ): MultiAgentNetworkError {
    let message = `${operation} failed: ${error.message}`
    
    // Handle specific error types
    if (error.name === 'AbortError') {
      return new MultiAgentTimeoutError(
        options.timeout || 10000,
        operation
      )
    }
    
    if (error.message.includes('fetch')) {
      message = `Network error during ${operation}: ${error.message}`
    }

    return new MultiAgentNetworkError(message, {
      ...options,
      cause: error
    })
  }

  /**
   * Create timeout error with AbortSignal
   */
  static createTimeoutError(
    timeoutMs: number,
    operation: string,
    options: {
      reviewId?: string
      agentType?: AgentType
    } = {}
  ): MultiAgentTimeoutError {
    return new MultiAgentTimeoutError(timeoutMs, operation, options)
  }
}

/**
 * Type guard to check if error is a Multi-Agent service error
 */
export function isMultiAgentServiceError(error: unknown): error is MultiAgentServiceError {
  return error instanceof MultiAgentServiceError
}

/**
 * Type guard to check if error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  return isMultiAgentServiceError(error) && error.retryable
}

/**
 * Get user-friendly error message from any error
 */
export function getUserFriendlyErrorMessage(error: unknown): string {
  if (isMultiAgentServiceError(error)) {
    return error.getUserFriendlyMessage()
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}