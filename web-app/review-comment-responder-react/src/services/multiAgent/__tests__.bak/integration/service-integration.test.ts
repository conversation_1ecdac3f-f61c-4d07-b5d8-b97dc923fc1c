/**
 * Integration Tests for Multi-Agent Service
 * Testing end-to-end service interactions and workflows
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MultiAgentReviewService } from '../../MultiAgentReviewService'
import { TestDataFactory, FIXTURES } from '../../../../test-utils/test-fixtures'
import { MockWebSocketFactory, setupWebSocketMock } from '../../../../test-utils/mock-websocket'
import type { 
  MultiAgentReviewRequest,
  MultiAgentReviewResponse,
  ReviewStatusResponse,
  EnhancedReviewResults
} from '../../../../types/multi-agent'

// Mock fetch for HTTP requests
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('Multi-Agent Service Integration Tests', () => {
  let service: MultiAgentReviewService
  let cleanupWebSocket: () => void

  beforeEach(() => {
    // Setup service
    service = new MultiAgentReviewService({
      baseUrl: 'http://localhost:5000/api/v1',
      timeout: 10000
    })

    // Setup WebSocket mock
    cleanupWebSocket = setupWebSocketMock()

    // Reset mocks
    mockFetch.mockClear()
  })

  afterEach(() => {
    cleanupWebSocket()
    MockWebSocketFactory.clear()
    vi.clearAllMocks()
  })

  describe('complete review workflow integration', () => {
    it('should successfully complete full multi-agent review workflow', async () => {
      const scenario = FIXTURES.scenarios.successful
      
      // Mock HTTP responses in sequence
      mockFetch
        // Health check
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { status: 'healthy' } })
        })
        // Start review
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.startResponse })
        })
        // Status check
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.runningStatus })
        })
        // Final status
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.completedStatus })
        })
        // Results
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.results })
        })

      // Step 1: Health check
      const isHealthy = await service.healthCheck()
      expect(isHealthy).toBe(true)

      // Step 2: Start review
      const startResponse = await service.startParallelReview(scenario.request)
      expect(startResponse.success).toBe(true)
      expect(startResponse.data?.review_id).toBe(scenario.reviewId)
      expect(startResponse.data?.session_id).toBe(scenario.sessionId)

      // Step 3: Monitor status
      const statusResponse = await service.getReviewStatus(scenario.reviewId)
      expect(statusResponse.success).toBe(true)
      expect(statusResponse.data?.status).toBe('running')
      expect(statusResponse.data?.progress).toBeGreaterThan(0)

      // Step 4: Check final status
      const finalStatusResponse = await service.getReviewStatus(scenario.reviewId)
      expect(finalStatusResponse.success).toBe(true)
      expect(finalStatusResponse.data?.status).toBe('completed')
      expect(finalStatusResponse.data?.progress).toBe(100)

      // Step 5: Get results
      const resultsResponse = await service.getReviewResults(scenario.reviewId)
      expect(resultsResponse.success).toBe(true)
      expect(resultsResponse.data?.review_id).toBe(scenario.reviewId)
      expect(resultsResponse.data?.overall_results).toBeDefined()
      expect(resultsResponse.data?.agent_results).toBeDefined()

      // Verify API call sequence
      expect(mockFetch).toHaveBeenCalledTimes(5)
      expect(mockFetch).toHaveBeenNthCalledWith(1, 
        'http://localhost:5000/api/v1/health',
        expect.objectContaining({ method: 'GET' })
      )
      expect(mockFetch).toHaveBeenNthCalledWith(2,
        'http://localhost:5000/api/v1/review/start',
        expect.objectContaining({ 
          method: 'POST',
          body: JSON.stringify(scenario.request)
        })
      )
    })

    it('should handle review failure workflow correctly', async () => {
      const scenario = FIXTURES.scenarios.failed
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.startResponse })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: scenario.failedStatus })
        })

      // Start review
      const startResponse = await service.startParallelReview(scenario.request)
      expect(startResponse.success).toBe(true)

      // Check failed status
      const statusResponse = await service.getReviewStatus(scenario.reviewId)
      expect(statusResponse.success).toBe(true)
      expect(statusResponse.data?.status).toBe('failed')
      expect(statusResponse.data?.failed_agents).toContain('security_analysis')

      // Attempting to get results should indicate failure
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 422,
        text: async () => 'Review failed - no results available'
      })

      await expect(service.getReviewResults(scenario.reviewId))
        .rejects.toThrow()
    })
  })

  describe('service health and availability', () => {
    it('should detect healthy service', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          data: { status: 'healthy', version: '1.0.0' } 
        })
      })

      const isHealthy = await service.healthCheck()
      expect(isHealthy).toBe(true)
    })

    it('should detect unhealthy service', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 503,
        json: async () => ({ 
          success: false, 
          data: { status: 'unhealthy', error: 'Database connection failed' } 
        })
      })

      const isHealthy = await service.healthCheck()
      expect(isHealthy).toBe(false)
    })

    it('should handle network failures gracefully', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const isHealthy = await service.healthCheck()
      expect(isHealthy).toBe(false)
    })
  })

  describe('error handling and resilience', () => {
    it('should retry on transient failures', async () => {
      const request = TestDataFactory.createMultiAgentReviewRequest()
      
      // First call fails with 503, second succeeds
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 503,
          text: async () => 'Service temporarily unavailable'
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createMultiAgentReviewResponse() 
          })
        })

      const response = await service.startParallelReview(request)
      expect(response.success).toBe(true)
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it('should not retry on non-retryable errors', async () => {
      const request = TestDataFactory.createMultiAgentReviewRequest()
      
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        text: async () => 'Bad request - invalid branch name'
      })

      await expect(service.startParallelReview(request)).rejects.toThrow()
      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    it('should handle timeout scenarios', async () => {
      const request = TestDataFactory.createMultiAgentReviewRequest()
      
      mockFetch.mockImplementation(() => 
        new Promise((resolve) => setTimeout(resolve, 15000)) // Longer than timeout
      )

      await expect(service.startParallelReview(request)).rejects.toThrow('timeout')
    })
  })

  describe('request validation and processing', () => {
    it('should validate request parameters before sending', async () => {
      const invalidRequest = {
        branch_name: '', // Invalid
        repository_path: '/test/repo',
        pr_url: 'https://example.com/pr/1',
        review_mode: 'full'
      } as MultiAgentReviewRequest

      await expect(service.startParallelReview(invalidRequest))
        .rejects.toThrow('Invalid value for field \'branch_name\'')
      
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('should handle valid request with all optional parameters', async () => {
      const fullRequest = TestDataFactory.createMultiAgentReviewRequest({
        agent_config: {
          enabled_agents: ['acceptance_criteria', 'bug_detection', 'security_analysis'],
          timeout_seconds: 600,
          priority: 'high',
          concurrent_agents: 5
        },
        context_config: {
          include_git_history: true,
          include_related_files: true,
          max_context_size: 100000
        },
        jira_ticket: {
          ticket_id: 'PROJ-123',
          summary: 'Test ticket',
          description: 'Test description',
          acceptance_criteria: ['AC 1', 'AC 2']
        }
      })

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          data: TestDataFactory.createMultiAgentReviewResponse() 
        })
      })

      const response = await service.startParallelReview(fullRequest)
      expect(response.success).toBe(true)

      // Verify request body contains all parameters
      const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body)
      expect(requestBody.agent_config.enabled_agents).toHaveLength(3)
      expect(requestBody.context_config.max_context_size).toBe(100000)
      expect(requestBody.jira_ticket.ticket_id).toBe('PROJ-123')
    })
  })

  describe('concurrent review handling', () => {
    it('should handle multiple concurrent reviews', async () => {
      const requests = [
        TestDataFactory.createMultiAgentReviewRequest({ branch_name: 'feature/branch-1' }),
        TestDataFactory.createMultiAgentReviewRequest({ branch_name: 'feature/branch-2' }),
        TestDataFactory.createMultiAgentReviewRequest({ branch_name: 'feature/branch-3' })
      ]

      // Mock responses for each request
      requests.forEach((_, index) => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createMultiAgentReviewResponse({
              review_id: `review-${index + 1}`
            })
          })
        })
      })

      // Start all reviews concurrently
      const promises = requests.map(request => service.startParallelReview(request))
      const responses = await Promise.all(promises)

      // Verify all succeeded
      responses.forEach((response, index) => {
        expect(response.success).toBe(true)
        expect(response.data?.review_id).toBe(`review-${index + 1}`)
      })

      expect(mockFetch).toHaveBeenCalledTimes(3)
    })

    it('should handle partial failures in concurrent reviews', async () => {
      const requests = [
        TestDataFactory.createMultiAgentReviewRequest({ branch_name: 'feature/branch-1' }),
        TestDataFactory.createMultiAgentReviewRequest({ branch_name: 'feature/branch-2' })
      ]

      // First succeeds, second fails
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createMultiAgentReviewResponse({
              review_id: 'review-success'
            })
          })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 503,
          text: async () => 'Service overloaded'
        })

      const promises = requests.map(request => service.startParallelReview(request))
      const results = await Promise.allSettled(promises)

      expect(results[0].status).toBe('fulfilled')
      expect(results[1].status).toBe('rejected')
      
      if (results[0].status === 'fulfilled') {
        expect(results[0].value.success).toBe(true)
      }
    })
  })

  describe('data consistency and validation', () => {
    it('should maintain data consistency across service calls', async () => {
      const reviewId = 'consistency-test-123'
      const sessionId = 'session-consistency-123'

      // Mock consistent responses
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createMultiAgentReviewResponse({
              review_id: reviewId,
              session_id: sessionId
            })
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createReviewStatusResponse({
              review_id: reviewId,
              session_id: sessionId
            })
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createEnhancedReviewResults({
              review_id: reviewId,
              session_id: sessionId
            })
          })
        })

      // Start review
      const request = TestDataFactory.createMultiAgentReviewRequest()
      const startResponse = await service.startParallelReview(request)
      
      // Get status
      const statusResponse = await service.getReviewStatus(reviewId)
      
      // Get results
      const resultsResponse = await service.getReviewResults(reviewId)

      // Verify consistency
      expect(startResponse.data?.review_id).toBe(reviewId)
      expect(statusResponse.data?.review_id).toBe(reviewId)
      expect(resultsResponse.data?.review_id).toBe(reviewId)
      
      expect(startResponse.data?.session_id).toBe(sessionId)
      expect(statusResponse.data?.session_id).toBe(sessionId)
      expect(resultsResponse.data?.session_id).toBe(sessionId)
    })

    it('should validate response data structure', async () => {
      const request = TestDataFactory.createMultiAgentReviewRequest()
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          data: {
            review_id: 'test-123',
            session_id: 'session-123',
            status: 'started',
            initial_agent_statuses: TestDataFactory.createAgentStatusSet(),
            estimated_completion_time: 180,
            created_at: new Date().toISOString()
          }
        })
      })

      const response = await service.startParallelReview(request)
      
      expect(response.success).toBe(true)
      expect(response.data).toMatchObject({
        review_id: expect.any(String),
        session_id: expect.any(String),
        status: expect.stringMatching(/^(started|running|completed|failed)$/),
        initial_agent_statuses: expect.any(Object),
        estimated_completion_time: expect.any(Number),
        created_at: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
      })
    })
  })

  describe('performance and load testing', () => {
    it('should handle large request payloads', async () => {
      const largeRequest = TestDataFactory.createMultiAgentReviewRequest({
        context_config: {
          include_git_history: true,
          include_related_files: true,
          max_context_size: 500000 // Large context
        },
        jira_ticket: {
          ticket_id: 'LARGE-123',
          summary: 'Large ticket with extensive description',
          description: 'A'.repeat(10000), // 10KB description
          acceptance_criteria: Array.from({ length: 50 }, (_, i) => `Acceptance criteria ${i + 1}`)
        }
      })

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ 
          success: true, 
          data: TestDataFactory.createMultiAgentReviewResponse()
        })
      })

      const response = await service.startParallelReview(largeRequest)
      expect(response.success).toBe(true)

      // Verify large payload was sent
      const sentPayload = mockFetch.mock.calls[0][1].body
      expect(sentPayload.length).toBeGreaterThan(10000)
    })

    it('should handle rapid sequential requests', async () => {
      const requests = Array.from({ length: 10 }, (_, i) => 
        TestDataFactory.createMultiAgentReviewRequest({
          branch_name: `feature/rapid-${i}`
        })
      )

      // Mock all responses
      requests.forEach((_, index) => {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: async () => ({ 
            success: true, 
            data: TestDataFactory.createMultiAgentReviewResponse({
              review_id: `rapid-${index}`
            })
          })
        })
      })

      const startTime = Date.now()
      
      // Send requests sequentially but quickly
      const responses = []
      for (const request of requests) {
        responses.push(await service.startParallelReview(request))
      }

      const endTime = Date.now()
      const totalTime = endTime - startTime

      // Verify all succeeded
      responses.forEach((response, index) => {
        expect(response.success).toBe(true)
        expect(response.data?.review_id).toBe(`rapid-${index}`)
      })

      // Should complete within reasonable time (allowing for mock delays)
      expect(totalTime).toBeLessThan(5000) // 5 seconds max
    })
  })
})