/**
 * Unit Tests for MultiAgentReviewService
 */

import { describe, it, expect, beforeEach, afterEach, vi, type MockedFunction } from 'vitest'
import { MultiAgentReviewService } from '../MultiAgentReviewService'
import { MultiAgentTimeoutError, MultiAgentHttpError } from '../MultiAgentServiceError'
import type { MultiAgentReviewRequest, MultiAgentReviewResponse } from '../../../types/multi-agent'

// Mock fetch globally
const mockFetch = vi.fn() as MockedFunction<typeof fetch>
global.fetch = mockFetch

describe('MultiAgentReviewService', () => {
  let service: MultiAgentReviewService
  let mockResponse: Partial<Response>

  beforeEach(() => {
    service = new MultiAgentReviewService({
      baseUrl: 'http://localhost:5000/api/v1',
      timeout: 5000,
      retryConfig: {
        maxRetries: 2,
        retryDelay: 100,
        exponentialBackoff: false
      }
    })

    mockResponse = {
      ok: true,
      status: 200,
      statusText: 'OK',
      json: vi.fn()
    }

    mockFetch.mockClear()
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('healthCheck', () => {
    it('should return true when service is healthy', async () => {
      const healthResponse = {
        success: true,
        data: { status: 'healthy' },
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(healthResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.healthCheck()

      expect(result).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:5000/api/v1/health',
        expect.objectContaining({
          method: 'GET',
          signal: expect.any(AbortSignal),
          credentials: 'include'
        })
      )
    })

    it('should return false when service is unhealthy', async () => {
      const healthResponse = {
        success: false,
        data: { status: 'unhealthy' },
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(healthResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.healthCheck()

      expect(result).toBe(false)
    })

    it('should return false on network error', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      const result = await service.healthCheck()

      expect(result).toBe(false)
    })

    it('should return false on timeout', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((resolve) => setTimeout(resolve, 10000))
      )

      const result = await service.healthCheck()

      expect(result).toBe(false)
    })
  })

  describe('startParallelReview', () => {
    const validRequest: MultiAgentReviewRequest = {
      branch_name: 'feature/test-branch',
      repository_path: '/test/repo',
      pr_url: 'https://bitbucket.org/test/repo/pull-requests/123',
      review_mode: 'parallel'
    }

    it('should start parallel review successfully', async () => {
      const expectedResponse: MultiAgentReviewResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'started',
        initial_agent_statuses: {
          bug_detection: {
            agent_type: 'bug_detection',
            status: 'pending',
            progress: 0
          }
        },
        estimated_completion_time: 300,
        websocket_session_id: 'ws-session-789',
        created_at: new Date().toISOString()
      }

      const serviceResponse = {
        success: true,
        data: expectedResponse,
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(serviceResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.startParallelReview(validRequest)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(expectedResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:5000/api/v1/review/start-parallel',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(validRequest),
          signal: expect.any(AbortSignal),
          credentials: 'include'
        })
      )
    })

    it('should validate request and throw validation error', async () => {
      const invalidRequest = {
        branch_name: '', // Invalid: empty string
        repository_path: '/test/repo',
        pr_url: 'https://bitbucket.org/test/repo/pull-requests/123',
        review_mode: 'parallel'
      } as MultiAgentReviewRequest

      await expect(service.startParallelReview(invalidRequest))
        .rejects.toThrow('Invalid value for field \'branch_name\'')
    })

    it('should handle HTTP errors', async () => {
      const errorResponse = {
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        text: vi.fn().mockResolvedValue(
          JSON.stringify({ 
            message: 'Invalid branch name',
            details: { field: 'branch_name' }
          })
        )
      }
      mockFetch.mockResolvedValue(errorResponse as unknown as Response)

      await expect(service.startParallelReview(validRequest))
        .rejects.toThrow()
    })

    it('should retry on retryable errors', async () => {
      // First call fails with 503
      const failedResponse = {
        ...mockResponse,
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        text: vi.fn().mockResolvedValue('Service temporarily unavailable')
      }

      // Second call succeeds
      const successResponse = {
        ...mockResponse,
        ok: true,
        status: 200,
        json: vi.fn().mockResolvedValue({
          success: true,
          data: { review_id: 'retry-success' },
          timestamp: new Date().toISOString()
        })
      }

      mockFetch
        .mockResolvedValueOnce(failedResponse as Response)
        .mockResolvedValueOnce(successResponse as Response)

      const result = await service.startParallelReview(validRequest)

      expect(result.success).toBe(true)
      expect(result.data?.review_id).toBe('retry-success')
      expect(mockFetch).toHaveBeenCalledTimes(2)
    })

    it('should not retry on non-retryable errors', async () => {
      const badRequestResponse = {
        ok: false,
        status: 400, // Bad Request - not retryable
        statusText: 'Bad Request',
        text: vi.fn().mockResolvedValue('Bad Request')
      }
      mockFetch.mockResolvedValue(badRequestResponse as unknown as Response)

      await expect(service.startParallelReview(validRequest))
        .rejects.toThrow(MultiAgentHttpError)

      expect(mockFetch).toHaveBeenCalledTimes(1)
    })

    it('should handle timeout', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((resolve) => setTimeout(resolve, 10000))
      )

      await expect(service.startParallelReview(validRequest))
        .rejects.toThrow(MultiAgentTimeoutError)
    })
  })

  describe('getReviewStatus', () => {
    it('should get review status successfully', async () => {
      const statusResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'running' as const,
        progress: 45,
        agent_statuses: {},
        active_agents: ['bug_detection'],
        completed_agents: ['security_analysis'],
        failed_agents: [],
        started_at: new Date().toISOString()
      }

      const serviceResponse = {
        success: true,
        data: statusResponse,
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(serviceResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.getReviewStatus('review-123')

      expect(result.success).toBe(true)
      expect(result.data).toEqual(statusResponse)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:5000/api/v1/review/review-123/status',
        expect.objectContaining({
          method: 'GET'
        })
      )
    })

    it('should validate reviewId parameter', async () => {
      await expect(service.getReviewStatus(''))
        .rejects.toThrow('Invalid value for field \'reviewId\'')

      await expect(service.getReviewStatus(null as any))
        .rejects.toThrow('Invalid value for field \'reviewId\'')
    })

    it('should handle 404 error for non-existent review', async () => {
      const notFoundResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        text: vi.fn().mockResolvedValue('Not Found')
      }
      mockFetch.mockResolvedValue(notFoundResponse as unknown as Response)

      await expect(service.getReviewStatus('non-existent'))
        .rejects.toThrow(MultiAgentHttpError)
    })
  })

  describe('getReviewResults', () => {
    it('should get review results successfully', async () => {
      const resultsResponse = {
        review_id: 'review-123',
        session_id: 'session-456',
        status: 'completed' as const,
        overall_results: {
          summary: 'Review completed successfully',
          priority_findings: [],
          execution_metrics: {
            total_execution_time: 120,
            parallel_efficiency: 0.85,
            agent_performance: {}
          }
        },
        agent_results: {},
        reports: {
          markdown: '# Review Report\n\nCompleted successfully',
          json: '{}'
        },
        context_metadata: {
          files_analyzed: ['src/test.ts'],
          context_size: 1024,
          git_history_included: true,
          related_files_included: false
        },
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString()
      }

      const serviceResponse = {
        success: true,
        data: resultsResponse,
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(serviceResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.getReviewResults('review-123')

      expect(result.success).toBe(true)
      expect(result.data).toEqual(resultsResponse)
    })
  })

  describe('cancelReview', () => {
    it('should cancel review successfully', async () => {
      const cancelResponse = {
        cancelled: true
      }

      const serviceResponse = {
        success: true,
        data: cancelResponse,
        timestamp: new Date().toISOString()
      }

      mockResponse.json = vi.fn().mockResolvedValue(serviceResponse)
      mockFetch.mockResolvedValue(mockResponse as Response)

      const result = await service.cancelReview('review-123')

      expect(result.success).toBe(true)
      expect(result.data?.cancelled).toBe(true)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:5000/api/v1/review/review-123/cancel',
        expect.objectContaining({
          method: 'POST'
        })
      )
    })
  })

  describe('utility methods', () => {
    it('should extract Jira ticket from branch name', () => {
      expect(service.extractJiraTicketFromBranch('feature/ABC-123-description'))
        .toBe('ABC-123')
      
      expect(service.extractJiraTicketFromBranch('bugfix/XYZ789-456'))
        .toBe('XYZ789-456')
      
      expect(service.extractJiraTicketFromBranch('feature/no-ticket'))
        .toBeNull()
    })

    it('should provide default repository path', () => {
      expect(service.getDefaultRepositoryPath())
        .toBe('/Users/<USER>/dev/rma-mono')
    })

    it('should create review request with defaults', () => {
      const request = service.createReviewRequest({
        branchName: 'feature/test',
        prUrl: 'https://example.com/pr/1'
      })

      expect(request).toEqual({
        branch_name: 'feature/test',
        repository_path: '/Users/<USER>/dev/rma-mono',
        pr_url: 'https://example.com/pr/1',
        review_mode: 'parallel',
        agent_config: {
          enabled_agents: [
            'acceptance_criteria',
            'bug_detection',
            'security_analysis',
            'logic_analysis',
            'quality_analysis',
            'architecture_analysis',
            'summary'
          ],
          timeout_seconds: 300,
          priority: 'normal',
          concurrent_agents: 7
        },
        context_config: {
          include_git_history: true,
          include_related_files: true,
          max_context_size: 50000
        }
      })
    })
  })

  describe('error handling', () => {
    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))

      await expect(service.healthCheck())
        .resolves.toBe(false) // healthCheck catches and returns false

      // But other methods should throw
      await expect(service.getReviewStatus('test'))
        .rejects.toThrow('Network error')
    })

    it('should handle JSON parse errors', async () => {
      mockResponse.json = vi.fn().mockRejectedValue(new Error('Invalid JSON'))
      mockFetch.mockResolvedValue(mockResponse as Response)

      await expect(service.getReviewStatus('test'))
        .rejects.toThrow()
    })
  })

  describe('configuration', () => {
    it('should use default configuration', () => {
      const defaultService = new MultiAgentReviewService()
      expect(defaultService).toBeDefined()

      // Access private properties through type assertion for testing
      const config = (defaultService as any)
      expect(config.API_BASE).toBe('http://localhost:5000/api/v1')
      expect(config.timeout).toBe(10000)
    })

    it('should use custom configuration', () => {
      const customService = new MultiAgentReviewService({
        baseUrl: 'http://custom:8080/api/v1',
        timeout: 15000,
        retryConfig: {
          maxRetries: 5,
          retryDelay: 2000,
          exponentialBackoff: true
        }
      })

      const config = (customService as any)
      expect(config.API_BASE).toBe('http://custom:8080/api/v1')
      expect(config.timeout).toBe(15000)
      expect(config.retryConfig.maxRetries).toBe(5)
    })
  })
})