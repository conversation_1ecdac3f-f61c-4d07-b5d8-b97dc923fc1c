/**
 * Multi-Agent Review Service
 * Central interface for Multi-Agent Code Reviewer Service on Port 5000
 */

import type {
  MultiAgentReviewRequest,
  MultiAgentReviewResponse,
  ReviewStatusResponse,
  EnhancedReviewResults,
  ServiceHealthResponse,
  MultiAgentServiceResponse,
  AgentType,
  ReviewMode
} from '../../types/multi-agent'

import {
  MultiAgentServiceError,
  MultiAgentNetworkError,
  MultiAgentTimeoutError,
  MultiAgentErrorFactory,
  MultiAgentValidationError
} from './MultiAgentServiceError'

/**
 * Configuration for MultiAgentReviewService
 */
export interface MultiAgentServiceConfig {
  baseUrl?: string
  timeout?: number
  retryConfig?: {
    maxRetries: number
    retryDelay: number
    exponentialBackoff: boolean
  }
}

/**
 * Multi-Agent Review Service Class
 * Implements all REST API calls to the Multi-Agent Service
 */
export class MultiAgentReviewService {
  private readonly API_BASE: string
  private readonly timeout: number
  private readonly retryConfig: {
    maxRetries: number
    retryDelay: number
    exponentialBackoff: boolean
  }

  constructor(config: MultiAgentServiceConfig = {}) {
    this.API_BASE = config.baseUrl || 'http://localhost:8000/api/v1'
    this.timeout = config.timeout || 10000 // 10 seconds default
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
      ...config.retryConfig
    }
  }

  /**
   * Health check for the Multi-Agent service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/health', {
        method: 'GET',
        timeout: 5000 // Shorter timeout for health checks
      })
      
      return response.success && (response.data as any)?.status === 'healthy'
    } catch (error) {
      console.error('Multi-Agent service health check failed:', error)
      return false
    }
  }

  /**
   * Get detailed health information
   */
  async getHealthStatus(): Promise<MultiAgentServiceResponse<ServiceHealthResponse>> {
    return this.makeRequest('/health', {
      method: 'GET',
      timeout: 5000
    })
  }

  /**
   * Start a parallel multi-agent review
   */
  async startParallelReview(
    request: MultiAgentReviewRequest
  ): Promise<MultiAgentServiceResponse<MultiAgentReviewResponse>> {
    // Validate request
    this.validateReviewRequest(request)

    return this.makeRequest('/review/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(request)
    })
  }

  /**
   * Get the status of a running review
   */
  async getReviewStatus(reviewId: string): Promise<MultiAgentServiceResponse<ReviewStatusResponse>> {
    if (!reviewId || typeof reviewId !== 'string') {
      throw new MultiAgentValidationError('reviewId', reviewId, 'non-empty string')
    }

    return this.makeRequest(`/review/${reviewId}/status`, {
      method: 'GET'
    })
  }

  /**
   * Get the results of a completed review
   */
  async getReviewResults(reviewId: string): Promise<MultiAgentServiceResponse<EnhancedReviewResults>> {
    if (!reviewId || typeof reviewId !== 'string') {
      throw new MultiAgentValidationError('reviewId', reviewId, 'non-empty string')
    }

    return this.makeRequest(`/review/${reviewId}/results`, {
      method: 'GET'
    })
  }

  /**
   * Cancel a running review
   */
  async cancelReview(reviewId: string): Promise<MultiAgentServiceResponse<{ cancelled: boolean }>> {
    if (!reviewId || typeof reviewId !== 'string') {
      throw new MultiAgentValidationError('reviewId', reviewId, 'non-empty string')
    }

    return this.makeRequest(`/review/${reviewId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Get list of available agents
   */
  async getAvailableAgents(): Promise<MultiAgentServiceResponse<{
    agents: Array<{
      type: AgentType
      name: string
      description: string
      available: boolean
      version: string
    }>
  }>> {
    return this.makeRequest('/agents', {
      method: 'GET'
    })
  }

  /**
   * Get agent-specific configuration
   */
  async getAgentConfig(agentType: AgentType): Promise<MultiAgentServiceResponse<{
    agent_type: AgentType
    config: Record<string, unknown>
    capabilities: string[]
  }>> {
    return this.makeRequest(`/agents/${agentType}/config`, {
      method: 'GET'
    })
  }

  /**
   * List active reviews
   */
  async listActiveReviews(): Promise<MultiAgentServiceResponse<{
    reviews: Array<{
      review_id: string
      status: string
      started_at: string
      progress: number
    }>
    total: number
  }>> {
    return this.makeRequest('/reviews', {
      method: 'GET'
    })
  }

  /**
   * Generic HTTP request handler with error handling and retries
   */
  private async makeRequest<T = unknown>(
    endpoint: string,
    options: {
      method: string
      headers?: Record<string, string>
      body?: string
      timeout?: number
    }
  ): Promise<MultiAgentServiceResponse<T>> {
    const url = `${this.API_BASE}${endpoint}`
    const timeout = options.timeout || this.timeout

    // Create AbortController for timeout
    const abortController = new AbortController()
    const timeoutId = setTimeout(() => {
      abortController.abort()
    }, timeout)

    let lastError: Error | undefined
    
    // Retry logic
    for (let attempt = 1; attempt <= this.retryConfig.maxRetries; attempt++) {
      try {
        const response = await fetch(url, {
          method: options.method,
          headers: {
            'Content-Type': 'application/json',
            ...options.headers
          },
          body: options.body,
          signal: abortController.signal,
          credentials: 'include'
        })

        clearTimeout(timeoutId)

        // Handle HTTP errors
        if (!response.ok) {
          const error = await MultiAgentErrorFactory.fromResponse(
            response, 
            `${options.method} ${endpoint}`
          )
          
          // Don't retry client errors (4xx) except for specific cases
          if (response.status >= 400 && response.status < 500 && !error.retryable) {
            throw error
          }
          
          lastError = error
          
          // If not the last attempt and error is retryable, continue to retry
          if (attempt < this.retryConfig.maxRetries && error.retryable) {
            await this.delay(this.calculateRetryDelay(attempt))
            continue
          }
          
          throw error
        }

        // Parse successful response
        const responseData = await response.json()
        
        return {
          success: true,
          data: responseData,
          timestamp: new Date().toISOString()
        }

      } catch (error) {
        clearTimeout(timeoutId)
        
        // Handle network errors
        if (error instanceof Error && error.name === 'AbortError') {
          lastError = new MultiAgentTimeoutError(timeout, `${options.method} ${endpoint}`)
        } else if (error instanceof MultiAgentServiceError) {
          lastError = error
        } else if (error instanceof Error) {
          lastError = MultiAgentErrorFactory.fromNetworkError(
            error,
            `${options.method} ${endpoint}`,
            { url, method: options.method, timeout }
          )
        } else {
          lastError = new MultiAgentNetworkError(
            `Unknown error during ${options.method} ${endpoint}`,
            { url, method: options.method }
          )
        }

        // If not retryable or last attempt, throw the error
        if (!this.isRetryableError(lastError) || attempt === this.retryConfig.maxRetries) {
          break
        }

        // Wait before retry
        await this.delay(this.calculateRetryDelay(attempt))
      }
    }

    // All retries exhausted, throw the last error
    throw lastError || new MultiAgentNetworkError(`Failed after ${this.retryConfig.maxRetries} attempts`)
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    if (!this.retryConfig.exponentialBackoff) {
      return this.retryConfig.retryDelay
    }
    
    return this.retryConfig.retryDelay * Math.pow(2, attempt - 1)
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    if (error instanceof MultiAgentServiceError) {
      return error.retryable
    }
    return false
  }

  /**
   * Validate review request
   */
  private validateReviewRequest(request: MultiAgentReviewRequest): void {
    if (!request.branch_name || typeof request.branch_name !== 'string') {
      throw new MultiAgentValidationError('branch_name', request.branch_name, 'non-empty string')
    }

    if (!request.repository_path || typeof request.repository_path !== 'string') {
      throw new MultiAgentValidationError('repository_path', request.repository_path, 'non-empty string')
    }

    if (!request.pr_url || typeof request.pr_url !== 'string') {
      throw new MultiAgentValidationError('pr_url', request.pr_url, 'non-empty string')
    }

    if (!request.review_mode || !['quick', 'full', 'ac_only', 'bug_analysis', 'summary_only'].includes(request.review_mode)) {
      throw new MultiAgentValidationError('review_mode', request.review_mode, '"quick", "full", "ac_only", "bug_analysis", or "summary_only"')
    }

    // Validate agent config if provided
    if (request.agent_config?.enabled_agents) {
      const validAgents: AgentType[] = [
        'acceptance_criteria', 'bug_detection', 'security_analysis',
        'logic_analysis', 'quality_analysis', 'architecture_analysis', 'summary'
      ]
      
      const invalidAgents = request.agent_config.enabled_agents.filter(
        agent => !validAgents.includes(agent)
      )
      
      if (invalidAgents.length > 0) {
        throw new MultiAgentValidationError(
          'agent_config.enabled_agents',
          invalidAgents,
          `valid agent types: ${validAgents.join(', ')}`
        )
      }
    }
  }

  /**
   * Get default repository path for RMA monorepo
   */
  getDefaultRepositoryPath(): string {
    return '/Users/<USER>/dev/rma-mono'
  }

  /**
   * Extract Jira ticket ID from branch name
   */
  extractJiraTicketFromBranch(branchName: string): string | null {
    const patterns = [
      /([A-Z]+-\d+)/,           // Standard format: ABC-123
      /([A-Z]+\d+-\d+)/,        // Alternative: ABC123-456
    ]

    for (const pattern of patterns) {
      const match = branchName.match(pattern)
      if (match) {
        return match[1]
      }
    }

    return null
  }

  /**
   * Create a review request with sensible defaults
   */
  createReviewRequest(options: {
    branchName: string
    repositoryPath?: string
    prUrl: string
    reviewMode?: ReviewMode
    enabledAgents?: AgentType[]
    jiraTicket?: {
      ticket_id: string
      summary: string
      description: string
      acceptance_criteria: string[]
    }
  }): MultiAgentReviewRequest {
    return {
      branch_name: options.branchName,
      repository_path: options.repositoryPath || this.getDefaultRepositoryPath(),
      pr_url: options.prUrl,
      review_mode: options.reviewMode || 'full',
      agent_config: {
        enabled_agents: options.enabledAgents || [
          'acceptance_criteria',
          'bug_detection', 
          'security_analysis',
          'logic_analysis',
          'quality_analysis',
          'architecture_analysis',
          'summary'
        ],
        timeout_seconds: 300, // 5 minutes per agent
        priority: 'normal',
        concurrent_agents: 7
      },
      jira_ticket: options.jiraTicket,
      context_config: {
        include_git_history: true,
        include_related_files: true,
        max_context_size: 50000 // 50KB max context
      }
    }
  }
}

// Export singleton instance
export const multiAgentReviewService = new MultiAgentReviewService()