import type { 
  BitbucketRepository, 
  BitbucketBranch, 
  BitbucketPullRequest, 
  Bitbucket<PERSON>Comment,
  BitbucketWorkspace,
  BitbucketUser
} from '../../types/bitbucket.types'

export type CacheableData = 
  | BitbucketRepository[] 
  | BitbucketBranch[] 
  | BitbucketPullRequest[] 
  | BitbucketPRComment[]
  | BitbucketWorkspace[]
  | BitbucketRepository
  | BitbucketPullRequest
  | BitbucketUser
  | { repositories: BitbucketRepository[]; hasNext: boolean }
  | { comments: BitbucketPRComment[]; hasNext: boolean }
  | string // For diff text

interface CacheEntry<T = CacheableData> {
  data: T
  timestamp: number
  expiresAt: number
  etag?: string
  lastModified?: string
  accessCount: number
  lastAccessed: number
}

interface CacheStats {
  totalEntries: number
  memoryUsage: number
  hitRate: number
  missRate: number
  totalHits: number
  totalMisses: number
  totalRequests: number
}

export class CacheManager {
  private cache = new Map<string, CacheEntry>()
  private stats = {
    totalHits: 0,
    totalMisses: 0,
    totalRequests: 0
  }

  // Cache TTL configurations (in milliseconds)
  private readonly TTL_CONFIG = {
    // Short-lived data (frequent changes)
    PULL_REQUESTS: 2 * 60 * 1000,     // 2 minutes
    PR_COMMENTS: 1 * 60 * 1000,       // 1 minute
    PR_DIFF: 5 * 60 * 1000,           // 5 minutes
    
    // Medium-lived data (occasional changes)
    BRANCHES: 15 * 60 * 1000,         // 15 minutes (longer cache for all branches)
    REPOSITORIES: 15 * 60 * 1000,     // 15 minutes
    
    // Long-lived data (rarely changes)
    WORKSPACES: 60 * 60 * 1000,       // 1 hour
    USER_PROFILE: 30 * 60 * 1000,     // 30 minutes
    
    // Default fallback
    DEFAULT: 5 * 60 * 1000            // 5 minutes
  }

  // Maximum cache size (number of entries)
  private readonly MAX_CACHE_SIZE = 1000
  
  // Memory usage threshold (in MB)
  private readonly MAX_MEMORY_MB = 50

  /**
   * Generate cache key with proper namespacing
   */
  private generateKey(
    type: keyof typeof this.TTL_CONFIG,
    identifier: string,
    params?: Record<string, any>
  ): string {
    const baseKey = `${type}:${identifier}`
    
    if (params) {
      const paramString = Object.entries(params)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => `${key}=${value}`)
        .join('&')
      return `${baseKey}?${paramString}`
    }
    
    return baseKey
  }

  /**
   * Store data in cache with automatic expiration
   */
  set<T extends CacheableData>(
    type: keyof typeof this.TTL_CONFIG,
    identifier: string,
    data: T,
    params?: Record<string, any>,
    options?: {
      customTTL?: number
      etag?: string
      lastModified?: string
    }
  ): void {
    try {
      const key = this.generateKey(type, identifier, params)
      const ttl = options?.customTTL || this.TTL_CONFIG[type] || this.TTL_CONFIG.DEFAULT
      const now = Date.now()

      const entry: CacheEntry<T> = {
        data,
        timestamp: now,
        expiresAt: now + ttl,
        etag: options?.etag,
        lastModified: options?.lastModified,
        accessCount: 0,
        lastAccessed: now
      }

      this.cache.set(key, entry)
      
      // Cleanup if cache is getting too large
      this.cleanup()
      
    } catch (error) {
      console.warn('Cache set failed:', error)
    }
  }

  /**
   * Retrieve data from cache if valid
   */
  get<T extends CacheableData>(
    type: keyof typeof this.TTL_CONFIG,
    identifier: string,
    params?: Record<string, any>
  ): T | null {
    try {
      this.stats.totalRequests++
      
      const key = this.generateKey(type, identifier, params)
      const entry = this.cache.get(key)

      if (!entry) {
        this.stats.totalMisses++
        return null
      }

      const now = Date.now()

      // Check if expired
      if (now > entry.expiresAt) {
        this.cache.delete(key)
        this.stats.totalMisses++
        return null
      }

      // Update access statistics
      entry.accessCount++
      entry.lastAccessed = now
      
      this.stats.totalHits++
      return entry.data as T

    } catch (error) {
      console.warn('Cache get failed:', error)
      this.stats.totalMisses++
      return null
    }
  }

  /**
   * Check if cached data is valid based on ETags/Last-Modified
   */
  isValid(
    type: keyof typeof this.TTL_CONFIG,
    identifier: string,
    params?: Record<string, any>,
    etag?: string,
    lastModified?: string
  ): boolean {
    try {
      const key = this.generateKey(type, identifier, params)
      const entry = this.cache.get(key)

      if (!entry) return false

      const now = Date.now()
      if (now > entry.expiresAt) return false

      // Check ETags
      if (etag && entry.etag) {
        return entry.etag === etag
      }

      // Check Last-Modified
      if (lastModified && entry.lastModified) {
        return entry.lastModified === lastModified
      }

      return true

    } catch (error) {
      console.warn('Cache validation failed:', error)
      return false
    }
  }

  /**
   * Invalidate specific cache entries
   */
  invalidate(
    type: keyof typeof this.TTL_CONFIG,
    identifier?: string,
    params?: Record<string, any>
  ): void {
    try {
      if (identifier) {
        const key = this.generateKey(type, identifier, params)
        this.cache.delete(key)
      } else {
        // Invalidate all entries of this type
        const pattern = `${type}:`
        for (const key of this.cache.keys()) {
          if (key.startsWith(pattern)) {
            this.cache.delete(key)
          }
        }
      }
    } catch (error) {
      console.warn('Cache invalidation failed:', error)
    }
  }

  /**
   * Invalidate related cache entries when data changes
   */
  invalidateRelated(changes: {
    workspace?: string
    repository?: string
    pullRequest?: number
    branch?: string
  }): void {
    try {
      const patterns: string[] = []

      if (changes.workspace) {
        patterns.push(`REPOSITORIES:${changes.workspace}`)
        patterns.push(`BRANCHES:${changes.workspace}`)
        patterns.push(`PULL_REQUESTS:${changes.workspace}`)
      }

      if (changes.repository) {
        patterns.push(`BRANCHES:${changes.workspace}/${changes.repository}`)
        patterns.push(`PULL_REQUESTS:${changes.workspace}/${changes.repository}`)
      }

      if (changes.pullRequest) {
        patterns.push(`PR_COMMENTS:${changes.workspace}/${changes.repository}/${changes.pullRequest}`)
        patterns.push(`PR_DIFF:${changes.workspace}/${changes.repository}/${changes.pullRequest}`)
      }

      for (const key of this.cache.keys()) {
        for (const pattern of patterns) {
          if (key.includes(pattern)) {
            this.cache.delete(key)
            break
          }
        }
      }

    } catch (error) {
      console.warn('Related cache invalidation failed:', error)
    }
  }

  /**
   * Clean up expired entries and manage memory usage
   */
  private cleanup(): void {
    try {
      const now = Date.now()
      let entriesDeleted = 0

      // Remove expired entries
      for (const [key, entry] of this.cache.entries()) {
        if (now > entry.expiresAt) {
          this.cache.delete(key)
          entriesDeleted++
        }
      }

      // If still too many entries, remove least recently used
      if (this.cache.size > this.MAX_CACHE_SIZE) {
        const entries = Array.from(this.cache.entries())
          .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)

        const toDelete = this.cache.size - this.MAX_CACHE_SIZE
        for (let i = 0; i < toDelete; i++) {
          this.cache.delete(entries[i][0])
          entriesDeleted++
        }
      }

      // Check memory usage (rough estimation)
      const estimatedMemoryMB = this.getEstimatedMemoryUsage()
      if (estimatedMemoryMB > this.MAX_MEMORY_MB) {
        // Remove entries with lowest access count
        const entries = Array.from(this.cache.entries())
          .sort(([, a], [, b]) => a.accessCount - b.accessCount)

        const deleteCount = Math.ceil(this.cache.size * 0.1) // Remove 10%
        for (let i = 0; i < deleteCount; i++) {
          this.cache.delete(entries[i][0])
          entriesDeleted++
        }
      }

      if (entriesDeleted > 0) {
        console.debug(`Cache cleanup: removed ${entriesDeleted} entries`)
      }

    } catch (error) {
      console.warn('Cache cleanup failed:', error)
    }
  }

  /**
   * Estimate memory usage in MB
   */
  private getEstimatedMemoryUsage(): number {
    try {
      let totalSize = 0
      
      for (const entry of this.cache.values()) {
        // Rough estimation: JSON string length as proxy for memory usage
        totalSize += JSON.stringify(entry.data).length
      }
      
      return totalSize / (1024 * 1024) // Convert to MB
    } catch (error) {
      console.warn('Memory estimation failed:', error)
      return 0
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return {
      totalEntries: this.cache.size,
      memoryUsage: this.getEstimatedMemoryUsage(),
      hitRate: this.stats.totalRequests > 0 
        ? (this.stats.totalHits / this.stats.totalRequests) * 100 
        : 0,
      missRate: this.stats.totalRequests > 0 
        ? (this.stats.totalMisses / this.stats.totalRequests) * 100 
        : 0,
      totalHits: this.stats.totalHits,
      totalMisses: this.stats.totalMisses,
      totalRequests: this.stats.totalRequests
    }
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.stats = {
      totalHits: 0,
      totalMisses: 0,
      totalRequests: 0
    }
    console.debug('Cache cleared')
  }

  /**
   * Get all cache keys (for debugging)
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache entry details (for debugging)
   */
  getEntryDetails(key: string): CacheEntry | null {
    return this.cache.get(key) || null
  }
}

// Singleton instance
export const cacheManager = new CacheManager()

// Helper function for cache-aware API calls
export async function withCache<T extends CacheableData>(
  type: keyof CacheManager['TTL_CONFIG'],
  identifier: string,
  fetcher: () => Promise<T>,
  params?: Record<string, any>,
  options?: {
    customTTL?: number
    etag?: string
    lastModified?: string
    forceRefresh?: boolean
  }
): Promise<T> {
  // Skip cache on force refresh
  if (!options?.forceRefresh) {
    const cached = cacheManager.get<T>(type, identifier, params)
    if (cached) {
      return cached
    }
  }

  // Fetch fresh data
  const data = await fetcher()
  
  // Store in cache
  cacheManager.set(type, identifier, data, params, options)
  
  return data
}