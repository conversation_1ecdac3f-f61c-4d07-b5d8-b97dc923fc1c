interface ClaudeCommentRequest {
  comment: string
  file: string
  line: string | number
  pullRequestContext: {
    workspace: string
    repository: string
    prId: number
    branchName?: string
    title?: string
    worktreePath?: string
  }
}

interface ClaudeResponse {
  success: boolean
  response: {
    comment_text: string
    file: string
    line: string
    response: string
    confidence: number
    analysis?: {
      affected_files: string[]
      dependencies: string[]
      impact: string
      todo_list: string[]
    }
    preview_changes?: Array<{
      file: string
      reason: string
      changes: Array<{
        type: string
        line_start: number
        line_end: number
        current_content: string
        suggested_content: string
        explanation: string
      }>
    }>
  }
  session_id?: string
  error?: string
}

export class ClaudeIntegrationService {
  private readonly API_BASE = 'http://localhost:5001/api'

  /**
   * Generate Claude response for a comment
   */
  async generateResponse(request: ClaudeCommentRequest): Promise<ClaudeResponse> {
    try {
      // Check if worktree context is available
      const hasWorktree = request.pullRequestContext.worktreePath && 
                         request.pullRequestContext.worktreePath.trim() !== ''

      // Create request data structure
      const commentData = {
        screenshot: {
          id: `comment_${Date.now()}`,
          dataUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 transparent PNG
          fileName: `comment_${request.file}_${request.line}.png`
        },
        context: {
          prUrl: `https://bitbucket.org/${request.pullRequestContext.workspace}/${request.pullRequestContext.repository}/pull-requests/${request.pullRequestContext.prId}`,
          branchName: request.pullRequestContext.branchName || 'unknown',
          ticketId: request.pullRequestContext.title || `PR-${request.pullRequestContext.prId}`,
          changesSummary: `Review comment on ${request.file}:${request.line}`,
          // Add worktree context when available
          ...(hasWorktree && {
            worktreePath: request.pullRequestContext.worktreePath,
            workingDirectory: request.pullRequestContext.worktreePath
          })
        },
        modificationPrompt: `
Analysiere diesen Code-Review-Kommentar und generiere eine hilfreiche Antwort:

KOMMENTAR: "${request.comment}"
DATEI: ${request.file}
ZEILE: ${request.line}
${hasWorktree ? `LOKALER WORKTREE: ${request.pullRequestContext.worktreePath}` : 'MODUS: API (kein lokaler Worktree verfügbar)'}

${hasWorktree ? 
  'Du hast Zugriff auf den lokalen Code im Worktree. Nutze diesen Kontext für eine noch präzisere Analyse.' :
  'Analysiere basierend auf den verfügbaren API-Informationen.'
}

Analysiere den kommentierten Code und gib eine konstruktive, deutsche Antwort die:
1. Das Problem/die Frage versteht
2. Eine konkrete Lösung oder Erklärung bietet
3. Bei Bedarf Code-Verbesserungen vorschlägt
4. Freundlich und professionell ist

Antworte direkt auf den Kommentar ohne Screenshots zu erwarten.
        `
      }

      const response = await fetch(`${this.API_BASE}/process-comment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(commentData)
      })

      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data as ClaudeResponse

    } catch (error) {
      console.error('Claude integration error:', error)
      
      // Extract error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      let userFriendlyMessage = 'Entschuldigung, ich konnte keine Antwort generieren. Bitte versuche es später erneut.'
      
      // Provide more specific error messages
      if (errorMessage.includes('Analysis zu komplex') || errorMessage.includes('max turns')) {
        userFriendlyMessage = '🔄 Die Analyse war zu komplex und benötigte zu viele Schritte. Bitte vereinfache deinen Kommentar oder teile ihn in kleinere Teile auf.'
      } else if (errorMessage.includes('Zeitüberschreitung') || errorMessage.includes('timeout')) {
        userFriendlyMessage = '⏱️ Die Analyse dauerte zu lange. Bitte versuche es mit einem einfacheren Kommentar erneut.'
      } else if (errorMessage.includes('Claude Code is not installed')) {
        userFriendlyMessage = '🛠️ Claude Code ist nicht installiert. Bitte kontaktiere den Administrator.'
      } else if (errorMessage.includes('API error: 5')) {
        userFriendlyMessage = '🔧 Backend-Service nicht verfügbar. Bitte versuche es später erneut.'
      }
      
      return {
        success: false,
        response: {
          comment_text: request.comment,
          file: request.file,
          line: request.line.toString(),
          response: userFriendlyMessage,
          confidence: 0
        },
        error: errorMessage
      }
    }
  }

  /**
   * Check if Claude backend is available
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      return response.ok
    } catch (error) {
      console.error('Claude health check failed:', error)
      return false
    }
  }
}

export const claudeIntegrationService = new ClaudeIntegrationService()