import type { Comment, PRContext, ApiResponse, GitDiffResponse, CommentResponse, PreviewChange } from '../types'

const API_BASE_URL = 'http://localhost:5001/api'

export class ApiService {
  static async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`, {
        method: 'GET',
        mode: 'cors'
      })
      return response.ok
    } catch (error) {
      console.error('Backend health check failed:', error)
      return false
    }
  }

  static async processComment(comment: Comment, context: PRContext): Promise<ApiResponse> {
    try {
      console.log('Processing comment:', {
        commentId: comment.id,
        fileName: comment.fileName,
        dataUrlLength: comment.dataUrl?.length,
        context
      })

      const response = await fetch(`${API_BASE_URL}/process-comment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          screenshot: {
            id: comment.id,
            dataUrl: comment.dataUrl,
            fileName: comment.fileName
          },
          context: context
        })
      })

      console.log('API Response status:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error Response:', errorText)
        throw new Error(`API error: ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()
      console.log('API Response data:', data)

      if (!data.success) {
        console.error('API returned error:', data.error)
        throw new Error(data.error || 'Unknown API error')
      }

      return {
        success: true,
        response: {
          commentText: data.response.comment_text,
          file: data.response.file,
          line: data.response.line,
          text: data.response.response,
          confidence: data.response.confidence,
          code_changes: data.response.code_changes,
          preview_changes: data.response.preview_changes,
          preview_mode: data.response.preview_mode,
          analysis: data.response.analysis
        },
        session_id: data.session_id
      }
    } catch (error) {
      console.error('Claude API call failed:', error)
      throw error
    }
  }

  static async executeApprovedChanges(sessionId: string, approvedChanges: any[]): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/session/${sessionId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          approved_changes: approvedChanges
        })
      })

      if (!response.ok) {
        throw new Error('Failed to execute changes')
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Execute changes failed:', error)
      throw error
    }
  }

  static async modifySessionInstructions(sessionId: string, modificationPrompt: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/session/${sessionId}/modify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          modification_prompt: modificationPrompt
        })
      })

      if (!response.ok) {
        throw new Error('Failed to process modification')
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Modify session failed:', error)
      throw error
    }
  }

  static async modifyFileSpecific(
    sessionId: string, 
    filename: string, 
    modificationPrompt: string,
    currentFileChanges: any[],
    fileDiffContext?: string
  ): Promise<ApiResponse & { targetFilename: string }> {
    try {
      // Create a comprehensive file-specific modification prompt
      let enhancedPrompt = `MODIFY ONLY THIS SPECIFIC FILE: ${filename}\n\n`
      enhancedPrompt += `USER INSTRUCTIONS FOR THIS FILE:\n${modificationPrompt}\n\n`
      
      if (currentFileChanges && currentFileChanges.length > 0) {
        enhancedPrompt += `CURRENT SUGGESTED CHANGES FOR THIS FILE:\n`
        currentFileChanges.forEach((change, index) => {
          enhancedPrompt += `Change ${index + 1}:\n`
          enhancedPrompt += `  Lines ${change.line_start}-${change.line_end}:\n`
          if (change.explanation) {
            enhancedPrompt += `  Reason: ${change.explanation}\n`
          }
          if (change.current_content) {
            enhancedPrompt += `  Current: ${change.current_content}\n`
          }
          if (change.suggested_content) {
            enhancedPrompt += `  Suggested: ${change.suggested_content}\n`
          }
          enhancedPrompt += `\n`
        })
      }
      
      if (fileDiffContext) {
        enhancedPrompt += `FILE DIFF CONTEXT:\n${fileDiffContext}\n\n`
      }
      
      enhancedPrompt += `IMPORTANT: Only modify the changes for ${filename}. Do not modify any other files. Focus specifically on the changes mentioned above for this single file.`

      const response = await fetch(`${API_BASE_URL}/session/${sessionId}/modify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          modification_prompt: enhancedPrompt
        })
      })

      if (!response.ok) {
        throw new Error('Failed to process file-specific modification')
      }

      const result = await response.json()
      
      // Add metadata about which file was targeted
      return {
        ...result,
        targetFilename: filename
      }
    } catch (error) {
      console.error('File-specific modification failed:', error)
      throw error
    }
  }

  // Utility function to extract file-specific changes from full response
  static extractFileSpecificChanges(fullResponse: any, targetFilename: string): {
    response: CommentResponse,
    previewChanges: PreviewChange[]
  } | null {
    if (!fullResponse?.preview_changes) return null

    // Filter preview changes to only include the target file
    const fileSpecificChanges = fullResponse.preview_changes.filter(
      (change: PreviewChange) => change.file === targetFilename
    )

    if (fileSpecificChanges.length === 0) return null

    return {
      response: {
        ...fullResponse,
        preview_changes: fileSpecificChanges
      },
      previewChanges: fileSpecificChanges
    }
  }

  static async closeSession(sessionId: string): Promise<ApiResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/session/${sessionId}/close`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors'
      })

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Close session failed:', error)
      throw error
    }
  }

  static async getGitDiff(worktreePath: string, branchName?: string): Promise<GitDiffResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/git-diff`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          worktreePath: worktreePath,
          branchName: branchName
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to load diff: ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Git diff failed:', error)
      throw error
    }
  }

  // Simulate Claude Code response (fallback for when backend is not available)
  static async simulateClaudeResponse(_comment: Comment, context: PRContext): Promise<ApiResponse> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    const sampleResponses = [
      {
        commentText: "Why not use async/await here instead of callbacks?",
        file: "src/services/content-service.ts",
        line: "45",
        text: "Great question! While async/await would indeed be cleaner, we're using callbacks here for a specific reason:\n\n1. This integrates with a legacy system that expects callback-style error handling\n2. The error propagation pattern here allows us to handle specific error types differently\n3. Performance benchmarks showed callbacks performed 15% better in this hot path\n\nHowever, I agree we should modernize this. I've created a tech debt ticket (TECH-123) to refactor this entire module to async/await once we migrate the legacy dependencies.",
        confidence: 0.95
      },
      {
        commentText: "This function is getting quite long. Consider breaking it down.",
        file: "src/controllers/weather.controller.ts",
        line: "123-189",
        text: "You're absolutely right. This function has grown beyond our 50-line guideline. Here's my plan to refactor it:\n\n```typescript\n// Extract validation logic\nprivate validateWeatherData(data: WeatherData): ValidationResult\n\n// Extract transformation logic  \nprivate transformWeatherResponse(raw: RawWeatherData): WeatherData\n\n// Extract caching logic\nprivate cacheWeatherData(data: WeatherData): Promise<void>\n```\n\nWould you prefer I do this refactoring in this PR or create a follow-up? The current implementation is tested and working, so I'm flexible on timing.",
        confidence: 0.92
      }
    ]

    const response = sampleResponses[Math.floor(Math.random() * sampleResponses.length)]

    // Add context-aware elements
    if (context.ticketDescription) {
      // Extract potential ticket ID from description or use first line
      const ticketRef = context.ticketDescription.split('\n')[0].substring(0, 50)
      response.text = response.text.replace('TECH-123', ticketRef)
    }

    return {
      success: true,
      response
    }
  }
}