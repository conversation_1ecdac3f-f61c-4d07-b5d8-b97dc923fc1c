
export interface LocalBranchInfo {
  exists: boolean
  worktreePath?: string
  isUpToDate?: boolean
  remoteRef?: string
}

export class LocalBranchDetector {
  /**
   * Check if a branch exists locally and get worktree path
   */
  static async checkLocalBranch(
    repositoryName: string,
    branchName: string,
    remoteUrl?: string
  ): Promise<LocalBranchInfo> {
    try {
      // Call backend to check if branch exists locally
      const response = await fetch('http://localhost:5001/api/git/check-branch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        mode: 'cors',
        body: JSON.stringify({
          repository: repositoryName,
          branch: branchName,
          remoteUrl: remoteUrl
        })
      })

      if (!response.ok) {
        console.warn('Failed to check local branch:', response.statusText)
        return { exists: false }
      }

      const data = await response.json()
      return {
        exists: data.exists || false,
        worktreePath: data.worktree_path,
        isUpToDate: data.is_up_to_date,
        remoteRef: data.remote_ref
      }
    } catch (error) {
      console.warn('Error checking local branch:', error)
      return { exists: false }
    }
  }

  /**
   * Get configured worktree base path from backend
   */
  private static async getWorktreeBasePath(): Promise<string> {
    try {
      const response = await fetch('http://localhost:5002/api/worktree/config', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors'
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.config.isValid && data.config.basePath) {
          console.log(`📁 Using configured worktree path: ${data.config.basePath}`)
          return data.config.basePath
        }
      }
    } catch (error) {
      console.warn('Failed to get worktree config:', error)
    }

    // No fallback - user must configure master repository path
    throw new Error(
      'No master repository configured. Please configure your master repository path via Settings → Git Worktree.'
    )
  }

  /**
   * Auto-detect worktree for a repository using configured master repository path
   * Worktrees are created in ../worktrees/ relative to the master repository
   */
  static async autoDetectWorktree(
    _workspace: string,
    repositoryName: string,
    branchName: string
  ): Promise<string | null> {
    try {
      // Get configured master repository path
      const masterRepoPath = await this.getWorktreeBasePath()
      
      // Create worktree path outside the master repository
      // Example: /Users/<USER>/rma/rma-mono -> /Users/<USER>/rma/worktrees/
      const parentDir = masterRepoPath.split('/').slice(0, -1).join('/')
      const worktreeBaseDir = `${parentDir}/worktrees`
      
      // Generate worktree name using same pattern as Python scripts
      const branchShort = branchName.length > 10 ? branchName.substring(0, 10) : branchName
      const branchSafe = branchShort.replace(/[^\w\-_]/g, '-')
      const worktreeName = `${branchSafe}-review`
      const expectedPath = `${worktreeBaseDir}/${worktreeName}`

      // Check if worktree exists at expected location
      const branchInfo = await this.checkLocalBranch(repositoryName, branchName)
      
      if (branchInfo.exists && branchInfo.worktreePath) {
        // Verify the path matches our expected location (outside master repo)
        if (branchInfo.worktreePath === expectedPath) {
          console.log(`✅ Found worktree at external location: ${expectedPath}`)
          return expectedPath
        } else {
          console.log(`⚠️ Worktree found at different location: ${branchInfo.worktreePath}`)
          console.log(`   Expected: ${expectedPath}`)
          return branchInfo.worktreePath
        }
      }

      console.warn(`❌ No worktree found for ${repositoryName}/${branchName}`)
      return null
    } catch (error) {
      console.error('Error in autoDetectWorktree:', error)
      return null
    }
  }

  /**
   * Get expected worktree path for a branch (without checking if it exists)
   */
  static async getExpectedWorktreePath(branchName: string): Promise<string> {
    const masterRepoPath = await this.getWorktreeBasePath()
    
    // Create worktree path outside the master repository  
    // Example: /Users/<USER>/rma/rma-mono -> /Users/<USER>/rma/worktrees/
    const parentDir = masterRepoPath.split('/').slice(0, -1).join('/')
    const worktreeBaseDir = `${parentDir}/worktrees`
    
    const branchShort = branchName.length > 10 ? branchName.substring(0, 10) : branchName
    const branchSafe = branchShort.replace(/[^\w\-_]/g, '-')
    const worktreeName = `${branchSafe}-review`
    return `${worktreeBaseDir}/${worktreeName}`
  }

  /**
   * Get suggested clone command if repository doesn't exist locally
   */
  static getCloneCommand(
    workspace: string,
    repositoryName: string,
    branchName: string
  ): string {
    return `git clone https://bitbucket.org/${workspace}/${repositoryName}.git && cd ${repositoryName} && git checkout ${branchName}`
  }
}
