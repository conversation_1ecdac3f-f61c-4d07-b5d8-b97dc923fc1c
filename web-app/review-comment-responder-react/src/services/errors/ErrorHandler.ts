import type { BitbucketApiError } from '../../types/bitbucket.types'

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'
export type ErrorCategory = 
  | 'authentication' 
  | 'authorization' 
  | 'network' 
  | 'api' 
  | 'validation' 
  | 'cache' 
  | 'unknown'

export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  sessionId?: string
  requestUrl?: string
  requestMethod?: string
  responseStatus?: number
  userAgent?: string
  timestamp?: number
  additionalData?: Record<string, unknown>
}

export interface EnhancedError {
  id: string
  message: string
  originalError: Error | BitbucketApiError | unknown
  category: ErrorCategory
  severity: ErrorSeverity
  context: ErrorContext
  timestamp: number
  userFriendlyMessage: string
  suggestedActions: string[]
  isRetryable: boolean
  retryCount?: number
  maxRetries?: number
}

export interface ErrorMetrics {
  totalErrors: number
  errorsByCategory: Record<ErrorCategory, number>
  errorsBySeverity: Record<ErrorSeverity, number>
  retryableErrors: number
  resolvedErrors: number
  lastErrorTime: number | null
}

class ErrorHandler {
  private errors: EnhancedError[] = []
  private maxErrorHistory = 100
  private listeners: ((error: EnhancedError) => void)[] = []

  /**
   * Process and enhance an error with context and user-friendly messaging
   */
  handleError(
    error: Error | BitbucketApiError | unknown,
    context: ErrorContext = {}
  ): EnhancedError {
    const enhancedError = this.enhanceError(error, context)
    
    // Store error in history
    this.addToHistory(enhancedError)
    
    // Notify listeners
    this.notifyListeners(enhancedError)
    
    // Log error (with appropriate level)
    this.logError(enhancedError)
    
    return enhancedError
  }

  /**
   * Enhance raw error with metadata and user-friendly information
   */
  private enhanceError(
    error: Error | BitbucketApiError | unknown,
    context: ErrorContext
  ): EnhancedError {
    const timestamp = Date.now()
    const id = this.generateErrorId(timestamp)

    // Parse error details
    const { message, category, severity, isRetryable } = this.analyzeError(error)
    
    // Generate user-friendly message and suggestions
    const userFriendlyMessage = this.generateUserFriendlyMessage(category)
    const suggestedActions = this.generateSuggestedActions(category, context)

    return {
      id,
      message,
      originalError: error,
      category,
      severity,
      context: {
        ...context,
        timestamp,
        userAgent: navigator.userAgent
      },
      timestamp,
      userFriendlyMessage,
      suggestedActions,
      isRetryable,
      retryCount: 0,
      maxRetries: isRetryable ? this.getMaxRetries(category) : 0
    }
  }

  /**
   * Analyze error to determine category, severity, and retry-ability
   */
  private analyzeError(error: unknown): {
    message: string
    category: ErrorCategory
    severity: ErrorSeverity
    isRetryable: boolean
  } {
    // Handle Bitbucket API errors
    if (this.isBitbucketApiError(error)) {
      return this.analyzeBitbucketError(error)
    }

    // Handle standard JavaScript errors
    if (error instanceof Error) {
      return this.analyzeStandardError(error)
    }

    // Handle unknown errors
    return {
      message: String(error),
      category: 'unknown',
      severity: 'medium',
      isRetryable: false
    }
  }

  /**
   * Analyze Bitbucket API specific errors
   */
  private analyzeBitbucketError(error: BitbucketApiError): {
    message: string
    category: ErrorCategory
    severity: ErrorSeverity
    isRetryable: boolean
  } {
    const message = error.error?.message || 'Bitbucket API error'

    // Determine category based on error type
    if (error.type?.includes('authentication')) {
      return {
        message,
        category: 'authentication',
        severity: 'high',
        isRetryable: false
      }
    }

    if (error.type?.includes('authorization') || message.includes('forbidden')) {
      return {
        message,
        category: 'authorization', 
        severity: 'high',
        isRetryable: false
      }
    }

    // Default API error
    return {
      message,
      category: 'api',
      severity: 'medium',
      isRetryable: true
    }
  }

  /**
   * Analyze standard JavaScript errors
   */
  private analyzeStandardError(error: Error): {
    message: string
    category: ErrorCategory
    severity: ErrorSeverity
    isRetryable: boolean
  } {
    const message = error.message

    // Network errors
    if (message.includes('fetch') || message.includes('network') || message.includes('timeout')) {
      return {
        message,
        category: 'network',
        severity: 'medium',
        isRetryable: true
      }
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return {
        message,
        category: 'validation',
        severity: 'low',
        isRetryable: false
      }
    }

    // Cache errors
    if (message.includes('cache')) {
      return {
        message,
        category: 'cache',
        severity: 'low',
        isRetryable: true
      }
    }

    // Default error
    return {
      message,
      category: 'unknown',
      severity: 'medium',
      isRetryable: false
    }
  }

  /**
   * Generate user-friendly error messages
   */
  private generateUserFriendlyMessage(category: ErrorCategory): string {
    switch (category) {
      case 'authentication':
        return 'Authentication failed. Please log in again to continue.'
      
      case 'authorization':
        return 'You don\'t have permission to access this resource. Please check your account permissions.'
      
      case 'network':
        return 'Network connection issue. Please check your internet connection and try again.'
      
      case 'api':
        return 'There was an issue communicating with Bitbucket. Please try again in a moment.'
      
      case 'validation':
        return 'The provided information is invalid. Please check your input and try again.'
      
      case 'cache':
        return 'There was a temporary issue with data storage. Refreshing may help.'
      
      default:
        return 'An unexpected error occurred. Please try again or contact support if the issue persists.'
    }
  }

  /**
   * Generate suggested actions based on error category
   */
  private generateSuggestedActions(
    category: ErrorCategory, 
    errorContext: ErrorContext
  ): string[] {
    const baseActions: Record<ErrorCategory, string[]> = {
      authentication: [
        'Click the login button to re-authenticate',
        'Clear your browser cache and cookies',
        'Check if your Bitbucket account is still active'
      ],
      
      authorization: [
        'Contact your workspace administrator for access',
        'Verify you have the correct permissions',
        'Try logging in with a different account'
      ],
      
      network: [
        'Check your internet connection',
        'Try refreshing the page',
        'Disable VPN or proxy if active',
        'Try again in a few minutes'
      ],
      
      api: [
        'Wait a moment and try again',
        'Check Bitbucket status page for outages',
        'Refresh the page',
        'Clear browser cache'
      ],
      
      validation: [
        'Check that all required fields are filled',
        'Verify the format of your input',
        'Try with different values'
      ],
      
      cache: [
        'Refresh the page',
        'Clear browser cache',
        'Try in an incognito/private window'
      ],
      
      unknown: [
        'Refresh the page',
        'Try again in a few minutes',
        'Clear browser cache',
        'Contact support if issue persists'
      ]
    }

    const actions = [...baseActions[category]]

    // Add context-specific actions
    if (errorContext.component === 'RepositorySelector' && category === 'api') {
      actions.unshift('Try selecting a different workspace')
    }

    if (errorContext.action === 'createComment' && category === 'authorization') {
      actions.unshift('Check if you have write access to this repository')
    }

    return actions
  }

  /**
   * Get maximum retry attempts for error category
   */
  private getMaxRetries(category: ErrorCategory): number {
    const retryConfig: Record<ErrorCategory, number> = {
      authentication: 0,  // Don't retry auth errors
      authorization: 0,   // Don't retry permission errors
      network: 3,         // Retry network errors
      api: 2,            // Retry API errors
      validation: 0,      // Don't retry validation errors
      cache: 1,          // Retry cache errors once
      unknown: 1         // Retry unknown errors once
    }

    return retryConfig[category] || 0
  }

  /**
   * Check if error is a Bitbucket API error
   */
  private isBitbucketApiError(error: unknown): error is BitbucketApiError {
    return typeof error === 'object' && 
           error !== null && 
           'type' in error && 
           'error' in error
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(timestamp: number): string {
    return `error_${timestamp}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Add error to history with cleanup
   */
  private addToHistory(error: EnhancedError): void {
    this.errors.unshift(error)
    
    // Keep only recent errors
    if (this.errors.length > this.maxErrorHistory) {
      this.errors = this.errors.slice(0, this.maxErrorHistory)
    }
  }

  /**
   * Notify registered listeners
   */
  private notifyListeners(error: EnhancedError): void {
    this.listeners.forEach(listener => {
      try {
        listener(error)
      } catch (listenerError) {
        console.error('Error listener failed:', listenerError)
      }
    })
  }

  /**
   * Log error with appropriate level
   */
  private logError(error: EnhancedError): void {
    const logData = {
      id: error.id,
      message: error.message,
      category: error.category,
      severity: error.severity,
      context: error.context,
      timestamp: new Date(error.timestamp).toISOString()
    }

    switch (error.severity) {
      case 'critical':
        console.error('CRITICAL ERROR:', logData)
        break
      case 'high':
        console.error('HIGH SEVERITY ERROR:', logData)
        break
      case 'medium':
        console.warn('MEDIUM SEVERITY ERROR:', logData)
        break
      case 'low':
        console.info('LOW SEVERITY ERROR:', logData)
        break
    }
  }

  /**
   * Register error listener
   */
  onError(listener: (error: EnhancedError) => void): () => void {
    this.listeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * Get error metrics
   */
  getMetrics(): ErrorMetrics {
    const errorsByCategory: Record<ErrorCategory, number> = {
      authentication: 0,
      authorization: 0,
      network: 0,
      api: 0,
      validation: 0,
      cache: 0,
      unknown: 0
    }

    const errorsBySeverity: Record<ErrorSeverity, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    }

    let retryableErrors = 0
    let resolvedErrors = 0
    let lastErrorTime: number | null = null

    this.errors.forEach(error => {
      errorsByCategory[error.category]++
      errorsBySeverity[error.severity]++
      
      if (error.isRetryable) retryableErrors++
      if (error.retryCount && error.retryCount > 0) resolvedErrors++
      
      if (!lastErrorTime || error.timestamp > lastErrorTime) {
        lastErrorTime = error.timestamp
      }
    })

    return {
      totalErrors: this.errors.length,
      errorsByCategory,
      errorsBySeverity,
      retryableErrors,
      resolvedErrors,
      lastErrorTime
    }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): EnhancedError[] {
    return this.errors.slice(0, limit)
  }

  /**
   * Clear error history
   */
  clearHistory(): void {
    this.errors = []
  }

  /**
   * Mark error as resolved/retried
   */
  markErrorResolved(errorId: string): void {
    const error = this.errors.find(e => e.id === errorId)
    if (error) {
      error.retryCount = (error.retryCount || 0) + 1
    }
  }
}

// Singleton instance
export const errorHandler = new ErrorHandler()

// Helper function for wrapping async operations with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context: ErrorContext = {}
): Promise<T> {
  try {
    return await operation()
  } catch (error) {
    const enhancedError = errorHandler.handleError(error, context)
    throw enhancedError
  }
}

// Helper function for retrying operations
export async function withRetry<T>(
  operation: () => Promise<T>,
  context: ErrorContext = {},
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: EnhancedError | null = null
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = errorHandler.handleError(error, {
        ...context,
        action: `${context.action || 'operation'}_attempt_${attempt + 1}`
      })

      // Don't retry if error is not retryable
      if (!lastError.isRetryable || attempt === maxRetries) {
        throw lastError
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => 
        setTimeout(resolve, delay * Math.pow(2, attempt))
      )
    }
  }

  throw lastError
}