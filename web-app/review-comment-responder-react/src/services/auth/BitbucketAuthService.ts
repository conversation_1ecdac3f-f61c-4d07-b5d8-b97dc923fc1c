import type { 
  OAuthConfig, 
  AccessTokenResponse, 
  AuthState, 
  BitbucketUser,
  BitbucketApiError 
} from '../../types/bitbucket.types'

class BitbucketAuthService {
  private config: OAuthConfig
  private readonly STORAGE_KEY = 'bitbucket_auth'
  private readonly API_BASE = 'https://api.bitbucket.org/2.0'
  
  constructor() {
    this.config = {
      clientId: import.meta.env.VITE_BITBUCKET_CLIENT_ID || '',
      clientSecret: import.meta.env.VITE_BITBUCKET_CLIENT_SECRET || '',
      redirectUri: import.meta.env.VITE_BITBUCKET_REDIRECT_URI || 'http://localhost:5173/auth/bitbucket/callback',
      scopes: ['account', 'repository', 'pullrequest']
    }

    if (!this.config.clientId) {
      console.warn('Bitbucket OAuth not configured: Missing VITE_BITBUCKET_CLIENT_ID')
    }
  }

  /**
   * Generate OAuth authorization URL and redirect user to Bitbucket
   */
  async initiateLogin(): Promise<void> {
    if (!this.config.clientId) {
      throw new Error('Bitbucket OAuth not configured')
    }

    // Clean up any previous OAuth state
    sessionStorage.removeItem('oauth_state')
    sessionStorage.removeItem('oauth_code_verifier')
    // Clear any processing flags from previous attempts
    const allKeys = Object.keys(sessionStorage)
    allKeys.forEach(key => {
      if (key.startsWith('oauth_processing_')) {
        sessionStorage.removeItem(key)
      }
    })

    // Generate PKCE code verifier and challenge for security
    const codeVerifier = this.generateCodeVerifier()
    const codeChallenge = await this.generateCodeChallenge(codeVerifier)
    
    // Store code verifier for later token exchange
    sessionStorage.setItem('oauth_code_verifier', codeVerifier)
    
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      response_type: 'code',
      redirect_uri: this.config.redirectUri,
      scope: this.config.scopes.join(' '),
      state: this.generateState(),
      code_challenge: codeChallenge,
      code_challenge_method: 'S256'
    })

    const authUrl = `https://bitbucket.org/site/oauth2/authorize?${params.toString()}`
    window.location.href = authUrl
  }

  /**
   * Handle OAuth callback and exchange code for access token
   */
  async handleCallback(code: string, state: string): Promise<AuthState> {
    try {
      // Check if we're already processing this callback
      const processingKey = `oauth_processing_${code}`
      if (sessionStorage.getItem(processingKey)) {
        throw new Error('OAuth callback already being processed')
      }
      
      // Mark as processing
      sessionStorage.setItem(processingKey, 'true')

      // Verify state parameter to prevent CSRF attacks
      const storedState = sessionStorage.getItem('oauth_state')
      if (state !== storedState) {
        sessionStorage.removeItem(processingKey)
        throw new Error('Invalid state parameter - possible CSRF attack')
      }

      const tokenResponse = await this.exchangeCodeForToken(code)
      const user = await this.fetchUserInfo(tokenResponse.access_token)

      // Clean up processing flag and OAuth state
      sessionStorage.removeItem(processingKey)
      sessionStorage.removeItem('oauth_state')
      sessionStorage.removeItem('oauth_code_verifier')

      const authState: AuthState = {
        isAuthenticated: true,
        user,
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token || null,
        expiresAt: Date.now() + (tokenResponse.expires_in * 1000),
        isLoading: false,
        error: null
      }

      // Store auth state securely
      this.storeAuthState(authState)
      
      // Clean up temporary storage
      sessionStorage.removeItem('oauth_code_verifier')
      sessionStorage.removeItem('oauth_state')

      return authState
    } catch (error) {
      // Clean up processing flag on error
      const processingKey = `oauth_processing_${code}`
      sessionStorage.removeItem(processingKey)
      
      console.error('OAuth callback failed:', error)
      throw error
    }
  }

  /**
   * Exchange authorization code for access token
   */
  private async exchangeCodeForToken(code: string): Promise<AccessTokenResponse> {
    const codeVerifier = sessionStorage.getItem('oauth_code_verifier')
    if (!codeVerifier) {
      throw new Error('Missing code verifier - OAuth flow compromised')
    }

    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
      redirect_uri: this.config.redirectUri,
      code_verifier: codeVerifier
    })

    const response = await fetch('https://bitbucket.org/site/oauth2/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: body.toString()
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => null)
      console.error('Token exchange error response:', {
        status: response.status,
        statusText: response.statusText,
        errorData
      })
      
      const errorMessage = errorData?.error_description || 
                          errorData?.error || 
                          `HTTP ${response.status}: ${response.statusText}`
      throw new Error(`Token exchange failed: ${errorMessage}`)
    }

    return response.json()
  }

  /**
   * Fetch user information from Bitbucket API
   */
  private async fetchUserInfo(accessToken: string): Promise<BitbucketUser> {
    const response = await fetch(`${this.API_BASE}/user`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch user info: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Refresh expired access token
   */
  async refreshAccessToken(refreshToken: string): Promise<AuthState> {
    try {
      const body = new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret
      })

      const response = await fetch('https://bitbucket.org/site/oauth2/access_token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        body: body.toString()
      })

      if (!response.ok) {
        throw new Error('Token refresh failed')
      }

      const tokenResponse: AccessTokenResponse = await response.json()
      const user = await this.fetchUserInfo(tokenResponse.access_token)

      const authState: AuthState = {
        isAuthenticated: true,
        user,
        accessToken: tokenResponse.access_token,
        refreshToken: tokenResponse.refresh_token || refreshToken,
        expiresAt: Date.now() + (tokenResponse.expires_in * 1000),
        isLoading: false,
        error: null
      }

      this.storeAuthState(authState)
      return authState
    } catch (error) {
      console.error('Token refresh failed:', error)
      // If refresh fails, user needs to re-authenticate
      this.logout()
      throw error
    }
  }

  /**
   * Check if token is expired or will expire soon
   */
  isTokenExpired(expiresAt: number): boolean {
    // Consider token expired if it expires within 5 minutes
    return Date.now() >= (expiresAt - 5 * 60 * 1000)
  }

  /**
   * Get current auth state from storage
   */
  getStoredAuthState(): AuthState | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (!stored) return null

      const authState: AuthState = JSON.parse(stored)
      
      // Check if token is expired
      if (authState.expiresAt && this.isTokenExpired(authState.expiresAt)) {
        // Try to refresh if we have a refresh token
        if (authState.refreshToken) {
          // Don't await here, return current state and refresh in background
          this.refreshAccessToken(authState.refreshToken).catch(() => {
            this.logout() // If refresh fails, logout
          })
        } else {
          this.logout()
          return null
        }
      }

      return authState
    } catch (error) {
      console.error('Failed to load auth state:', error)
      this.logout() // Clear corrupted state
      return null
    }
  }

  /**
   * Store auth state securely
   */
  private storeAuthState(authState: AuthState): void {
    try {
      // Don't store sensitive data in localStorage in production
      // Consider using httpOnly cookies via backend for production
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(authState))
    } catch (error) {
      console.error('Failed to store auth state:', error)
    }
  }

  /**
   * Logout and clear all auth data
   */
  logout(): void {
    localStorage.removeItem(this.STORAGE_KEY)
    sessionStorage.removeItem('oauth_code_verifier')
    sessionStorage.removeItem('oauth_state')
  }

  /**
   * Make authenticated API request to Bitbucket
   */
  async makeAuthenticatedRequest<T>(
    endpoint: string, 
    accessToken: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = endpoint.startsWith('http') ? endpoint : `${this.API_BASE}${endpoint}`
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      if (response.status === 401) {
        // Token expired, trigger re-authentication
        this.logout()
        throw new Error('Authentication expired. Please login again.')
      }

      let errorMessage = `API request failed: ${response.statusText}`
      try {
        const errorData: BitbucketApiError = await response.json()
        errorMessage = errorData.error?.message || errorMessage
      } catch {
        // Ignore JSON parse errors
      }

      throw new Error(errorMessage)
    }

    return response.json()
  }

  /**
   * Generate PKCE code verifier for OAuth security
   */
  private generateCodeVerifier(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  /**
   * Generate PKCE code challenge from verifier
   */
  private async generateCodeChallenge(verifier: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(verifier)
    const digest = await crypto.subtle.digest('SHA-256', data)
    
    const array = new Uint8Array(digest)
    return btoa(String.fromCharCode.apply(null, Array.from(array)))
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '')
  }

  /**
   * Generate random state parameter for CSRF protection
   */
  private generateState(): string {
    const state = Math.random().toString(36).substring(2, 15) + 
                 Math.random().toString(36).substring(2, 15)
    sessionStorage.setItem('oauth_state', state)
    return state
  }

  /**
   * Check if OAuth is properly configured
   */
  isConfigured(): boolean {
    return !!(this.config.clientId && this.config.clientSecret)
  }
}

export const bitbucketAuthService = new BitbucketAuthService()
export default BitbucketAuthService