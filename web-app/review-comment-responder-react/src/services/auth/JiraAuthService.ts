/**
 * Jira OAuth 2.0 Authentication Service
 * Handles authentication, token management, and API calls to Jira
 */

interface JiraOAuthConfig {
  clientId: string
  clientSecret: string
  redirectUri: string
  scope: string[]
  cloudId?: string
  baseUrl?: string
}

interface JiraUser {
  accountId: string
  emailAddress: string
  displayName: string
  avatarUrls: {
    '16x16': string
    '24x24': string
    '32x32': string
    '48x48': string
  }
  timeZone: string
  locale: string
}

interface JiraTicket {
  id: string
  key: string
  summary: string
  description: string
  status: {
    name: string
    statusCategory: {
      key: string
      name: string
    }
  }
  priority: {
    name: string
    iconUrl: string
  }
  assignee: {
    accountId: string
    displayName: string
    emailAddress: string
  }
  reporter: {
    accountId: string
    displayName: string
  }
  created: string
  updated: string
  customFields?: Record<string, any>
  acceptanceCriteria?: string[]
}

interface JiraSearchResult {
  issues: JiraTicket[]
  total: number
  maxResults: number
  startAt: number
}

export class JiraAuthService {
  private config: JiraOAuthConfig
  private accessToken: string | null = null
  private refreshToken: string | null = null
  private tokenExpiry: Date | null = null
  private currentUser: JiraUser | null = null
  private isProcessingCallback: boolean = false
  private ssoAttemptInProgress: boolean = false

  constructor() {
    this.config = {
      clientId: import.meta.env.VITE_JIRA_CLIENT_ID || '',
      clientSecret: import.meta.env.VITE_JIRA_CLIENT_SECRET || '',
      redirectUri: `${window.location.origin}/auth/jira/callback`,
      scope: ['read:jira-user', 'read:jira-work', 'write:jira-work', 'read:me', 'read:issue:jira', 'write:issue:jira', 'read:project:jira', 'write:project:jira'],
      baseUrl: this.getStoredBaseUrl() || import.meta.env.VITE_JIRA_BASE_URL || 'https://regionalmediendigital.atlassian.net'
    }

    // Load stored tokens on initialization
    this.loadStoredTokens()
    
    // Note: SSO will only be attempted when user explicitly requests Jira access
  }


  /**
   * Public method to attempt SSO (can be called by UI)
   */
  async attemptSSO(): Promise<boolean> {
    try {
      await this.tryInheritBitbucketAuth()
      return this.isAuthenticated()
    } catch (error) {
      console.log('ℹ️ SSO attempt failed:', error)
      return false
    }
  }

  /**
   * Try to inherit authentication from Bitbucket (same Atlassian account)
   */
  private async tryInheritBitbucketAuth(): Promise<void> {
    // Check if already authenticated with Jira
    if (this.isAuthenticated()) {
      return
    }

    // Prevent multiple SSO attempts
    if (this.ssoAttemptInProgress) {
      console.log('🔄 SSO attempt already in progress, skipping...')
      return
    }

    this.ssoAttemptInProgress = true

    try {
      // Get Bitbucket auth state from localStorage
      const bitbucketAuthData = localStorage.getItem('bitbucket_auth')
      if (!bitbucketAuthData) {
        return
      }

      const bitbucketAuth = JSON.parse(bitbucketAuthData)
      if (!bitbucketAuth.accessToken || !bitbucketAuth.expiresAt) {
        return
      }

      // Check if Bitbucket token is still valid
      const expiryTime = new Date(bitbucketAuth.expiresAt)
      if (expiryTime <= new Date()) {
        return
      }

      console.log('🔄 Trying to inherit Jira access from Bitbucket authentication...')

      // Use Bitbucket token to access Atlassian API
      this.accessToken = bitbucketAuth.accessToken
      this.tokenExpiry = expiryTime

      // Check if the Bitbucket token has Jira access
      // Try a simple Atlassian API call to test permissions
      const resourcesResponse = await fetch('https://api.atlassian.com/oauth/token/accessible-resources', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json'
        }
      })

      if (!resourcesResponse.ok) {
        // Bitbucket token doesn't have Jira permissions
        // This is normal - they need separate OAuth flows
        throw new Error(`Bitbucket token doesn't have Jira permissions (${resourcesResponse.status}). User needs to authorize Jira separately.`)
      }

      const resources = await resourcesResponse.json()
      console.log('📋 Accessible Atlassian resources:', resources.map((r: any) => r.name))

      // Find Jira instances
      const jiraResources = resources.filter((resource: any) => 
        resource.scopes?.some((scope: string) => scope.includes('jira')) || 
        resource.name?.toLowerCase().includes('jira')
      )

      if (jiraResources.length === 0) {
        throw new Error('No Jira instances accessible with this Bitbucket token')
      }

      // Use the first Jira instance
      const primaryJira = jiraResources[0]
      this.config.baseUrl = primaryJira.url
      this.config.cloudId = primaryJira.id

      console.log(`🎯 Found Jira instance: ${primaryJira.name} (${primaryJira.url})`)

      // Store the discovered values
      localStorage.setItem('jira_base_url', primaryJira.url)
      localStorage.setItem('jira_cloud_id', primaryJira.id)

      // Test if the token works for Jira API calls
      await this.fetchCurrentUser()

      console.log('✅ Successfully inherited Jira access from Bitbucket!')
      this.storeTokens()

    } catch (error) {
      console.log('ℹ️ Could not inherit Jira access from Bitbucket:', error)
      // Clear any partially set tokens
      this.clearTokens()
    } finally {
      this.ssoAttemptInProgress = false
    }
  }

  /**
   * Check if user is authenticated with Jira
   */
  isAuthenticated(): boolean {
    return !!(this.accessToken && this.tokenExpiry && this.tokenExpiry > new Date())
  }

  /**
   * Get current authenticated user
   */
  getCurrentUser(): JiraUser | null {
    return this.currentUser
  }

  /**
   * Set Jira base URL (e.g., 'https://yourcompany.atlassian.net')
   */
  setBaseUrl(baseUrl: string): void {
    // Validate URL format
    if (!baseUrl.includes('atlassian.net')) {
      throw new Error('Invalid Jira URL. Must be an Atlassian URL (e.g., https://yourcompany.atlassian.net)')
    }
    
    // Ensure https
    if (!baseUrl.startsWith('https://')) {
      baseUrl = `https://${baseUrl}`
    }
    
    this.config.baseUrl = baseUrl
    localStorage.setItem('jira_base_url', baseUrl)
  }

  /**
   * Get current Jira base URL
   */
  getBaseUrl(): string {
    return this.config.baseUrl || 'https://yourcompany.atlassian.net'
  }

  /**
   * Start OAuth flow by redirecting to Jira authorization
   */
  startAuthFlow(): void {
    // No need to check for baseUrl since we'll discover it automatically
    const state = this.generateRandomState()
    localStorage.setItem('jira_oauth_state', state)

    const authUrl = this.buildAuthUrl(state)
    console.log('🔐 Starting Jira OAuth flow (auto-discovery enabled):', authUrl)
    console.log('🔗 Redirect URI being used:', this.config.redirectUri)
    
    window.location.href = authUrl
  }

  /**
   * Handle OAuth callback with authorization code
   */
  async handleAuthCallback(code: string, state: string): Promise<boolean> {
    // Prevent duplicate processing in React Strict Mode
    if (this.isProcessingCallback) {
      console.log('⚠️ Callback already being processed, skipping duplicate')
      return this.isAuthenticated()
    }
    
    this.isProcessingCallback = true
    
    try {
      // Verify state parameter
      const storedState = localStorage.getItem('jira_oauth_state')
      if (state !== storedState) {
        throw new Error('Invalid state parameter - possible CSRF attack')
      }

      // Exchange code for tokens
      const tokenResponse = await this.exchangeCodeForTokens(code)
      
      if (tokenResponse.access_token) {
        this.accessToken = tokenResponse.access_token
        this.refreshToken = tokenResponse.refresh_token
        this.tokenExpiry = new Date(Date.now() + tokenResponse.expires_in * 1000)

        // Discover user's Jira instances automatically
        await this.discoverJiraInstances()

        // Store tokens securely
        this.storeTokens()

        // Get user information (non-blocking)
        this.fetchCurrentUser().catch(error => {
          console.warn('⚠️ Could not fetch user details, but authentication successful:', error)
        })

        console.log('✅ Jira authentication successful')
        return true
      }

      throw new Error('No access token received')
      
    } catch (error) {
      console.error('❌ Jira auth callback failed:', error)
      this.clearTokens()
      return false
    } finally {
      localStorage.removeItem('jira_oauth_state')
      this.isProcessingCallback = false
    }
  }

  /**
   * Logout and clear stored tokens
   */
  logout(): void {
    this.clearTokens()
    this.currentUser = null
    
    // Clear any cached OAuth state
    localStorage.removeItem('jira_oauth_state')
    
    console.log('🔓 Logged out from Jira')
  }

  /**
   * Force account selection by opening Atlassian logout first
   */
  async forceAccountSelection(): Promise<void> {
    console.log('🔄 Forcing account selection...')
    
    // First, try to logout from Atlassian to clear their session
    const logoutUrl = 'https://id.atlassian.com/logout'
    
    // Open logout in a popup, then start auth flow
    const popup = window.open(logoutUrl, 'atlassian-logout', 'width=600,height=400')
    
    // Wait a moment for logout, then close popup and start auth
    setTimeout(() => {
      if (popup && !popup.closed) {
        popup.close()
      }
      
      // Small delay to ensure logout is processed
      setTimeout(() => {
        this.startAuthFlow()
      }, 1000)
    }, 2000)
  }

  /**
   * Get tickets assigned to current user
   */
  async getAssignedTickets(maxResults: number = 50): Promise<JiraTicket[]> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Jira')
    }

    try {
      const response = await this.makeAuthenticatedRequest(
        `/tickets?maxResults=${maxResults}`
      )

      if (!response.ok) {
        throw new Error(`Failed to fetch tickets: ${response.status}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch tickets')
      }

      return data.tickets

    } catch (error) {
      console.error('❌ Failed to fetch assigned tickets:', error)
      throw error
    }
  }

  /**
   * Get specific ticket details including acceptance criteria
   */
  async getTicketDetails(ticketKey: string): Promise<JiraTicket | null> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Jira')
    }

    try {
      const response = await this.makeAuthenticatedRequest(
        `/ticket/${ticketKey}`
      )

      if (!response.ok) {
        if (response.status === 404) {
          return null
        }
        throw new Error(`Failed to fetch ticket: ${response.status}`)
      }

      const data = await response.json()
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch ticket')
      }

      return data.ticket

    } catch (error) {
      console.error(`❌ Failed to fetch ticket ${ticketKey}:`, error)
      throw error
    }
  }

  /**
   * Search for tickets with JQL
   */
  async searchIssues(jql: string, maxResults: number = 50, startAt: number = 0): Promise<JiraSearchResult> {
    if (!this.isAuthenticated()) {
      throw new Error('Not authenticated with Jira')
    }

    try {
      const requestBody = {
        jql,
        maxResults,
        startAt,
        fields: [
          'summary',
          'description', 
          'status',
          'priority',
          'assignee',
          'reporter',
          'created',
          'updated',
          'customfield_*' // Include custom fields for acceptance criteria
        ]
      }

      const response = await this.makeAuthenticatedRequest('/rest/api/3/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        throw new Error(`Search failed: ${response.status}`)
      }

      const data = await response.json()
      
      return {
        issues: data.issues.map((issue: any) => this.transformIssueData(issue)),
        total: data.total,
        maxResults: data.maxResults,
        startAt: data.startAt
      }

    } catch (error) {
      console.error('❌ Failed to search issues:', error)
      throw error
    }
  }

  /**
   * Get tickets related to a specific branch name
   */
  async getTicketsForBranch(branchName: string): Promise<JiraTicket[]> {
    // Extract ticket key from branch name
    const ticketKeyPattern = /([A-Z]+-\d+)/g
    const matches = branchName.match(ticketKeyPattern)
    
    if (!matches || matches.length === 0) {
      return []
    }

    const tickets: JiraTicket[] = []
    
    for (const ticketKey of matches) {
      try {
        const ticket = await this.getTicketDetails(ticketKey)
        if (ticket) {
          tickets.push(ticket)
        }
      } catch (error) {
        console.warn(`⚠️ Could not fetch ticket ${ticketKey}:`, error)
      }
    }

    return tickets
  }

  /**
   * Discover user's accessible Jira instances using OAuth token
   */
  private async discoverJiraInstances(): Promise<void> {
    if (!this.accessToken) {
      console.warn('⚠️ No access token available for instance discovery')
      return
    }

    try {
      console.log('🔍 Discovering accessible Jira instances...')
      
      // Use Atlassian API to get accessible resources
      const response = await fetch('https://api.atlassian.com/oauth/token/accessible-resources', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch accessible resources: ${response.status}`)
      }

      const resources = await response.json()
      console.log('📋 Found accessible resources:', resources)

      // Find Jira instances (they have scopes that include 'jira')
      const jiraResources = resources.filter((resource: any) => 
        resource.scopes?.some((scope: string) => scope.includes('jira')) || 
        resource.name?.toLowerCase().includes('jira')
      )

      if (jiraResources.length > 0) {
        // Use the first Jira instance found
        const primaryJira = jiraResources[0]
        const baseUrl = primaryJira.url
        
        console.log(`✅ Auto-discovered Jira instance: ${baseUrl} (${primaryJira.name})`)
        
        // Update config with discovered URL
        this.config.baseUrl = baseUrl
        this.config.cloudId = primaryJira.id
        
        // Store the discovered URL
        localStorage.setItem('jira_base_url', baseUrl)
        localStorage.setItem('jira_cloud_id', primaryJira.id)
        
        // If multiple instances, log them for debugging
        if (jiraResources.length > 1) {
          console.log(`ℹ️ Found ${jiraResources.length} Jira instances:`, 
            jiraResources.map((r: any) => ({ name: r.name, url: r.url }))
          )
        }
      } else {
        console.warn('⚠️ No Jira instances found in accessible resources')
      }

    } catch (error) {
      console.error('❌ Failed to discover Jira instances:', error)
      // Don't throw - authentication can still work with manual URL
    }
  }

  // Private helper methods

  private buildAuthUrl(state: string): string {
    const params = new URLSearchParams({
      audience: 'api.atlassian.com',
      client_id: this.config.clientId,
      scope: this.config.scope.join(' '),
      redirect_uri: this.config.redirectUri,
      state: state,
      response_type: 'code',
      prompt: 'select_account',
      max_age: '0'  // Force fresh authentication
    })

    return `https://auth.atlassian.com/authorize?${params.toString()}`
  }

  private async exchangeCodeForTokens(code: string): Promise<any> {
    // Use backend API for token exchange to keep client secret secure
    const backendUrl = import.meta.env.VITE_CODE_REVIEWER_API_URL || 'http://localhost:5002'
    const response = await fetch(`${backendUrl}/api/jira/exchange-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        code: code
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
      throw new Error(`Token exchange failed: ${response.status} - ${errorData.error || 'Unknown error'}`)
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Token exchange failed')
    }

    return data
  }

  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      return false
    }

    try {
      const tokenUrl = 'https://auth.atlassian.com/oauth/token'
      
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          grant_type: 'refresh_token',
          client_id: this.config.clientId,
          client_secret: this.config.clientSecret,
          refresh_token: this.refreshToken
        })
      })

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.status}`)
      }

      const tokenData = await response.json()
      
      this.accessToken = tokenData.access_token
      this.refreshToken = tokenData.refresh_token || this.refreshToken
      this.tokenExpiry = new Date(Date.now() + tokenData.expires_in * 1000)

      this.storeTokens()
      return true

    } catch (error) {
      console.error('❌ Failed to refresh token:', error)
      this.clearTokens()
      return false
    }
  }

  private async makeAuthenticatedRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
    // Check if token needs refresh
    if (this.tokenExpiry && this.tokenExpiry <= new Date(Date.now() + 60000)) { // Refresh 1 min before expiry
      const refreshed = await this.refreshAccessToken()
      if (!refreshed) {
        throw new Error('Unable to refresh access token')
      }
    }

    // Use backend API to avoid CORS issues
    const backendUrl = `${import.meta.env.VITE_CODE_REVIEWER_API_URL || 'http://localhost:5002'}/api/jira${endpoint}`
    
    const response = await fetch(backendUrl, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'X-Jira-Base-URL': this.config.baseUrl || 'https://regionalmediendigital.atlassian.net',
        'X-Jira-Cloud-ID': this.config.cloudId || '',
        'Accept': 'application/json',
        ...options.headers
      }
    })

    return response
  }

  private async fetchCurrentUser(): Promise<void> {
    try {
      const response = await this.makeAuthenticatedRequest('/user')
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          this.currentUser = data.user
        } else {
          throw new Error(data.error || 'Failed to fetch user')
        }
      }
    } catch (error) {
      console.error('❌ Failed to fetch current user:', error)
    }
  }

  private transformIssueData(issueData: any): JiraTicket {
    const fields = issueData.fields

    // Extract acceptance criteria from custom fields
    const acceptanceCriteria = this.extractAcceptanceCriteria(fields)

    return {
      id: issueData.id,
      key: issueData.key,
      summary: fields.summary,
      description: fields.description?.content ? this.extractTextFromADF(fields.description) : '',
      status: {
        name: fields.status.name,
        statusCategory: {
          key: fields.status.statusCategory.key,
          name: fields.status.statusCategory.name
        }
      },
      priority: {
        name: fields.priority?.name || 'None',
        iconUrl: fields.priority?.iconUrl || ''
      },
      assignee: {
        accountId: fields.assignee?.accountId || '',
        displayName: fields.assignee?.displayName || 'Unassigned',
        emailAddress: fields.assignee?.emailAddress || ''
      },
      reporter: {
        accountId: fields.reporter?.accountId || '',
        displayName: fields.reporter?.displayName || ''
      },
      created: fields.created,
      updated: fields.updated,
      customFields: this.extractCustomFields(fields),
      acceptanceCriteria
    }
  }

  private extractAcceptanceCriteria(fields: any): string[] {
    const criteria: string[] = []

    // Common custom field names for acceptance criteria
    const acFieldPatterns = [
      'customfield_10020', // Common AC field
      'customfield_10021',
      'customfield_10000',
      'acceptance criteria',
      'ac',
      'story points'
    ]

    for (const [fieldKey, fieldValue] of Object.entries(fields)) {
      if (acFieldPatterns.some(pattern => fieldKey.toLowerCase().includes(pattern.toLowerCase()))) {
        if (typeof fieldValue === 'string' && fieldValue.trim()) {
          // Split by common delimiters
          const items = fieldValue.split(/[\n\r•\-\*]/).filter(item => item.trim())
          criteria.push(...items.map(item => item.trim()))
        } else if (Array.isArray(fieldValue)) {
          criteria.push(...fieldValue.map(item => String(item).trim()))
        } else if (fieldValue && typeof fieldValue === 'object' && 'content' in fieldValue) {
          // ADF format
          const text = this.extractTextFromADF(fieldValue)
          if (text) {
            const items = text.split(/[\n\r•\-\*]/).filter(item => item.trim())
            criteria.push(...items.map(item => item.trim()))
          }
        }
      }
    }

    return criteria.filter(c => c.length > 0)
  }

  private extractTextFromADF(adfContent: any): string {
    // Simple ADF (Atlassian Document Format) text extraction
    if (typeof adfContent === 'string') {
      return adfContent
    }

    if (adfContent && adfContent.content && Array.isArray(adfContent.content)) {
      return adfContent.content
        .map((node: any) => {
          if (node.type === 'text') {
            return node.text
          } else if (node.content) {
            return this.extractTextFromADF(node)
          }
          return ''
        })
        .join(' ')
        .trim()
    }

    return ''
  }

  private extractCustomFields(fields: any): Record<string, any> {
    const customFields: Record<string, any> = {}
    
    for (const [key, value] of Object.entries(fields)) {
      if (key.startsWith('customfield_')) {
        customFields[key] = value
      }
    }

    return customFields
  }

  private generateRandomState(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  private storeTokens(): void {
    if (this.accessToken) {
      localStorage.setItem('jira_access_token', this.accessToken)
    }
    if (this.refreshToken) {
      localStorage.setItem('jira_refresh_token', this.refreshToken)
    }
    if (this.tokenExpiry) {
      localStorage.setItem('jira_token_expiry', this.tokenExpiry.toISOString())
    }
    if (this.config.cloudId) {
      localStorage.setItem('jira_cloud_id', this.config.cloudId)
    }
  }

  private loadStoredTokens(): void {
    this.accessToken = localStorage.getItem('jira_access_token')
    this.refreshToken = localStorage.getItem('jira_refresh_token')
    
    const expiryString = localStorage.getItem('jira_token_expiry')
    if (expiryString) {
      this.tokenExpiry = new Date(expiryString)
    }
    
    const cloudId = localStorage.getItem('jira_cloud_id')
    if (cloudId) {
      this.config.cloudId = cloudId
    }
  }

  private getStoredBaseUrl(): string | null {
    return localStorage.getItem('jira_base_url')
  }

  private clearTokens(): void {
    this.accessToken = null
    this.refreshToken = null
    this.tokenExpiry = null
    
    localStorage.removeItem('jira_access_token')
    localStorage.removeItem('jira_refresh_token')
    localStorage.removeItem('jira_token_expiry')
    localStorage.removeItem('jira_cloud_id')
  }
}

export const jiraAuthService = new JiraAuthService()