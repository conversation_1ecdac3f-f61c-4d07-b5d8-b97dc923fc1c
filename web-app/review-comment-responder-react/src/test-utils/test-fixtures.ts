/**
 * Test Fixtures for Multi-Agent Integration Tests
 * Pre-defined test scenarios and data for consistent testing
 */

import TestDataFactory from './test-data-factory'
import type {
  AgentType
} from '../types/multi-agent'
import type { 
  AgentStatus,
  ReviewFinding
} from '../types/enhanced-review'

// =================================
// BASIC FIXTURES
// =================================

/**
 * Standard test PR data
 */
export const TEST_PR_DATA = {
  basic: {
    id: 1,
    title: 'Add user authentication feature',
    branch: 'feature/user-authentication',
    repository: 'rma-mono/frontend',
    workspace: 'rma-workspace',
    author: 'test-developer',
    created_date: '2024-01-15T10:00:00Z',
    updated_date: '2024-01-15T15:30:00Z',
    comments: 3,
    state: 'OPEN',
    reviewers: ['reviewer1', 'reviewer2'],
    jira_ticket: 'PROJ-123'
  }
}

/**
 * Standard test ticket data
 */
export const TEST_TICKET_DATA = {
  withAC: {
    ticket_id: 'PROJ-123',
    summary: 'Implement user authentication system',
    status: 'In Progress',
    acceptance_criteria: [
      'User can register with email and password',
      'User can login with valid credentials', 
      'User session persists across browser sessions'
    ]
  }
}

// =================================
// MULTI-AGENT FIXTURES
// =================================

/**
 * Comprehensive fixtures collection
 */
export const FIXTURES = {
  // Basic entities
  pr: {
    basic: TEST_PR_DATA.basic
  },
  
  ticket: {
    withAC: TEST_TICKET_DATA.withAC
  },

  // UI component fixtures
  ui: {
    progressTracker: {
      running: TestDataFactory.createMultiAgentReviewStatus({
        status: 'running',
        progress: 45,
        agent_statuses: TestDataFactory.createAgentStatusSet({
          acceptance_criteria: { status: 'completed', progress: 100 },
          bug_detection: { status: 'running', progress: 75 },
          security_analysis: { status: 'pending', progress: 0 },
          logic_analysis: { status: 'pending', progress: 0 },
          quality_analysis: { status: 'pending', progress: 0 },
          architecture_analysis: { status: 'pending', progress: 0 },
          summary: { status: 'pending', progress: 0 }
        }),
        active_agents: ['bug_detection'],
        completed_agents: ['acceptance_criteria'],
        failed_agents: []
      }),

      completed: TestDataFactory.createMultiAgentReviewStatus({
        status: 'completed',
        progress: 100,
        agent_statuses: Object.fromEntries(
          TestDataFactory.ALL_AGENT_TYPES.map(agent => [
            agent,
            TestDataFactory.createAgentStatus(agent, { status: 'completed', progress: 100 })
          ])
        ),
        active_agents: [],
        completed_agents: TestDataFactory.ALL_AGENT_TYPES,
        failed_agents: []
      }),

      failed: TestDataFactory.createMultiAgentReviewStatus({
        status: 'failed',
        progress: 35,
        agent_statuses: TestDataFactory.createAgentStatusSet({
          acceptance_criteria: { status: 'completed', progress: 100 },
          bug_detection: { status: 'failed', progress: 25, error_message: 'Analysis timeout' },
          security_analysis: { status: 'failed', progress: 0, error_message: 'Service unavailable' }
        }),
        active_agents: [],
        completed_agents: ['acceptance_criteria'],
        failed_agents: ['bug_detection', 'security_analysis']
      })
    }
  },

  // Complete scenarios
  scenarios: {
    successful: TestDataFactory.createSuccessfulReviewScenario(),
    failed: TestDataFactory.createFailedReviewScenario(),
    partial: TestDataFactory.createPartialProgressScenario()
  },

  // Performance testing fixtures
  performance: {
    largeResultSet: TestDataFactory.createEnhancedReviewResults({
      structured_findings: {
        acceptance_criteria: Array.from({ length: 200 }, (_, i) => TestDataFactory.createFinding({
          text: `AC finding ${i}`,
          severity: 'medium'
        })),
        code_quality: Array.from({ length: 200 }, (_, i) => TestDataFactory.createFinding({
          text: `Quality finding ${i}`,
          severity: 'low'
        })),
        security_issues: Array.from({ length: 200 }, (_, i) => TestDataFactory.createFinding({
          text: `Security finding ${i}`,
          severity: 'high'
        })),
        performance_issues: Array.from({ length: 200 }, (_, i) => TestDataFactory.createFinding({
          text: `Performance finding ${i}`,
          severity: 'medium'
        })),
        bugs: Array.from({ length: 100 }, (_, i) => TestDataFactory.createFinding({
          text: `Bug finding ${i}`,
          severity: 'high'
        })),
        suggestions: Array.from({ length: 100 }, (_, i) => TestDataFactory.createFinding({
          text: `Suggestion ${i}`,
          severity: 'low'
        }))
      }
    }),

    manyAgentStatuses: Object.fromEntries(
      TestDataFactory.ALL_AGENT_TYPES.map((agent: AgentType) => [
        agent,
        TestDataFactory.createAgentStatus(agent, { status: 'running', progress: 50 })
      ])
    )
  }
}

// =================================
// MOCK DATA GENERATORS
// =================================

/**
 * Generate mock agent statuses with different states
 */
export const generateMockAgentStatuses = (count: number = 7): Record<string, AgentStatus> => {
  const statuses: Record<string, AgentStatus> = {}
  
  TestDataFactory.ALL_AGENT_TYPES.slice(0, count).forEach((agentType, index) => {
    const progressStates = ['pending', 'running', 'completed', 'failed'] as const
    const state = progressStates[index % progressStates.length]
    
    statuses[agentType] = TestDataFactory.createAgentStatus(agentType, {
      status: state,
      progress: state === 'completed' ? 100 : state === 'running' ? 75 : 0,
      error_message: state === 'failed' ? 'Mock failure message' : undefined
    })
  })
  
  return statuses
}

/**
 * Generate mock findings for testing
 */
export const generateMockFindings = (count: number = 10): ReviewFinding[] => {
  return Array.from({ length: count }, (_, i) =>
    TestDataFactory.createFinding({
      text: `Mock finding ${i + 1}`,
      severity: i % 3 === 0 ? 'high' : i % 2 === 0 ? 'medium' : 'low',
      file: `src/components/Component${Math.floor(i / 3) + 1}.tsx`,
      line: (i * 10) + 42
    })
  )
}

/**
 * Create mock WebSocket events sequence
 */
export const createMockWebSocketEvents = (reviewId: string = 'test-review-123') => {
  return [
    {
      review_id: reviewId,
      event_type: 'review_started' as const,
      timestamp: Date.now() - 10000,
      data: { agents_count: 7 }
    },
    {
      review_id: reviewId,
      event_type: 'agent_started' as const,
      agent_type: 'acceptance_criteria' as const,
      timestamp: Date.now() - 9000,
      data: { started_at: new Date().toISOString() }
    },
    {
      review_id: reviewId,
      event_type: 'agent_progress' as const,
      agent_type: 'acceptance_criteria' as const,
      timestamp: Date.now() - 8000,
      data: { progress: 50 }
    },
    {
      review_id: reviewId,
      event_type: 'agent_completed' as const,
      agent_type: 'acceptance_criteria' as const,
      timestamp: Date.now() - 7000,
      data: { 
        findings_count: 3,
        execution_time: 45.2 
      }
    },
    {
      review_id: reviewId,
      event_type: 'review_completed' as const,
      timestamp: Date.now() - 1000,
      data: { 
        total_time: 165.8,
        total_findings: 15 
      }
    }
  ]
}

// =================================
// EXPORT SHORTCUTS
// =================================

// Export commonly used fixtures for easy access
export const {
  scenarios: { successful: SUCCESSFUL_SCENARIO, failed: FAILED_SCENARIO, partial: PARTIAL_SCENARIO },
  ui: { progressTracker: PROGRESS_TRACKER_FIXTURES },
  performance: PERFORMANCE_FIXTURES
} = FIXTURES

// Default export for convenience
export default FIXTURES