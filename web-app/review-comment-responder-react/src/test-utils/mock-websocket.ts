/**
 * Mock WebSocket Implementation for Testing
 * Simulates WebSocket behavior for Multi-Agent integration tests
 */

import type { AgentType } from '../types/multi-agent'
import TestDataFactory from './test-data-factory'

// Mock event interface for WebSocket simulation
interface MockWebSocketEvent {
  review_id: string
  event_type: string
  agent_type?: AgentType
  timestamp: number
  data: Record<string, unknown>
}

/**
 * Mock WebSocket Class
 */
export class MockWebSocket extends EventTarget {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readonly CONNECTING = 0
  readonly OPEN = 1
  readonly CLOSING = 2
  readonly CLOSED = 3

  readyState: number = MockWebSocket.CONNECTING
  url: string
  protocol: string = ''
  extensions: string = ''
  binaryType: BinaryType = 'blob'

  private _isConnected: boolean = false
  private _shouldReconnect: boolean = true
  private _reconnectAttempts: number = 0
  private _maxReconnectAttempts: number = 3
  private _reconnectDelay: number = 1000

  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(url: string, _protocols?: string | string[]) {
    super()
    this.url = url
    
    // Simulate connection establishment
    setTimeout(() => {
      this._connect()
    }, 100)
  }

  private _connect(): void {
    this.readyState = MockWebSocket.OPEN
    this._isConnected = true
    this._reconnectAttempts = 0
    
    const openEvent = new Event('open')
    this.dispatchEvent(openEvent)
    if (this.onopen) this.onopen(openEvent)
  }

  send(data: string | ArrayBuffer | Blob | ArrayBufferView): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open')
    }
    
    // Echo confirmation for subscription messages
    try {
      const parsed = JSON.parse(data as string)
      if (parsed.type === 'subscribe') {
        setTimeout(() => {
          const confirmationEvent = new MessageEvent('message', {
            data: JSON.stringify({
              type: 'subscription_confirmed',
              channel: parsed.channel,
              review_id: parsed.review_id
            })
          })
          this.dispatchEvent(confirmationEvent)
          if (this.onmessage) this.onmessage(confirmationEvent)
        }, 50)
      }
    } catch {
      // Ignore parsing errors for non-JSON messages
    }
  }

  close(code?: number, reason?: string): void {
    if (this.readyState === MockWebSocket.CLOSED || this.readyState === MockWebSocket.CLOSING) {
      return
    }
    
    this.readyState = MockWebSocket.CLOSING
    
    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED
      this._isConnected = false
      
      const closeEvent = new CloseEvent('close', { code: code || 1000, reason: reason || '' })
      this.dispatchEvent(closeEvent)
      if (this.onclose) this.onclose(closeEvent)
    }, 10)
  }

  // =================================
  // TEST HELPER METHODS
  // =================================

  /**
   * Simulate connection issues
   */
  simulateConnectionIssues(): void {
    if (this._isConnected) {
      this.readyState = MockWebSocket.CLOSED
      this._isConnected = false
      
      const closeEvent = new CloseEvent('close', { code: 1006, reason: 'Connection lost' })
      this.dispatchEvent(closeEvent)
      if (this.onclose) this.onclose(closeEvent)
      
      // Attempt reconnection
      if (this._shouldReconnect && this._reconnectAttempts < this._maxReconnectAttempts) {
        this._reconnectAttempts++
        setTimeout(() => {
          this._connect()
        }, this._reconnectDelay * this._reconnectAttempts)
      }
    }
  }

  /**
   * Simulate a single agent event
   */
  simulateAgentEvent(reviewId: string, agentType: AgentType, eventType: string, data: Record<string, unknown> = {}): void {
    if (!this._isConnected) return
    
    const event = new MessageEvent('message', {
      data: JSON.stringify({
        review_id: reviewId,
        event_type: eventType,
        agent_type: agentType,
        timestamp: Date.now(),
        data
      })
    })
    
    this.dispatchEvent(event)
    if (this.onmessage) this.onmessage(event)
  }

  /**
   * Simulate a complete successful review workflow
   */
  simulateSuccessfulReview(reviewId: string): void {
    const agents = TestDataFactory.ALL_AGENT_TYPES
    let delay = 0

    // Review started
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'review_started',
        timestamp: Date.now(),
        data: { agents_count: agents.length }
      })
    }, delay)
    delay += 500

    // Agents start in sequence
    agents.forEach((agent, index) => {
      setTimeout(() => {
        this._sendEvent({
          review_id: reviewId,
          event_type: 'agent_started',
          agent_type: agent,
          timestamp: Date.now(),
          data: { started_at: new Date().toISOString() }
        })
      }, delay + index * 200)
    })
    delay += agents.length * 200 + 1000

    // Progress updates
    agents.forEach((agent, index) => {
      setTimeout(() => {
        this._sendEvent({
          review_id: reviewId,
          event_type: 'agent_progress',
          agent_type: agent,
          timestamp: Date.now(),
          data: { progress: 50 }
        })
      }, delay + index * 100)
      
      setTimeout(() => {
        this._sendEvent({
          review_id: reviewId,
          event_type: 'agent_progress',
          agent_type: agent,
          timestamp: Date.now(),
          data: { progress: 100 }
        })
      }, delay + index * 100 + 500)
    })
    delay += agents.length * 100 + 1000

    // Agents complete
    agents.forEach((agent, index) => {
      setTimeout(() => {
        this._sendEvent({
          review_id: reviewId,
          event_type: 'agent_completed',
          agent_type: agent,
          timestamp: Date.now(),
          data: { 
            findings_count: Math.floor(Math.random() * 5) + 1,
            execution_time: Math.random() * 60 + 20
          }
        })
      }, delay + index * 150)
    })
    delay += agents.length * 150 + 500

    // Review completed
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'review_completed',
        timestamp: Date.now(),
        data: { 
          total_time: 165.8,
          total_findings: 15,
          success: true
        }
      })
    }, delay)
  }

  /**
   * Simulate a failed review workflow
   */
  simulateFailedReview(reviewId: string): void {
    const agents = TestDataFactory.ALL_AGENT_TYPES.slice(0, 3) // Only use first 3 agents
    let delay = 0

    // Review started
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'review_started',
        timestamp: Date.now(),
        data: { agents_count: agents.length }
      })
    }, delay)
    delay += 500

    // First agent succeeds
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'agent_started',
        agent_type: agents[0],
        timestamp: Date.now(),
        data: { started_at: new Date().toISOString() }
      })
    }, delay)
    delay += 200

    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'agent_completed',
        agent_type: agents[0],
        timestamp: Date.now(),
        data: { findings_count: 2, execution_time: 35.2 }
      })
    }, delay)
    delay += 500

    // Second agent fails
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'agent_started',
        agent_type: agents[1],
        timestamp: Date.now(),
        data: { started_at: new Date().toISOString() }
      })
    }, delay)
    delay += 200

    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'agent_failed',
        agent_type: agents[1],
        timestamp: Date.now(),
        data: { 
          error_message: 'Analysis timeout - code complexity too high',
          partial_progress: 25
        }
      })
    }, delay)
    delay += 300

    // Third agent fails
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'agent_failed',
        agent_type: agents[2],
        timestamp: Date.now(),
        data: { 
          error_message: 'Security analysis service unavailable',
          partial_progress: 0
        }
      })
    }, delay)
    delay += 200

    // Review failed
    setTimeout(() => {
      this._sendEvent({
        review_id: reviewId,
        event_type: 'review_failed',
        timestamp: Date.now(),
        data: { 
          failed_agents: [agents[1], agents[2]],
          completed_agents: [agents[0]],
          partial_results: true
        }
      })
    }, delay)
  }

  private _sendEvent(event: MockWebSocketEvent): void {
    if (!this._isConnected) return
    
    const messageEvent = new MessageEvent('message', {
      data: JSON.stringify(event)
    })
    
    this.dispatchEvent(messageEvent)
    if (this.onmessage) this.onmessage(messageEvent)
  }
}

/**
 * WebSocket Factory for managing instances
 */
export class MockWebSocketFactory {
  private static instances: MockWebSocket[] = []

  static create(url: string, protocols?: string | string[]): MockWebSocket {
    const instance = new MockWebSocket(url, protocols)
    this.instances.push(instance)
    return instance
  }

  static getInstances(): MockWebSocket[] {
    return [...this.instances]
  }

  static clear(): void {
    this.instances.forEach(instance => instance.close())
    this.instances = []
  }
}

/**
 * Setup WebSocket mock for tests
 */
export const setupWebSocketMock = (): (() => void) => {
  const originalWebSocket = global.WebSocket
  
  // Replace global WebSocket with mock
  global.WebSocket = MockWebSocket as any
  
  // Return cleanup function
  return () => {
    global.WebSocket = originalWebSocket
    MockWebSocketFactory.clear()
  }
}

// Default export
export default MockWebSocket