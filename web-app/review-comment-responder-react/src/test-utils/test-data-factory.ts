/**
 * Test Data Factory for Multi-Agent Integration Tests
 * Provides consistent test data for all Multi-Agent scenarios
 */

import type { 
  MultiAgentReviewRequest,
  MultiAgentReviewResponse,
  ReviewStatusResponse,
  AgentResult,
  AgentType
} from '../types/multi-agent'
import type { 
  AgentStatus,
  ReviewSession,
  ReviewFinding,
  EnhancedReviewResults
} from '../types/enhanced-review'
import type { MultiAgentReviewStatus } from '../components/MultiAgentProgressTracker'

// Helper function to generate unique IDs
const generateId = (prefix: string): string => `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

// Helper function to generate timestamps
const generateTimestamp = (offsetSeconds: number = 0): string => {
  const date = new Date()
  date.setSeconds(date.getSeconds() + offsetSeconds)
  return date.toISOString()
}

// Agent Types for consistent testing
export const ALL_AGENT_TYPES: AgentType[] = [
  'acceptance_criteria',
  'bug_detection',
  'security_analysis',
  'logic_analysis',
  'quality_analysis',
  'architecture_analysis',
  'summary'
]

/**
 * Test Data Factory Class
 */
export class TestDataFactory {
  static ALL_AGENT_TYPES = ALL_AGENT_TYPES
  
  // =================================
  // BASIC DATA BUILDERS
  // =================================

  /**
   * Create a test finding
   */
  static createFinding(partial: Partial<ReviewFinding> = {}): ReviewFinding {
    return {
      text: partial.text || 'Test finding',
      severity: partial.severity || 'medium',
      file: partial.file || 'src/test.ts',
      line: partial.line || 42,
      suggestion: partial.suggestion || 'Consider refactoring',
      ...partial
    }
  }

  /**
   * Create a test agent status
   */
  static createAgentStatus(agentType: AgentType, overrides: Partial<AgentStatus> = {}): AgentStatus {
    return {
      agent_type: agentType,
      status: 'pending',
      progress: 0,
      started_at: undefined,
      completed_at: undefined,
      error_message: undefined,
      ...overrides
    }
  }

  /**
   * Create a complete set of agent statuses
   */
  static createAgentStatusSet(statusOverrides: Partial<Record<AgentType, Partial<AgentStatus>>> = {}): Record<AgentType, AgentStatus> {
    const statuses: Record<AgentType, AgentStatus> = {} as Record<AgentType, AgentStatus>
    
    ALL_AGENT_TYPES.forEach(agentType => {
      statuses[agentType] = this.createAgentStatus(agentType, statusOverrides[agentType])
    })
    
    return statuses
  }

  /**
   * Create a test agent result
   */
  static createAgentResult(agentType: AgentType, overrides: Partial<AgentResult> = {}): AgentResult {
    return {
      agent_type: agentType,
      status: 'completed',
      execution_time: 45.2,
      result_data: {
        model_used: 'claude-3-sonnet',
        temperature: 0.1,
        max_tokens: 4000
      },
      findings: [
        {
          type: `${agentType}_finding`,
          severity: 'medium' as const,
          description: `${agentType} finding 1`,
          file: 'src/test.ts',
          line: 42
        },
        {
          type: `${agentType}_finding`,
          severity: 'low' as const,
          description: `${agentType} finding 2`,
          file: 'src/test.ts',
          line: 45
        }
      ],
      recommendations: [`${agentType} recommendation 1`, `${agentType} recommendation 2`],
      metadata: {
        analysis_depth: 'standard',
        confidence: 0.85
      },
      ...overrides
    }
  }

  // =================================
  // MULTI-AGENT REVIEW DATA
  // =================================

  /**
   * Create a multi-agent review request
   */
  static createMultiAgentReviewRequest(overrides: Partial<MultiAgentReviewRequest> = {}): MultiAgentReviewRequest {
    return {
      branch_name: 'feature/test-branch',
      pr_url: 'https://bitbucket.org/test/repo/pull-requests/123',
      repository_path: '/test/repo/path',
      review_mode: 'parallel',
      agent_config: {
        enabled_agents: ALL_AGENT_TYPES,
        timeout_seconds: 300,
        priority: 'normal' as const,
        concurrent_agents: 7
      },
      ...overrides
    }
  }

  /**
   * Create a multi-agent review response
   */
  static createMultiAgentReviewResponse(overrides: Partial<MultiAgentReviewResponse> = {}): MultiAgentReviewResponse {
    return {
      review_id: generateId('review'),
      session_id: generateId('session'),
      status: 'started',
      initial_agent_statuses: this.createAgentStatusSet(),
      estimated_completion_time: 180,
      websocket_session_id: generateId('websocket'),
      context_preparation_status: {},
      created_at: generateTimestamp(),
      ...overrides
    }
  }

  /**
   * Create a review status response
   */
  static createReviewStatusResponse(overrides: Partial<ReviewStatusResponse> = {}): ReviewStatusResponse {
    return {
      review_id: generateId('review'),
      session_id: generateId('session'),
      status: 'running',
      progress: 45,
      agent_statuses: this.createAgentStatusSet({
        acceptance_criteria: { status: 'completed', progress: 100 },
        bug_detection: { status: 'running', progress: 75 },
        security_analysis: { status: 'pending', progress: 0 }
      }),
      active_agents: ['bug_detection'],
      completed_agents: ['acceptance_criteria'],
      failed_agents: [],
      started_at: generateTimestamp(-300),
      ...overrides
    }
  }

  /**
   * Create enhanced review results
   */
  static createEnhancedReviewResults(overrides: Partial<EnhancedReviewResults> = {}): EnhancedReviewResults {
    return {
      session_id: generateId('session'),
      review_mode: 'multi_agent',
      branch_name: 'feature/test-branch',
      pr_url: 'https://bitbucket.org/test/repo/pull-requests/123',
      worktree_path: '/test/worktree/path',
      timestamp: generateTimestamp(),
      raw_review: '# Multi-Agent Review Results\n\nReview completed successfully.',
      metadata: {
        changed_files: ['src/component.tsx', 'src/utils.ts'],
        diff_summary: 'Added new component and utility functions',
        file_count: 2
      },
      jira_ticket: {
        ticket_id: 'PROJ-123',
        summary: 'Implement new feature',
        status: 'In Progress',
        acceptance_criteria_count: 3,
        acceptance_criteria: [
          'User can perform action A',
          'System validates input B',
          'Error handling for case C'
        ]
      },
      structured_findings: {
        acceptance_criteria: [this.createFinding({ text: 'AC finding 1' })],
        code_quality: [this.createFinding({ text: 'Quality finding 1' })],
        security_issues: [this.createFinding({ text: 'Security finding 1', severity: 'high' })],
        performance_issues: [this.createFinding({ text: 'Performance finding 1' })],
        bugs: [this.createFinding({ text: 'Bug finding 1', severity: 'high' })],
        suggestions: [this.createFinding({ text: 'Suggestion 1', severity: 'low' })]
      },
      summary: {
        total_findings: 10,
        high_severity_count: 3,
        files_changed: 2,
        review_length: 1500,
        categories: {
          'acceptance_criteria': 2,
          'code_quality': 2,
          'security_issues': 3,
          'performance_issues': 1,
          'bugs': 1,
          'suggestions': 1
        },
        completion_status: 'completed'
      },
      ...overrides
    }
  }

  /**
   * Create a multi-agent review status for progress tracker
   */
  static createMultiAgentReviewStatus(overrides: Partial<MultiAgentReviewStatus> = {}): MultiAgentReviewStatus {
    return {
      review_id: generateId('review'),
      status: 'running',
      progress: 45,
      agent_statuses: this.createAgentStatusSet({
        acceptance_criteria: { status: 'completed', progress: 100 },
        bug_detection: { status: 'running', progress: 75 },
        security_analysis: { status: 'pending', progress: 0 }
      }),
      active_agents: ['bug_detection'],
      completed_agents: ['acceptance_criteria'],
      failed_agents: [],
      started_at: generateTimestamp(-300),
      ...overrides
    }
  }

  /**
   * Create a review session
   */
  static createReviewSession(overrides: Partial<ReviewSession> = {}): ReviewSession {
    return {
      session_id: generateId('session'),
      branch_name: 'feature/test-branch',
      status: 'running',
      review_mode: 'multi_agent',
      progress: 45,
      created_at: generateTimestamp(-300),
      results: undefined,
      ...overrides
    }
  }

  // =================================
  // SCENARIO BUILDERS
  // =================================

  /**
   * Create a successful review scenario
   */
  static createSuccessfulReviewScenario() {
    return {
      request: this.createMultiAgentReviewRequest(),
      response: this.createMultiAgentReviewResponse({ status: 'started' }),
      status: this.createReviewStatusResponse({ 
        status: 'completed',
        progress: 100,
        agent_statuses: this.createAgentStatusSet(
          ALL_AGENT_TYPES.reduce((acc, agent) => {
            acc[agent] = { status: 'completed', progress: 100 }
            return acc
          }, {} as Partial<Record<AgentType, Partial<AgentStatus>>>)
        ),
        completed_agents: ALL_AGENT_TYPES,
        active_agents: [],
        failed_agents: []
      }),
      results: this.createEnhancedReviewResults()
    }
  }

  /**
   * Create a failed review scenario
   */
  static createFailedReviewScenario() {
    return {
      request: this.createMultiAgentReviewRequest(),
      response: this.createMultiAgentReviewResponse({ status: 'started' }),
      status: this.createReviewStatusResponse({
        status: 'failed',
        progress: 35,
        agent_statuses: this.createAgentStatusSet({
          acceptance_criteria: { status: 'completed', progress: 100 },
          bug_detection: { status: 'failed', progress: 25, error_message: 'Analysis timeout' },
          security_analysis: { status: 'failed', progress: 0, error_message: 'Service unavailable' }
        }),
        completed_agents: ['acceptance_criteria'],
        active_agents: [],
        failed_agents: ['bug_detection', 'security_analysis']
      }),
      results: null
    }
  }

  /**
   * Create a partial progress scenario
   */
  static createPartialProgressScenario() {
    return {
      request: this.createMultiAgentReviewRequest(),
      response: this.createMultiAgentReviewResponse({ status: 'started' }),
      status: this.createReviewStatusResponse({
        status: 'running',
        progress: 65,
        agent_statuses: this.createAgentStatusSet({
          acceptance_criteria: { status: 'completed', progress: 100 },
          bug_detection: { status: 'completed', progress: 100 },
          security_analysis: { status: 'running', progress: 80 },
          logic_analysis: { status: 'running', progress: 45 },
          quality_analysis: { status: 'pending', progress: 0 },
          architecture_analysis: { status: 'pending', progress: 0 },
          summary: { status: 'pending', progress: 0 }
        }),
        completed_agents: ['acceptance_criteria', 'bug_detection'],
        active_agents: ['security_analysis', 'logic_analysis'],
        failed_agents: []
      }),
      results: null
    }
  }
}

// Export default instance for convenience
export default TestDataFactory