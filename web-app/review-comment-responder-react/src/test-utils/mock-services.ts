/**
 * Mock Services for Multi-Agent Integration Tests
 * Provides consistent mock implementations for all services
 */

import { vi } from 'vitest'
// Types are only used in method return types - keeping import for compilation
import TestDataFactory from './test-data-factory'

// =================================
// MULTI-AGENT SERVICE MOCKS
// =================================

/**
 * Mock MultiAgentReviewService implementation
 */
export class MockMultiAgentReviewService {
  // Mock methods
  healthCheck = vi.fn()
  startParallelReview = vi.fn()
  getReviewStatus = vi.fn()
  getReviewResults = vi.fn()
  cancelReview = vi.fn()
  
  // Utility methods
  extractJiraTicketFromBranch = vi.fn()
  getDefaultRepositoryPath = vi.fn()
  createReviewRequest = vi.fn()

  scenario: 'success' | 'failure' | 'timeout' | 'partial'

  constructor(scenario: 'success' | 'failure' | 'timeout' | 'partial' = 'success') {
    this.scenario = scenario
    this.setupDefaultBehavior()
  }

  private setupDefaultBehavior() {
    // Health check - always healthy in tests unless specified
    this.healthCheck.mockResolvedValue(true)
    
    // Default repository path
    this.getDefaultRepositoryPath.mockReturnValue('/Users/<USER>/dev/rma-mono')
    
    // Jira ticket extraction
    this.extractJiraTicketFromBranch.mockImplementation((branchName: string) => {
      const match = branchName.match(/([A-Z]+-\d+)/)
      return match ? match[1] : null
    })

    // Create review request
    this.createReviewRequest.mockImplementation(() => {
      return TestDataFactory.createMultiAgentReviewRequest()
    })

    // Setup scenario-specific behavior
    switch (this.scenario) {
      case 'success':
        this.setupSuccessScenario()
        break
      case 'failure':
        this.setupFailureScenario()
        break
      case 'timeout':
        this.setupTimeoutScenario()
        break
      case 'partial':
        this.setupPartialScenario()
        break
    }
  }

  private setupSuccessScenario() {
    // Start review succeeds
    this.startParallelReview.mockResolvedValue({
      success: true,
      data: TestDataFactory.createMultiAgentReviewResponse({ status: 'started' })
    })

    // Status check returns running then completed
    this.getReviewStatus.mockResolvedValueOnce({
      success: true,
      data: TestDataFactory.createReviewStatusResponse({ status: 'running', progress: 45 })
    }).mockResolvedValue({
      success: true,
      data: TestDataFactory.createReviewStatusResponse({ status: 'completed', progress: 100 })
    })

    // Results available
    this.getReviewResults.mockResolvedValue({
      success: true,
      data: TestDataFactory.createEnhancedReviewResults()
    })

    // Cancel succeeds
    this.cancelReview.mockResolvedValue({
      success: true,
      data: { message: 'Review cancelled successfully' }
    })
  }

  private setupFailureScenario() {
    // Start review fails
    this.startParallelReview.mockRejectedValue(
      new Error('Multi-agent service unavailable')
    )

    // Status check fails
    this.getReviewStatus.mockRejectedValue(
      new Error('Service unavailable')
    )

    // No results available
    this.getReviewResults.mockRejectedValue(
      new Error('No results available')
    )
  }

  private setupTimeoutScenario() {
    // Start review times out
    this.startParallelReview.mockImplementation(() => 
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), 5000)
      )
    )
  }

  private setupPartialScenario() {
    // Start review succeeds
    this.startParallelReview.mockResolvedValue({
      success: true,
      data: TestDataFactory.createMultiAgentReviewResponse({ status: 'started' })
    })

    // Status shows partial completion
    this.getReviewStatus.mockResolvedValue({
      success: true,
      data: TestDataFactory.createReviewStatusResponse({
        status: 'running',
        progress: 65,
        agent_statuses: TestDataFactory.createAgentStatusSet({
          acceptance_criteria: { status: 'completed', progress: 100 },
          bug_detection: { status: 'completed', progress: 100 },
          security_analysis: { status: 'running', progress: 80 },
          logic_analysis: { status: 'running', progress: 45 },
          quality_analysis: { status: 'pending', progress: 0 },
          architecture_analysis: { status: 'pending', progress: 0 },
          summary: { status: 'pending', progress: 0 }
        }),
        completed_agents: ['acceptance_criteria', 'bug_detection'],
        active_agents: ['security_analysis', 'logic_analysis'],
        failed_agents: []
      })
    })
  }
}

/**
 * Mock CodeReviewerService implementation
 */
export class MockCodeReviewerService {
  startReview = vi.fn()
  getReviewStatus = vi.fn()
  cancelReview = vi.fn()

  constructor() {
    // Legacy service always works as fallback
    this.startReview.mockResolvedValue({
      success: true,
      data: { session_id: 'legacy-session-123', status: 'started' }
    })

    this.getReviewStatus.mockResolvedValue({
      success: true,
      data: { session_id: 'legacy-session-123', status: 'completed', progress: 100 }
    })

    this.cancelReview.mockResolvedValue({
      success: true,
      data: { message: 'Review cancelled' }
    })
  }
}

// =================================
// HOOK MOCKS
// =================================

/**
 * Create mock useMultiAgentReview hook
 */
export const createMockMultiAgentReviewHook = (scenario: 'idle' | 'running' | 'completed' | 'failed' = 'idle') => {
  const baseHook = {
    // State
    status: TestDataFactory.createMultiAgentReviewStatus({ 
      status: scenario === 'idle' ? 'completed' : scenario as any
    }),
    results: scenario === 'completed' ? TestDataFactory.createEnhancedReviewResults() : null,
    isLoading: scenario === 'running',
    error: scenario === 'failed' ? new Error('Review failed') : null,
    
    // Actions
    startReview: vi.fn(),
    cancelReview: vi.fn(),
    refreshStatus: vi.fn(),
    clearError: vi.fn(),
    
    // Config
    config: {
      enable_websocket: true,
      polling_interval: 5000,
      enable_debug_logs: false
    }
  }

  // Setup scenario-specific behavior
  switch (scenario) {
    case 'running':
      baseHook.startReview.mockResolvedValue({
        success: true,
        data: TestDataFactory.createMultiAgentReviewResponse()
      })
      break
    case 'failed':
      baseHook.startReview.mockRejectedValue(new Error('Service unavailable'))
      break
    default:
      baseHook.startReview.mockResolvedValue({
        success: true,
        data: TestDataFactory.createMultiAgentReviewResponse()
      })
  }

  return baseHook
}

/**
 * Create mock WebSocket hook
 */
export const createMockWebSocketHook = () => ({
  isConnected: true,
  hasError: false,
  connectionStatus: { connected: true, lastError: null },
  currentService: 'multi-agent' as const,
  connect: vi.fn(),
  disconnect: vi.fn(),
  switchService: vi.fn()
})

// =================================
// COMPLETE SERVICE SUITE
// =================================

/**
 * Create a complete mock service suite for components
 */
export const createMockServiceSuite = (scenario: 'success' | 'failure' = 'success') => {
  return {
    // Services
    multiAgentService: new MockMultiAgentReviewService(scenario),
    codeReviewerService: new MockCodeReviewerService(),
    
    // Hooks
    multiAgentHook: createMockMultiAgentReviewHook(scenario === 'success' ? 'completed' : 'failed'),
    webSocketHook: createMockWebSocketHook(),
    
    // Store states
    store: {
      selectedPR: null,
      selectedTicket: null,
      mode: 'quick',
      repositoryPath: '/test/repo/path',
      activeSession: null,
      progress: 0,
      
      // Actions
      selectPR: vi.fn(),
      selectTicket: vi.fn(),
      setMode: vi.fn(),
      addSession: vi.fn(),
      updateSession: vi.fn()
    },
    
    // Context states
    authStatus: {
      isAuthenticated: true,
      user: { username: 'test-user', email: '<EMAIL>' },
      login: vi.fn(),
      logout: vi.fn()
    },
    
    worktreeStatus: {
      isConfigured: true,
      isValid: true,
      path: '/test/worktree/path',
      configure: vi.fn(),
      refresh: vi.fn()
    }
  }
}

// =================================
// SCENARIO BUILDERS
// =================================

/**
 * Create mock data for different test scenarios
 */
export const createMockScenarios = () => ({
  successful: {
    multiAgentService: new MockMultiAgentReviewService('success'),
    hook: createMockMultiAgentReviewHook('completed'),
    webSocket: createMockWebSocketHook()
  },
  
  failed: {
    multiAgentService: new MockMultiAgentReviewService('failure'),
    hook: createMockMultiAgentReviewHook('failed'),
    webSocket: { ...createMockWebSocketHook(), isConnected: false, hasError: true }
  },
  
  partial: {
    multiAgentService: new MockMultiAgentReviewService('partial'),
    hook: createMockMultiAgentReviewHook('running'),
    webSocket: createMockWebSocketHook()
  },
  
  timeout: {
    multiAgentService: new MockMultiAgentReviewService('timeout'),
    hook: createMockMultiAgentReviewHook('idle'),
    webSocket: createMockWebSocketHook()
  }
})

// =================================
// EXPORTS
// =================================

export default {
  MockMultiAgentReviewService,
  MockCodeReviewerService,
  createMockMultiAgentReviewHook,
  createMockWebSocketHook,
  createMockServiceSuite,
  createMockScenarios
}