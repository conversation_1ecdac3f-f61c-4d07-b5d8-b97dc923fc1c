import React, { useEffect, useState } from 'react'
import { Bo<PERSON>, Play, Trash2, Download, Copy, MessageSquare } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { CommentCard } from '../components/CommentCard'
import { HybridReviewInterface } from '../components/HybridReviewInterface'
import { useCommentStore } from '../store/useCommentStore'
import { ApiService } from '../services/api'
// import { useAuthStatus } from '../hooks/useAuth'

export const CodeReviewResponder: React.FC = () => {
  const { 
    comments, 
    prContext, 
    isBackendAvailable, 
    setBackendAvailable,
    updateComment,
    clearAllComments,
    getPendingComments,
    getCompletedComments,
    getStats
  } = useCommentStore()

  // const { isAuthenticated } = useAuthStatus()
  const [isProcessingAll, setIsProcessingAll] = useState(false)

  // Check backend health on startup
  useEffect(() => {
    const checkBackend = async () => {
      const available = await ApiService.checkBackendHealth()
      setBackendAvailable(available)
      
      if (!available) {
        console.warn('Backend not available - running in demo mode')
      } else {
        console.log('✅ Backend API available')
      }
    }
    
    checkBackend()
  }, [setBackendAvailable])

  const processComment = async (commentId: number, modificationPrompt?: string) => {
    const comment = comments.find(c => c.id === commentId)
    if (!comment || (comment.status !== 'pending' && !modificationPrompt)) return

    updateComment(commentId, { status: 'processing' })

    try {
      const contextWithModification = modificationPrompt 
        ? { ...prContext, modificationPrompt }
        : prContext

      let response
      if (isBackendAvailable) {
        response = await ApiService.processComment(comment, contextWithModification)
      } else {
        response = await ApiService.simulateClaudeResponse(comment, contextWithModification)
      }

      updateComment(commentId, {
        status: 'completed',
        response: response.response,
        extractedData: {
          file: response.response?.file || 'Unknown',
          line: response.response?.line || 'Unknown'
        },
        session_id: response.session_id,
        previewChanges: response.response?.preview_changes,
        needsApproval: response.response?.preview_mode,
        pendingCodeChanges: response.response?.code_changes
      })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      let displayError = errorMessage
      
      // Handle specific error types
      if (errorMessage.includes('Claude Code timed out')) {
        displayError = 'Request timed out (10+ minutes). The screenshot might be too complex or Claude Code is experiencing issues.'
      } else if (errorMessage.includes('INTERNAL SERVER ERROR')) {
        displayError = 'Server error occurred. Please try again or check the backend logs.'
      } else if (errorMessage.includes('Could not process image')) {
        displayError = 'Invalid or corrupted image. Please upload a clear screenshot.'
      }
      
      updateComment(commentId, {
        status: 'error',
        response: {
          text: `Error: ${displayError}`,
          confidence: 0,
          file: 'Unknown',
          line: 'Unknown'
        }
      })
    }
  }

  const processAllComments = async () => {
    const pendingComments = getPendingComments()

    if (pendingComments.length === 0) {
      console.warn('No pending comments to process')
      return
    }

    setIsProcessingAll(true)

    try {
      console.log(`Processing ${pendingComments.length} comments...`)

      // Process all comments in parallel (Claude Code supports parallel sessions)
      const processPromises = pendingComments.map(comment => 
        processComment(comment.id).catch(error => {
          console.error(`Failed to process comment ${comment.id}:`, error)
        })
      )

      await Promise.all(processPromises)

      const stats = getStats()
      const successful = stats.completed
      const failed = stats.error

      if (failed > 0) {
        console.warn(`Processed ${successful} comments, ${failed} failed`)
      } else {
        console.log(`All ${successful} comments processed successfully!`)
      }
    } finally {
      setIsProcessingAll(false)
    }
  }

  const regenerateResponse = async (commentId: number) => {
    updateComment(commentId, { 
      status: 'pending', 
      response: null,
      previewChanges: undefined,
      needsApproval: false,
      pendingCodeChanges: undefined
    })
    
    await processComment(commentId)
  }

  const exportResponses = () => {
    const completedComments = getCompletedComments().filter(c => c.response)

    if (completedComments.length === 0) {
      console.warn('No responses to export')
      return
    }

    let markdown = `# Code Review Responses\n\n`
    markdown += `**PR:** ${prContext.prUrl || 'N/A'}\n`
    markdown += `**Branch:** ${prContext.branchName || 'N/A'}\n`
    markdown += `**Ticket:** ${prContext.ticketDescription ? prContext.ticketDescription.substring(0, 100) + (prContext.ticketDescription.length > 100 ? '...' : '') : 'N/A'}\n`
    markdown += `**Generated:** ${new Date().toLocaleString()}\n\n`

    completedComments.forEach((comment, i) => {
      const response = comment.response!
      markdown += `## Comment ${i + 1}\n\n`
      markdown += `**File:** \`${response.file}:${response.line}\`\n`
      markdown += `**Comment:** ${response.commentText || response.comment_text || 'N/A'}\n\n`
      markdown += `**Response:**\n${response.text || response.response}\n\n`
      markdown += `---\n\n`
    })

    const blob = new Blob([markdown], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `review-responses-${new Date().toISOString().split('T')[0]}.md`
    a.click()

    console.log('Responses exported successfully!')
  }

  const copyAllResponses = async () => {
    const completedComments = getCompletedComments().filter(c => c.response)

    if (completedComments.length === 0) {
      console.warn('No responses to copy')
      return
    }

    const responsesText = completedComments
      .map(c => `**${c.response!.file}:${c.response!.line}**\n${c.response!.text || c.response!.response}`)
      .join('\n\n---\n\n')

    await navigator.clipboard.writeText(responsesText)
    console.log('All responses copied to clipboard!')
  }

  const handleClearAll = () => {
    if (comments.length === 0) return

    if (confirm('Are you sure you want to clear all comments?')) {
      clearAllComments()
      console.log('All comments cleared')
    }
  }

  const stats = getStats()

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl animate-fade-in relative z-10">
      {/* Header */}
      <Card className="mb-8 overflow-hidden relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5"></div>
        <CardHeader className="relative">
          <CardTitle className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-primary shadow-lg shadow-primary/30">
              <Bot className="h-10 w-10 text-white" />
            </div>
            <div className="flex flex-col">
              <h1 className="text-4xl text-white font-black uppercase tracking-tighter" style={{ letterSpacing: '-0.05em', fontWeight: 900 }}>
                Code Review
                <span className="text-primary ml-3">Responder</span>
              </h1>
              <span className="text-base font-normal text-muted-foreground mt-1">
                Powered by Claude Code - Drop review comment screenshots to generate intelligent responses
              </span>
            </div>
          </CardTitle>
          {!isBackendAvailable && (
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 text-yellow-800 px-6 py-4 rounded-xl mt-4 backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <strong>Demo Mode:</strong> Backend not available. Using simulated responses.
              </div>
            </div>
          )}
        </CardHeader>
      </Card>

      {/* Hybrid Review Interface */}
      <HybridReviewInterface className="mb-8" />

      {/* Action Buttons */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="flex gap-4 flex-wrap">
            <Button 
              onClick={processAllComments}
              disabled={isProcessingAll || getPendingComments().length === 0}
              className="flex items-center gap-3"
              size="lg"
            >
              <Play className="h-5 w-5" />
              Process All Comments
              {isProcessingAll && <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>}
            </Button>
            
            <Button 
              variant="destructive"
              onClick={handleClearAll}
              disabled={comments.length === 0}
              className="flex items-center gap-3"
              size="lg"
            >
              <Trash2 className="h-5 w-5" />
              Clear All
            </Button>
            
            <Button 
              variant="outline"
              onClick={exportResponses}
              disabled={getCompletedComments().length === 0}
              className="flex items-center gap-3"
              size="lg"
            >
              <Download className="h-5 w-5" />
              Export Responses
            </Button>
            
            <Button 
              variant="outline"
              onClick={copyAllResponses}
              disabled={getCompletedComments().length === 0}
              className="flex items-center gap-3"
              size="lg"
            >
              <Copy className="h-5 w-5" />
              Copy All to Clipboard
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Comments List */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <Card className="bg-gradient-to-br from-muted/30 to-muted/10">
            <CardContent className="text-center py-16">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent rounded-full blur-3xl"></div>
                <MessageSquare className="relative mx-auto h-20 w-20 text-muted-foreground mb-6" />
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-2">No comments yet</h3>
              <p className="text-muted-foreground text-lg">Drop some screenshots to get started!</p>
            </CardContent>
          </Card>
        ) : (
          comments.map((comment, index) => (
            <div 
              key={comment.id} 
              className="animate-fade-in"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CommentCard
                comment={comment}
                onProcess={processComment}
                onRegenerate={regenerateResponse}
              />
            </div>
          ))
        )}
      </div>

      {/* Stats */}
      {comments.length > 0 && (
        <Card className="mt-8 bg-gradient-to-br from-card to-card/50">
          <CardContent className="p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div className="p-4 rounded-lg bg-primary/10 border border-primary/20">
                <p className="text-4xl font-bold text-primary mb-2">
                  {stats.total}
                </p>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Total Comments</p>
              </div>
              <div className="p-4 rounded-lg bg-yellow-500/10 border border-yellow-500/20">
                <p className="text-4xl font-bold text-yellow-500 mb-2">{stats.pending}</p>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Pending</p>
              </div>
              <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                <p className="text-4xl font-bold text-green-500 mb-2">{stats.completed}</p>
                <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Completed</p>
              </div>
              {stats.error > 0 && (
                <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20">
                  <p className="text-4xl font-bold text-red-500 mb-2">{stats.error}</p>
                  <p className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Errors</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}