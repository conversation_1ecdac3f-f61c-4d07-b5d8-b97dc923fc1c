import React from 'react'
import { <PERSON>down<PERSON>ender<PERSON> } from '../components/markdown/MarkdownRenderer'

const testContent = `# Mermaid Diagram Tests

## Test 1: Graph with TB direction (wie im Screenshot)
\`\`\`
graph TB
    subgraph "Frontend Applications"
        CMS[CMS Frontend]
        Editor[Article Editor]
    end

    subgraph "API Gateway"
        Gateway[OpenAPI Service]
    end

    subgraph "Microservices"
        Vertex[Vertex Service]
    end

    subgraph "External Services"
        VertexAI[Google Vertex AI<br/>Gemini 2.5 Flash]
    end

    CMS --> Gateway
    Editor --> Gateway
    Gateway --> Vertex
    Vertex --> VertexAI

    style Vertex fill:#90EE90
    style VertexAI fill:#87CEEB
\`\`\`

## Test 2: Explicit Mermaid Language
\`\`\`mermaid
sequenceDiagram
    participant A as User
    participant B as API
    participant C as AI Service
    
    A->>B: Request Review
    B->>C: Generate Tutorial
    C-->>B: Tutorial Content
    B-->>A: Tutorial Response
\`\`\`

## Test 3: Flowchart
\`\`\`mermaid
flowchart LR
    A[Start] --> B{Is Tutorial?}
    B -->|Yes| C[Render Mermaid]
    B -->|No| D[Show Code Block]
    C --> E[Display Diagram]
    D --> E
\`\`\`

## Test 4: Simple arrows (sollte auch erkannt werden)
\`\`\`
A --> B
B --> C
C --> D
\`\`\`

## Normal Code Block (zum Vergleich - sollte NICHT als Mermaid erkannt werden)
\`\`\`typescript
function test() {
  return "This should stay as code block";
}
\`\`\`
`

export const MermaidTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">🧪 Mermaid Integration Test</h1>
          <p className="text-gray-400">
            Teste die Mermaid-Diagramm-Darstellung. Öffne die Browser-Konsole (F12) für Debug-Logs.
          </p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6">
          <MarkdownRenderer 
            content={testContent}
            enableCopy={true}
            enableAnchors={true}
          />
        </div>
        
        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
          <h3 className="font-semibold mb-2">🔍 Was zu erwarten ist:</h3>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• <strong>Test 1-3:</strong> Sollten als interaktive Mermaid-Diagramme gerendert werden</li>
            <li>• <strong>Test 4:</strong> Sollte auch als Mermaid erkannt werden (Arrow-Pattern)</li>
            <li>• <strong>TypeScript Block:</strong> Sollte als normaler Code-Block bleiben</li>
            <li>• <strong>Debug-Logs:</strong> In der Konsole sollten 🔍🎨🎉 Emojis erscheinen</li>
          </ul>
        </div>
      </div>
    </div>
  )
}