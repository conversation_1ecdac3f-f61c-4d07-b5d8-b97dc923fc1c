import React, { useState, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'
import { Settings as SettingsIcon, Database, AlertTriangle, Activity, Info, GitBranch } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Badge } from '../components/ui/badge'
import { PerformanceMonitor } from '../components/monitoring/PerformanceMonitor'
import { useReviewMode } from '../hooks/useReviewMode'
import { useAuthStatus } from '../hooks/useAuth'
import { cacheManager } from '../services/cache/CacheManager'
import { errorHandler } from '../services/errors/ErrorHandler'
import { DirectoryPicker } from '../components/worktree/DirectoryPicker'

export const Settings: React.FC = () => {
  const { preferences, updatePreferences } = useReviewMode()
  const { isAuthenticated, user } = useAuthStatus()
  const [searchParams] = useSearchParams()
  const [activeTab, setActiveTab] = useState<'general' | 'git' | 'performance' | 'debug'>('general')

  // Handle URL tab parameter
  useEffect(() => {
    const tabParam = searchParams.get('tab')
    if (tabParam && ['general', 'git', 'performance', 'debug'].includes(tabParam)) {
      setActiveTab(tabParam as 'general' | 'git' | 'performance' | 'debug')
    }
  }, [searchParams])

  const isDevelopment = import.meta.env.NODE_ENV === 'development'
  const buildInfo = {
    version: '1.0.0',
    buildDate: new Date().toISOString().split('T')[0],
    nodeEnv: import.meta.env.NODE_ENV,
    commit: 'latest' // This would normally come from build process
  }

  const handlePreferenceChange = (key: keyof typeof preferences, value: boolean) => {
    updatePreferences({ [key]: value })
  }

  const tabs = [
    { id: 'general', label: 'General', icon: SettingsIcon },
    { id: 'git', label: 'Git Worktree', icon: GitBranch },
    { id: 'performance', label: 'Performance', icon: Activity },
    ...(isDevelopment ? [{ id: 'debug', label: 'Debug', icon: AlertTriangle }] : [])
  ]

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl animate-fade-in relative z-10">
      {/* Header */}
      <Card className="mb-8 overflow-hidden relative">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5"></div>
        <CardHeader className="relative">
          <CardTitle className="flex items-center gap-4">
            <div className="p-3 rounded-lg bg-primary shadow-lg shadow-primary/30">
              <SettingsIcon className="h-10 w-10 text-white" />
            </div>
            <div className="flex flex-col">
              <h1 className="text-4xl text-white font-black uppercase tracking-tighter" style={{ letterSpacing: '-0.05em', fontWeight: 900 }}>
                Settings
                <span className="text-primary ml-3">&</span>
                <span className="text-primary ml-1">Diagnostics</span>
              </h1>
              <span className="text-base font-normal text-muted-foreground mt-1">
                Configure application preferences and monitor performance
              </span>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Build Info */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-primary" />
            Application Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-primary/10 border border-primary/20 rounded-lg">
              <p className="text-lg font-bold text-primary">{buildInfo.version}</p>
              <p className="text-xs text-muted-foreground uppercase tracking-wide">Version</p>
            </div>
            <div className="text-center p-3 bg-secondary/10 border border-secondary/20 rounded-lg">
              <p className="text-lg font-bold text-secondary-foreground">{buildInfo.buildDate}</p>
              <p className="text-xs text-muted-foreground uppercase tracking-wide">Build Date</p>
            </div>
            <div className="text-center p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
              <Badge variant={buildInfo.nodeEnv === 'production' ? 'default' : 'secondary'}>
                {buildInfo.nodeEnv}
              </Badge>
              <p className="text-xs text-muted-foreground uppercase tracking-wide mt-1">Environment</p>
            </div>
            <div className="text-center p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <p className="text-lg font-bold text-blue-600">{buildInfo.commit}</p>
              <p className="text-xs text-muted-foreground uppercase tracking-wide">Commit</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-muted/30 p-1 rounded-lg">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as 'general' | 'git' | 'performance' | 'debug')}
                className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium text-sm transition-all ${
                  activeTab === tab.id
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            )
          })}
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'general' && (
        <div className="space-y-6">
          {/* User Info */}
          {isAuthenticated && user && (
            <Card>
              <CardHeader>
                <CardTitle>Account Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                    <span className="text-lg font-medium text-primary">
                      {user.display_name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-medium">{user.display_name}</h3>
                    <p className="text-sm text-muted-foreground">@{user.username}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Review Mode Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Review Mode Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Auto-switch to API Mode</h4>
                  <p className="text-sm text-muted-foreground">
                    Automatically switch to API mode when authenticated
                  </p>
                </div>
                <Button
                  variant={preferences.autoSwitchToApi ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePreferenceChange('autoSwitchToApi', !preferences.autoSwitchToApi)}
                >
                  {preferences.autoSwitchToApi ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Remember Mode Choice</h4>
                  <p className="text-sm text-muted-foreground">
                    Remember your preferred review mode between sessions
                  </p>
                </div>
                <Button
                  variant={preferences.rememberModeChoice ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePreferenceChange('rememberModeChoice', !preferences.rememberModeChoice)}
                >
                  {preferences.rememberModeChoice ? 'Enabled' : 'Disabled'}
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Show Mode Selector</h4>
                  <p className="text-sm text-muted-foreground">
                    Display mode selection options and tips
                  </p>
                </div>
                <Button
                  variant={preferences.showModeSelector ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePreferenceChange('showModeSelector', !preferences.showModeSelector)}
                >
                  {preferences.showModeSelector ? 'Enabled' : 'Disabled'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'git' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <GitBranch className="h-5 w-5 text-green-600" />
                Git Worktree Configuration
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Configure your master repository path for Git worktree management. 
                Worktrees will be created in a separate `worktrees/` directory outside your main repository.
              </p>
            </CardHeader>
            <CardContent>
              <DirectoryPicker />
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'performance' && (
        <PerformanceMonitor />
      )}

      {activeTab === 'debug' && isDevelopment && (
        <div className="space-y-6">
          {/* Cache Debug */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5 text-blue-500" />
                Cache Debug
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Cache Keys</h4>
                <div className="max-h-40 overflow-y-auto space-y-1">
                  {cacheManager.getKeys().map((key, index) => (
                    <div key={index} className="text-xs font-mono bg-muted/30 p-2 rounded">
                      {key}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('Cache Keys:', cacheManager.getKeys())
                    console.log('Cache Stats:', cacheManager.getStats())
                  }}
                >
                  Log Cache Data
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    cacheManager.clear()
                    window.location.reload()
                  }}
                >
                  Clear Cache & Reload
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Error Debug */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Error Debug
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Recent Errors</h4>
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {errorHandler.getRecentErrors(5).map((error) => (
                    <div key={error.id} className="text-xs bg-red-500/10 border border-red-500/20 p-2 rounded">
                      <div className="font-medium text-red-600">{error.category}: {error.severity}</div>
                      <div className="text-muted-foreground mt-1">{error.message}</div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('Error Metrics:', errorHandler.getMetrics())
                    console.log('Recent Errors:', errorHandler.getRecentErrors())
                  }}
                >
                  Log Error Data
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Trigger test error
                    errorHandler.handleError(new Error('Test error for debugging'), {
                      component: 'Settings',
                      action: 'test_error'
                    })
                  }}
                >
                  Trigger Test Error
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Environment Variables */}
          <Card>
            <CardHeader>
              <CardTitle>Environment Variables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(import.meta.env)
                  .filter(([key]) => key.startsWith('VITE_'))
                  .map(([key, value]) => (
                    <div key={key} className="flex justify-between items-center py-1">
                      <span className="font-mono text-sm">{key}</span>
                      <span className="text-sm text-muted-foreground">
                        {key.includes('SECRET') || key.includes('KEY') ? '***' : value}
                      </span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}