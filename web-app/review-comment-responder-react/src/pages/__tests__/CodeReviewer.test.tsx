/**
 * Unit Tests for CodeReviewer Page - Multi-Agent Integration
 * Testing enhanced workflow with Multi-Agent Review capabilities
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { CodeReviewer } from '../CodeReviewer'
import type { ReviewSession } from '../../types/enhanced-review'

// Mock all the hooks and services
const mockUseAuthStatus = vi.fn()
const mockUseWorktreeStatus = vi.fn()
const mockUseCodeReviewerStore = vi.fn()
const mockUseCurrentSelection = vi.fn()
const mockUseReviewConfig = vi.fn()
const mockUseReviewSession = vi.fn()
const mockUseMultiAgentReview = vi.fn()
const mockUseWebSocketConnection = vi.fn()
const mockUseProgressPolling = vi.fn()
const mockUseWorkflowAutoAdvance = vi.fn()

// Mock services
const mockCodeReviewerService = {
  startReview: vi.fn(),
  generateAcceptanceCriteria: vi.fn()
}

const mockMultiAgentReviewService = vi.fn()

// Mock components
vi.mock('../../hooks/useAuth', () => ({
  useAuthStatus: mockUseAuthStatus
}))

vi.mock('../../contexts/WorktreeStatusContext', () => ({
  useWorktreeStatus: mockUseWorktreeStatus
}))

vi.mock('../../store/useCodeReviewerStore', () => ({
  useCodeReviewerStore: mockUseCodeReviewerStore,
  useCurrentSelection: mockUseCurrentSelection,  
  useReviewConfig: mockUseReviewConfig,
  useReviewSession: mockUseReviewSession
}))

vi.mock('../../hooks/useMultiAgentReview', () => ({
  useMultiAgentReview: mockUseMultiAgentReview
}))

vi.mock('../../hooks/useWebSocketConnection', () => ({
  useWebSocketConnection: mockUseWebSocketConnection
}))

vi.mock('../../hooks/useProgressPolling', () => ({
  useProgressPolling: mockUseProgressPolling
}))

vi.mock('../../hooks/useWorkflowAutoAdvance', () => ({
  useWorkflowAutoAdvance: mockUseWorkflowAutoAdvance
}))

vi.mock('../../services/codeReviewer/CodeReviewerService', () => ({
  codeReviewerService: mockCodeReviewerService
}))

vi.mock('../../services/multiAgent/MultiAgentReviewService', () => ({
  MultiAgentReviewService: mockMultiAgentReviewService
}))

vi.mock('../../config/featureFlags', () => ({
  featureFlags: {
    isMultiAgentEnabled: vi.fn(() => true)
  }
}))

// Mock all page components
vi.mock('../../components/CodeReviewerHeader', () => ({
  CodeReviewerHeader: () => <div data-testid="code-reviewer-header">Code Reviewer Header</div>
}))

vi.mock('../../components/AuthenticationPrompt', () => ({
  AuthenticationPrompt: () => <div data-testid="authentication-prompt">Please authenticate</div>
}))

vi.mock('../../components/WorkflowSteps', () => ({
  WorkflowSteps: ({ currentStep, onResetWorkflow }: { currentStep: string; onResetWorkflow: () => void }) => (
    <div data-testid="workflow-steps" data-current-step={currentStep}>
      <button data-testid="reset-workflow-btn" onClick={onResetWorkflow}>Reset</button>
      Workflow Steps: {currentStep}
    </div>
  )
}))

vi.mock('../../components/AssignedWorkPanel', () => ({
  AssignedWorkPanel: ({ onStartReview }: { onStartReview: (pr: any, ticket?: any) => void }) => (
    <div data-testid="assigned-work-panel">
      <button 
        data-testid="start-review-btn" 
        onClick={() => onStartReview({ id: 1, branch: 'test-branch' }, { ticket_id: 'JIRA-123' })}
      >
        Start Review
      </button>
    </div>
  )
}))

vi.mock('../../components/ConfigureReviewStep', () => ({
  ConfigureReviewStep: ({ 
    onStartReview, 
    onModeSelect,
    isStartingReview 
  }: { 
    onStartReview: () => void;
    onModeSelect: (mode: string) => void;
    isStartingReview: boolean;
  }) => (
    <div data-testid="configure-review-step">
      <button data-testid="select-multi-agent" onClick={() => onModeSelect('multi_agent')}>
        Multi-Agent Mode
      </button>
      <button data-testid="select-legacy" onClick={() => onModeSelect('quick')}>
        Legacy Mode
      </button>
      <button 
        data-testid="start-review-final-btn" 
        onClick={onStartReview}
        disabled={isStartingReview}
      >
        {isStartingReview ? 'Starting...' : 'Start Review'}
      </button>
    </div>
  )
}))

vi.mock('../../components/ReviewProgressStep', () => ({
  ReviewProgressStep: ({ mode }: { mode: string }) => (
    <div data-testid="review-progress-step" data-mode={mode}>
      Review in progress with mode: {mode}
    </div>
  )
}))

vi.mock('../../components/ReviewResultsStep', () => ({
  ReviewResultsStep: () => (
    <div data-testid="review-results-step">Review Results</div>
  )
}))

vi.mock('../../components/worktree/WorktreeStatusIndicator', () => ({
  WorktreeStatusIndicator: ({ showFullCard }: { showFullCard: boolean }) => (
    <div data-testid="worktree-status-indicator" data-show-full-card={showFullCard}>
      Worktree Status
    </div>
  )
}))

describe('CodeReviewer Page - Multi-Agent Integration', () => {
  let mockMultiAgentHook: any

  beforeEach(() => {
    // Default mock implementations
    mockUseAuthStatus.mockReturnValue({ isAuthenticated: true })
    mockUseWorktreeStatus.mockReturnValue({ 
      status: { isConfigured: true, isValid: true } 
    })
    
    mockUseCodeReviewerStore.mockReturnValue({
      selectPR: vi.fn(),
      selectTicket: vi.fn()
    })
    
    mockUseCurrentSelection.mockReturnValue({
      selectedPR: null,
      selectedTicket: null
    })
    
    mockUseReviewConfig.mockReturnValue({
      mode: 'quick',
      repositoryPath: '/test/repo',
      setMode: vi.fn()
    })
    
    mockUseReviewSession.mockReturnValue({
      activeSession: null,
      progress: 0,
      addSession: vi.fn(),
      updateSession: vi.fn()
    })

    // Multi-Agent hook mock
    mockMultiAgentHook = {
      startReview: vi.fn(),
      reset: vi.fn(),
      state: {
        review_id: null,
        status: 'idle',
        progress: 0
      }
    }
    mockUseMultiAgentReview.mockReturnValue(mockMultiAgentHook)
    
    mockUseWebSocketConnection.mockReturnValue({})
    mockUseProgressPolling.mockReturnValue({})
    mockUseWorkflowAutoAdvance.mockReturnValue({})

    // Service mocks
    mockCodeReviewerService.startReview.mockResolvedValue({
      success: true,
      session: {
        session_id: 'legacy-session-123',
        branch_name: 'test-branch',
        status: 'initializing',
        progress: 0,
        progress_message: 'Starting review...',
        created_at: '2024-01-01T10:00:00Z'
      }
    })

    mockCodeReviewerService.generateAcceptanceCriteria.mockResolvedValue({
      success: true,
      acceptance_criteria: ['AC 1', 'AC 2', 'AC 3']
    })
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('authentication and setup', () => {
    it('should show authentication prompt when not authenticated', () => {
      mockUseAuthStatus.mockReturnValue({ isAuthenticated: false })
      
      render(<CodeReviewer />)
      
      expect(screen.getByTestId('authentication-prompt')).toBeInTheDocument()
      expect(screen.queryByTestId('workflow-steps')).not.toBeInTheDocument()
    })

    it('should show worktree status indicator when not configured', () => {
      mockUseWorktreeStatus.mockReturnValue({
        status: { isConfigured: false, isValid: false }
      })
      
      render(<CodeReviewer />)
      
      expect(screen.getByTestId('worktree-status-indicator')).toBeInTheDocument()
      expect(screen.getByText('Please configure your Git Worktree settings to proceed')).toBeInTheDocument()
    })

    it('should render main interface when authenticated and configured', () => {
      render(<CodeReviewer />)
      
      expect(screen.getByTestId('code-reviewer-header')).toBeInTheDocument()
      expect(screen.getByTestId('workflow-steps')).toBeInTheDocument()
      expect(screen.getByTestId('assigned-work-panel')).toBeInTheDocument()
    })
  })

  describe('workflow navigation', () => {
    it('should start with select-work step', () => {
      render(<CodeReviewer />)
      
      expect(screen.getByTestId('workflow-steps')).toHaveAttribute('data-current-step', 'select-work')
      expect(screen.getByTestId('assigned-work-panel')).toBeInTheDocument()
    })

    it('should navigate to configure-review step when work is selected', async () => {
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      expect(screen.queryByTestId('assigned-work-panel')).not.toBeInTheDocument()
    })

    it('should handle workflow reset', () => {
      render(<CodeReviewer />)
      
      const resetButton = screen.getByTestId('reset-workflow-btn')
      fireEvent.click(resetButton)
      
      expect(mockMultiAgentHook.reset).toHaveBeenCalled()
    })
  })

  describe('multi-agent integration', () => {
    beforeEach(() => {
      // Set up selected PR and ticket
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: {
          id: 1,
          branch: 'feature/test-branch',
          workspace: 'test-workspace',
          repository: 'test-repo'
        },
        selectedTicket: {
          ticket_id: 'JIRA-123',
          summary: 'Test ticket',
          description: 'Test description'
        }
      })
      
      mockUseReviewConfig.mockReturnValue({
        mode: 'multi_agent',
        repositoryPath: '/test/repo',
        setMode: vi.fn()
      })
    })

    it('should detect multi-agent mode correctly', () => {
      render(<CodeReviewer />)
      
      // Navigate to configure step
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
    })

    it('should start multi-agent review when mode is multi_agent', async () => {
      mockMultiAgentHook.startReview.mockResolvedValue(undefined)
      
      render(<CodeReviewer />)
      
      // Navigate to configure step and start review
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      fireEvent.click(screen.getByTestId('start-review-final-btn'))
      
      await waitFor(() => {
        expect(mockMultiAgentHook.startReview).toHaveBeenCalledWith({
          branch_name: 'feature/test-branch',
          repository_path: '/test/repo',
          pr_url: expect.stringContaining('test-workspace/test-repo/pull-requests/1'),
          review_mode: 'multi_agent',
          agent_config: expect.objectContaining({
            enabled_agents: expect.arrayContaining([
              'acceptance_criteria',
              'bug_detection',
              'security_analysis',
              'logic_analysis',
              'quality_analysis',
              'architecture_analysis',
              'summary'
            ])
          }),
          jira_ticket: expect.objectContaining({
            ticket_id: 'JIRA-123'
          })
        })
      })
    })

    it('should start parallel review when mode is parallel', async () => {
      mockUseReviewConfig.mockReturnValue({
        mode: 'parallel',
        repositoryPath: '/test/repo',
        setMode: vi.fn()
      })
      
      mockMultiAgentHook.startReview.mockResolvedValue(undefined)
      
      render(<CodeReviewer />)
      
      // Navigate to configure step and start review
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      fireEvent.click(screen.getByTestId('start-review-final-btn'))
      
      await waitFor(() => {
        expect(mockMultiAgentHook.startReview).toHaveBeenCalledWith(
          expect.objectContaining({
            review_mode: 'parallel'
          })
        )
      })
    })

    it('should fallback to legacy mode when multi-agent is not available', async () => {
      // Mock feature flag as disabled
      const mockFeatureFlags = await import('../../config/featureFlags')
      mockFeatureFlags.featureFlags.isMultiAgentEnabled = vi.fn(() => false)
      
      render(<CodeReviewer />)
      
      // Navigate to configure step and start review
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      fireEvent.click(screen.getByTestId('start-review-final-btn'))
      
      await waitFor(() => {
        expect(mockCodeReviewerService.startReview).toHaveBeenCalled()
        expect(mockMultiAgentHook.startReview).not.toHaveBeenCalled()
      })
    })
  })

  describe('acceptance criteria generation', () => {
    it('should generate AC for tickets with 0 acceptance criteria', async () => {
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: { id: 1, branch: 'test-branch' },
        selectedTicket: {
          ticket_id: 'JIRA-123',
          summary: 'Test ticket',
          description: 'Test description',
          acceptance_criteria_count: 0,
          acceptance_criteria: []
        }
      })
      
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(mockCodeReviewerService.generateAcceptanceCriteria).toHaveBeenCalledWith({
          ticket_id: 'JIRA-123',
          summary: 'Test ticket',
          description: 'Test description'
        })
      })
    })

    it('should not generate AC for tickets that already have acceptance criteria', async () => {
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: { id: 1, branch: 'test-branch' },
        selectedTicket: {
          ticket_id: 'JIRA-123',
          summary: 'Test ticket',
          description: 'Test description',
          acceptance_criteria_count: 3,
          acceptance_criteria: ['AC 1', 'AC 2', 'AC 3']
        }
      })
      
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(mockCodeReviewerService.generateAcceptanceCriteria).not.toHaveBeenCalled()
      })
    })
  })

  describe('review progress and results', () => {
    it('should show progress step when review is running', () => {
      mockUseReviewSession.mockReturnValue({
        activeSession: {
          session_id: 'test-session-123',
          status: 'running'
        } as ReviewSession,
        progress: 50,
        addSession: vi.fn(),
        updateSession: vi.fn()
      })
      
      render(<CodeReviewer />)
      
      // Manually navigate to progress step (would happen automatically in real scenario)
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      // Should eventually show progress step
      expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
    })

    it('should show results step when review is completed', () => {
      mockUseReviewSession.mockReturnValue({
        activeSession: {
          session_id: 'test-session-123',
          status: 'completed'
        } as ReviewSession,
        progress: 100,
        addSession: vi.fn(),
        updateSession: vi.fn()
      })
      
      render(<CodeReviewer />)
      
      // Results step would be shown in real scenario based on workflow logic
      expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle review start errors gracefully', async () => {
      mockMultiAgentHook.startReview.mockRejectedValue(new Error('Service unavailable'))
      
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: { id: 1, branch: 'test-branch' },
        selectedTicket: null
      })
      
      mockUseReviewConfig.mockReturnValue({
        mode: 'multi_agent',
        repositoryPath: '/test/repo',
        setMode: vi.fn()
      })
      
      render(<CodeReviewer />)
      
      // Navigate to configure step and start review
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      fireEvent.click(screen.getByTestId('start-review-final-btn'))
      
      // Should handle error gracefully without crashing
      await waitFor(() => {
        expect(mockMultiAgentHook.startReview).toHaveBeenCalled()
      })
    })

    it('should handle AC generation failures gracefully', async () => {
      mockCodeReviewerService.generateAcceptanceCriteria.mockResolvedValue({
        success: false,
        error: 'Generation failed'
      })
      
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: { id: 1, branch: 'test-branch' },
        selectedTicket: {
          ticket_id: 'JIRA-123',
          summary: 'Test ticket',
          description: 'Test description',
          acceptance_criteria_count: 0
        }
      })
      
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(mockCodeReviewerService.generateAcceptanceCriteria).toHaveBeenCalled()
      })
      
      // Should continue to configure step despite AC generation failure
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
    })
  })

  describe('mode selection', () => {
    it('should allow switching between multi-agent and legacy modes', async () => {
      const mockSetMode = vi.fn()
      mockUseReviewConfig.mockReturnValue({
        mode: 'quick',
        repositoryPath: '/test/repo',
        setMode: mockSetMode
      })
      
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      // Switch to multi-agent mode
      fireEvent.click(screen.getByTestId('select-multi-agent'))
      expect(mockSetMode).toHaveBeenCalledWith('multi_agent')
      
      // Switch to legacy mode
      fireEvent.click(screen.getByTestId('select-legacy'))
      expect(mockSetMode).toHaveBeenCalledWith('quick')
    })
  })

  describe('loading states', () => {
    it('should show loading state when starting review', async () => {
      mockUseCurrentSelection.mockReturnValue({
        selectedPR: { id: 1, branch: 'test-branch' },
        selectedTicket: null
      })
      
      render(<CodeReviewer />)
      
      fireEvent.click(screen.getByTestId('start-review-btn'))
      
      await waitFor(() => {
        expect(screen.getByTestId('configure-review-step')).toBeInTheDocument()
      })
      
      // Mock starting state
      fireEvent.click(screen.getByTestId('start-review-final-btn'))
      
      // Button should show loading state (tested through component prop)
      expect(screen.getByTestId('start-review-final-btn')).toBeInTheDocument()
    })
  })
})