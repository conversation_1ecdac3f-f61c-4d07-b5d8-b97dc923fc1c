import React, { useState, useCallback } from 'react'
import { useAuthStatus } from '../hooks/useAuth'
import { CodeReviewerHeader } from '../components/CodeReviewerHeader'
import { AuthenticationPrompt } from '../components/AuthenticationPrompt'
import { WorkflowSteps } from '../components/WorkflowSteps'
import { AssignedWorkPanel } from '../components/AssignedWorkPanel'
import { ConfigureReviewStep } from '../components/ConfigureReviewStep'
import { ReviewProgressStep } from '../components/ReviewProgressStep'
import { ReviewResultsStep } from '../components/ReviewResultsStep'
import { 
  useCodeReviewerStore,
  useCurrentSelection,
  useReviewConfig,
  useReviewSession
} from '../store/useCodeReviewerStore'
import type { ReviewSession } from '../types/enhanced-review'
import { codeReviewerService } from '../services/codeReviewer/CodeReviewerService'
import { useWebSocketConnection } from '../hooks/useWebSocketConnection'
import { useProgressPolling } from '../hooks/useProgressPolling'
import { useWorkflowAutoAdvance } from '../hooks/useWorkflowAutoAdvance'
import { WorktreeStatusIndicator } from '../components/worktree/WorktreeStatusIndicator'
import { useWorktreeStatus } from '../contexts/WorktreeStatusContext'

// Types
interface AssignedPR {
  id: number
  title: string
  branch: string
  repository: string
  workspace: string
  author: string
  created_date: string
  updated_date: string
  comments: number
  state: string
  reviewers: string[]
  jira_ticket?: string
}

interface AssignedTicket {
  ticket_id: string
  summary: string
  description: string
  status: string
  priority: string
  assignee: string
  created_date: string
  updated_date: string
  acceptance_criteria_count: number
  acceptance_criteria?: string[]
  related_prs: number[]
}

interface ProgressEvent {
  id: string
  type: string
  message: string
  timestamp: Date
  data?: Record<string, unknown>
}

type ReviewStep = 'select-work' | 'configure-review' | 'review-progress' | 'review-results'

export const CodeReviewer: React.FC = () => {
  const { isAuthenticated } = useAuthStatus()
  const { status: worktreeStatus } = useWorktreeStatus()
  
  // Local state
  const [currentStep, setCurrentStep] = useState<ReviewStep>('select-work')
  const [isStartingReview, setIsStartingReview] = useState(false)
  const [progressEvents, setProgressEvents] = useState<ProgressEvent[]>([])
  const [currentProgressStep, setCurrentProgressStep] = useState<string>('')
  
  // Store hooks
  const { selectPR, selectTicket } = useCodeReviewerStore()
  const { selectedPR, selectedTicket } = useCurrentSelection()
  const { mode, repositoryPath, setMode } = useReviewConfig()
  const { activeSession, progress, addSession, updateSession } = useReviewSession()

  // Event handlers for hooks
  const handleProgressEvent = useCallback((event: ProgressEvent) => {
    setProgressEvents(prev => {
      console.log('📝 Current progress events:', prev.length)
      const newEvents = [...prev, event]
      console.log('📝 New progress events:', newEvents.length)
      return newEvents
    })
  }, [])

  const handleProgressStepUpdate = useCallback((step: string) => {
    setCurrentProgressStep(step)
  }, [])

  const handleSessionUpdate = useCallback((sessionId: string, updates: Partial<ReviewSession>) => {
    updateSession(sessionId, updates)
  }, [updateSession])

  // Custom hooks
  useWebSocketConnection({
    activeSession,
    onProgressEvent: handleProgressEvent,
    onProgressStepUpdate: handleProgressStepUpdate,
    onSessionUpdate: handleSessionUpdate
  })

  useProgressPolling({
    activeSession,
    onSessionUpdate: handleSessionUpdate
  })

  useWorkflowAutoAdvance({
    selectedPR,
    selectedTicket,
    activeSession,
    currentStep,
    onStepChange: setCurrentStep
  })

  // Event handlers
  const handlePRSelect = (pr: AssignedPR) => {
    selectPR(pr)
  }

  const handleTicketSelect = (ticket: AssignedTicket | null) => {
    selectTicket(ticket)
  }

  // Handler for Step 1 → Step 2 transition (with AC generation if needed)
  const handleContinueToConfigure = async (pr: AssignedPR, ticket: AssignedTicket) => {
    console.log('🚀 Moving to configure step with:', { pr: pr.branch, ticket: ticket.ticket_id })
    
    // Update selected items first
    selectPR(pr)
    selectTicket(ticket)
    
    // Check if ticket needs AC generation (has 0 AC)
    if (ticket.acceptance_criteria_count === 0 || !ticket.acceptance_criteria || ticket.acceptance_criteria.length === 0) {
      console.log('🤖 Ticket has 0 AC, generating with Claude AI...')
      
      try {
        // Show loading state
        setProgressEvents(prev => [...prev, {
          id: `ac-generation-${Date.now()}`,
          type: 'ac_generation_started',
          message: `Generiere Acceptance Criteria für ${ticket.ticket_id}...`,
          timestamp: new Date()
        }])

        const result = await codeReviewerService.generateAcceptanceCriteria({
          ticket_id: ticket.ticket_id,
          summary: ticket.summary,
          description: ticket.description
        })

        if (result.success && result.acceptance_criteria) {
          console.log('✅ AC generated successfully:', result.acceptance_criteria.length, 'criteria')
          
          // Update the ticket with generated AC
          const updatedTicket: AssignedTicket = {
            ...ticket,
            acceptance_criteria: result.acceptance_criteria,
            acceptance_criteria_count: result.acceptance_criteria.length
          }
          
          // Update the store with the enhanced ticket
          selectTicket(updatedTicket)
          
          setProgressEvents(prev => [...prev, {
            id: `ac-generation-success-${Date.now()}`,
            type: 'ac_generation_completed',
            message: `✅ ${result.acceptance_criteria.length} Acceptance Criteria generiert`,
            timestamp: new Date()
          }])
        } else {
          console.error('❌ AC generation failed:', result.error)
          setProgressEvents(prev => [...prev, {
            id: `ac-generation-error-${Date.now()}`,
            type: 'ac_generation_error',
            message: `⚠️ AC Generation fehlgeschlagen: ${result.error || 'Unbekannter Fehler'}`,
            timestamp: new Date()
          }])
        }
      } catch (error) {
        console.error('❌ AC generation error:', error)
        setProgressEvents(prev => [...prev, {
          id: `ac-generation-error-${Date.now()}`,
          type: 'ac_generation_error',
          message: `⚠️ AC Generation Fehler: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`,
          timestamp: new Date()
        }])
      }
    } else {
      console.log('✅ Ticket already has AC, proceeding directly')
    }
    
    // Move to configure step
    setCurrentStep('configure-review')
  }

  // Handler for Step 2 → Step 3 transition (existing review functionality)
  const handleStartReview = async () => {
    if (!selectedPR) return

    setIsStartingReview(true)
    
    // Clear previous progress events
    setProgressEvents([])
    setCurrentProgressStep('')
    
    try {
      console.log('🚀 Starting review with data:', {
        pr: selectedPR,
        ticket: selectedTicket,
        mode,
        repositoryPath
      })
      
      const ticketData = selectedTicket ? {
        ticket_id: selectedTicket.ticket_id,
        summary: selectedTicket.summary,
        description: selectedTicket.description,
        acceptance_criteria: selectedTicket.acceptance_criteria || [],
        acceptance_criteria_count: selectedTicket.acceptance_criteria_count
      } : null
      
      console.log('🎫 Ticket data being sent:', ticketData)
      
      const response = await codeReviewerService.startReview({
        branch_name: selectedPR.branch,
        repository_path: repositoryPath,
        pr_url: `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}`,
        review_mode: mode,
        jira_ticket: ticketData
      })

      console.log('🔥 API Response:', response)

      if (response.success && response.session) {
        // Create session object from response
        const session = {
          session_id: response.session.session_id,
          branch_name: response.session.branch_name,
          pr_url: `https://bitbucket.org/${selectedPR.workspace}/${selectedPR.repository.split('/').pop()}/pull-requests/${selectedPR.id}`,
          status: response.session.status as 'initializing' | 'running' | 'completed' | 'error',
          progress: response.session.progress,
          progress_message: response.session.progress_message,
          created_at: response.session.created_at
        }
        
        console.log('🔥 Created session object:', session)
        
        addSession(session)
        setCurrentStep('review-progress')
        
        console.log('🔥 Review started successfully')
      } else {
        console.error('Failed to start review:', response.error)
      }
    } catch (error) {
      console.error('Error starting review:', error)
    } finally {
      setIsStartingReview(false)
    }
  }

  const handleResetWorkflow = () => {
    selectPR(null)
    selectTicket(null)
    setCurrentStep('select-work')
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'select-work':
        return (
          <AssignedWorkPanel
            onPRSelect={handlePRSelect}
            onTicketSelect={handleTicketSelect}
            onStartReview={handleContinueToConfigure}
          />
        )
      
      case 'configure-review':
        return (
          <ConfigureReviewStep
            selectedPR={selectedPR}
            selectedTicket={selectedTicket}
            mode={mode}
            repositoryPath={repositoryPath}
            onModeSelect={setMode}
            onStartReview={handleStartReview}
            isStartingReview={isStartingReview}
          />
        )
      
      case 'review-progress':
        return (
          <ReviewProgressStep
            activeSession={activeSession}
            selectedPR={selectedPR}
            progress={progress}
            progressEvents={progressEvents}
            currentProgressStep={currentProgressStep}
            mode={mode}
          />
        )
      
      case 'review-results':
        return (
          <ReviewResultsStep
            activeSession={activeSession}
            selectedPR={selectedPR}
            onSessionUpdate={handleSessionUpdate}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl animate-fade-in relative z-10">
      {/* Header */}
      <CodeReviewerHeader />

      {/* Authentication Check */}
      {!isAuthenticated ? (
        <AuthenticationPrompt />
      ) : (
        <div className="space-y-6">
          {/* Worktree Status Check */}
          {(!worktreeStatus.isConfigured || !worktreeStatus.isValid) && (
            <WorktreeStatusIndicator 
              showFullCard={true}
              className="border-amber-200 bg-amber-50/50"
            />
          )}

          {/* Workflow Steps */}
          <WorkflowSteps 
            currentStep={currentStep}
            onResetWorkflow={handleResetWorkflow}
          />

          {/* Step Content - Block if worktree not configured */}
          {worktreeStatus.isConfigured && worktreeStatus.isValid ? (
            renderStepContent()
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>Please configure your Git Worktree settings to proceed with code reviews.</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}