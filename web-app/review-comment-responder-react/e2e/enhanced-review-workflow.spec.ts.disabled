import { test, expect, Page } from '@playwright/test'

/**
 * End-to-End tests for the complete Enhanced Review workflow
 * Tests the integration between frontend, backend, and Claude Code Reviewer
 */

// Test configuration
const BACKEND_URL = 'http://localhost:5002'
const FRONTEND_URL = 'http://localhost:5173'

// Mock data for testing
const MOCK_PR_URL = 'https://bitbucket.org/rmamedia/rma-mono/pull-requests/123'
const MOCK_TICKET_DATA = {
  ticket_id: 'CMS20-1234',
  summary: 'Test feature implementation',
  description: 'Test description for E2E testing',
  acceptance_criteria: [
    'Feature should work correctly',
    'Code should be well documented',
    'Tests should pass'
  ]
}

test.describe('Enhanced Review Workflow E2E', () => {
  let page: Page

  test.beforeEach(async ({ browser }) => {
    page = await browser.newPage()
    
    // Mock API responses to avoid real backend calls during testing
    await page.route('**/api/auth/status', async route => {
      await route.fulfill({
        json: {
          authenticated: true,
          user: { username: 'test-user', displayName: 'Test User' }
        }
      })
    })

    await page.route('**/api/jira/assigned-tickets', async route => {
      await route.fulfill({
        json: [MOCK_TICKET_DATA]
      })
    })
  })

  test.afterEach(async () => {
    await page.close()
  })

  test('Complete Review Workflow - Happy Path', async () => {
    // Step 1: Navigate to Code Reviewer page
    await page.goto(`${FRONTEND_URL}/reviewer`)
    await expect(page).toHaveTitle(/Code Reviewer/)

    // Step 2: Select PR and Ticket
    await page.fill('[data-testid="pr-url-input"]', MOCK_PR_URL)
    await page.selectOption('[data-testid="ticket-selector"]', MOCK_TICKET_DATA.ticket_id)

    // Step 3: Start Review
    await page.click('[data-testid="start-review-button"]')

    // Step 4: Verify progress page is shown
    await expect(page.locator('[data-testid="progress-timeline"]')).toBeVisible()
    await expect(page.locator('text=Initializing Review')).toBeVisible()

    // Step 5: Mock WebSocket progress updates
    await page.evaluate(() => {
      // Simulate WebSocket events
      const events = [
        { type: 'session_started', data: { session_id: 'test-session-123' } },
        { type: 'tools_available', data: { tools: ['Read', 'Edit', 'Bash'] } },
        { type: 'turn_started', data: { turn: 1 } },
        { type: 'thinking', data: { content: 'Analyzing code changes...' } },
        { type: 'tool_use', data: { tool: 'Read', file: 'src/components/test.tsx' } },
        { type: 'claude_response', data: { content: 'Found several issues to review...' } },
        { type: 'review_completed', data: { success: true } }
      ]

      // Simulate events with delays
      events.forEach((event, index) => {
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('socket-message', { detail: event }))
        }, index * 1000)
      })
    })

    // Step 6: Wait for progress updates to appear
    await expect(page.locator('[data-testid="progress-step-session_started"]')).toBeVisible()
    await expect(page.locator('[data-testid="progress-step-thinking"]')).toBeVisible()

    // Step 7: Mock review completion
    await page.route('**/api/review/results/*', async route => {
      await route.fulfill({
        json: {
          session_id: 'test-session-123',
          status: 'completed',
          enhanced_report: {
            metadata: {
              generated_at: new Date().toISOString(),
              review_type: 'comprehensive'
            },
            acceptance_criteria_analysis: {
              executive_summary: {
                total_ac: 3,
                fulfilled: 2,
                partially_fulfilled: 1,
                not_fulfilled: 0,
                compliance_rate: 83.3,
                business_alignment_score: 8
              },
              detailed_results: [
                {
                  id: 'ac_1',
                  text: 'Feature should work correctly',
                  status: 'fulfilled',
                  implementation_evidence: 'src/components/feature.tsx:15-45',
                  issues: []
                },
                {
                  id: 'ac_2',
                  text: 'Code should be well documented',
                  status: 'partially_fulfilled',
                  implementation_evidence: 'src/components/feature.tsx:1-10',
                  issues: ['Missing JSDoc comments for complex functions']
                },
                {
                  id: 'ac_3',
                  text: 'Tests should pass',
                  status: 'fulfilled',
                  implementation_evidence: 'tests/feature.test.tsx:1-50',
                  issues: []
                }
              ]
            },
            code_quality_analysis: {
              executive_summary: {
                overall_score: 8,
                critical_issues: 0,
                code_smells: 2,
                duplication_level: 5
              }
            },
            bug_detection_results: {
              critical_bugs: [],
              logic_errors: [],
              runtime_risks: []
            },
            action_items: {
              critical: [],
              important: [
                {
                  text: 'Add JSDoc comments to complex functions',
                  priority: 'important',
                  category: 'documentation'
                }
              ],
              suggestions: []
            },
            architectural_assessment: {
              design_patterns: [],
              integration_quality: [],
              violations: []
            },
            variable_parameter_analysis: {
              executive_summary: {
                total_variables: 15,
                naming_issues: 2,
                scope_issues: 0,
                consistency_score: 8
              },
              naming_analysis: [
                {
                  concept: 'User Data',
                  file: 'src/types.ts',
                  variable_name: 'userData',
                  pattern: 'camelCase',
                  status: 'consistent',
                  line_number: 10
                }
              ],
              scope_analysis: [],
              type_consistency: []
            },
            next_steps: {
              priority_assessment: {
                critical_blockers: 0,
                high_priority: 0,
                medium_priority: 1,
                can_merge: true,
                estimated_effort: '1-2 hours'
              },
              immediate_actions: [],
              follow_up_tasks: [
                {
                  task: 'Improve documentation coverage',
                  category: 'Quality',
                  timeline: 'Next sprint',
                  description: 'Add comprehensive JSDoc comments'
                }
              ],
              merge_readiness: {
                status: 'ready',
                blockers: [],
                recommendations: ['Consider adding more unit tests']
              },
              post_merge_actions: []
            },
            questions_clarifications: []
          },
          pr_url: MOCK_PR_URL,
          jira_ticket: MOCK_TICKET_DATA
        }
      })
    })

    // Step 8: Wait for results to load
    await page.waitForTimeout(8000) // Wait for all progress steps
    await expect(page.locator('[data-testid="enhanced-report"]')).toBeVisible()

    // Step 9: Verify Enhanced Report sections are displayed
    await expect(page.locator('text=Enhanced Code Review Report')).toBeVisible()
    await expect(page.locator('text=Executive Summary')).toBeVisible()
    await expect(page.locator('text=83.3%')).toBeVisible() // AC Compliance rate

    // Step 10: Test section expansion
    await page.click('[data-testid="ac-analysis-toggle"]')
    await expect(page.locator('[data-testid="ac-analysis-details"]')).toBeVisible()
    await expect(page.locator('text=Feature should work correctly')).toBeVisible()
    await expect(page.locator('text=src/components/feature.tsx:15-45')).toBeVisible()

    // Step 11: Test Variable Analysis section
    await page.click('[data-testid="variable-analysis-toggle"]')
    await expect(page.locator('[data-testid="variable-analysis-table"]')).toBeVisible()
    await expect(page.locator('text=User Data')).toBeVisible()
    await expect(page.locator('text=camelCase')).toBeVisible()

    // Step 12: Test Next Steps section
    await expect(page.locator('[data-testid="next-steps-section"]')).toBeVisible()
    await expect(page.locator('text=Ready to merge')).toBeVisible()
    await expect(page.locator('text=1-2 hours')).toBeVisible()

    // Step 13: Test export functionality
    const downloadPromise = page.waitForEvent('download')
    await page.click('[data-testid="export-report-button"]')
    const download = await downloadPromise
    expect(download.suggestedFilename()).toMatch(/enhanced-review-.+\.json/)

    // Step 14: Test PR navigation
    await page.click('[data-testid="view-pr-button"]')
    await expect(page).toHaveURL(MOCK_PR_URL, { timeout: 5000 })
  })

  test('Review Workflow - Error Handling', async () => {
    await page.goto(`${FRONTEND_URL}/reviewer`)

    // Test validation errors
    await page.click('[data-testid="start-review-button"]')
    await expect(page.locator('text=Please select a PR')).toBeVisible()

    // Fill PR URL but no ticket
    await page.fill('[data-testid="pr-url-input"]', MOCK_PR_URL)
    await page.click('[data-testid="start-review-button"]')
    await expect(page.locator('text=Please select a Jira ticket')).toBeVisible()

    // Mock backend error
    await page.route('**/api/review/start', async route => {
      await route.fulfill({
        status: 500,
        json: { success: false, error: 'Backend connection failed' }
      })
    })

    await page.selectOption('[data-testid="ticket-selector"]', MOCK_TICKET_DATA.ticket_id)
    await page.click('[data-testid="start-review-button"]')
    await expect(page.locator('text=Backend connection failed')).toBeVisible()
  })

  test('WebSocket Connection and Progress Updates', async () => {
    await page.goto(`${FRONTEND_URL}/reviewer`)

    // Fill required fields
    await page.fill('[data-testid="pr-url-input"]', MOCK_PR_URL)
    await page.selectOption('[data-testid="ticket-selector"]', MOCK_TICKET_DATA.ticket_id)

    // Mock successful review start
    await page.route('**/api/review/start', async route => {
      await route.fulfill({
        json: { 
          success: true, 
          session_id: 'test-session-123',
          websocket_url: 'ws://localhost:5002/ws'
        }
      })
    })

    await page.click('[data-testid="start-review-button"]')

    // Verify progress timeline appears
    await expect(page.locator('[data-testid="progress-timeline"]')).toBeVisible()
    await expect(page.locator('text=Waiting for Claude progress updates')).toBeVisible()

    // Simulate WebSocket events
    await page.evaluate(() => {
      const progressEvents = [
        { type: 'session_started', data: { message: 'Review session initialized' } },
        { type: 'tools_available', data: { message: 'Claude tools loaded successfully' } },
        { type: 'turn_started', data: { message: 'Starting code analysis' } },
        { type: 'thinking', data: { message: 'Analyzing code patterns and structure...' } },
        { type: 'tool_use', data: { tool: 'Read', message: 'Reading source files' } },
        { type: 'claude_response', data: { message: 'Analysis complete, generating report' } }
      ]

      progressEvents.forEach((event, index) => {
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('claude-progress', { detail: event }))
        }, index * 500)
      })
    })

    // Verify progress steps appear
    await expect(page.locator('[data-testid="progress-session_started"]')).toBeVisible()
    await expect(page.locator('[data-testid="progress-thinking"]')).toBeVisible()
    await expect(page.locator('text=Analyzing code patterns')).toBeVisible()
  })

  test('Field Detection Integration', async () => {
    await page.goto(`${FRONTEND_URL}/reviewer`)

    // Open field detector
    await page.click('[data-testid="jira-settings-button"]')
    await expect(page.locator('[data-testid="field-detector"]')).toBeVisible()

    // Mock field analysis
    await page.route('**/api/jira/analyze-fields', async route => {
      await route.fulfill({
        json: {
          possibleReviewerFields: ['customfield_10009', 'customfield_10010'],
          fieldSuggestions: [
            {
              field: 'customfield_10009',
              confidence: 'high',
              description: 'Contains user objects, Present in 8/10 tickets'
            },
            {
              field: 'customfield_10010',
              confidence: 'medium',
              description: 'Matches reviewer pattern, Present in 5/10 tickets'
            }
          ]
        }
      })
    })

    await page.click('[data-testid="analyze-fields-button"]')
    await expect(page.locator('text=customfield_10009')).toBeVisible()
    await expect(page.locator('text=HIGH confidence')).toBeVisible()

    // Select field
    await page.click('[data-testid="field-customfield_10009"]')
    await expect(page.locator('text=Field Selected')).toBeVisible()

    // Test field
    await page.fill('[data-testid="test-ticket-input"]', 'CMS20-1234')
    await page.click('[data-testid="test-field-button"]')
    await expect(page.locator('text=Test Result')).toBeVisible()
  })

  test('Performance with Large Datasets', async () => {
    await page.goto(`${FRONTEND_URL}/reviewer`)

    // Mock large dataset response
    const largeMockData = {
      enhanced_report: {
        acceptance_criteria_analysis: {
          detailed_results: Array.from({ length: 50 }, (_, i) => ({
            id: `ac_${i}`,
            text: `Acceptance criteria ${i}`,
            status: i % 3 === 0 ? 'fulfilled' : i % 3 === 1 ? 'partially_fulfilled' : 'not_fulfilled',
            implementation_evidence: `src/file${i}.tsx:${i * 10}-${i * 10 + 20}`,
            issues: i % 5 === 0 ? [`Issue in AC ${i}`] : []
          }))
        },
        variable_parameter_analysis: {
          naming_analysis: Array.from({ length: 100 }, (_, i) => ({
            concept: `Concept ${i}`,
            file: `src/file${i}.tsx`,
            variable_name: `variable${i}`,
            pattern: 'camelCase',
            status: i % 4 === 0 ? 'inconsistent' : 'consistent',
            line_number: i * 5
          }))
        },
        action_items: {
          critical: Array.from({ length: 20 }, (_, i) => ({
            text: `Critical issue ${i}`,
            priority: 'critical',
            category: 'security'
          }))
        }
      }
    }

    await page.route('**/api/review/results/*', async route => {
      await route.fulfill({ json: largeMockData })
    })

    // Measure performance
    const startTime = Date.now()
    
    // Navigate to results (simulate completed review)
    await page.goto(`${FRONTEND_URL}/reviewer?session=test-large-dataset`)
    await expect(page.locator('[data-testid="enhanced-report"]')).toBeVisible()

    const loadTime = Date.now() - startTime
    console.log(`Large dataset load time: ${loadTime}ms`)

    // Verify large dataset renders correctly
    await expect(page.locator('text=50 Items')).toBeVisible() // AC count
    await expect(page.locator('text=100 Variables')).toBeVisible() // Variable count
    await expect(page.locator('text=20')).toBeVisible() // Critical issues

    // Test scrolling performance
    await page.click('[data-testid="variable-analysis-toggle"]')
    await page.waitForTimeout(100) // Allow render
    
    const tableRows = page.locator('[data-testid="variable-analysis-table"] tr')
    await expect(tableRows).toHaveCountGreaterThan(50)

    // Ensure load time is reasonable (< 3 seconds for large datasets)
    expect(loadTime).toBeLessThan(3000)
  })
})