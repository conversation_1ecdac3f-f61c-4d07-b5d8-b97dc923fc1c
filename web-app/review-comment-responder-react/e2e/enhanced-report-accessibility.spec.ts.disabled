/**
 * Accessibility Tests für Enhanced Report Fallback UI States
 * 
 * Diese Tests validieren die Barrierefreiheit (a11y) der verschiedenen
 * Fallback-Modi und degraded UI states gemäß WCAG 2.1 AA Standards.
 */

import { test, expect, Page } from '@playwright/test'
import AxeBuilder from '@axe-core/playwright'

test.describe('Enhanced Report Fallback Accessibility', () => {

  // Mock-Daten für verschiedene Accessibility-Testszenarien
  const mockAccessibilityData = {
    clientFallback: {
      session_id: 'a11y-test-1',
      review_mode: 'comprehensive',
      branch_name: 'feature/accessibility',
      timestamp: new Date().toISOString(),
      raw_review: `
# Code Review Results

## AC 1: Screen Reader Compatibility
✅ AC 1 ist erfüllt. Screen reader compatibility wurde implementiert.
Code-Implementierung: ARIA labels and semantic HTML structure added.

## AC 2: Keyboard Navigation
⚠️ AC 2 ist teilweise erfüllt. Basic keyboard navigation works.
Probleme: Some interactive elements missing focus indicators.

## AC 3: Color Contrast
❌ AC 3 ist nicht erfüllt. Color contrast ratios need improvement.

## Critical Issues
- Critical bug: Focus trap not working in modal dialogs
- Accessibility issue: Missing alt text for important images
      `,
      structured_findings: {
        bugs: [
          { text: 'Focus management broken in dropdown', severity: 'high', file: 'Dropdown.tsx' },
          { text: 'ARIA labels missing on form controls', severity: 'medium', file: 'Form.tsx' }
        ],
        code_quality: [],
        security_issues: [],
        performance_issues: []
      },
      jira_ticket: {
        ticket_id: 'A11Y-123',
        summary: 'Improve accessibility compliance',
        acceptance_criteria: [
          'Screen reader compatibility implemented',
          'Keyboard navigation fully functional',
          'Color contrast meets WCAG AA standards'
        ],
        acceptance_criteria_count: 3
      },
      metadata: { changed_files: ['src/components/Dropdown.tsx'], file_count: 1 },
      summary: { total_files_reviewed: 1, total_findings: 3, security_issues: 0, potential_bugs: 2 },
      enhanced_report: null
    },

    backendFallback: {
      session_id: 'a11y-test-2',
      review_mode: 'accessibility',
      branch_name: 'feature/backend-fallback-a11y',
      timestamp: new Date().toISOString(),
      raw_review: 'Limited accessibility review data',
      parsing_metadata: {
        fallback_mode: true,
        error_details: {
          error_type: 'AccessibilityParseError',
          error_message: 'Accessibility-specific parsing failed',
          timestamp: new Date().toISOString()
        }
      },
      enhanced_report: {
        metadata: {
          generated_at: new Date().toISOString(),
          review_type: 'fallback',
          fallback_mode: true,
          parsing_error: 'Limited accessibility analysis available'
        },
        acceptance_criteria_analysis: {
          executive_summary: { total_ac: 0, fulfilled: 0, partially_fulfilled: 0, not_fulfilled: 0, compliance_rate: 0, business_alignment_score: 0 },
          detailed_results: []
        },
        code_quality_analysis: {
          executive_summary: { overall_score: 5, critical_issues: 0, code_smells: 0, duplication_level: 0 },
          code_duplication: [], complexity_issues: [], naming_consistency: []
        },
        bug_detection_results: { critical_bugs: [], logic_errors: [], runtime_risks: [] },
        action_items: { critical: [], important: [], suggestions: [] },
        architectural_assessment: { design_patterns: [], integration_quality: [], violations: [] },
        security_performance: { security_findings: [], performance_analysis: [] },
        variable_parameter_analysis: {
          executive_summary: { total_variables: 0, naming_issues: 0, scope_issues: 0, consistency_score: 5 },
          naming_analysis: [], scope_analysis: [], type_consistency: []
        },
        next_steps: {
          priority_assessment: { critical_blockers: 0, high_priority: 0, medium_priority: 0, can_merge: false, estimated_effort: 'Unknown' },
          immediate_actions: [], follow_up_tasks: [],
          merge_readiness: { status: 'needs_review', blockers: ['Accessibility analysis incomplete'], recommendations: ['Manual accessibility audit recommended'] },
          post_merge_actions: []
        },
        questions_clarifications: []
      },
      structured_findings: {},
      metadata: { changed_files: [], file_count: 0 },
      summary: { total_files_reviewed: 0, total_findings: 0, security_issues: 0, potential_bugs: 0 }
    }
  }

  // Helper functions
  async function setupMockAPI(page: Page, scenario: 'clientFallback' | 'backendFallback') {
    await page.route('**/api/code-reviewer/review-results/*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          session_id: mockAccessibilityData[scenario].session_id,
          results: mockAccessibilityData[scenario]
        })
      })
    })
  }

  async function navigateToReviewResults(page: Page, sessionId: string) {
    await page.goto(`/code-reviewer?session=${sessionId}&step=review-results`)
    await page.waitForLoadState('networkidle')
    // Wait for potential fallback generation
    await page.waitForTimeout(1500)
  }

  async function runAxeAudit(page: Page, tags: string[] = ['wcag2a', 'wcag2aa']) {
    const axeBuilder = new AxeBuilder({ page }).withTags(tags)
    return await axeBuilder.analyze()
  }

  test.describe('Client-side Fallback Accessibility', () => {

    test('should pass WCAG 2.1 AA compliance audit', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      const results = await runAxeAudit(page)
      
      // Should have no violations
      expect(results.violations).toHaveLength(0)
      
      // Should have good accessibility practices
      expect(results.passes.length).toBeGreaterThan(10)
    })

    test('should have proper ARIA labels on fallback banner', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      
      // Banner should have alert role for screen readers
      await expect(fallbackBanner).toHaveAttribute('role', 'alert')
      
      // Banner should have appropriate ARIA label
      await expect(fallbackBanner).toHaveAttribute('aria-label', /fallback.*analysis.*mode/i)
      
      // Technical details should be properly labeled
      const technicalDetails = page.locator('summary:has-text("Technical Details")')
      await expect(technicalDetails).toHaveAttribute('aria-expanded', 'false')
      
      // Expand and check
      await technicalDetails.click()
      await expect(technicalDetails).toHaveAttribute('aria-expanded', 'true')
    })

    test('should support keyboard navigation through fallback UI', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Start from beginning and tab through interactive elements
      await page.keyboard.press('Tab')
      
      // Toggle button should be focusable
      const toggleButton = page.locator('button:has-text("Show Raw Review")')
      await expect(toggleButton).toBeFocused()
      
      // Tab to next element
      await page.keyboard.press('Tab')
      const exportButton = page.locator('button:has-text("Export Report")')
      await expect(exportButton).toBeFocused()
      
      // Tab to technical details
      await page.keyboard.press('Tab')
      const technicalDetails = page.locator('summary:has-text("Technical Details")')
      await expect(technicalDetails).toBeFocused()
      
      // Activate with Enter
      await page.keyboard.press('Enter')
      await expect(technicalDetails).toHaveAttribute('aria-expanded', 'true')
      
      // Continue tabbing to section toggles
      await page.keyboard.press('Tab')
      const sectionToggle = page.locator('[data-testid="section-toggle"]').first()
      if (await sectionToggle.isVisible()) {
        await expect(sectionToggle).toBeFocused()
      }
    })

    test('should have proper focus indicators for all interactive elements', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check that all buttons have visible focus indicators
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i)
        if (await button.isVisible()) {
          await button.focus()
          
          // Check that focus ring is visible (CSS outline or box-shadow)
          const computedStyle = await button.evaluate(el => {
            const style = window.getComputedStyle(el, ':focus')
            return {
              outline: style.outline,
              boxShadow: style.boxShadow,
              outlineWidth: style.outlineWidth
            }
          })
          
          const hasFocusIndicator = 
            computedStyle.outline !== 'none' || 
            computedStyle.boxShadow !== 'none' ||
            computedStyle.outlineWidth !== '0px'
          
          expect(hasFocusIndicator).toBe(true)
        }
      }
    })

    test('should provide appropriate color contrast for all text', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Run axe with color contrast rules
      const results = await runAxeAudit(page, ['color-contrast'])
      
      // Should have no color contrast violations
      const colorContrastViolations = results.violations.filter(v => v.id === 'color-contrast')
      expect(colorContrastViolations).toHaveLength(0)
    })

    test('should announce view mode changes to screen readers', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      const toggleButton = page.locator('button:has-text("Show Raw Review")')
      
      // Check initial ARIA label
      await expect(toggleButton).toHaveAttribute('aria-label', /toggle.*view.*enhanced.*raw/i)
      
      // Toggle to raw mode
      await toggleButton.click()
      
      // Check updated ARIA label and live region announcement
      const updatedButton = page.locator('button:has-text("Show Enhanced View")')
      await expect(updatedButton).toHaveAttribute('aria-label', /toggle.*view.*raw.*enhanced/i)
      
      // Check for live region announcement
      const liveRegion = page.locator('[aria-live="polite"]')
      if (await liveRegion.isVisible()) {
        await expect(liveRegion).toContainText(/switched.*raw.*view/i)
      }
    })

    test('should have semantic heading structure', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check heading hierarchy
      const headings = page.locator('h1, h2, h3, h4, h5, h6')
      const headingCount = await headings.count()
      
      expect(headingCount).toBeGreaterThan(0)
      
      // Check that main title is h1 or h2
      const mainTitle = page.locator('text="Enhanced Code Review Report"')
      const tagName = await mainTitle.evaluate(el => el.tagName.toLowerCase())
      expect(['h1', 'h2']).toContain(tagName)
      
      // Check section headings are properly nested
      const sectionHeadings = page.locator('[data-testid*="section"] h3, [data-testid*="section"] h4')
      const sectionCount = await sectionHeadings.count()
      expect(sectionCount).toBeGreaterThan(0)
    })

    test('should provide alternative text for status indicators', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check badges have proper accessible names
      const badges = page.locator('[role="badge"], .badge')
      const badgeCount = await badges.count()
      
      for (let i = 0; i < badgeCount; i++) {
        const badge = badges.nth(i)
        if (await badge.isVisible()) {
          const accessibleName = await badge.getAttribute('aria-label') || 
                                 await badge.textContent()
          expect(accessibleName).toBeTruthy()
          expect(accessibleName?.length).toBeGreaterThan(0)
        }
      }
      
      // Specifically check fallback mode badges
      const fallbackBadge = page.locator('text="Fallback Generated"')
      await expect(fallbackBadge).toHaveAttribute('aria-label', /fallback.*mode.*indicator/i)
      
      const exportBadge = page.locator('text="Export Available"')
      await expect(exportBadge).toHaveAttribute('aria-label', /export.*functionality.*available/i)
    })
  })

  test.describe('Backend Fallback Accessibility', () => {

    test('should pass WCAG audit in limited analysis mode', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'a11y-test-2')

      const results = await runAxeAudit(page)
      expect(results.violations).toHaveLength(0)
    })

    test('should properly label degraded experience indicators', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'a11y-test-2')

      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toHaveAttribute('role', 'alert')
      await expect(fallbackBanner).toHaveAttribute('aria-label', /limited.*analysis.*mode/i)
      
      const degradedBadge = page.locator('text="Degraded Experience"')
      await expect(degradedBadge).toHaveAttribute('aria-label', /degraded.*experience.*indicator/i)
    })

    test('should provide accessible empty state messaging', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'a11y-test-2')

      // Check empty state messages are properly announced
      const emptyStates = page.locator('[data-testid*="empty-state"]')
      const emptyStateCount = await emptyStates.count()
      
      if (emptyStateCount > 0) {
        for (let i = 0; i < emptyStateCount; i++) {
          const emptyState = emptyStates.nth(i)
          
          // Should have proper role or ARIA label
          const role = await emptyState.getAttribute('role')
          const ariaLabel = await emptyState.getAttribute('aria-label')
          
          expect(role || ariaLabel).toBeTruthy()
        }
      }
      
      // Check specific empty state messages
      const noACMessage = page.locator('text="No acceptance criteria analysis available"')
      if (await noACMessage.isVisible()) {
        await expect(noACMessage).toHaveAttribute('role', 'status')
      }
    })

    test('should handle error details accessibility', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'a11y-test-2')

      const technicalDetails = page.locator('summary:has-text("Technical Details")')
      
      // Should be keyboard accessible
      await technicalDetails.focus()
      await expect(technicalDetails).toBeFocused()
      
      // Should expand with keyboard
      await page.keyboard.press('Enter')
      await expect(technicalDetails).toHaveAttribute('aria-expanded', 'true')
      
      // Error details should be properly structured
      const errorType = page.locator('text="AccessibilityParseError"')
      if (await errorType.isVisible()) {
        const parent = errorType.locator('..')
        const accessibleName = await parent.getAttribute('aria-label') || 
                              await parent.textContent()
        expect(accessibleName).toContain('Error Type')
      }
    })
  })

  test.describe('Cross-modal Accessibility', () => {

    test('should support screen reader navigation patterns', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check landmark navigation
      const landmarks = page.locator('[role="main"], [role="banner"], [role="navigation"], [role="complementary"]')
      const landmarkCount = await landmarks.count()
      expect(landmarkCount).toBeGreaterThan(0)
      
      // Check for proper heading navigation
      const headings = page.locator('h1, h2, h3, h4, h5, h6')
      const headingCount = await headings.count()
      expect(headingCount).toBeGreaterThan(3) // Should have multiple navigation points
      
      // Check for skip links or similar navigation aids
      const skipLinks = page.locator('[href="#main-content"], [data-testid="skip-link"]')
      // Skip links are optional but recommended
    })

    test('should provide appropriate live regions for dynamic content', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Look for live regions
      const liveRegions = page.locator('[aria-live]')
      const liveRegionCount = await liveRegions.count()
      
      if (liveRegionCount > 0) {
        for (let i = 0; i < liveRegionCount; i++) {
          const region = liveRegions.nth(i)
          const liveValue = await region.getAttribute('aria-live')
          expect(['polite', 'assertive']).toContain(liveValue)
        }
      }
      
      // Test dynamic content announcement
      await page.locator('button:has-text("Show Raw Review")').click()
      
      // Check if mode change is announced
      const statusRegion = page.locator('[aria-live="polite"]')
      if (await statusRegion.isVisible()) {
        const announcement = await statusRegion.textContent()
        expect(announcement).toMatch(/view.*mode.*changed|switched.*view/i)
      }
    })

    test('should handle reduced motion preferences', async ({ page }) => {
      // Set reduced motion preference
      await page.emulateMedia({ reducedMotion: 'reduce' })
      
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check that animations respect reduced motion
      const animatedElements = page.locator('[data-testid*="animated"], .transition')
      const animatedCount = await animatedElements.count()
      
      for (let i = 0; i < animatedCount; i++) {
        const element = animatedElements.nth(i)
        if (await element.isVisible()) {
          const computedStyle = await element.evaluate(el => {
            const style = window.getComputedStyle(el)
            return {
              animation: style.animation,
              transition: style.transition
            }
          })
          
          // Should respect prefers-reduced-motion
          const hasReducedMotion = 
            computedStyle.animation.includes('none') || 
            computedStyle.transition.includes('none') ||
            computedStyle.animation === '' ||
            computedStyle.transition === ''
          
          // This is more of a recommendation than a strict requirement
          // but good for accessibility
        }
      }
    })

    test('should support high contrast mode', async ({ page }) => {
      // Simulate high contrast mode
      await page.emulateMedia({ colorScheme: 'dark', forcedColors: 'active' })
      
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check that content is still visible and accessible
      const results = await runAxeAudit(page, ['color-contrast'])
      expect(results.violations).toHaveLength(0)
      
      // Check that important UI elements are still distinguishable
      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toBeVisible()
      
      const toggleButton = page.locator('button:has-text("Show Raw Review")')
      await expect(toggleButton).toBeVisible()
    })
  })

  test.describe('Mobile Accessibility', () => {

    test('should be accessible on touch devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE
      
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check touch target sizes (minimum 44px)
      const touchTargets = page.locator('button, a, [role="button"]')
      const targetCount = await touchTargets.count()
      
      for (let i = 0; i < targetCount; i++) {
        const target = touchTargets.nth(i)
        if (await target.isVisible()) {
          const box = await target.boundingBox()
          if (box) {
            expect(box.width).toBeGreaterThanOrEqual(44)
            expect(box.height).toBeGreaterThanOrEqual(44)
          }
        }
      }
    })

    test('should handle zoom up to 200% without horizontal scrolling', async ({ page }) => {
      await page.setViewportSize({ width: 1280, height: 720 })
      
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Zoom to 200%
      await page.evaluate(() => {
        document.body.style.zoom = '2'
      })

      // Check that content is still accessible
      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toBeVisible()
      
      // Check that horizontal scrolling is not required
      const bodyWidth = await page.evaluate(() => document.body.scrollWidth)
      const viewportWidth = await page.evaluate(() => window.innerWidth)
      
      // Allow some tolerance for zooming effects
      expect(bodyWidth).toBeLessThanOrEqual(viewportWidth * 1.1)
    })
  })

  test.describe('Assistive Technology Integration', () => {

    test('should work with keyboard-only navigation', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Navigate entire interface with keyboard only
      let tabCount = 0
      const maxTabs = 20
      
      while (tabCount < maxTabs) {
        await page.keyboard.press('Tab')
        tabCount++
        
        const activeElement = page.locator(':focus')
        const tagName = await activeElement.evaluate(el => el.tagName.toLowerCase()).catch(() => '')
        
        // Should be able to interact with focused elements
        if (['button', 'a', 'summary'].includes(tagName)) {
          // Element should be visible and accessible
          await expect(activeElement).toBeVisible()
          
          // Should have accessible name
          const accessibleName = await activeElement.getAttribute('aria-label') ||
                                 await activeElement.textContent() ||
                                 await activeElement.getAttribute('title')
          expect(accessibleName?.trim()).toBeTruthy()
        }
      }
      
      // Should be able to navigate back with Shift+Tab
      await page.keyboard.press('Shift+Tab')
      const backElement = page.locator(':focus')
      await expect(backElement).toBeVisible()
    })

    test('should provide meaningful page title and meta information', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check page title
      const title = await page.title()
      expect(title).toMatch(/code.*review|enhanced.*report/i)
      
      // Check meta description if present
      const metaDescription = page.locator('meta[name="description"]')
      if (await metaDescription.count() > 0) {
        const content = await metaDescription.getAttribute('content')
        expect(content).toBeTruthy()
      }
      
      // Check lang attribute
      const htmlLang = await page.locator('html').getAttribute('lang')
      expect(htmlLang).toBeTruthy()
    })

    test('should handle voice control and speech recognition', async ({ page }) => {
      await setupMockAPI(page, 'clientFallback')
      await navigateToReviewResults(page, 'a11y-test-1')

      // Check that interactive elements have voice-friendly labels
      const buttons = page.locator('button')
      const buttonCount = await buttons.count()
      
      for (let i = 0; i < buttonCount; i++) {
        const button = buttons.nth(i)
        if (await button.isVisible()) {
          const accessibleName = await button.getAttribute('aria-label') || 
                                 await button.textContent()
          
          // Should not contain only symbols or be empty
          expect(accessibleName?.trim()).toBeTruthy()
          expect(accessibleName).toMatch(/[a-zA-Z]/) // Should contain letters
        }
      }
    })
  })
})