// Browser Console Test Script
// <PERSON><PERSON><PERSON> das in die Browser-Konsole um Mermaid direkt zu testen

// Test 1: Teste Mermaid-Erkennung
console.log('🧪 Testing Mermaid Detection...');

const testCodes = [
  { name: 'Graph TB (Screenshot)', code: 'graph TB\n    CMS --> Gateway\n    Gateway --> Vertex' },
  { name: 'Sequence Diagram', code: 'sequenceDiagram\n    A->>B: Hello' },
  { name: 'Simple Arrow', code: 'A --> B\nB --> C' },
  { name: 'TypeScript (should NOT match)', code: 'function test() {\n  return "hello";\n}' }
];

// Importiere die isMermaidCode Funktion (falls exportiert)
// Du kannst das auch manuell testen:
function testMermaidCode(language, code) {
  // Simplified patterns für Test
  const mermaidPatterns = [
    /graph\s+(TB|TD|BT|RL|LR)/i,
    /graph\s*$/im,
    /sequenceDiagram/i,
    /-->/,
  ];
  
  if (language === 'mermaid') return true;
  return mermaidPatterns.some(pattern => pattern.test(code));
}

testCodes.forEach(test => {
  const result = testMermaidCode('', test.code);
  console.log(`${result ? '✅' : '❌'} ${test.name}: ${result}`);
});

// Test 2: Teste Mermaid Import
console.log('\n🔍 Testing Mermaid Import...');
import('mermaid').then(mermaid => {
  console.log('✅ Mermaid imported successfully:', mermaid);
  
  // Test 3: Teste Mermaid Rendering
  console.log('\n🎨 Testing Mermaid Rendering...');
  
  // Erstelle Test-Container
  const container = document.createElement('div');
  container.id = 'mermaid-test';
  container.style.width = '400px';
  container.style.height = '300px';
  container.style.border = '2px solid #blue';
  container.style.position = 'fixed';
  container.style.top = '10px';
  container.style.right = '10px';
  container.style.backgroundColor = 'white';
  container.style.zIndex = '9999';
  document.body.appendChild(container);
  
  // Rendere Test-Diagramm
  mermaid.default.initialize({
    startOnLoad: false,
    theme: 'default'
  });
  
  const testCode = `graph TB
    A[Test] --> B[Success]
    B --> C[Mermaid Works!]`;
  
  mermaid.default.render('test-diagram', testCode)
    .then(({ svg }) => {
      container.innerHTML = svg;
      console.log('🎉 Mermaid rendering successful! Check top-right corner.');
      
      // Auto-remove nach 10 Sekunden
      setTimeout(() => {
        document.body.removeChild(container);
        console.log('🧹 Test diagram removed');
      }, 10000);
    })
    .catch(err => {
      console.error('❌ Mermaid rendering failed:', err);
      container.innerHTML = `<p style="color: red; padding: 10px;">Rendering failed: ${err.message}</p>`;
    });
    
}).catch(err => {
  console.error('❌ Mermaid import failed:', err);
});