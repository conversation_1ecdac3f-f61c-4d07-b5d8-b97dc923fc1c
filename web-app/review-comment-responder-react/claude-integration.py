#!/usr/bin/env python3
"""
Claude Code Integration for Review Comment Responder
This handles the actual Claude Code API calls for processing screenshots
"""

import os
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import base64
import tempfile
from datetime import datetime
import threading
import queue
import uuid

class ClaudeReviewResponder:
    """
    Integrates with Claude Code SDK to process review comment screenshots
    Works in the specific git worktree where the PR branch is checked out
    """
    
    def __init__(self, working_directory: str, worktree_path: str = None):
        # Use worktree path if provided, otherwise fall back to working directory
        if worktree_path and Path(worktree_path).exists():
            self.working_directory = Path(worktree_path)
            print(f"🌳 Using Git Worktree: {self.working_directory}")
        else:
            self.working_directory = Path(working_directory)
            print(f"📂 Using Working Directory: {self.working_directory}")
        
        # Create unique temp dir for this instance
        self.instance_id = f"{os.getpid()}_{id(self)}_{datetime.now().timestamp()}"
        self.temp_dir = Path(tempfile.mkdtemp(prefix=f"review_comments_{self.instance_id}_"))
        
        # Verify we're in a git repository
        try:
            branch_info = subprocess.run(
                ["git", "branch", "--show-current"],
                cwd=self.working_directory,
                capture_output=True,
                text=True,
                check=True
            )
            self.current_branch = branch_info.stdout.strip()
            print(f"🌿 Current branch in worktree: {self.current_branch}")
            
            # Show recent commits for context
            recent_commits = subprocess.run(
                ["git", "log", "--oneline", "-5"],
                cwd=self.working_directory,
                capture_output=True,
                text=True
            )
            print(f"📝 Recent commits:\n{recent_commits.stdout}")
            
        except Exception as e:
            print(f"⚠️  Git info error: {e}")
            self.current_branch = None
        
    def process_screenshot(self, screenshot_data: Dict, pr_context: Dict, preview_only: bool = True) -> Dict:
        """
        Process a single screenshot using Claude Code
        
        Args:
            screenshot_data: Contains base64 image data and metadata
            pr_context: PR information (url, branch, ticket, etc.)
            preview_only: If True, only analyze and preview changes without applying
        
        Returns:
            Response with extracted comment and generated response/preview
        """
        
        # Save screenshot to temp file for Claude Code to read
        screenshot_path = self._save_screenshot(screenshot_data)
        
        # Create timestamp for temp file
        self.current_timestamp = datetime.now().timestamp()
        
        # Create the prompt for Claude Code
        prompt = self._create_prompt(screenshot_path, pr_context, preview_only)
        
        # Execute Claude Code
        result = self._run_claude_code(prompt, screenshot_path)
        
        # Parse and return result
        return self._parse_response(result, preview_only)
    
    def _save_screenshot(self, screenshot_data: Dict) -> Path:
        """Save base64 screenshot to temporary file"""
        
        # Extract base64 data (remove data:image/png;base64, prefix)
        base64_data = screenshot_data['dataUrl'].split(',')[1]
        image_data = base64.b64decode(base64_data)
        
        # Save to temp file
        file_path = self.temp_dir / f"comment_{screenshot_data['id']}.png"
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        return file_path
    
    def _create_prompt(self, screenshot_path: Path, pr_context: Dict, preview_only: bool = True) -> str:
        """Create the prompt for Claude Code
        
        Args:
            screenshot_path: Path to the screenshot
            pr_context: PR context information
            preview_only: If True, only analyze and suggest changes without applying them
        """
        
        # We'll use a unique temp filename when copying to worktree
        temp_filename = f"temp_review_screenshot_{self.instance_id}_{self.current_timestamp}.png"
        
        # Check for modification prompt
        modification_prompt = pr_context.get('modificationPrompt', '')
        
        # Different prompts based on preview mode
        if preview_only:
            # PREVIEW MODE: Only analyze and suggest changes, DO NOT apply them
            return f'''Schaue dir die Datei {temp_filename} im aktuellen Verzeichnis an. Es ist ein Screenshot eines Code-Review-Kommentars.

WICHTIG: Dies ist der ANALYSE-MODUS! Du sollst NUR analysieren und Vorschläge machen, KEINE Änderungen durchführen!

Du bist ein intelligenter Code-Review-Agent. Deine Aufgabe im ANALYSE-MODUS:

1. Extrahiere aus dem Screenshot:
   - Den Kommentartext des Reviewers
   - Die kommentierte Datei und Zeile

2. Analysiere die Codebasis GRÜNDLICH:
   - Untersuche die kommentierte Stelle
   - Finde ALLE anderen betroffenen Stellen mit Grep/Glob
   - Verfolge ALLE Abhängigkeiten und Verbindungen
   - Prüfe wo Funktionen/Klassen verwendet werden
   - Analysiere Import-Statements
   - Erstelle einen VOLLSTÄNDIGEN Änderungsplan

3. WICHTIG - Im Analyse-Modus:
   - NUR lesen und analysieren (Read, Grep, Glob Tools)
   - KEINE Änderungen machen (KEIN Edit, Write, etc.)
   - KEINE Tests/Builds/Typechecks ausführen - das macht der Benutzer
   - Einen FINALEN, VOLLSTÄNDIGEN Plan erstellen

PR-Kontext: {pr_context.get('ticketId', 'Feature')}, Branch {pr_context.get('branchName', 'unbekannt')}

{modification_prompt if modification_prompt else ''}

WICHTIGE REGELN:
- Zeige den FINALEN, VOLLSTÄNDIGEN Plan - nichts soll später überraschen!
- Liste ALLE betroffenen Stellen auf, nicht nur die offensichtlichen
- Der Benutzer führt Build/Tests selbst aus - schlage das NICHT als Todo vor
- Konzentriere dich auf die CODE-ÄNDERUNGEN

Antworte auf DEUTSCH und gib einen DETAILLIERTEN Änderungsplan zurück:

{{
    "comment_text": "[extrahierter Kommentar]",
    "file": "[Datei aus Screenshot]",
    "line": "[Zeile aus Screenshot]",
    "response": "[Deine Analyse und Empfehlung auf Deutsch]",
    "confidence": 0.9,
    "analysis": {{
        "affected_files": ["alle betroffenen Dateien"],
        "dependencies": ["Liste aller gefundenen Abhängigkeiten und Verwendungen"],
        "impact": "Beschreibung der Auswirkungen",
        "todo_list": [
            "Schritt 1: Code-Änderung X in Datei Y",
            "Schritt 2: Import-Anpassung in Datei Z",
            "HINWEIS: Build und Tests werden vom Benutzer durchgeführt"
        ]
    }},
    "preview_changes": [  // Geplante Änderungen (NICHT ausführen!)
        {{
            "file": "pfad/zur/datei.ts",
            "reason": "Warum diese Datei geändert werden muss",
            "changes": [
                {{
                    "type": "replace",
                    "line_start": 10,
                    "line_end": 15,
                    "current_content": "EXAKTER aktueller Code",
                    "suggested_content": "Vorgeschlagener neuer Code",
                    "explanation": "Warum diese Änderung nötig ist"
                }}
            ]
        }}
    ]
}}'''
        else:
            # EXECUTION MODE: Actually apply the approved changes
            approved_changes = pr_context.get('approved_changes', [])
            return f'''WICHTIG: EXECUTION MODE - Du musst EXAKT und AUSSCHLIESSLICH die folgenden genehmigten Änderungen durchführen!

GENEHMIGTE ÄNDERUNGEN:
{json.dumps(approved_changes, indent=2, ensure_ascii=False)}

STRIKTE REGELN:
1. Führe EXAKT die oben gelisteten Änderungen aus - NICHTS ANDERES!
2. Ändere GENAU die Zeilen line_start bis line_end wie angegeben
3. Ersetze current_content mit suggested_content - OHNE Anpassungen!
4. Auch wenn der Code danach nicht kompiliert - mache TROTZDEM genau diese Änderungen
5. KEINE eigenen Verbesserungen oder Korrekturen
6. KEINE zusätzlichen Änderungen
7. KEINE Anpassung von Imports oder anderen Stellen
8. Verwende Edit oder MultiEdit Tools für die Änderungen

Der Benutzer hat diese EXAKTEN Änderungen genehmigt. Wenn etwas nicht passt, wird er dir neue Anweisungen geben.

Für jede genehmigte Datei-Änderung:
1. Lies die Datei mit Read
2. Finde EXAKT den current_content an den angegebenen Zeilen
3. Ersetze ihn mit EXAKT dem suggested_content
4. Melde ob erfolgreich oder nicht

Antworte mit:
{{
    "status": "completed",
    "session_active": true,
    "applied_changes": [
        {{
            "file": "pfad/zur/datei.ts",
            "success": true/false,
            "message": "Details was gemacht wurde",
            "line_start": 10,
            "line_end": 15
        }}
    ],
    "notes": "Hinweise falls etwas nicht geklappt hat"
}}'''
    
    def _calculate_max_turns(self, prompt: str, screenshot_path: Path) -> int:
        """Calculate optimal max turns based on complexity indicators"""
        base_turns = 40  # Default turns
        
        # Factors that increase complexity:
        complexity_score = 0
        
        # Long prompts need more turns
        if len(prompt) > 2000:
            complexity_score += 10
        elif len(prompt) > 1000:
            complexity_score += 5
            
        # Screenshots usually need more analysis
        if screenshot_path and screenshot_path.exists():
            complexity_score += 15
            
        # Multiple file references
        file_mentions = prompt.count('.js') + prompt.count('.ts') + prompt.count('.py') + prompt.count('.java')
        complexity_score += min(file_mentions * 3, 15)
        
        # Code analysis keywords
        analysis_keywords = ['refactor', 'optimize', 'performance', 'security', 'architecture', 'design pattern']
        for keyword in analysis_keywords:
            if keyword.lower() in prompt.lower():
                complexity_score += 5
                
        # Multiple questions/requests
        question_count = prompt.count('?') + prompt.count('wie') + prompt.count('warum') + prompt.count('was')
        complexity_score += min(question_count * 2, 10)
        
        # Calculate final turns (minimum 30, maximum 80)
        final_turns = max(30, min(80, base_turns + complexity_score))
        
        print(f"📊 Complexity analysis: score={complexity_score}, turns={final_turns}")
        return final_turns
    
    def _run_claude_code(self, prompt: str, screenshot_path: Path) -> str:
        """Execute Claude Code with the prompt - based on claude_code_reviewer.py implementation"""
        
        try:
            # Use the same approach as claude_code_reviewer.py
            # Dynamically adjust max turns based on complexity
            max_turns = self._calculate_max_turns(prompt, screenshot_path)
            
            cmd = [
                "claude", 
                "-p", prompt,
                "--output-format", "text",
                "--max-turns", str(max_turns)
            ]
            
            print(f"🤖 Running Claude Code for comment analysis...")
            print(f"🔄 Max turns allocated: {max_turns}")
            print(f"📍 Working directory: {self.working_directory}")
            print(f"📂 Temp directory: {self.temp_dir}")
            print(f"🖼️ Screenshot files in temp dir: {list(self.temp_dir.glob('*.png'))}")
            
            # Run Claude from the worktree directory so it has access to the code
            # Copy screenshot to worktree temporarily with unique name
            temp_screenshot = self.working_directory / f"temp_review_screenshot_{self.instance_id}_{self.current_timestamp}.png"
            import shutil
            shutil.copy(str(screenshot_path), str(temp_screenshot))
            
            result = subprocess.run(
                cmd,
                cwd=str(self.working_directory),  # Run from worktree so Claude has code access
                capture_output=True,
                text=True,
                check=True,
                timeout=600  # 10 minute timeout for complex agent analysis
            )
            
            # Clean up temp screenshot
            if temp_screenshot.exists():
                temp_screenshot.unlink()
            
            print(f"✅ Claude Code executed successfully")
            print(f"📝 Output length: {len(result.stdout)} characters")
            
            if result.returncode != 0:
                raise Exception(f"Claude Code failed with exit code {result.returncode}: {result.stderr}")
            
            return result.stdout.strip()
            
        except subprocess.TimeoutExpired:
            raise Exception("Claude Code timed out after 10 minutes")
        except subprocess.CalledProcessError as e:
            error_msg = f"Claude Code error (Exit Code: {e.returncode})"
            if e.stderr:
                error_msg += f"\nStderr: {e.stderr}"
            if e.stdout:
                error_msg += f"\nStdout: {e.stdout}"
            
            # Check for specific error types
            if "Reached max turns" in error_msg or "max turns" in error_msg.lower():
                raise Exception("Analysis zu komplex: Das Code-Review benötigt mehr Gesprächsrunden als erlaubt. Bitte vereinfache den Kommentar oder teile ihn in kleinere Teile auf.")
            elif "timeout" in error_msg.lower():
                raise Exception("Zeitüberschreitung: Die Analyse dauerte zu lange. Bitte versuche es mit einem einfacheren Kommentar erneut.")
            else:
                raise Exception(error_msg)
        except FileNotFoundError:
            raise Exception(
                "Claude Code is not installed or not in PATH.\n"
                "Please install: npm install -g @anthropic-ai/claude-code"
            )
    
    def _parse_response(self, claude_output: str, preview_only: bool = True) -> Dict:
        """Parse Claude Code output - handles both JSON and structured text responses"""
        
        try:
            # First try to find JSON in the output
            import re
            
            # Try to find JSON block
            json_match = re.search(r'\{[\s\S]*"comment_text"[\s\S]*\}', claude_output)
            
            if json_match:
                # Found JSON, parse it
                response_data = json.loads(json_match.group())
                
                # Validate response structure
                required_fields = ['comment_text', 'file', 'line', 'response', 'confidence']
                for field in required_fields:
                    if field not in response_data:
                        response_data[field] = 'Unknown' if field != 'confidence' else 0.5
                
                # Process analysis data if present
                if 'analysis' in response_data:
                    # Keep the analysis data for UI display
                    pass
                
                # Process code changes or preview changes based on mode
                if preview_only and 'preview_changes' in response_data:
                    # In preview mode, we get preview_changes instead of code_changes
                    response_data['preview_changes'] = response_data.get('preview_changes', [])
                    response_data['preview_mode'] = True
                elif 'code_changes' in response_data:
                    response_data['code_changes'] = self._validate_code_changes(response_data['code_changes'])
                    response_data['preview_mode'] = False
                
                return response_data
            
            # No JSON found - parse structured text response
            # Claude might respond with a natural language answer
            print(f"No JSON found, parsing text response. Output length: {len(claude_output)}")
            
            # Try to extract information from the text
            response_data = {
                "comment_text": "Review comment from screenshot",
                "file": "Unknown",
                "line": "Unknown",
                "reviewer": "Unknown",
                "response": claude_output.strip(),  # Use the entire output as response
                "confidence": 0.8  # Default confidence for text responses
            }
            
            # Try to extract file and line info if mentioned in the response
            file_match = re.search(r'(?:file|File):\s*([^\s,]+)', claude_output)
            if file_match:
                response_data["file"] = file_match.group(1)
            
            line_match = re.search(r'(?:line|Line):\s*(\d+)', claude_output)
            if line_match:
                response_data["line"] = line_match.group(1)
            
            # Look for comment text if Claude mentions it
            comment_match = re.search(r'(?:comment|Comment|Review comment):\s*"([^"]+)"', claude_output)
            if comment_match:
                response_data["comment_text"] = comment_match.group(1)
            
            return response_data
            
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {str(e)}")
        except Exception as e:
            print(f"Parse error: {str(e)}")
            
        # Fallback response
        return {
            "comment_text": "Failed to extract comment",
            "file": "Unknown",
            "line": "Unknown", 
            "reviewer": "Unknown",
            "response": claude_output.strip() if claude_output else "Error processing comment",
            "confidence": 0.0
        }
    
    def _validate_code_changes(self, code_changes):
        """Validate and process code changes"""
        if not isinstance(code_changes, list):
            return []
        
        validated_changes = []
        for change in code_changes:
            if isinstance(change, dict) and 'file' in change and 'changes' in change:
                # Verify file exists
                file_path = self.working_directory / change['file']
                if file_path.exists():
                    validated_changes.append(change)
        
        return validated_changes
    
    def apply_code_changes(self, code_changes):
        """Apply code changes to files"""
        results = []
        
        for file_change in code_changes:
            file_path = self.working_directory / file_change['file']
            
            try:
                # Read current file content
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # Apply changes in reverse order to maintain line numbers
                for change in reversed(file_change['changes']):
                    if change['type'] == 'replace':
                        start = change['line_start'] - 1  # Convert to 0-based
                        end = change['line_end']  # line_end is inclusive in Claude's format
                        
                        # Handle new content
                        if change['new_content'].strip():  # If there's new content
                            new_lines = change['new_content'].split('\n')
                            # Replace lines including the end line
                            lines[start:end] = [line + '\n' if not line.endswith('\n') else line for line in new_lines]
                        else:  # If new_content is empty, just delete the lines
                            # Delete lines from start to end (inclusive)
                            del lines[start:end]
                
                # Write back
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.writelines(lines)
                
                results.append({
                    'file': file_change['file'],
                    'success': True
                })
                
            except Exception as e:
                results.append({
                    'file': file_change['file'],
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    def cleanup(self):
        """Clean up temporary files"""
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)


# Session Manager for keeping Claude sessions alive
class SessionManager:
    def __init__(self):
        self.sessions = {}  # session_id -> session_data
        self.lock = threading.Lock()
    
    def create_session(self, comment_id: str, worktree_path: str) -> str:
        """Create a new session for a comment"""
        session_id = f"session_{comment_id}_{uuid.uuid4().hex[:8]}"
        
        with self.lock:
            self.sessions[session_id] = {
                'comment_id': comment_id,
                'worktree_path': worktree_path,
                'status': 'active',
                'created_at': datetime.now(),
                'history': []
            }
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session data"""
        with self.lock:
            return self.sessions.get(session_id)
    
    def update_session(self, session_id: str, data: Dict):
        """Update session data"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id].update(data)
                self.sessions[session_id]['last_updated'] = datetime.now()
    
    def close_session(self, session_id: str):
        """Close and remove a session"""
        with self.lock:
            if session_id in self.sessions:
                self.sessions[session_id]['status'] = 'closed'
                # Keep for history, but mark as closed
    
    def get_active_sessions(self) -> List[Dict]:
        """Get all active sessions"""
        with self.lock:
            return [
                {'session_id': sid, **data} 
                for sid, data in self.sessions.items() 
                if data['status'] == 'active'
            ]

# Global session manager
session_manager = SessionManager()

# Flask API for web interface
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=['http://localhost:5173'], methods=['GET', 'POST', 'OPTIONS'], allow_headers=['Content-Type'])

@app.route('/api/process-comment', methods=['POST'])
def process_comment():
    """
    Process a single comment screenshot
    
    Expected POST data:
    {
        "screenshot": {
            "id": 1,
            "dataUrl": "data:image/png;base64,...",
            "fileName": "comment_1.png"
        },
        "context": {
            "prUrl": "...",
            "branchName": "...",
            "ticketId": "...",
            "changesSummary": "..."
        },
        "modificationPrompt": "optional prompt to modify suggested changes"
    }
    """
    responder = None
    try:
        data = request.json
        screenshot_data = data['screenshot']
        pr_context = data['context']
        modification_prompt = data.get('modificationPrompt')
        
        # Initialize responder with worktree path if provided
        working_dir = pr_context.get('workingDirectory', '.')
        worktree_path = pr_context.get('worktreePath')
        responder = ClaudeReviewResponder(working_dir, worktree_path)
        
        # Add modification prompt to context if provided
        if modification_prompt:
            pr_context['modificationPrompt'] = modification_prompt
        
        # Process the screenshot
        result = responder.process_screenshot(screenshot_data, pr_context)
        
        # Create session if preview mode detected
        session_id = None
        if result.get('preview_mode', False):
            session_id = session_manager.create_session(
                comment_id=str(screenshot_data['id']),
                worktree_path=worktree_path or working_dir
            )
            session_manager.update_session(session_id, {
                'preview_result': result,
                'pr_context': pr_context,
                'original_screenshot': screenshot_data  # Store original screenshot
            })
        
        # Clean up immediately after processing
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'response': result,
            'session_id': session_id
        })
        
    except Exception as e:
        # Ensure cleanup even on error
        if responder:
            responder.cleanup()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/execute-approved-changes', methods=['POST'])
def execute_approved_changes():
    """
    Execute approved changes after preview
    """
    responder = None
    try:
        data = request.json
        approved_changes = data['approved_changes']
        worktree_path = data['worktreePath']
        comment_id = data['comment_id']
        
        # Initialize responder
        responder = ClaudeReviewResponder('.', worktree_path)
        
        # Create context with approved changes
        pr_context = {
            'approved_changes': approved_changes,
            'worktreePath': worktree_path
        }
        
        # Run Claude in execution mode (preview_only=False)
        # Create a dummy screenshot data (we don't need the screenshot again)
        dummy_screenshot = {
            'id': comment_id,
            'dataUrl': 'data:image/png;base64,',  # Empty image
            'fileName': f'approved_changes_{comment_id}.png'
        }
        
        result = responder.process_screenshot(dummy_screenshot, pr_context, preview_only=False)
        
        # Cleanup
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'response': result
        })
        
    except Exception as e:
        if responder:
            responder.cleanup()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/apply-changes', methods=['POST'])
def apply_changes():
    """
    Apply code changes to files (legacy endpoint for direct changes)
    """
    try:
        data = request.json
        code_changes = data['code_changes']
        worktree_path = data['worktreePath']
        
        # Initialize responder
        responder = ClaudeReviewResponder('.', worktree_path)
        
        # Apply changes
        results = responder.apply_code_changes(code_changes)
        
        # Cleanup
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/batch-process', methods=['POST'])
def batch_process():
    """
    Process multiple screenshots in batch
    """
    try:
        data = request.json
        screenshots = data['screenshots']
        pr_context = data['context']
        
        # Initialize responder
        working_dir = pr_context.get('workingDirectory', '.')
        responder = ClaudeReviewResponder(working_dir)
        
        results = []
        for screenshot in screenshots:
            try:
                result = responder.process_screenshot(screenshot, pr_context)
                results.append({
                    'id': screenshot['id'],
                    'success': True,
                    'response': result
                })
            except Exception as e:
                results.append({
                    'id': screenshot['id'],
                    'success': False,
                    'error': str(e)
                })
        
        # Cleanup
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'results': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/git-diff', methods=['POST'])
def get_git_diff():
    """
    Get git diff for the worktree
    Similar to bitbucket_pr_analyzer's approach
    """
    try:
        data = request.json
        worktree_path = data.get('worktreePath')
        branch_name = data.get('branchName')
        
        if not worktree_path:
            return jsonify({
                'success': False,
                'error': 'Worktree path is required'
            }), 400
        
        worktree_path = Path(worktree_path)
        if not worktree_path.exists():
            return jsonify({
                'success': False,
                'error': f'Worktree path does not exist: {worktree_path}'
            }), 400
        
        # Get current branch
        try:
            current_branch = subprocess.run(
                ["git", "branch", "--show-current"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
        except:
            current_branch = branch_name or "unknown"
        
        # Determine target branch (main or master)
        try:
            branches = subprocess.run(
                ["git", "branch", "-r"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
            
            if "origin/main" in branches:
                target_branch = "origin/main"
            elif "origin/master" in branches:
                target_branch = "origin/master"
            else:
                target_branch = "origin/develop"  # fallback
                
        except:
            target_branch = "origin/main"
        
        # Get merge base
        try:
            merge_base = subprocess.run(
                ["git", "merge-base", target_branch, "HEAD"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
        except:
            # Fallback to simple diff
            merge_base = target_branch
        
        # Get the diff with more context
        try:
            # Get unified diff with more context lines
            diff = subprocess.run(
                ["git", "diff", f"{merge_base}...HEAD", "--unified=3"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Failed to get diff: {str(e)}'
            }), 500
        
        # Get changed files
        try:
            changed_files_output = subprocess.run(
                ["git", "diff", "--name-status", f"{merge_base}...HEAD"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
            
            changed_files = []
            
            # Get file stats for each changed file
            for line in changed_files_output.strip().split('\n'):
                if line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        status = parts[0]
                        filename = parts[1]
                        
                        # Get stats for this file
                        try:
                            file_stats = subprocess.run(
                                ["git", "diff", "--numstat", f"{merge_base}...HEAD", "--", filename],
                                cwd=worktree_path,
                                capture_output=True,
                                text=True,
                                check=True
                            ).stdout.strip()
                            
                            additions = 0
                            deletions = 0
                            if file_stats:
                                stat_parts = file_stats.split('\t')
                                if len(stat_parts) >= 2:
                                    additions = int(stat_parts[0]) if stat_parts[0] != '-' else 0
                                    deletions = int(stat_parts[1]) if stat_parts[1] != '-' else 0
                        except:
                            additions = 0
                            deletions = 0
                        
                        # Handle renamed files
                        if status.startswith('R') and len(parts) >= 3:
                            old_filename = parts[1]
                            new_filename = parts[2]
                            changed_files.append({
                                'status': 'R',
                                'filename': new_filename,
                                'oldFilename': old_filename,
                                'displayName': f"{old_filename} → {new_filename}",
                                'additions': additions,
                                'deletions': deletions
                            })
                        else:
                            changed_files.append({
                                'status': status[0],  # First letter only
                                'filename': filename,
                                'additions': additions,
                                'deletions': deletions
                            })
                        
        except:
            changed_files = []
        
        return jsonify({
            'success': True,
            'diff': diff,
            'changedFiles': changed_files,
            'currentBranch': current_branch,
            'targetBranch': target_branch,
            'stats': {
                'additions': diff.count('\n+') - diff.count('\n+++'),
                'deletions': diff.count('\n-') - diff.count('\n---'),
                'files': len(changed_files)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/file-diff', methods=['POST'])
def get_file_diff():
    """
    Get diff for a specific file
    """
    try:
        data = request.json
        worktree_path = data.get('worktreePath')
        filename = data.get('filename')
        
        if not worktree_path or not filename:
            return jsonify({
                'success': False,
                'error': 'Worktree path and filename are required'
            }), 400
        
        worktree_path = Path(worktree_path)
        if not worktree_path.exists():
            return jsonify({
                'success': False,
                'error': f'Worktree path does not exist: {worktree_path}'
            }), 400
        
        # Get target branch
        try:
            branches = subprocess.run(
                ["git", "branch", "-r"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
            
            if "origin/main" in branches:
                target_branch = "origin/main"
            elif "origin/master" in branches:
                target_branch = "origin/master"
            else:
                target_branch = "origin/develop"
        except:
            target_branch = "origin/main"
        
        # Get merge base
        try:
            merge_base = subprocess.run(
                ["git", "merge-base", target_branch, "HEAD"],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
        except:
            merge_base = target_branch
        
        # Get file diff with line numbers
        try:
            diff = subprocess.run(
                ["git", "diff", f"{merge_base}...HEAD", "--", filename],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout
            
            # Get file stats
            stats = subprocess.run(
                ["git", "diff", "--numstat", f"{merge_base}...HEAD", "--", filename],
                cwd=worktree_path,
                capture_output=True,
                text=True,
                check=True
            ).stdout.strip()
            
            additions = 0
            deletions = 0
            if stats:
                parts = stats.split('\t')
                if len(parts) >= 2:
                    additions = int(parts[0]) if parts[0] != '-' else 0
                    deletions = int(parts[1]) if parts[1] != '-' else 0
            
            return jsonify({
                'success': True,
                'filename': filename,
                'diff': diff,
                'stats': {
                    'additions': additions,
                    'deletions': deletions
                }
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Failed to get file diff: {str(e)}'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/session/<session_id>/execute', methods=['POST'])
def execute_session_changes(session_id):
    """Execute approved changes for a specific session"""
    responder = None
    try:
        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found or expired'
            }), 404
        
        if session['status'] != 'active':
            return jsonify({
                'success': False,
                'error': 'Session is not active'
            }), 400
        
        data = request.json
        approved_changes = data.get('approved_changes', [])
        
        # Initialize responder
        responder = ClaudeReviewResponder('.', session['worktree_path'])
        
        # Create context with approved changes
        pr_context = session.get('pr_context', {})
        pr_context['approved_changes'] = approved_changes
        
        # Run Claude in execution mode
        dummy_screenshot = {
            'id': session['comment_id'],
            'dataUrl': 'data:image/png;base64,',
            'fileName': f'session_{session_id}.png'
        }
        
        result = responder.process_screenshot(dummy_screenshot, pr_context, preview_only=False)
        
        # Update session history
        session_manager.update_session(session_id, {
            'history': session.get('history', []) + [{
                'timestamp': datetime.now().isoformat(),
                'action': 'execute',
                'result': result
            }]
        })
        
        # Cleanup
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'response': result,
            'session_id': session_id,
            'session_active': True
        })
        
    except Exception as e:
        if responder:
            responder.cleanup()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/session/<session_id>/modify', methods=['POST'])
def modify_session_instructions(session_id):
    """Modify instructions for a session and re-analyze"""
    responder = None
    try:
        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found or expired'
            }), 404
        
        data = request.json
        modification_prompt = data.get('modification_prompt', '')
        
        # Get original data from session
        pr_context = session.get('pr_context', {}).copy()  # Make a copy to avoid mutations
        pr_context['modificationPrompt'] = modification_prompt
        
        # Get original screenshot if stored
        original_screenshot = session.get('original_screenshot')
        if not original_screenshot:
            # Create a minimal screenshot data
            original_screenshot = {
                'id': session['comment_id'],
                'dataUrl': 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',  # 1x1 transparent PNG
                'fileName': f'session_{session_id}.png'
            }
        
        # Re-run analysis with modification
        responder = ClaudeReviewResponder('.', session['worktree_path'])
        
        # Process with modification
        result = responder.process_screenshot(original_screenshot, pr_context, preview_only=True)
        
        # Update session
        session_manager.update_session(session_id, {
            'history': session.get('history', []) + [{
                'timestamp': datetime.now().isoformat(),
                'action': 'modify',
                'modification_prompt': modification_prompt,
                'result': result
            }],
            'latest_result': result
        })
        
        # Cleanup
        responder.cleanup()
        
        return jsonify({
            'success': True,
            'response': result,
            'session_id': session_id
        })
        
    except Exception as e:
        import traceback
        print(f"Error in modify session: {str(e)}")
        print(traceback.format_exc())
        
        if responder:
            responder.cleanup()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/session/<session_id>/close', methods=['POST'])
def close_session(session_id):
    """Close a session when user confirms everything works"""
    try:
        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        session_manager.close_session(session_id)
        
        return jsonify({
            'success': True,
            'message': 'Session closed successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/sessions/active', methods=['GET'])
def get_active_sessions():
    """Get all active sessions"""
    try:
        active_sessions = session_manager.get_active_sessions()
        return jsonify({
            'success': True,
            'sessions': active_sessions
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/git/check-branch', methods=['POST', 'OPTIONS'])
def check_local_branch():
    """
    Check if a branch exists locally and get worktree path
    
    Expected POST data:
    {
        "repository": "repository-name",
        "branch": "branch-name",
        "remoteUrl": "optional-remote-url"
    }
    """
    # Handle CORS preflight request
    if request.method == 'OPTIONS':
        return '', 200
    
    try:
        data = request.json
        repository = data.get('repository')
        branch = data.get('branch')
        remote_url = data.get('remoteUrl')
        
        if not repository or not branch:
            return jsonify({
                'success': False,
                'error': 'Repository and branch are required'
            }), 400
        
        # Common paths to check for git repositories
        common_paths = [
            f"~/dev/{repository}",
            f"~/projects/{repository}",
            f"~/work/{repository}",
            f"~/code/{repository}",
            f"./{repository}",
            f"../{repository}"
        ]
        
        # Expand ~ to actual home directory
        import os.path
        expanded_paths = []
        for path in common_paths:
            if path.startswith('~/'):
                expanded_path = os.path.expanduser(path)
            else:
                expanded_path = os.path.abspath(path)
            expanded_paths.append(expanded_path)
        
        worktree_path = None
        is_up_to_date = False
        remote_ref = None
        
        # Check each path for the repository
        for path in expanded_paths:
            repo_path = Path(path)
            git_path = repo_path / '.git'
            
            if repo_path.exists() and (git_path.exists() or git_path.is_file()):
                try:
                    # Check if this is a git repository
                    result = subprocess.run(
                        ['git', 'rev-parse', '--git-dir'],
                        cwd=repo_path,
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    
                    # Check if the branch exists locally
                    branch_check = subprocess.run(
                        ['git', 'show-ref', '--verify', '--quiet', f'refs/heads/{branch}'],
                        cwd=repo_path,
                        capture_output=True
                    )
                    
                    if branch_check.returncode == 0:
                        # Branch exists locally
                        worktree_path = str(repo_path)
                        
                        # Check if it's up to date with remote (if remote exists)
                        try:
                            # Fetch latest info (don't pull, just fetch)
                            subprocess.run(
                                ['git', 'fetch', '--dry-run'],
                                cwd=repo_path,
                                capture_output=True,
                                timeout=10
                            )
                            
                            # Check if local branch is behind remote
                            local_commit = subprocess.run(
                                ['git', 'rev-parse', branch],
                                cwd=repo_path,
                                capture_output=True,
                                text=True,
                                check=True
                            ).stdout.strip()
                            
                            try:
                                remote_commit = subprocess.run(
                                    ['git', 'rev-parse', f'origin/{branch}'],
                                    cwd=repo_path,
                                    capture_output=True,
                                    text=True,
                                    check=True
                                ).stdout.strip()
                                
                                is_up_to_date = (local_commit == remote_commit)
                                remote_ref = f'origin/{branch}'
                                
                            except subprocess.CalledProcessError:
                                # Remote branch doesn't exist
                                is_up_to_date = True
                                
                        except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                            # Can't check remote status
                            is_up_to_date = None
                        
                        break
                    
                except subprocess.CalledProcessError:
                    # Not a git repo or other error
                    continue
        
        return jsonify({
            'exists': worktree_path is not None,
            'worktree_path': worktree_path,
            'is_up_to_date': is_up_to_date,
            'remote_ref': remote_ref
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'active_sessions': len(session_manager.get_active_sessions())
    })


if __name__ == '__main__':
    # Run Flask server with threading enabled for parallel processing
    PORT = 5001  # Changed from 5000 to avoid macOS AirPlay conflict
    print(f"🚀 Starting Claude Review Responder API on http://localhost:{PORT}")
    print(f"🔧 Threading enabled - can process multiple comments in parallel")
    app.run(debug=True, port=PORT, threaded=True)