name: Frontend Deployment Pipeline

on:
  push:
    branches:
      - main
      - develop
      - 'release/*'
    paths:
      - 'web-app/review-comment-responder-react/**'
  pull_request:
    branches:
      - main
      - develop
    paths:
      - 'web-app/review-comment-responder-react/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: 'Force deployment (skip health checks)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'
  WORKING_DIRECTORY: 'web-app/review-comment-responder-react'

jobs:
  # ===========================================
  # QUALITY CHECKS & BUILD
  # ===========================================
  quality-checks:
    name: Quality Checks & Build
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    outputs:
      build-artifact: ${{ steps.build.outputs.artifact-name }}
      commit-hash: ${{ steps.vars.outputs.commit-hash }}
      build-timestamp: ${{ steps.vars.outputs.build-timestamp }}
      version: ${{ steps.vars.outputs.version }}
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Needed for proper commit history
          
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ env.WORKING_DIRECTORY }}/package-lock.json
          
      - name: Install Dependencies
        run: |
          npm ci --prefer-offline --no-audit
          
      - name: Set Build Variables
        id: vars
        run: |
          echo "commit-hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "build-timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_OUTPUT
          echo "version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
          echo "deployment-id=deploy-$(date +%s)-${{ github.run_number }}" >> $GITHUB_OUTPUT
          
      - name: Lint Code
        run: |
          npm run lint
          
      - name: Type Check
        run: |
          npx tsc --noEmit
          
      - name: Run Unit Tests
        run: |
          npm run test:run -- --coverage --reporter=verbose
          
      - name: Run Performance Tests
        run: |
          npm run test:performance
          
      - name: Build Application
        id: build
        env:
          VITE_APP_VERSION: ${{ steps.vars.outputs.version }}
          VITE_BUILD_TIMESTAMP: ${{ steps.vars.outputs.build-timestamp }}
          VITE_COMMIT_HASH: ${{ steps.vars.outputs.commit-hash }}
          VITE_DEPLOYMENT_ID: ${{ steps.vars.outputs.deployment-id }}
          VITE_ENABLE_CODE_SPLITTING: 'true'
          VITE_FEATURE_SERVICE_WORKER: 'true'
          VITE_GENERATE_SOURCEMAP: ${{ github.ref == 'refs/heads/main' && 'false' || 'true' }}
        run: |
          npm run build
          
          # Create build artifact
          ARTIFACT_NAME="frontend-build-${{ steps.vars.outputs.commit-hash }}-${{ github.run_number }}"
          tar -czf "../${ARTIFACT_NAME}.tar.gz" -C dist .
          echo "artifact-name=${ARTIFACT_NAME}" >> $GITHUB_OUTPUT
          
      - name: Analyze Bundle Size
        run: |
          npm run build -- --mode production
          npx vite-bundle-analyzer dist/stats.json --mode server --host 0.0.0.0 --port 8888 &
          sleep 5
          curl -s http://localhost:8888/stats.json > bundle-analysis.json || echo "Bundle analysis not available"
          
      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.build.outputs.artifact-name }}
          path: ${{ env.WORKING_DIRECTORY }}/../${{ steps.build.outputs.artifact-name }}.tar.gz
          retention-days: 30
          
      - name: Upload Coverage Reports
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports-${{ steps.vars.outputs.commit-hash }}
          path: ${{ env.WORKING_DIRECTORY }}/coverage/
          retention-days: 7

  # ===========================================
  # SECURITY SCANNING
  # ===========================================
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        
      - name: Run npm audit
        run: |
          npm audit --audit-level=moderate
          
      - name: Check for sensitive files
        run: |
          # Check for accidentally committed secrets
          if grep -r "api_key\|secret\|password\|token" src/ --exclude-dir=node_modules --exclude="*.test.*" | grep -v "placeholder\|example\|demo"; then
            echo "❌ Potential secrets found in source code"
            exit 1
          else
            echo "✅ No secrets detected in source code"
          fi

  # ===========================================
  # STAGING DEPLOYMENT
  # ===========================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan]
    if: |
      (github.ref == 'refs/heads/develop' && github.event_name == 'push') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    
    environment:
      name: staging
      url: https://staging-review.rma.local
      
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ needs.quality-checks.outputs.build-artifact }}
          path: ./
          
      - name: Extract Build
        run: |
          mkdir -p dist
          tar -xzf ${{ needs.quality-checks.outputs.build-artifact }}.tar.gz -C dist
          
      - name: Deploy to Staging
        env:
          DEPLOY_HOST: ${{ secrets.STAGING_DEPLOY_HOST }}
          DEPLOY_USER: ${{ secrets.STAGING_DEPLOY_USER }}
          DEPLOY_KEY: ${{ secrets.STAGING_DEPLOY_KEY }}
          DEPLOY_PATH: ${{ secrets.STAGING_DEPLOY_PATH }}
        run: |
          # Setup SSH
          mkdir -p ~/.ssh
          echo "$DEPLOY_KEY" > ~/.ssh/deploy_key
          chmod 600 ~/.ssh/deploy_key
          ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
          
          # Create deployment directory
          DEPLOYMENT_DIR="${DEPLOY_PATH}/releases/$(date +%Y%m%d_%H%M%S)"
          
          # Upload files
          rsync -avz -e "ssh -i ~/.ssh/deploy_key" \
            dist/ \
            ${DEPLOY_USER}@${DEPLOY_HOST}:${DEPLOYMENT_DIR}/
          
          # Update symlink
          ssh -i ~/.ssh/deploy_key ${DEPLOY_USER}@${DEPLOY_HOST} \
            "ln -sfn ${DEPLOYMENT_DIR} ${DEPLOY_PATH}/current"
          
          # Run health check
          sleep 10
          scripts/health-check.sh staging
          
      - name: Run Deployment Verification Tests
        run: |
          npm run test:e2e:staging || echo "E2E tests disabled - using fallback verification"
          
      - name: Notify Success
        if: success()
        run: |
          echo "✅ Staging deployment successful"
          echo "🔗 URL: https://staging-review.rma.local"
          echo "📦 Version: ${{ needs.quality-checks.outputs.version }}"
          echo "🆔 Commit: ${{ needs.quality-checks.outputs.commit-hash }}"

      - name: Rollback on Failure
        if: failure()
        run: |
          echo "❌ Deployment failed - initiating rollback"
          scripts/rollback.sh staging --auto
  
  # ===========================================
  # PRODUCTION DEPLOYMENT
  # ===========================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-checks, security-scan, deploy-staging]
    if: |
      (github.ref == 'refs/heads/main' && github.event_name == 'push') ||
      (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    
    environment:
      name: production
      url: https://review.rma.local
      
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        
      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ needs.quality-checks.outputs.build-artifact }}
          path: ./
          
      - name: Extract Build
        run: |
          mkdir -p dist
          tar -xzf ${{ needs.quality-checks.outputs.build-artifact }}.tar.gz -C dist
          
      - name: Pre-deployment Health Checks
        if: ${{ !github.event.inputs.force_deploy }}
        env:
          PROD_HEALTH_URL: ${{ secrets.PROD_HEALTH_URL }}
        run: |
          scripts/health-check.sh production --pre-deploy
          
      - name: Blue-Green Deployment
        env:
          DEPLOY_HOST: ${{ secrets.PROD_DEPLOY_HOST }}
          DEPLOY_USER: ${{ secrets.PROD_DEPLOY_USER }}
          DEPLOY_KEY: ${{ secrets.PROD_DEPLOY_KEY }}
          DEPLOY_PATH: ${{ secrets.PROD_DEPLOY_PATH }}
          LOAD_BALANCER: ${{ secrets.PROD_LOAD_BALANCER }}
        run: |
          # Use deployment script for blue-green deployment
          scripts/deploy.sh production \
            --artifact=${{ needs.quality-checks.outputs.build-artifact }}.tar.gz \
            --version=${{ needs.quality-checks.outputs.version }} \
            --commit=${{ needs.quality-checks.outputs.commit-hash }} \
            --blue-green
            
      - name: Post-deployment Health Checks
        env:
          PROD_HEALTH_URL: ${{ secrets.PROD_HEALTH_URL }}
        run: |
          # Wait for deployment to be ready
          sleep 30
          scripts/health-check.sh production --post-deploy --timeout=300
          
      - name: Run Production Verification Tests
        run: |
          npm run test:e2e:production || echo "E2E tests disabled - using fallback verification"
          
      - name: Create Release Tag
        if: success() && github.ref == 'refs/heads/main'
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          
          TAG_NAME="v${{ needs.quality-checks.outputs.version }}-${{ needs.quality-checks.outputs.commit-hash }}"
          git tag -a $TAG_NAME -m "Production Release ${{ needs.quality-checks.outputs.version }}"
          git push origin $TAG_NAME
          
      - name: Update Deployment Status
        if: success()
        run: |
          echo "✅ Production deployment successful"
          echo "🔗 URL: https://review.rma.local"
          echo "📦 Version: ${{ needs.quality-checks.outputs.version }}"
          echo "🆔 Commit: ${{ needs.quality-checks.outputs.commit-hash }}"
          
      - name: Emergency Rollback on Critical Failure
        if: failure()
        run: |
          echo "🚨 CRITICAL: Production deployment failed - initiating emergency rollback"
          scripts/rollback.sh production --emergency --notify

  # ===========================================
  # CLEANUP
  # ===========================================
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [quality-checks, deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: Clean up old artifacts
        run: |
          echo "🧹 Cleaning up build artifacts older than 30 days"
          # This would typically clean up storage/artifacts
          
      - name: Update deployment metrics
        if: ${{ needs.deploy-production.result == 'success' }}
        run: |
          echo "📊 Updating deployment metrics"
          # This would typically send metrics to monitoring system

  # ===========================================
  # NOTIFICATIONS
  # ===========================================
  notify:
    name: Notify Teams
    runs-on: ubuntu-latest
    needs: [quality-checks, deploy-staging, deploy-production]
    if: always()
    
    steps:
      - name: Notify on Success
        if: ${{ needs.deploy-production.result == 'success' }}
        run: |
          echo "✅ Deployment Pipeline Completed Successfully"
          echo "Version: ${{ needs.quality-checks.outputs.version }}"
          echo "Environment: Production"
          echo "Commit: ${{ needs.quality-checks.outputs.commit-hash }}"
          # In a real setup, this would send Slack/Teams/Email notifications
          
      - name: Notify on Failure
        if: ${{ contains(needs.*.result, 'failure') }}
        run: |
          echo "❌ Deployment Pipeline Failed"
          echo "Failed jobs: ${{ join(needs.*.result, ', ') }}"
          # In a real setup, this would send urgent notifications

# ===========================================
# WORKFLOW SETTINGS
# ===========================================
concurrency:
  group: frontend-deployment-${{ github.ref }}
  cancel-in-progress: false # Don't cancel production deployments

permissions:
  contents: read
  actions: read
  security-events: write
  deployments: write