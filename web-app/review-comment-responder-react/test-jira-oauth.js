#!/usr/bin/env node
/**
 * Test Jira OAuth Flow
 * Run this script to test the OAuth configuration and flow
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🔧 Jira OAuth Setup Helper\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  try {
    console.log('This helper will generate the authorization URL for testing.\n');
    
    const clientId = await askQuestion('Enter your Jira Client ID: ');
    
    if (!clientId || clientId === 'your_client_id_here') {
      console.log('❌ Please set up your Jira OAuth app first and get the Client ID');
      process.exit(1);
    }
    
    const state = Math.random().toString(36).substring(7);
    
    const authUrl = `https://auth.atlassian.com/authorize?` +
      `audience=api.atlassian.com&` +
      `client_id=${encodeURIComponent(clientId)}&` +
      `scope=${encodeURIComponent('read:jira-user read:jira-work')}&` +
      `redirect_uri=${encodeURIComponent('http://localhost:5173/auth/jira/callback')}&` +
      `state=${state}&` +
      `response_type=code&` +
      `prompt=consent`;
    
    console.log('\n✅ Authorization URL generated:');
    console.log('─'.repeat(80));
    console.log(authUrl);
    console.log('─'.repeat(80));
    
    console.log('\n📋 Next steps:');
    console.log('1. Copy the URL above');
    console.log('2. Open it in your browser');
    console.log('3. Grant permissions to your Jira site');
    console.log('4. You should be redirected to: http://localhost:5173/auth/jira/callback');
    console.log('5. Check the URL parameters for the authorization code');
    
    console.log('\n💡 Make sure your React app is running on port 5173');
    console.log('   Run: npm run dev');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    rl.close();
  }
}

main();