{"extends": "./tsconfig.app.json", "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.test.tsbuildinfo", "types": ["vitest/globals", "@testing-library/jest-dom"], "allowImportingTsExtensions": false, "allowJs": true}, "include": ["src/**/*.test.ts", "src/**/*.test.tsx", "src/**/*.spec.ts", "src/**/*.spec.tsx", "src/test/**/*", "src/**/*.ts", "src/**/*.tsx", "vite.config.test.ts"], "exclude": ["e2e/**/*"]}