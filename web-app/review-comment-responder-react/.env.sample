# Environment Variables Configuration
# Copy this file to .env.local and update values as needed

# ===========================================
# MULTI-AGENT SERVICE CONFIGURATION
# ===========================================

# Multi-Agent Code Reviewer Service (NEW - Port 8000)
VITE_MULTI_AGENT_URL=http://localhost:8000
VITE_MULTI_AGENT_WS_URL=ws://localhost:8000
VITE_MULTI_AGENT_TIMEOUT=10000

# Multi-Agent Retry Configuration
VITE_MULTI_AGENT_MAX_RETRIES=3
VITE_MULTI_AGENT_RETRY_DELAY=1000
VITE_MULTI_AGENT_EXPONENTIAL_BACKOFF=true

# ===========================================
# LEGACY SERVICES CONFIGURATION
# ===========================================

# Legacy Code Reviewer Service (Port 5002)
VITE_CODE_REVIEWER_URL=http://localhost:5002
VITE_CODE_REVIEWER_TIMEOUT=15000

# Legacy Claude Service (Port 5001)
VITE_CLAUDE_URL=http://localhost:5001
VITE_CLAUDE_TIMEOUT=30000

# ===========================================
# EXTERNAL API CONFIGURATION
# ===========================================

# Bitbucket API
VITE_BITBUCKET_API_URL=https://api.bitbucket.org/2.0
VITE_BITBUCKET_TIMEOUT=10000

# Jira API
VITE_JIRA_URL=https://rma.atlassian.net
VITE_JIRA_TIMEOUT=10000

# ===========================================
# FEATURE FLAGS - MAIN FEATURES
# ===========================================

# Multi-Agent Reviews (Primary Feature)
VITE_FEATURE_MULTI_AGENT_REVIEWS=false
VITE_MULTI_AGENT_ROLLOUT_PERCENTAGE=0
VITE_MULTI_AGENT_ALLOWED_USERS=
VITE_MULTI_AGENT_ALLOWED_WORKSPACES=
VITE_MULTI_AGENT_BETA_MODE=true

# Service Fallback Behavior
VITE_FEATURE_AUTO_FALLBACK=true
VITE_FALLBACK_ON_TIMEOUT=true
VITE_FALLBACK_ON_ERROR=true
VITE_MAX_FALLBACK_ATTEMPTS=2
VITE_SHOW_FALLBACK_NOTIFICATIONS=true

# Real-Time Updates (WebSocket)
VITE_FEATURE_REALTIME_UPDATES=true
VITE_WEBSOCKET_AUTO_RECONNECT=true
VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS=5
VITE_WEBSOCKET_RECONNECT_DELAY=1000
VITE_WEBSOCKET_HEARTBEAT_INTERVAL=30000

# Enhanced Reports
VITE_FEATURE_ENHANCED_REPORTS=true
VITE_SHOW_AGENT_BREAKDOWN=true
VITE_ENABLE_MERMAID_DIAGRAMS=true
VITE_ENABLE_PDF_EXPORT=false
VITE_ENABLE_JSON_EXPORT=true
VITE_SHOW_PERFORMANCE_METRICS=true

# ===========================================
# FEATURE FLAGS - UI ENHANCEMENTS
# ===========================================

# User Interface Features
VITE_FEATURE_MODERN_PROGRESS_TRACKER=true
VITE_FEATURE_DARK_MODE=false
VITE_FEATURE_A11Y_ENHANCEMENTS=true
VITE_FEATURE_KEYBOARD_SHORTCUTS=false
VITE_FEATURE_COMPACT_MODE=false

# ===========================================
# FEATURE FLAGS - PERFORMANCE
# ===========================================

# Performance Optimizations
VITE_FEATURE_LAZY_LOADING=true
VITE_FEATURE_SERVICE_WORKER=false
VITE_FEATURE_CACHE_RESULTS=true
VITE_FEATURE_PRELOAD_ROUTES=true
VITE_FEATURE_OPTIMIZE_IMAGES=true

# ===========================================
# FEATURE FLAGS - DEVELOPMENT
# ===========================================

# Development and Debugging
VITE_FEATURE_DEBUG_MODE=false
VITE_FEATURE_API_LOGS=false
VITE_FEATURE_MOCK_SERVICES=false
VITE_FEATURE_PERFORMANCE_PANEL=false
VITE_FEATURE_ERROR_BOUNDARY_INFO=true

# ===========================================
# FEATURE FLAGS - INTEGRATIONS
# ===========================================

# External Service Integrations
VITE_FEATURE_JIRA_INTEGRATION=true
VITE_FEATURE_BITBUCKET_INTEGRATION=true
VITE_FEATURE_SLACK_NOTIFICATIONS=false
VITE_FEATURE_EMAIL_NOTIFICATIONS=false
VITE_FEATURE_WEBHOOK_SUPPORT=false

# ===========================================
# FEATURE FLAGS - ANALYTICS
# ===========================================

# Analytics and Monitoring
VITE_FEATURE_USAGE_TRACKING=false
VITE_FEATURE_ERROR_TRACKING=true
VITE_FEATURE_PERFORMANCE_TRACKING=false
VITE_ANONYMIZE_USER_DATA=true

# Production-specific analytics
VITE_PROD_USAGE_TRACKING=false

# ===========================================
# HEALTH CHECK CONFIGURATION
# ===========================================

# Health Check Intervals (in milliseconds)
VITE_HEALTH_CHECK_MULTI_AGENT_INTERVAL=30000
VITE_HEALTH_CHECK_LEGACY_INTERVAL=60000
VITE_HEALTH_CHECK_EXTERNAL_INTERVAL=300000
VITE_HEALTH_CHECK_TIMEOUT=5000
VITE_HEALTH_CHECK_MAX_FAILURES=3

# ===========================================
# PRODUCTION OVERRIDES
# ===========================================

# Production Service URLs (override defaults)
VITE_PROD_MULTI_AGENT_URL=
VITE_PROD_CODE_REVIEWER_URL=

# ===========================================
# LOGGING AND MONITORING
# ===========================================

# Service Request Logging
VITE_LOG_SERVICE_REQUESTS=false
VITE_ENABLE_SERVICE_DEBUGGING=false

# Mock Services (Development)
VITE_ENABLE_MOCK_SERVICES=false

# ===========================================
# EXAMPLE CONFIGURATIONS
# ===========================================

# Example: Enable Multi-Agent for specific users
# VITE_MULTI_AGENT_ALLOWED_USERS=<EMAIL>,<EMAIL>

# Example: Enable Multi-Agent for specific workspaces  
# VITE_MULTI_AGENT_ALLOWED_WORKSPACES=rma-workspace,test-workspace

# Example: Gradual rollout (enable for 25% of users)
# VITE_MULTI_AGENT_ROLLOUT_PERCENTAGE=25

# Example: Development setup with all features enabled
# VITE_FEATURE_MULTI_AGENT_REVIEWS=true
# VITE_MULTI_AGENT_ROLLOUT_PERCENTAGE=100
# VITE_FEATURE_DEBUG_MODE=true
# VITE_FEATURE_API_LOGS=true

# Example: Production setup with conservative defaults
# VITE_FEATURE_MULTI_AGENT_REVIEWS=true
# VITE_MULTI_AGENT_ROLLOUT_PERCENTAGE=10
# VITE_FEATURE_DEBUG_MODE=false
# VITE_FEATURE_API_LOGS=false
# VITE_FEATURE_ERROR_TRACKING=true

# ===========================================
# NOTES
# ===========================================

# 1. All VITE_ prefixed variables are available in the frontend
# 2. Boolean values: use 'true' or 'false' (strings)
# 3. Numeric values: use numbers without quotes
# 4. List values: use comma-separated strings
# 5. Environment-specific files: .env.local, .env.development, .env.production
# 6. Secret values should never be committed to version control