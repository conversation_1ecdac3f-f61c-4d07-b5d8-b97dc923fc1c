/**
 * End-to-End Tests for Multi-Agent Integration
 * Testing complete user journeys and workflows
 */

import { test, expect, type Page, type BrowserContext } from '@playwright/test'

// Test configuration
const TEST_CONFIG = {
  baseURL: 'http://localhost:5173',
  apiURL: 'http://localhost:5000',
  timeout: 30000,
  slowMo: 100 // Slow down for debugging
}

// Test data
const TEST_DATA = {
  pr: {
    id: 1,
    title: 'Add user authentication feature',
    branch: 'feature/user-authentication',
    workspace: 'test-workspace',
    repository: 'test-repo'
  },
  ticket: {
    ticket_id: 'PROJ-123',
    summary: 'Implement user authentication system',
    acceptance_criteria: [
      'User can register with email and password',
      'User can login with valid credentials',
      'User session persists across browser sessions'
    ]
  }
}

// Helper functions
const setupMockAPI = async (context: BrowserContext) => {
  // Mock Multi-Agent API responses
  await context.route('**/api/v1/health', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: { status: 'healthy', version: '1.0.0' }
      })
    })
  })

  await context.route('**/api/v1/review/start-parallel', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: {
          review_id: 'e2e-review-123',
          session_id: 'e2e-session-123',
          status: 'started',
          initial_agent_statuses: {
            acceptance_criteria: { agent_type: 'acceptance_criteria', status: 'pending', progress: 0 },
            bug_detection: { agent_type: 'bug_detection', status: 'pending', progress: 0 },
            security_analysis: { agent_type: 'security_analysis', status: 'pending', progress: 0 }
          },
          estimated_completion_time: 180,
          created_at: new Date().toISOString()
        }
      })
    })
  })

  await context.route('**/api/v1/review/*/status', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: {
          review_id: 'e2e-review-123',
          session_id: 'e2e-session-123',
          status: 'running',
          progress: 75,
          agent_statuses: {
            acceptance_criteria: { status: 'completed', progress: 100 },
            bug_detection: { status: 'running', progress: 80 },
            security_analysis: { status: 'running', progress: 60 }
          },
          active_agents: ['bug_detection', 'security_analysis'],
          completed_agents: ['acceptance_criteria'],
          failed_agents: []
        }
      })
    })
  })

  await context.route('**/api/v1/review/*/results', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        data: {
          review_id: 'e2e-review-123',
          session_id: 'e2e-session-123',
          status: 'completed',
          overall_results: {
            summary: 'Multi-agent review completed successfully',
            priority_findings: [
              {
                id: 'finding-1',
                type: 'security',
                severity: 'high',
                priority: 'high',
                title: 'Potential security vulnerability',
                file_path: 'src/auth/login.tsx',
                line_number: 42
              }
            ],
            execution_metrics: {
              total_execution_time: 165.8,
              parallel_efficiency: 92.3,
              agent_performance: {
                acceptance_criteria: { execution_time: 35.2, success: true, findings_count: 0 },
                bug_detection: { execution_time: 48.6, success: true, findings_count: 0 },
                security_analysis: { execution_time: 52.1, success: true, findings_count: 1 }
              }
            }
          },
          agent_results: {},
          reports: {
            markdown: '# Multi-Agent Review Report\n\nReview completed successfully.',
            json: '{"agents": 3, "findings": 1}'
          }
        }
      })
    })
  })
}

const authenticateUser = async (page: Page) => {
  // Mock authentication state
  await page.evaluate(() => {
    localStorage.setItem('auth_token', 'mock-token-123')
    localStorage.setItem('user_info', JSON.stringify({
      username: 'test-user',
      email: '<EMAIL>'
    }))
  })
}

const configureWorktree = async (page: Page) => {
  // Mock worktree configuration
  await page.evaluate(() => {
    localStorage.setItem('worktree_config', JSON.stringify({
      isConfigured: true,
      isValid: true,
      path: '/test/worktree/path'
    }))
  })
}

test.describe('Multi-Agent Integration E2E Tests', () => {
  test.beforeEach(async ({ context, page }) => {
    await setupMockAPI(context)
    await page.goto(TEST_CONFIG.baseURL)
    await authenticateUser(page)
    await configureWorktree(page)
  })

  test.describe('Complete Multi-Agent Review Workflow', () => {
    test('should complete full multi-agent review journey', async ({ page }) => {
      // Step 1: Navigate to Code Reviewer
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      await expect(page.locator('[data-testid="code-reviewer-header"]')).toBeVisible()

      // Step 2: Select work (PR and ticket)
      await expect(page.locator('[data-testid="assigned-work-panel"]')).toBeVisible()
      
      // Mock selecting a PR and ticket
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('select-pr', { 
          detail: {
            id: 1,
            title: 'Add user authentication feature',
            branch: 'feature/user-authentication'
          }
        }))
      })

      await page.click('[data-testid="continue-to-configure"]')

      // Step 3: Configure review mode
      await expect(page.locator('[data-testid="configure-review-step"]')).toBeVisible()
      
      // Select multi-agent mode
      await page.click('[data-testid="multi-agent-mode-option"]')
      await expect(page.locator('[data-testid="multi-agent-mode-option"]')).toHaveClass(/selected/)

      // Start the review
      await page.click('[data-testid="start-review-button"]')

      // Step 4: Monitor progress
      await expect(page.locator('[data-testid="review-progress-step"]')).toBeVisible()
      await expect(page.locator('[data-testid="multi-agent-progress-tracker"]')).toBeVisible()

      // Verify multi-agent progress elements
      await expect(page.locator('text=Multi-Agent Code Review')).toBeVisible()
      await expect(page.locator('[data-testid="agent-card"]')).toHaveCount(3) // acceptance_criteria, bug_detection, security_analysis
      await expect(page.locator('text=Running')).toBeVisible()

      // Wait for progress updates
      await page.waitForTimeout(2000)

      // Step 5: View results
      // Mock completion
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('review-completed', {
          detail: { review_id: 'e2e-review-123' }
        }))
      })

      await expect(page.locator('[data-testid="review-results-step"]')).toBeVisible()
      await expect(page.locator('[data-testid="multi-agent-results-aggregator"]')).toBeVisible()

      // Verify results display
      await expect(page.locator('text=Multi-Agent Performance Overview')).toBeVisible()
      await expect(page.locator('text=7 Parallel Agents')).toBeVisible()
      await expect(page.locator('text=Total Time')).toBeVisible()
      await expect(page.locator('text=Parallel Efficiency')).toBeVisible()
    })

    test('should handle multi-agent review failure gracefully', async ({ page }) => {
      // Mock API failure
      await page.route('**/api/v1/review/start-parallel', route => {
        route.fulfill({
          status: 503,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            error: {
              error_code: 'SERVICE_UNAVAILABLE',
              error_message: 'Multi-agent service temporarily unavailable'
            }
          })
        })
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Navigate through workflow
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Should show error message
      await expect(page.locator('[data-testid="error-alert"]')).toBeVisible()
      await expect(page.locator('text=Multi-agent service temporarily unavailable')).toBeVisible()

      // Should provide fallback option
      await expect(page.locator('[data-testid="fallback-to-legacy-button"]')).toBeVisible()
      
      // Test fallback
      await page.click('[data-testid="fallback-to-legacy-button"]')
      await expect(page.locator('text=Switched to Legacy Mode')).toBeVisible()
    })
  })

  test.describe('Multi-Agent Progress Tracking', () => {
    test('should display real-time progress updates', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Start multi-agent review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Verify progress tracker is visible
      await expect(page.locator('[data-testid="multi-agent-progress-tracker"]')).toBeVisible()

      // Check initial state
      await expect(page.locator('[data-testid="overall-progress-bar"]')).toHaveAttribute('data-value', '0')
      
      // Simulate progress updates via WebSocket
      await page.evaluate(() => {
        const mockProgress = [
          { agent: 'acceptance_criteria', progress: 50 },
          { agent: 'bug_detection', progress: 30 },
          { agent: 'security_analysis', progress: 20 }
        ]

        mockProgress.forEach((update, index) => {
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('agent-progress', {
              detail: update
            }))
          }, index * 1000)
        })
      })

      // Wait for updates and verify
      await page.waitForTimeout(3500)
      
      // Should show updated progress
      await expect(page.locator('[data-testid="agent-card-acceptance_criteria"]')).toContainText('50%')
      await expect(page.locator('[data-testid="agent-card-bug_detection"]')).toContainText('30%')
    })

    test('should show agent timeline and statistics', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Navigate to progress view
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Verify timeline elements
      await expect(page.locator('[data-testid="agent-execution-timeline"]')).toBeVisible()
      await expect(page.locator('[data-testid="quick-stats-grid"]')).toBeVisible()

      // Check statistics
      await expect(page.locator('[data-testid="stat-running"]')).toContainText('2') // Running agents
      await expect(page.locator('[data-testid="stat-completed"]')).toContainText('1') // Completed agents
      await expect(page.locator('[data-testid="stat-pending"]')).toContainText('0') // Pending agents
      await expect(page.locator('[data-testid="stat-failed"]')).toContainText('0') // Failed agents
    })
  })

  test.describe('Multi-Agent Results Display', () => {
    test('should display comprehensive results aggregation', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Complete workflow to results
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Mock completion
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('review-completed'))
      })

      await expect(page.locator('[data-testid="multi-agent-results-aggregator"]')).toBeVisible()

      // Verify performance overview
      await expect(page.locator('text=Multi-Agent Performance Overview')).toBeVisible()
      await expect(page.locator('[data-testid="performance-metric-total-time"]')).toContainText('2m 46s')
      await expect(page.locator('[data-testid="performance-metric-efficiency"]')).toContainText('92.3%')

      // Verify findings display
      await expect(page.locator('[data-testid="priority-findings-section"]')).toBeVisible()
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(1)
      
      // Check finding details
      await expect(page.locator('text=Potential security vulnerability')).toBeVisible()
      await expect(page.locator('text=src/auth/login.tsx:42')).toBeVisible()
    })

    test('should support findings filtering and categorization', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Navigate to results
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')
      await page.evaluate(() => window.dispatchEvent(new CustomEvent('review-completed')))

      // Test priority filtering
      await page.click('[data-testid="priority-filter-high"]')
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(1) // Only high priority

      await page.click('[data-testid="priority-filter-all"]')
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(1) // All findings

      // Test agent type filtering
      await page.click('[data-testid="agent-filter-security_analysis"]')
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(1) // Security findings only

      // Test search functionality
      await page.fill('[data-testid="findings-search"]', 'security')
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(1)

      await page.fill('[data-testid="findings-search"]', 'nonexistent')
      await expect(page.locator('[data-testid="finding-item"]')).toHaveCount(0)
    })
  })

  test.describe('Service Fallback and Error Handling', () => {
    test('should fallback to legacy mode when multi-agent service is unavailable', async ({ page }) => {
      // Mock multi-agent service as unhealthy
      await page.route('**/api/v1/health', route => {
        route.fulfill({
          status: 503,
          contentType: 'application/json',
          body: JSON.stringify({
            success: false,
            data: { status: 'unhealthy' }
          })
        })
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Try to select multi-agent mode
      await page.click('[data-testid="continue-to-configure"]')
      
      // Multi-agent option should be disabled or show warning
      await expect(page.locator('[data-testid="multi-agent-unavailable-warning"]')).toBeVisible()
      
      // Should automatically suggest legacy mode
      await expect(page.locator('[data-testid="legacy-mode-option"]')).toHaveClass(/selected/)
      
      // Starting review should use legacy service
      await page.click('[data-testid="start-review-button"]')
      await expect(page.locator('text=Legacy Review Progress')).toBeVisible()
    })

    test('should handle network failures gracefully', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Start review process
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')

      // Mock network failure during start
      await page.route('**/api/v1/review/start-parallel', route => {
        route.abort('failed')
      })

      await page.click('[data-testid="start-review-button"]')

      // Should show network error
      await expect(page.locator('[data-testid="network-error-alert"]')).toBeVisible()
      await expect(page.locator('text=Network connection failed')).toBeVisible()

      // Should provide retry option
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
    })
  })

  test.describe('WebSocket Real-time Communication', () => {
    test('should handle WebSocket connection and events', async ({ page }) => {
      // Mock WebSocket
      await page.addInitScript(() => {
        class MockWebSocket extends EventTarget {
          readyState = WebSocket.OPEN
          url: string
          
          constructor(url: string) {
            super()
            this.url = url
            setTimeout(() => {
              this.dispatchEvent(new Event('open'))
            }, 100)
          }

          send(data: string) {
            // Echo back mock events
            setTimeout(() => {
              const mockEvent = {
                data: JSON.stringify({
                  review_id: 'e2e-review-123',
                  event_type: 'agent_progress',
                  agent_type: 'bug_detection',
                  timestamp: Date.now(),
                  data: { progress: 50 }
                })
              }
              this.dispatchEvent(new MessageEvent('message', mockEvent))
            }, 500)
          }

          close() {
            this.readyState = WebSocket.CLOSED
            this.dispatchEvent(new CloseEvent('close'))
          }
        }

        (window as any).WebSocket = MockWebSocket
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Start multi-agent review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Should establish WebSocket connection
      await expect(page.locator('[data-testid="websocket-status-connected"]')).toBeVisible()

      // Should receive and display real-time updates
      await page.waitForTimeout(1000)
      await expect(page.locator('[data-testid="agent-card-bug_detection"]')).toContainText('50%')
    })

    test('should handle WebSocket disconnection and reconnection', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Start review with WebSocket
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Initially connected
      await expect(page.locator('[data-testid="websocket-status-connected"]')).toBeVisible()

      // Simulate disconnection
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('websocket-disconnected'))
      })

      // Should show disconnected state
      await expect(page.locator('[data-testid="websocket-status-disconnected"]')).toBeVisible()
      await expect(page.locator('text=Connection lost - attempting to reconnect')).toBeVisible()

      // Simulate reconnection
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('websocket-reconnected'))
      })

      // Should show reconnected state
      await expect(page.locator('[data-testid="websocket-status-connected"]')).toBeVisible()
      await expect(page.locator('text=Connection restored')).toBeVisible()
    })
  })

  test.describe('Performance and Responsiveness', () => {
    test('should load and render within acceptable time limits', async ({ page }) => {
      const startTime = Date.now()
      
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Wait for main components to load
      await expect(page.locator('[data-testid="code-reviewer-header"]')).toBeVisible()
      await expect(page.locator('[data-testid="workflow-steps"]')).toBeVisible()
      
      const loadTime = Date.now() - startTime
      
      // Should load within 3 seconds
      expect(loadTime).toBeLessThan(3000)
    })

    test('should remain responsive during intensive operations', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Start multi-agent review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Simulate intensive background operations
      await page.evaluate(() => {
        for (let i = 0; i < 100; i++) {
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('progress-update', {
              detail: { progress: i }
            }))
          }, i * 10)
        }
      })

      // UI should remain responsive to user interactions
      const startTime = Date.now()
      await page.click('[data-testid="workflow-steps"]') // Any interactive element
      const responseTime = Date.now() - startTime

      // Should respond within 200ms
      expect(responseTime).toBeLessThan(200)
    })
  })

  test.describe('User Experience and Accessibility', () => {
    test('should maintain proper focus management', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Test focus flow through workflow
      await page.keyboard.press('Tab')
      await expect(page.locator('[data-testid="continue-to-configure"]')).toBeFocused()

      await page.keyboard.press('Enter')
      await expect(page.locator('[data-testid="multi-agent-mode-option"]')).toBeFocused() // Should focus first option

      await page.keyboard.press('Tab')
      await expect(page.locator('[data-testid="start-review-button"]')).toBeFocused()
    })

    test('should provide appropriate ARIA labels and descriptions', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Navigate to progress view
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Check ARIA attributes
      await expect(page.locator('[data-testid="overall-progress-bar"]')).toHaveAttribute('role', 'progressbar')
      await expect(page.locator('[data-testid="multi-agent-progress-tracker"]')).toHaveAttribute('aria-label')
      
      // Agent cards should have proper descriptions
      const agentCards = page.locator('[data-testid^="agent-card-"]')
      const firstCard = agentCards.first()
      await expect(firstCard).toHaveAttribute('aria-describedby')
    })

    test('should work with screen readers', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      
      // Navigate using screen reader simulation
      await page.keyboard.press('Tab')
      
      // Important elements should have proper labels
      const focusedElement = page.locator(':focus')
      const ariaLabel = await focusedElement.getAttribute('aria-label')
      const textContent = await focusedElement.textContent()
      
      // Should have either aria-label or meaningful text content
      expect(ariaLabel || textContent).toBeTruthy()
    })
  })
})