/**
 * End-to-End Error Handling Tests
 * Testing error scenarios and recovery mechanisms
 */

import { test, expect, type Page, type BrowserContext } from '@playwright/test'

const TEST_CONFIG = {
  baseURL: 'http://localhost:5173',
  apiURL: 'http://localhost:5000',
  timeout: 15000
}

// Error scenario helpers
const setupErrorScenarios = async (context: BrowserContext) => {
  // Service unavailable scenario
  await context.route('**/api/v1/health', route => {
    if (route.request().url().includes('simulate-unhealthy')) {
      route.fulfill({
        status: 503,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: {
            error_code: 'SERVICE_UNAVAILABLE',
            error_message: 'Multi-agent service is currently unavailable'
          }
        })
      })
    } else {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: { status: 'healthy' }
        })
      })
    }
  })

  // Network timeout scenario
  await context.route('**/api/v1/review/start-parallel', route => {
    if (route.request().url().includes('simulate-timeout')) {
      // Never respond to simulate timeout
      return
    } else if (route.request().url().includes('simulate-error')) {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          success: false,
          error: {
            error_code: 'INTERNAL_SERVER_ERROR',
            error_message: 'Internal server error occurred'
          }
        })
      })
    } else {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            review_id: 'error-test-123',
            session_id: 'error-session-123',
            status: 'started',
            initial_agent_statuses: {},
            estimated_completion_time: 180,
            created_at: new Date().toISOString()
          }
        })
      })
    }
  })

  // Review failure scenario
  await context.route('**/api/v1/review/*/status', route => {
    if (route.request().url().includes('simulate-review-failure')) {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            review_id: 'error-test-123',
            status: 'failed',
            progress: 35,
            agent_statuses: {
              acceptance_criteria: { status: 'completed', progress: 100 },
              bug_detection: { status: 'failed', progress: 25, error_message: 'Analysis timeout' },
              security_analysis: { status: 'failed', progress: 0, error_message: 'Service unavailable' }
            },
            failed_agents: ['bug_detection', 'security_analysis'],
            completed_agents: ['acceptance_criteria']
          }
        })
      })
    } else {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            review_id: 'error-test-123',
            status: 'running',
            progress: 50,
            agent_statuses: {},
            active_agents: [],
            completed_agents: [],
            failed_agents: []
          }
        })
      })
    }
  })
}

const authenticateUser = async (page: Page) => {
  await page.evaluate(() => {
    localStorage.setItem('auth_token', 'mock-token-123')
    localStorage.setItem('user_info', JSON.stringify({
      username: 'test-user',
      email: '<EMAIL>'
    }))
  })
}

const configureWorktree = async (page: Page) => {
  await page.evaluate(() => {
    localStorage.setItem('worktree_config', JSON.stringify({
      isConfigured: true,
      isValid: true,
      path: '/test/worktree/path'
    }))
  })
}

test.describe('Error Handling E2E Tests', () => {
  test.beforeEach(async ({ context, page }) => {
    await setupErrorScenarios(context)
    await page.goto(TEST_CONFIG.baseURL)
    await authenticateUser(page)
    await configureWorktree(page)
  })

  test.describe('Service Availability Errors', () => {
    test('should handle multi-agent service unavailable gracefully', async ({ page }) => {
      // Navigate to reviewer with unhealthy service
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer?simulate-unhealthy=true`)

      // Should detect service unavailability
      await expect(page.locator('[data-testid="service-health-warning"]')).toBeVisible()
      await expect(page.locator('text=Multi-agent service is currently unavailable')).toBeVisible()

      // Should offer fallback options
      await expect(page.locator('[data-testid="fallback-to-legacy-button"]')).toBeVisible()
      await expect(page.locator('[data-testid="retry-connection-button"]')).toBeVisible()

      // Test fallback to legacy mode
      await page.click('[data-testid="fallback-to-legacy-button"]')
      await expect(page.locator('text=Switched to Legacy Review Mode')).toBeVisible()
      
      // Legacy workflow should continue normally
      await page.click('[data-testid="continue-to-configure"]')
      await expect(page.locator('[data-testid="legacy-mode-option"]')).toHaveClass(/selected disabled/)
      await expect(page.locator('[data-testid="multi-agent-mode-option"]')).toHaveClass(/disabled/)
    })

    test('should retry service connection and recover', async ({ page, context }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer?simulate-unhealthy=true`)

      // Initial unhealthy state
      await expect(page.locator('[data-testid="service-health-warning"]')).toBeVisible()

      // Update route to simulate service recovery
      await context.route('**/api/v1/health', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { status: 'healthy', version: '1.0.0' }
          })
        })
      })

      // Retry connection
      await page.click('[data-testid="retry-connection-button"]')

      // Should show recovery
      await expect(page.locator('[data-testid="service-health-success"]')).toBeVisible()
      await expect(page.locator('text=Multi-agent service is now available')).toBeVisible()

      // Multi-agent option should be enabled
      await page.click('[data-testid="continue-to-configure"]')
      await expect(page.locator('[data-testid="multi-agent-mode-option"]')).not.toHaveClass(/disabled/)
    })

    test('should handle partial service degradation', async ({ page, context }) => {
      // Mock partial service availability
      await context.route('**/api/v1/health', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { 
              status: 'degraded',
              available_agents: ['acceptance_criteria', 'bug_detection'],
              unavailable_agents: ['security_analysis', 'logic_analysis']
            }
          })
        })
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Should show degraded service warning
      await expect(page.locator('[data-testid="service-degraded-warning"]')).toBeVisible()
      await expect(page.locator('text=Some agents are currently unavailable')).toBeVisible()

      // Should allow proceeding with limited agents
      await page.click('[data-testid="continue-with-available-agents"]')
      await page.click('[data-testid="continue-to-configure"]')

      // Should show which agents are available
      await expect(page.locator('[data-testid="available-agents-list"]')).toContainText('acceptance_criteria')
      await expect(page.locator('[data-testid="available-agents-list"]')).toContainText('bug_detection')
      await expect(page.locator('[data-testid="unavailable-agents-list"]')).toContainText('security_analysis')
    })
  })

  test.describe('Network and API Errors', () => {
    test('should handle network timeouts during review start', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Navigate to configure step
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')

      // Attempt to start review (will timeout)
      await page.click('[data-testid="start-review-button"]?simulate-timeout=true')

      // Should show loading state initially
      await expect(page.locator('[data-testid="start-review-loading"]')).toBeVisible()

      // Should eventually show timeout error
      await expect(page.locator('[data-testid="timeout-error-alert"]')).toBeVisible({ timeout: 10000 })
      await expect(page.locator('text=Request timed out')).toBeVisible()

      // Should provide retry option
      await expect(page.locator('[data-testid="retry-start-review"]')).toBeVisible()
      
      // Test retry functionality
      await page.click('[data-testid="retry-start-review"]')
      await expect(page.locator('[data-testid="start-review-loading"]')).toBeVisible()
    })

    test('should handle API errors with proper user feedback', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]?simulate-error=true')

      // Should show API error
      await expect(page.locator('[data-testid="api-error-alert"]')).toBeVisible()
      await expect(page.locator('text=Internal server error occurred')).toBeVisible()

      // Should show error code for debugging
      await expect(page.locator('[data-testid="error-code"]')).toContainText('INTERNAL_SERVER_ERROR')

      // Should provide support/contact information
      await expect(page.locator('[data-testid="support-contact-info"]')).toBeVisible()
      
      // Should allow going back to configure
      await page.click('[data-testid="back-to-configure-button"]')
      await expect(page.locator('[data-testid="configure-review-step"]')).toBeVisible()
    })

    test('should handle intermittent connectivity issues', async ({ page, context }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Start a review successfully first
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      await expect(page.locator('[data-testid="review-progress-step"]')).toBeVisible()

      // Simulate network issues during progress tracking
      await context.route('**/api/v1/review/*/status', route => {
        route.abort('failed')
      })

      // Should detect connection issues
      await expect(page.locator('[data-testid="connection-issue-warning"]')).toBeVisible({ timeout: 8000 })
      await expect(page.locator('text=Connection issues detected')).toBeVisible()

      // Should continue attempting to reconnect
      await expect(page.locator('[data-testid="reconnection-attempts"]')).toBeVisible()

      // Restore connection
      await context.route('**/api/v1/review/*/status', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              review_id: 'error-test-123',
              status: 'running',
              progress: 75,
              agent_statuses: {},
              active_agents: [],
              completed_agents: [],
              failed_agents: []
            }
          })
        })
      })

      // Should recover and continue
      await expect(page.locator('[data-testid="connection-restored-notice"]')).toBeVisible()
      await expect(page.locator('[data-testid="connection-issue-warning"]')).not.toBeVisible()
    })
  })

  test.describe('Review Process Errors', () => {
    test('should handle multi-agent review failures', async ({ page, context }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Start review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      await expect(page.locator('[data-testid="review-progress-step"]')).toBeVisible()

      // Simulate review failure
      await context.route('**/api/v1/review/*/status', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              review_id: 'error-test-123',
              status: 'failed',
              progress: 35,
              agent_statuses: {
                acceptance_criteria: { status: 'completed', progress: 100 },
                bug_detection: { 
                  status: 'failed', 
                  progress: 25, 
                  error_message: 'Analysis timeout - code complexity too high' 
                },
                security_analysis: { 
                  status: 'failed', 
                  progress: 0, 
                  error_message: 'Security analysis service unavailable' 
                }
              },
              failed_agents: ['bug_detection', 'security_analysis'],
              completed_agents: ['acceptance_criteria']
            }
          })
        })
      })

      // Wait for failure detection
      await page.waitForTimeout(2000)

      // Should show review failure status
      await expect(page.locator('[data-testid="review-failed-alert"]')).toBeVisible()
      await expect(page.locator('text=Review failed')).toBeVisible()

      // Should show detailed error information
      await expect(page.locator('[data-testid="failed-agents-list"]')).toBeVisible()
      await expect(page.locator('text=bug_detection')).toBeVisible()
      await expect(page.locator('text=security_analysis')).toBeVisible()
      await expect(page.locator('text=Analysis timeout - code complexity too high')).toBeVisible()

      // Should show partial results from successful agents
      await expect(page.locator('[data-testid="partial-results-section"]')).toBeVisible()
      await expect(page.locator('text=1 of 3 agents completed successfully')).toBeVisible()

      // Should provide retry options
      await expect(page.locator('[data-testid="retry-failed-agents-button"]')).toBeVisible()
      await expect(page.locator('[data-testid="retry-entire-review-button"]')).toBeVisible()
      await expect(page.locator('[data-testid="continue-with-partial-results-button"]')).toBeVisible()
    })

    test('should handle individual agent failures during execution', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Start review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Simulate progressive agent failures via WebSocket events
      await page.evaluate(() => {
        // Simulate agent started events
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('agent-started', {
            detail: { agent_type: 'acceptance_criteria' }
          }))
        }, 500)

        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('agent-started', {
            detail: { agent_type: 'bug_detection' }
          }))
        }, 1000)

        // Simulate one agent failure
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('agent-failed', {
            detail: { 
              agent_type: 'bug_detection',
              error_message: 'Memory limit exceeded during analysis'
            }
          }))
        }, 3000)

        // Simulate another agent success
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('agent-completed', {
            detail: { agent_type: 'acceptance_criteria' }
          }))
        }, 4000)
      })

      // Should show individual agent failure
      await expect(page.locator('[data-testid="agent-failure-notification"]')).toBeVisible({ timeout: 5000 })
      await expect(page.locator('text=bug_detection agent failed')).toBeVisible()
      await expect(page.locator('text=Memory limit exceeded during analysis')).toBeVisible()

      // Should continue with other agents
      await expect(page.locator('[data-testid="agent-card-acceptance_criteria"]')).toHaveClass(/completed/)
      await expect(page.locator('[data-testid="agent-card-bug_detection"]')).toHaveClass(/failed/)

      // Should offer to retry just the failed agent
      await expect(page.locator('[data-testid="retry-agent-bug_detection"]')).toBeVisible()
    })

    test('should handle data corruption and validation errors', async ({ page, context }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Mock corrupted API response
      await context.route('**/api/v1/review/start-parallel', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              // Missing required fields to simulate corruption
              review_id: null,
              session_id: '',
              status: 'invalid-status',
              corrupted_field: 'unexpected_data'
            }
          })
        })
      })

      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Should detect and handle data validation error
      await expect(page.locator('[data-testid="data-validation-error"]')).toBeVisible()
      await expect(page.locator('text=Invalid response from server')).toBeVisible()

      // Should provide clear error message
      await expect(page.locator('[data-testid="validation-error-details"]')).toContainText('review_id')
      
      // Should suggest refreshing or contacting support
      await expect(page.locator('[data-testid="refresh-and-retry-button"]')).toBeVisible()
    })
  })

  test.describe('WebSocket Error Handling', () => {
    test('should handle WebSocket connection failures', async ({ page }) => {
      // Mock WebSocket failure
      await page.addInitScript(() => {
        class FailingWebSocket extends EventTarget {
          readyState = WebSocket.CONNECTING
          
          constructor(url: string) {
            super()
            setTimeout(() => {
              this.readyState = WebSocket.CLOSED
              this.dispatchEvent(new ErrorEvent('error', {
                message: 'Connection refused'
              }))
              this.dispatchEvent(new CloseEvent('close', {
                code: 1006,
                reason: 'Connection failed'
              }))
            }, 1000)
          }

          send() {
            throw new Error('WebSocket is not connected')
          }

          close() {}
        }

        (window as any).WebSocket = FailingWebSocket
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Start multi-agent review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Should detect WebSocket failure
      await expect(page.locator('[data-testid="websocket-connection-error"]')).toBeVisible()
      await expect(page.locator('text=Real-time updates unavailable')).toBeVisible()

      // Should offer fallback to polling
      await expect(page.locator('[data-testid="fallback-to-polling-button"]')).toBeVisible()
      
      await page.click('[data-testid="fallback-to-polling-button"]')
      await expect(page.locator('text=Switched to polling mode')).toBeVisible()
    })

    test('should handle WebSocket message parsing errors', async ({ page }) => {
      // Mock WebSocket with malformed messages
      await page.addInitScript(() => {
        class MalformedWebSocket extends EventTarget {
          readyState = WebSocket.OPEN
          
          constructor(url: string) {
            super()
            setTimeout(() => {
              this.dispatchEvent(new Event('open'))
            }, 100)

            // Send malformed messages periodically
            setTimeout(() => {
              this.dispatchEvent(new MessageEvent('message', {
                data: '{ invalid json }'
              }))
            }, 1000)

            setTimeout(() => {
              this.dispatchEvent(new MessageEvent('message', {
                data: JSON.stringify({
                  // Missing required fields
                  incomplete: 'data'
                })
              }))
            }, 2000)
          }

          send() {}
          close() {}
        }

        (window as any).WebSocket = MalformedWebSocket
      })

      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Start review
      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]')

      // Should handle malformed messages gracefully
      await page.waitForTimeout(3000)

      // Should show parsing error notification
      await expect(page.locator('[data-testid="message-parsing-error"]')).toBeVisible()
      await expect(page.locator('text=Some real-time updates may be delayed')).toBeVisible()

      // Should continue functioning despite errors
      await expect(page.locator('[data-testid="review-progress-step"]')).toBeVisible()
    })
  })

  test.describe('User Experience During Errors', () => {
    test('should maintain accessibility during error states', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer?simulate-unhealthy=true`)

      // Error alert should be properly announced
      await expect(page.locator('[data-testid="service-health-warning"]')).toHaveAttribute('role', 'alert')
      await expect(page.locator('[data-testid="service-health-warning"]')).toHaveAttribute('aria-live', 'assertive')

      // Focus should be managed properly
      await page.keyboard.press('Tab')
      await expect(page.locator('[data-testid="retry-connection-button"]')).toBeFocused()

      // Error details should be accessible
      await expect(page.locator('[data-testid="error-details"]')).toHaveAttribute('aria-describedby')
    })

    test('should provide clear recovery instructions', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]?simulate-error=true')

      // Should show clear next steps
      await expect(page.locator('[data-testid="recovery-instructions"]')).toBeVisible()
      await expect(page.locator('text=What you can do:')).toBeVisible()
      
      // Should provide multiple recovery options
      await expect(page.locator('[data-testid="recovery-option-retry"]')).toBeVisible()
      await expect(page.locator('[data-testid="recovery-option-fallback"]')).toBeVisible()
      await expect(page.locator('[data-testid="recovery-option-support"]')).toBeVisible()

      // Each option should have clear descriptions
      await expect(page.locator('[data-testid="recovery-option-retry"]')).toContainText('Try the same operation again')
      await expect(page.locator('[data-testid="recovery-option-fallback"]')).toContainText('Use legacy review mode')
    })

    test('should preserve user data during error recovery', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      // Fill in some configuration
      await page.click('[data-testid="continue-to-configure"]')
      await page.fill('[data-testid="custom-config-field"]', 'custom configuration')
      await page.click('[data-testid="multi-agent-mode-option"]')

      // Trigger error
      await page.click('[data-testid="start-review-button"]?simulate-error=true')

      // Go back to configure
      await page.click('[data-testid="back-to-configure-button"]')

      // User data should be preserved
      await expect(page.locator('[data-testid="custom-config-field"]')).toHaveValue('custom configuration')
      await expect(page.locator('[data-testid="multi-agent-mode-option"]')).toHaveClass(/selected/)
    })
  })

  test.describe('Error Reporting and Logging', () => {
    test('should collect and display relevant error information', async ({ page }) => {
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)

      await page.click('[data-testid="continue-to-configure"]')
      await page.click('[data-testid="multi-agent-mode-option"]')
      await page.click('[data-testid="start-review-button"]?simulate-error=true')

      // Error should include relevant debugging information
      await expect(page.locator('[data-testid="error-timestamp"]')).toBeVisible()
      await expect(page.locator('[data-testid="error-request-id"]')).toBeVisible()
      await expect(page.locator('[data-testid="error-user-agent"]')).toBeVisible()

      // Should provide error export functionality
      await expect(page.locator('[data-testid="export-error-report"]')).toBeVisible()
      
      await page.click('[data-testid="export-error-report"]')
      
      // Should download error report (mock file download)
      const downloadPromise = page.waitForEvent('download')
      await downloadPromise
    })

    test('should handle console errors gracefully', async ({ page }) => {
      // Monitor console errors
      const consoleErrors: string[] = []
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text())
        }
      })

      // Trigger JavaScript error
      await page.goto(`${TEST_CONFIG.baseURL}/reviewer`)
      await page.evaluate(() => {
        // Simulate runtime error
        setTimeout(() => {
          throw new Error('Simulated runtime error')
        }, 1000)
      })

      await page.waitForTimeout(2000)

      // Should show error boundary
      await expect(page.locator('[data-testid="error-boundary"]')).toBeVisible()
      await expect(page.locator('text=Something went wrong')).toBeVisible()

      // Should provide recovery option
      await expect(page.locator('[data-testid="reload-application"]')).toBeVisible()
      
      // Console error should be captured
      expect(consoleErrors.some(error => error.includes('Simulated runtime error'))).toBe(true)
    })
  })
})