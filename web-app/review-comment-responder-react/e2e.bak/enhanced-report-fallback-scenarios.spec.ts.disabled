/**
 * User Experience Tests für Enhanced Report Fallback Scenarios
 * 
 * Diese Tests validieren die Benutzerfreundlichkeit und Interaktivität
 * der Fallback-Modi in verschiedenen Fehler- und Datenverfügbarkeitsszenarien.
 */

import { test, expect, Page } from '@playwright/test'

test.describe('Enhanced Report Fallback User Experience', () => {
  
  // Mock-Daten für verschiedene Fallback-Szenarien
  const mockReviewResults = {
    standard: {
      session_id: 'test-session-1',
      review_mode: 'comprehensive',
      branch_name: 'feature/user-auth',
      timestamp: new Date().toISOString(),
      raw_review: `
# Code Review Results

## AC 1: User Login Implementation
✅ AC 1 ist erfüllt. Die Login-Funktionalität wurde korrekt implementiert.
Code-Implementierung: LoginComponent mit proper validation und error handling.

## AC 2: Password Reset
⚠️ AC 2 ist teilweise erfüllt. Password reset email sending ist implementiert.
Probleme: Reset token validation benötigt Verbesserung.

## AC 3: User Dashboard  
❌ AC 3 ist nicht erfüllt. Dashboard components fehlen komplett.

## Critical Issues Found
- Critical bug: SQL injection vulnerability in user query
- Security issue: Missing CSRF protection
- Performance issue: N+1 query problem in user data loading

## Code Quality Assessment
- Overall code quality is acceptable
- Some duplication in validation logic
- Naming conventions are consistent
      `,
      structured_findings: {
        acceptance_criteria: [],
        code_quality: [
          { text: 'Duplicate validation logic in multiple files', severity: 'medium' }
        ],
        security_issues: [
          { text: 'SQL injection in user queries', severity: 'high', file: 'UserService.ts' },
          { text: 'Missing CSRF tokens', severity: 'medium' }
        ],
        performance_issues: [
          { text: 'N+1 query pattern detected', severity: 'medium', file: 'UserRepository.ts' }
        ],
        bugs: [
          { text: 'Null pointer exception in logout', severity: 'high', file: 'AuthService.ts' }
        ],
        suggestions: []
      },
      jira_ticket: {
        ticket_id: 'AUTH-123',
        summary: 'Implement user authentication system',
        status: 'In Progress',
        acceptance_criteria_count: 3,
        acceptance_criteria: [
          'User can log in with email and password',
          'User can reset password via email link',
          'User dashboard displays personal information'
        ]
      },
      metadata: {
        changed_files: ['src/components/Login.tsx', 'src/services/AuthService.ts'],
        file_count: 2
      },
      summary: {
        total_files_reviewed: 2,
        total_findings: 5,
        security_issues: 2,
        potential_bugs: 1,
        quality_suggestions: 1
      },
      enhanced_report: null // Simulate missing enhanced report
    },
    
    backendFallback: {
      session_id: 'test-session-2',
      review_mode: 'comprehensive',
      branch_name: 'feature/backend-fallback',
      timestamp: new Date().toISOString(),
      raw_review: 'Limited review data available',
      parsing_metadata: {
        fallback_mode: true,
        error_details: {
          error_type: 'ParseError',
          error_message: 'Enhanced report parsing failed due to malformed Claude output',
          timestamp: new Date().toISOString()
        }
      },
      enhanced_report: {
        metadata: {
          generated_at: new Date().toISOString(),
          review_type: 'fallback',
          fallback_mode: true,
          parsing_error: 'Backend parsing failed, limited data available'
        },
        acceptance_criteria_analysis: {
          executive_summary: {
            total_ac: 0,
            fulfilled: 0,
            partially_fulfilled: 0,
            not_fulfilled: 0,
            compliance_rate: 0,
            business_alignment_score: 0
          },
          detailed_results: []
        },
        code_quality_analysis: {
          executive_summary: {
            overall_score: 5,
            critical_issues: 0,
            code_smells: 0,
            duplication_level: 0
          },
          code_duplication: [],
          complexity_issues: [],
          naming_consistency: []
        },
        bug_detection_results: {
          critical_bugs: [],
          logic_errors: [],
          runtime_risks: []
        },
        action_items: {
          critical: [],
          important: [],
          suggestions: []
        },
        architectural_assessment: {
          design_patterns: [],
          integration_quality: [],
          violations: []
        },
        security_performance: {
          security_findings: [],
          performance_analysis: []
        },
        variable_parameter_analysis: {
          executive_summary: {
            total_variables: 0,
            naming_issues: 0,
            scope_issues: 0,
            consistency_score: 5
          },
          naming_analysis: [],
          scope_analysis: [],
          type_consistency: []
        },
        next_steps: {
          priority_assessment: {
            critical_blockers: 0,
            high_priority: 0,
            medium_priority: 0,
            can_merge: false,
            estimated_effort: 'Unknown'
          },
          immediate_actions: [],
          follow_up_tasks: [],
          merge_readiness: {
            status: 'needs_review',
            blockers: ['Enhanced parsing failed'],
            recommendations: ['Re-run enhanced review', 'Manual review recommended']
          },
          post_merge_actions: []
        },
        questions_clarifications: []
      },
      structured_findings: {},
      metadata: { changed_files: [], file_count: 0 },
      summary: { total_files_reviewed: 0, total_findings: 0, security_issues: 0, potential_bugs: 0 }
    }
  }

  // Helper functions
  async function setupMockAPI(page: Page, scenario: 'standard' | 'backendFallback') {
    await page.route('**/api/code-reviewer/review-results/*', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          session_id: mockReviewResults[scenario].session_id,
          results: mockReviewResults[scenario]
        })
      })
    })
  }

  async function navigateToReviewResults(page: Page, sessionId: string) {
    await page.goto(`/code-reviewer?session=${sessionId}&step=review-results`)
    await page.waitForLoadState('networkidle')
  }

  test.describe('Szenario 1: Client-side Fallback Generation', () => {
    test('should display fallback banner with generation info', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Check for fallback banner
      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toBeVisible()
      await expect(fallbackBanner).toContainText('Fallback Analysis Mode')
      await expect(fallbackBanner).toContainText('Generated fallback analysis from raw review')
      
      // Check for badges
      await expect(page.locator('text="Fallback Generated"')).toBeVisible()
      await expect(page.locator('text="Export Available"')).toBeVisible()
    })

    test('should generate functional AC analysis from raw review', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Wait for fallback generation
      await page.waitForTimeout(1000)

      // Check AC Analysis section
      const acSection = page.locator('[data-testid="ac-analysis-section"]')
      await expect(acSection).toBeVisible()

      // Check executive summary metrics
      await expect(page.locator('text="33%"')).toBeVisible() // Compliance rate
      await expect(page.locator('text="1"').first()).toBeVisible() // Fulfilled count
      await expect(page.locator('text="1"').nth(1)).toBeVisible() // Partial count
      await expect(page.locator('text="1"').nth(2)).toBeVisible() // Not fulfilled count

      // Check individual AC results
      await expect(page.locator('text="AC_1"')).toBeVisible()
      await expect(page.locator('text="AC_2"')).toBeVisible()
      await expect(page.locator('text="AC_3"')).toBeVisible()
    })

    test('should extract and display bugs from structured findings', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      await page.waitForTimeout(1000)

      // Check Bug Detection section
      const bugSection = page.locator('[data-testid="bug-detection-section"]')
      await expect(bugSection).toBeVisible()

      // Check extracted bugs
      await expect(page.locator('text="SQL injection vulnerability"')).toBeVisible()
      await expect(page.locator('text="Missing CSRF protection"')).toBeVisible()
      await expect(page.locator('text="Null pointer exception in logout"')).toBeVisible()
    })

    test('should provide accurate merge readiness assessment', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      await page.waitForTimeout(1000)

      // Check Next Steps section
      const nextStepsSection = page.locator('[data-testid="next-steps-section"]')
      await expect(nextStepsSection).toBeVisible()

      // Should be blocked due to critical issues
      await expect(page.locator('text="blocked"')).toBeVisible()
      await expect(page.locator('text="2-4 hours"')).toBeVisible() // Effort estimate

      // Check blockers list
      await expect(page.locator('text="Critical bug:"')).toBeVisible()
      await expect(page.locator('text="Missing AC:"')).toBeVisible()
    })
  })

  test.describe('Szenario 2: Backend Fallback Mode', () => {
    test('should display limited analysis warning', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'test-session-2')

      // Check for limited analysis banner
      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toBeVisible()
      await expect(fallbackBanner).toContainText('Limited Analysis Mode')
      await expect(fallbackBanner).toContainText('Enhanced report parsing encountered issues')

      // Check for degraded experience indicator
      await expect(page.locator('text="Degraded Experience"')).toBeVisible()
    })

    test('should show technical error details', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'test-session-2')

      // Expand technical details
      await page.locator('summary:has-text("Technical Details")').click()

      // Check error information
      await expect(page.locator('text="ParseError"')).toBeVisible()
      await expect(page.locator('text="Enhanced report parsing failed"')).toBeVisible()
      
      // Check timestamp formatting
      const timestampElement = page.locator('text=/Time: \\d{1,2}\\.\\d{1,2}\\.\\d{4}/')
      await expect(timestampElement).toBeVisible()
    })

    test('should display empty state messaging', async ({ page }) => {
      await setupMockAPI(page, 'backendFallback')
      await navigateToReviewResults(page, 'test-session-2')

      // Check for empty state messages
      await expect(page.locator('text="No acceptance criteria analysis available"')).toBeVisible()
      await expect(page.locator('text="No critical bugs found"')).toBeVisible()
      await expect(page.locator('text="No variable analysis data available"')).toBeVisible()
    })
  })

  test.describe('User Toggle zwischen Modi', () => {
    test('should toggle between enhanced and raw view', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Initially in enhanced mode
      await expect(page.locator('[data-testid="enhanced-report-sections"]')).toBeVisible()
      await expect(page.locator('[data-testid="raw-review-display"]')).not.toBeVisible()

      // Click toggle to raw mode
      await page.locator('button:has-text("Show Raw Review")').click()

      // Should show raw review
      await expect(page.locator('[data-testid="raw-review-display"]')).toBeVisible()
      await expect(page.locator('[data-testid="enhanced-report-sections"]')).not.toBeVisible()
      await expect(page.locator('text="AC 1 ist erfüllt"')).toBeVisible()

      // Toggle back to enhanced
      await page.locator('button:has-text("Show Enhanced View")').click()
      await expect(page.locator('[data-testid="enhanced-report-sections"]')).toBeVisible()
      await expect(page.locator('[data-testid="raw-review-display"]')).not.toBeVisible()
    })

    test('should maintain toggle state during interaction', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Switch to raw mode
      await page.locator('button:has-text("Show Raw Review")').click()
      await expect(page.locator('[data-testid="raw-review-display"]')).toBeVisible()

      // Interact with other UI elements
      await page.locator('button:has-text("Export Report")').click()

      // Should still be in raw mode
      await expect(page.locator('[data-testid="raw-review-display"]')).toBeVisible()
      await expect(page.locator('button:has-text("Show Enhanced View")')).toBeVisible()
    })

    test('should show appropriate button text for current mode', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Initially enhanced mode
      await expect(page.locator('button:has-text("Show Raw Review")')).toBeVisible()
      await expect(page.locator('button:has-text("Show Enhanced View")')).not.toBeVisible()

      // Switch to raw mode
      await page.locator('button:has-text("Show Raw Review")').click()
      await expect(page.locator('button:has-text("Show Enhanced View")')).toBeVisible()
      await expect(page.locator('button:has-text("Show Raw Review")')).not.toBeVisible()
    })
  })

  test.describe('Export Functionality mit Fallback Indicators', () => {
    test('should export with fallback metadata', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Mock file download
      const downloadPromise = page.waitForEvent('download')
      await page.locator('button:has-text("Export Report")').click()
      const download = await downloadPromise

      // Check filename includes fallback suffix
      expect(download.suggestedFilename()).toMatch(/enhanced-review-.*-fallback-.*\.json/)
    })

    test('should include export metadata in downloaded file', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Intercept download content
      await page.route('**/download', async route => {
        const response = await route.fetch()
        const body = await response.text()
        const exportData = JSON.parse(body)

        // Verify export metadata
        expect(exportData.export_metadata.fallback_mode).toBe(true)
        expect(exportData.export_metadata.data_source).toBe('fallback_generated')
        expect(exportData.export_metadata.generation_method).toBe('client_side_parsing')
        
        await route.fulfill({ response })
      })

      await page.locator('button:has-text("Export Report")').click()
    })
  })

  test.describe('Responsive Design und Accessibility', () => {
    test('should display fallback banner responsively on mobile', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 }) // iPhone viewport
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toBeVisible()
      
      // Check that toggle button is accessible on mobile
      const toggleButton = page.locator('button:has-text("Show Raw Review")')
      await expect(toggleButton).toBeVisible()
      
      // Check banner content wraps properly
      const bannerText = page.locator('text="Generated fallback analysis"')
      await expect(bannerText).toBeVisible()
    })

    test('should maintain keyboard navigation in fallback mode', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Tab through interactive elements
      await page.keyboard.press('Tab') // Toggle button
      await expect(page.locator('button:has-text("Show Raw Review")')).toBeFocused()

      await page.keyboard.press('Tab') // Export button
      await expect(page.locator('button:has-text("Export Report")')).toBeFocused()

      await page.keyboard.press('Tab') // Technical details
      const detailsElement = page.locator('summary:has-text("Technical Details")')
      await expect(detailsElement).toBeFocused()

      // Activate toggle with keyboard
      await page.keyboard.press('Tab')
      await page.keyboard.press('Tab')
      await page.locator('button:has-text("Show Raw Review")').focus()
      await page.keyboard.press('Enter')
      await expect(page.locator('[data-testid="raw-review-display"]')).toBeVisible()
    })

    test('should announce fallback mode to screen readers', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')

      // Check ARIA labels and announcements
      const fallbackBanner = page.locator('[data-testid="fallback-banner"]')
      await expect(fallbackBanner).toHaveAttribute('role', 'alert')
      
      const toggleButton = page.locator('button:has-text("Show Raw Review")')
      await expect(toggleButton).toHaveAttribute('aria-label', /toggle.*view.*mode/i)

      // Check that badges have appropriate ARIA labels
      const fallbackBadge = page.locator('text="Fallback Generated"')
      await expect(fallbackBadge).toHaveAttribute('aria-label', /fallback.*mode.*indicator/i)
    })
  })

  test.describe('Performance und User Feedback', () => {
    test('should show loading state during fallback generation', async ({ page }) => {
      await setupMockAPI(page, 'standard')
      await page.goto(`/code-reviewer?session=test-session-1&step=review-results`)

      // Should show loading indicator initially
      await expect(page.locator('[data-testid="fallback-generation-loading"]')).toBeVisible()
      
      // Wait for fallback generation to complete
      await page.waitForLoadState('networkidle')
      await expect(page.locator('[data-testid="fallback-generation-loading"]')).not.toBeVisible()
      await expect(page.locator('[data-testid="fallback-banner"]')).toBeVisible()
    })

    test('should complete fallback generation within performance threshold', async ({ page }) => {
      const startTime = Date.now()
      
      await setupMockAPI(page, 'standard')
      await navigateToReviewResults(page, 'test-session-1')
      
      // Wait for fallback analysis to be visible
      await expect(page.locator('[data-testid="ac-analysis-section"]')).toBeVisible()
      
      const endTime = Date.now()
      const generationTime = endTime - startTime
      
      // Should complete within 3 seconds
      expect(generationTime).toBeLessThan(3000)
    })

    test('should provide retry mechanism for failed generation', async ({ page }) => {
      // Mock a failed generation scenario
      await page.route('**/api/code-reviewer/review-results/*', async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            results: {
              ...mockReviewResults.standard,
              raw_review: null, // Force generation failure
              structured_findings: null
            }
          })
        })
      })

      await navigateToReviewResults(page, 'test-session-1')

      // Should show retry option
      await expect(page.locator('button:has-text("Retry Loading")')).toBeVisible()
      
      // Click retry should reload the page
      const navigationPromise = page.waitForNavigation()
      await page.locator('button:has-text("Retry Loading")').click()
      await navigationPromise
    })
  })
})