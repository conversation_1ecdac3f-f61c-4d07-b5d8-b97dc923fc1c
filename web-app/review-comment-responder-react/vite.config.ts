import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import { VitePWA } from 'vite-plugin-pwa'
// import { splitVendorChunkPlugin } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')
  
  const isProduction = mode === 'production'
  const enableCodeSplitting = env.VITE_ENABLE_CODE_SPLITTING === 'true' || isProduction
  const enableServiceWorker = env.VITE_FEATURE_SERVICE_WORKER === 'true' || isProduction
  const enableBundleAnalyzer = env.VITE_BUNDLE_ANALYZER === 'true'

  return {
    plugins: [
      react({
        // JSX runtime optimization
        jsxRuntime: 'automatic'
      }),
      
      // Vendor chunk splitting for better caching (handled by manualChunks)
      // splitVendorChunkPlugin(),
      
      // Progressive Web App configuration
      ...(enableServiceWorker ? [
        VitePWA({
          registerType: 'autoUpdate',
          workbox: {
            // Cache strategy for assets
            runtimeCaching: [
              {
                urlPattern: /^https:\/\/api\./,
                handler: 'NetworkFirst',
                options: {
                  cacheName: 'api-cache',
                  networkTimeoutSeconds: 10,
                  expiration: {
                    maxEntries: 100,
                    maxAgeSeconds: 60 * 60 * 24 // 24 hours
                  }
                }
              },
              {
                urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
                handler: 'CacheFirst',
                options: {
                  cacheName: 'images-cache',
                  expiration: {
                    maxEntries: 200,
                    maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
                  }
                }
              },
              {
                urlPattern: /\.(?:js|css|html)$/,
                handler: 'StaleWhileRevalidate',
                options: {
                  cacheName: 'static-resources',
                  expiration: {
                    maxEntries: 100,
                    maxAgeSeconds: 60 * 60 * 24 * 7 // 7 days
                  }
                }
              }
            ]
          },
          manifest: {
            name: 'RMA Code Review System',
            short_name: 'CodeReview',
            description: 'Multi-Agent Code Review System for RMA',
            theme_color: '#000000',
            background_color: '#ffffff',
            display: 'standalone',
            start_url: '/',
            icons: [
              {
                src: '/icon-192x192.png',
                sizes: '192x192',
                type: 'image/png'
              },
              {
                src: '/icon-512x512.png',
                sizes: '512x512',
                type: 'image/png'
              }
            ]
          }
        })
      ] : [])
    ],

    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      }
    },

    // Performance optimizations
    build: {
      // Production optimizations
      ...(isProduction && {
        minify: 'esbuild',
        sourcemap: env.VITE_GENERATE_SOURCEMAP !== 'false',
        
        // Code splitting configuration
        rollupOptions: {
          output: {
            // Manual chunk splitting for better caching
            manualChunks: enableCodeSplitting ? {
              // Vendor chunks
              'react-vendor': ['react', 'react-dom', 'react-router-dom'],
              'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-progress'],
              'editor-vendor': ['@monaco-editor/react', 'highlight.js'],
              'chart-vendor': ['mermaid'],
              'socket-vendor': ['socket.io-client'],
              
              // Feature-specific chunks are dynamically determined
              'multi-agent': [
                './src/services/multiAgent/MultiAgentReviewService.ts',
                './src/components/MultiAgentProgressTracker.tsx',
                './src/hooks/useMultiAgentReview.ts'
              ],
              'monitoring': [
                './src/utils/monitoring.ts',
                './src/utils/logger.ts',
                './src/hooks/useAnalytics.ts'
              ]
            } : undefined,
            
            // Asset file naming for better caching
            chunkFileNames: (chunkInfo) => {
              const facadeModuleId = chunkInfo.facadeModuleId
              if (facadeModuleId) {
                const fileName = path.basename(facadeModuleId, path.extname(facadeModuleId))
                return `js/${fileName}-[hash].js`
              }
              return `js/[name]-[hash].js`
            },
            assetFileNames: (assetInfo) => {
              const extType = path.extname(assetInfo.name || '').substring(1)
              if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
                return `images/[name]-[hash][extname]`
              }
              if (/css/i.test(extType)) {
                return `css/[name]-[hash][extname]`
              }
              return `assets/[name]-[hash][extname]`
            }
          },
          
          // External dependencies (for micro-frontend scenarios)
          external: env.VITE_EXTERNALIZE_DEPS ? ['react', 'react-dom'] : []
        },
        
        // Bundle size limits
        chunkSizeWarningLimit: 1000,
        
        // Asset optimization
        assetsInlineLimit: 4096, // 4kb
        
        // CSS code splitting
        cssCodeSplit: enableCodeSplitting,
        
        // Report bundle size
        reportCompressedSize: enableBundleAnalyzer
      }),
      
      // Target modern browsers for better performance
      target: isProduction ? 'es2020' : 'esnext',
      
      // Optimize dependencies
      optimizeDeps: {
        include: [
          'react',
          'react-dom',
          'react-router-dom',
          'zustand',
          'socket.io-client'
        ],
        exclude: ['@vite/client', '@vite/env']
      }
    },

    // Development server configuration
    server: {
      port: 5173,
      host: '0.0.0.0',
      open: env.VITE_AUTO_OPEN_BROWSER !== 'false',
      
      // API proxy for development
      proxy: {
        '/api/multi-agent': {
          target: env.VITE_MULTI_AGENT_URL || 'http://localhost:8000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/multi-agent/, '')
        },
        '/api/code-reviewer': {
          target: env.VITE_CODE_REVIEWER_URL || 'http://localhost:5002',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/code-reviewer/, '')
        },
        '/api/claude': {
          target: env.VITE_CLAUDE_URL || 'http://localhost:5001',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api\/claude/, '')
        }
      },
      
      // Enable gzip compression
      middlewareMode: false
    },

    // Preview server configuration
    preview: {
      port: 4173,
      host: '0.0.0.0'
    },

    // Environment variable configuration
    envPrefix: 'VITE_',
    
    // Define global constants
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version || '0.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __COMMIT_HASH__: JSON.stringify(process.env.VITE_COMMIT_HASH || 'unknown')
    },

    // CSS configuration
    css: {
      // PostCSS configuration - let postcss.config.js handle this
      postcss: undefined,
      
      // CSS modules configuration
      modules: {
        localsConvention: 'camelCase'
      },
      
      // Development source maps
      devSourcemap: !isProduction
    },

    // JSON optimization
    json: {
      namedExports: true,
      stringify: false
    },

    // Worker configuration for web workers
    worker: {
      format: 'es',
      plugins: () => []
    },

    // Performance and caching
    cacheDir: 'node_modules/.vite',
    
    // Experimental features
    experimental: {
      renderBuiltUrl(filename, { hostType }) {
        if (hostType === 'js') {
          // Custom asset URL resolution for CDN deployment
          return env.VITE_CDN_URL ? `${env.VITE_CDN_URL}/${filename}` : filename
        }
        return filename
      }
    }
  }
})
