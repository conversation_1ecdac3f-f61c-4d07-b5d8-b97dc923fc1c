/**
 * Service Worker for RMA Code Review System
 * Provides offline capability and caching strategies for PWA functionality
 */

const CACHE_NAME = 'rma-code-review-v1.0.0'
const API_CACHE_NAME = 'rma-api-cache-v1.0.0'
const STATIC_CACHE_NAME = 'rma-static-cache-v1.0.0'

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/offline.html'
]

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/health',
  '/api/multi-agent/health',
  '/api/code-reviewer/health'
]

// Cache strategies
const CACHE_STRATEGIES = {
  // Network first for API calls
  NETWORK_FIRST: 'network-first',
  // Cache first for static assets
  CACHE_FIRST: 'cache-first',
  // Stale while revalidate for frequent updates
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
}

/**
 * Install event - cache initial assets
 */
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...')
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        console.log('[SW] Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      }),
      
      // Cache API health endpoints
      caches.open(API_CACHE_NAME).then((cache) => {
        console.log('[SW] Pre-caching API endpoints')
        return Promise.allSettled(
          API_ENDPOINTS.map(endpoint => 
            fetch(endpoint)
              .then(response => response.ok ? cache.put(endpoint, response) : null)
              .catch(() => null) // Ignore failures during pre-caching
          )
        )
      })
    ]).then(() => {
      console.log('[SW] Installation complete')
      // Force activation of new service worker
      return self.skipWaiting()
    })
  )
})

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...')
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames
            .filter(cacheName => 
              cacheName !== CACHE_NAME && 
              cacheName !== API_CACHE_NAME && 
              cacheName !== STATIC_CACHE_NAME
            )
            .map(cacheName => {
              console.log('[SW] Deleting old cache:', cacheName)
              return caches.delete(cacheName)
            })
        )
      }),
      
      // Take control of all clients
      self.clients.claim()
    ]).then(() => {
      console.log('[SW] Activation complete')
    })
  )
})

/**
 * Fetch event - implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return
  }
  
  // Skip chrome-extension and other protocols
  if (!url.protocol.startsWith('http')) {
    return
  }
  
  event.respondWith(handleRequest(request))
})

/**
 * Handle fetch requests with appropriate caching strategy
 */
async function handleRequest(request) {
  const url = new URL(request.url)
  
  try {
    // API requests - Network First strategy
    if (url.pathname.startsWith('/api/')) {
      return await networkFirstStrategy(request, API_CACHE_NAME)
    }
    
    // Static assets - Cache First strategy
    if (isStaticAsset(url.pathname)) {
      return await cacheFirstStrategy(request, STATIC_CACHE_NAME)
    }
    
    // HTML pages - Stale While Revalidate strategy
    if (request.headers.get('accept')?.includes('text/html')) {
      return await staleWhileRevalidateStrategy(request, CACHE_NAME)
    }
    
    // Default - Network First with fallback
    return await networkFirstStrategy(request, CACHE_NAME)
    
  } catch (error) {
    console.error('[SW] Error handling request:', error)
    return await getOfflineFallback(request)
  }
}

/**
 * Network First caching strategy
 */
async function networkFirstStrategy(request, cacheName) {
  try {
    // Try network first
    const networkResponse = await fetch(request.clone())
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
    
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', request.url)
    
    // Fallback to cache
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    throw error
  }
}

/**
 * Cache First caching strategy
 */
async function cacheFirstStrategy(request, cacheName) {
  // Try cache first
  const cachedResponse = await caches.match(request)
  if (cachedResponse) {
    return cachedResponse
  }
  
  // Fallback to network
  try {
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      const cache = await caches.open(cacheName)
      cache.put(request, networkResponse.clone())
    }
    
    return networkResponse
    
  } catch (error) {
    console.error('[SW] Both cache and network failed:', error)
    throw error
  }
}

/**
 * Stale While Revalidate caching strategy
 */
async function staleWhileRevalidateStrategy(request, cacheName) {
  const cache = await caches.open(cacheName)
  const cachedResponse = await cache.match(request)
  
  // Background fetch to update cache
  const fetchPromise = fetch(request).then(response => {
    if (response.ok) {
      cache.put(request, response.clone())
    }
    return response
  }).catch(() => {
    // Ignore network errors in background
  })
  
  // Return cached response immediately, or wait for network
  return cachedResponse || await fetchPromise
}

/**
 * Check if URL is a static asset
 */
function isStaticAsset(pathname) {
  return (
    pathname.startsWith('/assets/') ||
    pathname.startsWith('/js/') ||
    pathname.startsWith('/css/') ||
    pathname.startsWith('/images/') ||
    pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/)
  )
}

/**
 * Get offline fallback response
 */
async function getOfflineFallback(request) {
  const url = new URL(request.url)
  
  // HTML requests - return offline page
  if (request.headers.get('accept')?.includes('text/html')) {
    const offlineResponse = await caches.match('/offline.html')
    if (offlineResponse) {
      return offlineResponse
    }
    
    // Fallback offline HTML
    return new Response(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Offline - RMA Code Review</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { 
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              text-align: center; 
              padding: 50px;
              background: #f5f5f5;
            }
            .offline-container {
              max-width: 500px;
              margin: 0 auto;
              background: white;
              padding: 40px;
              border-radius: 8px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 { color: #333; }
            p { color: #666; line-height: 1.6; }
            .retry-btn {
              background: #007bff;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 4px;
              cursor: pointer;
              margin-top: 20px;
            }
            .retry-btn:hover { background: #0056b3; }
          </style>
        </head>
        <body>
          <div class="offline-container">
            <h1>🔌 You're offline</h1>
            <p>It looks like you've lost your internet connection. The page you're trying to access isn't available offline.</p>
            <p>Please check your connection and try again.</p>
            <button class="retry-btn" onclick="window.location.reload()">Retry</button>
          </div>
        </body>
      </html>
    `, {
      status: 200,
      headers: { 'Content-Type': 'text/html' }
    })
  }
  
  // API requests - return offline message
  if (url.pathname.startsWith('/api/')) {
    return new Response(JSON.stringify({
      error: 'Offline',
      message: 'This feature is not available offline',
      offline: true,
      timestamp: Date.now()
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    })
  }
  
  // Other requests - return 503
  return new Response('Service Unavailable - Offline', {
    status: 503,
    headers: { 'Content-Type': 'text/plain' }
  })
}

/**
 * Message event - handle commands from main thread
 */
self.addEventListener('message', (event) => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'GET_VERSION':
      event.ports[0].postMessage({
        type: 'VERSION',
        payload: { version: CACHE_NAME }
      })
      break
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          payload: { success: true }
        })
      }).catch((error) => {
        event.ports[0].postMessage({
          type: 'CACHE_CLEARED',
          payload: { success: false, error: error.message }
        })
      })
      break
      
    case 'CACHE_URLS':
      if (payload && payload.urls) {
        cacheUrls(payload.urls).then((results) => {
          event.ports[0].postMessage({
            type: 'URLS_CACHED',
            payload: { results }
          })
        })
      }
      break
      
    default:
      console.log('[SW] Unknown message type:', type)
  }
})

/**
 * Clear all caches
 */
async function clearAllCaches() {
  const cacheNames = await caches.keys()
  return Promise.all(cacheNames.map(name => caches.delete(name)))
}

/**
 * Cache specific URLs
 */
async function cacheUrls(urls) {
  const cache = await caches.open(CACHE_NAME)
  const results = []
  
  for (const url of urls) {
    try {
      const response = await fetch(url)
      if (response.ok) {
        await cache.put(url, response)
        results.push({ url, success: true })
      } else {
        results.push({ url, success: false, error: 'Network error' })
      }
    } catch (error) {
      results.push({ url, success: false, error: error.message })
    }
  }
  
  return results
}

/**
 * Background sync event (if supported)
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Implement background sync logic here
      // For example, sync pending API calls
      syncPendingRequests()
    )
  }
})

/**
 * Sync pending requests (placeholder)
 */
async function syncPendingRequests() {
  // Implementation would sync any pending requests stored in IndexedDB
  console.log('[SW] Background sync triggered')
}

/**
 * Push event - handle push notifications
 */
self.addEventListener('push', (event) => {
  if (!event.data) return
  
  try {
    const data = event.data.json()
    const options = {
      body: data.body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: data.data,
      actions: data.actions || []
    }
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    )
  } catch (error) {
    console.error('[SW] Error handling push event:', error)
  }
})

/**
 * Notification click event
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close()
  
  // Handle notification click
  event.waitUntil(
    self.clients.matchAll().then((clients) => {
      // Focus existing client or open new one
      if (clients.length > 0) {
        return clients[0].focus()
      } else {
        return self.clients.openWindow('/')
      }
    })
  )
})

console.log('[SW] Service worker script loaded')