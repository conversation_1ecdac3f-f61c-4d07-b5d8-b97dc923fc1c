#!/usr/bin/env python3
"""
Enhanced Claude Code PR Reviewer mit Jira Integration
Erweitert den Standard Reviewer um Acceptance Criteria Analysis und Business Context Discovery.
"""

import json
import os
import sys
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Union

# Import base reviewer and Jira integration
from claude_code_reviewer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from jira_integration import JiraIntegration, JiraTicket
from summary_analyzer import SummaryAnalyzer


class EnhancedClaudeReviewer(ClaudeCodeReviewer):
    """Enhanced reviewer with Jira integration and acceptance criteria analysis"""
    
    def __init__(self, config: Dict, worktree_path=None, repo_path=".", pr_url=None, branch_name=None):
        # Setup PR analyzer with worktree FIRST if we have branch info
        self.pr_analyzer = None
        actual_worktree_path = worktree_path
        
        if branch_name and not worktree_path:
            print(f"🔧 Initialisiere PR Analyzer für Branch: {branch_name}")
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cli-tools'))
                from bitbucket_pr_analyzer import <PERSON>bu<PERSON><PERSON><PERSON>nalyzer  # type: ignore
                self.pr_analyzer = BitbucketPRAnalyzer(
                    pr_url=pr_url,
                    branch_name=branch_name,
                    repo_path=repo_path,
                    use_worktree=True,
                    cleanup_worktree=False  # Keep worktree for review
                )
                
                # Setup worktree (this will create isolated environment for the branch)
                self.pr_analyzer._setup_worktree()
                
                if self.pr_analyzer.worktree_path:
                    actual_worktree_path = str(self.pr_analyzer.worktree_path)
                    print(f"✅ Worktree erstellt für Review: {actual_worktree_path}")
                else:
                    print(f"⚠️  Kein Worktree erstellt - arbeite im Haupt-Repository")
                    
            except Exception as e:
                print(f"⚠️  PR Analyzer Setup fehlgeschlagen: {e}")
                print(f"💡 Arbeite im Haupt-Repository: {repo_path}")
        
        # Initialize base reviewer with the (possibly new) worktree path
        super().__init__(actual_worktree_path, repo_path, pr_url, branch_name)
        
        self.config = config
        self.jira_integration = JiraIntegration(config)
        self.current_ticket = None
        
        # Extract ticket from branch if not provided and auto-extraction is enabled
        auto_extract = self.jira_integration.jira_config.get('ticket_extraction', {}).get('auto_extract_from_branch', True)
        if branch_name and not self.current_ticket and auto_extract:
            ticket_id = self.jira_integration.extract_ticket_id_from_branch(branch_name)
            if ticket_id:
                self.current_ticket = self.jira_integration.get_ticket(ticket_id)
        
        # Fallback to manual ticket ID from config if still no ticket
        if not self.current_ticket:
            manual_ticket_id = self.jira_integration.jira_config.get('ticket_extraction', {}).get('manual_ticket_id')
            if manual_ticket_id:
                print(f"🎫 Versuche manuell konfigurierte Ticket ID: {manual_ticket_id}")
                self.current_ticket = self.jira_integration.get_ticket(manual_ticket_id)
        
        print(f"🚀 Enhanced Claude Reviewer initialisiert")
        if self.current_ticket:
            print(f"🎫 Ticket geladen: {self.current_ticket.ticket_id} - {self.current_ticket.summary}")
            print(f"📋 Acceptance Criteria: {self.current_ticket.get_acceptance_criteria_count()} Items")
            print(f"📝 Ticket Description: {self.current_ticket.description[:200]}...")
            if self.current_ticket.acceptance_criteria:
                print(f"📋 AC Details:")
                for i, ac in enumerate(self.current_ticket.acceptance_criteria[:3], 1):
                    print(f"   {i}. {ac[:100]}...")
        else:
            print("⚠️  Kein Jira Ticket verfügbar - Standard Review Modus")
            # Debug why no ticket was found
            print(f"🔍 Debug Ticket Loading:")
            print(f"   Auto extract enabled: {self.jira_integration.jira_config.get('ticket_extraction', {}).get('auto_extract_from_branch', True)}")
            print(f"   Manual ticket file: {self.jira_integration.jira_config.get('ticket_extraction', {}).get('manual_ticket_file')}")
            print(f"   Jira enabled: {self.jira_integration.enabled}")
    
    def set_ticket(self, ticket_id: str) -> bool:
        """Manually set a specific ticket for analysis"""
        ticket = self.jira_integration.get_ticket(ticket_id)
        if ticket:
            self.current_ticket = ticket
            print(f"✅ Ticket gesetzt: {ticket.ticket_id} - {ticket.summary}")
            return True
        else:
            print(f"❌ Ticket {ticket_id} nicht gefunden")
            return False
    
    def generate_enhanced_review_prompt(self, review_type="comprehensive", include_business_context=True) -> str:
        """
        Generate enhanced review prompt with Jira ticket context and AC analysis
        """
        # Get base context
        base_context = ""
        if self.pr_url:
            base_context += f"PR URL: {self.pr_url}\n"
        if self.branch_name:
            base_context += f"Branch: {self.branch_name}\n"
        
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        base_context += f"\nÄnderungsstatistik:\n{diff_summary}\n"
        base_context += f"\nGeänderte Dateien ({len(changed_files)}):\n"
        
        if isinstance(changed_files, list):
            for file in changed_files[:10]:
                base_context += f"- {file}\n"
            if len(changed_files) > 10:
                base_context += f"... und {len(changed_files) - 10} weitere Dateien\n"
        
        # Add Jira ticket context if available
        ticket_context = ""
        if self.current_ticket:
            ticket_context = self._generate_ticket_context()
        
        # Enhanced prompts with AC analysis
        if review_type == "acceptance_criteria_focused":
            return self._generate_ac_focused_prompt(base_context, ticket_context)
        elif review_type == "business_context_discovery":
            return self._generate_business_context_prompt(base_context, ticket_context)
        elif review_type == "comprehensive_with_ac":
            return self._generate_comprehensive_ac_prompt(base_context, ticket_context)
        elif review_type == "bug_and_quality_analysis":
            return self._generate_bug_quality_prompt(base_context, ticket_context)
        else:
            # Fall back to standard prompts with ticket context
            standard_prompt = super().generate_review_prompt(review_type)
            if ticket_context:
                return f"{ticket_context}\n\n{standard_prompt}"
            return standard_prompt
    
    def _generate_ticket_context(self) -> str:
        """Generate comprehensive ticket context for Claude"""
        if not self.current_ticket:
            return ""
        
        ticket = self.current_ticket
        
        context = f"""
# 🎫 JIRA TICKET CONTEXT

## Ticket Information
- **ID:** [{ticket.ticket_id}]({self.jira_integration.server_url}/browse/{ticket.ticket_id})
- **Summary:** {ticket.summary}
- **Type:** {ticket.issue_type}
- **Status:** {ticket.status}
- **Priority:** {ticket.priority}
- **Assignee:** {ticket.assignee}

## Ticket Description
{ticket.description}

## 📋 Acceptance Criteria ({len(ticket.acceptance_criteria)} Items)
**WICHTIG: Diese Acceptance Criteria müssen vom Code erfüllt werden!**
"""
        
        if ticket.acceptance_criteria:
            for i, criterion in enumerate(ticket.acceptance_criteria, 1):
                context += f"{i}. {criterion}\n"
        else:
            context += "❌ Keine Acceptance Criteria definiert\n"
        
        if ticket.labels:
            context += f"\n**Labels:** {', '.join(ticket.labels)}\n"
        
        if ticket.components:
            context += f"**Components:** {', '.join(ticket.components)}\n"
        
        return context
    
    def _generate_ac_focused_prompt(self, base_context: str, ticket_context: str) -> str:
        """Generate prompt focused on acceptance criteria compliance"""
        
        if not self.current_ticket or not self.current_ticket.acceptance_criteria:
            return f"""
{base_context}

⚠️  **HINWEIS:** Kein Jira Ticket oder keine Acceptance Criteria verfügbar.
Führe ein Standard Code Review durch und identifiziere mögliche Business Requirements aus dem Code.

Analysiere:
1. Was versucht der Code zu implementieren? (Business Logic Analysis)
2. Welche Funktionalität wird hinzugefügt/geändert?
3. Gibt es offensichtliche Requirements die fehlen könnten?
4. Empfehlungen für bessere Requirement-Definition

Zusätzlich führe Standard Code Quality Review durch.
"""
        
        return f"""
{ticket_context}

{base_context}

# 🎯 ZWEISTUFIGER ENHANCED REVIEW

Du führst einen **zweistufigen Enhanced Review** für diese Pull Request durch.

## PHASE 1: 📋 ACCEPTANCE CRITERIA COMPLIANCE REVIEW

### 1. 🔍 BUSINESS CONTEXT ANALYSIS
Verstehe zuerst das Ticket und den Code:
- Analysiere die Ticket-Beschreibung und AC
- Verstehe was das Feature/Fix bewirken soll
- Identifiziere die betroffenen Codebase-Bereiche
- Erkenne Business Logic Patterns

### 2. 📋 ACCEPTANCE CRITERIA COMPLIANCE CHECK
Für jede AC punkt-für-punkt prüfen:

**WICHTIG: Verwende das View tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets!**

**Format für jede AC:**
```
### ✅/❌/⚠️ AC [nummer]: [kurze beschreibung]
**Status:** ERFÜLLT / NICHT ERFÜLLT / TEILWEISE ERFÜLLT
**Location:** `file.ts:123-127` (EXAKTE Zeilen-Angabe erforderlich!)
**Code-Implementierung:** 
````language
// Zeige den relevanten Code hier mit Zeilennummern
const example = "actual code from the file";
````
**Problem:** [Was fehlt oder ist falsch] (falls zutreffend)
**Erwarteter Code:** (Bei Problemen zeige wie es richtig aussehen sollte)
````language
// Korrekte Implementation
const corrected = "how it should be implemented";
````
**Empfehlung:** [Konkrete Lösung mit Beispielen]
```

**ANALYSE-VORGEHEN:**
1. **View verwenden:** Lies alle relevanten Dateien mit dem View tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien
4. **Context bieten:** Zeige 2-3 Zeilen vor/nach für Verständnis

### 3. 🔍 THEMENVERFEHLUNG DETECTION
- Wurden Features implementiert die NICHT in den AC stehen?
- Fehlen offensichtliche Requirements?
- Stimmt die technische Umsetzung mit dem Business Case überein?

---

## PHASE 2: 🐛 DETAILLIERTE CODE QUALITY & BUG ANALYSIS

**NACH** der AC-Analyse führe eine gründliche Code-Untersuchung durch:

### A. 🐛 BUG DETECTION & LOGIC ERRORS
- **Logic Errors:** Falsche Conditional Logic, Off-by-one Errors
- **Null/Undefined Handling:** Potentielle NPE, undefined access
- **Type Errors:** Implizite Type Conversions, Type Mismatches
- **Edge Cases:** Boundary Conditions, Empty Arrays/Objects
- **Error Handling:** Missing try/catch, unhandled Promise rejections
- **Race Conditions:** Async/Promise Chains, parallel execution issues

### B. 🔍 CODE SMELLS & QUALITY ISSUES
- **Code Duplication:** Identische oder ähnliche Code-Blöcke
- **Long Methods:** Funktionen >50 Zeilen
- **Deep Nesting:** >3 Ebenen Verschachtelung
- **Magic Numbers:** Hardcoded Values ohne Konstanten
- **Inconsistent Naming:** Variable/Function Naming Patterns
- **Dead Code:** Unused Variables, Functions, Imports
- **Complex Expressions:** Schwer lesbare boolean/math Expressions

### C. 📊 VARIABLE & PARAMETER ANALYSIS
- **Naming Consistency:** Vergleiche ähnliche Variable/Parameter Namen
- **Scope Issues:** Variable Shadowing, unintended Global Access
- **Parameter Validation:** Missing Input Validation
- **Return Value Consistency:** Inconsistent Return Types/Patterns
- **Side Effects:** Unerwartete Variable Mutations

### D. 🏗️ ARCHITECTURAL CONCERNS
- **Separation of Concerns:** Business Logic in Controller/View
- **Dependency Issues:** Circular Dependencies, Tight Coupling
- **Error Propagation:** Proper Error Bubbling
- **Resource Management:** Memory Leaks, Connection Leaks
- **Performance Anti-Patterns:** N+1 Queries, unnecessary loops

### E. 🔒 SECURITY & SAFETY
- **Input Validation:** SQL Injection, XSS vulnerabilities
- **Authentication:** Missing access controls
- **Data Exposure:** Sensitive data in logs/responses
- **Hardcoded Secrets:** API Keys, Passwords in code

## 📊 OUTPUT FORMAT:

```markdown
# Enhanced Two-Phase Review Report

## PHASE 1: Acceptance Criteria Compliance
### Executive Summary - AC
- AC Compliance Rate: X/Y erfüllt
- Business Logic Alignment: ✅/❌

### Detailed AC Analysis
[Für jede AC wie oben beschrieben]

---

## PHASE 2: Code Quality & Bug Analysis
### Executive Summary - Code Quality
- Code Quality Score: X/10
- Critical Bugs Found: X
- Code Smells: X
- Security Issues: X

### 🐛 Bug Detection Results
[Detailed findings]

### 🔍 Code Quality Issues
[Specific code smells and quality problems]

### 📊 Variable & Parameter Analysis
[Naming, scope, consistency issues]

### 🏗️ Architectural Assessment
[Design and structure issues]

### 🔒 Security Findings
[Security vulnerabilities]

## 🎯 Combined Recommendations
### Must-Fix (Blocker):
- [ ] [Critical AC violations]
- [ ] [Critical bugs/security issues]

### Should-Fix (Important):
- [ ] [Minor AC issues]
- [ ] [Code quality improvements]

### Nice-to-Have (Optional):
- [ ] [Refactoring suggestions]
- [ ] [Performance optimizations]
```

## ❓ CLARIFYING QUESTIONS
Bei Unklarheiten stelle konkrete Fragen:
- An Product Owner (für AC)
- An Technical Lead (für Architecture)
- An Security Team (für Security Issues)

**WICHTIG:** 
- Beide Phasen sind gleichwertig wichtig
- AC Compliance ist business-kritisch
- Code Quality ist maintenance-kritisch
- Sei sehr genau und detail-orientiert in beiden Phasen
- Prüfe jeden Code-Block systematisch auf die oben genannten Issues
"""
    
    def _generate_business_context_prompt(self, base_context: str, ticket_context: str) -> str:
        """Generate prompt for business context discovery"""
        
        return f"""
{ticket_context}

{base_context}

# 🏢 BUSINESS CONTEXT DISCOVERY & ANALYSIS

Du führst eine **Business Context Analysis** für diese Pull Request durch.

## Deine Aufgaben:

### 1. 🔍 DOMAIN EXPERTISE ENTWICKLUNG
Verstehe das Business Domain:
- Was ist der Kontext dieser Änderung im größeren System?
- Welche User Journey wird beeinflusst?
- Welche Business Processes sind betroffen?
- Wie fügt sich das in die Produkt-Roadmap ein?

### 2. 📊 IMPACT ANALYSIS
Analysiere Business Impact:
- **User Experience:** Wie verändert sich die UX?
- **Performance:** Business-kritische Performance Impacts?
- **Data Flow:** Welche Daten werden wie verarbeitet?
- **Integration:** Impacts auf andere Services/Teams?

### 3. 🎯 REQUIREMENT DISCOVERY
Erkenne implicit Requirements:
- Was wurde NICHT in den AC erwähnt aber ist offensichtlich nötig?
- Welche Edge Cases sind business-relevant?
- Monitoring/Alerting Requirements?
- Compliance/Legal Considerations?

### 4. 🔮 FUTURE-PROOFING
Denke an zukünftige Requirements:
- Skalierbarkeit für Business Growth
- Erweiterbarkeit für neue Features
- Maintainability für das Team

### 5. ❓ STRATEGIC QUESTIONS
Stelle strategische Fragen wenn unklar:
- "Warum wurde dieser Ansatz gewählt?"
- "Wie misst man Erfolg dieses Features?"
- "Was sind die Business KPIs?"
- "Gibt es Regulatory Requirements?"

## Output Format:
1. **Business Context Summary**
2. **User Story Impact Analysis**
3. **Technical-Business Alignment Review**
4. **Implicit Requirements Discovery**
5. **Strategic Questions & Recommendations**

**WICHTIG:** Denke wie ein Business Analyst und Technical Lead gleichzeitig.
"""
    
    def _generate_comprehensive_ac_prompt(self, base_context: str, ticket_context: str) -> str:
        """Generate comprehensive prompt with AC analysis and detailed code quality check"""
        
        return f"""
{ticket_context}

{base_context}

# 🎯 COMPREHENSIVE ENHANCED REVIEW (Zweistufig)

Du führst einen **umfassenden zweistufigen Code Review mit Jira Integration** durch.

**🚨 MANDATORY: Du MUSST für jedes Issue präzise Code-Locations und Snippets zeigen!**

## TOOL USAGE REQUIREMENTS:
- **View Tool:** ZWINGEND für jede Code-Analyse verwenden
- **Code Snippets:** IMMER tatsächlichen Code aus Dateien zeigen
- **Exakte Locations:** Format `file.ts:123-127` für alle Issues
- **Before/After:** Bei Problemen Originalcode UND Lösung zeigen

## PHASE 1: 📋 ACCEPTANCE CRITERIA COMPLIANCE

### 1.1 AC Analysis Instructions
{self._get_ac_analysis_instructions() if self.current_ticket and self.current_ticket.acceptance_criteria else "Keine AC verfügbar - analysiere Business Requirements aus Code"}

---

## PHASE 2: 🔍 DETAILLIERTE CODE QUALITY & BUG ANALYSIS

### 2.1 🐛 SYSTEMATIC BUG DETECTION

**🚨 FÜR JEDEN BUG MANDATORY:**
1. **View Tool verwenden** um Code zu analysieren
2. **Exakte Location** angeben (`file.ts:123-127`)
3. **Problematischen Code zeigen** (Original aus Datei)
4. **Erklären warum** es ein Problem ist
5. **Lösung zeigen** (korrigierter Code)

Untersuche den Code systematisch auf:

**Logic Errors:** (Jeder Bug braucht Code-Snippet!)
- Conditional Logic Fehler (if/else, switch statements)
- Loop Logic Fehler (for, while, forEach)
- Boolean Logic Fehler (&&, ||, ! Operatoren)
- Off-by-one Errors in Arrays/Indices
- Falsche Vergleiche (==, ===, <, >, etc.)

**Runtime Errors:** (Jeder Bug braucht Code-Snippet!)
- Null/Undefined Access (`obj.prop` ohne null check)
- Array Access (`arr[i]` ohne bounds check)
- Type Errors (String.number operations)
- Async/Promise handling errors
- Exception handling gaps

**Data Flow Issues:** (Jeder Bug braucht Code-Snippet!)
- Variable Mutations at wrong scope
- Unintended side effects
- State inconsistencies
- Race conditions in async code

**BUG ANALYSIS BEISPIEL:**
```
#### 🚨 Null Access Risk
**Location:** `services/UserService.ts:45-47`
**Code:**
````typescript
const userData = await api.getUser(id);
return userData.profile.name; // ❌ Gefahr: userData könnte null sein
````
**Problem:** Kein null-check vor Zugriff auf nested properties
**Impact:** Runtime Error wenn API null zurückgibt
**Fix:**
````typescript
const userData = await api.getUser(id);
if (!userData?.profile) {{
  throw new Error('User data not found');
}}
return userData.profile.name; // ✅ Sicher
````
```

### 2.2 🔍 CODE SMELL DETECTION
Identifiziere systematisch:

**Duplication Analysis:**
- Identische Code-Blöcke (>5 Zeilen)
- Ähnliche Funktionen mit kleinen Unterschieden
- Copy-Paste Code Patterns
- Redundante Conditional Logic

**Complexity Issues:**
- Funktionen >50 Zeilen
- Deep Nesting (>3 Ebenen)
- Komplexe Boolean Expressions
- Switch statements mit >10 cases
- Callback Hell / Promise Chains

**Naming & Consistency:**
- Inkonsistente Variablennamen
- Missverständliche Function Namen
- Magic Numbers ohne Konstanten
- Abbreviations vs Full Names

### 2.3 📊 VARIABLE & PARAMETER DEEP ANALYSIS
Vergleiche systematisch:

**Naming Patterns:**
- Alle Variable/Parameter Namen auflisten
- Naming Conventions prüfen (camelCase, etc.)
- Konsistenz zwischen ähnlichen Variables
- Meaningful vs Generic Namen

**Scope & Usage:**
- Variable Shadowing Detection
- Unused Variables/Parameters
- Global vs Local Variable Usage
- Parameter Order Consistency

**Type & Value Analysis:**
- Type Consistency (string vs number)
- Return Value Patterns
- Default Parameter Usage
- Optional vs Required Parameters

### 2.4 🏗️ ARCHITECTURAL CODE ANALYSIS

**Design Patterns:**
- Single Responsibility Principle violations
- Dependency Injection usage
- Error Handling Patterns
- Abstraction Levels

**Integration Points:**
- API Call Patterns
- Database Query Patterns
- Error Propagation
- Logging Consistency

### 2.5 🔒 SECURITY & PERFORMANCE DEEP DIVE

**Security Analysis:**
- Input Validation gaps
- SQL Injection possibilities
- XSS vulnerabilities
- Authentication/Authorization checks
- Sensitive Data Handling

**Performance Issues:**
- N+1 Query patterns
- Unnecessary Loops
- Memory Usage patterns
- Async/Await efficiency

## 📊 COMPREHENSIVE OUTPUT FORMAT:

```markdown
# Comprehensive Two-Phase Enhanced Review

## PHASE 1: 📋 ACCEPTANCE CRITERIA ANALYSIS
### AC Executive Summary
- Total AC: X
- Fulfilled: X (X%)
- Partially Fulfilled: X
- Not Fulfilled: X
- Business Alignment Score: X/10

### Detailed AC Results
[Für jede AC detailliert]

---

## PHASE 2: 🔍 CODE QUALITY & BUG ANALYSIS
### Code Quality Executive Summary
- Overall Code Quality: X/10
- Critical Bugs: X
- Code Smells: X
- Security Issues: X
- Performance Issues: X
- Duplication Level: X%

### 🐛 Bug Detection Results

**WICHTIG: Für jeden Bug MUSS gezeigt werden:**
- Exakte Datei und Zeilennummer
- Der problematische Code-Snippet
- Warum es ein Problem ist
- Wie die Lösung aussehen sollte

#### Critical Bugs (Must Fix)
**Format pro Bug:**
```
#### 🚨 [Bug Name]
**Location:** `file.ts:123-125`
**Severity:** Critical
**Code:**
````language
// Problematischer Code hier
const buggy = code.example();
````
**Problem:** Beschreibung warum es problematisch ist
**Impact:** Welche Auswirkungen es hat
**Fix:**
````language
// Korrekte Implementation
const fixed = proper.implementation();
````
**Priority:** Must-Fix vor Merge
```

#### Logic Errors
**Format:** Gleich wie Critical Bugs, aber mit Severity: Logic Error

#### Runtime Risk Issues
**Format:** Gleich wie Critical Bugs, aber mit Severity: Runtime Risk

### 🔍 Code Quality Analysis

#### Code Duplication
**Format mit Code-Beispielen:**
```
#### 📋 Duplicated Code Block: [Description]
**Block 1:** `file1.ts:45-52`
````typescript
// Duplicated code example 1
const duplicatedLogic = processData(input);
return formatResult(duplicatedLogic);
````

**Block 2:** `file2.ts:78-85`
````typescript
// Similar/identical code
const duplicatedLogic = processData(input);
return formatResult(duplicatedLogic);  
````

**Impact:** Code maintenance, potential inconsistencies
**Refactoring Suggestion:**
````typescript
// Extract to shared utility
function sharedDataProcessor(input) {{
  const processedLogic = processData(input);
  return formatResult(processedLogic);
}}
````
```

#### Complexity Issues
**Format mit Code-Analyse:**
```
#### 🔍 Complex Function: [FunctionName]
**Location:** `file.ts:123-170` (47 lines)
**Complexity Issues:**
- Nested loops: 3 levels deep
- Cyclomatic complexity: 12 (recommended: <10)
- Multiple responsibilities

**Problematic Code Pattern:**
````language
// Show the complex function structure
function complexFunction() {{
  for (const item of items) {{        // Level 1
    if (condition) {{
      for (const subItem of item) {{  // Level 2
        if (anotherCondition) {{
          for (const deep of sub) {{  // Level 3
            // Complex logic here
          }}
        }}
      }}
    }}
  }}
}}
````
**Refactoring Recommendation:** Break into smaller functions
```

#### Naming & Consistency Issues
**Format mit Beispielen:**
```
#### 📝 Inconsistent Naming Pattern: [Description]
**Examples:**
- `file1.ts:23`: `userDataObject` (camelCase)
- `file2.ts:45`: `user_data_item` (snake_case)  
- `file3.ts:67`: `UserDataList` (PascalCase)

**Impact:** Code readability and maintenance
**Recommended Pattern:** `camelCase` für variables, `PascalCase` für types
```

### 📊 Variable & Parameter Analysis
#### Naming Comparison Table
| File | Variable/Param | Pattern | Consistency | Suggestion |
|------|----------------|---------|-------------|------------|
| file1.ts | airQualityData | camelCase | ✅ | - |
| file2.ts | air_quality_id | snake_case | ❌ | airQualityId |

#### Scope & Usage Issues
- Unused variables: [List]
- Shadowed variables: [List]
- Global scope usage: [List]

### 🏗️ Architectural Assessment
#### Design Pattern Usage
- [ ] Proper separation of concerns
- [ ] Consistent error handling
- [ ] Appropriate abstraction levels

#### Integration Quality
- API usage patterns: [Assessment]
- Database interaction: [Assessment]
- Error propagation: [Assessment]

### 🔒 Security & Performance
#### Security Findings
- Input validation: [Status]
- Authentication checks: [Status]
- Data exposure risks: [List any issues]

#### Performance Analysis
- Query efficiency: [Assessment]
- Memory usage: [Assessment]
- Async patterns: [Assessment]

## 🎯 COMBINED ACTION ITEMS

### 🚨 CRITICAL (Must Fix Before Merge)
- [ ] [AC violation item]
- [ ] [Critical bug item]
- [ ] [Security issue item]

### ⚠️ IMPORTANT (Should Fix)
- [ ] [Code quality item]
- [ ] [Performance item]
- [ ] [Consistency item]

### 💡 SUGGESTIONS (Nice to Have)
- [ ] [Refactoring suggestion]
- [ ] [Performance optimization]
- [ ] [Code simplification]

## ❓ QUESTIONS & CLARIFICATIONS
1. [Specific question about AC X]
2. [Technical question about implementation Y]
3. [Architecture question about approach Z]
```

## 🔧 ANALYSIS TOOLS USAGE
Verwende diese Tools systematisch:
- **View**: Jede geänderte Datei vollständig lesen
- **Grep**: Pattern suchen (duplicated code, naming patterns)
- **Bash**: Git commands für context

**WICHTIG:** 
- Beide Phasen sind gleichwertig kritisch
- Jede AC muss explizit geprüft werden
- Jede Code-Datei muss auf Bugs/Smells untersucht werden
- Alle Variablen/Parameter müssen verglichen werden
- Sei systematisch und gründlich in beiden Phasen
"""
    
    def _get_ac_analysis_instructions(self) -> str:
        """Get specific instructions for AC analysis"""
        if not self.current_ticket or not self.current_ticket.acceptance_criteria:
            return "Keine Acceptance Criteria verfügbar"
        
        instructions = "Prüfe jede Acceptance Criteria punkt-für-punkt:\n\n"
        
        for i, criterion in enumerate(self.current_ticket.acceptance_criteria, 1):
            instructions += f"**AC {i}:** {criterion}\n"
            instructions += f"- Status: ✅ ERFÜLLT / ❌ NICHT ERFÜLLT / ⚠️ TEILWEISE\n"
            instructions += f"- Code-Implementierung: [wo und wie implementiert]\n"
            instructions += f"- Probleme: [falls vorhanden]\n\n"
        
        return instructions
    
    def _generate_bug_quality_prompt(self, base_context: str, ticket_context: str) -> str:
        """Generate focused bug detection and code quality analysis prompt"""
        
        return f"""
{ticket_context if ticket_context else "# 🔍 CODE QUALITY & BUG ANALYSIS"}

{base_context}

# 🐛 DETAILLIERTE BUG DETECTION & CODE QUALITY ANALYSIS

Du führst eine **systematische Bug Detection und Code Quality Analyse** durch.

## 🎯 DEINE AUFGABE:
Untersuche den Code systematisch und gründlich auf:
1. **Bugs & Logic Errors**
2. **Code Smells & Quality Issues** 
3. **Variable & Parameter Inconsistencies**
4. **Architectural Problems**
5. **Security & Performance Issues**

---

## 🔍 SYSTEMATIC ANALYSIS FRAMEWORK

### PHASE 1: 🐛 BUG DETECTION

#### A. Logic Errors (Critical)
- **Conditional Logic:** Prüfe alle if/else, switch statements
- **Loop Logic:** for, while, forEach - Off-by-one errors?
- **Boolean Logic:** &&, ||, ! - Korrekte Operator usage?
- **Comparison Errors:** ==, ===, <, > - Type-safe comparisons?
- **Null/Undefined Handling:** Fehlende null checks?

#### B. Runtime Error Risks
- **Array Access:** arr[i] ohne bounds checking
- **Object Property Access:** obj.prop ohne null check
- **Type Conversion Errors:** Implicit string/number conversion
- **Async/Promise Errors:** Unhandled promise rejections
- **Exception Handling:** Missing try/catch blocks

#### C. Data Flow Issues
- **Variable Mutations:** Unerwartete state changes
- **Side Effects:** Funktionen mit ungewollten side effects
- **Race Conditions:** Async operations order
- **Memory Leaks:** Event listeners, timers not cleaned up

### PHASE 2: 🔍 CODE QUALITY ANALYSIS

#### A. Code Duplication Detection
**Aufgabe:** Finde identische oder sehr ähnliche Code-Blöcke
- Identische Funktionen (>5 Zeilen)
- Copy-Paste Code Patterns
- Redundante Conditional Logic
- Ähnliche Error Handling Patterns

#### B. Complexity & Readability
- **Long Functions:** >50 Zeilen
- **Deep Nesting:** >3 Ebenen
- **Complex Boolean Expressions:** Schwer lesbar
- **Magic Numbers:** Hardcoded values ohne Konstanten
- **Callback Hell:** Verschachtelte Callbacks

#### C. Naming & Consistency Analysis
**Aufgabe:** Vergleiche ALLE Variablen und Parameter Namen
- Inconsistent naming patterns (camelCase vs snake_case)
- Misleading variable names
- Generic names (data, result, temp)
- Abbreviations vs full names inconsistency

### PHASE 3: 📊 VARIABLE & PARAMETER COMPARISON

#### Systematic Variable Analysis:
1. **Liste ALLE Variablen/Parameter auf** aus allen Files
2. **Gruppiere ähnliche Concepts** (z.B. alle "article" related)
3. **Vergleiche Naming Patterns** zwischen ähnlichen Variablen
4. **Identifiziere Inconsistencies** in naming/typing

**Example Analysis Format:**
```
Variable Group: "Article Data"
- File1: articleData (camelCase) ✅
- File2: article_data (snake_case) ❌ 
- File3: artData (abbreviated) ❌
-> Recommendation: Standardize to articleData
```

### PHASE 4: 🏗️ ARCHITECTURAL ANALYSIS

#### Design Issues:
- **Single Responsibility:** Functions doing too much
- **Tight Coupling:** Hard dependencies between modules
- **Missing Abstractions:** Repeated business logic
- **Error Handling Patterns:** Inconsistent error propagation

### PHASE 5: 🔒 SECURITY & PERFORMANCE

#### Security Checks:
- **Input Validation:** SQL injection, XSS risks
- **Authentication:** Missing access controls
- **Data Exposure:** Sensitive data in logs/responses
- **Hardcoded Secrets:** API keys, passwords

#### Performance Issues:
- **N+1 Queries:** Database query patterns
- **Unnecessary Loops:** Inefficient iterations
- **Memory Usage:** Large object allocations
- **Async Efficiency:** Proper Promise usage

---

## 📊 REQUIRED OUTPUT FORMAT:

```markdown
# Systematic Bug Detection & Code Quality Report

## Executive Summary
- **Bugs Found:** X Critical, Y Medium, Z Low
- **Code Quality Score:** X/10
- **Duplication Level:** X% of code
- **Variable Consistency:** X/10
- **Security Risk Level:** Low/Medium/High

---

## 🐛 BUG DETECTION RESULTS

### Critical Bugs (Must Fix)
1. **[Bug Type]** in [File:Line]
   - **Issue:** [Detailed description]
   - **Impact:** [What could go wrong]
   - **Fix:** [Specific solution]

### Logic Errors
1. **[Error Type]** in [File:Line]
   - **Issue:** [Description]
   - **Risk:** [Potential impact]
   - **Fix:** [Solution]

### Runtime Risk Issues
1. **[Risk Type]** in [File:Line]
   - **Scenario:** [When it could fail]
   - **Fix:** [Prevention method]

---

## 🔍 CODE QUALITY ANALYSIS

### Code Duplication Found
1. **Duplicated Block 1:**
   - **Files:** [File1:Lines] and [File2:Lines]
   - **Similarity:** [Percentage or description]
   - **Refactoring:** [Suggested solution]

### Complexity Issues
1. **Function: [functionName]** in [File:Line]
   - **Issue:** [Too long/complex/nested]
   - **Lines:** [Number of lines]
   - **Suggestion:** [How to simplify]

### Code Smells
1. **[Smell Type]** in [File:Line]
   - **Issue:** [Description]
   - **Impact:** [Why it's problematic]
   - **Fix:** [How to improve]

---

## 📊 VARIABLE & PARAMETER ANALYSIS

### Naming Inconsistencies Table
| Concept | File | Variable Name | Pattern | Status | Suggestion |
|---------|------|---------------|---------|--------|------------|
| Article Data | file1.ts | articleData | camelCase | ✅ | - |
| Article Data | file2.ts | article_data | snake_case | ❌ | articleData |
| Air Quality | file3.ts | airQualityId | camelCase | ✅ | - |

### Scope & Usage Issues
- **Unused Variables:** [List with locations]
- **Variable Shadowing:** [List with locations]
- **Global Scope Usage:** [List issues]

### Parameter Consistency
- **Inconsistent Parameter Order:** [List functions]
- **Missing Parameter Validation:** [List functions]
- **Type Inconsistencies:** [List issues]

---

## 🏗️ ARCHITECTURAL ASSESSMENT

### Design Pattern Issues
- **SRP Violations:** [List functions doing too much]
- **Tight Coupling:** [List dependency issues]
- **Missing Abstractions:** [List repeated logic]

### Error Handling
- **Inconsistent Patterns:** [List different error handling approaches]
- **Missing Error Handling:** [List unhandled scenarios]

---

## 🔒 SECURITY & PERFORMANCE

### Security Findings
- **Input Validation Gaps:** [List missing validations]
- **Potential Vulnerabilities:** [List security risks]

### Performance Issues
- **Query Efficiency:** [Assessment]
- **Loop Optimization:** [Issues found]
- **Memory Usage:** [Potential leaks]

---

## 🎯 PRIORITIZED ACTION ITEMS

### 🚨 CRITICAL (Fix Before Merge)
- [ ] [Critical bug with specific location]
- [ ] [Security vulnerability with location]

### ⚠️ IMPORTANT (Should Fix Soon)
- [ ] [Logic error with location]
- [ ] [Major code duplication]
- [ ] [Variable naming inconsistency]

### 💡 IMPROVEMENTS (Nice to Have)
- [ ] [Code smell fix]
- [ ] [Performance optimization]
- [ ] [Refactoring suggestion]

## 📈 RECOMMENDATIONS

### Immediate Actions:
1. [Specific action for critical bug]
2. [Specific action for security issue]

### Code Quality Improvements:
1. [Specific refactoring suggestion]
2. [Naming standardization plan]

### Long-term Suggestions:
1. [Architectural improvement]
2. [Process improvement]
```

---

## 🛠️ ANALYSIS INSTRUCTIONS:

1. **Be Systematic:** Analyze each file thoroughly
2. **Use Tools:** View all files, Grep for patterns, Bash for git context
3. **Compare Everything:** Variables, functions, patterns across files
4. **Be Specific:** Give exact file names, line numbers, code examples
5. **Prioritize:** Critical bugs > Quality issues > Suggestions

**WICHTIG:** Dies ist eine gründliche Code-Untersuchung. Nimm dir die Zeit, jeden Code-Block systematisch zu analysieren und alle Variablen/Parameter zu vergleichen.
"""
    
    def perform_enhanced_review(self, review_type="comprehensive_with_ac", output_file="enhanced_review_report.md", 
                              include_context=True, show_progress=True, include_summary=False) -> str:
        """
        Perform enhanced review with Jira integration
        """
        print(f"🚀 Starte Enhanced Code Review...")
        print(f"📂 Arbeitsverzeichnis: {self.working_path}")
        print(f"🔍 Review-Typ: {review_type}")
        
        if self.current_ticket:
            print(f"🎫 Ticket: {self.current_ticket.ticket_id} - {self.current_ticket.summary}")
            print(f"📋 AC Items: {self.current_ticket.get_acceptance_criteria_count()}")
        
        try:
            # Create enhanced CLAUDE.md context
            claude_md_path = None
            if include_context:
                claude_md_path = self.create_enhanced_claude_md_context()
            
            # Generate enhanced prompt
            prompt = self.generate_enhanced_review_prompt(review_type)
            
            # Add explicit instruction to avoid writing tools at the end
            prompt += """

⚠️  WICHTIGE ANWEISUNG:
- Erstelle KEINE Dateien oder TODO-Listen
- Verwende KEINE Write, Edit, TodoWrite Tools
- Gib dein Review direkt als Text-Antwort
- Der Report wird automatisch vom System erstellt

Erstelle dein Review als Markdown-Text in deiner Antwort.
"""
            
            # Perform review with progress tracking
            if show_progress:
                # Determine max turns based on review type
                if review_type in ["comprehensive_with_ac", "bug_and_quality_analysis"]:
                    max_turns = 35  # More turns for comprehensive analysis
                elif review_type == "acceptance_criteria_focused":
                    max_turns = 25  # Medium turns for AC analysis
                else:
                    max_turns = 20  # Standard turns
                
                review_result = self._run_claude_command_with_progress(
                    prompt, 
                    timeout=1200,  # 20 minutes for comprehensive reviews
                    max_turns=max_turns
                )
            else:
                review_result = self._run_claude_command(
                    prompt, 
                    output_format="text",
                    timeout=900
                )
            
            # Ensure review_result is a string for the report
            if isinstance(review_result, dict):
                # If result is a dict (from JSON response), convert to string
                result_str = json.dumps(review_result, indent=2) if review_result else ""
            elif review_result is None:
                result_str = ""
            else:
                result_str = str(review_result)
            
            # Create enhanced report
            self._create_enhanced_report(result_str, review_type, output_file)
            
            print(f"✅ Enhanced Review abgeschlossen!")
            
            # Phase 3: Summary & Explanation (optional)
            if include_summary:
                print(f"\n" + "="*80)
                print(f"🚀 Starte Phase 3: Summary & Explanation...")
                try:
                    summary_result = self.perform_summary_phase(result_str, review_type)
                    print(f"✅ Phase 3: Summary & Explanation abgeschlossen!")
                except Exception as e:
                    print(f"⚠️  Phase 3 fehlgeschlagen, Review-Ergebnisse bleiben verfügbar: {e}")
            
            # Show worktree information for manual review
            if self.pr_analyzer and hasattr(self.pr_analyzer, 'worktree_path') and self.pr_analyzer.worktree_path:
                print(f"\n📁 Worktree für manuelle Überprüfung: {self.pr_analyzer.worktree_path}")
                print("💡 Für manuelle Code-Inspektion:")
                print(f"   cd {self.pr_analyzer.worktree_path}")
                print("   # Code manuell überprüfen")
                if include_summary:
                    print("   # IMPLEMENTATION_SUMMARY.md anzeigen für detaillierte Analyse")
                print("\n🗑️  Zum späteren Aufräumen:")
                print(f"   git worktree remove {self.pr_analyzer.worktree_path}")
            
            return result_str
            
        except Exception as e:
            print(f"❌ Enhanced Review fehlgeschlagen: {e}")
            raise e
            
        finally:
            # Cleanup temporary CLAUDE.md
            if claude_md_path and claude_md_path.exists() and include_context:
                claude_md_path.unlink()
                print(f"🗑️  Temporäre CLAUDE.md entfernt")
                
                # Restore backup if exists
                backup_path = claude_md_path.with_suffix('.md.backup')
                if backup_path.exists():
                    backup_path.rename(claude_md_path)
                    print(f"♻️  Original CLAUDE.md wiederhergestellt")
    
    def create_enhanced_claude_md_context(self) -> Path:
        """Create enhanced CLAUDE.md with Jira ticket context"""
        claude_md_path = self.working_path / "CLAUDE.md"
        
        # Backup existing CLAUDE.md
        if claude_md_path.exists():
            backup_path = claude_md_path.with_suffix('.md.backup')
            claude_md_path.rename(backup_path)
            print(f"📋 Bestehende CLAUDE.md als Backup gespeichert: {backup_path}")
        
        # Create enhanced context
        context_content = f"""# Enhanced PR Review Context mit Jira Integration

## Pull Request Information
"""
        
        if self.pr_url:
            context_content += f"- **PR URL**: {self.pr_url}\n"
        if self.branch_name:
            context_content += f"- **Branch**: {self.branch_name}\n"
        
        context_content += f"""- **Review Timestamp**: {datetime.now().isoformat()}
- **Worktree Path**: {self.working_path}
- **Enhanced Review**: Mit Jira Integration aktiviert

"""
        
        # Add Jira ticket context
        if self.current_ticket:
            ticket = self.current_ticket
            context_content += f"""## 🎫 Jira Ticket Context

- **Ticket ID**: [{ticket.ticket_id}]({self.jira_integration.server_url}/browse/{ticket.ticket_id})
- **Summary**: {ticket.summary}
- **Type**: {ticket.issue_type}
- **Status**: {ticket.status}
- **Priority**: {ticket.priority}
- **Assignee**: {ticket.assignee}

### Ticket Description
{ticket.description}

### 📋 Acceptance Criteria ({len(ticket.acceptance_criteria)} Items)
"""
            
            if ticket.acceptance_criteria:
                for i, criterion in enumerate(ticket.acceptance_criteria, 1):
                    context_content += f"{i}. {criterion}\n"
            else:
                context_content += "❌ Keine Acceptance Criteria definiert\n"
            
            if ticket.labels:
                context_content += f"\n**Labels**: {', '.join(ticket.labels)}\n"
            if ticket.components:
                context_content += f"**Components**: {', '.join(ticket.components)}\n"
        else:
            context_content += """## 🎫 Jira Ticket Context

⚠️  **Kein Jira Ticket verfügbar**
- Führe Standard Code Review durch
- Analysiere Business Requirements aus Code
- Identifiziere mögliche fehlende Requirements

"""
        
        context_content += f"""
## Enhanced Review Instructions

Du führst einen **Enhanced Code Review mit Jira Integration** durch.

### 🎯 Prioritäten (in dieser Reihenfolge):

1. **Acceptance Criteria Compliance** (KRITISCH)
   - Jede AC muss punkt-für-punkt geprüft werden
   - Status: ERFÜLLT / NICHT ERFÜLLT / TEILWEISE ERFÜLLT
   - Konkrete Code-Stellen identifizieren
   - Bei Problemen: konkrete Lösungsvorschläge

2. **Business Logic Correctness**
   - Stimmt die Implementierung mit dem Business Case überein?
   - Wurden alle Edge Cases bedacht?
   - Ist die fachliche Logik korrekt?

3. **Technical Code Quality**
   - Clean Code Principles
   - Security & Performance
   - Test Coverage
   - Documentation

### 🔍 Analyse-Vorgehen:

1. **Verstehe das Ticket**: Was soll implementiert werden?
2. **Analysiere den Code**: Was wurde implementiert?
3. **Vergleiche**: Stimmt Implementation mit Requirements überein?
4. **Identifiziere Gaps**: Was fehlt oder ist falsch?
5. **Stelle Fragen**: Bei Unklarheiten konkrete Fragen stellen

### 📊 Output Format:

```markdown
# Enhanced Code Review Report

## Executive Summary
- AC Compliance Rate: X/Y erfüllt
- Code Quality Score: X/10
- Critical Issues: X
- Must-Fix Items: X

## Acceptance Criteria Analysis
### ✅/❌/⚠️ AC 1: [Description]
**Status:** [ERFÜLLT/NICHT ERFÜLLT/TEILWEISE]
**Implementiert:** [Beschreibung]
**Code-Stelle:** [File:Line]
**Problem:** [Falls zutreffend]
**Empfehlung:** [Konkrete Lösung]

[Repeat for all AC]

## Technical Code Review
[Standard code review findings]

## Questions & Clarifications
1. [Frage an PO/BA/Tech Lead]

## Action Items
### Must-Fix vor Merge:
- [ ] [Critical item]

### Should-Fix:
- [ ] [Important item]

### Nice-to-Have:
- [ ] [Optional improvement]
```

### ❓ Fragen stellen:
Wenn Requirements unklar sind, stelle spezifische Fragen:
- "Was genau bedeutet 'major cities' in AC 2?"
- "Welche Timezone soll für '6 AM' verwendet werden?"
- "Wie soll das System auf API-Failures reagieren?"

### 🛠️ MANDATORY Tool Usage for Comprehensive Analysis:

**Du MUSST diese Tools aktiv verwenden:**

#### 1. **View Tool** (ZWINGEND für jeden Issue)
- **Wann:** Für JEDEN Bug, AC-Check, Code-Quality Issue
- **Wie:** Lies die komplette Datei, nicht nur Ausschnitte
- **Ziel:** Verstehe den vollständigen Kontext um präzise Locations zu geben

#### 2. **Grep Tool** (für Code-Pattern Suche)
- **Wann:** Um ähnliche Probleme in mehreren Dateien zu finden
- **Beispiel:** `grep -n "console.log" **/*.ts` für Debug-Code

#### 3. **Bash Tool** (für zusätzliche Validierung)
- **Git commands:** `git log --oneline -5` für Commit-Kontext
- **File analysis:** `find . -name "*.ts" | wc -l` für Projektgröße

**ANALYSE-WORKFLOW (MANDATORY):**
```
Für jeden Issue:
1. 🔍 View → Datei komplett lesen
2. 📍 Location → Exakte Zeilen identifizieren  
3. 📄 Code → Relevanten Snippet kopieren
4. 🔧 Analysis → Problem & Lösung beschreiben
5. ✅ Verify → Mit Grep ähnliche Issues finden
```

**Beispiel Workflow:**
```
Issue: "Missing null check"
1. View file.ts → Read complete file
2. Identify: Line 45-47 has potential null access
3. Extract: Show the problematic code block
4. Explain: Why it's dangerous, what could happen
5. Solution: Show corrected code with null check
```

**WICHTIG**: Sei sehr detailliert bei AC-Analysis. Jeder Punkt muss explizit adressiert werden.
"""
        
        # Write enhanced CLAUDE.md
        with open(claude_md_path, 'w', encoding='utf-8') as f:
            f.write(context_content)
        
        print(f"📝 Enhanced CLAUDE.md erstellt: {claude_md_path}")
        return claude_md_path
    
    def _create_enhanced_report(self, review_content: str, review_type: str, output_file: str):
        """Create enhanced report with Jira ticket information"""
        
        output_path = self.working_path / output_file
        
        # Git statistics
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        # Report header
        report_content = f"""# Enhanced Code Review Report mit Jira Integration

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Review Type:** {review_type.upper()}  
**Repository:** {self.repo_path}  
**Working Directory:** {self.working_path}  
"""
        
        if self.pr_url:
            report_content += f"**Pull Request:** {self.pr_url}  \n"
        if self.branch_name:
            report_content += f"**Branch:** {self.branch_name}  \n"
        
        # Jira ticket information
        if self.current_ticket:
            ticket = self.current_ticket
            report_content += f"""
---

## 🎫 Jira Ticket Information

- **Ticket ID:** [{ticket.ticket_id}]({self.jira_integration.server_url}/browse/{ticket.ticket_id})
- **Summary:** {ticket.summary}
- **Type:** {ticket.issue_type}
- **Status:** {ticket.status}
- **Priority:** {ticket.priority}
- **Assignee:** {ticket.assignee}

### 📋 Acceptance Criteria ({len(ticket.acceptance_criteria)} Items)
"""
            
            if ticket.acceptance_criteria:
                for i, criterion in enumerate(ticket.acceptance_criteria, 1):
                    report_content += f"{i}. {criterion}\n"
            else:
                report_content += "❌ Keine Acceptance Criteria definiert\n"
            
            if ticket.labels:
                report_content += f"\n**Labels:** {', '.join(ticket.labels)}  \n"
            if ticket.components:
                report_content += f"**Components:** {', '.join(ticket.components)}  \n"
        else:
            report_content += f"""
---

## 🎫 Jira Ticket Information

⚠️  **Kein Jira Ticket verfügbar für diesen Branch**

**Empfehlung:** 
- Verwenden Sie Branch-Namen mit Ticket-ID (z.B. `feature/CMS20-1166-description`)
- Oder erstellen Sie eine `ticket.md` Datei mit Ticket-Informationen
- Oder konfigurieren Sie Jira API Zugang
"""
        
        # Change summary
        report_content += f"""
---

## 📊 Change Summary

```
{diff_summary}
```

**Geänderte Dateien:** {len(changed_files)}

"""
        
        if isinstance(changed_files, list) and changed_files:
            for file in changed_files:
                report_content += f"- `{file}`\n"
        
        # Review results
        report_content += f"""
---

## 🎯 Enhanced Review Results

{review_content}

---

## 📈 Report Information

- **Generated by:** Enhanced Claude Code PR Reviewer
- **Claude Code Version:** {self._get_claude_version()}
- **Jira Integration:** {'✅ Enabled' if self.jira_integration.enabled else '❌ Disabled'}
- **Ticket Loaded:** {'✅ Yes' if self.current_ticket else '❌ No'}
- **AC Analysis:** {'✅ Included' if self.current_ticket and self.current_ticket.acceptance_criteria else '❌ Not Available'}
- **Working Directory:** `{self.working_path}`

---

## 🔧 Next Steps

### If AC Issues Found:
1. **Address Critical AC Failures** before merging
2. **Clarify Requirements** with Product Owner if needed
3. **Update Implementation** to match AC
4. **Re-run Review** after fixes

### Standard Process:
1. Review and address feedback
2. Update tests as needed
3. Update documentation
4. Request re-review if major changes

---

*Dieser Enhanced Review Report wurde automatisch mit Jira Integration generiert. Bei Fragen zu Requirements kontaktieren Sie den Product Owner oder Business Analyst.*
"""
        
        # Write report
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📊 Enhanced Review Report erstellt: {output_path}")
        print(f"🎫 Jira Integration: {'✅ Aktiv' if self.current_ticket else '❌ Inaktiv'}")
        if self.current_ticket:
            print(f"📋 AC Analysis: {self.current_ticket.get_acceptance_criteria_count()} Kriterien überprüft")
        
        return output_path
    
    def quick_enhanced_review(self, focus_area=None, show_progress=True) -> str:
        """Quick enhanced review with AC focus"""
        
        if self.current_ticket and self.current_ticket.acceptance_criteria:
            # AC-focused quick review
            review_type = "acceptance_criteria_focused"
            print(f"⚡ Quick Enhanced Review: AC-Focused")
        elif focus_area:
            # Standard quick review with enhancement
            review_type = focus_area
            print(f"⚡ Quick Enhanced Review: {focus_area.upper()}")
        else:
            # Standard quick review
            review_type = "comprehensive"
            print(f"⚡ Quick Enhanced Review: Standard")
        
        try:
            prompt = self.generate_enhanced_review_prompt(review_type)
            
            # Add explicit instruction to avoid writing tools for quick review
            prompt += """

⚠️  QUICK REVIEW ANWEISUNG:
- KEINE Dateien erstellen (TodoWrite, Edit, etc.)
- Direkte Markdown-Antwort geben
- Kurz und präzise bleiben
- Maximal 300 Wörter
"""
            
            if show_progress:
                result = self._run_claude_command_with_progress(
                    prompt, 
                    timeout=180,  # 3 minutes
                    max_turns=15   # Increased for AC-focused review
                )
            else:
                result = self._run_claude_command(prompt, output_format="text", timeout=180)
            
            # Ensure result is a string for the report
            if isinstance(result, dict):
                # If result is a dict (from JSON response), convert to string
                result_str = json.dumps(result, indent=2) if result else ""
            elif result is None:
                result_str = ""
            else:
                result_str = str(result)
            
            # Create quick report
            output_file = "quick_enhanced_review_report.md"
            self._create_enhanced_report(result_str, f"quick_{review_type}", output_file)
            
            return result_str
            
        except Exception as e:
            print(f"❌ Quick Enhanced Review fehlgeschlagen: {e}")
            return ""  # Return empty string instead of None to match return type annotation
    
    def perform_summary_phase(self, review_result: str, review_type: str) -> str:
        """
        Perform Phase 3: Summary & Explanation - Create comprehensive tutorial-style documentation
        Uses the SummaryAnalyzer module for better maintainability
        """
        # Initialize the Summary Analyzer
        summary_analyzer = SummaryAnalyzer(
            working_path=Path(self.working_path),
            branch_name=self.branch_name,
            repo_path=Path(self.repo_path),
            pr_url=self.pr_url,
            current_ticket=self.current_ticket,
            jira_integration=self.jira_integration
        )
        
        # Delegate to the SummaryAnalyzer
        return summary_analyzer.perform_summary_phase(
            review_result=review_result,
            review_type=review_type,
            run_claude_command_func=self._run_claude_command_with_progress,
            get_changed_files_func=self._get_changed_files_list,
            get_git_diff_summary_func=self._get_git_diff_summary,
            generate_ticket_context_func=self._generate_ticket_context,
            get_claude_version_func=self._get_claude_version
        )
    
    def cleanup_worktree(self):
        """Clean up the worktree created by PR analyzer"""
        if self.pr_analyzer and hasattr(self.pr_analyzer, 'worktree_path') and self.pr_analyzer.worktree_path:
            try:
                print(f"🧹 Räume Worktree auf: {self.pr_analyzer.worktree_path}")
                self.pr_analyzer._cleanup_worktree()
            except Exception as e:
                print(f"⚠️  Worktree Cleanup fehlgeschlagen: {e}")


def load_config_file(config_path: str) -> Dict:
    """Load configuration from JSON file"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ Config geladen: {config_path}")
        return config
        
    except Exception as e:
        print(f"❌ Fehler beim Laden der Config: {e}")
        raise


def main():
    """CLI interface for Enhanced Claude Reviewer"""
    parser = argparse.ArgumentParser(
        description='Enhanced Claude Code PR Reviewer mit Jira Integration',
        epilog="""
Beispiele:
  # Enhanced Review mit Config
  python enhanced_claude_reviewer.py --config pr_review_config.json

  # Quick Enhanced Review
  python enhanced_claude_reviewer.py --config config.json --quick

  # Spezifisches Ticket
  python enhanced_claude_reviewer.py --config config.json --jira-ticket CMS20-1166

  # Nur AC Analysis
  python enhanced_claude_reviewer.py --config config.json --ac-only
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Config file (required)
    parser.add_argument('--config', '-c', required=True,
                       help='Pfad zur JSON-Konfigurationsdatei')
    
    # Review mode options
    parser.add_argument('--quick', '-q', action='store_true',
                       help='Schnelles Enhanced Review (3-5 Minuten)')
    
    parser.add_argument('--ac-only', action='store_true',
                       help='Nur Acceptance Criteria Analysis')
    
    parser.add_argument('--business-context', action='store_true',
                       help='Business Context Discovery Mode')
    
    parser.add_argument('--bug-analysis', action='store_true',
                       help='Detaillierte Bug Detection & Code Quality Analysis')
    
    parser.add_argument('--with-summary', action='store_true',
                       help='Include Phase 3: Summary & Explanation (tutorial-style documentation)')
    
    parser.add_argument('--summary-only', action='store_true',
                       help='Only run Phase 3: Summary & Explanation (skip review phases)')
    
    # Ticket options
    parser.add_argument('--jira-ticket', '-t',
                       help='Spezifische Jira Ticket ID laden')
    
    parser.add_argument('--branch', '-b',
                       help='Branch Name (überschreibt Config)')
    
    # Output options
    parser.add_argument('--output', '-o', default='enhanced_review_report.md',
                       help='Output-Datei (Standard: enhanced_review_report.md)')
    
    parser.add_argument('--no-context', action='store_true',
                       help='Keine enhanced CLAUDE.md erstellen')
    
    parser.add_argument('--no-progress', action='store_true',
                       help='Live-Progress deaktivieren')
    
    # Testing options
    parser.add_argument('--test-jira', action='store_true',
                       help='Teste Jira-Verbindung und exit')
    
    parser.add_argument('--validate-config', action='store_true',
                       help='Validiere Konfiguration und exit')
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = load_config_file(args.config)
        
        # Test Jira connection if requested
        if args.test_jira:
            jira = JiraIntegration(config)
            validation = jira.validate_config()
            print(f"\n📊 Jira Validation Results:")
            for key, value in validation.items():
                status = "✅" if value else "❌"
                print(f"   {status} {key}")
            return
        
        # Validate config if requested
        if args.validate_config:
            # Basic config validation
            required_sections = ['pr_config', 'review_config']
            missing = [section for section in required_sections if section not in config]
            if missing:
                print(f"❌ Fehlende Config-Abschnitte: {missing}")
                return
            else:
                print("✅ Basis-Konfiguration valide")
            
            # Test Jira if enabled
            if config.get('jira_config', {}).get('enabled', False):
                jira = JiraIntegration(config)
                jira.validate_config()
            
            return
        
        # Extract parameters from config
        pr_config = config.get('pr_config', {})
        worktree_path = pr_config.get('worktree_path')
        repo_path = pr_config.get('repository', {}).get('path', '.')
        pr_url = pr_config.get('source', {}).get('pr_url')
        branch_name = args.branch or pr_config.get('source', {}).get('value')
        
        # Initialize enhanced reviewer
        reviewer = EnhancedClaudeReviewer(
            config=config,
            worktree_path=worktree_path,
            repo_path=repo_path,
            pr_url=pr_url,
            branch_name=branch_name
        )
        
        # Set specific ticket if requested
        if args.jira_ticket:
            if not reviewer.set_ticket(args.jira_ticket):
                print(f"❌ Konnte Ticket {args.jira_ticket} nicht laden")
                return
        
        # Determine review type
        if args.ac_only:
            review_type = "acceptance_criteria_focused"
        elif args.business_context:
            review_type = "business_context_discovery"
        elif args.bug_analysis:
            review_type = "bug_and_quality_analysis"
        else:
            review_type = "comprehensive_with_ac"
        
        # Handle summary-only mode
        if args.summary_only:
            print("🎯 Running Phase 3: Summary & Explanation Only...")
            try:
                # Generate dummy review result for summary-only mode
                dummy_review = "Phase 1 & 2 skipped - Running summary-only mode based on code changes."
                summary_result = reviewer.perform_summary_phase(dummy_review, review_type)
                print("✅ Summary-only mode completed!")
                return
            except Exception as e:
                print(f"❌ Summary-only mode failed: {e}")
                return
        
        # Perform review
        if args.quick:
            reviewer.quick_enhanced_review(
                focus_area=review_type if args.ac_only else None,
                show_progress=not args.no_progress
            )
        else:
            reviewer.perform_enhanced_review(
                review_type=review_type,
                output_file=args.output,
                include_context=not args.no_context,
                show_progress=not args.no_progress,
                include_summary=args.with_summary
            )
        
    except Exception as e:
        print(f"❌ Enhanced Review Fehler: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()