#!/usr/bin/env python3
"""
Acceptance Criteria Service
Handles extraction and generation of acceptance criteria for Jira tickets using Claude AI.
Ported from the old system that provided perfect AC generation.
"""

import logging
import subprocess
import json
import re
from typing import List, Dict, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class AcceptanceCriteriaResult:
    """Result of acceptance criteria extraction/generation"""
    criteria: List[str]
    source: str  # 'existing' or 'generated'
    ticket_id: str
    method: str  # 'custom_fields', 'description_parsing', 'claude_generation', 'fallback'
    

class AcceptanceCriteriaService:
    """Service for extracting and generating acceptance criteria from Jira tickets"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_or_generate_acceptance_criteria(
        self, 
        ticket_key: str, 
        summary: str, 
        description_text: str, 
        custom_fields: Dict
    ) -> AcceptanceCriteriaResult:
        """
        Extract existing acceptance criteria or generate them using Claude AI.
        This is the main method that replicates the old system's perfect functionality.
        """
        try:
            self.logger.info(f"🎯 Processing AC for {ticket_key}...")
            
            # First try to extract existing AC from text or custom fields
            existing_ac = self._extract_existing_acceptance_criteria(description_text, custom_fields)
            
            if existing_ac and len(existing_ac) > 0:
                self.logger.info(f"✅ EXTRACTED {len(existing_ac)} existing AC from {ticket_key} - NO GENERATION NEEDED")
                for i, ac in enumerate(existing_ac, 1):
                    self.logger.info(f"   AC{i}: {ac[:60]}...")
                
                return AcceptanceCriteriaResult(
                    criteria=existing_ac,
                    source='existing',
                    ticket_id=ticket_key,
                    method='custom_fields' if self._found_in_custom_fields(custom_fields) else 'description_parsing'
                )
            
            # If no AC found, generate them using Claude AI (the key missing feature!)
            self.logger.warning(f"⚠️ NO existing AC found in {ticket_key} - GENERATING with Claude AI...")
            generated_ac = self._generate_acceptance_criteria_with_claude(ticket_key, summary, description_text)
            
            self.logger.info(f"🤖 GENERATED {len(generated_ac)} AC for {ticket_key}")
            for i, ac in enumerate(generated_ac, 1):
                self.logger.info(f"   Generated AC{i}: {ac[:60]}...")
            
            return AcceptanceCriteriaResult(
                criteria=generated_ac,
                source='generated',
                ticket_id=ticket_key,
                method='claude_generation'
            )
            
        except Exception as e:
            self.logger.error(f"❌ Error processing AC for {ticket_key}: {e}")
            # Fallback to basic AC generation
            fallback_ac = self._generate_fallback_acceptance_criteria(summary, description_text)
            self.logger.warning(f"🔧 FALLBACK: Generated {len(fallback_ac)} basic AC for {ticket_key}")
            
            return AcceptanceCriteriaResult(
                criteria=fallback_ac,
                source='generated',
                ticket_id=ticket_key,
                method='fallback'
            )
    
    def _extract_existing_acceptance_criteria(self, description_text: str, custom_fields: Dict) -> List[str]:
        """Extract existing acceptance criteria from text or custom fields"""
        criteria = []
        
        # Debug logging
        self.logger.info(f"🔍 AC Extraction Debug:")
        self.logger.info(f"   Description length: {len(description_text) if description_text else 0}")
        self.logger.info(f"   Description preview: {description_text[:200] if description_text else 'None'}...")
        self.logger.info(f"   Custom fields count: {len(custom_fields)}")
        
        # Check custom fields first (same logic as old system)
        for field_key, field_value in custom_fields.items():
            if field_key.startswith('customfield_') and field_value:
                if isinstance(field_value, str) and any(keyword in field_value.lower() 
                    for keyword in ['akzeptanz', 'acceptance', 'criteria', 'ac:']):
                    self.logger.info(f"   Found AC in custom field {field_key}")
                    lines = field_value.split('\n')
                    for line in lines:
                        clean_line = line.strip()
                        if len(clean_line) > 10:
                            criteria.append(clean_line)
        
        # If found in custom fields, return those
        if criteria:
            self.logger.info(f"✅ Found {len(criteria)} AC in custom fields")
            return criteria
        
        # Look for AC section in description (enhanced logic)
        if not description_text:
            self.logger.info("❌ No description text available")
            return []
            
        lines = description_text.split('\n')
        in_ac_section = False
        section_found = False
        
        # Enhanced AC section detection patterns (from old system + improved)
        ac_patterns = [
            'akzeptanzkriterien:',
            'akzeptanzkriterien',
            'acceptance criteria:',
            'acceptance criteria', 
            'akzeptanz-kriterien:',
            'akzeptanz-kriterien',
            'ac:',
            'kriterien:'
        ]
        
        for i, line in enumerate(lines):
            line_lower = line.strip().lower()
            original_line = line.strip()
            
            # Check if we found an AC section header
            for pattern in ac_patterns:
                if pattern in line_lower:
                    in_ac_section = True
                    section_found = True
                    self.logger.info(f"📋 Found AC section at line {i+1}: '{original_line[:50]}...'")
                    continue
                    
            # End of AC section detection (improved)
            if in_ac_section and line_lower and any(section_header in line_lower for section_header in [
                'input:', 'qa:', 'notes:', 'description:', 'background:', 'context:', 
                'implementation:', 'technical details:', 'details:', 'benutzer:', 'user:'
            ]):
                self.logger.info(f"📋 AC section ended at line {i+1}: '{original_line[:50]}...'")
                break
                
            # Extract criteria lines (enhanced patterns)
            if in_ac_section and original_line:
                # More flexible bullet point detection
                if (original_line.startswith(('•', '-', '*', '◦', '→', '▪', '▫')) or
                    original_line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.')) or
                    ((':' in original_line or 'der ' in original_line.lower() or 'ein ' in original_line.lower()) 
                     and len(original_line) > 15)):
                    
                    # Clean the line but preserve German characters
                    clean_line = original_line.strip()
                    
                    # Skip empty lines or section headers
                    if len(clean_line) > 10 and not any(skip in clean_line.lower() for skip in [
                        'akzeptanzkriterien', 'acceptance criteria', 'implementation', 'technical'
                    ]):
                        criteria.append(clean_line)
                        self.logger.info(f"   ✓ Added AC: '{clean_line[:50]}...'")
        
        if section_found and not criteria:
            self.logger.warning("⚠️ AC section found but no criteria extracted - might be parsing issue")
        elif not section_found:
            self.logger.info("❌ No AC section found in description")
            
        self.logger.info(f"📋 Final AC count: {len(criteria)}")
        return criteria
    
    def _generate_acceptance_criteria_with_claude(self, ticket_key: str, summary: str, description_text: str) -> List[str]:
        """
        Use Claude AI to generate acceptance criteria from ticket content.
        This uses the exact same German prompts that worked perfectly in the old system.
        """
        try:
            # Create prompt for Claude (exact German prompt from old system that worked perfectly)
            prompt = f"""Du bist ein Experte für Software-Entwicklung und Requirement Engineering. Analysiere dieses Jira Ticket und erstelle spezifische, testbare Akzeptanzkriterien.

**Ticket:** {ticket_key}
**Zusammenfassung:** {summary}

**Beschreibung:**
{description_text}

**Aufgabe:**
Erstelle 3-5 konkrete, messbare Akzeptanzkriterien für dieses Ticket. Jedes Kriterium soll:
- Spezifisch und testbar sein
- Den Nutzen für den Entwickler/Benutzer beschreiben
- Technische Implementierungsdetails berücksichtigen
- In deutscher Sprache verfasst sein

**WICHTIG:** Antworte NUR mit einem JSON Array aus Strings. Keine zusätzlichen Erklärungen oder Text!

**Erwartetes Format:**
["Kriterium 1: Beschreibung des ersten testbaren Ergebnisses", "Kriterium 2: Beschreibung des zweiten testbaren Ergebnisses"]

**Antwort:**"""

            # Call Claude Code CLI directly with the prompt (same as old system)
            result = subprocess.run([
                'claude'
            ], input=prompt, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                response_text = result.stdout.strip()
                self.logger.info(f"🔍 Claude raw response for {ticket_key}: {response_text[:200]}...")
                
                # Try to parse as JSON (same parsing logic as old system)
                try:
                    criteria = json.loads(response_text)
                    if isinstance(criteria, list) and all(isinstance(item, str) for item in criteria):
                        self.logger.info(f"✅ Generated {len(criteria)} AC for {ticket_key}")
                        return criteria
                except json.JSONDecodeError:
                    # If not valid JSON, try to extract from text (same fallback as old system)
                    self.logger.info(f"⚠️ Claude response not JSON, parsing manually for {ticket_key}")
                    parsed_criteria = self._parse_ac_from_text_response(response_text)
                    self.logger.info(f"🔧 Manually parsed {len(parsed_criteria)} AC: {parsed_criteria}")
                    return parsed_criteria
            else:
                self.logger.error(f"❌ Claude CLI error for {ticket_key}: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ Error generating AC for {ticket_key}: {e}")
        
        # Fallback: generate basic AC from summary (same fallback as old system)
        return self._generate_fallback_acceptance_criteria(summary, description_text)
    
    def _parse_ac_from_text_response(self, response_text: str) -> List[str]:
        """Parse acceptance criteria from Claude's text response (same logic as old system)"""
        criteria = []
        
        # Try to find JSON-like content
        json_match = re.search(r'\\[([^\\]]+)\\]', response_text, re.DOTALL)
        if json_match:
            json_content = json_match.group(0)
            try:
                parsed = json.loads(json_content)
                if isinstance(parsed, list):
                    return [str(item).strip() for item in parsed if str(item).strip()]
            except:
                pass
        
        # Fallback: parse line by line (same logic as old system)
        lines = response_text.split('\\n')
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith(('**', '#', 'Here', 'Based', 'I ', 'Die ', 'Das ', 'Als ')):
                # Remove common prefixes and quotes
                line = line.lstrip('- * • ◦ 1. 2. 3. 4. 5. ').strip()
                line = line.strip('"').strip("'")
                
                # Only accept lines that look like acceptance criteria
                if len(line) > 15 and (':' in line or any(keyword in line.lower() for keyword in [
                    'service', 'api', 'daten', 'implementier', 'bereitgestellt', 'funktionier'
                ])):
                    criteria.append(line)
        
        return criteria[:5]  # Limit to 5 criteria
    
    def _generate_fallback_acceptance_criteria(self, summary: str, description_text: str) -> List[str]:
        """Generate basic acceptance criteria as fallback (same logic as old system)"""
        criteria = []
        
        if 'service' in summary.lower():
            criteria.append("Service-Bereitstellung: Der Service ist erfolgreich bereitgestellt und erreichbar")
            criteria.append("Funktionalität: Alle beschriebenen Funktionen sind implementiert und funktionsfähig")
            
        if 'api' in summary.lower():
            criteria.append("API-Integration: Die API-Verbindung funktioniert korrekt")
            criteria.append("Datenverarbeitung: Daten werden korrekt verarbeitet und zurückgegeben")
            
        if not criteria:
            criteria.append("Implementierung: Die beschriebene Funktionalität ist vollständig implementiert")
            criteria.append("Tests: Alle relevanten Tests sind erstellt und bestehen")
            criteria.append("Dokumentation: Die Implementierung ist ausreichend dokumentiert")
        
        return criteria
    
    def _found_in_custom_fields(self, custom_fields: Dict) -> bool:
        """Check if AC were found in custom fields"""
        for field_key, field_value in custom_fields.items():
            if field_key.startswith('customfield_') and field_value:
                if isinstance(field_value, str) and any(keyword in field_value.lower() 
                    for keyword in ['akzeptanz', 'acceptance', 'criteria', 'ac:']):
                    return True
        return False
    
    def extract_existing_only(self, description_text: str, custom_fields: Dict) -> List[str]:
        """
        Extract only existing AC without generating new ones.
        Used for performance in ticket lists where we don't want to call Claude for every ticket.
        """
        return self._extract_existing_acceptance_criteria(description_text, custom_fields)