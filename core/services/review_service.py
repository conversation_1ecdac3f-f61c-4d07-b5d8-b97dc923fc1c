"""Review session management service with native Claude Code SDK support"""

import asyncio
import threading
import uuid
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Optional, Callable, List
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
try:
    from ..models.review_session import ReviewSession
    from ..enhanced_claude_reviewer import <PERSON>hancedClaudeReviewer
    from ..enhanced_claude_reviewer_sdk import EnhancedClaudeReviewerSDK
except ImportError:
    from models.review_session import ReviewSession
    from enhanced_claude_reviewer import Enhanced<PERSON>laudeReviewer
    from enhanced_claude_reviewer_sdk import EnhancedClaudeReviewerSDK

logger = logging.getLogger(__name__)


class ReviewService:
    """Service for managing code review sessions"""
    
    def __init__(self):
        self.review_sessions_lock = threading.Lock()
        self.review_sessions: Dict[str, ReviewSession] = {}
        self.active_threads: Dict[str, threading.Thread] = {}
    
    def create_session(self, config: Dict, branch_name: str, pr_url: Optional[str] = None, 
                      jira_ticket_data: Optional[Dict] = None) -> ReviewSession:
        """Create a new review session"""
        session_id = str(uuid.uuid4())
        
        # Create review session
        review_session = ReviewSession(session_id, config, branch_name, pr_url)
        
        # Store Jira ticket data if provided
        if jira_ticket_data:
            review_session.jira_ticket_data = jira_ticket_data
        
        with self.review_sessions_lock:
            self.review_sessions[session_id] = review_session
        
        logger.info(f"Created review session {session_id} for branch {branch_name}")
        return review_session
    
    def get_session(self, session_id: str) -> Optional[ReviewSession]:
        """Get a review session by ID"""
        with self.review_sessions_lock:
            return self.review_sessions.get(session_id)
    
    def list_sessions(self) -> Dict:
        """List all review sessions"""
        with self.review_sessions_lock:
            sessions = [session.to_dict() for session in self.review_sessions.values()]
        return {
            'sessions': sessions,
            'total': len(sessions)
        }
    
    def get_session_count(self) -> int:
        """Get total number of active sessions"""
        with self.review_sessions_lock:
            return len(self.review_sessions)
    
    def start_review_thread(self, session: ReviewSession, review_executor):
        """Start a review in a background thread"""
        review_thread = threading.Thread(
            target=review_executor,
            args=(session,),
            daemon=True
        )
        self.active_threads[session.session_id] = review_thread
        review_thread.start()
        return review_thread
    
    def cleanup_thread(self, session_id: str):
        """Clean up thread reference after completion"""
        with self.review_sessions_lock:
            if session_id in self.active_threads:
                del self.active_threads[session_id]
    
    def get_focus_areas_for_mode(self, review_mode: str) -> list:
        """Get focus areas based on review mode"""
        focus_areas_map = {
            'quick': ['code_quality', 'security'],
            'comprehensive': ['acceptance_criteria_compliance', 'code_quality', 'security', 'testing'],
            'ac-only': ['acceptance_criteria_compliance'],
            'bug-analysis': ['code_quality', 'testing', 'error_handling'],
            'security': ['security', 'input_validation', 'authentication'],
            'performance': ['performance', 'scalability', 'optimization']
        }
        
        return focus_areas_map.get(review_mode, ['code_quality', 'security'])
    
    def create_enhanced_reviewer(self, session: 'ReviewSession') -> EnhancedClaudeReviewer:
        """Create an EnhancedClaudeReviewer instance for the session"""
        try:
            # Extract repo path from config
            repo_path = session.config.get('pr_config', {}).get('repository', {}).get('path', '.')
            
            # Create enhanced reviewer with session config
            reviewer = EnhancedClaudeReviewer(
                config=session.config,
                branch_name=session.branch_name,
                pr_url=session.pr_url,
                repo_path=repo_path
            )
            
            # Process Jira ticket data if available
            if session.jira_ticket_data:
                reviewer = self._process_jira_ticket_data(reviewer, session)
            
            logger.info(f"✅ Enhanced reviewer created for session {session.session_id}")
            return reviewer
            
        except Exception as e:
            logger.error(f"❌ Failed to create enhanced reviewer: {e}")
            raise
    
    def _process_jira_ticket_data(self, reviewer: EnhancedClaudeReviewer, session: 'ReviewSession') -> EnhancedClaudeReviewer:
        """Process frontend Jira ticket data and integrate with reviewer"""
        try:
            jira_ticket_data = session.jira_ticket_data
            logger.info(f"🎫 Processing Jira ticket: {jira_ticket_data.get('ticket_id')}")
            
            # Create raw ticket text from frontend data
            ticket_summary = jira_ticket_data.get('summary', '')
            ticket_description = jira_ticket_data.get('description', '')
            
            # Construct natural text format
            raw_ticket_text = f"{ticket_summary}\n\n{ticket_description}".strip()
            
            # Get repo path from session config
            repo_path = session.config.get('pr_config', {}).get('repository', {}).get('path', '.')
            
            # Convert using ticket_converter if available
            formatted_ticket_md = self._convert_ticket_with_ai(
                raw_ticket_text, 
                jira_ticket_data.get('ticket_id'),
                repo_path  # Use repo path from frontend configuration
            )
            
            # Write the formatted ticket to a temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as tmp:
                tmp.write(formatted_ticket_md)
                temp_ticket_file = tmp.name
            
            # Update config to use the formatted ticket file
            session.config['jira_config']['ticket_extraction']['manual_ticket_file'] = temp_ticket_file
            session.config['jira_config']['enabled'] = False  # Disable API, use file
            
            logger.info(f"✅ Jira ticket processed and saved to: {temp_ticket_file}")
            
            # Force reload the ticket in the reviewer
            if hasattr(reviewer, 'jira_integration'):
                ticket_id = jira_ticket_data.get('ticket_id')
                if ticket_id:
                    reviewer.current_ticket = reviewer.jira_integration.get_ticket(ticket_id)
            
            return reviewer
            
        except Exception as e:
            logger.error(f"❌ Error processing Jira ticket data: {e}")
            return reviewer
    
    def _convert_ticket_with_ai(self, raw_text: str, ticket_id: str, repo_path: str) -> str:
        """Convert ticket text using AI ticket converter if available"""
        try:
            # Try to import and use ticket_converter
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'utils'))
            from ticket_converter import AITicketConverter  # type: ignore
            
            converter = AITicketConverter(repo_path=repo_path)
            formatted_ticket_md = converter.convert_ticket_text(
                raw_text=raw_text,
                ticket_id=ticket_id,
                analyze_codebase=True
            )
            
            return formatted_ticket_md
            
        except ImportError:
            logger.warning("ticket_converter module not available - using basic formatting")
            return f"# Ticket: {ticket_id}\n\n## Summary\n{raw_text}"
        except Exception as e:
            logger.error(f"Error in ticket conversion: {e}")
            return f"# Ticket: {ticket_id}\n\n## Summary\n{raw_text}"
    
    def execute_review(self, session: 'ReviewSession', progress_callback=None) -> str:
        """Execute the actual code review using EnhancedClaudeReviewer"""
        try:
            session.status = 'running'
            session.progress = 10
            
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Initializing enhanced reviewer...'
                })
            
            # Create enhanced reviewer
            reviewer = self.create_enhanced_reviewer(session)
            session.reviewer = reviewer
            session.progress = 25
            
            # Update worktree path if created
            if hasattr(reviewer, 'pr_analyzer') and reviewer.pr_analyzer and reviewer.pr_analyzer.worktree_path:
                session.worktree_path = str(reviewer.pr_analyzer.worktree_path)
            
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': f'Worktree setup completed: {session.worktree_path or "using main repository"}',
                    'worktree_path': session.worktree_path
                })
            
            # Perform enhanced review based on mode
            session.progress = 50
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Starting enhanced code review with Claude...'
                })
            
            review_mode = session.config.get('review_config', {}).get('type', 'comprehensive')
            
            if review_mode == 'ac-only':
                results = self._perform_ac_only_review(reviewer, session, progress_callback)
            elif review_mode == 'bug-analysis':
                results = self._perform_bug_analysis_review(reviewer, session, progress_callback)
            else:
                results = self._perform_comprehensive_review(reviewer, session, progress_callback)
            
            session.progress = 90
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Processing review results...'
                })
            
            # Structure results for UI consumption
            session.results = self._structure_review_results(results, reviewer, session)
            session.progress = 100
            session.status = 'completed'
            session.completed_at = datetime.now()
            
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Review completed successfully!',
                    'results_available': True
                })
            
            logger.info(f"Review session {session.session_id} completed successfully")
            return results
            
        except Exception as e:
            session.status = 'error'
            session.error = str(e)
            logger.error(f"Review session {session.session_id} failed: {e}")
            
            if progress_callback:
                progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'error': str(e),
                    'message': f'Review failed: {str(e)}'
                })
            raise
    
    def _perform_comprehensive_review(self, reviewer: EnhancedClaudeReviewer, session: 'ReviewSession', progress_callback=None) -> str:
        """Perform comprehensive review with all focus areas"""
        try:
            return reviewer._run_claude_command_with_progress(
                reviewer.generate_enhanced_review_prompt('comprehensive'),
                timeout=900,
                max_turns=25,
                websocket_callback=progress_callback
            )
        except Exception as e:
            logger.error(f"Comprehensive review failed: {e}")
            raise
    
    def _perform_ac_only_review(self, reviewer: EnhancedClaudeReviewer, session: 'ReviewSession', progress_callback=None) -> str:
        """Perform acceptance criteria only review"""
        try:
            prompt = reviewer.generate_enhanced_review_prompt('comprehensive', include_business_context=True)
            # Add AC-focused instruction
            ac_prompt = prompt + "\n\nFOKUS: Analysiere ausschließlich die Acceptance Criteria Compliance. Ignoriere Code-Qualität und Security Issues."
            
            return reviewer._run_claude_command_with_progress(
                ac_prompt,
                timeout=600,
                max_turns=15,
                websocket_callback=progress_callback
            )
        except Exception as e:
            logger.error(f"AC-only review failed: {e}")
            raise
    
    def _perform_bug_analysis_review(self, reviewer: EnhancedClaudeReviewer, session: 'ReviewSession', progress_callback=None) -> str:
        """Perform bug analysis focused review"""
        try:
            return reviewer._run_claude_command_with_progress(
                reviewer.generate_review_prompt('bugs'),
                timeout=600,
                max_turns=20,
                websocket_callback=progress_callback
            )
        except Exception as e:
            logger.error(f"Bug analysis review failed: {e}")
            raise
    
    def _structure_review_results(self, raw_results: str, reviewer: EnhancedClaudeReviewer, session: 'ReviewSession') -> Dict:
        """Structure raw review results for UI consumption"""
        try:
            # Get additional context
            try:
                changed_files = reviewer._get_changed_files_list() if hasattr(reviewer, '_get_changed_files_list') else []
            except Exception as e:
                logger.error(f"Error getting changed files list: {e}")
                changed_files = []
            
            try:
                diff_summary = reviewer._get_git_diff_summary() if hasattr(reviewer, '_get_git_diff_summary') else ""
            except Exception as e:
                logger.error(f"Error getting git diff summary: {e}")
                diff_summary = ""
            
            # Get Jira ticket info if available
            ticket_info = None
            if hasattr(reviewer, 'current_ticket') and reviewer.current_ticket:
                ticket_info = {
                    'ticket_id': reviewer.current_ticket.ticket_id,
                    'summary': reviewer.current_ticket.summary,
                    'status': reviewer.current_ticket.status,
                    'acceptance_criteria_count': reviewer.current_ticket.get_acceptance_criteria_count(),
                    'acceptance_criteria': getattr(reviewer.current_ticket, 'acceptance_criteria', [])
                }
            
            result = {
                'session_id': session.session_id,
                'review_mode': session.config.get('review_config', {}).get('type', 'comprehensive'),
                'branch_name': session.branch_name,
                'pr_url': session.pr_url,
                'worktree_path': session.worktree_path,
                'timestamp': datetime.now().isoformat(),
                
                # Raw review content
                'raw_review': raw_results,
                
                # Structured metadata
                'metadata': {
                    'changed_files': changed_files,
                    'diff_summary': diff_summary,
                    'file_count': len(changed_files) if changed_files else 0
                },
                
                # Jira integration
                'jira_ticket': ticket_info,
            }
            
            # Parse structured findings from Claude output
            from utils.text_processing import TextProcessor
            structured_findings = TextProcessor.parse_structured_findings(raw_results)
            result['structured_findings'] = structured_findings
            
            # Generate summary stats
            result['summary'] = self._generate_review_summary(raw_results, changed_files, structured_findings)
            
            return result
            
        except Exception as e:
            logger.error(f"Error structuring results: {e}")
            return {
                'error': f'Failed to structure results: {str(e)}',
                'raw_review': raw_results
            }
    
    def _generate_review_summary(self, raw_results: str, changed_files: list, structured_findings: Dict) -> Dict:
        """Generate summary statistics from review results"""
        total_findings = sum(len(findings) for findings in structured_findings.values())
        high_severity = sum(1 for finding in [f for findings in structured_findings.values() for f in findings] 
                           if finding.get('severity', 'low').lower() in ['high', 'critical'])
        
        return {
            'total_findings': total_findings,
            'high_severity_count': high_severity,
            'files_changed': len(changed_files) if changed_files else 0,
            'review_length': len(raw_results),
            'categories': {k: len(v) for k, v in structured_findings.items()},
            'completion_status': 'completed'
        }
    
    # ========================================
    # NEW: Native Claude Code SDK Methods
    # ========================================
    
    async def execute_structured_review_async(
        self, 
        session: 'ReviewSession', 
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """Execute review using native Claude Code SDK with structured output"""
        
        try:
            session.status = 'running'
            session.progress = 10
            
            if progress_callback:
                await progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Initializing enhanced reviewer with native SDK...',
                    'type': 'review_progress'
                })
            
            # Create SDK-based reviewer
            reviewer = self._create_sdk_reviewer(session)
            session.reviewer = reviewer
            session.progress = 25
            
            # Update worktree path if created
            if hasattr(reviewer, 'worktree_path') and reviewer.worktree_path:
                session.worktree_path = str(reviewer.worktree_path)
            
            if progress_callback:
                await progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': f'Worktree setup completed: {session.worktree_path or "using main repository"}',
                    'worktree_path': session.worktree_path,
                    'type': 'review_progress'
                })
            
            # Determine review type and include_summary
            review_config = session.config.get('review_config', {})
            review_type = review_config.get('type', 'comprehensive')
            include_summary = review_config.get('include_summary', False)
            
            # Map review types to SDK types
            sdk_review_type = self._map_review_type_to_sdk(review_type)
            
            session.progress = 50
            if progress_callback:
                await progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': f'Starting {sdk_review_type} review with Claude Code SDK...',
                    'type': 'review_progress'
                })
            
            # Execute the review with streaming progress
            async def sdk_progress_callback(*args, **kwargs):
                """Forward SDK progress to main progress callback"""
                if progress_callback:
                    # Handle multiple argument signatures from SDK
                    if len(args) == 3:
                        # SDK calling with (event_type, message, data)
                        event_type, message, data = args
                        if isinstance(data, dict):
                            enhanced_data = {**data}
                        else:
                            enhanced_data = {}
                        enhanced_data.update({
                            'type': event_type,
                            'message': message
                        })
                    elif len(args) == 1:
                        # Single data argument
                        data = args[0]
                        if isinstance(data, dict):
                            enhanced_data = {**data}
                        else:
                            enhanced_data = {'message': str(data)}
                    else:
                        # Fallback
                        enhanced_data = kwargs.get('data', {})
                    
                    # Add session context to SDK callbacks
                    enhanced_data.update({
                        'session_id': session.session_id,
                        'status': session.status
                    })
                    
                    # Update session progress based on SDK events
                    if enhanced_data.get('type') == 'claude_response':
                        session.progress = min(85, session.progress + 5)
                        enhanced_data['progress'] = session.progress
                    elif enhanced_data.get('type') == 'review_completed':
                        session.progress = 90
                        enhanced_data['progress'] = session.progress
                    elif enhanced_data.get('type') == 'phase3_started':
                        session.progress = 95
                        enhanced_data['progress'] = session.progress
                    
                    if asyncio.iscoroutinefunction(progress_callback):
                        await progress_callback(enhanced_data)
                    else:
                        progress_callback(enhanced_data)
            
            # Execute the SDK review
            structured_result = await reviewer.perform_enhanced_review_async(
                review_type=sdk_review_type,
                include_summary=include_summary,
                progress_callback=sdk_progress_callback
            )
            
            # Store results in session
            session.results = structured_result
            session.progress = 100
            session.status = 'completed'
            session.completed_at = datetime.now()
            
            if progress_callback:
                await progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'progress': session.progress,
                    'message': 'Enhanced review completed successfully!',
                    'results_available': True,
                    'type': 'review_completed',
                    'structured_data': structured_result.get('structured_data', {}),
                    'metadata': structured_result.get('metadata', {})
                })
            
            logger.info(f"SDK Review session {session.session_id} completed successfully")
            return structured_result
            
        except Exception as e:
            session.status = 'error'
            session.error = str(e)
            logger.error(f"SDK Review session {session.session_id} failed: {e}")
            
            if progress_callback:
                await progress_callback({
                    'session_id': session.session_id,
                    'status': session.status,
                    'error': str(e),
                    'message': f'Review failed: {str(e)}',
                    'type': 'review_error'
                })
            raise
    
    async def execute_phase3_tutorial_async(self, session: 'ReviewSession') -> Dict:
        """Execute Phase 3 tutorial generation using native SDK"""
        
        if not session.results:
            raise ValueError("Main review must be completed before generating tutorial")
        
        if not hasattr(session, 'reviewer') or not session.reviewer:
            # Recreate reviewer if needed
            session.reviewer = self._create_sdk_reviewer(session)
        
        logger.info(f"🎯 Starting Phase 3 tutorial generation for session {session.session_id}")
        
        try:
            # Get the review content
            review_content = session.results.get('raw_content', '')
            review_type = session.config.get('review_config', {}).get('type', 'comprehensive')
            
            # Execute Phase 3 using SDK
            tutorial_result = await session.reviewer._perform_phase3_summary(
                review_content=review_content,
                review_type=review_type,
                progress_callback=None  # Could add progress callback here
            )
            
            # Structure tutorial for API consumption
            structured_tutorial = {
                'tutorial_id': f"{session.session_id}_tutorial",
                'session_id': session.session_id,
                'generated_at': datetime.now().isoformat(),
                'branch_name': session.branch_name,
                'pr_url': session.pr_url,
                
                # Raw tutorial content
                'raw_content': tutorial_result.get('raw_content', ''),
                
                # Structured sections
                'structured_sections': tutorial_result.get('structured_sections', {}),
                
                # Metadata
                'metadata': tutorial_result.get('metadata', {}),
                
                # Extract specific tutorial components
                'business_context': self._extract_business_context(tutorial_result.get('raw_content', '')),
                'architecture_overview': self._extract_architecture_overview(tutorial_result.get('raw_content', '')),
                'implementation_guide': self._extract_implementation_guide(tutorial_result.get('raw_content', '')),
                'mermaid_diagrams': tutorial_result.get('structured_sections', {}).get('mermaid_diagrams', []),
                'code_examples': tutorial_result.get('structured_sections', {}).get('code_examples', [])
            }
            
            logger.info(f"✅ Phase 3 tutorial completed for session {session.session_id}")
            return structured_tutorial
            
        except Exception as e:
            logger.error(f"❌ Phase 3 tutorial failed for session {session.session_id}: {e}")
            raise
    
    def execute_structured_review_sync(
        self, 
        session: 'ReviewSession', 
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """Synchronous wrapper for structured review (for threading)"""
        
        # Create async wrapper callback if provided
        async_progress_callback = None
        if progress_callback:
            async def async_wrapper(*args, **kwargs):
                # Convert async callback to sync
                if callable(progress_callback):
                    # Handle different argument patterns
                    if len(args) == 3:
                        # SDK calling with (event_type, message, data)
                        event_type, message, data = args
                        if isinstance(data, dict):
                            enhanced_data = {**data}
                        else:
                            enhanced_data = {}
                        enhanced_data.update({
                            'type': event_type,
                            'message': message
                        })
                        progress_callback(enhanced_data)
                    elif len(args) == 1:
                        # Single data argument
                        progress_callback(args[0])
                    else:
                        # Fallback
                        progress_callback(kwargs.get('data', {}))
            async_progress_callback = async_wrapper
        
        # Run the async review in event loop
        try:
            # Check if there's already an event loop running
            try:
                loop = asyncio.get_running_loop()
                # If we're in an existing loop, we need to run in a thread
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(
                        asyncio.run,
                        self.execute_structured_review_async(session, async_progress_callback)
                    )
                    return future.result()
            except RuntimeError:
                # No loop running, we can use asyncio.run directly
                return asyncio.run(
                    self.execute_structured_review_async(session, async_progress_callback)
                )
        except Exception as e:
            logger.error(f"Structured review sync wrapper failed: {e}")
            raise

    def _get_master_repo_path(self) -> Optional[str]:
        """Get master repository path from frontend configuration"""
        try:
            from services.worktree_config_service import get_worktree_config
            config_data = get_worktree_config("default")

            if config_data.get("base_path") and config_data.get("is_valid"):
                master_repo_path = config_data["base_path"]
                logger.info(f"📁 Using frontend configured master repo path: {master_repo_path}")
                return master_repo_path
            else:
                logger.warning(f"⚠️  Frontend worktree config not valid: {config_data}")
                return None

        except Exception as e:
            logger.error(f"❌ Failed to get master repo path from frontend config: {e}")
            return None

    def _create_sdk_reviewer(self, session: 'ReviewSession') -> EnhancedClaudeReviewerSDK:
        """Create an EnhancedClaudeReviewerSDK instance for the session"""
        try:
            # Extract repo path from config
            repo_path = session.config.get('pr_config', {}).get('repository', {}).get('path', '.')

            # Get master repository path from frontend configuration
            master_repo_path = self._get_master_repo_path()
            if not master_repo_path:
                raise ValueError("master_repo_path is required. Please provide the master repository path from frontend configuration.")

            # Create worktree if needed and not already created
            actual_worktree_path = session.worktree_path
            if session.branch_name and not session.worktree_path:
                logger.info(f"🔧 Creating worktree for SDK review: {session.branch_name}")
                try:
                    import sys
                    import os
                    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'cli-tools'))
                    from bitbucket_pr_analyzer import BitbucketPRAnalyzer  # type: ignore

                    pr_analyzer = BitbucketPRAnalyzer(
                        pr_url=session.pr_url,
                        branch_name=session.branch_name,
                        repo_path=master_repo_path,  # Use master repo path from frontend
                        use_worktree=True,
                        cleanup_worktree=False,  # Keep worktree for review
                        master_repo_path=master_repo_path  # Pass master repo path from frontend
                    )
                    
                    # Setup worktree (this will create isolated environment for the branch)
                    pr_analyzer._setup_worktree()
                    
                    if pr_analyzer.worktree_path:
                        actual_worktree_path = str(pr_analyzer.worktree_path)
                        session.worktree_path = actual_worktree_path
                        logger.info(f"✅ Worktree created for SDK review: {actual_worktree_path}")
                    else:
                        logger.warning(f"⚠️  No worktree created - using main repository")
                        
                except Exception as e:
                    logger.warning(f"⚠️  PR Analyzer setup failed: {e}")
                    logger.info(f"💡 Using main repository: {repo_path}")
            
            # Create enhanced reviewer with SDK
            reviewer = EnhancedClaudeReviewerSDK(
                config=session.config,
                worktree_path=actual_worktree_path,
                repo_path=master_repo_path,  # Use master repo path from frontend
                pr_url=session.pr_url,
                branch_name=session.branch_name
            )
            
            # Process Jira ticket data if available (same as original)
            if session.jira_ticket_data:
                reviewer = self._process_jira_ticket_data_sdk(reviewer, session)
            
            logger.info(f"✅ Enhanced SDK reviewer created for session {session.session_id}")
            return reviewer
            
        except Exception as e:
            logger.error(f"❌ Failed to create SDK reviewer: {e}")
            raise
    
    def _process_jira_ticket_data_sdk(self, reviewer: EnhancedClaudeReviewerSDK, session: 'ReviewSession') -> EnhancedClaudeReviewerSDK:
        """Process frontend Jira ticket data for SDK reviewer"""
        try:
            jira_ticket_data = session.jira_ticket_data
            logger.info(f"🎫 Processing Jira ticket for SDK: {jira_ticket_data.get('ticket_id')}")
            
            # Load complete ticket data from Jira API (same logic as "View Ticket" click)
            ticket_id = jira_ticket_data.get('ticket_id')
            logger.info(f"🔄 Loading complete ticket data from Jira API for: {ticket_id}")
            
            try:
                # IMPORTANT: Use the exact same logic as /api/jira/ticket/<ticket_key> endpoint
                # This replicates what happens when user clicks "View Ticket" in frontend
                
                # Import the same services used by the Jira API endpoint
                import requests
                import os
                from services.acceptance_criteria_service import AcceptanceCriteriaService
                from utils.text_processing import TextProcessor
                
                # Get OAuth token from session/environment (this should match frontend auth)
                # For now, let's use the AcceptanceCriteriaService with full API call
                
                ac_service = AcceptanceCriteriaService()
                
                # Try to get the ticket using the same API logic as the frontend
                # This should include full description, custom fields, etc.
                
                # Step 1: Try to load via API if we have access
                complete_ticket_data = None
                
                # Try to fetch complete ticket data from Jira API using same logic as /api/jira/ticket/<ticket_key>
                try:
                    # This mimics the exact logic from /api/jira/ticket/<ticket_key> route
                    logger.info(f"🌐 Attempting to fetch complete ticket data from Jira API...")
                    
                    # We'll use the AcceptanceCriteriaService with a mock request to get full data
                    # But first, let's try the core Jira integration if available
                    
                    # Create a JiraIntegration instance with the session config
                    # This mimics exactly what happens in the Jira API routes
                    logger.info(f"🎫 Creating JiraIntegration to fetch complete ticket data...")
                    
                    # Use session config to create JiraIntegration
                    from jira_integration import JiraIntegration
                    temp_jira_integration = JiraIntegration(session.config)
                    
                    if temp_jira_integration.enabled:
                        logger.info(f"🌐 JiraIntegration enabled, fetching ticket {ticket_id}...")
                        complete_jira_ticket = temp_jira_integration.get_ticket(ticket_id)
                        
                        if complete_jira_ticket:
                            logger.info(f"✅ Got complete ticket from JiraIntegration!")
                            logger.info(f"   - Description: {len(complete_jira_ticket.description)} chars")
                            logger.info(f"   - AC count: {len(complete_jira_ticket.acceptance_criteria)}")
                            logger.info(f"   - Status: {complete_jira_ticket.status}")
                            
                            # Update jira_ticket_data with complete information from API
                            jira_ticket_data.update({
                                'summary': complete_jira_ticket.summary,
                                'description': complete_jira_ticket.description,
                                'status': complete_jira_ticket.status,
                                'priority': complete_jira_ticket.priority,
                                'assignee': complete_jira_ticket.assignee,
                                'issue_type': complete_jira_ticket.issue_type,
                                'acceptance_criteria': complete_jira_ticket.acceptance_criteria,
                                'acceptance_criteria_count': len(complete_jira_ticket.acceptance_criteria)
                            })
                            
                            logger.info(f"🎯 Complete ticket data updated:")
                            logger.info(f"   - Description: {len(jira_ticket_data.get('description', ''))} chars")
                            logger.info(f"   - AC: {jira_ticket_data.get('acceptance_criteria_count', 0)} items")
                            logger.info(f"   - Status: {jira_ticket_data.get('status', 'Unknown')}")
                            
                            # Update session with complete data
                            session.jira_ticket_data = jira_ticket_data
                            
                        else:
                            logger.warning(f"⚠️ JiraIntegration could not fetch ticket {ticket_id}")
                    else:
                        logger.warning(f"⚠️ JiraIntegration not enabled in config")
                        
                except Exception as jira_api_error:
                    logger.error(f"❌ Could not fetch complete ticket from Jira API: {jira_api_error}")
                
                # Step 2: If we still don't have complete data, fall back to AcceptanceCriteriaService
                current_description = jira_ticket_data.get('description', '')
                if not current_description or len(current_description) < 10:
                    logger.warning(f"⚠️ Still no description available, using AcceptanceCriteriaService as fallback")
                    
                    # Use AcceptanceCriteriaService as fallback (will generate AC)
                    ac_result = ac_service.extract_or_generate_acceptance_criteria(
                        ticket_key=ticket_id,
                        summary=jira_ticket_data.get('summary', ''),
                        description_text=current_description,
                        custom_fields={}  # We don't have custom fields from frontend
                    )
                    
                    if ac_result.criteria and len(ac_result.criteria) > 0:
                        jira_ticket_data['acceptance_criteria'] = ac_result.criteria
                        jira_ticket_data['acceptance_criteria_count'] = len(ac_result.criteria)
                        session.jira_ticket_data = jira_ticket_data
                        
                        if ac_result.source == 'existing':
                            logger.info(f"✅ Fallback: Extracted {len(ac_result.criteria)} existing AC via {ac_result.method}")
                        else:
                            logger.info(f"🤖 Fallback: Generated {len(ac_result.criteria)} AC via {ac_result.method}")
                else:
                    logger.info(f"✅ Complete ticket data available - Description: {len(current_description)} chars")
                    
            except Exception as e:
                logger.error(f"❌ Error loading complete ticket data for {ticket_id}: {e}")
                import traceback
                logger.error(f"📍 Traceback: {traceback.format_exc()}")
                # Continue with original frontend data
            
            # Create raw ticket text from frontend data
            ticket_summary = jira_ticket_data.get('summary', '')
            ticket_description = jira_ticket_data.get('description', '')
            
            # Write the updated ticket data directly as JSON (simpler and more reliable)
            import tempfile
            import json
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as tmp:
                # Ensure the ticket has all required fields with updated AC
                complete_ticket_data = {
                    'key': jira_ticket_data.get('ticket_id'),
                    'ticket_id': jira_ticket_data.get('ticket_id'),
                    'summary': jira_ticket_data.get('summary', ''),
                    'description': jira_ticket_data.get('description', ''),
                    'acceptance_criteria': jira_ticket_data.get('acceptance_criteria', []),
                    'acceptance_criteria_count': jira_ticket_data.get('acceptance_criteria_count', 0),
                    'issue_type': 'Story',  # Default
                    'status': 'In Progress',  # Default
                    'priority': 'Medium',  # Default
                    'assignee': 'Unassigned',  # Default
                }
                json.dump(complete_ticket_data, tmp, indent=2, ensure_ascii=False)
                temp_ticket_file = tmp.name
            
            # Update config to use the JSON ticket file
            session.config['jira_config']['ticket_extraction']['manual_ticket_file'] = temp_ticket_file
            session.config['jira_config']['enabled'] = False  # Disable API, use file
            
            logger.info(f"✅ Jira ticket processed for SDK and saved to: {temp_ticket_file}")
            logger.info(f"📋 Ticket contains {len(jira_ticket_data.get('acceptance_criteria', []))} acceptance criteria")
            
            # Force reload the ticket in the reviewer
            if hasattr(reviewer, 'jira_integration'):
                ticket_id = jira_ticket_data.get('ticket_id')
                if ticket_id:
                    reviewer.current_ticket = reviewer.jira_integration.get_ticket(ticket_id)
            
            return reviewer
            
        except Exception as e:
            logger.error(f"❌ Error processing Jira ticket data for SDK: {e}")
            return reviewer
    
    def _map_review_type_to_sdk(self, review_type: str) -> str:
        """Map API review types to SDK review types"""
        type_mapping = {
            'quick': 'comprehensive',
            'comprehensive': 'comprehensive_with_ac',
            'ac-only': 'acceptance_criteria_focused',
            'bug-analysis': 'bug_and_quality_analysis',
            'security': 'security',
            'performance': 'performance'
        }
        
        return type_mapping.get(review_type, 'comprehensive_with_ac')
    
    def _extract_business_context(self, content: str) -> Dict:
        """Extract business context from tutorial content"""
        import re
        
        business_section = re.search(
            r'(?:Business Context|BUSINESS CONTEXT)[^#]*?(?=##|$)', 
            content, 
            re.DOTALL | re.IGNORECASE
        )
        
        if business_section:
            return {
                'raw_text': business_section.group(0).strip(),
                'user_impact': self._extract_user_impact(business_section.group(0)),
                'business_value': self._extract_business_value(business_section.group(0))
            }
        
        return {'raw_text': '', 'user_impact': '', 'business_value': ''}
    
    def _extract_architecture_overview(self, content: str) -> Dict:
        """Extract architecture overview from tutorial content"""
        import re
        
        arch_section = re.search(
            r'(?:Architecture|ARCHITECTURE)[^#]*?(?=##|$)', 
            content, 
            re.DOTALL | re.IGNORECASE
        )
        
        if arch_section:
            return {
                'raw_text': arch_section.group(0).strip(),
                'design_decisions': self._extract_design_decisions(arch_section.group(0)),
                'patterns_used': self._extract_patterns_used(arch_section.group(0))
            }
        
        return {'raw_text': '', 'design_decisions': [], 'patterns_used': []}
    
    def _extract_implementation_guide(self, content: str) -> Dict:
        """Extract implementation guide from tutorial content"""
        import re
        
        impl_section = re.search(
            r'(?:Implementation|IMPLEMENTATION)[^#]*?(?=##|$)', 
            content, 
            re.DOTALL | re.IGNORECASE
        )
        
        if impl_section:
            return {
                'raw_text': impl_section.group(0).strip(),
                'key_files': self._extract_key_files(impl_section.group(0)),
                'integration_points': self._extract_integration_points(impl_section.group(0))
            }
        
        return {'raw_text': '', 'key_files': [], 'integration_points': []}
    
    def _extract_user_impact(self, text: str) -> str:
        """Extract user impact from business context"""
        import re
        impact_match = re.search(r'(?:User Impact|user impact)[:\-]\s*([^\n]+)', text, re.IGNORECASE)
        return impact_match.group(1).strip() if impact_match else ''
    
    def _extract_business_value(self, text: str) -> str:
        """Extract business value from business context"""
        import re
        value_match = re.search(r'(?:Business Value|business value)[:\-]\s*([^\n]+)', text, re.IGNORECASE)
        return value_match.group(1).strip() if value_match else ''
    
    def _extract_design_decisions(self, text: str) -> List[str]:
        """Extract design decisions from architecture section"""
        import re
        decisions = re.findall(r'(?:Decision|decision)[:\-]\s*([^\n]+)', text, re.IGNORECASE)
        return [d.strip() for d in decisions]
    
    def _extract_patterns_used(self, text: str) -> List[str]:
        """Extract patterns used from architecture section"""
        import re
        patterns = re.findall(r'(?:Pattern|pattern)[:\-]\s*([^\n]+)', text, re.IGNORECASE)
        return [p.strip() for p in patterns]
    
    def _extract_key_files(self, text: str) -> List[str]:
        """Extract key files from implementation section"""
        import re
        files = re.findall(r'`([^`]+\.[a-zA-Z]+)`', text)
        return list(set(files))  # Remove duplicates
    
    def _extract_integration_points(self, text: str) -> List[str]:
        """Extract integration points from implementation section"""
        import re
        integrations = re.findall(r'(?:integrates? with|connects? to)\s+([^\n]+)', text, re.IGNORECASE)
        return [i.strip() for i in integrations]