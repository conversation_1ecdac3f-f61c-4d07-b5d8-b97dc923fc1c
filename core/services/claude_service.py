"""Claude Code command execution service"""

import subprocess
import logging
import shutil
from pathlib import Path
from typing import Dict

logger = logging.getLogger(__name__)


class ClaudeService:
    """Service for executing Claude Code commands"""
    
    @staticmethod
    def execute_command(prompt: str, output_format: str = "text", timeout: int = 300) -> Dict:
        """
        Execute a Claude Code command with the given prompt
        
        Args:
            prompt: The prompt to send to Claude
            output_format: Output format ("text" or "json")
            timeout: Command timeout in seconds
            
        Returns:
            Dict with 'success', 'response', and optional 'error' keys
        """
        try:
            cmd = [
                "claude", 
                "-p", prompt,
                "--output-format", output_format
            ]
            
            # Use current working directory for Claude commands
            working_path = Path.cwd()
            
            result = subprocess.run(
                cmd,
                cwd=working_path,
                capture_output=True,
                text=True,
                check=True,
                timeout=timeout
            )
            
            response_text = result.stdout.strip()
            
            if output_format == "json":
                try:
                    # Try to parse as JSON
                    import json
                    parsed_response = json.loads(response_text)
                    return {
                        'success': True,
                        'response': parsed_response
                    }
                except json.JSONDecodeError:
                    # If JSON parsing fails, return as text
                    return {
                        'success': True,
                        'response': response_text
                    }
            else:
                return {
                    'success': True,
                    'response': response_text
                }
                
        except subprocess.TimeoutExpired:
            logger.error(f"Claude command timed out after {timeout}s")
            return {
                'success': False,
                'error': f'Command timed out after {timeout} seconds',
                'response': None
            }
        except subprocess.CalledProcessError as e:
            logger.error(f"Claude command failed: {e.stderr}")
            return {
                'success': False,
                'error': f'Claude command failed: {e.stderr}',
                'response': None
            }
        except Exception as e:
            logger.error(f"Unexpected error executing Claude command: {e}")
            return {
                'success': False,
                'error': f'Unexpected error: {str(e)}',
                'response': None
            }
    
    @staticmethod
    def is_available() -> bool:
        """Check if Claude CLI is available"""
        return shutil.which('claude') is not None