"""Text processing utilities for Claude Code Reviewer API"""

import json
import logging
import re
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class TextProcessor:
    """Utilities for text processing and parsing"""
    
    @staticmethod
    def parse_adf_to_text(adf_content):
        """Parse Atlassian Document Format to plain text"""
        if not isinstance(adf_content, dict):
            return str(adf_content)
        
        def extract_text(node, indent=0):
            if isinstance(node, dict):
                node_type = node.get('type', '')
                
                # Handle different node types
                if node_type == 'text':
                    return node.get('text', '')
                elif node_type == 'paragraph':
                    content_parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text:
                            content_parts.append(child_text)
                    return ' '.join(content_parts)
                elif node_type == 'heading':
                    level = node.get('attrs', {}).get('level', 1)
                    content_parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text:
                            content_parts.append(child_text)
                    heading_text = ' '.join(content_parts)
                    return f"\n{'#' * level} {heading_text}\n" if heading_text else ''
                elif node_type == 'bulletList':
                    items = []
                    for item in node.get('content', []):
                        item_text = extract_text(item, indent)
                        if item_text.strip():
                            items.append(f"{'  ' * indent}• {item_text.strip()}")
                    return '\n'.join(items) if items else ''
                elif node_type == 'orderedList':
                    items = []
                    for i, item in enumerate(node.get('content', []), 1):
                        item_text = extract_text(item, indent)
                        if item_text.strip():
                            items.append(f"{'  ' * indent}{i}. {item_text.strip()}")
                    return '\n'.join(items) if items else ''
                elif node_type == 'listItem':
                    content_parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text:
                            content_parts.append(child_text)
                    return ' '.join(content_parts)
                elif node_type == 'doc':
                    # This is the root document node
                    parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text.strip():
                            parts.append(child_text)
                    return '\n\n'.join(parts)
                elif node_type in ['blockquote', 'panel']:
                    parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text.strip():
                            parts.append(child_text)
                    return '\n'.join(parts)
                elif node_type == 'hardBreak':
                    return '\n'
                elif 'content' in node:
                    # Generic handler for nodes with content
                    parts = []
                    for child in node.get('content', []):
                        child_text = extract_text(child, indent)
                        if child_text:
                            parts.append(child_text)
                    return ' '.join(parts) if parts else ''
            elif isinstance(node, list):
                parts = []
                for item in node:
                    item_text = extract_text(item, indent)
                    if item_text:
                        parts.append(item_text)
                return '\n'.join(parts)
            return ''
        
        # Main parsing
        result = extract_text(adf_content)
        
        # Clean up extra newlines but preserve structure
        lines = result.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            if line.strip():
                cleaned_lines.append(line)
                prev_empty = False
            elif not prev_empty:
                cleaned_lines.append('')
                prev_empty = True
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    @staticmethod
    def get_plain_text_description(fields):
        """Extract plain text description from Jira fields"""
        if 'description' in fields and fields['description']:
            desc_field = fields['description']
            if isinstance(desc_field, dict) and 'content' in desc_field:
                return TextProcessor.parse_adf_to_text(desc_field)
            elif isinstance(desc_field, str):
                return desc_field
            else:
                return str(desc_field) if desc_field else ''
        return ''
    
    @staticmethod
    def extract_json_from_response(response_text: str) -> str:
        """Extract JSON content from Claude's markdown-formatted response"""
        # Remove markdown code block markers
        response_text = response_text.strip()
        
        # Pattern to match ```json ... ``` blocks
        json_pattern = r'```json\s*(.*?)\s*```'
        match = re.search(json_pattern, response_text, re.DOTALL | re.IGNORECASE)
        
        if match:
            json_content = match.group(1).strip()
            logger.info(f"📝 Extracted JSON from markdown block: {len(json_content)} chars")
            return json_content
        
        # Try to find JSON object directly (fallback)
        json_object_pattern = r'\{.*\}'
        match = re.search(json_object_pattern, response_text, re.DOTALL)
        
        if match:
            json_content = match.group(0).strip()
            logger.info(f"📝 Extracted raw JSON object: {len(json_content)} chars")
            return json_content
        
        # If no JSON found, return original (will likely fail parsing)
        logger.warning(f"⚠️ No JSON pattern found in response, returning original")
        return response_text
    
    @staticmethod
    def parse_structured_findings(raw_results: str) -> Dict:
        """Parse structured findings from Claude review output using intelligent pattern matching"""
        findings = {
            'acceptance_criteria': [],
            'code_quality': [],
            'security_issues': [],
            'performance_issues': [],
            'bugs': [],
            'suggestions': []
        }
        
        # Enhanced parsing with multiple strategies
        lines = raw_results.split('\n')
        current_section = None
        
        # Patterns for detecting different types of content
        section_patterns = {
            'acceptance_criteria': [
                r'acceptance\s+criteria', r'ac\s+compliance', r'user\s+story', r'requirements?'
            ],
            'security_issues': [
                r'security', r'vulnerability', r'authentication', r'authorization', r'credential', 
                r'injection', r'xss', r'csrf', r'secrets?', r'password'
            ],
            'performance_issues': [
                r'performance', r'optimization', r'slow', r'memory', r'cpu', r'cache', 
                r'async', r'blocking', r'bottleneck'
            ],
            'bugs': [
                r'bug', r'error', r'exception', r'crash', r'fail', r'race\s+condition', 
                r'null\s+pointer', r'undefined', r'memory\s+leak'
            ],
            'code_quality': [
                r'code\s+quality', r'refactor', r'clean\s+code', r'maintainability', 
                r'readability', r'complexity', r'duplication', r'pattern', r'structure'
            ],
            'suggestions': [
                r'suggestion', r'recommend', r'consider', r'improvement', r'enhancement', 
                r'best\s+practice', r'tip'
            ]
        }
        
        file_pattern = r'(?:in\s+|file\s+|`)?([a-zA-Z0-9/_.-]+\.[a-zA-Z]{1,4})(?:`|:|\\s|$)'
        line_pattern = r'line\s*(?:number\s*)?(\\d+)|:(\\d+)(?::|$)'
        severity_pattern = r'\\b(critical|high|medium|low|minor|major)\\b'
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            # Detect section headers
            line_lower = line.lower()
            detected_section = None
            
            for section_key, patterns in section_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, line_lower):
                        detected_section = section_key
                        break
                if detected_section:
                    break
            
            # If we found a clear section header, switch context
            if detected_section and any(marker in line for marker in ['##', '###', '**', '__', ':']):
                current_section = detected_section
                continue
            
            # Extract findings from bullet points and numbered lists
            bullet_match = re.match(r'^[\s]*[-*•]\s*(.+)$', line)
            number_match = re.match(r'^[\s]*\d+\.\s*(.+)$', line)
            
            if bullet_match or number_match:
                finding_text = bullet_match.group(1) if bullet_match else (number_match.group(1) if number_match else "")
                
                # Auto-categorize if no current section
                if not current_section:
                    current_section = TextProcessor._auto_categorize_finding(finding_text, section_patterns)
                
                # Extract metadata
                file_match = re.search(file_pattern, finding_text)
                line_match = re.search(line_pattern, finding_text)
                severity_match = re.search(severity_pattern, finding_text.lower())
                
                finding = {
                    'text': finding_text.strip(),
                    'severity': severity_match.group(1) if severity_match else TextProcessor._infer_severity(finding_text),
                }
                
                if file_match:
                    finding['file'] = file_match.group(1)
                
                if line_match:
                    finding['line'] = int(line_match.group(1) or line_match.group(2))
                
                # Look for suggestion in next few lines
                suggestion = TextProcessor._extract_suggestion(lines, i)
                if suggestion:
                    finding['suggestion'] = suggestion
                
                if current_section and current_section in findings:
                    findings[current_section].append(finding)
                else:
                    # Default to suggestions if unsure
                    findings['suggestions'].append(finding)
        
        # Post-process to clean up and validate findings
        for category in findings:
            findings[category] = TextProcessor._clean_findings(findings[category])
        
        return findings
    
    @staticmethod
    def _auto_categorize_finding(text: str, section_patterns: Dict) -> str:
        """Automatically categorize a finding based on its content"""
        text_lower = text.lower()
        
        # Score each category
        scores = {}
        for category, patterns in section_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower))
                score += matches
            scores[category] = score
        
        # Return category with highest score, or 'suggestions' as default
        if scores:
            max_category = max(scores, key=lambda x: scores.get(x, 0))
            return max_category if scores[max_category] > 0 else 'suggestions'
        
        return 'suggestions'
    
    @staticmethod
    def _infer_severity(text: str) -> str:
        """Infer severity based on content and keywords"""
        text_lower = text.lower()
        
        high_keywords = [
            'critical', 'security', 'vulnerability', 'sql injection', 'xss', 'csrf',
            'password', 'secret', 'authentication', 'race condition', 'crash',
            'memory leak', 'fail', 'error', 'exception', 'bug'
        ]
        
        medium_keywords = [
            'performance', 'slow', 'optimization', 'refactor', 'maintainability',
            'complexity', 'duplication', 'warning', 'issue'
        ]
        
        low_keywords = [
            'suggestion', 'consider', 'improvement', 'style', 'formatting',
            'comment', 'documentation', 'naming', 'minor'
        ]
        
        if any(keyword in text_lower for keyword in high_keywords):
            return 'high'
        elif any(keyword in text_lower for keyword in medium_keywords):
            return 'medium'
        elif any(keyword in text_lower for keyword in low_keywords):
            return 'low'
        else:
            return 'medium'  # default
    
    @staticmethod
    def _extract_suggestion(lines: List[str], current_index: int) -> Optional[str]:
        """Extract suggestion from following lines"""
        suggestion_keywords = ['suggest', 'recommend', 'should', 'consider', 'fix', 'solution']
        
        # Look in next 3 lines for suggestions
        for i in range(current_index + 1, min(current_index + 4, len(lines))):
            line = lines[i].strip()
            if not line:
                continue
            
            line_lower = line.lower()
            if any(keyword in line_lower for keyword in suggestion_keywords):
                # Clean up the suggestion
                suggestion = re.sub(r'^[\s\-\*•]*', '', line)
                suggestion = re.sub(r'^(suggestion|recommend|should|consider)[:.]?\s*', '', suggestion, flags=re.IGNORECASE)
                return suggestion.strip()
        
        return None
    
    @staticmethod
    def _clean_findings(findings: List[Dict]) -> List[Dict]:
        """Clean and validate findings"""
        cleaned = []
        
        for finding in findings:
            # Skip very short or empty findings
            if not finding.get('text') or len(finding['text']) < 10:
                continue
            
            # Normalize severity
            if finding.get('severity') not in ['high', 'medium', 'low']:
                finding['severity'] = 'medium'
            
            # Clean up text
            finding['text'] = finding['text'].strip()
            
            cleaned.append(finding)
        
        return cleaned