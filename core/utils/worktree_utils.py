from pathlib import Path
from typing import Optional
import logging

logger = logging.getLogger(__name__)

def get_configured_worktree_path() -> str:
    """
    DEPRECATED: This function is no longer used. 
    All worktree paths now come from the frontend configuration.
    
    Raises:
        ValueError: Always - this function should not be called
    """
    raise ValueError(
        "get_configured_worktree_path() is deprecated. "
        "Please use generate_worktree_path() with master_repo_path parameter from frontend."
    )

def generate_worktree_path(branch_name: str, master_repo_path: Optional[str] = None) -> str:
    """
    Generate worktree path for a given branch name.
    The worktree will be created OUTSIDE the master repository in ../worktrees/
    
    Args:
        branch_name: The branch name to generate path for
        master_repo_path: Master repo path from frontend (REQUIRED)
        
    Returns:
        str: Full path to the worktree directory (outside master repo)
        
    Raises:
        ValueError: If master_repo_path is not provided
    """
    if master_repo_path is None:
        raise ValueError(
            "master_repo_path is required. Please provide the master repository path from frontend configuration."
        )
    
    # Create worktrees outside the master repository
    # If master is /Users/<USER>/rma/rma-mono -> worktrees in /Users/<USER>/rma/worktrees/
    master_path = Path(master_repo_path)
    parent_dir = master_path.parent  # /Users/<USER>/rma
    worktree_base_dir = parent_dir / "worktrees"  # /Users/<USER>/rma/worktrees
    
    # Generate worktree name using same pattern as existing scripts
    branch_short = branch_name[:10] if len(branch_name) > 10 else branch_name
    branch_safe = ''.join(c if c.isalnum() or c in '-_' else '-' for c in branch_short)
    worktree_name = f"{branch_safe}-review"
    
    worktree_path = worktree_base_dir / worktree_name
    logger.info(f"📁 Generated worktree path: {worktree_path} (outside master repo: {master_repo_path})")
    
    return str(worktree_path)

def ensure_worktree_base_directory(master_repo_path: Optional[str] = None) -> bool:
    """
    Ensure the worktree base directory exists and is writable.
    Creates ../worktrees/ directory relative to master repository.
    
    Args:
        master_repo_path: Master repo path from frontend (REQUIRED)
        
    Returns:
        bool: True if directory is ready for use, False otherwise
        
    Raises:
        ValueError: If master_repo_path is not provided
    """
    if master_repo_path is None:
        raise ValueError(
            "master_repo_path is required. Please provide the master repository path from frontend configuration."
        )
    
    try:
        # Create worktree base directory outside master repo
        master_path = Path(master_repo_path)
        parent_dir = master_path.parent  # /Users/<USER>/rma
        worktree_base_dir = parent_dir / "worktrees"  # /Users/<USER>/rma/worktrees
        
        worktree_base_dir.mkdir(parents=True, exist_ok=True)
        
        # Test write permissions
        test_file = worktree_base_dir / ".worktree-test"
        test_file.touch()
        test_file.unlink()
        
        logger.info(f"✅ Worktree base directory ready: {worktree_base_dir}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to prepare worktree directory: {e}")
        return False