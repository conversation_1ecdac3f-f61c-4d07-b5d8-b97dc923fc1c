#!/usr/bin/env python3
"""
Claude Code Reviewer API Service
Separater Flask API Service für Enhanced Code Review Funktionalität.
Integriert die bestehende CLI-Logik in Web API Format.
"""

import json
import os
import sys
import uuid
import threading
import time
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from flask import Flask, request, jsonify, session
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import logging
from dotenv import load_dotenv

# Load environment variables from .env files
# Path to .env.local in web-app/review-comment-responder-react/
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'web-app', 'review-comment-responder-react', '.env.local')
load_dotenv(dotenv_path=dotenv_path)
load_dotenv()  # Also load from default .env file

# Import existing enhanced reviewer components
from enhanced_claude_reviewer import EnhancedClaudeReviewer
from jira_integration import JiraIntegration, JiraTicket
import subprocess

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')

# Enable CORS for React frontend
CORS(app, origins=["http://localhost:5173", "http://localhost:5174"], supports_credentials=True)

# Enable WebSocket support for real-time updates
socketio = SocketIO(app, cors_allowed_origins=["http://localhost:5173", "http://localhost:5174"])


def execute_claude_command(prompt: str, output_format: str = "text", timeout: int = 300) -> Dict:
    """
    Execute a Claude Code command with the given prompt
    
    Args:
        prompt: The prompt to send to Claude
        output_format: Output format ("text" or "json")
        timeout: Command timeout in seconds
        
    Returns:
        Dict with 'success', 'response', and optional 'error' keys
    """
    try:
        cmd = [
            "claude", 
            "-p", prompt,
            "--output-format", output_format
        ]
        
        # Use current working directory for Claude commands
        working_path = Path.cwd()
        
        result = subprocess.run(
            cmd,
            cwd=working_path,
            capture_output=True,
            text=True,
            check=True,
            timeout=timeout
        )
        
        response_text = result.stdout.strip()
        
        if output_format == "json":
            try:
                # Try to parse as JSON
                parsed_response = json.loads(response_text)
                return {
                    'success': True,
                    'response': parsed_response
                }
            except json.JSONDecodeError:
                # If JSON parsing fails, return as text
                return {
                    'success': True,
                    'response': response_text
                }
        else:
            return {
                'success': True,
                'response': response_text
            }
            
    except subprocess.TimeoutExpired:
        app.logger.error(f"Claude command timed out after {timeout}s")
        return {
            'success': False,
            'error': f'Command timed out after {timeout} seconds',
            'response': None
        }
    except subprocess.CalledProcessError as e:
        app.logger.error(f"Claude command failed: {e.stderr}")
        return {
            'success': False,
            'error': f'Claude command failed: {e.stderr}',
            'response': None
        }
    except Exception as e:
        app.logger.error(f"Unexpected error executing Claude command: {e}")
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'response': None
        }

# Global state management
review_sessions: Dict[str, 'ReviewSession'] = {}
active_threads: Dict[str, threading.Thread] = {}


class ReviewSession:
    """Manages a single code review session"""
    
    def __init__(self, session_id: str, config: Dict, branch_name: str, pr_url: Optional[str] = None):
        self.session_id: str = session_id
        self.config: Dict = config
        self.branch_name: str = branch_name
        self.pr_url: Optional[str] = pr_url
        self.status: str = 'initializing'  # initializing, running, completed, error
        self.progress: int = 0
        self.created_at: datetime = datetime.now()
        self.completed_at: Optional[datetime] = None
        self.results: Optional[Dict] = None
        self.error: Optional[str] = None
        self.reviewer: Optional[EnhancedClaudeReviewer] = None
        self.worktree_path: Optional[str] = None
        self.jira_ticket_data: Optional[Dict] = None
        
    def to_dict(self) -> Dict:
        """Convert session to JSON serializable dict"""
        return {
            'session_id': self.session_id,
            'branch_name': self.branch_name,
            'pr_url': self.pr_url,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'results': self.results,
            'error': self.error,
            'worktree_path': self.worktree_path,
            'jira_ticket_data': self.jira_ticket_data
        }


@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'claude-code-reviewer-api',
        'timestamp': datetime.now().isoformat(),
        'active_sessions': len(review_sessions)
    })


# JIRA OAUTH BACKEND ENDPOINTS
@app.route('/api/jira/exchange-token', methods=['POST'])
def exchange_jira_token():
    """Exchange Jira authorization code for access token"""
    try:
        data = request.json
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON data is required'
            }), 400
            
        code = data.get('code')
        
        if not code:
            return jsonify({
                'success': False,
                'error': 'Authorization code is required'
            }), 400
        
        # Exchange code for tokens
        import requests
        
        # Try both VITE_ prefixed and non-prefixed environment variables
        client_id = os.getenv('JIRA_CLIENT_ID') or os.getenv('VITE_JIRA_CLIENT_ID')
        client_secret = os.getenv('JIRA_CLIENT_SECRET') or os.getenv('VITE_JIRA_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            logger.error(f"Missing OAuth credentials: client_id={bool(client_id)}, client_secret={bool(client_secret)}")
            return jsonify({
                'success': False,
                'error': 'OAuth credentials not configured'
            }), 500
        
        token_response = requests.post('https://auth.atlassian.com/oauth/token', {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'code': code,
            'redirect_uri': data.get('redirect_uri', 'http://localhost:5173/auth/jira/callback')
        })
        
        if not token_response.ok:
            logger.error(f"Token exchange failed: {token_response.text}")
            return jsonify({
                'success': False,
                'error': 'Failed to exchange authorization code'
            }), 400
        
        token_data = token_response.json()
        
        return jsonify({
            'success': True,
            'access_token': token_data.get('access_token'),
            'refresh_token': token_data.get('refresh_token'),
            'expires_in': token_data.get('expires_in')
        })
        
    except Exception as e:
        logger.error(f"Error in Jira token exchange: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/assigned-prs', methods=['GET'])
def get_assigned_prs():
    """Get pull requests assigned to the current user for review"""
    try:
        # Note: This endpoint expects the frontend to handle Bitbucket OAuth
        # and pass PRs to the review workflow. The frontend has full Bitbucket integration.
        # For direct API access, Bitbucket integration would need to be added to backend.
        
        logger.info("Assigned PRs endpoint called - Frontend handles Bitbucket integration")
        
        return jsonify({
            'success': True,
            'prs': [],
            'total': 0,
            'message': 'Use frontend Bitbucket integration for PR fetching. Backend focuses on code review execution.'
        })
        
    except Exception as e:
        logger.error(f"Error in assigned PRs endpoint: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


def _parse_adf_to_text(adf_content):
    """Parse Atlassian Document Format to plain text"""
    if not isinstance(adf_content, dict):
        return str(adf_content)
    
    def extract_text(node, indent=0):
        if isinstance(node, dict):
            node_type = node.get('type', '')
            
            # Handle different node types
            if node_type == 'text':
                return node.get('text', '')
            elif node_type == 'paragraph':
                content_parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text:
                        content_parts.append(child_text)
                return ' '.join(content_parts)
            elif node_type == 'heading':
                level = node.get('attrs', {}).get('level', 1)
                content_parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text:
                        content_parts.append(child_text)
                heading_text = ' '.join(content_parts)
                return f"\n{'#' * level} {heading_text}\n" if heading_text else ''
            elif node_type == 'bulletList':
                items = []
                for item in node.get('content', []):
                    item_text = extract_text(item, indent)
                    if item_text.strip():
                        items.append(f"{'  ' * indent}• {item_text.strip()}")
                return '\n'.join(items) if items else ''
            elif node_type == 'orderedList':
                items = []
                for i, item in enumerate(node.get('content', []), 1):
                    item_text = extract_text(item, indent)
                    if item_text.strip():
                        items.append(f"{'  ' * indent}{i}. {item_text.strip()}")
                return '\n'.join(items) if items else ''
            elif node_type == 'listItem':
                content_parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text:
                        content_parts.append(child_text)
                return ' '.join(content_parts)
            elif node_type == 'doc':
                # This is the root document node
                parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text.strip():
                        parts.append(child_text)
                return '\n\n'.join(parts)
            elif node_type in ['blockquote', 'panel']:
                parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text.strip():
                        parts.append(child_text)
                return '\n'.join(parts)
            elif node_type == 'hardBreak':
                return '\n'
            elif 'content' in node:
                # Generic handler for nodes with content
                parts = []
                for child in node.get('content', []):
                    child_text = extract_text(child, indent)
                    if child_text:
                        parts.append(child_text)
                return ' '.join(parts) if parts else ''
        elif isinstance(node, list):
            parts = []
            for item in node:
                item_text = extract_text(item, indent)
                if item_text:
                    parts.append(item_text)
            return '\n'.join(parts)
        return ''
    
    # Main parsing
    result = extract_text(adf_content)
    
    # Clean up extra newlines but preserve structure
    lines = result.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        if line.strip():
            cleaned_lines.append(line)
            prev_empty = False
        elif not prev_empty:
            cleaned_lines.append('')
            prev_empty = True
    
    # Remove trailing empty lines
    while cleaned_lines and not cleaned_lines[-1].strip():
        cleaned_lines.pop()
    
    return '\n'.join(cleaned_lines)


def _get_plain_text_description(fields):
    """Extract plain text description from Jira fields"""
    if 'description' in fields and fields['description']:
        desc_field = fields['description']
        if isinstance(desc_field, dict) and 'content' in desc_field:
            return _parse_adf_to_text(desc_field)
        elif isinstance(desc_field, str):
            return desc_field
        else:
            return str(desc_field) if desc_field else ''
    return ''


def _extract_acceptance_criteria_with_claude(ticket_key, summary, description_text, custom_fields):
    """Use Claude AI to extract or generate acceptance criteria from ticket content"""
    import subprocess
    import json
    import tempfile
    import os
    
    try:
        # First try to extract existing AC from text
        existing_ac = _extract_existing_acceptance_criteria(description_text, custom_fields)
        if existing_ac:
            logger.info(f"📋 Found {len(existing_ac)} existing AC in {ticket_key}")
            return existing_ac
        
        # If no AC found, generate them using Claude
        logger.info(f"🤖 Generating AC for {ticket_key} using Claude AI...")
        
        # Create prompt for Claude
        prompt = f"""Du bist ein Experte für Software-Entwicklung und Requirement Engineering. Analysiere dieses Jira Ticket und erstelle spezifische, testbare Akzeptanzkriterien.

**Ticket:** {ticket_key}
**Zusammenfassung:** {summary}

**Beschreibung:**
{description_text}

**Aufgabe:**
Erstelle 3-5 konkrete, messbare Akzeptanzkriterien für dieses Ticket. Jedes Kriterium soll:
- Spezifisch und testbar sein
- Den Nutzen für den Entwickler/Benutzer beschreiben
- Technische Implementierungsdetails berücksichtigen
- In deutscher Sprache verfasst sein

**WICHTIG:** Antworte NUR mit einem JSON Array aus Strings. Keine zusätzlichen Erklärungen oder Text!

**Erwartetes Format:**
["Kriterium 1: Beschreibung des ersten testbaren Ergebnisses", "Kriterium 2: Beschreibung des zweiten testbaren Ergebnisses"]

**Antwort:**"""

        # Call Claude Code CLI directly with the prompt
        result = subprocess.run([
            'claude'
        ], input=prompt, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            response_text = result.stdout.strip()
            logger.info(f"🔍 Claude raw response for {ticket_key}: {response_text[:200]}...")
            
            # Try to parse as JSON
            try:
                criteria = json.loads(response_text)
                if isinstance(criteria, list) and all(isinstance(item, str) for item in criteria):
                    logger.info(f"✅ Generated {len(criteria)} AC for {ticket_key}")
                    return criteria
            except json.JSONDecodeError:
                # If not valid JSON, try to extract from text
                logger.info(f"⚠️ Claude response not JSON, parsing manually for {ticket_key}")
                parsed_criteria = _parse_ac_from_text_response(response_text)
                logger.info(f"🔧 Manually parsed {len(parsed_criteria)} AC: {parsed_criteria}")
                return parsed_criteria
        else:
            logger.error(f"❌ Claude CLI error for {ticket_key}: {result.stderr}")
            
    except Exception as e:
        logger.error(f"❌ Error generating AC for {ticket_key}: {e}")
    
    # Fallback: generate basic AC from summary
    return _generate_fallback_acceptance_criteria(summary, description_text)


def _extract_existing_acceptance_criteria(description_text, custom_fields):
    """Extract existing acceptance criteria from text or custom fields"""
    criteria = []
    
    # Check custom fields first
    for field_key, field_value in custom_fields.items():
        if field_key.startswith('customfield_') and field_value:
            if isinstance(field_value, str) and any(keyword in field_value.lower() 
                for keyword in ['akzeptanz', 'acceptance', 'criteria', 'ac:']):
                lines = field_value.split('\n')
                for line in lines:
                    clean_line = line.strip()
                    if len(clean_line) > 10:
                        criteria.append(clean_line)
    
    # If found in custom fields, return those
    if criteria:
        return criteria
    
    # Look for AC section in description
    if not description_text:
        return []
        
    lines = description_text.split('\n')
    in_ac_section = False
    
    for line in lines:
        line_lower = line.strip().lower()
        
        # Start of AC section
        if any(ac_header in line_lower for ac_header in [
            'akzeptanzkriterien', 'acceptance criteria', 'akzeptanz-kriterien'
        ]):
            in_ac_section = True
            continue
            
        # End of AC section
        if in_ac_section and any(section_header in line_lower for section_header in [
            'input:', 'qa:', 'notes:', 'description:', 'background:', 'context:'
        ]):
            break
            
        # Extract criteria lines
        if in_ac_section and line.strip():
            clean_line = line.strip()
            if (clean_line.startswith(('•', '-', '*', '◦', '1.', '2.', '3.', '4.', '5.')) or 
                (':' in clean_line and len(clean_line) > 10)):
                criteria.append(clean_line)
    
    return criteria


def _parse_ac_from_text_response(response_text):
    """Parse acceptance criteria from Claude's text response"""
    criteria = []
    
    # Try to find JSON-like content
    import re
    
    # Look for JSON array pattern
    json_match = re.search(r'\[([^\]]+)\]', response_text, re.DOTALL)
    if json_match:
        json_content = json_match.group(0)
        try:
            import json
            parsed = json.loads(json_content)
            if isinstance(parsed, list):
                return [str(item).strip() for item in parsed if str(item).strip()]
        except:
            pass
    
    # Fallback: parse line by line
    lines = response_text.split('\n')
    
    for line in lines:
        line = line.strip()
        if line and not line.startswith(('**', '#', 'Here', 'Based', 'I ', 'Die ', 'Das ', 'Als ')):
            # Remove common prefixes and quotes
            line = line.lstrip('- * • ◦ 1. 2. 3. 4. 5. ').strip()
            line = line.strip('"\'')
            
            # Only accept lines that look like acceptance criteria
            if len(line) > 15 and (':' in line or any(keyword in line.lower() for keyword in [
                'service', 'api', 'daten', 'implementier', 'bereitgestellt', 'funktionier'
            ])):
                criteria.append(line)
    
    return criteria[:5]  # Limit to 5 criteria


def _generate_fallback_acceptance_criteria(summary, description_text):
    """Generate basic acceptance criteria as fallback"""
    criteria = []
    
    if 'service' in summary.lower():
        criteria.append("Service-Bereitstellung: Der Service ist erfolgreich bereitgestellt und erreichbar")
        criteria.append("Funktionalität: Alle beschriebenen Funktionen sind implementiert und funktionsfähig")
        
    if 'api' in summary.lower():
        criteria.append("API-Integration: Die API-Verbindung funktioniert korrekt")
        criteria.append("Datenverarbeitung: Daten werden korrekt verarbeitet und zurückgegeben")
        
    if not criteria:
        criteria.append("Implementierung: Die beschriebene Funktionalität ist vollständig implementiert")
        criteria.append("Tests: Alle relevanten Tests sind erstellt und bestehen")
        criteria.append("Dokumentation: Die Implementierung ist ausreichend dokumentiert")
    
    return criteria


@app.route('/api/jira/tickets', methods=['GET'])
def get_jira_tickets():
    """Get assigned Jira tickets for current user - Full implementation from comment responder"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'Bearer token required'
            }), 401
        
        access_token = auth_header.split(' ')[1]
        max_results = request.args.get('maxResults', 50)
        
        # Get Jira base URL from header or use default
        jira_base_url = request.headers.get('X-Jira-Base-URL', 'https://regionalmediendigital.atlassian.net')
        
        # Use Cloud ID from header if provided, otherwise extract from URL
        cloud_id = request.headers.get('X-Jira-Cloud-ID')
        if cloud_id:
            # Use the proper Cloud ID from OAuth discovery
            api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/search'
        else:
            # Extract cloud ID from the Jira base URL as fallback
            import re
            match = re.search(r'https://([^.]+)\.atlassian\.net', jira_base_url)
            if match:
                cloud_id = match.group(1)
                api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/search'
            else:
                # Fallback to direct URL (shouldn't happen with OAuth)
                api_url = f'{jira_base_url}/rest/api/3/search'
        
        # JQL to get tickets that need code review
        jql = 'status = "Code Review" AND resolution = Unresolved ORDER BY updated DESC'
        
        logger.info(f"🔍 Jira Search Request: BaseURL={jira_base_url}, ApiURL={api_url}, JQL={jql}")
        
        # Call Jira Search API
        import requests
        search_response = requests.post(
            api_url,
            headers={
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            json={
                'jql': jql,
                'maxResults': max_results,
                'fields': [
                    'summary', 'description', 'status', 'priority',
                    'assignee', 'reporter', 'created', 'updated', 
                    'customfield_10020',  # Common AC field
                    'customfield_10021',  # Alternative AC field
                    'customfield_*'       # Include all custom fields
                ],
                'expand': ['renderedFields']  # Get full rendered description
            }
        )
        
        if not search_response.ok:
            logger.error(f"❌ Jira API error: {search_response.status_code} - {search_response.text}")
            return jsonify({
                'success': False,
                'error': f'Jira API error: {search_response.status_code}'
            }), search_response.status_code
        
        search_data = search_response.json()
        issues = search_data.get('issues', [])
        
        # Transform to our format
        tickets = []
        for issue in issues:
            fields = issue.get('fields', {})
            
            # For ticket list, only extract existing AC (don't generate with Claude to save time)
            acceptance_criteria = _extract_existing_acceptance_criteria(
                description_text=_get_plain_text_description(fields),
                custom_fields=fields
            )
            
            # Get full description - prioritize rendered HTML for better formatting
            description = ''
            
            # Debug what we have
            if issue['key'] == 'CMS20-1206':  # Debug specific ticket
                logger.info(f"🔍 DEBUG CMS20-1206:")
                logger.info(f"   Has renderedFields: {'renderedFields' in issue}")
                if 'renderedFields' in issue:
                    logger.info(f"   renderedFields keys: {list(issue['renderedFields'].keys())}")
                    if 'description' in issue['renderedFields']:
                        logger.info(f"   Rendered desc preview: {str(issue['renderedFields']['description'])[:200]}...")
                
            if 'renderedFields' in issue and 'description' in issue['renderedFields']:
                # Rendered fields contain HTML with proper formatting
                description = issue['renderedFields']['description']
                logger.info(f"Using rendered HTML description for {issue['key']}")
            elif 'description' in fields and fields['description']:
                # Fallback to raw description if no rendered version
                desc_field = fields['description']
                if isinstance(desc_field, dict) and 'content' in desc_field:
                    # Parse ADF format to plain text
                    description = _parse_adf_to_text(desc_field)
                    logger.info(f"Parsed ADF description for {issue['key']}")
                elif isinstance(desc_field, str):
                    description = desc_field
                else:
                    description = str(desc_field) if desc_field else ''
            
            # Log description length for debugging
            logger.info(f"Ticket {issue['key']}: Description length = {len(description)} chars")
            
            ticket = {
                'id': issue['id'],
                'key': issue['key'],
                'summary': fields.get('summary', ''),
                'description': description,
                'status': {
                    'name': fields.get('status', {}).get('name', 'Unknown'),
                    'statusCategory': fields.get('status', {}).get('statusCategory', {})
                },
                'priority': {
                    'name': fields.get('priority', {}).get('name', 'Medium'),
                    'iconUrl': fields.get('priority', {}).get('iconUrl', '')
                },
                'assignee': {
                    'accountId': fields.get('assignee', {}).get('accountId', '') if fields.get('assignee') else '',
                    'displayName': fields.get('assignee', {}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned',
                    'emailAddress': fields.get('assignee', {}).get('emailAddress', '') if fields.get('assignee') else ''
                },
                'reporter': {
                    'accountId': fields.get('reporter', {}).get('accountId', '') if fields.get('reporter') else '',
                    'displayName': fields.get('reporter', {}).get('displayName', 'Unknown') if fields.get('reporter') else 'Unknown',
                    'emailAddress': fields.get('reporter', {}).get('emailAddress', '') if fields.get('reporter') else ''
                },
                'created': fields.get('created', ''),
                'updated': fields.get('updated', ''),
                'acceptance_criteria': acceptance_criteria,
                'acceptance_criteria_count': len(acceptance_criteria),
                'customFields': {k: v for k, v in fields.items() if k.startswith('customfield_')}
            }
            tickets.append(ticket)
        
        # Transform tickets to AssignedTicket format expected by frontend
        assigned_tickets = []
        for ticket in tickets:
            assigned_ticket = {
                'ticket_id': ticket['key'],
                'summary': ticket['summary'],
                'description': ticket['description'],
                'status': ticket['status']['name'],
                'priority': ticket['priority']['name'],
                'assignee': ticket['assignee']['displayName'],
                'created_date': ticket['created'],
                'updated_date': ticket['updated'],
                'acceptance_criteria_count': ticket['acceptance_criteria_count'],
                'acceptance_criteria': ticket['acceptance_criteria'],
                'acceptanceCriteria': ticket['acceptance_criteria'],  # Support both formats
                'related_prs': []  # Would need to determine from branch names
            }
            assigned_tickets.append(assigned_ticket)
        
        logger.info(f"✅ Successfully fetched {len(assigned_tickets)} Jira tickets")
        
        return jsonify({
            'success': True,
            'tickets': assigned_tickets,
            'total': search_data.get('total', len(assigned_tickets))
        })
        
    except Exception as e:
        logger.error(f"Error fetching Jira tickets: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/jira/ticket/<ticket_key>', methods=['GET'])
def get_jira_ticket_details(ticket_key):
    """Get detailed information for a specific Jira ticket"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({
                'success': False,
                'error': 'Bearer token required'
            }), 401
        
        access_token = auth_header.split(' ')[1]
        
        # Get Jira base URL from header or use default
        jira_base_url = request.headers.get('X-Jira-Base-URL', 'https://regionalmediendigital.atlassian.net')
        cloud_id = request.headers.get('X-Jira-Cloud-ID')
        
        if cloud_id:
            api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/issue/{ticket_key}'
        else:
            # Extract cloud ID from the Jira base URL as fallback
            import re
            match = re.search(r'https://([^.]+)\.atlassian\.net', jira_base_url)
            if match:
                cloud_id = match.group(1)
                api_url = f'https://api.atlassian.com/ex/jira/{cloud_id}/rest/api/3/issue/{ticket_key}'
            else:
                api_url = f'{jira_base_url}/rest/api/3/issue/{ticket_key}'
        
        # Fetch ticket details with rendered fields
        import requests
        response = requests.get(
            api_url,
            headers={
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json'
            },
            params={
                'expand': 'renderedFields',
                'fields': '*all'  # Get all fields including custom fields
            }
        )
        
        if response.status_code == 404:
            return jsonify({
                'success': False,
                'error': 'Ticket not found'
            }), 404
        
        if not response.ok:
            return jsonify({
                'success': False,
                'error': f'Failed to fetch ticket: {response.status_code}'
            }), response.status_code
        
        issue = response.json()
        fields = issue.get('fields', {})
        
        # Debug all fields for the specific ticket
        if ticket_key == 'CMS20-1206':
            import json
            logger.info(f"🔍 RAW JIRA API RESPONSE for {ticket_key}:")
            logger.info(f"🔍 Full issue object: {json.dumps(issue, indent=2, default=str)}")
            
            logger.info(f"🔍 DESCRIPTION FIELD ANALYSIS:")
            desc_field = fields.get('description')
            logger.info(f"   Type: {type(desc_field)}")
            if isinstance(desc_field, dict):
                logger.info(f"   Keys: {list(desc_field.keys())}")
                logger.info(f"   Content: {json.dumps(desc_field, indent=2, default=str)}")
            else:
                logger.info(f"   Value: {desc_field}")
                
            # Also debug renderedFields
            if 'renderedFields' in issue:
                logger.info(f"🔍 RenderedFields for {ticket_key}:")
                for field_key in sorted(issue['renderedFields'].keys()):
                    field_value = issue['renderedFields'][field_key]
                    if field_value is not None:
                        logger.info(f"   {field_key}: '{str(field_value)[:200]}{'...' if len(str(field_value)) > 200 else ''}'")
                        
        
        # Use Claude AI to extract/generate acceptance criteria from ticket text
        acceptance_criteria = _extract_acceptance_criteria_with_claude(
            ticket_key=issue['key'],
            summary=fields.get('summary', ''),
            description_text=_get_plain_text_description(fields),
            custom_fields=fields
        )
        
        # Get complete raw Jira text for description field
        description = ''
        
        # Priority 1: Try rendered HTML first (has formatting)
        if 'renderedFields' in issue and 'description' in issue['renderedFields']:
            description = issue['renderedFields']['description']
            logger.info(f"✅ Using rendered HTML description for {issue['key']}")
        # Priority 2: Parse ADF to readable text with formatting
        elif 'description' in fields and fields['description']:
            desc_field = fields['description']
            if isinstance(desc_field, dict) and 'content' in desc_field:
                description = _parse_adf_to_text(desc_field)
                logger.info(f"✅ Parsed ADF description for {issue['key']}")
            elif isinstance(desc_field, str):
                description = desc_field
                logger.info(f"✅ Using string description for {issue['key']}")
            else:
                description = str(desc_field) if desc_field else ''
                logger.info(f"⚠️ Using fallback description for {issue['key']}")
        else:
            logger.info(f"❌ No description found for {issue['key']}")
            
        # If we still don't have a description, create a fallback from available fields
        if not description and fields.get('summary'):
            description = f"**{fields['summary']}**\n\n_No detailed description available in this Jira ticket._"
        
        # Transform to AssignedTicket format
        ticket = {
            'ticket_id': issue['key'],
            'summary': fields.get('summary', ''),
            'description': description,
            'status': fields.get('status', {}).get('name', 'Unknown'),
            'priority': fields.get('priority', {}).get('name', 'Medium') if fields.get('priority') else 'Medium',
            'assignee': fields.get('assignee', {}).get('displayName', 'Unassigned') if fields.get('assignee') else 'Unassigned',
            'created_date': fields.get('created', ''),
            'updated_date': fields.get('updated', ''),
            'acceptance_criteria_count': len(acceptance_criteria),
            'acceptance_criteria': acceptance_criteria,
            'acceptanceCriteria': acceptance_criteria,  # Support both formats
            'related_prs': []
        }
        
        # Debug log the final ticket data
        logger.info(f"📋 Transformed Ticket: {ticket['ticket_id']}")
        logger.info(f"   Status: {ticket['status']}")
        logger.info(f"   Priority: {ticket['priority']}")
        logger.info(f"   AC Count: {ticket['acceptance_criteria_count']}")
        logger.info(f"   Description length: {len(ticket['description'])}")
        logger.info(f"   Description preview: {ticket['description'][:100] if ticket['description'] else 'No description'}...")
        
        return jsonify({
            'success': True,
            'ticket': ticket
        })
        
    except Exception as e:
        logger.error(f"Error fetching ticket {ticket_key}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/assigned-tickets', methods=['GET'])
def get_assigned_tickets():
    """Legacy endpoint - redirect to new Jira tickets endpoint"""
    return get_jira_tickets()


@app.route('/api/code-reviewer/start-review', methods=['POST'])
def start_review():
    """Start an enhanced code review session"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['branch_name', 'repository_path']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Missing required field: {field}'
                }), 400
        
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Extract configuration
        config = data.get('config', {})
        branch_name = data['branch_name']
        pr_url = data.get('pr_url')
        repository_path = data['repository_path']
        review_mode = data.get('review_mode', 'comprehensive')  # quick, full, ac-only, bug-analysis
        jira_ticket_data = data.get('jira_ticket')
        
        # Debug logging for received data
        logger.info(f"🎯 Starting review session:")
        logger.info(f"   Branch: {branch_name}")
        logger.info(f"   PR URL: {pr_url}")
        logger.info(f"   Review Mode: {review_mode}")
        logger.info(f"   Repository: {repository_path}")
        
        print(f"🔍 Raw request data: {data}")
        print(f"🔍 Jira ticket value: {jira_ticket_data}")
        print(f"🔍 Jira ticket type: {type(jira_ticket_data)}")
        
        if jira_ticket_data:
            logger.info(f"🎫 Jira Ticket received from Frontend:")
            logger.info(f"   Ticket ID: {jira_ticket_data.get('ticket_id', 'Unknown')}")
            logger.info(f"   Summary: {jira_ticket_data.get('summary', 'No summary')}")
            logger.info(f"   Description: {jira_ticket_data.get('description', 'No description')[:100]}...")
            print(f"🎫 Frontend Ticket Data: {jira_ticket_data.get('ticket_id')} - {jira_ticket_data.get('summary')}")
        else:
            logger.info("⚠️  No Jira ticket data received from Frontend")
            print("⚠️  No Jira ticket data received from Frontend")
            print("💡 Make sure you select both a PR AND a Ticket before starting the review!")
        
        # Create default config if not provided
        if not config:
            config = {
                "pr_config": {
                    "source": {
                        "type": "branch",
                        "value": branch_name,
                        "pr_url": pr_url
                    },
                    "repository": {
                        "path": repository_path,
                        "worktree_options": {
                            "create": True,
                            "cleanup_after": False
                        }
                    }
                },
                "jira_config": {
                    "enabled": False,  # Disable all automatic loading
                    "server_url": "https://regionalmedienaustria.atlassian.net", 
                    "ticket_data": jira_ticket_data,  # Use ticket data from frontend
                    "ticket_extraction": {
                        "auto_extract_from_branch": False,  # Disable auto-extraction
                        "branch_patterns": []
                    }
                },
                "review_config": {
                    "type": review_mode,
                    "focus_areas": _get_focus_areas_for_mode(review_mode),
                    "output": {
                        "include_live_progress": True,
                        "include_context": True
                    },
                    "claude_code_options": {
                        "max_turns": 20,
                        "timeout_seconds": 900,
                        "enhanced_thinking": True
                    }
                }
            }
        
        # Create review session
        review_session = ReviewSession(session_id, config, branch_name, pr_url)
        
        # Store Jira ticket data if provided
        if jira_ticket_data:
            review_session.jira_ticket_data = jira_ticket_data
        review_sessions[session_id] = review_session
        
        # Start review in background thread
        review_thread = threading.Thread(
            target=_execute_review_session,
            args=(review_session,),
            daemon=True
        )
        active_threads[session_id] = review_thread
        review_thread.start()
        
        logger.info(f"Started review session {session_id} for branch {branch_name}")
        
        return jsonify({
            'success': True,
            'session': {
                'session_id': session_id,
                'branch_name': branch_name,
                'status': 'initializing',
                'progress': 0,
                'progress_message': 'Review session started',
                'created_at': datetime.now().isoformat()
            },
            'message': f'Review session started for branch {branch_name}'
        })
        
    except Exception as e:
        logger.error(f"Error starting review: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/review-status/<session_id>', methods=['GET'])
def get_review_status(session_id: str):
    """Get the status of a review session"""
    try:
        if session_id not in review_sessions:
            return jsonify({
                'success': False,
                'error': 'Review session not found'
            }), 404
        
        session = review_sessions[session_id]
        return jsonify({
            'success': True,
            'session': session.to_dict()
        })
        
    except Exception as e:
        logger.error(f"Error getting review status: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/review-results/<session_id>', methods=['GET'])
def get_review_results(session_id: str):
    """Get the results of a completed review session"""
    try:
        if session_id not in review_sessions:
            return jsonify({
                'success': False,
                'error': 'Review session not found'
            }), 404
        
        session = review_sessions[session_id]
        
        if session.status != 'completed':
            return jsonify({
                'success': False,
                'error': f'Review not completed yet. Current status: {session.status}'
            }), 400
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'results': session.results
        })
        
    except Exception as e:
        logger.error(f"Error getting review results: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/sessions', methods=['GET'])
def list_review_sessions():
    """List all review sessions"""
    try:
        sessions = [session.to_dict() for session in review_sessions.values()]
        return jsonify({
            'success': True,
            'sessions': sessions,
            'total': len(sessions)
        })
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/parsing-metrics', methods=['GET'])
def get_parsing_metrics():
    """Get enhanced report parsing success metrics for monitoring"""
    try:
        # Count sessions with different parsing states
        total_sessions = len(review_sessions)
        successful_parsing = 0
        fallback_mode = 0
        failed_parsing = 0
        
        for session in review_sessions.values():
            if session.results:
                # Check if enhanced report exists and is valid
                enhanced_report = session.results.get('enhanced_report')
                parsing_metadata = session.results.get('parsing_metadata', {})
                
                if enhanced_report:
                    if enhanced_report.get('metadata', {}).get('fallback_mode') or parsing_metadata.get('fallback_mode'):
                        fallback_mode += 1
                    else:
                        successful_parsing += 1
                else:
                    failed_parsing += 1
        
        # Calculate success rate
        completed_sessions = successful_parsing + fallback_mode + failed_parsing
        success_rate = (successful_parsing / completed_sessions * 100) if completed_sessions > 0 else 0
        fallback_rate = (fallback_mode / completed_sessions * 100) if completed_sessions > 0 else 0
        
        return jsonify({
            'success': True,
            'metrics': {
                'total_sessions': total_sessions,
                'completed_sessions': completed_sessions,
                'successful_parsing': successful_parsing,
                'fallback_mode': fallback_mode,
                'failed_parsing': failed_parsing,
                'success_rate': round(success_rate, 1),
                'fallback_rate': round(fallback_rate, 1),
                'data_integrity_score': round(((successful_parsing + fallback_mode * 0.8) / completed_sessions * 100) if completed_sessions > 0 else 0, 1)
            },
            'status': {
                'healthy': success_rate >= 95,
                'warning': 80 <= success_rate < 95,
                'critical': success_rate < 80
            },
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting parsing metrics: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/score-statistics', methods=['GET'])
def get_score_statistics():
    """Get historical score statistics for monitoring and calibration"""
    try:
        stats = _get_score_statistics()
        
        return jsonify({
            'success': True,
            'statistics': stats,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting score statistics: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@app.route('/api/code-reviewer/score-monitoring', methods=['GET'])
def get_score_monitoring():
    """Get score distribution monitoring with sanity checks"""
    try:
        stats = _get_score_statistics()
        sanity_checks = _perform_score_sanity_checks(stats)
        
        return jsonify({
            'success': True,
            'statistics': stats,
            'sanity_checks': sanity_checks,
            'monitoring_status': _get_monitoring_status(sanity_checks),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error getting score monitoring: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


def _perform_score_sanity_checks(stats: Dict) -> Dict:
    """Perform sanity checks on score distribution"""
    checks = {
        'mean_score_check': {
            'status': 'healthy',
            'message': 'Mean score within normal range',
            'value': stats.get('mean_quality_score', 0)
        },
        'distribution_skew_check': {
            'status': 'healthy', 
            'message': 'Score distribution is balanced',
            'value': None
        },
        'duplication_trend_check': {
            'status': 'healthy',
            'message': 'Duplication levels normal',
            'value': stats.get('mean_duplication', 0)
        },
        'data_volume_check': {
            'status': 'healthy',
            'message': 'Sufficient data for analysis',
            'value': stats.get('total_reviews', 0)
        }
    }
    
    # Check 1: Mean score below threshold
    mean_score = stats.get('mean_quality_score', 0)
    if mean_score < 5.0:
        checks['mean_score_check'] = {
            'status': 'critical',
            'message': f'Mean score critically low: {mean_score:.1f}',
            'value': mean_score
        }
    elif mean_score < 6.5:
        checks['mean_score_check'] = {
            'status': 'warning',
            'message': f'Mean score below expected: {mean_score:.1f}',
            'value': mean_score
        }
    
    # Check 2: Distribution skew
    distribution = stats.get('score_distribution', {})
    total_reviews = stats.get('total_reviews', 0)
    
    if total_reviews > 10:  # Only check if we have enough data
        # Calculate percentage in each range
        low_scores = sum(distribution.get(str(i), 0) for i in range(0, 4))  # 0-3
        high_scores = sum(distribution.get(str(i), 0) for i in range(8, 11))  # 8-10
        
        low_percentage = (low_scores / total_reviews) * 100
        high_percentage = (high_scores / total_reviews) * 100
        
        if low_percentage > 30:
            checks['distribution_skew_check'] = {
                'status': 'critical',
                'message': f'Too many low scores: {low_percentage:.1f}%',
                'value': low_percentage
            }
        elif high_percentage > 70:
            checks['distribution_skew_check'] = {
                'status': 'warning', 
                'message': f'Distribution too high-skewed: {high_percentage:.1f}%',
                'value': high_percentage
            }
        else:
            checks['distribution_skew_check']['value'] = f'Low: {low_percentage:.1f}%, High: {high_percentage:.1f}%'
    
    # Check 3: Duplication trend
    mean_duplication = stats.get('mean_duplication', 0)
    if mean_duplication > 25:
        checks['duplication_trend_check'] = {
            'status': 'critical',
            'message': f'High duplication average: {mean_duplication:.1f}%',
            'value': mean_duplication
        }
    elif mean_duplication > 15:
        checks['duplication_trend_check'] = {
            'status': 'warning',
            'message': f'Elevated duplication: {mean_duplication:.1f}%', 
            'value': mean_duplication
        }
    
    # Check 4: Data volume
    if total_reviews < 5:
        checks['data_volume_check'] = {
            'status': 'warning',
            'message': f'Insufficient data: {total_reviews} reviews',
            'value': total_reviews
        }
    elif total_reviews == 0:
        checks['data_volume_check'] = {
            'status': 'critical',
            'message': 'No historical data available',
            'value': 0
        }
    
    return checks


def _get_monitoring_status(sanity_checks: Dict) -> Dict:
    """Determine overall monitoring status"""
    statuses = [check['status'] for check in sanity_checks.values()]
    
    if 'critical' in statuses:
        overall_status = 'critical'
        message = 'Critical issues detected in score distribution'
    elif 'warning' in statuses:
        overall_status = 'warning' 
        message = 'Some concerns with score patterns'
    else:
        overall_status = 'healthy'
        message = 'All score distribution checks passed'
    
    critical_count = statuses.count('critical')
    warning_count = statuses.count('warning')
    healthy_count = statuses.count('healthy')
    
    return {
        'overall_status': overall_status,
        'message': message,
        'summary': {
            'critical': critical_count,
            'warning': warning_count, 
            'healthy': healthy_count,
            'total_checks': len(sanity_checks)
        }
    }


def _get_focus_areas_for_mode(review_mode: str) -> List[str]:
    """Get focus areas based on review mode"""
    focus_areas_map = {
        'quick': ['code_quality', 'security'],
        'comprehensive': ['acceptance_criteria_compliance', 'code_quality', 'security', 'testing'],
        'ac-only': ['acceptance_criteria_compliance'],
        'bug-analysis': ['code_quality', 'testing', 'error_handling'],
        'security': ['security', 'input_validation', 'authentication'],
        'performance': ['performance', 'scalability', 'optimization']
    }
    
    return focus_areas_map.get(review_mode, ['code_quality', 'security'])


def _execute_review_session(session: ReviewSession):
    """Execute the review session in background thread"""
    try:
        session.status = 'running'
        session.progress = 10
        
        # Emit progress update via WebSocket
        socketio.emit('review_progress', {
            'session_id': session.session_id,
            'status': session.status,
            'progress': session.progress,
            'message': 'Initializing enhanced reviewer...'
        })
        
        # Process frontend Jira ticket data BEFORE creating reviewer
        if hasattr(session, 'jira_ticket_data') and session.jira_ticket_data:
            print(f"🎫 Processing Frontend Ticket: {session.jira_ticket_data.get('ticket_id')}")
            
            # Use ticket_converter.py to format the ticket data properly
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils'))
            try:
                from ticket_converter import AITicketConverter  # type: ignore
            except ImportError:
                logger.warning("ticket_converter module not available - skipping ticket conversion")
                print("⚠️ ticket_converter module not found - proceeding without conversion")
                AITicketConverter = None
            
            # Create raw ticket text from frontend data in natural format
            ticket_summary = session.jira_ticket_data.get('summary', '')
            ticket_description = session.jira_ticket_data.get('description', '')
            
            # Construct natural text format that ticket_converter expects
            raw_ticket_text = f"{ticket_summary}\n\n{ticket_description}".strip()
            
            print(f"📝 Raw Ticket Text for Conversion:")
            print(f"   Ticket ID: {session.jira_ticket_data.get('ticket_id')}")
            print(f"   Summary: {session.jira_ticket_data.get('summary')}")
            print(f"   Description: {session.jira_ticket_data.get('description', '')[:200]}...")
            
            # Convert using ticket_converter if available
            if AITicketConverter:
                converter = AITicketConverter(repo_path=session.config['pr_config']['repository']['path'])
                formatted_ticket_md = converter.convert_ticket_text(
                    raw_text=raw_ticket_text,
                    ticket_id=session.jira_ticket_data.get('ticket_id'),
                    analyze_codebase=True
                )
            else:
                # Fallback: create basic markdown without converter
                formatted_ticket_md = f"# Ticket: {session.jira_ticket_data.get('ticket_id')}\n\n## Summary\n{ticket_summary}\n\n## Description\n{ticket_description}"
            
            # Write the formatted ticket to a temporary file that the reviewer can use
            import os
            temp_ticket_file = os.path.join(os.path.dirname(__file__), 'frontend_ticket.md')
            with open(temp_ticket_file, 'w', encoding='utf-8') as f:
                f.write(formatted_ticket_md)
            
            logger.info(f"✅ Converted frontend ticket using ticket_converter: {session.jira_ticket_data.get('ticket_id')}")
            print(f"🎫 Frontend Ticket converted and saved to: {temp_ticket_file}")
            print(f"📄 Ticket file content preview:")
            print(f"   {formatted_ticket_md[:300]}...")
            
            # Update config to use the formatted ticket file BEFORE creating reviewer
            session.config['jira_config']['ticket_extraction']['manual_ticket_file'] = temp_ticket_file
            session.config['jira_config']['enabled'] = False  # Disable API, use file
            
            print(f"🔧 Updated config to use ticket file: {temp_ticket_file}")
        else:
            print(f"❌ No Frontend Ticket Data found - session has jira_ticket_data: {hasattr(session, 'jira_ticket_data')}")
        
        # Initialize Enhanced Claude Reviewer (with ticket file already configured)
        reviewer = EnhancedClaudeReviewer(
            config=session.config,
            branch_name=session.branch_name,
            pr_url=session.pr_url,
            repo_path=session.config['pr_config']['repository']['path']
        )
        
        # If we processed frontend ticket data, explicitly load the ticket now
        if hasattr(session, 'jira_ticket_data') and session.jira_ticket_data:
            temp_ticket_file = os.path.join(os.path.dirname(__file__), 'frontend_ticket.md')
            print(f"🔄 Forcing ticket reload from: {temp_ticket_file}")
            
            # Force reload the ticket from the created file
            reviewer.current_ticket = reviewer.jira_integration.get_ticket("CMS20-1251")
            
            if reviewer.current_ticket:
                print(f"✅ Ticket successfully loaded after frontend processing!")
                print(f"🎫 Ticket: {reviewer.current_ticket.ticket_id} - {reviewer.current_ticket.summary}")
                print(f"📋 AC Count: {reviewer.current_ticket.get_acceptance_criteria_count()}")
            else:
                print(f"❌ Failed to load ticket even after processing frontend data")
        
        session.reviewer = reviewer
        session.progress = 25
        
        # Update worktree path if created
        if hasattr(reviewer, 'pr_analyzer') and reviewer.pr_analyzer and reviewer.pr_analyzer.worktree_path:
            session.worktree_path = str(reviewer.pr_analyzer.worktree_path)
        
        socketio.emit('review_progress', {
            'session_id': session.session_id,
            'status': session.status,
            'progress': session.progress,
            'message': f'Worktree setup completed: {session.worktree_path or "using main repository"}',
            'worktree_path': session.worktree_path
        })
        
        # Perform enhanced review
        session.progress = 50
        socketio.emit('review_progress', {
            'session_id': session.session_id,
            'status': session.status,
            'progress': session.progress,
            'message': 'Starting enhanced code review with Claude...'
        })
        
        # Generate enhanced review based on mode
        review_mode = session.config.get('review_config', {}).get('type', 'comprehensive')
        
        if review_mode == 'ac-only':
            results = _perform_ac_only_review(reviewer, session)
        elif review_mode == 'bug-analysis':
            results = _perform_bug_analysis_review(reviewer, session)
        else:
            results = _perform_comprehensive_review(reviewer, session)
        
        session.progress = 90
        socketio.emit('review_progress', {
            'session_id': session.session_id,
            'status': session.status,
            'progress': session.progress,
            'message': 'Processing review results...'
        })
        
        # Structure the results for UI consumption
        session.results = _structure_review_results(results, reviewer, session)
        session.progress = 100
        session.status = 'completed'
        session.completed_at = datetime.now()
        
        socketio.emit('review_completed', {
            'session_id': session.session_id,
            'status': session.status,
            'progress': session.progress,
            'message': 'Review completed successfully!',
            'results_available': True
        })
        
        logger.info(f"Review session {session.session_id} completed successfully")
        
    except Exception as e:
        session.status = 'error'
        session.error = str(e)
        logger.error(f"Review session {session.session_id} failed: {e}")
        
        socketio.emit('review_error', {
            'session_id': session.session_id,
            'status': session.status,
            'error': str(e),
            'message': f'Review failed: {str(e)}'
        })
    
    finally:
        # Cleanup thread reference
        if session.session_id in active_threads:
            del active_threads[session.session_id]


def _perform_comprehensive_review(reviewer: EnhancedClaudeReviewer, session: ReviewSession) -> str:
    """Perform comprehensive review with all focus areas"""
    try:
        # Create WebSocket callback function
        def websocket_progress_callback(data):
            socketio.emit('claude_progress', {
                'session_id': session.session_id,
                **data
            })
        
        return reviewer._run_claude_command_with_progress(
            reviewer.generate_enhanced_review_prompt('comprehensive'),
            timeout=900,
            max_turns=25,
            websocket_callback=websocket_progress_callback
        )
    except Exception as e:
        logger.error(f"Comprehensive review failed: {e}")
        raise


def _perform_ac_only_review(reviewer: EnhancedClaudeReviewer, session: ReviewSession) -> str:
    """Perform acceptance criteria only review"""
    try:
        # Create WebSocket callback function
        def websocket_progress_callback(data):
            socketio.emit('claude_progress', {
                'session_id': session.session_id,
                **data
            })
        
        prompt = reviewer.generate_enhanced_review_prompt('comprehensive', include_business_context=True)
        # Add AC-focused instruction
        ac_prompt = prompt + "\n\nFOKUS: Analysiere ausschließlich die Acceptance Criteria Compliance. Ignoriere Code-Qualität und Security Issues."
        
        return reviewer._run_claude_command_with_progress(
            ac_prompt,
            timeout=600,
            max_turns=15,
            websocket_callback=websocket_progress_callback
        )
    except Exception as e:
        logger.error(f"AC-only review failed: {e}")
        raise


def _perform_bug_analysis_review(reviewer: EnhancedClaudeReviewer, session: ReviewSession) -> str:
    """Perform bug analysis focused review"""
    try:
        # Create WebSocket callback function
        def websocket_progress_callback(data):
            socketio.emit('claude_progress', {
                'session_id': session.session_id,
                **data
            })
        
        return reviewer._run_claude_command_with_progress(
            reviewer.generate_review_prompt('bugs'),
            timeout=600,
            max_turns=20,
            websocket_callback=websocket_progress_callback
        )
    except Exception as e:
        logger.error(f"Bug analysis review failed: {e}")
        raise


def _structure_review_results(raw_results: str, reviewer: EnhancedClaudeReviewer, session: ReviewSession) -> Dict:
    """Structure raw review results for UI consumption"""
    try:
        # Get additional context
        changed_files = reviewer._get_changed_files_list() if hasattr(reviewer, '_get_changed_files_list') else []
        diff_summary = reviewer._get_git_diff_summary() if hasattr(reviewer, '_get_git_diff_summary') else ""
        
        # Get Jira ticket info if available
        ticket_info = None
        if reviewer.current_ticket:
            ticket_info = {
                'ticket_id': reviewer.current_ticket.ticket_id,
                'summary': reviewer.current_ticket.summary,
                'status': reviewer.current_ticket.status,
                'acceptance_criteria_count': reviewer.current_ticket.get_acceptance_criteria_count(),
                'acceptance_criteria': getattr(reviewer.current_ticket, 'acceptance_criteria', []) if reviewer.current_ticket else []
            }
        
        result = {
            'session_id': session.session_id,
            'review_mode': session.config.get('review_config', {}).get('type', 'comprehensive'),
            'branch_name': session.branch_name,
            'pr_url': session.pr_url,
            'worktree_path': session.worktree_path,
            'timestamp': datetime.now().isoformat(),
            
            # Raw review content
            'raw_review': raw_results,
            
            # Structured metadata
            'metadata': {
                'changed_files': changed_files,
                'diff_summary': diff_summary,
                'file_count': len(changed_files) if changed_files else 0
            },
            
            # Jira integration
            'jira_ticket': ticket_info,
        }
        
        # Parse structured findings from Claude output
        structured_findings = _parse_structured_findings(raw_results)
        result['structured_findings'] = structured_findings
        
        # Parse enhanced report sections for comprehensive view with fallback
        try:
            enhanced_report = _parse_enhanced_report(raw_results, ticket_info, structured_findings, session.session_id)
            result['enhanced_report'] = enhanced_report
            logger.info("Enhanced report parsing completed successfully")
            
            # Save historical scores for tracking and monitoring
            if enhanced_report and 'code_quality_analysis' in enhanced_report:
                quality_score = enhanced_report['code_quality_analysis']['executive_summary'].get('overall_score', 8)
                duplication_level = enhanced_report['code_quality_analysis']['executive_summary'].get('duplication_level', 0)
                _save_historical_score(session.session_id, session.branch_name, quality_score, duplication_level)
            
            # Save historical effort data for tracking and calibration
            if enhanced_report and 'effort_estimation' in enhanced_report:
                effort_data = enhanced_report['effort_estimation']
                estimated_hours = effort_data.get('calculated_hours', 0)
                complexity_metrics = effort_data.get('complexity_metrics', {})
                _save_historical_effort(session.session_id, session.branch_name, estimated_hours, 
                                      complexity_metrics=complexity_metrics)
        except Exception as e:
            # Structured error logging for parsing pattern analysis
            error_details = {
                'error_type': type(e).__name__,
                'error_message': str(e),
                'raw_results_length': len(raw_results),
                'raw_results_preview': raw_results[:500] if raw_results else '',
                'ticket_info_available': bool(ticket_info),
                'session_id': session.session_id,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.error(f"Enhanced report parsing failed: {error_details}")
            logger.warning("Falling back to minimal viable enhanced report")
            
            # Create fallback enhanced report to maintain UI functionality
            fallback_report = _create_fallback_enhanced_report(
                raw_results, 
                ticket_info, 
                error_info=f"{type(e).__name__}: {str(e)}",
                session_id=session.session_id
            )
            result['enhanced_report'] = fallback_report
            
            # Add parsing failure metadata for monitoring
            result['parsing_metadata'] = {
                'enhanced_report_parsing_failed': True,
                'fallback_mode': True,
                'error_details': error_details,
                'parsing_success_rate': 0.0  # Failed this time
            }
        
        # Debug logging
        logger.info(f"Raw results length: {len(raw_results)}")
        logger.info(f"Structured findings summary: {[(k, len(v)) for k, v in structured_findings.items()]}")
        if not any(findings for findings in structured_findings.values()):
            logger.warning(f"No structured findings parsed from raw results. First 500 chars: {raw_results[:500]}")
        else:
            logger.info(f"Successfully parsed {sum(len(findings) for findings in structured_findings.values())} total findings")
        
        # Summary stats using structured findings
        result['summary'] = _generate_review_summary(raw_results, changed_files, structured_findings)
        
        return result
        
    except Exception as e:
        logger.error(f"Error structuring results: {e}")
        return {
            'error': f'Failed to structure results: {str(e)}',
            'raw_review': raw_results
        }


def _parse_structured_findings(raw_results: str) -> Dict:
    """Parse structured findings from Claude review output using intelligent pattern matching"""
    import re
    
    findings = {
        'acceptance_criteria': [],
        'code_quality': [],
        'security_issues': [],
        'performance_issues': [],
        'bugs': [],
        'suggestions': []
    }
    
    # Enhanced parsing with multiple strategies
    lines = raw_results.split('\n')
    current_section = None
    current_finding = {}
    
    # Patterns for detecting different types of content
    section_patterns = {
        'acceptance_criteria': [
            r'acceptance\s+criteria', r'ac\s+compliance', r'user\s+story', r'requirements?'
        ],
        'security_issues': [
            r'security', r'vulnerability', r'authentication', r'authorization', r'credential', 
            r'injection', r'xss', r'csrf', r'secrets?', r'password'
        ],
        'performance_issues': [
            r'performance', r'optimization', r'slow', r'memory', r'cpu', r'cache', 
            r'async', r'blocking', r'bottleneck'
        ],
        'bugs': [
            r'bug', r'error', r'exception', r'crash', r'fail', r'race\s+condition', 
            r'null\s+pointer', r'undefined', r'memory\s+leak'
        ],
        'code_quality': [
            r'code\s+quality', r'refactor', r'clean\s+code', r'maintainability', 
            r'readability', r'complexity', r'duplication', r'pattern', r'structure'
        ],
        'suggestions': [
            r'suggestion', r'recommend', r'consider', r'improvement', r'enhancement', 
            r'best\s+practice', r'tip'
        ]
    }
    
    file_pattern = r'(?:in\s+|file\s+|`)?([a-zA-Z0-9/_.-]+\.[a-zA-Z]{1,4})(?:`|:|\s|$)'
    line_pattern = r'line\s*(?:number\s*)?(\d+)|:(\d+)(?::|$)'
    severity_pattern = r'\b(critical|high|medium|low|minor|major)\b'
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        # Detect section headers (improved)
        line_lower = line.lower()
        detected_section = None
        
        for section_key, patterns in section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line_lower):
                    detected_section = section_key
                    break
            if detected_section:
                break
        
        # If we found a clear section header, switch context
        if detected_section and any(marker in line for marker in ['##', '###', '**', '__', ':']):
            current_section = detected_section
            continue
        
        # Extract findings from bullet points and numbered lists
        bullet_match = re.match(r'^[\s]*[-*•]\s*(.+)$', line)
        number_match = re.match(r'^[\s]*\d+\.\s*(.+)$', line)
        
        if bullet_match or number_match:
            finding_text = bullet_match.group(1) if bullet_match else (number_match.group(1) if number_match else "")
            
            # Auto-categorize if no current section
            if not current_section:
                current_section = _auto_categorize_finding(finding_text, section_patterns)
            
            # Extract metadata
            file_match = re.search(file_pattern, finding_text)
            line_match = re.search(line_pattern, finding_text)
            severity_match = re.search(severity_pattern, finding_text.lower())
            
            finding = {
                'text': finding_text.strip(),
                'severity': severity_match.group(1) if severity_match else _infer_severity(finding_text),
            }
            
            if file_match:
                finding['file'] = file_match.group(1)
            
            if line_match:
                finding['line'] = int(line_match.group(1) or line_match.group(2))
            
            # Look for suggestion in next few lines
            suggestion = _extract_suggestion(lines, i)
            if suggestion:
                finding['suggestion'] = suggestion
            
            if current_section and current_section in findings:
                findings[current_section].append(finding)
            else:
                # Default to suggestions if unsure
                findings['suggestions'].append(finding)
    
    # Post-process to clean up and validate findings
    for category in findings:
        findings[category] = _clean_findings(findings[category])
    
    return findings


def _auto_categorize_finding(text: str, section_patterns: Dict) -> str:
    """Automatically categorize a finding based on its content"""
    text_lower = text.lower()
    
    # Score each category
    scores = {}
    for category, patterns in section_patterns.items():
        score = 0
        for pattern in patterns:
            matches = len(re.findall(pattern, text_lower))
            score += matches
        scores[category] = score
    
    # Return category with highest score, or 'suggestions' as default
    if scores:
        max_category = max(scores, key=lambda x: scores.get(x, 0))
        return max_category if scores[max_category] > 0 else 'suggestions'
    
    return 'suggestions'


def _infer_severity(text: str) -> str:
    """Infer severity based on content and keywords"""
    text_lower = text.lower()
    
    high_keywords = [
        'critical', 'security', 'vulnerability', 'sql injection', 'xss', 'csrf',
        'password', 'secret', 'authentication', 'race condition', 'crash',
        'memory leak', 'fail', 'error', 'exception', 'bug'
    ]
    
    medium_keywords = [
        'performance', 'slow', 'optimization', 'refactor', 'maintainability',
        'complexity', 'duplication', 'warning', 'issue'
    ]
    
    low_keywords = [
        'suggestion', 'consider', 'improvement', 'style', 'formatting',
        'comment', 'documentation', 'naming', 'minor'
    ]
    
    if any(keyword in text_lower for keyword in high_keywords):
        return 'high'
    elif any(keyword in text_lower for keyword in medium_keywords):
        return 'medium'
    elif any(keyword in text_lower for keyword in low_keywords):
        return 'low'
    else:
        return 'medium'  # default


def _extract_suggestion(lines: List[str], current_index: int) -> Optional[str]:
    """Extract suggestion from following lines"""
    suggestion_keywords = ['suggest', 'recommend', 'should', 'consider', 'fix', 'solution']
    
    # Look in next 3 lines for suggestions
    for i in range(current_index + 1, min(current_index + 4, len(lines))):
        line = lines[i].strip()
        if not line:
            continue
        
        line_lower = line.lower()
        if any(keyword in line_lower for keyword in suggestion_keywords):
            # Clean up the suggestion
            suggestion = re.sub(r'^[\s\-\*•]*', '', line)
            suggestion = re.sub(r'^(suggestion|recommend|should|consider)[:.]?\s*', '', suggestion, flags=re.IGNORECASE)
            return suggestion.strip()
    
    return None


def _clean_findings(findings: List[Dict]) -> List[Dict]:
    """Clean and validate findings"""
    cleaned = []
    
    for finding in findings:
        # Skip very short or empty findings
        if not finding.get('text') or len(finding['text']) < 10:
            continue
        
        # Normalize severity
        if finding.get('severity') not in ['high', 'medium', 'low']:
            finding['severity'] = 'medium'
        
        # Clean up text
        finding['text'] = finding['text'].strip()
        
        cleaned.append(finding)
    
    return cleaned


def _extract_severity(text: str) -> str:
    """Extract severity level from finding text"""
    text_lower = text.lower()
    if any(word in text_lower for word in ['critical', 'severe', 'high']):
        return 'high'
    elif any(word in text_lower for word in ['medium', 'moderate']):
        return 'medium'
    elif any(word in text_lower for word in ['low', 'minor']):
        return 'low'
    else:
        return 'medium'  # default


def _extract_file_reference(text: str) -> Optional[str]:
    """Extract file reference from finding text"""
    # Enhanced pattern matching for file references including line numbers
    import re
    patterns = [
        r'`([^`]+\.[a-zA-Z]+(?::\d+(?:-\d+)?)?)`',  # `filename.ext:123-456` or `filename.ext:123`
        r'Code-Implementierung:\s*`([^`]+)`',  # Code-Implementierung: `filename.ext:123-456`
        r'in\s+([^\s]+\.[a-zA-Z]+(?::\d+)?)',  # in filename.ext:123
        r'file\s+([^\s]+\.[a-zA-Z]+(?::\d+)?)',  # file filename.ext:123
        r'([a-zA-Z0-9/_-]+\.[a-zA-Z]+:\d+(?:-\d+)?)',  # direct file:line references
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            return match.group(1)
    
    return None


def _extract_line_number(file_ref: str) -> Optional[int]:
    """Extract line number from file reference"""
    import re
    match = re.search(r':(\d+)', file_ref)
    return int(match.group(1)) if match else None


def _parse_table_row(line: str) -> Optional[Dict]:
    """Parse a markdown table row into structured data"""
    # Handle markdown table format: | col1 | col2 | col3 | col4 | col5 | col6 |
    if '|' not in line or line.strip().startswith('|---'):
        return None
    
    parts = [part.strip() for part in line.split('|')[1:-1]]  # Remove empty first/last elements
    if len(parts) < 4:
        return None
    
    return {
        'concept': parts[0] if len(parts) > 0 else '',
        'file': parts[1] if len(parts) > 1 else '',
        'variable_name': parts[2] if len(parts) > 2 else '',
        'pattern': parts[3] if len(parts) > 3 else '',
        'status': 'consistent' if parts[4].strip() in ['✅', 'good'] else 'inconsistent' if parts[4].strip() in ['⚠️', 'mixed'] else 'needs_review',
        'suggestion': parts[5] if len(parts) > 5 and parts[5] != '-' else None,
        'line_number': _extract_line_number(parts[1]) if len(parts) > 1 else None
    }


def _create_fallback_enhanced_report(raw_results: str, ticket_info: Optional[Dict] = None, error_info: Optional[str] = None, session_id: str = 'fallback') -> Dict:
    """Create a minimal viable enhanced report when parsing fails"""
    import re
    from datetime import datetime
    
    # Extract basic stats from raw results for fallback
    lines = raw_results.split('\n')
    
    # Basic pattern matching for fallback data
    bug_count = len([line for line in lines if re.search(r'\b(bug|error|critical|fail)\b', line.lower())])
    security_count = len([line for line in lines if re.search(r'\b(security|vulnerability|auth)\b', line.lower())])
    quality_count = len([line for line in lines if re.search(r'\b(quality|refactor|clean)\b', line.lower())])
    
    # Create minimal AC analysis if Jira ticket available
    ac_analysis = {
        'executive_summary': {
            'total_ac': 0,
            'fulfilled': 0,
            'partially_fulfilled': 0,
            'not_fulfilled': 0,
            'compliance_rate': 0,
            'business_alignment_score': 5
        },
        'detailed_results': []
    }
    
    if ticket_info and 'acceptance_criteria' in ticket_info:
        total_ac = len(ticket_info['acceptance_criteria'])
        ac_analysis['executive_summary']['total_ac'] = total_ac
        
        # Basic AC analysis from raw results
        for i, ac in enumerate(ticket_info['acceptance_criteria']):
            ac_id = f"ac_{i+1}"
            # Simple pattern matching for AC status
            if re.search(rf'ac\s*{i+1}.*(?:completed|implemented|fulfilled|✅)', raw_results, re.IGNORECASE):
                status = 'fulfilled'
                ac_analysis['executive_summary']['fulfilled'] += 1
            elif re.search(rf'ac\s*{i+1}.*(?:partial|progress|⚠️)', raw_results, re.IGNORECASE):
                status = 'partially_fulfilled'
                ac_analysis['executive_summary']['partially_fulfilled'] += 1
            else:
                status = 'not_fulfilled'
                ac_analysis['executive_summary']['not_fulfilled'] += 1
            
            ac_analysis['detailed_results'].append({
                'id': ac_id,
                'text': str(ac),
                'status': status,
                'implementation_evidence': 'Fallback analysis - limited parsing available',
                'issues': ['Enhanced parsing failed - showing basic analysis'] if error_info else []
            })
        
        # Calculate compliance rate
        fulfilled = ac_analysis['executive_summary']['fulfilled']
        if total_ac > 0:
            ac_analysis['executive_summary']['compliance_rate'] = round((fulfilled / total_ac * 100), 1)
            ac_analysis['executive_summary']['business_alignment_score'] = min(10, round((fulfilled / total_ac * 10)))
    
    fallback_report = {
        'metadata': {
            'generated_at': datetime.now().isoformat(),
            'review_type': 'fallback',
            'parsing_error': error_info,
            'fallback_mode': True
        },
        'acceptance_criteria_analysis': ac_analysis,
        'code_quality_analysis': {
            'executive_summary': {
                'overall_score': _calculate_quality_score_with_claude(session_id, raw_results, {}).get('quality_score', 6),  # AI-calculated score
                'critical_issues': bug_count,
                'code_smells': quality_count,
                'duplication_level': _detect_code_duplication_with_claude(session_id, raw_results).get('duplication_percentage', 3)
            },
            'code_duplication': [],
            'complexity_issues': [],
            'naming_consistency': []
        },
        'bug_detection_results': {
            'critical_bugs': [{
                'text': f'Enhanced parsing failed - {bug_count} potential issues detected in raw output',
                'severity': 'medium',
                'file': None,
                'type': 'parsing_fallback'
            }] if bug_count > 0 else [],
            'logic_errors': [],
            'runtime_risks': []
        },
        'architectural_assessment': {
            'design_patterns': [],
            'integration_quality': [],
            'violations': []
        },
        'security_performance': {
            'security_findings': [{
                'text': f'Enhanced parsing failed - {security_count} potential security references detected',
                'severity': 'medium',
                'category': 'parsing_fallback'
            }] if security_count > 0 else [],
            'performance_analysis': []
        },
        'action_items': {
            'critical': [{
                'text': 'Enhanced report parsing failed - review raw output for complete analysis',
                'priority': 'critical',
                'category': 'parsing_error'
            }],
            'important': [],
            'suggestions': []
        },
        'variable_parameter_analysis': {
            'executive_summary': {
                'total_variables': 0,
                'naming_issues': 0,
                'scope_issues': 0,
                'consistency_score': 5
            },
            'naming_analysis': [],
            'scope_analysis': [],
            'type_consistency': []
        },
        'next_steps': {
            'priority_assessment': {
                'critical_blockers': 1,  # Parsing failure is a blocker
                'high_priority': bug_count,
                'medium_priority': quality_count,
                'can_merge': False,  # Conservative approach
                'estimated_effort': 'Review required - parsing failed'
            },
            'immediate_actions': [{
                'action': 'Review raw Claude output due to enhanced parsing failure',
                'category': 'parsing_error',
                'priority': 'critical',
                'effort': '30 minutes',
                'description': 'Enhanced report parsing failed - manual review of raw output recommended'
            }],
            'follow_up_tasks': [],
            'merge_readiness': {
                'status': 'needs_review',
                'blockers': ['Enhanced parsing failed'],
                'recommendations': ['Manual review of raw output', 'Check parsing patterns']
            },
            'post_merge_actions': []
        },
        'questions_clarifications': [
            'Enhanced report parsing failed - review raw output for complete analysis'
        ]
    }
    
    return fallback_report


def _extract_json_from_response(response_text: str) -> str:
    """Extract JSON content from Claude's markdown-formatted response"""
    import re
    
    # Remove markdown code block markers
    response_text = response_text.strip()
    
    # Pattern to match ```json ... ``` blocks
    json_pattern = r'```json\s*(.*?)\s*```'
    match = re.search(json_pattern, response_text, re.DOTALL | re.IGNORECASE)
    
    if match:
        json_content = match.group(1).strip()
        logger.info(f"📝 Extracted JSON from markdown block: {len(json_content)} chars")
        return json_content
    
    # Try to find JSON object directly (fallback)
    json_object_pattern = r'\{.*\}'
    match = re.search(json_object_pattern, response_text, re.DOTALL)
    
    if match:
        json_content = match.group(0).strip()
        logger.info(f"📝 Extracted raw JSON object: {len(json_content)} chars")
        return json_content
    
    # If no JSON found, return original (will likely fail parsing)
    logger.warning(f"⚠️ No JSON pattern found in response, returning original")
    return response_text


def _calculate_quality_score_with_claude(session_id: str, raw_results: str, structured_findings: Dict, changed_files: Optional[List[str]] = None) -> float:
    """Calculate realistic quality score using Claude Code AI analysis
    
    Uses Claude Code to analyze code quality based on multiple factors:
    - Code maintainability and readability
    - Test coverage and documentation
    - Architecture and design patterns
    - Performance and security considerations
    - Bug density and severity
    
    Returns a score between 0 and 10 based on AI analysis
    """
    import subprocess
    import json
    import tempfile
    import os
    
    try:
        # Create analysis prompt for Claude Code
        prompt = f"""
Analysiere diese Code-Review Ergebnisse und berechne einen realistischen Code Quality Score (1-10):

## Code Review Findings:
{raw_results[:3000] if raw_results else 'No detailed review available'}...

## Structured Findings Summary:
- Bugs: {len(structured_findings.get('bugs', []))}
- Security Issues: {len(structured_findings.get('security_issues', []))}
- Performance Issues: {len(structured_findings.get('performance_issues', []))}
- Code Quality Issues: {len(structured_findings.get('code_quality', []))}
- Total Findings: {sum(len(findings) for findings in structured_findings.values())}

## Changed Files:
{', '.join(changed_files[:10]) if changed_files else 'No file information available'}

**Deine Aufgabe:**
Bewerte die Code-Qualität (1-10) basierend auf:
1. **Kritische Bugs** (schwere Abzüge: -3 bis -5 Punkte)
2. **Security Vulnerabilities** (schwere Abzüge: -2 bis -4 Punkte)
3. **Code Maintainability** (Lesbarkeit, Struktur: -1 bis -3 Punkte)
4. **Test Coverage** (fehlende Tests: -1 bis -2 Punkte)
5. **Documentation** (fehlende Docs: -0.5 bis -1 Punkt)
6. **Performance Issues** (Performance Probleme: -1 bis -2 Punkte)
7. **Code Smells** (kleinere Probleme: -0.5 bis -1 Punkt)

**Scoring Guidelines:**
- 9-10: Exzellente Qualität, minimale Issues
- 7-8: Gute Qualität, kleinere Verbesserungen nötig
- 5-6: Durchschnittliche Qualität, mehrere Issues
- 3-4: Schlechte Qualität, viele kritische Issues
- 1-2: Sehr schlechte Qualität, schwerwiegende Probleme

**WICHTIG:** Antworte NUR mit einem JSON-Objekt:
{{
  "quality_score": 7.5,
  "reasoning": "Kurze Begründung für den Score",
  "main_issues": ["Liste der Hauptprobleme"],
  "strengths": ["Liste der positiven Aspekte"]
}}

**Antwort:**"""
        
        # Call Claude Code CLI
        result = subprocess.run([
            'claude'
        ], input=prompt, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            response_text = result.stdout.strip()
            logger.info(f"🤖 Claude quality analysis response: {response_text[:200]}...")
            
            try:
                # Extract JSON from markdown code block if present
                json_text = _extract_json_from_response(response_text)
                
                # Parse Claude's JSON response
                data = json.loads(json_text)
                quality_score = float(data.get('quality_score', 6.0))
                
                # Validate score range
                if 0 <= quality_score <= 10:
                    logger.info(f"✅ Claude calculated quality score: {quality_score}")
                    return {
                        'quality_score': quality_score,
                        'claude_analysis': True,
                        'calculation_method': 'claude_ai_analysis',
                        'ai_reasoning': data.get('reasoning', ''),
                        'main_issues': data.get('main_issues', []),
                        'strengths': data.get('strengths', []),
                        'analysis_timestamp': datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ Claude returned invalid score: {quality_score}, using fallback")
                    return _calculate_quality_score_fallback(structured_findings)
                    
            except json.JSONDecodeError:
                logger.warning(f"⚠️ Claude response not valid JSON, using fallback")
                return _calculate_quality_score_fallback(structured_findings)
        else:
            logger.error(f"❌ Claude Code command failed: {result.stderr}")
            return _calculate_quality_score_fallback(structured_findings)
            
    except Exception as e:
        logger.error(f"❌ Error in Claude quality score calculation: {e}")
        return _calculate_quality_score_fallback(structured_findings)


def _calculate_quality_score_fallback(structured_findings: Dict) -> Dict:
    """Fallback quality score calculation when Claude is unavailable"""
    high_count = sum(1 for finding in [f for findings in structured_findings.values() for f in findings] 
                    if finding.get('severity', 'low').lower() in ['high', 'critical'])
    medium_count = sum(1 for finding in [f for findings in structured_findings.values() for f in findings] 
                      if finding.get('severity', 'low').lower() == 'medium')
    low_count = sum(1 for finding in [f for findings in structured_findings.values() for f in findings] 
                   if finding.get('severity', 'low').lower() == 'low')
    
    # Simple fallback calculation
    score = 10 - (high_count * 3 + medium_count * 1.5 + low_count * 0.5)
    final_score = max(0, min(10, score))
    
    return {
        'quality_score': final_score,
        'claude_analysis': False,
        'calculation_method': 'mathematical_fallback',
        'fallback_reason': 'Claude Code CLI unavailable or failed',
        'fallback_timestamp': datetime.now().isoformat(),
        'calculation_details': {
            'high_issues': high_count,
            'medium_issues': medium_count,
            'low_issues': low_count,
            'formula': f'10 - ({high_count}×3 + {medium_count}×1.5 + {low_count}×0.5) = {final_score:.1f}'
        }
    }


def _detect_code_duplication_with_claude(session_id: str, raw_results: str, changed_files: Optional[List[str]] = None) -> Dict:
    """Detect code duplication using Claude Code AI analysis
    
    Uses Claude Code to analyze semantic code duplication:
    - Identical code blocks
    - Copy-paste patterns with minor modifications
    - Similar logic structures
    - Redundant implementations
    
    Returns duplication percentage (0-100)
    """
    import subprocess
    import json
    
    try:
        # Create duplication analysis prompt
        prompt = f"""
Analysiere diese geänderten Dateien auf Code-Duplikation und semantische Ähnlichkeiten:

## Review Ergebnisse:
{raw_results[:2000] if raw_results else 'No review data available'}...

## Geänderte Dateien:
{', '.join(changed_files[:15]) if changed_files else 'No file list available'}

**Deine Aufgabe:**
1. **Exakte Duplikation** - Identische Code-Blöcke (>5 Zeilen)
2. **Semantische Ähnlichkeit** - Copy-Paste mit kleinen Änderungen
3. **Redundante Logic** - Ähnliche Funktionalität unterschiedlich implementiert
4. **Pattern Duplikation** - Wiederholte Code-Patterns

**Analysiere systematisch:**
- Verwende die View und Grep Tools um Code zu analysieren
- Suche nach ähnlichen Funktionen und Logic
- Erkenne Copy-Paste Patterns
- Bewerte semantische Ähnlichkeiten

**Bewertung:**
- 0-5%: Minimale oder keine Duplikation
- 5-15%: Leichte Duplikation, akzeptabel
- 15-30%: Moderate Duplikation, sollte reduziert werden
- 30-50%: Hohe Duplikation, Refactoring nötig
- 50%+: Kritische Duplikation, sofortiges Refactoring

**WICHTIG:** Antworte NUR mit einem JSON-Objekt:
{{
  "duplication_percentage": 25,
  "analysis": "Kurze Zusammenfassung der gefundenen Duplikation",
  "duplicated_blocks": [
    {{
      "files": ["file1.ts:10-20", "file2.ts:30-40"],
      "similarity": 95,
      "description": "Identische Error-Handling Logic"
    }}
  ],
  "refactoring_recommendations": ["Konkrete Refactoring-Vorschläge"]
}}

**Antwort:**"""
        
        # Call Claude Code CLI for analysis
        result = subprocess.run([
            'claude'
        ], input=prompt, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            response_text = result.stdout.strip()
            logger.info(f"🔍 Claude duplication analysis: {response_text[:200]}...")
            
            try:
                # Extract JSON from markdown code block if present
                json_text = _extract_json_from_response(response_text)
                
                data = json.loads(json_text)
                duplication_percentage = int(data.get('duplication_percentage', 0))
                
                # Validate percentage range
                if 0 <= duplication_percentage <= 100:
                    logger.info(f"✅ Claude found {duplication_percentage}% code duplication")
                    return {
                        'duplication_percentage': duplication_percentage,
                        'claude_analysis': True,
                        'calculation_method': 'claude_ai_analysis',
                        'analysis_summary': data.get('analysis', ''),
                        'duplicated_blocks': data.get('duplicated_blocks', []),
                        'refactoring_recommendations': data.get('refactoring_recommendations', []),
                        'analysis_timestamp': datetime.now().isoformat()
                    }
                else:
                    logger.warning(f"⚠️ Invalid duplication percentage: {duplication_percentage}")
                    return _detect_code_duplication_fallback(raw_results)
                    
            except json.JSONDecodeError:
                logger.warning(f"⚠️ Claude duplication response not valid JSON")
                return _detect_code_duplication_fallback(raw_results)
        else:
            logger.error(f"❌ Claude duplication analysis failed: {result.stderr}")
            return _detect_code_duplication_fallback(raw_results)
            
    except Exception as e:
        logger.error(f"❌ Error in Claude duplication detection: {e}")
        return _detect_code_duplication_fallback(raw_results)


def _detect_code_duplication_fallback(raw_results: str) -> Dict:
    """Fallback duplication detection using text analysis"""
    import re
    
    duplication_indicators = {
        r'significant\s+duplication': 30,
        r'substantial\s+duplication': 25,
        r'moderate\s+duplication': 15,
        r'some\s+duplication': 10,
        r'minimal\s+duplication': 5,
        r'DRY\s+principle\s+violated': 20,
        r'copy-paste\s+detected': 25
    }
    
    detected_patterns = []
    highest_level = 5  # Default minimal duplication
    
    for pattern, level in duplication_indicators.items():
        if re.search(pattern, raw_results, re.IGNORECASE):
            detected_patterns.append(pattern)
            if level > highest_level:
                highest_level = level
    
    return {
        'duplication_percentage': highest_level,
        'claude_analysis': False,
        'calculation_method': 'text_pattern_fallback',
        'fallback_reason': 'Claude Code CLI unavailable or failed',
        'fallback_timestamp': datetime.now().isoformat(),
        'detected_patterns': detected_patterns,
        'analysis_summary': f'Pattern-based detection found {len(detected_patterns)} duplication indicators'
    }


def _calculate_effort_estimate_with_claude(session_id: str, raw_results: str, structured_findings: Dict, 
                                         complexity_metrics: Optional[Dict] = None,
                                         historical_data: Optional[Dict] = None,
                                         config: Optional[Dict] = None) -> Dict:
    """Calculate realistic effort estimate using Claude Code AI analysis
    
    Uses Claude Code to analyze code complexity and provide realistic effort estimates
    based on:
    - Code complexity and scope of changes
    - Risk level and dependencies
    - Required testing effort
    - Historical patterns and team velocity
    
    Returns effort estimate with confidence intervals
    """
    import subprocess
    import json
    
    try:
        # Prepare findings summary for Claude
        findings_summary = {
            'critical_bugs': len([f for f in structured_findings.get('bugs', []) if f.get('severity') in ['high', 'critical']]),
            'security_issues': len(structured_findings.get('security_issues', [])),
            'performance_issues': len(structured_findings.get('performance_issues', [])),
            'code_quality_issues': len(structured_findings.get('code_quality', [])),
            'ac_violations': len(structured_findings.get('acceptance_criteria', [])),
            'total_findings': sum(len(findings) for findings in structured_findings.values())
        }
        
        prompt = f"""
Schätze den realistischen Aufwand für die Behebung dieser Code-Review Findings:

## Findings Summary:
{json.dumps(findings_summary, indent=2)}

## Code Complexity Indicators:
{json.dumps(complexity_metrics, indent=2) if complexity_metrics else 'No complexity data available'}

## Review Details (Auszug):
{raw_results[:1500] if raw_results else 'No detailed review available'}...

**Berücksichtige bei der Schätzung:**
1. **Komplexität der betroffenen Code-Bereiche**
   - Einfache Fixes: 15-30 Minuten
   - Mittlere Komplexität: 1-2 Stunden
   - Hohe Komplexität: 3-8 Stunden

2. **Risk Level der Änderungen**
   - Low Risk: Direkter Fix möglich
   - Medium Risk: Testing und Review nötig
   - High Risk: Umfangreiche Tests und Validation

3. **Abhängigkeiten und Side Effects**
   - Isolierte Änderungen: Geringer Overhead
   - System-übergreifend: Hoher Test-Aufwand

4. **Test-Aufwand**
   - Unit Tests: +30% der Fix-Zeit
   - Integration Tests: +50% der Fix-Zeit
   - E2E Tests: +100% der Fix-Zeit

**Severity Multipliers:**
- Critical Security/Bugs: 3-4 Stunden pro Issue
- High Severity: 2-3 Stunden pro Issue
- Medium Severity: 1-2 Stunden pro Issue
- Low Severity/Suggestions: 15-30 Minuten pro Issue

**WICHTIG:** Antworte NUR mit einem JSON-Objekt:
{{
  "estimated_hours": 4.5,
  "confidence_level": "medium",
  "effort_breakdown": {{
    "critical_fixes": {{"hours": 2.0, "description": "Critical bug fixes"}},
    "code_quality": {{"hours": 1.5, "description": "Code quality improvements"}},
    "testing": {{"hours": 1.0, "description": "Testing and validation"}}
  }},
  "risk_factors": ["Factors that could increase effort"],
  "confidence_range": "±45 minutes"
}}

**Antwort:**"""
        
        # Call Claude Code CLI
        result = subprocess.run([
            'claude'
        ], input=prompt, capture_output=True, text=True, timeout=240)
        
        if result.returncode == 0:
            response_text = result.stdout.strip()
            logger.info(f"🕐 Claude effort analysis: {response_text[:200]}...")
            
            try:
                # Extract JSON from markdown code block if present
                json_text = _extract_json_from_response(response_text)
                
                data = json.loads(json_text)
                estimated_hours = float(data.get('estimated_hours', 2.0))
                
                # Validate and format response
                if 0 <= estimated_hours <= 40:  # Reasonable range
                    # Generate human-readable effort string
                    if estimated_hours <= 0.1:
                        effort_string = 'Ready to merge'
                    elif estimated_hours <= 0.5:
                        effort_string = '15-30 minutes'
                    elif estimated_hours <= 1.0:
                        effort_string = '30-60 minutes'
                    elif estimated_hours <= 2.0:
                        effort_string = '1-2 hours'
                    elif estimated_hours <= 8.0:
                        effort_string = f'{estimated_hours:.1f} hours'
                    else:
                        days = estimated_hours / 8
                        effort_string = f'{days:.1f} days'
                    
                    logger.info(f"✅ Claude estimated effort: {estimated_hours} hours ({effort_string})")
                    
                    return {
                        'estimated_hours': round(estimated_hours, 2),
                        'estimated_effort': effort_string,
                        'confidence_level': data.get('confidence_level', 'medium'),
                        'confidence_range': data.get('confidence_range', '±30 minutes'),
                        'calculation_breakdown': data.get('effort_breakdown', {}),  # Map effort_breakdown to calculation_breakdown for consistency
                        'effort_breakdown': data.get('effort_breakdown', {}),
                        'risk_factors': data.get('risk_factors', []),
                        'calculation_method': 'claude_ai_analysis',
                        'claude_analysis': True
                    }
                else:
                    logger.warning(f"⚠️ Claude returned unrealistic effort: {estimated_hours} hours")
                    return _calculate_effort_estimate_fallback(structured_findings, complexity_metrics, historical_data, config)
                    
            except json.JSONDecodeError:
                logger.warning(f"⚠️ Claude effort response not valid JSON")
                return _calculate_effort_estimate_fallback(structured_findings, complexity_metrics, historical_data, config)
        else:
            logger.error(f"❌ Claude effort analysis failed: {result.stderr}")
            return _calculate_effort_estimate_fallback(structured_findings, complexity_metrics, historical_data, config)
            
    except Exception as e:
        logger.error(f"❌ Error in Claude effort estimation: {e}")
        return _calculate_effort_estimate_fallback(structured_findings, complexity_metrics, historical_data, config)


def _calculate_effort_estimate_fallback(structured_findings: Dict, 
                                      complexity_metrics: Optional[Dict] = None,
                                      historical_data: Optional[Dict] = None,
                                      config: Optional[Dict] = None) -> Dict:
    """Fallback effort calculation using simple algorithm
    
    Returns:
        Dict containing effort estimation data
    """
    # Simple fallback calculation
    total_findings = sum(len(findings) for findings in structured_findings.values())
    high_severity = sum(1 for finding in [f for findings in structured_findings.values() for f in findings] 
                       if finding.get('severity', 'low').lower() in ['high', 'critical'])
    
    # Basic effort estimation
    base_hours = high_severity * 2 + (total_findings - high_severity) * 0.5
    final_hours = max(0.5, min(8.0, base_hours))  # Clamp between 30 minutes and 8 hours
    
    if final_hours <= 0.5:
        effort_string = '15-30 minutes'
    elif final_hours <= 1.0:
        effort_string = '30-60 minutes'
    elif final_hours <= 2.0:
        effort_string = '1-2 hours'
    else:
        effort_string = f'{final_hours:.1f} hours'
    
    return {
        'estimated_hours': round(final_hours, 2),
        'estimated_effort': effort_string,
        'confidence_level': 'low',
        'confidence_range': '±60 minutes',
        'calculation_method': 'fallback_algorithm',
        'claude_analysis': False,
        'fallback_reason': 'Claude Code CLI unavailable or failed',
        'fallback_timestamp': datetime.now().isoformat(),
        'calculation_breakdown': {
            'base_estimation': f'{base_hours:.1f} hours from {total_findings} findings',
            'high_severity_impact': f'{high_severity} high-severity issues (+{high_severity * 2:.1f}h)',
            'complexity_adjustment': 'Not available in fallback mode',
            'final_result': f'Clamped to {final_hours:.1f} hours (0.5-8h range)'
        }
    }


def _extract_complexity_metrics_with_claude(session_id: str, raw_results: str, changed_files: Optional[List[str]] = None) -> Dict:
    """Extract code complexity metrics using Claude Code AI analysis
    
    Uses Claude Code to analyze code complexity and provide realistic metrics:
    - Cyclomatic complexity assessment
    - Cognitive load analysis
    - Nesting depth evaluation
    - Function/method length assessment
    - Complexity hotspot identification
    
    Args:
        session_id: Session identifier for tracking
        raw_results: Raw Claude review output text
        changed_files: List of changed file paths for analysis
        
    Returns:
        Dict containing complexity metrics:
        - cyclomatic_complexity: Estimated cyclomatic complexity (0-25)
        - cognitive_load: Estimated cognitive load score (0-30)
        - nesting_depth: Maximum nesting depth indicators (0-10)
        - function_length: Function/method length indicators (0-15)
        - complexity_hotspots: Files/areas flagged as complex
        - analysis_confidence: Confidence level (low/medium/high)
    """
    
    # Fallback function in case Claude is unavailable
    def fallback_complexity_analysis() -> Dict:
        return {
            'cyclomatic_complexity': 5,  # Default moderate complexity
            'cognitive_load': 7,         # Default moderate cognitive load
            'nesting_depth': 3,          # Default moderate nesting
            'function_length': 5,        # Default moderate function length
            'complexity_hotspots': [],
            'analysis_confidence': 'low'
        }
    
    if not raw_results:
        return fallback_complexity_analysis()
    
    try:
        # Prepare context for Claude analysis
        files_context = ""
        if changed_files:
            files_context = f"\n\nChanged files for analysis:\n{chr(10).join(f'- {file}' for file in changed_files[:10])}"
        
        # Use Claude Code to analyze complexity metrics
        claude_prompt = f"""
Analyze the following code review output and provide detailed complexity metrics. Your analysis should be realistic and based on actual code complexity patterns.

Review Output:
{raw_results[:3000]}
{files_context}

Please analyze the code complexity and provide a JSON response with these specific metrics:

{{
  "cyclomatic_complexity": <number 0-25>,  // Actual cyclomatic complexity based on decision points, branches, loops
  "cognitive_load": <number 0-30>,         // Mental overhead to understand the code (readability, nested logic)
  "nesting_depth": <number 0-10>,          // Maximum nesting levels in control structures
  "function_length": <number 0-15>,        // Function/method length complexity (lines, responsibilities)
  "complexity_hotspots": [                 // Files or areas with high complexity
    "filename.ext",
    "path/to/complex_file.js"
  ],
  "analysis_confidence": "<low|medium|high>",  // How confident you are in this analysis
  "explanation": {{
    "cyclomatic_reasoning": "Brief explanation of cyclomatic complexity score",
    "cognitive_reasoning": "Brief explanation of cognitive load score",
    "nesting_reasoning": "Brief explanation of nesting depth",
    "length_reasoning": "Brief explanation of function length complexity",
    "hotspots_reasoning": "Brief explanation of identified complexity hotspots"
  }}
}}

Focus on:
1. **Cyclomatic Complexity**: Count decision points, branches, loops, and conditional logic
2. **Cognitive Load**: Assess readability, nested conditions, complex expressions, mental overhead
3. **Nesting Depth**: Identify deep control structure nesting (if/else, loops, try/catch)
4. **Function Length**: Evaluate method/function size and single responsibility adherence
5. **Hotspots**: Identify specific files or code areas that show complexity indicators

Be realistic in your scoring - only give high scores if there's actual evidence of complexity in the code.
"""
        
        # Execute Claude command for complexity analysis
        claude_result = execute_claude_command(claude_prompt)
        
        if claude_result and claude_result.get('success') and claude_result.get('response'):
            try:
                # Try to extract JSON from Claude's response
                response_text = claude_result['response']
                
                # Extract JSON from markdown code block if present
                json_text = _extract_json_from_response(response_text)
                
                import json
                complexity_data = json.loads(json_text)
                
                # Validate and sanitize the data
                metrics = {
                    'cyclomatic_complexity': min(25, max(0, complexity_data.get('cyclomatic_complexity', 5))),
                    'cognitive_load': min(30, max(0, complexity_data.get('cognitive_load', 7))),
                    'nesting_depth': min(10, max(0, complexity_data.get('nesting_depth', 3))),
                    'function_length': min(15, max(0, complexity_data.get('function_length', 5))),
                    'complexity_hotspots': complexity_data.get('complexity_hotspots', [])[:10],  # Limit to 10 hotspots
                    'analysis_confidence': complexity_data.get('analysis_confidence', 'medium')
                }
                
                # Add explanation if available
                if 'explanation' in complexity_data:
                    metrics['explanation'] = complexity_data['explanation']
                
                logger.info(f"Claude complexity analysis successful for session {session_id}")
                return metrics
                    
            except (json.JSONDecodeError, KeyError, TypeError) as e:
                logger.warning(f"Failed to parse Claude complexity response: {e}")
        
        # If Claude analysis fails, try to extract basic metrics from response text
        if claude_result and claude_result.get('response'):
            response_text = claude_result['response'].lower()
            
            # Basic complexity indicators from Claude's text response
            cyclomatic = 5  # Default
            if 'high complexity' in response_text or 'very complex' in response_text:
                cyclomatic = 15
            elif 'moderate complexity' in response_text or 'medium complexity' in response_text:
                cyclomatic = 8
            elif 'low complexity' in response_text or 'simple' in response_text:
                cyclomatic = 3
            
            cognitive = 7  # Default
            if 'hard to understand' in response_text or 'difficult to read' in response_text:
                cognitive = 18
            elif 'readable' in response_text or 'clear' in response_text:
                cognitive = 4
            
            return {
                'cyclomatic_complexity': cyclomatic,
                'cognitive_load': cognitive,
                'nesting_depth': 3,
                'function_length': 5,
                'complexity_hotspots': [],
                'analysis_confidence': 'medium'
            }
        
        app.logger.warning(f"Claude complexity analysis failed for session {session_id}, using fallback")
        return fallback_complexity_analysis()
        
    except Exception as e:
        app.logger.error(f"Error in Claude complexity analysis for session {session_id}: {e}")
        return fallback_complexity_analysis()


def _extract_complexity_metrics(raw_results: str, changed_files: Optional[List[str]] = None) -> Dict:
    """Legacy fallback function - redirects to Claude-based analysis"""
    # Convert to new Claude-based function
    session_id = "legacy_" + str(int(time.time()))
    return _extract_complexity_metrics_with_claude(session_id, raw_results, changed_files)


def _save_historical_score(session_id: str, branch_name: str, quality_score: float, duplication_level: int) -> None:
    """Save historical score data for tracking and calibration
    
    Args:
        session_id: Review session ID
        branch_name: Git branch name
        quality_score: Calculated quality score (0-10)
        duplication_level: Detected duplication percentage (0-100)
    """
    history_file = Path(__file__).parent / 'review_scores_history.json'
    
    try:
        # Load existing history
        if history_file.exists():
            with open(history_file, 'r') as f:
                history = json.load(f)
        else:
            history = {'scores': []}
        
        # Add new score entry
        score_entry = {
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'branch_name': branch_name,
            'quality_score': quality_score,
            'duplication_level': duplication_level
        }
        
        history['scores'].append(score_entry)
        
        # Keep only last 1000 entries to prevent file from growing too large
        if len(history['scores']) > 1000:
            history['scores'] = history['scores'][-1000:]
        
        # Save updated history
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
            
        logger.info(f"Saved historical score for session {session_id}: score={quality_score}, duplication={duplication_level}%")
        
    except Exception as e:
        logger.error(f"Failed to save historical score: {e}")


def _get_score_statistics() -> Dict:
    """Get statistics from historical scores for monitoring and calibration
    
    Returns:
        Dict with score statistics (mean, std, distribution)
    """
    history_file = Path(__file__).parent / 'review_scores_history.json'
    
    if not history_file.exists():
        return {
            'total_reviews': 0,
            'mean_quality_score': 0,
            'mean_duplication': 0,
            'score_distribution': {},
            'last_updated': None
        }
    
    try:
        with open(history_file, 'r') as f:
            history = json.load(f)
        
        scores = history.get('scores', [])
        if not scores:
            return {
                'total_reviews': 0,
                'mean_quality_score': 0,
                'mean_duplication': 0,
                'score_distribution': {},
                'last_updated': None
            }
        
        quality_scores = [s['quality_score'] for s in scores]
        duplication_levels = [s['duplication_level'] for s in scores]
        
        # Calculate distribution
        score_distribution = {}
        for score in quality_scores:
            bucket = int(score)  # Round down to nearest integer
            score_distribution[bucket] = score_distribution.get(bucket, 0) + 1
        
        return {
            'total_reviews': len(scores),
            'mean_quality_score': sum(quality_scores) / len(quality_scores),
            'mean_duplication': sum(duplication_levels) / len(duplication_levels),
            'score_distribution': score_distribution,
            'last_updated': scores[-1]['timestamp'] if scores else None,
            'recent_scores': scores[-10:]  # Last 10 scores for trend analysis
        }
        
    except Exception as e:
        logger.error(f"Failed to get score statistics: {e}")
        return {
            'total_reviews': 0,
            'mean_quality_score': 0,
            'mean_duplication': 0,
            'score_distribution': {},
            'last_updated': None
        }


def _save_historical_effort(session_id: str, branch_name: str, estimated_hours: float, 
                           actual_hours: Optional[float] = None, complexity_metrics: Optional[Dict] = None) -> None:
    """Save historical effort data for tracking and model improvement
    
    Args:
        session_id: Review session ID
        branch_name: Git branch name
        estimated_hours: Our calculated effort estimate
        actual_hours: Actual time spent (if known)
        complexity_metrics: Complexity metrics used in calculation
    """
    history_file = Path(__file__).parent / 'review_effort_history.json'
    
    try:
        # Load existing history
        if history_file.exists():
            with open(history_file, 'r') as f:
                history = json.load(f)
        else:
            history = {'efforts': []}
        
        # Add new effort entry
        effort_entry = {
            'session_id': session_id,
            'timestamp': datetime.now().isoformat(),
            'branch_name': branch_name,
            'estimated_hours': estimated_hours,
            'actual_hours': actual_hours,
            'complexity_metrics': complexity_metrics or {},
            'accuracy_ratio': actual_hours / estimated_hours if actual_hours and estimated_hours > 0 else None
        }
        
        history['efforts'].append(effort_entry)
        
        # Keep only last 1000 entries to manage file size
        if len(history['efforts']) > 1000:
            history['efforts'] = history['efforts'][-1000:]
        
        # Save updated history
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
            
        logger.info(f"Saved historical effort for session {session_id}: estimated={estimated_hours}h, actual={actual_hours}h")
        
    except Exception as e:
        logger.error(f"Failed to save historical effort: {e}")


def _get_historical_effort_data() -> Dict:
    """Get historical effort data for effort estimation calibration
    
    Returns:
        Dict containing:
        - average_accuracy_ratio: How accurate our estimates typically are
        - effort_variance: Standard deviation of estimation errors
        - recent_trends: Recent estimation accuracy trends
        - complexity_correlations: How complexity affects estimation accuracy
    """
    history_file = Path(__file__).parent / 'review_effort_history.json'
    
    if not history_file.exists():
        return {
            'average_accuracy_ratio': 1.0,
            'effort_variance': 0.5,
            'total_estimates': 0,
            'accuracy_trend': 'stable'
        }
    
    try:
        with open(history_file, 'r') as f:
            history = json.load(f)
        
        efforts = history.get('efforts', [])
        
        # Filter entries with both estimated and actual hours
        completed_efforts = [e for e in efforts if e.get('actual_hours') is not None and e.get('estimated_hours', 0) > 0]
        
        if not completed_efforts:
            return {
                'average_accuracy_ratio': 1.0,
                'effort_variance': 0.5,
                'total_estimates': len(efforts),
                'accuracy_trend': 'stable'
            }
        
        # Calculate accuracy metrics
        accuracy_ratios = [e['accuracy_ratio'] for e in completed_efforts if e.get('accuracy_ratio')]
        
        import statistics
        
        avg_accuracy = statistics.mean(accuracy_ratios) if accuracy_ratios else 1.0
        variance = statistics.stdev(accuracy_ratios) if len(accuracy_ratios) > 1 else 0.5
        
        # Analyze recent trends (last 10 completed efforts)
        recent_ratios = accuracy_ratios[-10:] if len(accuracy_ratios) >= 10 else accuracy_ratios
        if len(recent_ratios) >= 3:
            recent_avg = statistics.mean(recent_ratios)
            trend = 'improving' if recent_avg > avg_accuracy else 'declining' if recent_avg < avg_accuracy else 'stable'
        else:
            trend = 'stable'
        
        # Analyze complexity correlation
        complexity_data = []
        for effort in completed_efforts:
            if effort.get('complexity_metrics'):
                complexity_score = effort['complexity_metrics'].get('cyclomatic_complexity', 0) + \
                                 effort['complexity_metrics'].get('cognitive_load', 0)
                complexity_data.append({
                    'complexity': complexity_score,
                    'accuracy': effort['accuracy_ratio']
                })
        
        return {
            'average_accuracy_ratio': max(0.5, min(2.0, avg_accuracy)),  # Clamp between 0.5 and 2.0
            'effort_variance': variance,
            'total_estimates': len(efforts),
            'completed_estimates': len(completed_efforts),
            'accuracy_trend': trend,
            'recent_accuracy': statistics.mean(recent_ratios) if recent_ratios else avg_accuracy,
            'complexity_correlation_available': len(complexity_data) > 5
        }
        
    except Exception as e:
        logger.error(f"Failed to get historical effort data: {e}")
        return {
            'average_accuracy_ratio': 1.0,
            'effort_variance': 0.5,
            'total_estimates': 0,
            'accuracy_trend': 'stable'
        }


def _test_effort_calculation_performance() -> Dict:
    """Test effort calculation performance to ensure < 500ms requirement
    
    Returns performance test results and timing data
    """
    import time
    
    # Create test data similar to real-world scenarios
    test_structured_findings = {
        'bugs': [
            {'text': 'Critical bug 1', 'severity': 'high'},
            {'text': 'Medium bug 1', 'severity': 'medium'},
            {'text': 'Minor bug 1', 'severity': 'low'}
        ],
        'security_issues': [
            {'text': 'SQL injection risk', 'severity': 'high'},
            {'text': 'Auth bypass potential', 'severity': 'medium'}
        ],
        'code_quality': [
            {'text': 'Code smell 1', 'severity': 'low'},
            {'text': 'Code smell 2', 'severity': 'medium'},
            {'text': 'Code smell 3', 'severity': 'low'}
        ],
        'performance_issues': [
            {'text': 'N+1 query detected', 'severity': 'medium'}
        ],
        'acceptance_criteria': [
            {'text': 'AC violation 1', 'severity': 'high'}
        ],
        'suggestions': [
            {'text': 'Consider refactoring', 'severity': 'low'},
            {'text': 'Add unit tests', 'severity': 'low'}
        ]
    }
    
    test_complexity_metrics = {
        'cyclomatic_complexity': 12,
        'cognitive_load': 8,
        'nesting_depth': 4,
        'function_length': 6,
        'complexity_hotspots': ['src/complex_file.py', 'src/another_file.js'],
        'analysis_confidence': 'high'
    }
    
    test_historical_data = {
        'average_accuracy_ratio': 1.1,
        'effort_variance': 0.3,
        'total_estimates': 50,
        'accuracy_trend': 'stable'
    }
    
    # Run multiple test iterations to get average performance
    test_iterations = 100
    times = []
    
    for i in range(test_iterations):
        start_time = time.perf_counter()
        
        result = _calculate_effort_estimate_with_claude(
            session_id='test',
            raw_results='test raw results',
            structured_findings=test_structured_findings,
            complexity_metrics=test_complexity_metrics,
            historical_data=test_historical_data
        )
        
        end_time = time.perf_counter()
        execution_time_ms = (end_time - start_time) * 1000
        times.append(execution_time_ms)
    
    # Calculate performance statistics
    avg_time = sum(times) / len(times)
    max_time = max(times)
    min_time = min(times)
    
    # Check if performance requirement is met
    performance_pass = avg_time < 500 and max_time < 500
    
    return {
        'performance_test_passed': performance_pass,
        'average_time_ms': round(avg_time, 2),
        'max_time_ms': round(max_time, 2),
        'min_time_ms': round(min_time, 2),
        'test_iterations': test_iterations,
        'requirement_threshold_ms': 500,
        'test_timestamp': datetime.now().isoformat(),
        'sample_calculation_result': result
    }


def _parse_enhanced_report(raw_results: str, ticket_info: Optional[Dict] = None, structured_findings: Optional[Dict] = None, session_id: str = 'parse_enhanced') -> Dict:
    """Parse enhanced report sections from Claude output for comprehensive UI display"""
    import re
    
    report = {
        'metadata': {
            'generated_at': datetime.now().isoformat(),
            'review_type': 'comprehensive',
        },
        'acceptance_criteria_analysis': {
            'executive_summary': {},
            'detailed_results': []
        },
        'code_quality_analysis': {
            'executive_summary': {},
            'code_duplication': [],
            'complexity_issues': [],
            'naming_consistency': []
        },
        'bug_detection_results': {
            'critical_bugs': [],
            'logic_errors': [],
            'runtime_risks': []
        },
        'architectural_assessment': {
            'design_patterns': [],
            'integration_quality': [],
            'violations': []
        },
        'security_performance': {
            'security_findings': [],
            'performance_analysis': []
        },
        'action_items': {
            'critical': [],
            'important': [],
            'suggestions': []
        },
        'variable_parameter_analysis': {
            'executive_summary': {
                'total_variables': 0,
                'naming_issues': 0,
                'scope_issues': 0,
                'consistency_score': 8
            },
            'naming_analysis': [],
            'scope_analysis': [],
            'type_consistency': []
        },
        'next_steps': {
            'priority_assessment': {
                'critical_blockers': 0,
                'high_priority': 0,
                'medium_priority': 0,
                'can_merge': True,
                'estimated_effort': '2-4 hours'
            },
            'immediate_actions': [],
            'follow_up_tasks': [],
            'merge_readiness': {
                'status': 'ready',
                'blockers': [],
                'recommendations': []
            },
            'post_merge_actions': []
        },
        'questions_clarifications': []
    }
    
    lines = raw_results.split('\n')
    current_section = None
    current_subsection = None
    
    # Section patterns for identification
    section_patterns = {
        'ac_analysis': [
            r'PHASE 1.*ACCEPTANCE CRITERIA',
            r'AC Executive Summary',
            r'Acceptance Criteria.*Analysis'
        ],
        'code_quality': [
            r'PHASE 2.*CODE QUALITY',
            r'Code Quality.*Analysis',
            r'Code Quality Executive Summary'
        ],
        'bug_detection': [
            r'Bug Detection Results',
            r'Critical Bugs',
            r'Logic Errors'
        ],
        'architectural': [
            r'Architectural Assessment',
            r'Design Pattern',
            r'Integration Quality'
        ],
        'security_performance': [
            r'Security.*Performance',
            r'Security Findings',
            r'Performance Analysis'
        ],
        'action_items': [
            r'COMBINED ACTION ITEMS',
            r'Must.Fix',
            r'Should.Fix'
        ],
        'variable_analysis': [
            r'Variable.*Parameter.*Analysis',
            r'Naming.*Comparison.*Table',
            r'📊.*Variable.*Parameter',
            r'Naming.*Consistency'
        ],
        'next_steps': [
            r'Next Steps',
            r'🚀.*Next.*Steps',
            r'Action.*Plan',
            r'Merge.*Readiness'
        ]
    }
    
    # Parse AC Analysis from ticket info if available
    if ticket_info and 'acceptance_criteria' in ticket_info:
        report['acceptance_criteria_analysis']['detailed_results'] = []
        for i, ac in enumerate(ticket_info['acceptance_criteria']):
            # Try to find AC status in raw results
            ac_status = 'pending'
            ac_text = str(ac).lower()
            
            # Look for AC status indicators in raw results
            implementation_evidence = ''
            issues = []
            
            if re.search(rf'ac\s*{i+1}.*erfüllt|✅.*ac\s*{i+1}', raw_results, re.IGNORECASE):
                ac_status = 'fulfilled'
            elif re.search(rf'ac\s*{i+1}.*teilweise|⚠️.*ac\s*{i+1}', raw_results, re.IGNORECASE):
                ac_status = 'partially_fulfilled'
            elif re.search(rf'ac\s*{i+1}.*nicht.*erfüllt|❌.*ac\s*{i+1}', raw_results, re.IGNORECASE):
                ac_status = 'not_fulfilled'
            
            # Extract implementation evidence and file references for this AC
            ac_section_match = re.search(rf'ac\s*{i+1}.*?(?=ac\s*{i+2}|$)', raw_results, re.IGNORECASE | re.DOTALL)
            if ac_section_match:
                ac_section = ac_section_match.group(0)
                
                # Look for Code-Implementierung sections
                impl_match = re.search(r'code-implementierung:\s*(.+?)(?=probleme:|$)', ac_section, re.IGNORECASE | re.DOTALL)
                if impl_match:
                    implementation_evidence = impl_match.group(1).strip()
                
                # Look for problems/issues
                issues_match = re.search(r'probleme:\s*(.+?)(?=ac\s*\d+|$)', ac_section, re.IGNORECASE | re.DOTALL)
                if issues_match:
                    issues_text = issues_match.group(1).strip()
                    issues = [issue.strip() for issue in issues_text.split('\n') if issue.strip() and not issue.strip().startswith('-')]
            
            report['acceptance_criteria_analysis']['detailed_results'].append({
                'id': f"ac_{i+1}",
                'text': str(ac),
                'status': ac_status,
                'implementation_evidence': implementation_evidence,
                'issues': issues
            })
    
    # Parse structured sections from raw results
    for i, line in enumerate(lines):
        line_lower = line.lower().strip()
        if not line_lower:
            continue
        
        # Detect section headers
        detected_section = None
        for section_key, patterns in section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    detected_section = section_key
                    break
            if detected_section:
                break
        
        if detected_section:
            current_section = detected_section
            continue
        
        # Parse content based on current section
        if current_section == 'bug_detection':
            if re.match(r'^[\s]*[-*•]\s*', line):
                bug_text = re.sub(r'^[\s]*[-*•]\s*', '', line).strip()
                severity = 'medium'
                
                if any(word in line_lower for word in ['critical', 'severe', 'must fix']):
                    severity = 'high'
                    report['bug_detection_results']['critical_bugs'].append({
                        'text': bug_text,
                        'severity': severity,
                        'file': _extract_file_reference(line),
                        'type': 'critical'
                    })
                elif any(word in line_lower for word in ['logic', 'race condition', 'type error']):
                    report['bug_detection_results']['logic_errors'].append({
                        'text': bug_text,
                        'severity': severity,
                        'file': _extract_file_reference(line),
                        'type': 'logic'
                    })
                else:
                    report['bug_detection_results']['runtime_risks'].append({
                        'text': bug_text,
                        'severity': severity,
                        'file': _extract_file_reference(line),
                        'type': 'runtime'
                    })
        
        elif current_section == 'action_items':
            if re.match(r'^[\s]*[-*•]\s*', line):
                item_text = re.sub(r'^[\s]*[-*•]\s*', '', line).strip()
                
                if any(word in line_lower for word in ['critical', 'must fix', 'blocker']):
                    report['action_items']['critical'].append({
                        'text': item_text,
                        'priority': 'critical',
                        'category': 'must_fix'
                    })
                elif any(word in line_lower for word in ['important', 'should fix']):
                    report['action_items']['important'].append({
                        'text': item_text,
                        'priority': 'important',
                        'category': 'should_fix'
                    })
                else:
                    report['action_items']['suggestions'].append({
                        'text': item_text,
                        'priority': 'suggestion',
                        'category': 'nice_to_have'
                    })
        
        elif current_section == 'variable_analysis':
            # Parse variable analysis tables
            table_row = _parse_table_row(line)
            if table_row:
                report['variable_parameter_analysis']['naming_analysis'].append(table_row)
            
            # Parse scope issues
            elif re.match(r'^[\s]*[-*•]\s*', line) and any(word in line_lower for word in ['shadowing', 'unused', 'scope']):
                issue_text = re.sub(r'^[\s]*[-*•]\s*', '', line).strip()
                file_ref = _extract_file_reference(line)
                
                issue_type = 'unused' if 'unused' in line_lower else 'shadowing' if 'shadow' in line_lower else 'scope_pollution'
                
                report['variable_parameter_analysis']['scope_analysis'].append({
                    'issue_type': issue_type,
                    'file': file_ref or 'unknown',
                    'variable_name': 'extracted_variable',  # Would need more sophisticated parsing
                    'description': issue_text,
                    'recommendation': 'Review and fix',
                    'line_number': _extract_line_number(file_ref) if file_ref else None
                })
        
        elif current_section == 'next_steps':
            # Parse immediate actions
            if re.match(r'^[\s]*[-*•]\s*', line) and any(word in line_lower for word in ['critical', 'immediate', 'must', 'blocker']):
                action_text = re.sub(r'^[\s]*[-*•]\s*', '', line).strip()
                
                priority = 'critical' if any(word in line_lower for word in ['critical', 'blocker']) else 'high'
                category = 'security' if 'security' in line_lower else 'bugs' if 'bug' in line_lower else 'ac_compliance' if 'ac' in line_lower else 'quality'
                
                report['next_steps']['immediate_actions'].append({
                    'action': action_text,
                    'category': category,
                    'priority': priority,
                    'effort': '1-2 hours',  # Default estimate
                    'description': action_text
                })
            
            # Parse merge readiness indicators
            elif any(word in line_lower for word in ['ready to merge', 'blocked', 'needs review']):
                if 'blocked' in line_lower:
                    report['next_steps']['merge_readiness']['status'] = 'blocked'
                    report['next_steps']['merge_readiness']['blockers'].append(line.strip())
                elif 'needs review' in line_lower:
                    report['next_steps']['merge_readiness']['status'] = 'needs_review'
                elif 'ready' in line_lower:
                    report['next_steps']['merge_readiness']['status'] = 'ready'
    
    # Calculate executive summaries
    if report['acceptance_criteria_analysis']['detailed_results']:
        total_ac = len(report['acceptance_criteria_analysis']['detailed_results'])
        fulfilled = len([ac for ac in report['acceptance_criteria_analysis']['detailed_results'] if ac['status'] == 'fulfilled'])
        partially = len([ac for ac in report['acceptance_criteria_analysis']['detailed_results'] if ac['status'] == 'partially_fulfilled'])
        not_fulfilled = len([ac for ac in report['acceptance_criteria_analysis']['detailed_results'] if ac['status'] == 'not_fulfilled'])
        
        report['acceptance_criteria_analysis']['executive_summary'] = {
            'total_ac': total_ac,
            'fulfilled': fulfilled,
            'partially_fulfilled': partially,
            'not_fulfilled': not_fulfilled,
            'compliance_rate': round((fulfilled / total_ac * 100) if total_ac > 0 else 0, 1),
            'business_alignment_score': min(10, round((fulfilled + partially * 0.5) / total_ac * 10)) if total_ac > 0 else 0
        }
    
    # Calculate quality score and duplication level using Claude AI
    quality_score_result = _calculate_quality_score_with_claude(session_id, raw_results, structured_findings or {}) if structured_findings else {'quality_score': 8, 'claude_analysis': False}
    overall_score = quality_score_result.get('quality_score', 8) if isinstance(quality_score_result, dict) else quality_score_result
    duplication_result = _detect_code_duplication_with_claude(session_id, raw_results)
    duplication_level = duplication_result.get('duplication_percentage', 5) if isinstance(duplication_result, dict) else duplication_result
    
    report['code_quality_analysis']['executive_summary'] = {
        'overall_score': overall_score,
        'critical_issues': len(report['bug_detection_results']['critical_bugs']),
        'code_smells': len(report['code_quality_analysis']['complexity_issues']),
        'duplication_level': duplication_level
    }
    
    # Add AI analysis metadata for frontend monitoring
    report['ai_analysis_metadata'] = {
        'quality_score_analysis': quality_score_result if isinstance(quality_score_result, dict) else {'quality_score': quality_score_result, 'claude_analysis': True},
        'duplication_analysis': duplication_result if isinstance(duplication_result, dict) else {'duplication_percentage': duplication_result, 'claude_analysis': True},
        'has_fallback_calculations': not (
            quality_score_result.get('claude_analysis', True) if isinstance(quality_score_result, dict) else True
        ) or not (
            duplication_result.get('claude_analysis', True) if isinstance(duplication_result, dict) else True
        ),
        'analysis_timestamp': datetime.now().isoformat()
    }
    
    # Calculate variable analysis summary
    if report['variable_parameter_analysis']['naming_analysis'] or report['variable_parameter_analysis']['scope_analysis']:
        total_vars = len(report['variable_parameter_analysis']['naming_analysis'])
        naming_issues = len([v for v in report['variable_parameter_analysis']['naming_analysis'] if v['status'] != 'consistent'])
        scope_issues = len(report['variable_parameter_analysis']['scope_analysis'])
        
        # Calculate consistency score (1-10 scale)
        consistency_score = 10
        if total_vars > 0:
            consistency_score = max(1, round(10 - (naming_issues / total_vars * 5)))
        
        report['variable_parameter_analysis']['executive_summary'] = {
            'total_variables': total_vars,
            'naming_issues': naming_issues,
            'scope_issues': scope_issues,
            'consistency_score': consistency_score
        }
    
    # Calculate next steps priority assessment
    critical_actions = len([a for a in report['next_steps']['immediate_actions'] if a['priority'] == 'critical'])
    high_actions = len([a for a in report['next_steps']['immediate_actions'] if a['priority'] == 'high'])
    medium_actions = len([a for a in report['next_steps']['immediate_actions'] if a['priority'] == 'medium'])
    
    # Determine if merge is blocked
    can_merge = critical_actions == 0 and report['next_steps']['merge_readiness']['status'] != 'blocked'
    
    # Dynamic effort estimation based on findings, complexity, and historical data
    # Extract complexity metrics from raw results using Claude AI analysis
    session_id = "enhanced_report_" + str(int(time.time()))
    complexity_metrics = _extract_complexity_metrics_with_claude(session_id, raw_results)
    
    # Get historical effort data for calibration
    historical_data = _get_historical_effort_data()
    
    # Calculate dynamic effort estimate using Claude AI
    effort_calculation = _calculate_effort_estimate_with_claude(
        session_id='unknown',
        raw_results=raw_results,
        structured_findings=structured_findings or {},
        complexity_metrics=complexity_metrics,
        historical_data=historical_data,
        config=None  # Could be passed from session config in future
    )
    
    # Extract results from calculation
    effort_hours = effort_calculation['estimated_hours']
    estimated_effort = effort_calculation['estimated_effort']
    
    # Add detailed estimation data to report for transparency
    report['effort_estimation'] = {
        'calculated_hours': effort_hours,
        'confidence_range': effort_calculation['confidence_range'],
        'calculation_breakdown': effort_calculation['calculation_breakdown'],
        'complexity_metrics': complexity_metrics,
        'historical_calibration': {
            'accuracy_ratio': historical_data.get('average_accuracy_ratio', 1.0),
            'total_historical_estimates': historical_data.get('total_estimates', 0),
            'accuracy_trend': historical_data.get('accuracy_trend', 'stable')
        }
    }
    
    report['next_steps']['priority_assessment'].update({
        'critical_blockers': critical_actions,
        'high_priority': high_actions,
        'medium_priority': medium_actions,
        'can_merge': can_merge,
        'estimated_effort': estimated_effort
    })
    
    return report


def _generate_review_summary(raw_results: str, changed_files: List, structured_findings: Optional[Dict] = None) -> Dict:
    """Generate summary statistics from review results"""
    if structured_findings:
        # Use structured findings for accurate counting
        return {
            'total_files_reviewed': len(changed_files) if changed_files else 0,
            'total_findings': sum(len(findings) for findings in structured_findings.values()),
            'security_issues': len(structured_findings.get('security_issues', [])),
            'potential_bugs': len(structured_findings.get('bugs', [])),
            'code_quality_issues': len(structured_findings.get('code_quality', [])),
            'performance_issues': len(structured_findings.get('performance_issues', [])),
            'acceptance_criteria_findings': len(structured_findings.get('acceptance_criteria', [])),
            'suggestions': len(structured_findings.get('suggestions', [])),
            'high_priority_findings': _count_by_severity(structured_findings, 'high'),
            'medium_priority_findings': _count_by_severity(structured_findings, 'medium'),
            'low_priority_findings': _count_by_severity(structured_findings, 'low')
        }
    else:
        # Fallback to basic text analysis
        lines = raw_results.split('\n')
        
        security_count = sum(1 for line in lines if any(word in line.lower() for word in ['security', 'vulnerability']))
        bug_count = sum(1 for line in lines if any(word in line.lower() for word in ['bug', 'error', 'issue']))
        quality_count = sum(1 for line in lines if any(word in line.lower() for word in ['quality', 'refactor']))
        
        return {
            'total_files_reviewed': len(changed_files) if changed_files else 0,
            'total_findings': security_count + bug_count + quality_count,
            'security_issues': security_count,
            'potential_bugs': bug_count,
            'quality_suggestions': quality_count,
            'review_length': len(raw_results),
            'estimated_review_time': f"{len(raw_results) // 500} min"  # rough estimate
        }


def _count_by_severity(structured_findings: Dict, severity: str) -> int:
    """Count findings by severity level across all categories"""
    total = 0
    for category_findings in structured_findings.values():
        if isinstance(category_findings, list):
            total += sum(1 for finding in category_findings 
                        if finding.get('severity') == severity)
    return total


# WebSocket event handlers
@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info("Client connected")
    emit('connected', {'message': 'Connected to Claude Code Reviewer API'})


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info("Client disconnected")


@socketio.on('subscribe_to_session')
def handle_session_subscription(data):
    """Subscribe client to specific review session updates"""
    session_id = data.get('session_id')
    if session_id and session_id in review_sessions:
        # Join room for this session
        from flask_socketio import join_room
        join_room(session_id)
        logger.info(f"Client subscribed to session {session_id}")
        emit('subscribed', {'session_id': session_id})
    else:
        emit('subscription_error', {'error': 'Invalid session ID'})


@app.route('/api/code-reviewer/performance-test', methods=['GET'])
def run_effort_calculation_performance_test():
    """API endpoint to run effort calculation performance tests
    
    Returns:
        JSON response with performance test results
    """
    try:
        test_results = _test_effort_calculation_performance()
        
        return jsonify({
            'success': True,
            'performance_test_results': test_results,
            'message': f"Performance test {'PASSED' if test_results['performance_test_passed'] else 'FAILED'}"
        })
        
    except Exception as e:
        logger.error(f"Performance test failed: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Performance test execution failed'
        }), 500


if __name__ == '__main__':
    print("🚀 Starting Claude Code Reviewer API Service...")
    print("📡 Service will be available at: http://localhost:5002")
    print("🔌 WebSocket support enabled for real-time updates")
    print("💡 Frontend should connect to this service for code review functionality")
    print()
    
    # Run the Flask-SocketIO app
    socketio.run(
        app,
        host='0.0.0.0',
        port=5002,
        debug=True,
        allow_unsafe_werkzeug=True  # For development only
    )