#!/usr/bin/env python3
"""
Claude Code PR Reviewer
Verwendet Claude Code SDK um Pull Requests automatisch zu reviewen und einen Report zu erstellen.
Funktioniert zusammen mit dem Bitbucket PR Analyzer.
"""

import subprocess
import json
import os
import sys
import argparse
import tempfile
from pathlib import Path
from datetime import datetime
import re
import threading
import time


class ClaudeCodeReviewer:
    def __init__(self, worktree_path=None, repo_path=".", pr_url=None, branch_name=None):
        self.worktree_path = Path(worktree_path) if worktree_path else None
        self.repo_path = Path(repo_path).resolve()
        self.working_path = self.worktree_path if self.worktree_path else self.repo_path
        self.pr_url = pr_url
        self.branch_name = branch_name
        
        # Überprüfe ob Claude Code verfügbar ist
        self._check_claude_code_availability()
        
    def _check_claude_code_availability(self):
        """Überprüft ob Claude Code installiert und verfügbar ist"""
        try:
            result = subprocess.run(
                ["claude", "--version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            print(f"✅ Claude Code gefunden: {result.stdout.strip()}")
            
            # Test ob Claude Code im Arbeitsverzeichnis funktioniert
            try:
                test_result = subprocess.run(
                    ["claude", "-p", "Hello, test if this works", "--output-format", "text"],
                    cwd=self.working_path,
                    capture_output=True,
                    text=True,
                    timeout=10,
                    check=True
                )
                print(f"✅ Claude Code Test im Arbeitsverzeichnis erfolgreich")
            except Exception as e:
                print(f"⚠️  Claude Code Test im Arbeitsverzeichnis fehlgeschlagen: {e}")
                print("💡 Falls Reviews fehlschlagen, könnte das an der Worktree-Umgebung liegen")
                
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise Exception(
                "❌ Claude Code ist nicht installiert oder nicht im PATH verfügbar.\n"
                "Installation: npm install -g @anthropic-ai/claude-code\n"
                "Weitere Infos: https://docs.anthropic.com/en/docs/claude-code/getting-started"
            )
    
    def _run_claude_command_with_progress(self, prompt, timeout=600, max_turns=None, websocket_callback=None):
        """
        Führt einen Claude Code Befehl mit real-time Progress-Updates aus
        Verwendet stream-json Format für Live-Feedback
        """
        # Default max_turns if not provided
        if max_turns is None:
            max_turns = 20  # Increased default for comprehensive reviews
        
        cmd = [
            "claude", 
            "-p", prompt,
            "--output-format", "stream-json",
            "--verbose",  # Required für stream-json mit --print
            "--max-turns", str(max_turns),  # Dynamic max turns
            "--allowedTools", "Read", "Grep", "Glob", "LS",  # Only allow reading tools
            "--disallowedTools", "Write", "Edit", "MultiEdit", "TodoWrite", "NotebookEdit"  # Explicitly disable writing tools
        ]
        
        print(f"🤖 Starte Claude Code Review...")
        print(f"📍 Arbeitsverzeichnis: {self.working_path}")
        print(f"⏱️  Timeout: {timeout}s")
        print("🔄 Streaming Output aktiviert - Live-Updates folgen...")
        print("=" * 60)
        
        session_info = {
            "session_id": None,
            "tools": [],
            "mcp_servers": [],
            "turn_count": 0,
            "current_response": "",
            "total_cost": 0.0,
            "total_duration": 0,
            "api_duration": 0,
            "is_thinking": False,
            "last_activity": time.time(),
            "websocket_callback": websocket_callback
        }
        
        # Verwende threading.Event statt function attributes für bessere Thread-Sicherheit
        spinner_stop_event = threading.Event()
        
        def progress_spinner():
            """Zeigt einen Spinner für Aktivitäts-Indikator"""
            spinner_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
            i = 0
            while not spinner_stop_event.is_set():
                if time.time() - session_info['last_activity'] > 2:  # 2 Sekunden ohne Update
                    sys.stdout.write(f"\r{spinner_chars[i % len(spinner_chars)]} Thinking...")
                    sys.stdout.flush()
                    i += 1
                time.sleep(0.1)
        
        # Starte Progress Spinner in separatem Thread
        spinner_thread = threading.Thread(target=progress_spinner, daemon=True)
        spinner_thread.start()
        
        try:
            print(f"🚀 Starte Claude Prozess: {' '.join(cmd)}")
            process = subprocess.Popen(
                cmd,
                cwd=self.working_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print(f"✅ Prozess gestartet (PID: {process.pid})")
            final_result = None
            error_occurred = False
            
            # Lese Zeilen aus stdout
            try:
                turn_count = 0
                last_turn_time = time.time()
                max_turn_duration = 120  # 2 Minuten pro Turn
                
                while True:
                    line = process.stdout.readline()
                    if not line:
                        # Check if process ended
                        if process.poll() is not None:
                            break
                        time.sleep(0.1)
                        continue
                    
                    session_info['last_activity'] = time.time()
                    
                    # Parse JSON Message
                    try:
                        if line.strip():
                            message = json.loads(line.strip())
                            self._handle_stream_message(message, session_info)
                            
                            # Track turns und prüfe auf zu lange Turns
                            if message.get("type") == "user":
                                turn_count += 1
                                current_time = time.time()
                                
                                # Warnung bei vielen Turns
                                if turn_count > 6:
                                    print(f"⚠️  Viele Turns ({turn_count}) - Claude arbeitet intensiv...")
                                
                                # Timeout pro Turn
                                if current_time - last_turn_time > max_turn_duration:
                                    print(f"⏰ Turn {turn_count} dauert sehr lange (>{max_turn_duration}s)")
                                    print("💡 Tipp: Drücken Sie Ctrl+C um abzubrechen")
                                
                                last_turn_time = current_time
                            
                            # Sammle finales Ergebnis
                            if message.get("type") == "result":
                                final_result = message
                                break
                                
                    except json.JSONDecodeError as e:
                        # Manchmal kommen non-JSON Debug-Nachrichten
                        if line.strip():
                            print(f"💬 {line.strip()}")
                            
            except KeyboardInterrupt:
                print(f"\n🛑 Review durch Benutzer abgebrochen (Turn {turn_count})")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                raise Exception("Review durch Benutzer abgebrochen")
            except Exception as e:
                error_occurred = True
                print(f"❌ Fehler beim Lesen des Outputs: {e}")
                import traceback
                print(f"🔍 Traceback: {traceback.format_exc()}")
            
            # Stoppe Spinner
            spinner_stop_event.set()
            sys.stdout.write("\r" + " " * 50 + "\r")  # Clear spinner line
            
            # Warte auf Prozess-Ende mit Timeout
            try:
                return_code = process.wait(timeout=30)
                print(f"🏁 Prozess beendet mit Code: {return_code}")
            except subprocess.TimeoutExpired:
                print("⏰ Prozess-Timeout beim Warten auf Ende")
                process.kill()
                return_code = -1
            
            # Check stderr
            try:
                stderr_output = process.stderr.read()
                if stderr_output:
                    print(f"⚠️  Stderr: {stderr_output}")
            except Exception as e:
                print(f"⚠️  Konnte stderr nicht lesen: {e}")
            
            if return_code != 0:
                raise Exception(f"Claude Code failed with exit code {return_code}")
            
            if error_occurred:
                raise Exception("Fehler beim Verarbeiten des Claude Code Outputs")
            
            # Return final response content
            if final_result and final_result.get("subtype") == "success":
                print(f"\n✅ Review erfolgreich abgeschlossen!")
                print(f"💰 Kosten: ${session_info['total_cost']:.4f}")
                print(f"⏱️  Dauer: {session_info['total_duration']/1000:.1f}s (API: {session_info['api_duration']/1000:.1f}s)")
                print(f"🔄 Turns: {session_info['turn_count']}")
                return final_result.get("result", "")
            elif final_result and final_result.get("subtype") == "error_during_execution":
                error_detail = final_result.get("result", "Unbekannter Ausführungsfehler")
                print(f"\n❌ Claude Code Ausführungsfehler:")
                print(f"   Details: {error_detail}")
                print(f"💡 Mögliche Ursachen:")
                print(f"   - Tool-Berechtigungen (Dateien schreiben/lesen)")
                print(f"   - Git-Repository Status")
                print(f"   - Worktree-Zugriffsprobleme")
                raise Exception(f"Claude Code Execution Error: {error_detail}")
            elif final_result and final_result.get("subtype") == "error_max_turns":
                print(f"\n⚠️  Maximum Turns erreicht ({session_info['turn_count']})")
                print(f"💡 Der Review war zu komplex für die aktuelle Turn-Limitierung")
                print(f"🔧 Lösungen:")
                print(f"   - Verwenden Sie --quick für einfachere Reviews")
                print(f"   - Erhöhen Sie max_turns in der Konfiguration")
                print(f"   - Teilen Sie den Review in kleinere Teile auf")
                raise Exception(f"Max turns reached: {session_info['turn_count']}")
            elif final_result:
                raise Exception(f"Claude Code beendet mit Status: {final_result.get('subtype', 'unknown')}")
            else:
                raise Exception("Kein finales Ergebnis von Claude Code erhalten")
                
        except subprocess.TimeoutExpired:
            spinner_stop_event.set()
            process.kill()
            raise Exception(f"Claude Code Timeout nach {timeout} Sekunden")
        except Exception as e:
            spinner_stop_event.set()
            print(f"❌ Unerwarteter Fehler: {e}")
            import traceback
            print(f"🔍 Traceback: {traceback.format_exc()}")
            raise e
    
    def _handle_stream_message(self, message, session_info):
        """
        Verarbeitet eine einzelne Stream-Message und zeigt entsprechende Updates
        """
        msg_type = message.get("type")
        websocket_callback = session_info.get("websocket_callback")
        
        if msg_type == "system" and message.get("subtype") == "init":
            # Conversation Start
            session_info["session_id"] = message.get("session_id")
            session_info["tools"] = message.get("tools", [])
            session_info["mcp_servers"] = message.get("mcp_servers", [])
            
            session_id = session_info['session_id'][:8] if session_info['session_id'] else 'unknown'
            log_message = f"🚀 Session gestartet: {session_id}..."
            print(log_message)
            
            if websocket_callback:
                websocket_callback({
                    'type': 'session_started',
                    'message': log_message,
                    'session_id': session_id
                })
            
            tools = session_info.get("tools", [])
            if tools:
                try:
                    if isinstance(tools, list):
                        tools_str = ', '.join(str(tool) for tool in tools[:5])
                        tools_message = f"🛠️  Verfügbare Tools: {tools_str}"
                        print(tools_message)
                        if len(tools) > 5:
                            extra_message = f"    ... und {len(tools)-5} weitere"
                            print(extra_message)
                            tools_message += extra_message
                        
                        if websocket_callback:
                            websocket_callback({
                                'type': 'tools_available',
                                'message': tools_message,
                                'tools': tools[:5]
                            })
                    else:
                        tools_message = f"🛠️  Verfügbare Tools: {tools}"
                        print(tools_message)
                        if websocket_callback:
                            websocket_callback({
                                'type': 'tools_available',
                                'message': tools_message
                            })
                except Exception as e:
                    error_message = f"⚠️  Fehler bei Tools-Anzeige: {e} (tools: {tools})"
                    print(error_message)
                    if websocket_callback:
                        websocket_callback({
                            'type': 'error',
                            'message': error_message
                        })
            
            mcp_servers = session_info.get("mcp_servers", [])
            if mcp_servers:
                try:
                    if isinstance(mcp_servers, list):
                        active_servers = [s.get("name", "unknown") for s in mcp_servers if isinstance(s, dict) and s.get("status") == "connected"]
                        if active_servers:
                            servers_str = ', '.join(str(server) for server in active_servers)
                            mcp_message = f"🔌 MCP Server: {servers_str}"
                            print(mcp_message)
                            if websocket_callback:
                                websocket_callback({
                                    'type': 'mcp_servers',
                                    'message': mcp_message,
                                    'servers': active_servers
                                })
                    else:
                        mcp_message = f"🔌 MCP Server: {mcp_servers}"
                        print(mcp_message)
                        if websocket_callback:
                            websocket_callback({
                                'type': 'mcp_servers',
                                'message': mcp_message
                            })
                except Exception as e:
                    error_message = f"⚠️  Fehler bei MCP-Server-Anzeige: {e} (servers: {mcp_servers})"
                    print(error_message)
                    if websocket_callback:
                        websocket_callback({
                            'type': 'error',
                            'message': error_message
                        })
        
        elif msg_type == "user":
            # User Message (unser Prompt)
            session_info["turn_count"] += 1
            turn_message = f"📝 Turn {session_info['turn_count']}: Sende Review-Anfrage..."
            print(turn_message)
            
            if websocket_callback:
                websocket_callback({
                    'type': 'turn_started',
                    'message': turn_message,
                    'turn': session_info['turn_count']
                })
        
        elif msg_type == "assistant":
            # Assistant Response (Claude denkt/antwortet)
            assistant_msg = message.get("message", {})
            content = assistant_msg.get("content", [])
            
            # Prüfe ob Claude "extended thinking" nutzt
            thinking_content = [c for c in content if c.get("type") == "thinking"]
            text_content = [c for c in content if c.get("type") == "text"]
            tool_use = [c for c in content if c.get("type") == "tool_use"]
            
            if thinking_content and not session_info["is_thinking"]:
                session_info["is_thinking"] = True
                thinking_message = f"🧠 Claude analysiert Code mit Extended Thinking..."
                print(thinking_message)
                
                if websocket_callback:
                    websocket_callback({
                        'type': 'thinking',
                        'message': thinking_message
                    })
            
            if tool_use:
                for tool in tool_use:
                    tool_name = tool.get("name", "unknown")
                    tool_message = f"🛠️  Claude verwendet Tool: {tool_name}"
                    print(tool_message)
                    
                    if websocket_callback:
                        websocket_callback({
                            'type': 'tool_use',
                            'message': tool_message,
                            'tool': tool_name
                        })
            
            if text_content:
                session_info["is_thinking"] = False
                # Zeige kurzen Preview des Responses
                text = text_content[0].get("text", "")
                if len(text) > 100:
                    preview = text[:100] + "..."
                else:
                    preview = text
                
                if preview.strip():
                    claude_message = f"💭 Claude: {preview}"
                    print(claude_message)
                    session_info["current_response"] += text
                    
                    if websocket_callback:
                        websocket_callback({
                            'type': 'claude_response',
                            'message': claude_message,
                            'preview': preview,
                            'full_text': text[:500] + "..." if len(text) > 500 else text
                        })
        
        elif msg_type == "result":
            # Final Result
            subtype = message.get("subtype")
            session_info["total_cost"] = message.get("cost_usd", 0)
            session_info["total_duration"] = message.get("duration_ms", 0)
            session_info["api_duration"] = message.get("duration_api_ms", 0)
            session_info["turn_count"] = message.get("num_turns", session_info["turn_count"])
            
            if subtype == "success":
                success_message = f"🎉 Review abgeschlossen!"
                print(success_message)
                
                if websocket_callback:
                    websocket_callback({
                        'type': 'review_completed',
                        'message': success_message,
                        'subtype': subtype,
                        'cost': session_info["total_cost"],
                        'duration': session_info["total_duration"],
                        'turns': session_info["turn_count"]
                    })
            elif subtype == "error_max_turns":
                error_message = f"⚠️  Max turns erreicht ({session_info['turn_count']})"
                print(error_message)
                
                if websocket_callback:
                    websocket_callback({
                        'type': 'review_error',
                        'message': error_message,
                        'subtype': subtype,
                        'turns': session_info["turn_count"]
                    })
            else:
                final_message = f"❌ Review beendet: {subtype}"
                print(final_message)
                
                if websocket_callback:
                    websocket_callback({
                        'type': 'review_error',
                        'message': final_message,
                        'subtype': subtype
                    })
    
    def _run_claude_command(self, prompt, output_format="text", timeout=300):
        """
        Legacy-Methode für Kompatibilität - nutzt neue Progress-Version bei JSON
        """
        if output_format == "stream-json" or output_format == "json":
            # Nutze neue Progress-Methode
            return self._run_claude_command_with_progress(prompt, timeout)
        
        # Original Implementation für text output
        cmd = [
            "claude", 
            "-p", prompt,
            "--output-format", output_format
        ]
        
        print(f"🤖 Führe Claude Code Befehl aus...")
        print(f"📍 Arbeitsverzeichnis: {self.working_path}")
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.working_path,
                capture_output=True,
                text=True,
                check=True,
                timeout=timeout
            )
            
            if output_format == "json":
                try:
                    return json.loads(result.stdout)
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON Parse Error: {e}")
                    return {"error": "Invalid JSON response", "raw_output": result.stdout}
            else:
                return result.stdout.strip()
                
        except subprocess.TimeoutExpired:
            raise Exception(f"Claude Code Timeout nach {timeout} Sekunden")
        except subprocess.CalledProcessError as e:
            error_msg = f"Claude Code Fehler (Exit Code: {e.returncode})"
            if e.stderr:
                error_msg += f"\nStderr: {e.stderr}"
            if e.stdout:
                error_msg += f"\nStdout: {e.stdout}"
            raise Exception(error_msg)
    
    def _run_git_command(self, command):
        """Führt einen Git-Befehl aus und gibt das Ergebnis zurück"""
        try:
            # Convert string command to list for safer execution
            if isinstance(command, str):
                import shlex
                command_list = shlex.split(command)
            else:
                command_list = command
                
            result = subprocess.run(
                command_list,
                shell=False,
                cwd=self.working_path,
                capture_output=True, 
                text=True, 
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            raise Exception(f"Git-Befehl fehlgeschlagen: {' '.join(command_list) if isinstance(command_list, list) else command}\nFehler: {e.stderr}")
    
    def _get_git_diff_summary(self):
        """Holt eine Zusammenfassung der Git-Änderungen"""
        try:
            # Git diff zwischen target branch und current branch
            result = subprocess.run(
                ["git", "diff", "--stat", "origin/main...HEAD"],
                cwd=self.working_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                # Fallback auf master falls main nicht existiert
                result = subprocess.run(
                    ["git", "diff", "--stat", "origin/master...HEAD"],
                    cwd=self.working_path,
                    capture_output=True,
                    text=True
                )
            
            return result.stdout.strip() if result.returncode == 0 else "Keine Diff-Statistiken verfügbar"
            
        except Exception as e:
            return f"Fehler beim Abrufen der Git-Statistiken: {e}"
    
    def _get_changed_files_list(self):
        """Holt die Liste der geänderten Dateien"""
        try:
            result = subprocess.run(
                ["git", "diff", "--name-only", "origin/main...HEAD"],
                cwd=self.working_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                # Fallback auf master
                result = subprocess.run(
                    ["git", "diff", "--name-only", "origin/master...HEAD"],
                    cwd=self.working_path,
                    capture_output=True,
                    text=True
                )
            
            if result.stdout.strip():
                files = result.stdout.strip().split('\n')
                files = [f for f in files if f]  # Entferne leere Strings
                print(f"🔍 Debug: Gefundene Dateien: {files}")
                return files
            else:
                print(f"🔍 Debug: Keine geänderten Dateien gefunden")
                return []
            
        except Exception as e:
            print(f"⚠️  Fehler beim Abrufen der geänderten Dateien: {e}")
            return []
    
    def generate_review_prompt(self, review_type="comprehensive"):
        """
        Generiert den Review-Prompt basierend auf dem Review-Typ
        
        Args:
            review_type: comprehensive, security, performance, style, bugs
        """
        base_context = ""
        if self.pr_url:
            base_context += f"PR URL: {self.pr_url}\n"
        if self.branch_name:
            base_context += f"Branch: {self.branch_name}\n"
        
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        base_context += f"\nÄnderungsstatistik:\n{diff_summary}\n"
        base_context += f"\nGeänderte Dateien ({len(changed_files)}):\n"
        
        if isinstance(changed_files, list):
            for file in changed_files[:10]:  # Limit um Prompt-Größe zu begrenzen
                base_context += f"- {file}\n"
            if len(changed_files) > 10:
                base_context += f"... und {len(changed_files) - 10} weitere Dateien\n"
        else:
            print(f"⚠️  changed_files ist nicht list: {type(changed_files)} = {changed_files}")
            base_context += "- Fehler beim Laden der geänderten Dateien\n"
        
        prompts = {
            "comprehensive": f"""
{base_context}

Führe ein Code Review für diese Pull Request durch. 

**Aufgabe**: Analysiere die geänderten Dateien und erstelle einen strukturierten Review-Report.

**Fokus-Bereiche**:
1. Code Qualität & Architektur
2. Potential Bugs & Edge Cases  
3. Performance & Security
4. Test Coverage

**Format**: 
- Datei-für-Datei Review
- Konkrete Issues mit Code-Beispielen
- Verbesserungsvorschläge
- Gesamtbewertung (1-10)

**Wichtig**: Halte dich fokussiert auf die wichtigsten Findings. Vermeide übermäßige Tool-Verwendung.
""",
            
            "security": f"""
{base_context}

Führe ein Security-fokussiertes Code Review durch. Analysiere alle Änderungen auf Sicherheitsrisiken.

Security Fokus-Bereiche:
1. **Input Validation**: Unvalidierte User-Inputs
2. **Authentication & Authorization**: Zugriffskontrollen
3. **SQL Injection**: Database Query Sicherheit
4. **XSS Prevention**: Cross-Site Scripting Risiken
5. **CSRF Protection**: Cross-Site Request Forgery
6. **Data Exposure**: Sensitive Data Leaks
7. **Dependency Security**: Unsichere Libraries
8. **Secrets Management**: Hardcoded Credentials
9. **Error Handling**: Information Disclosure
10. **API Security**: Endpoint Sicherheit

Für jede identifizierte Schwachstelle:
- Beschreibe das Risiko (OWASP Kategorie)
- Zeige den problematischen Code
- Gib konkrete Lösungsvorschläge
- Bewerte die Kritikalität (Low/Medium/High/Critical)

Erstelle einen Security Report mit prioritisierten Empfehlungen.
""",
            
            "performance": f"""
{base_context}

Analysiere diese Pull Request auf Performance-Aspekte und Optimierungsmöglichkeiten.

Performance Fokus-Bereiche:
1. **Algorithmus-Effizienz**: Big-O Complexity, ineffiziente Loops
2. **Database Performance**: N+1 Queries, fehlende Indizes
3. **Memory Usage**: Memory Leaks, unnötige Objekt-Erstellung
4. **Network Efficiency**: API Call Optimization, Caching
5. **Frontend Performance**: Bundle Size, Rendering Performance
6. **Concurrency**: Threading, Async/Await Patterns
7. **Resource Management**: File Handles, Connection Pooling
8. **Caching Strategy**: Redis, In-Memory Caching
9. **Query Optimization**: Database Query Performance
10. **Load Testing**: Scalability Considerations

Für jede Performance-Issue:
- Identifiziere Bottlenecks
- Messe potentielle Impact
- Schlage Optimierungen vor
- Schätze Performance-Verbesserung

Erstelle Empfehlungen für Performance-Testing und Monitoring.
""",
            
            "style": f"""
{base_context}

Überprüfe diese Pull Request auf Code Style, Konventionen und Konsistenz.

Style & Convention Checks:
1. **Naming Conventions**: Variables, Functions, Classes
2. **Code Formatting**: Indentation, Spacing, Line Length
3. **Documentation**: Comments, Docstrings, README
4. **Project Structure**: File Organization, Module Structure
5. **Consistency**: Mit bestehendem Codebase
6. **Language Idioms**: Best Practices der Programmiersprache
7. **Error Messages**: User-friendly, konsistent
8. **Logging**: Log Levels, Format Konsistenz
9. **Configuration**: Environment Variables, Config Files
10. **Dependencies**: Package Management, Version Pinning

Für jede Style-Issue:
- Zeige inkonsistente Stellen
- Referenziere Style Guide (falls vorhanden)
- Gib Refactoring-Vorschläge
- Priorisiere wichtige vs. kosmetische Änderungen

Fokussiere auf Maintainability und Team-Konsistenz.
""",
            
            "bugs": f"""
{base_context}

Analysiere diese Pull Request auf potentielle Bugs, Logikfehler und Edge Cases.

Bug Detection Focus:
1. **Logic Errors**: Falsche Conditional Logic, Off-by-one Errors
2. **Null/Undefined Handling**: Null Pointer Exceptions
3. **Error Handling**: Unhandled Exceptions, Silent Failures
4. **Edge Cases**: Boundary Conditions, Empty Inputs
5. **Race Conditions**: Concurrency Issues, Thread Safety
6. **Resource Leaks**: Memory, File Handles, DB Connections
7. **Type Safety**: Type Mismatches, Casting Issues
8. **Integration Issues**: API Contract Violations
9. **State Management**: Inconsistent State Updates
10. **Data Validation**: Input Sanitization, Format Validation

Für jeden potentiellen Bug:
- Beschreibe das Problem detailliert
- Zeige den problematischen Code
- Erkläre Reproduktions-Szenario
- Schlage Fix-Lösung vor
- Bewerte Bug-Schweregrad

Erstelle Test-Scenarios um die Bugs zu reproduzieren.
"""
        }
        
        return prompts.get(review_type, prompts["comprehensive"])
    
    def create_claude_md_context(self):
        """
        Erstellt eine CLAUDE.md Datei mit PR-spezifischem Kontext
        für bessere Claude Code Analyse
        """
        claude_md_path = self.working_path / "CLAUDE.md"
        
        # Backup existierende CLAUDE.md falls vorhanden
        if claude_md_path.exists():
            backup_path = claude_md_path.with_suffix('.md.backup')
            claude_md_path.rename(backup_path)
            print(f"📋 Bestehende CLAUDE.md als Backup gespeichert: {backup_path}")
        
        context_content = f"""# PR Review Context

## Pull Request Information
"""
        
        if self.pr_url:
            context_content += f"- **PR URL**: {self.pr_url}\n"
        if self.branch_name:
            context_content += f"- **Branch**: {self.branch_name}\n"
        
        context_content += f"""- **Review Timestamp**: {datetime.now().isoformat()}
- **Worktree Path**: {self.working_path}

## Review Instructions

Du führst ein Code Review für diese Pull Request durch. Fokussiere dich auf:

### Code Quality Kriterien
1. **Funktionalität**: Erfüllt der Code die Anforderungen?
2. **Lesbarkeit**: Ist der Code verständlich und gut dokumentiert?
3. **Wartbarkeit**: Lässt sich der Code einfach erweitern/modifizieren?
4. **Performance**: Gibt es Performance-Probleme oder Optimierungsmöglichkeiten?
5. **Sicherheit**: Existieren Security-Vulnerabilities?
6. **Testing**: Ist der Code ausreichend getestet?

### Analyse-Vorgehen
1. Überprüfe alle geänderten Dateien systematisch
2. Identifiziere Breaking Changes
3. Prüfe auf Regressions-Risiken
4. Bewerte Test-Coverage
5. Checke Dependency-Updates

### Output Format
Strukturiere dein Review als Markdown mit:
- Executive Summary
- Datei-spezifische Reviews
- Identified Issues (categorized by severity)
- Recommendations
- Overall Rating

## Git Commands für Kontext
```bash
# Zeige alle Änderungen seit main branch
git diff origin/main...HEAD

# Zeige geänderte Dateien
git diff --name-only origin/main...HEAD

# Zeige Commit Messages
git log origin/main...HEAD --oneline
```

Verwende diese Kommandos um den vollständigen Kontext der Änderungen zu verstehen.
"""
        
        # Schreibe CLAUDE.md
        with open(claude_md_path, 'w', encoding='utf-8') as f:
            f.write(context_content)
        
        print(f"📝 CLAUDE.md Kontext erstellt: {claude_md_path}")
        return claude_md_path
    
    def perform_review(self, review_types=["comprehensive"], output_file="review_report.md", include_context=True, show_progress=True):
        """
        Führt das Code Review durch und erstellt einen Report
        
        Args:
            review_types: Liste der Review-Typen ["comprehensive", "security", "performance", "style", "bugs"]
            output_file: Pfad zur Output-Datei
            include_context: Erstellt CLAUDE.md für besseren Kontext
        """
        
        print(f"🚀 Starte Code Review mit Claude Code...")
        print(f"📂 Arbeitsverzeichnis: {self.working_path}")
        print(f"🔍 Review-Typen: {', '.join(review_types)}")
        
        results = {}
        claude_md_path = None
        
        try:
            # Erstelle CLAUDE.md Kontext falls gewünscht
            if include_context:
                claude_md_path = self.create_claude_md_context()
            
            # Führe Reviews für jeden Typ durch
            for i, review_type in enumerate(review_types, 1):
                print(f"\n[{i}/{len(review_types)}] 🔍 Führe {review_type.upper()} Review durch...")
                
                prompt = self.generate_review_prompt(review_type)
                
                try:
                    # Führe Claude Code Review durch 
                    if show_progress:
                        review_result = self._run_claude_command_with_progress(
                            prompt, 
                            timeout=600  # 10 Minuten für umfassende Reviews
                        )
                    else:
                        review_result = self._run_claude_command(
                            prompt, 
                            output_format="text",
                            timeout=600
                        )
                    
                    results[review_type] = {
                        "success": True,
                        "content": review_result,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    print(f"✅ {review_type.upper()} Review abgeschlossen")
                    
                except Exception as e:
                    print(f"❌ Fehler bei {review_type.upper()} Review: {e}")
                    results[review_type] = {
                        "success": False,
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    }
            
            # Erstelle zusammengefassten Report
            self._create_comprehensive_report(results, output_file)
            
        finally:
            # Cleanup: Entferne temporäre CLAUDE.md
            if claude_md_path and claude_md_path.exists() and include_context:
                claude_md_path.unlink()
                print(f"🗑️  Temporäre CLAUDE.md entfernt")
                
                # Restore backup falls vorhanden
                backup_path = claude_md_path.with_suffix('.md.backup')
                if backup_path.exists():
                    backup_path.rename(claude_md_path)
                    print(f"♻️  Original CLAUDE.md wiederhergestellt")
    
    def _create_comprehensive_report(self, results, output_file):
        """Erstellt einen umfassenden Markdown-Report aus allen Review-Ergebnissen"""
        
        output_path = self.working_path / output_file
        
        report_content = f"""# Code Review Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Repository:** {self.repo_path}  
**Working Directory:** {self.working_path}  
"""
        
        if self.pr_url:
            report_content += f"**Pull Request:** {self.pr_url}  \n"
        if self.branch_name:
            report_content += f"**Branch:** {self.branch_name}  \n"
        
        report_content += f"""
---

## Executive Summary

Dieser Report wurde automatisch mit Claude Code generiert und analysiert die folgenden Aspekte:
"""
        
        # Zusammenfassung der durchgeführten Reviews
        successful_reviews = [rtype for rtype, result in results.items() if result.get("success")]
        failed_reviews = [rtype for rtype, result in results.items() if not result.get("success")]
        
        if successful_reviews:
            report_content += f"\n✅ **Erfolgreich analysiert:** {', '.join(successful_reviews)}\n"
        
        if failed_reviews:
            report_content += f"\n❌ **Fehlgeschlagen:** {', '.join(failed_reviews)}\n"
        
        # Git Änderungsstatistik
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        report_content += f"""
## Change Summary

```
{diff_summary}
```

**Geänderte Dateien:** {len(changed_files)}

"""
        
        # Detaillierte Review-Ergebnisse
        report_content += "---\n\n"
        
        for review_type, result in results.items():
            report_content += f"## {review_type.upper()} Review\n\n"
            
            if result.get("success"):
                report_content += f"**Status:** ✅ Erfolgreich  \n"
                report_content += f"**Zeitstempel:** {result['timestamp']}  \n\n"
                report_content += result['content']
                report_content += "\n\n---\n\n"
            else:
                report_content += f"**Status:** ❌ Fehlgeschlagen  \n"
                report_content += f"**Zeitstempel:** {result['timestamp']}  \n"
                report_content += f"**Fehler:** {result['error']}\n\n"
                report_content += "---\n\n"
        
        # Footer
        report_content += f"""
## Report Information

- **Generated by:** Claude Code PR Reviewer
- **Claude Code Version:** {self._get_claude_version()}
- **Total Review Types:** {len(results)}
- **Successful Reviews:** {len(successful_reviews)}
- **Working Directory:** `{self.working_path}`

---

*Dieser Report wurde automatisch generiert. Bitte überprüfen Sie die Empfehlungen manuell vor der Implementierung.*
"""
        
        # Schreibe Report
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📊 Review Report erstellt: {output_path}")
        print(f"📈 Erfolgreiche Reviews: {len(successful_reviews)}/{len(results)}")
        
        if successful_reviews:
            print("\n🎯 Review abgeschlossen! Nächste Schritte:")
            print(f"   1. Öffnen Sie den Report: {output_path}")
            print("   2. Überprüfen Sie die Empfehlungen")
            print("   3. Implementieren Sie kritische Fixes")
            print("   4. Update Tests falls nötig")
            print("   5. Re-Review nach Änderungen")
    
    def _create_quick_review_report(self, review_content, review_type, output_file):
        """Erstellt einen Report für Quick Reviews"""
        
        output_path = self.working_path / output_file
        
        # Git Änderungsstatistik
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        report_content_md = f"""# Quick Code Review Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Review Type:** {review_type.upper()}  
**Repository:** {self.repo_path}  
**Working Directory:** {self.working_path}  
"""
        
        if self.pr_url:
            report_content_md += f"**Pull Request:** {self.pr_url}  \n"
        if self.branch_name:
            report_content_md += f"**Branch:** {self.branch_name}  \n"
        
        report_content_md += f"""
---

## Change Summary

```
{diff_summary}
```

**Geänderte Dateien:** {len(changed_files)}

"""
        
        if isinstance(changed_files, list) and changed_files:
            for file in changed_files:
                report_content_md += f"- `{file}`\n"
        
        report_content_md += f"""
---

## Review Results

{review_content}

---

## Report Information

- **Generated by:** Claude Code Quick Reviewer
- **Claude Code Version:** {self._get_claude_version()}
- **Review Duration:** Quick (≤2 minutes)
- **Working Directory:** `{self.working_path}`

---

*Dieser Quick Review Report wurde automatisch generiert. Für detailliertere Analyse führen Sie ein vollständiges Review durch.*
"""
        
        # Schreibe Report
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report_content_md)
        
        print(f"\n📊 Quick Review Report erstellt: {output_path}")
        
        return output_path
    
    def _get_claude_version(self):
        """Holt die Claude Code Version"""
        try:
            result = subprocess.run(
                ["claude", "--version"],
                capture_output=True,
                text=True
            )
            return result.stdout.strip()
        except:
            return "Unknown"
    
    def quick_review(self, focus_area=None, show_progress=True):
        """
        Führt ein schnelles, fokussiertes Review durch
        
        Args:
            focus_area: Spezifischer Fokus (z.B. "security", "performance", "bugs")
        """
        
        if focus_area:
            prompt = self.generate_review_prompt(focus_area)
            review_type = focus_area
        else:
            # Quick comprehensive review
            prompt = f"""
                {self._get_git_diff_summary()}

                Führe ein schnelles Code Review dieser Pull Request durch. Fokussiere dich auf die wichtigsten Issues:

                1. Offensichtliche Bugs oder Logikfehler
                2. Security-Probleme
                3. Performance-Issues
                4. Breaking Changes
                5. Test-Coverage

                Gib eine kurze, prägnante Zusammenfassung mit den wichtigsten Findings und einer Gesamtbewertung.
                """
            review_type = "quick"
        
        print(f"⚡ Führe schnelles {review_type.upper()} Review durch...")
        
        try:
            if show_progress:
                result = self._run_claude_command_with_progress(prompt, timeout=120)
            else:
                result = self._run_claude_command(prompt, timeout=120)  # 2 Minuten
            
            print(f"\n{'='*80}")
            print(f"QUICK REVIEW RESULTS ({review_type.upper()})")
            print(f"{'='*80}")
            print(result)
            print(f"{'='*80}")
            
            return result
            
        except Exception as e:
            print(f"❌ Quick Review fehlgeschlagen: {e}")
            import traceback
            print(f"🔍 Traceback: {traceback.format_exc()}")
            return None


def main():
    parser = argparse.ArgumentParser(
        description='Automatisches Code Review mit Claude Code SDK',
        epilog="""
Beispiele:
  # Review eines bestehenden Worktree
  python claude_code_reviewer.py --worktree /tmp/pr_analysis_xyz

  # Quick Review im aktuellen Verzeichnis  
  python claude_code_reviewer.py --quick

  # Spezifische Review-Typen
  python claude_code_reviewer.py --review-types security performance

  # Mit PR-Kontext
  python claude_code_reviewer.py --pr-url "https://bitbucket.org/workspace/repo/pull-requests/123"
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Verzeichnis-Optionen
    parser.add_argument('--worktree', '-w', 
                       help='Pfad zum Git Worktree (vom PR Analyzer erstellt)')
    parser.add_argument('--repo-path', '-r', default='.',
                       help='Pfad zum Repository (falls kein Worktree)')
    
    # Review-Konfiguration
    parser.add_argument('--review-types', '-t', nargs='+', 
                       choices=['comprehensive', 'security', 'performance', 'style', 'bugs'],
                       default=['comprehensive'],
                       help='Review-Typen (Standard: comprehensive)')
    
    parser.add_argument('--quick', '-q', action='store_true',
                       help='Schnelles Review (überschreibt --review-types)')
    
    parser.add_argument('--focus', '-f',
                       choices=['security', 'performance', 'bugs', 'style'],
                       help='Fokus für Quick Review')
    
    # Output-Optionen
    parser.add_argument('--output', '-o', default='review_report.md',
                       help='Output-Datei für Review Report (Standard: review_report.md)')
    
    parser.add_argument('--no-context', action='store_true',
                       help='Erstellt keine temporäre CLAUDE.md für Kontext')
    
    parser.add_argument('--no-progress', action='store_true',
                       help='Deaktiviert Live-Progress Updates (verwendet legacy Modus)')
    
    # PR-Kontext
    parser.add_argument('--pr-url', '-u',
                       help='Bitbucket PR URL für Kontext')
    
    parser.add_argument('--branch', '-b',
                       help='Branch Name für Kontext')
    
    args = parser.parse_args()
    
    # Bestimme Arbeitsverzeichnis
    worktree_path = args.worktree
    repo_path = args.repo_path
    
    # Validierung
    if worktree_path and not Path(worktree_path).exists():
        print(f"❌ Worktree Pfad existiert nicht: {worktree_path}")
        sys.exit(1)
    
    try:
        reviewer = ClaudeCodeReviewer(
            worktree_path=worktree_path,
            repo_path=repo_path,
            pr_url=args.pr_url,
            branch_name=args.branch
        )
        
        if args.quick:
            # Quick Review
            reviewer.quick_review(focus_area=args.focus, show_progress=not args.no_progress)
        else:
            # Vollständiges Review
            reviewer.perform_review(
                review_types=args.review_types,
                output_file=args.output,
                include_context=not args.no_context,
                show_progress=not args.no_progress
            )
            
    except Exception as e:
        print(f"❌ Fehler: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()