#!/usr/bin/env python3
"""
Prompt Manager for Enhanced Claude Code Reviewer
Manages loading and parsing of prompt templates from markdown files.
Provides clean separation of prompts from code logic.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PromptTemplate:
    """Represents a loaded prompt template"""
    name: str
    content: str
    variables: Dict[str, Any]
    metadata: Dict[str, Any]

class PromptManager:
    """Manages prompt templates and provides them to the reviewer"""
    
    def __init__(self, prompts_dir: Optional[str] = None, config_file: Optional[str] = None):
        """
        Initialize the PromptManager
        
        Args:
            prompts_dir: Directory containing prompt markdown files
            config_file: JSON configuration file for prompt mappings
        """
        self.base_dir = Path(__file__).parent.parent
        self.prompts_dir = Path(prompts_dir) if prompts_dir else self.base_dir / "prompts"
        self.config_file = Path(config_file) if config_file else self.base_dir / "prompt_config.json"
        
        self._prompts: Dict[str, PromptTemplate] = {}
        self._config: Dict[str, Any] = {}
        
        self._load_config()
        self._load_prompts()
        
        logger.info(f"PromptManager initialized with {len(self._prompts)} prompts")
    
    def _load_config(self):
        """Load prompt configuration from JSON file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"Loaded prompt configuration from {self.config_file}")
            else:
                logger.warning(f"Config file {self.config_file} not found, using defaults")
                self._config = self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading prompt config: {e}")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if config file is missing"""
        return {
            "prompt_mappings": {
                "comprehensive": "comprehensive_review.md",
                "comprehensive_with_ac": "comprehensive_review.md",
                "ac_only": "ac_focused_review.md",
                "ac_focused": "ac_focused_review.md", 
                "bug_analysis": "bug_analysis_review.md",
                "code_quality": "bug_analysis_review.md",
                "phase3_summary": "phase3_tutorial.md",
                "system_prompt": "system_prompt.md"
            },
            "variables": {
                "language": "de",
                "output_format": "markdown",
                "include_examples": True
            },
            "prompt_settings": {
                "max_length": 50000,
                "include_metadata": True,
                "variable_substitution": True
            }
        }
    
    def _load_prompts(self):
        """Load all prompt templates from markdown files"""
        if not self.prompts_dir.exists():
            logger.error(f"Prompts directory {self.prompts_dir} does not exist")
            return
        
        prompt_mappings = self._config.get("prompt_mappings", {})
        
        for prompt_type, filename in prompt_mappings.items():
            file_path = self.prompts_dir / filename
            if file_path.exists():
                try:
                    content = self._load_prompt_file(file_path)
                    variables = self._extract_variables(content)
                    metadata = self._extract_metadata(content)
                    
                    self._prompts[prompt_type] = PromptTemplate(
                        name=prompt_type,
                        content=content,
                        variables=variables,
                        metadata=metadata
                    )
                    logger.debug(f"Loaded prompt: {prompt_type} from {filename}")
                except Exception as e:
                    logger.error(f"Error loading prompt {prompt_type} from {filename}: {e}")
            else:
                logger.warning(f"Prompt file {filename} not found for {prompt_type}")
    
    def _load_prompt_file(self, file_path: Path) -> str:
        """Load content from a prompt markdown file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _extract_variables(self, content: str) -> Dict[str, Any]:
        """Extract variable placeholders from prompt content"""
        import re
        
        # Find variables in format {variable_name} or {{variable_name}}
        variables = {}
        variable_pattern = r'\{(\w+)\}'
        
        matches = re.findall(variable_pattern, content)
        for match in matches:
            variables[match] = None  # Will be filled during substitution
        
        return variables
    
    def _extract_metadata(self, content: str) -> Dict[str, Any]:
        """Extract metadata from prompt content (if any)"""
        metadata = {
            "length": len(content),
            "has_examples": "```" in content,
            "has_mermaid": "```mermaid" in content,
            "sections": len([line for line in content.split('\n') if line.startswith('#')])
        }
        return metadata
    
    def get_prompt(self, prompt_type: str, variables: Optional[Dict[str, Any]] = None) -> str:
        """
        Get a prompt template with variable substitution
        
        Args:
            prompt_type: Type of prompt (comprehensive, ac_only, etc.)
            variables: Variables to substitute in the prompt
            
        Returns:
            Formatted prompt string
        """
        if prompt_type not in self._prompts:
            logger.error(f"Prompt type '{prompt_type}' not found")
            raise ValueError(f"Unknown prompt type: {prompt_type}")
        
        template = self._prompts[prompt_type]
        content = template.content
        
        # Apply variable substitution if enabled
        if self._config.get("prompt_settings", {}).get("variable_substitution", True):
            content = self._substitute_variables(content, variables or {})
        
        return content
    
    def _substitute_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """Substitute variables in prompt content"""
        # Get default variables from config
        default_vars = self._config.get("variables", {})
        
        # Merge with provided variables (provided takes precedence)
        all_variables = {**default_vars, **variables}
        
        # Simple variable substitution
        for var_name, var_value in all_variables.items():
            if var_value is not None:
                placeholder = f"{{{var_name}}}"
                content = content.replace(placeholder, str(var_value))
        
        return content
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for Claude Code"""
        return self.get_prompt("system_prompt")
    
    def get_comprehensive_prompt(self, variables: Optional[Dict[str, Any]] = None) -> str:
        """Get comprehensive review prompt"""
        return self.get_prompt("comprehensive", variables)
    
    def get_ac_focused_prompt(self, variables: Optional[Dict[str, Any]] = None) -> str:
        """Get AC-focused review prompt"""
        return self.get_prompt("ac_focused", variables)
    
    def get_bug_analysis_prompt(self, variables: Optional[Dict[str, Any]] = None) -> str:
        """Get bug analysis review prompt"""
        return self.get_prompt("bug_analysis", variables)
    
    def get_phase3_prompt(self, review_content: str, review_type: str) -> str:
        """Get Phase 3 tutorial generation prompt with review context"""
        variables = {
            "review_content": review_content[:2000] + "..." if len(review_content) > 2000 else review_content,
            "review_type": review_type
        }
        return self.get_prompt("phase3_summary", variables)
    
    def get_enhanced_review_prompt(self, review_type: str, ticket_context: Optional[str] = None) -> str:
        """
        Get enhanced review prompt based on review type with additional context
        
        Args:
            review_type: Type of review (comprehensive, ac_only, bug_analysis)
            ticket_context: Optional Jira ticket context to include
            
        Returns:
            Complete prompt string with context
        """
        # Map review types to prompt types
        prompt_mapping = {
            "comprehensive": "comprehensive",
            "comprehensive_with_ac": "comprehensive",
            "ac_only": "ac_focused",
            "ac_focused": "ac_focused",
            "bug_analysis": "bug_analysis",
            "code_quality": "bug_analysis"
        }
        
        prompt_type = prompt_mapping.get(review_type, "comprehensive")
        base_prompt = self.get_prompt(prompt_type)
        
        # Add ticket context if provided
        if ticket_context:
            enhanced_prompt = f"""
## 🎫 JIRA TICKET CONTEXT

{ticket_context}

---

{base_prompt}
"""
        else:
            enhanced_prompt = base_prompt
        
        # Add common instructions that are always appended
        enhanced_prompt += """

⚠️  WICHTIGE ANWEISUNG:
- Erstelle KEINE Dateien oder TODO-Listen
- Verwende KEINE Write, Edit, TodoWrite Tools
- Gib dein Review direkt als Text-Antwort
- Der Report wird automatisch vom System erstellt

Erstelle dein Review als Markdown-Text in deiner Antwort.
"""
        
        return enhanced_prompt
    
    def list_available_prompts(self) -> Dict[str, Dict[str, Any]]:
        """List all available prompts with metadata"""
        return {
            name: {
                "variables": template.variables,
                "metadata": template.metadata
            }
            for name, template in self._prompts.items()
        }
    
    def reload_prompts(self):
        """Reload all prompts from files (useful for development)"""
        self._prompts.clear()
        self._load_config()
        self._load_prompts()
        logger.info("Prompts reloaded successfully")
    
    def validate_prompt_files(self) -> Dict[str, bool]:
        """Validate that all configured prompt files exist and are readable"""
        results = {}
        prompt_mappings = self._config.get("prompt_mappings", {})
        
        for prompt_type, filename in prompt_mappings.items():
            file_path = self.prompts_dir / filename
            try:
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        results[prompt_type] = len(content) > 0
                else:
                    results[prompt_type] = False
            except Exception as e:
                logger.error(f"Error validating {prompt_type}: {e}")
                results[prompt_type] = False
        
        return results

# Singleton instance for global access
_prompt_manager = None

def get_prompt_manager(prompts_dir: Optional[str] = None, config_file: Optional[str] = None) -> PromptManager:
    """Get the global PromptManager instance"""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = PromptManager(prompts_dir, config_file)
    return _prompt_manager

def reload_prompts():
    """Reload prompts in the global instance"""
    global _prompt_manager
    if _prompt_manager:
        _prompt_manager.reload_prompts()