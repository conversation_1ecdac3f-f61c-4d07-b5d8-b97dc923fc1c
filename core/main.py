#!/usr/bin/env python3
"""
Claude Code Reviewer API Service - Main Entry Point
Refactored Flask API Service für Enhanced Code Review Funktionalität.
"""

import logging
from api.app import create_app
from api.routes.health import health_bp
from api.routes.review import review_bp
from api.websocket import WebSocketManager
from services.review_service import ReviewService

logger = logging.getLogger(__name__)


def main():
    """Main entry point for the Claude Code Reviewer API"""
    
    # Create Flask app and SocketIO instance
    app, socketio = create_app()
    
    # Create and inject services
    review_service = ReviewService()
    
    # Setup WebSocket manager
    WebSocketManager.set_socketio(socketio)
    
    # Inject services into route modules
    health_bp.get_session_count = review_service.get_session_count
    
    # Import and set the review service using the setter function
    from api.routes.review import set_review_service
    set_review_service(review_service)
    
    logger.info("🚀 Claude Code Reviewer API starting...")
    logger.info(f"📊 Application configured with {len(app.blueprints)} blueprints")
    
    # Run the application
    try:
        socketio.run(
            app, 
            host='0.0.0.0', 
            port=5002, 
            debug=True,
            allow_unsafe_werkzeug=True
        )
    except KeyboardInterrupt:
        logger.info("🛑 Application shutting down...")
    except Exception as e:
        logger.error(f"❌ Application error: {e}")
        raise


if __name__ == '__main__':
    main()