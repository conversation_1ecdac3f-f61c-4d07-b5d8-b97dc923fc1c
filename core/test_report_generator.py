#!/usr/bin/env python3
"""
Simple test for ReportGenerator to verify it works correctly.
"""

import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock

# Import only what we need
from report_generator import ReportGenerator


def test_report_generator():
    """Test ReportGenerator functionality"""
    
    # Create temporary directory
    test_dir = Path(tempfile.mkdtemp())
    repo_path = test_dir / "test_repo"
    repo_path.mkdir()
    
    try:
        # Initialize a minimal git repo
        import subprocess
        subprocess.run(["git", "init"], cwd=repo_path, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=repo_path, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=repo_path, capture_output=True)
        
        # Create a test file and commit
        test_file = repo_path / "test.py"
        test_file.write_text("print('Hello World')")
        subprocess.run(["git", "add", "."], cwd=repo_path, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=repo_path, capture_output=True)
        
        # Create ReportGenerator
        generator = ReportGenerator(
            working_path=repo_path,
            repo_path=repo_path,
            pr_url="https://github.com/test/repo/pull/123",
            branch_name="feature/test-branch"
        )
        
        # Test review content
        test_review_content = """
# Test Review Content

## Code Quality Analysis
- ✅ Code follows best practices
- ⚠️ Minor style issues found
- ❌ Critical bug detected in authentication

## Acceptance Criteria Analysis
#### ✅ AC 1: User Authentication
**Status:** ERFÜLLT
Implementation correctly validates user credentials.

#### ❌ AC 2: Error Handling
**Status:** NICHT ERFÜLLT
Missing proper error handling for edge cases.
"""
        
        # Mock Jira ticket
        mock_jira_ticket = Mock()
        mock_jira_ticket.ticket_id = "TEST-123"
        mock_jira_ticket.summary = "Test Feature Implementation"
        mock_jira_ticket.issue_type = "Story"
        mock_jira_ticket.status = "In Progress"
        mock_jira_ticket.priority = "High"
        mock_jira_ticket.assignee = "Test User"
        mock_jira_ticket.acceptance_criteria = [
            "User can authenticate with valid credentials",
            "System handles invalid credentials gracefully",
            "Error messages are user-friendly"
        ]
        mock_jira_ticket.labels = ["frontend", "authentication"]
        mock_jira_ticket.components = ["web-app"]
        
        # Generate report
        report = generator.create_enhanced_report(
            review_content=test_review_content,
            review_type="comprehensive_with_ac",
            jira_ticket=mock_jira_ticket,
            jira_integration_enabled=True
        )
        
        # Verify report structure
        expected_sections = [
            "# Enhanced Code Review Report mit Jira Integration",
            "**Generated:**",
            "**Review Type:** COMPREHENSIVE_WITH_AC",
            "**Repository:**",
            "**Working Directory:**",
            "**Pull Request:**",
            "**Branch:**",
            "## 🎫 Jira Ticket Information",
            "- **Ticket ID:** [TEST-123]",
            "- **Summary:** Test Feature Implementation",
            "### 📋 Acceptance Criteria (3 Items)",
            "## 📊 Change Summary",
            "**Geänderte Dateien:**",
            "## 🎯 Enhanced Review Results",
            "## 📈 Report Information",
            "- **Generated by:** Enhanced Claude Code PR Reviewer",
            "- **Jira Integration:** ✅ Enabled",
            "- **Ticket Loaded:** ✅ Yes",
            "- **AC Analysis:** ✅ Included",
            "## 🔧 Next Steps"
        ]
        
        print("🧪 Testing ReportGenerator...")
        
        for section in expected_sections:
            if section not in report:
                print(f"❌ Missing section: {section}")
                return False
            else:
                print(f"✅ Found section: {section}")
        
        # Test without Jira ticket
        print("\n🧪 Testing without Jira ticket...")
        report_no_jira = generator.create_enhanced_report(
            review_content=test_review_content,
            review_type="comprehensive",
            jira_ticket=None,
            jira_integration_enabled=False
        )
        
        no_jira_sections = [
            "⚠️  **Kein Jira Ticket verfügbar für diesen Branch**",
            "- **Jira Integration:** ❌ Disabled",
            "- **Ticket Loaded:** ❌ No",
            "- **AC Analysis:** ❌ Not Available"
        ]
        
        for section in no_jira_sections:
            if section not in report_no_jira:
                print(f"❌ Missing no-jira section: {section}")
                return False
            else:
                print(f"✅ Found no-jira section: {section}")
        
        print("\n✅ All tests passed! ReportGenerator works correctly.")
        print(f"\n📊 Sample report length: {len(report)} characters")
        print(f"📊 No-jira report length: {len(report_no_jira)} characters")
        
        # Show first few lines of report
        print("\n📝 Sample report preview:")
        print("=" * 50)
        print('\n'.join(report.split('\n')[:15]))
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(test_dir)


if __name__ == "__main__":
    success = test_report_generator()
    exit(0 if success else 1)
