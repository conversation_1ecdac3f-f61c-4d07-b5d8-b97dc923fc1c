"""Review Session Model"""

from datetime import datetime
from typing import Dict, Optional


class ReviewSession:
    """Manages a single code review session"""
    
    def __init__(self, session_id: str, config: Dict, branch_name: str, pr_url: Optional[str] = None):
        self.session_id: str = session_id
        self.config: Dict = config
        self.branch_name: str = branch_name
        self.pr_url: Optional[str] = pr_url
        self.status: str = 'initializing'  # initializing, running, completed, error
        self.progress: int = 0
        self.created_at: datetime = datetime.now()
        self.completed_at: Optional[datetime] = None
        self.results: Optional[Dict] = None
        self.error: Optional[str] = None
        self.reviewer = None  # Will be set to EnhancedClaudeReviewer instance
        self.worktree_path: Optional[str] = None
        self.jira_ticket_data: Optional[Dict] = None
        
    def to_dict(self) -> Dict:
        """Convert session to JSON serializable dict"""
        return {
            'session_id': self.session_id,
            'branch_name': self.branch_name,
            'pr_url': self.pr_url,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'results': self.results,
            'error': self.error,
            'worktree_path': self.worktree_path,
            'jira_ticket_data': self.jira_ticket_data
        }