"""API Response Models"""

from typing import Dict, Any, Optional
from datetime import datetime


class APIResponse:
    """Base API response model"""
    
    @staticmethod
    def success(data: Any = None, message: str = None) -> Dict:
        response = {'success': True}
        if data is not None:
            response.update(data if isinstance(data, dict) else {'data': data})
        if message:
            response['message'] = message
        return response
    
    @staticmethod
    def error(message: str, code: int = 500, details: Dict = None) -> tuple[Dict, int]:
        response = {
            'success': False,
            'error': message
        }
        if details:
            response['details'] = details
        return response, code


class HealthResponse:
    """Health check response model"""
    
    @staticmethod
    def healthy(active_sessions: int = 0) -> Dict:
        return {
            'status': 'healthy',
            'service': 'claude-code-reviewer-api',
            'timestamp': datetime.now().isoformat(),
            'active_sessions': active_sessions
        }


class ReviewStatusResponse:
    """Review status response model"""
    
    @staticmethod
    def session_not_found() -> tuple[Dict, int]:
        return APIResponse.error('Review session not found', 404)
    
    @staticmethod
    def review_not_completed(status: str) -> tuple[Dict, int]:
        return APIResponse.error(f'Review not completed yet. Current status: {status}', 400)