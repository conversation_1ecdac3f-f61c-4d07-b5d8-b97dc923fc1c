"""Authentication decorators and utilities"""

import logging
from functools import wraps
from flask import request, jsonify

logger = logging.getLogger(__name__)


def require_auth(f):
    """Decorator to require authentication for API endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning(f"Unauthorized access attempt to {request.endpoint}")
            return jsonify({
                'success': False, 
                'error': 'Authentication required. Please provide a valid Bearer token.'
            }), 401
        
        # Basic token validation (in production, validate against actual auth service)
        token = auth_header.split(' ')[1]
        if not token or len(token) < 10:  # Basic validation
            logger.warning(f"Invalid token provided for {request.endpoint}")
            return jsonify({
                'success': False, 
                'error': 'Invalid authentication token'
            }), 401
        
        return f(*args, **kwargs)
    return decorated_function