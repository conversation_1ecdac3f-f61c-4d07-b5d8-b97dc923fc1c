"""WebSocket handlers for real-time updates"""

import logging
from flask_socketio import emit

logger = logging.getLogger(__name__)

# Global SocketIO instance (will be injected by main app)
socketio = None


class WebSocketManager:
    """Manages WebSocket connections and events"""
    
    @staticmethod
    def set_socketio(socketio_instance):
        """Set the SocketIO instance"""
        global socketio
        socketio = socketio_instance
    
    @staticmethod
    def emit_review_progress(data):
        """Emit review progress updates"""
        if socketio:
            try:
                socketio.emit('review_progress', data)
                logger.debug(f"Emitted review progress: {data.get('message', 'No message')}")
            except Exception as e:
                logger.error(f"Failed to emit review progress: {e}")
        else:
            logger.warning("SocketIO not available for progress updates")
    
    @staticmethod
    def emit_review_completed(data):
        """Emit review completion event"""
        if socketio:
            try:
                socketio.emit('review_completed', data)
                logger.info(f"Emitted review completed for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit review completed: {e}")
        else:
            logger.warning("SocketIO not available for completion event")
    
    @staticmethod
    def emit_review_error(data):
        """Emit review error event"""
        if socketio:
            try:
                socketio.emit('review_error', data)
                logger.error(f"Emitted review error for session {data.get('session_id')}: {data.get('error')}")
            except Exception as e:
                logger.error(f"Failed to emit review error: {e}")
        else:
            logger.warning("SocketIO not available for error event")
    
    @staticmethod
    def emit_claude_progress(data):
        """Emit Claude-specific progress updates"""
        if socketio:
            try:
                socketio.emit('claude_progress', data)
                logger.debug(f"Emitted Claude progress for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit Claude progress: {e}")
        else:
            logger.warning("SocketIO not available for Claude progress updates")
    
    # ========================================
    # NEW: Enhanced WebSocket Events for SDK
    # ========================================
    
    @staticmethod
    def emit_structured_result(data):
        """Emit structured review results from native Claude Code SDK"""
        if socketio:
            try:
                socketio.emit('structured_result', {
                    'session_id': data.get('session_id'),
                    'structured_data': data.get('structured_data', {}),
                    'metadata': data.get('metadata', {}),
                    'timestamp': data.get('timestamp'),
                    'success': True
                })
                logger.info(f"Emitted structured result for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit structured result: {e}")
        else:
            logger.warning("SocketIO not available for structured results")
    
    @staticmethod
    def emit_claude_thinking(data):
        """Emit Claude extended thinking events"""
        if socketio:
            try:
                socketio.emit('claude_thinking', {
                    'session_id': data.get('session_id'),
                    'message': data.get('message', 'Claude is thinking...'),
                    'type': 'thinking',
                    'timestamp': data.get('timestamp')
                })
                logger.debug(f"Emitted Claude thinking for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit Claude thinking: {e}")
        else:
            logger.warning("SocketIO not available for thinking updates")
    
    @staticmethod
    def emit_tool_usage(data):
        """Emit tool usage events from Claude Code SDK"""
        if socketio:
            try:
                socketio.emit('tool_usage', {
                    'session_id': data.get('session_id'),
                    'tool_name': data.get('tool_name'),
                    'message': data.get('message'),
                    'type': 'tool_use',
                    'timestamp': data.get('timestamp')
                })
                logger.debug(f"Emitted tool usage ({data.get('tool_name')}) for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit tool usage: {e}")
        else:
            logger.warning("SocketIO not available for tool usage updates")
    
    @staticmethod
    def emit_session_started(data):
        """Emit session started event with SDK metadata"""
        if socketio:
            try:
                socketio.emit('session_started', {
                    'session_id': data.get('session_id'),
                    'tools': data.get('tools', []),
                    'model': data.get('model'),
                    'cwd': data.get('cwd'),
                    'sdk_enabled': True,
                    'timestamp': data.get('timestamp')
                })
                logger.info(f"Emitted session started for {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit session started: {e}")
        else:
            logger.warning("SocketIO not available for session started event")
    
    @staticmethod
    def emit_phase3_started(data):
        """Emit Phase 3 tutorial generation started event"""
        if socketio:
            try:
                socketio.emit('phase3_started', {
                    'session_id': data.get('session_id'),
                    'message': 'Starting Phase 3: Tutorial & Implementation Summary generation...',
                    'type': 'phase3_started',
                    'progress': data.get('progress', 95),
                    'timestamp': data.get('timestamp')
                })
                logger.info(f"Emitted Phase 3 started for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit Phase 3 started: {e}")
        else:
            logger.warning("SocketIO not available for Phase 3 started event")
    
    @staticmethod
    def emit_phase3_completed(data):
        """Emit Phase 3 tutorial generation completed event"""
        if socketio:
            try:
                socketio.emit('phase3_completed', {
                    'session_id': data.get('session_id'),
                    'tutorial_data': data.get('tutorial_data', {}),
                    'message': 'Phase 3: Tutorial generation completed!',
                    'type': 'phase3_completed',
                    'timestamp': data.get('timestamp')
                })
                logger.info(f"Emitted Phase 3 completed for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit Phase 3 completed: {e}")
        else:
            logger.warning("SocketIO not available for Phase 3 completed event")
    
    @staticmethod
    def emit_streaming_update(data):
        """Emit real-time streaming updates from Claude Code SDK"""
        if socketio:
            try:
                event_type = data.get('type', 'streaming_update')
                
                # Route different types of streaming updates
                if event_type == 'claude_response':
                    WebSocketManager.emit_claude_response_stream(data)
                elif event_type == 'claude_thinking':
                    WebSocketManager.emit_claude_thinking(data)
                elif event_type == 'tool_use':
                    WebSocketManager.emit_tool_usage(data)
                elif event_type == 'session_started':
                    WebSocketManager.emit_session_started(data)
                elif event_type == 'phase3_started':
                    WebSocketManager.emit_phase3_started(data)
                elif event_type == 'phase3_completed':
                    WebSocketManager.emit_phase3_completed(data)
                else:
                    # Generic streaming update
                    socketio.emit('streaming_update', data)
                    logger.debug(f"Emitted generic streaming update for session {data.get('session_id')}")
                    
            except Exception as e:
                logger.error(f"Failed to emit streaming update: {e}")
        else:
            logger.warning("SocketIO not available for streaming updates")
    
    @staticmethod
    def emit_claude_response_stream(data):
        """Emit real-time Claude response streaming"""
        if socketio:
            try:
                socketio.emit('claude_response_stream', {
                    'session_id': data.get('session_id'),
                    'content': data.get('content', ''),
                    'preview': data.get('preview', ''),
                    'partial': True,  # Indicates this is partial content
                    'type': 'claude_response_stream',
                    'timestamp': data.get('timestamp')
                })
                logger.debug(f"Emitted Claude response stream for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit Claude response stream: {e}")
        else:
            logger.warning("SocketIO not available for response streaming")
    
    @staticmethod
    def emit_review_metadata(data):
        """Emit review metadata updates (cost, duration, turns, etc.)"""
        if socketio:
            try:
                socketio.emit('review_metadata', {
                    'session_id': data.get('session_id'),
                    'metadata': data.get('metadata', {}),
                    'cost_usd': data.get('metadata', {}).get('cost_usd', 0),
                    'duration_ms': data.get('metadata', {}).get('duration_ms', 0),
                    'num_turns': data.get('metadata', {}).get('num_turns', 0),
                    'type': 'metadata_update',
                    'timestamp': data.get('timestamp')
                })
                logger.debug(f"Emitted review metadata for session {data.get('session_id')}")
            except Exception as e:
                logger.error(f"Failed to emit review metadata: {e}")
        else:
            logger.warning("SocketIO not available for metadata updates")