from flask import Blueprint, request, jsonify
from datetime import datetime
import logging
import os
import sys
from pathlib import Path

# Add paths for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

try:
    from services.worktree_config_service import WorktreeConfigService, WorktreeConfig, CONFIG_DIR
except ImportError:
    from core.services.worktree_config_service import WorktreeConfigService, WorktreeConfig, CONFIG_DIR

logger = logging.getLogger(__name__)

worktree_bp = Blueprint('worktree', __name__, url_prefix='/api/worktree')
config_service = WorktreeConfigService()

@worktree_bp.route('/config', methods=['GET'])
def get_worktree_config():
    """Get current worktree configuration"""
    try:
        user_id = request.args.get('user_id', 'default')
        config = config_service.get_config(user_id)
        
        return jsonify({
            "success": True,
            "config": {
                "base_path": config.base_path,
                "is_valid": config.is_valid,
                "last_validated": config.last_validated,
                "user_id": config.user_id
            }
        })
    except Exception as e:
        logger.error(f"Failed to get config: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@worktree_bp.route('/config', methods=['POST'])
def save_worktree_config():
    """Save worktree configuration"""
    try:
        data = request.get_json()
        base_path = data.get('base_path')
        user_id = data.get('user_id', 'default')
        
        if not base_path:
            return jsonify({"success": False, "error": "base_path is required"}), 400
        
        # Validate path
        is_valid, message = config_service.validate_path(base_path)
        
        config = WorktreeConfig(
            base_path=base_path,
            is_valid=is_valid,
            last_validated=datetime.now().isoformat(),
            user_id=user_id
        )
        
        success = config_service.save_config(config)
        
        return jsonify({
            "success": success,
            "config": {
                "base_path": config.base_path,
                "is_valid": config.is_valid,
                "last_validated": config.last_validated
            },
            "validation": {
                "is_valid": is_valid,
                "message": message
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to save config: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@worktree_bp.route('/validate', methods=['POST'])
def validate_worktree_path():
    """Validate a worktree path without saving"""
    try:
        data = request.get_json()
        path = data.get('path')
        
        if not path:
            return jsonify({"success": False, "error": "path is required"}), 400
        
        is_valid, message = config_service.validate_path(path)
        
        return jsonify({
            "success": True,
            "validation": {
                "is_valid": is_valid,
                "message": message,
                "path": path
            }
        })
        
    except Exception as e:
        logger.error(f"Failed to validate path: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@worktree_bp.route('/list-dirs', methods=['GET'])
def list_suggested_directories():
    """List suggested directories for worktrees"""
    try:
        suggestions = config_service.list_suggested_directories()
        
        return jsonify({
            "success": True,
            "suggestions": suggestions
        })
        
    except Exception as e:
        logger.error(f"Failed to list directories: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@worktree_bp.route('/browse-directories', methods=['POST', 'OPTIONS'])
def browse_directories():
    """Browse directories and identify Git repositories"""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        path = data.get('path', os.path.expanduser('~'))
        
        # List directories in the given path
        directories = []
        
        if os.path.exists(path) and os.path.isdir(path):
            try:
                items = os.listdir(path)
            except PermissionError:
                return jsonify({
                    'success': False,
                    'error': 'Permission denied'
                }), 403
                
            for item in items:
                item_path = os.path.join(path, item)
                
                # Skip hidden files except .devtools
                if item.startswith('.') and item != '.devtools':
                    continue
                    
                try:
                    if os.path.isdir(item_path):
                        # Check if it's a Git repository
                        is_git_repo = os.path.exists(os.path.join(item_path, '.git'))
                        
                        directories.append({
                            'name': item,
                            'path': item_path,
                            'isDirectory': True,
                            'isGitRepo': is_git_repo
                        })
                except PermissionError:
                    # Skip directories we can't access
                    continue
        
        # Sort directories: Git repos first, then alphabetically
        directories.sort(key=lambda x: (not x['isGitRepo'], x['name'].lower()))
        
        return jsonify({
            'success': True,
            'directories': directories[:20]  # Limit to 20 items
        })
        
    except Exception as e:
        logger.error(f"Error browsing directories: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500