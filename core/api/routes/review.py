"""Code review routes"""

import logging
import sys
import os
from datetime import datetime
from flask import Blueprint, request, jsonify
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from models.responses import APIResponse, ReviewStatusResponse
from services.review_service import ReviewService
from api.auth import require_auth
from api.websocket import WebSocketManager

logger = logging.getLogger(__name__)
review_bp = Blueprint('review', __name__)

# Global review service instance (will be injected by main app)
review_service: ReviewService = None

def set_review_service(service: ReviewService):
    """Set the review service instance for this module"""
    global review_service
    review_service = service


@review_bp.route('/api/code-reviewer/start-review', methods=['POST'])
def start_review():
    """Legacy review endpoint - use start-structured-review for better performance"""
    # Redirect to new structured endpoint
    return start_structured_review()

@review_bp.route('/api/code-reviewer/start-structured-review', methods=['POST'])
def start_structured_review():
    """Start an enhanced code review session with native Claude Code SDK"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['branch_name', 'repository_path']
        for field in required_fields:
            if field not in data:
                return APIResponse.error(f'Missing required field: {field}', 400)
        
        # Extract configuration
        config = data.get('config', {})
        branch_name = data['branch_name']
        pr_url = data.get('pr_url')
        repository_path = data['repository_path']
        review_mode = data.get('review_mode', 'comprehensive')
        include_summary = data.get('include_summary', False)  # Phase 3 tutorial
        jira_ticket_data = data.get('jira_ticket')
        
        # Debug logging for received data
        logger.info(f"🎯 Starting structured review session with SDK:")
        logger.info(f"   Branch: {branch_name}")
        logger.info(f"   PR URL: {pr_url}")
        logger.info(f"   Review Mode: {review_mode}")
        logger.info(f"   Include Summary: {include_summary}")
        logger.info(f"   Repository: {repository_path}")
        
        if jira_ticket_data:
            logger.info(f"🎫 Jira Ticket received from Frontend:")
            logger.info(f"   Ticket ID: {jira_ticket_data.get('ticket_id', 'Unknown')}")
            logger.info(f"   Summary: {jira_ticket_data.get('summary', 'No summary')}")
        else:
            logger.info("⚠️  No Jira ticket data received from Frontend")
        
        # Create enhanced config for SDK
        if not config:
            config = {
                "pr_config": {
                    "source": {
                        "type": "branch",
                        "value": branch_name,
                        "pr_url": pr_url
                    },
                    "repository": {
                        "path": repository_path,
                        "worktree_options": {
                            "create": True,
                            "cleanup_after": False
                        }
                    }
                },
                "jira_config": {
                    "enabled": False,  # Disable automatic loading
                    "server_url": "https://regionalmedienaustria.atlassian.net", 
                    "ticket_data": jira_ticket_data,  # Use ticket data from frontend
                    "ticket_extraction": {
                        "auto_extract_from_branch": False,  # Disable auto-extraction
                        "branch_patterns": []
                    }
                },
                "review_config": {
                    "type": review_mode,
                    "include_summary": include_summary,  # Enable Phase 3 if requested
                    "focus_areas": _get_focus_areas_for_mode(review_mode),
                    "output": {
                        "include_live_progress": True,
                        "include_context": True,
                        "structured_output": True  # Enable structured JSON output
                    },
                    "claude_code_sdk_options": {
                        "max_turns": 25 if review_mode == 'comprehensive' else 20,
                        "timeout_seconds": 1200,  # Increased for SDK
                        "enhanced_thinking": True,
                        "native_streaming": True
                    }
                }
            }
        
        # Ensure review service is available - create if needed
        global review_service
        if not review_service:
            logger.warning("Review service not injected, creating new instance")
            review_service = ReviewService()
            
        # Create review session
        session = review_service.create_session(config, branch_name, pr_url, jira_ticket_data)
        
        # Start SDK-based review in background thread
        def sdk_review_executor(session_obj):
            try:
                # Use the new SDK-based method
                result = review_service.execute_structured_review_sync(
                    session_obj, 
                    progress_callback=websocket_progress_callback
                )
                logger.info(f"✅ SDK review completed for session {session_obj.session_id}")
            except Exception as e:
                logger.error(f"❌ SDK review failed for session {session_obj.session_id}: {e}")
                # Emit error via WebSocket
                if websocket_progress_callback:
                    websocket_progress_callback({
                        'session_id': session_obj.session_id,
                        'type': 'review_error',
                        'error': str(e),
                        'message': f'Review failed: {str(e)}'
                    })
            finally:
                review_service.cleanup_thread(session_obj.session_id)
        
        def websocket_progress_callback(*args, **kwargs):
            """Enhanced WebSocket progress callback for SDK"""
            try:
                # Handle multiple argument signatures from SDK
                if len(args) == 1:
                    data = args[0]
                elif len(args) == 3:
                    # SDK might be passing (session_id, progress, data) or similar
                    data = args[2] if isinstance(args[2], dict) else {'message': str(args[2])}
                else:
                    data = kwargs.get('data', {})
                
                # Ensure data is a dictionary
                if not isinstance(data, dict):
                    data = {'message': str(data)}
                
                # Forward all SDK events to WebSocket manager
                event_type = data.get('type', 'unknown')
                
                if event_type == 'review_error':
                    WebSocketManager.emit_review_error(data)
                elif event_type == 'review_completed':
                    WebSocketManager.emit_structured_result(data)
                elif event_type in ['claude_response', 'claude_thinking', 'tool_use']:
                    WebSocketManager.emit_claude_progress(data)
                elif event_type == 'session_started':
                    WebSocketManager.emit_review_progress({
                        **data,
                        'message': 'Claude Code SDK session started'
                    })
                elif event_type == 'phase3_started':
                    WebSocketManager.emit_review_progress({
                        **data,
                        'message': 'Starting Phase 3: Tutorial generation...'
                    })
                else:
                    # Default progress update
                    WebSocketManager.emit_review_progress(data)
                    
            except Exception as e:
                logger.error(f"WebSocket callback error: {e}")
        
        # Start the SDK review thread
        review_service.start_review_thread(session, sdk_review_executor)
        
        return APIResponse.success({
            'session': {
                'session_id': session.session_id,
                'branch_name': branch_name,
                'status': 'initializing',
                'progress': 0,
                'progress_message': 'Enhanced SDK review session started',
                'created_at': datetime.now().isoformat(),
                'sdk_enabled': True,
                'structured_output': True,
                'include_summary': include_summary
            }
        }, f'Enhanced SDK review session started for branch {branch_name}')
        
    except Exception as e:
        logger.error(f"Error starting structured review: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/review-status/<session_id>', methods=['GET'])
def get_review_status(session_id: str):
    """Get the status of a review session"""
    try:
        # Ensure review service is available
        if not review_service:
            logger.warning("Review service not available for status check")
            return APIResponse.error('Review service not available', 500)
            
        session = review_service.get_session(session_id)
        if not session:
            return ReviewStatusResponse.session_not_found()
        
        return APIResponse.success({'session': session.to_dict()})
        
    except Exception as e:
        logger.error(f"Error getting review status: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/review-results/<session_id>', methods=['GET'])
def get_review_results(session_id: str):
    """Get the results of a completed review session"""
    try:
        session = review_service.get_session(session_id)
        if not session:
            return ReviewStatusResponse.session_not_found()
        
        if session.status != 'completed':
            return ReviewStatusResponse.review_not_completed(session.status)
        
        return APIResponse.success({
            'session_id': session_id,
            'results': session.results
        })
        
    except Exception as e:
        logger.error(f"Error getting review results: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/sessions', methods=['GET'])
def list_review_sessions():
    """List all review sessions"""
    try:
        # Ensure review service is available
        if not review_service:
            return APIResponse.success({'sessions': [], 'total': 0})
            
        sessions_data = review_service.list_sessions()
        return APIResponse.success(sessions_data)
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/assigned-prs', methods=['GET'])
def get_assigned_prs():
    """Get pull requests assigned to the current user for review"""
    try:
        # Note: This endpoint expects the frontend to handle Bitbucket OAuth
        # and pass PRs to the review workflow. The frontend has full Bitbucket integration.
        # For direct API access, Bitbucket integration would need to be added to backend.
        
        logger.info("Assigned PRs endpoint called - Frontend handles Bitbucket integration")
        
        return APIResponse.success({
            'prs': [],
            'total': 0,
            'message': 'Use frontend Bitbucket integration for PR fetching. Backend focuses on code review execution.'
        })
        
    except Exception as e:
        logger.error(f"Error in assigned PRs endpoint: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/generate-tutorial/<session_id>', methods=['POST'])
def generate_tutorial(session_id: str):
    """Generate Phase 3 tutorial documentation using native Claude Code SDK"""
    try:
        session = review_service.get_session(session_id)
        if not session:
            return APIResponse.error('Review session not found', 404)
        
        if session.status != 'completed':
            return APIResponse.error('Review must be completed before generating tutorial', 400)
        
        logger.info(f"🎯 Starting Phase 3 tutorial generation for session {session_id}")
        
        # Check if tutorial was already generated during initial review
        if session.results and session.results.get('phase3_summary'):
            logger.info(f"✅ Tutorial already exists for session {session_id}")
            return APIResponse.success({
                'tutorial': session.results['phase3_summary'],
                'session_id': session_id,
                'cached': True,
                'generated_at': session.completed_at.isoformat() if session.completed_at else None
            })
        
        # Generate tutorial asynchronously in background thread
        tutorial_result = {'error': None, 'data': None}
        
        def tutorial_executor():
            try:
                # Use the async tutorial generation method
                import asyncio
                tutorial_data = asyncio.run(
                    review_service.execute_phase3_tutorial_async(session)
                )
                tutorial_result['data'] = tutorial_data
                
                # Store tutorial in session for future requests
                if not session.results:
                    session.results = {}
                session.results['phase3_summary'] = tutorial_data
                
                logger.info(f"✅ Phase 3 tutorial completed for session {session_id}")
                
            except Exception as e:
                tutorial_result['error'] = str(e)
                logger.error(f"❌ Phase 3 tutorial failed for session {session_id}: {e}")
        
        # Run tutorial generation
        import threading
        tutorial_thread = threading.Thread(target=tutorial_executor, daemon=True)
        tutorial_thread.start()
        tutorial_thread.join(timeout=300)  # 5 minute timeout
        
        if tutorial_result['error']:
            return APIResponse.error(f"Tutorial generation failed: {tutorial_result['error']}", 500)
        
        if not tutorial_result['data']:
            return APIResponse.error("Tutorial generation timed out", 500)
        
        return APIResponse.success({
            'tutorial': tutorial_result['data'],
            'session_id': session_id,
            'cached': False,
            'generated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"Error generating tutorial: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/tutorial-status/<session_id>', methods=['GET'])
def get_tutorial_status(session_id: str):
    """Check if Phase 3 tutorial is available for a session"""
    try:
        session = review_service.get_session(session_id)
        if not session:
            return APIResponse.error('Review session not found', 404)
        
        has_tutorial = (
            session.results and 
            session.results.get('phase3_summary') is not None
        )
        
        tutorial_data = None
        if has_tutorial:
            tutorial_data = session.results.get('phase3_summary', {})
        
        return APIResponse.success({
            'session_id': session_id,
            'has_tutorial': has_tutorial,
            'tutorial_available': has_tutorial,
            'review_completed': session.status == 'completed',
            'tutorial_metadata': {
                'generated_at': tutorial_data.get('generated_at') if tutorial_data else None,
                'sections_count': len(tutorial_data.get('structured_sections', {})) if tutorial_data else 0,
                'has_diagrams': len(tutorial_data.get('mermaid_diagrams', [])) > 0 if tutorial_data else False,
                'has_code_examples': len(tutorial_data.get('code_examples', [])) > 0 if tutorial_data else False
            }
        })
        
    except Exception as e:
        logger.error(f"Error checking tutorial status: {e}")
        return APIResponse.error(str(e), 500)


@review_bp.route('/api/code-reviewer/tutorial/<session_id>', methods=['GET'])
def get_tutorial(session_id: str):
    """Get the Phase 3 tutorial for a completed review session"""
    try:
        session = review_service.get_session(session_id)
        if not session:
            return APIResponse.error('Review session not found', 404)
        
        if not session.results or not session.results.get('phase3_summary'):
            return APIResponse.error('Tutorial not available. Generate tutorial first.', 404)
        
        tutorial_data = session.results['phase3_summary']
        
        return APIResponse.success({
            'tutorial': tutorial_data,
            'session_id': session_id,
            'metadata': {
                'generated_at': tutorial_data.get('generated_at'),
                'branch_name': tutorial_data.get('branch_name'),
                'pr_url': tutorial_data.get('pr_url'),
                'tutorial_id': tutorial_data.get('tutorial_id')
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting tutorial: {e}")
        return APIResponse.error(str(e), 500)


def _get_focus_areas_for_mode(review_mode: str) -> list:
    """Get focus areas based on review mode (fallback when service not injected)"""
    focus_areas_map = {
        'quick': ['code_quality', 'security'],
        'comprehensive': ['acceptance_criteria_compliance', 'code_quality', 'security', 'testing'],
        'ac-only': ['acceptance_criteria_compliance'],
        'bug-analysis': ['code_quality', 'testing', 'error_handling'],
        'security': ['security', 'input_validation', 'authentication'],
        'performance': ['performance', 'scalability', 'optimization']
    }
    
    return focus_areas_map.get(review_mode, ['code_quality', 'security'])