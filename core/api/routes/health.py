"""Health check routes"""

from flask import Blueprint, jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from models.responses import HealthResponse

health_bp = Blueprint('health', __name__)

# Global state for session counting (will be injected by main app)
get_session_count = lambda: 0


@health_bp.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify(HealthResponse.healthy(active_sessions=get_session_count()))