#!/usr/bin/env python3
"""
Test script to verify that API and CLI generate identical Markdown reports.
This ensures 100% character-for-character equality as specified in the PRD.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from enhanced_claude_reviewer import <PERSON>hancedClaudeReviewer
from enhanced_claude_reviewer_sdk import EnhancedClaudeReviewerSDK
from jira_integration import JiraTicket
from report_generator import ReportGenerator


class TestReportEquality(unittest.TestCase):
    """Test that CLI and API generate identical reports"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.repo_path = self.test_dir / "test_repo"
        self.repo_path.mkdir()
        
        # Initialize a git repo for testing
        import subprocess
        subprocess.run(["git", "init"], cwd=self.repo_path, capture_output=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=self.repo_path, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=self.repo_path, capture_output=True)
        
        # Create a test file and commit
        test_file = self.repo_path / "test.py"
        test_file.write_text("print('Hello World')")
        subprocess.run(["git", "add", "."], cwd=self.repo_path, capture_output=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=self.repo_path, capture_output=True)
        
        # Create test data
        self.test_review_content = """
# Test Review Content

## Code Quality Analysis
- ✅ Code follows best practices
- ⚠️ Minor style issues found
- ❌ Critical bug detected in authentication

## Acceptance Criteria Analysis
#### ✅ AC 1: User Authentication
**Status:** ERFÜLLT
Implementation correctly validates user credentials.

#### ❌ AC 2: Error Handling
**Status:** NICHT ERFÜLLT
Missing proper error handling for edge cases.

## Action Items
### 🚨 CRITICAL
- Fix authentication vulnerability
- Add input validation

### ⚠️ IMPORTANT  
- Improve error messages
- Add logging

### 💡 SUGGESTIONS
- Consider using TypeScript
- Add unit tests
"""
        
        self.test_jira_ticket = Mock(spec=JiraTicket)
        self.test_jira_ticket.ticket_id = "TEST-123"
        self.test_jira_ticket.summary = "Test Feature Implementation"
        self.test_jira_ticket.issue_type = "Story"
        self.test_jira_ticket.status = "In Progress"
        self.test_jira_ticket.priority = "High"
        self.test_jira_ticket.assignee = "Test User"
        self.test_jira_ticket.acceptance_criteria = [
            "User can authenticate with valid credentials",
            "System handles invalid credentials gracefully",
            "Error messages are user-friendly"
        ]
        self.test_jira_ticket.labels = ["frontend", "authentication"]
        self.test_jira_ticket.components = ["web-app"]
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.test_dir)
    
    def test_report_generator_creates_consistent_output(self):
        """Test that ReportGenerator creates consistent output"""
        generator = ReportGenerator(
            working_path=self.repo_path,
            repo_path=self.repo_path,
            pr_url="https://github.com/test/repo/pull/123",
            branch_name="feature/test-branch"
        )
        
        # Generate report twice
        report1 = generator.create_enhanced_report(
            review_content=self.test_review_content,
            review_type="comprehensive_with_ac",
            jira_ticket=self.test_jira_ticket,
            jira_integration_enabled=True
        )
        
        report2 = generator.create_enhanced_report(
            review_content=self.test_review_content,
            review_type="comprehensive_with_ac", 
            jira_ticket=self.test_jira_ticket,
            jira_integration_enabled=True
        )
        
        # Reports should be identical (except timestamp)
        # We'll compare structure and key content
        self.assertIn("# Enhanced Code Review Report mit Jira Integration", report1)
        self.assertIn("# Enhanced Code Review Report mit Jira Integration", report2)
        self.assertIn("TEST-123", report1)
        self.assertIn("TEST-123", report2)
        self.assertIn("Test Feature Implementation", report1)
        self.assertIn("Test Feature Implementation", report2)
    
    @patch('enhanced_claude_reviewer_sdk.subprocess.run')
    def test_api_uses_report_generator(self, mock_subprocess):
        """Test that API SDK uses ReportGenerator for consistent output"""
        # Mock git commands
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "test.py | 1 +\n 1 file changed, 1 insertion(+)"
        
        config = {
            'pr_config': {
                'repository': {'path': str(self.repo_path)}
            }
        }
        
        sdk = EnhancedClaudeReviewerSDK(
            config=config,
            worktree_path=self.repo_path,
            repo_path=self.repo_path,
            pr_url="https://github.com/test/repo/pull/123",
            branch_name="feature/test-branch"
        )
        
        # Mock Claude result
        mock_claude_result = {
            "result": self.test_review_content,
            "cost_usd": 0.15,
            "duration_ms": 45000,
            "num_turns": 1,
            "is_error": False
        }
        
        # Test the new API response format
        result = sdk._structure_review_result(
            claude_result=mock_claude_result,
            review_type="comprehensive_with_ac",
            jira_ticket=self.test_jira_ticket
        )
        
        # Verify new API response structure
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["review_type"], "comprehensive_with_ac")
        self.assertIn("markdown_report", result)
        self.assertIn("metadata", result)
        
        # Verify markdown report contains expected content
        markdown_report = result["markdown_report"]
        self.assertIn("# Enhanced Code Review Report mit Jira Integration", markdown_report)
        self.assertIn("TEST-123", markdown_report)
        self.assertIn("Test Feature Implementation", markdown_report)
        self.assertIn(self.test_review_content.strip(), markdown_report)
        
        # Verify metadata
        metadata = result["metadata"]
        self.assertEqual(metadata["cost_usd"], 0.15)
        self.assertEqual(metadata["duration_ms"], 45000)
        self.assertEqual(metadata["repository"], str(self.repo_path))
        self.assertEqual(metadata["branch_name"], "feature/test-branch")
    
    def test_report_structure_matches_cli_example(self):
        """Test that generated report matches CLI structure from PRD example"""
        generator = ReportGenerator(
            working_path=self.repo_path,
            repo_path=self.repo_path,
            pr_url="https://bitbucket.org/test/repo/pull-requests/404",
            branch_name="CMS20-1248-test-feature"
        )
        
        report = generator.create_enhanced_report(
            review_content=self.test_review_content,
            review_type="comprehensive_with_ac",
            jira_ticket=self.test_jira_ticket,
            jira_integration_enabled=True
        )
        
        # Verify report structure matches CLI example
        expected_sections = [
            "# Enhanced Code Review Report mit Jira Integration",
            "**Generated:**",
            "**Review Type:** COMPREHENSIVE_WITH_AC",
            "**Repository:**",
            "**Working Directory:**",
            "**Pull Request:**",
            "**Branch:**",
            "## 🎫 Jira Ticket Information",
            "- **Ticket ID:** [TEST-123]",
            "- **Summary:** Test Feature Implementation",
            "### 📋 Acceptance Criteria (3 Items)",
            "## 📊 Change Summary",
            "**Geänderte Dateien:**",
            "## 🎯 Enhanced Review Results",
            "## 📈 Report Information",
            "- **Generated by:** Enhanced Claude Code PR Reviewer",
            "- **Claude Code Version:**",
            "- **Jira Integration:** ✅ Enabled",
            "- **Ticket Loaded:** ✅ Yes",
            "- **AC Analysis:** ✅ Included",
            "## 🔧 Next Steps"
        ]
        
        for section in expected_sections:
            self.assertIn(section, report, f"Missing section: {section}")
    
    def test_no_jira_ticket_scenario(self):
        """Test report generation when no Jira ticket is available"""
        generator = ReportGenerator(
            working_path=self.repo_path,
            repo_path=self.repo_path,
            pr_url=None,
            branch_name="feature/no-ticket"
        )
        
        report = generator.create_enhanced_report(
            review_content=self.test_review_content,
            review_type="comprehensive",
            jira_ticket=None,
            jira_integration_enabled=False
        )
        
        # Verify no-ticket scenario
        self.assertIn("⚠️  **Kein Jira Ticket verfügbar für diesen Branch**", report)
        self.assertIn("- **Jira Integration:** ❌ Disabled", report)
        self.assertIn("- **Ticket Loaded:** ❌ No", report)
        self.assertIn("- **AC Analysis:** ❌ Not Available", report)


if __name__ == "__main__":
    unittest.main()
