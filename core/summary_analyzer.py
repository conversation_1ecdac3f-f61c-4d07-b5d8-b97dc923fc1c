"""
Phase 3: Summary & Explanation Module

This module handles the creation of comprehensive tutorial-style documentation
for code changes, providing broader codebase context and implementation analysis.

Extracted from enhanced_claude_reviewer.py for better maintainability.
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional


class SummaryAnalyzer:
    """
    Handles Phase 3: Summary & Explanation functionality
    Creates comprehensive tutorial-style documentation for code reviews
    """
    
    def __init__(self, working_path: Path, branch_name: str, repo_path: Path, 
                 pr_url: str = None, current_ticket = None, jira_integration = None):
        """
        Initialize the Summary Analyzer
        
        Args:
            working_path: Path to the worktree directory
            branch_name: Name of the current branch
            repo_path: Path to the main repository
            pr_url: Optional PR URL
            current_ticket: Optional Jira ticket object
            jira_integration: Optional Jira integration object
        """
        self.working_path = working_path
        self.branch_name = branch_name
        self.repo_path = repo_path
        self.pr_url = pr_url
        self.current_ticket = current_ticket
        self.jira_integration = jira_integration
    
    def perform_summary_phase(self, review_result: str, review_type: str, 
                            run_claude_command_func, get_changed_files_func, 
                            get_git_diff_summary_func, generate_ticket_context_func,
                            get_claude_version_func) -> str:
        """
        Perform Phase 3: Summary & Explanation - Create comprehensive tutorial-style documentation
        
        Args:
            review_result: Results from previous review phases
            review_type: Type of review being performed
            run_claude_command_func: Function to run Claude commands
            get_changed_files_func: Function to get list of changed files
            get_git_diff_summary_func: Function to get git diff summary
            generate_ticket_context_func: Function to generate ticket context
            get_claude_version_func: Function to get Claude version
            
        Returns:
            str: The generated summary content
        """
        print(f"🚀 Starte Phase 3: Summary & Explanation...")
        print(f"📂 Arbeitsverzeichnis: {self.working_path}")
        
        try:
            # Store function references for use in other methods
            self._run_claude_command_func = run_claude_command_func
            self._get_changed_files_func = get_changed_files_func
            self._get_git_diff_summary_func = get_git_diff_summary_func
            self._generate_ticket_context_func = generate_ticket_context_func
            self._get_claude_version_func = get_claude_version_func
            
            # Analyze broader codebase context
            print("🔍 Analysiere Codebase-Kontext...")
            codebase_context = self._analyze_codebase_context()
            
            # Generate comprehensive summary prompt
            print("📝 Generiere Summary-Prompt...")
            summary_prompt = self._generate_summary_prompt(review_result, review_type, codebase_context)
            
            # Execute summary generation
            print("🤖 Führe Summary-Generierung durch...")
            summary_result = run_claude_command_func(
                summary_prompt,
                timeout=900,  # 15 minutes for comprehensive analysis
                max_turns=25
            )
            
            # Ensure summary_result is a string
            if isinstance(summary_result, dict):
                summary_str = json.dumps(summary_result, indent=2) if summary_result else ""
            elif summary_result is None:
                summary_str = ""
            else:
                summary_str = str(summary_result)
            
            # Create summary document
            print("📊 Erstelle Summary-Dokument...")
            summary_path = self._create_summary_document(summary_str, review_type, codebase_context)
            
            print(f"✅ Phase 3: Summary & Explanation abgeschlossen!")
            print(f"📁 Summary Dokument: {summary_path}")
            
            return summary_str
            
        except Exception as e:
            print(f"❌ Phase 3: Summary & Explanation fehlgeschlagen: {e}")
            raise e
    
    def _analyze_codebase_context(self) -> dict:
        """
        Analyze broader codebase context beyond just changed files
        """
        context = {
            'dependencies': {},
            'related_files': [],
            'architectural_patterns': [],
            'integration_points': [],
            'business_domain': {}
        }
        
        try:
            # Get changed files for context
            changed_files = self._get_changed_files_func()
            if not isinstance(changed_files, list):
                changed_files = []
            
            # Analyze dependencies and imports
            context['dependencies'] = self._analyze_imports_and_dependencies(changed_files)
            
            # Find related files in the codebase
            context['related_files'] = self._find_related_files(changed_files)
            
            # Identify architectural patterns
            context['architectural_patterns'] = self._identify_architectural_patterns()
            
            # Analyze integration points
            context['integration_points'] = self._analyze_integration_points(changed_files)
            
            # Business domain analysis
            context['business_domain'] = self._analyze_business_domain_context()
            
        except Exception as e:
            print(f"⚠️  Codebase context analysis teilweise fehlgeschlagen: {e}")
        
        return context
    
    def _analyze_imports_and_dependencies(self, changed_files: List[str]) -> dict:
        """Analyze import chains and dependency relationships"""
        dependencies = {
            'imports': [],
            'exports': [],
            'internal_deps': [],
            'external_deps': []
        }
        
        try:
            for file_path in changed_files[:10]:  # Limit to avoid performance issues
                full_path = self.working_path / file_path
                if full_path.exists() and full_path.suffix in ['.js', '.ts', '.tsx', '.jsx']:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # Extract import statements
                    import_lines = [line.strip() for line in content.split('\n') 
                                  if line.strip().startswith(('import ', 'const ', 'require('))]
                    dependencies['imports'].extend(import_lines[:20])  # Limit imports
                    
                    # Extract export statements
                    export_lines = [line.strip() for line in content.split('\n') 
                                  if line.strip().startswith('export ')]
                    dependencies['exports'].extend(export_lines[:10])  # Limit exports
                    
        except Exception as e:
            print(f"⚠️  Import analysis fehlgeschlagen: {e}")
            
        return dependencies
    
    def _find_related_files(self, changed_files: List[str]) -> List[str]:
        """Find files that might be related to the changed files"""
        related_files = []
        
        try:
            # Look for files with similar names or in same directories
            for changed_file in changed_files[:5]:  # Limit to avoid performance issues
                file_dir = Path(changed_file).parent
                file_stem = Path(changed_file).stem
                
                # Find files in same directory
                search_pattern = f"{file_dir}/*"
                try:
                    # Use a simple approach to find related files
                    if file_dir != Path('.'):
                        related_files.append(f"Directory: {file_dir}")
                except:
                    pass
                    
        except Exception as e:
            print(f"⚠️  Related files analysis fehlgeschlagen: {e}")
            
        return related_files[:20]  # Limit results
    
    def _identify_architectural_patterns(self) -> List[str]:
        """Identify architectural patterns used in the codebase"""
        patterns = []
        
        try:
            # Check for common RMA monorepo patterns based on CLAUDE.md context
            common_patterns = [
                "Monorepo with npm workspaces",
                "Microservices architecture",
                "Express.js backend services", 
                "Remix frontend applications",
                "Shared utility packages",
                "PandaCSS design system",
                "Directus CMS integration"
            ]
            patterns.extend(common_patterns)
            
        except Exception as e:
            print(f"⚠️  Pattern analysis fehlgeschlagen: {e}")
            
        return patterns
    
    def _analyze_integration_points(self, changed_files: List[str]) -> List[str]:
        """Analyze API endpoints, database connections, and service integrations"""
        integration_points = []
        
        try:
            for file_path in changed_files[:5]:  # Limit analysis
                if 'service' in file_path.lower() or 'api' in file_path.lower():
                    integration_points.append(f"Service/API file: {file_path}")
                elif 'controller' in file_path.lower():
                    integration_points.append(f"Controller file: {file_path}")
                elif 'route' in file_path.lower():
                    integration_points.append(f"Route file: {file_path}")
                    
        except Exception as e:
            print(f"⚠️  Integration analysis fehlgeschlagen: {e}")
            
        return integration_points
    
    def _analyze_business_domain_context(self) -> dict:
        """Analyze business domain context from ticket and code"""
        business_context = {
            'domain': 'Unknown',
            'user_impact': 'Unknown', 
            'business_value': 'Unknown'
        }
        
        try:
            if self.current_ticket:
                # Extract business context from ticket
                business_context['domain'] = self.current_ticket.summary[:100]
                business_context['business_value'] = "Based on Jira ticket requirements"
                
                if self.current_ticket.description:
                    if 'user' in self.current_ticket.description.lower():
                        business_context['user_impact'] = "User-facing changes identified"
                        
        except Exception as e:
            print(f"⚠️  Business context analysis fehlgeschlagen: {e}")
            
        return business_context
    
    def _generate_summary_prompt(self, review_result: str, review_type: str, codebase_context: dict) -> str:
        """
        Generate comprehensive prompt for Phase 3: Summary & Explanation
        """
        # Get base context
        base_context = ""
        if self.pr_url:
            base_context += f"PR URL: {self.pr_url}\n"
        if self.branch_name:
            base_context += f"Branch: {self.branch_name}\n"
        
        diff_summary = self._get_git_diff_summary_func()
        changed_files = self._get_changed_files_func()
        
        base_context += f"\nÄnderungsstatistik:\n{diff_summary}\n"
        base_context += f"\nGeänderte Dateien ({len(changed_files)}):\n"
        
        if isinstance(changed_files, list):
            for file in changed_files[:10]:
                base_context += f"- {file}\n"
            if len(changed_files) > 10:
                base_context += f"... und {len(changed_files) - 10} weitere Dateien\n"
        
        # Add Jira ticket context if available
        ticket_context = ""
        if self.current_ticket and self._generate_ticket_context_func:
            ticket_context = self._generate_ticket_context_func()
        
        # Generate comprehensive codebase context
        context_info = f"""
## 🏗️ CODEBASE CONTEXT ANALYSIS

### Architectural Patterns:
{chr(10).join(f"- {pattern}" for pattern in codebase_context.get('architectural_patterns', []))}

### Dependencies & Imports:
- Import statements: {len(codebase_context.get('dependencies', {}).get('imports', []))} found
- Export statements: {len(codebase_context.get('dependencies', {}).get('exports', []))} found

### Integration Points:
{chr(10).join(f"- {point}" for point in codebase_context.get('integration_points', []))}

### Business Domain:
- Domain: {codebase_context.get('business_domain', {}).get('domain', 'Unknown')}
- User Impact: {codebase_context.get('business_domain', {}).get('user_impact', 'Unknown')}
- Business Value: {codebase_context.get('business_domain', {}).get('business_value', 'Unknown')}
"""
        
        # Create comprehensive summary prompt
        return f"""
{ticket_context}

{base_context}

{context_info}

# 🎯 PHASE 3: COMPREHENSIVE IMPLEMENTATION SUMMARY & TUTORIAL

Du erstellst eine **umfassende Implementation Summary** als Tutorial für Code-Reviewer. Dies ist die **Phase 3** nach dem Review und soll eine detaillierte Erklärung der Implementierung liefern.

## 📋 REVIEW RESULTS CONTEXT (Phases 1 & 2)

Die vorherigen Review-Phasen haben folgende Ergebnisse geliefert:

```
{review_result[:2000]}...
```

## 🎯 DEINE AUFGABE: TUTORIAL-STYLE IMPLEMENTATION GUIDE

### 1. 🎯 EXECUTIVE SUMMARY
- **Was wurde implementiert?** (High-level Überblick)
- **Warum war diese Implementierung nötig?** (Business Context)
- **Welche Architektur-Entscheidungen wurden getroffen?**
- **Wie fügt sich das in das Gesamtsystem ein?**

### 2. 📊 BUSINESS CONTEXT & ZIELE
- **Ticket-Kontext**: Was sollte erreicht werden?
- **User Impact**: Welche Nutzer-Erfahrung verändert sich?
- **Business Value**: Welcher Geschäftswert wird geschaffen?
- **Success Metrics**: Wie wird Erfolg gemessen?

### 3. 🏗️ ARCHITECTURE & DESIGN OVERVIEW
- **System-Architektur**: Wie die Änderungen in die Monorepo-Struktur passen
- **Design Patterns**: Welche Patterns wurden verwendet und warum?
- **Integration Points**: Welche Services/APIs/Datenbanken sind betroffen?
- **Data Flow**: Wie fließen Daten durch das System?

### 4. 📁 DETAILLIERTE FILE-BY-FILE ANALYSE
Für jede geänderte Datei:
- **Zweck der Änderung**: Was macht diese Datei jetzt anders?
- **Code-Highlights**: Wichtigste Funktionen/Klassen/Methoden
- **Dependencies**: Wie hängt sie mit anderen Dateien zusammen?
- **Testing**: Wie wird diese Datei getestet?

### 5. 🔄 INTEGRATION & WORKFLOW
- **API Changes**: Neue oder geänderte Endpoints
- **Database Changes**: Schema-Änderungen, Migrations
- **Frontend-Backend Integration**: Wie arbeiten UI und Services zusammen?
- **External Services**: Abhängigkeiten zu externen APIs/Services

### 6. 🧪 TESTING STRATEGY
- **Unit Tests**: Was wird wie getestet?
- **Integration Tests**: Welche Integrationspunkte sind abgedeckt?
- **E2E Tests**: User-Journey Tests
- **Performance**: Load/Performance Überlegungen

### 7. 🚀 DEPLOYMENT & CONFIGURATION
- **Deployment Pipeline**: Wie wird das deployed?
- **Environment Configuration**: Welche Config-Änderungen sind nötig?
- **Feature Flags**: Gibt es Feature Toggles?
- **Rollback Strategy**: Wie kann man zurückrollen?

### 8. 🎯 SUCCESS METRICS & MONITORING
- **KPIs**: Welche Metriken zeigen Erfolg?
- **Monitoring**: Was sollte überwacht werden?
- **Alerting**: Welche Alerts sind sinnvoll?
- **Performance Metrics**: Response Times, Throughput, etc.

### 9. 🔮 FUTURE CONSIDERATIONS
- **Technical Debt**: Was sollte später verbessert werden?
- **Scalability**: Wie skaliert die Lösung?
- **Maintenance**: Was muss regelmäßig gewartet werden?
- **Extension Points**: Wo kann man in Zukunft erweitern?

### 10. 📚 DEVELOPER GUIDE
- **Setup Instructions**: Wie startet man lokal?
- **Development Workflow**: Wie entwickelt man weiter?
- **Debugging**: Wie debuggt man Probleme?
- **Common Pitfalls**: Häufige Fallen und wie man sie vermeidet

## 🎨 DIAGRAMME & VISUALISIERUNGEN

Verwende **Mermaid-Diagramme** für:
- System Architecture Overview
- Data Flow Diagramme
- Sequence Diagramme für API Calls
- Entity Relationship Diagramme

Beispiel:
```mermaid
graph TD
    A[Frontend] --> B[API Gateway]
    B --> C[Service 1]
    B --> D[Service 2]
    C --> E[Database]
```

## 📖 WRITING STYLE GUIDELINES

- **Tutorial-Style**: Erkläre, als würdest du einem neuen Entwickler das System beibringen
- **Context First**: Beginne immer mit dem "Warum" vor dem "Wie"
- **Concrete Examples**: Verwende konkrete Code-Beispiele und Szenarien
- **Practical Focus**: Konzentriere dich auf praktische Aspekte und reale Anwendung
- **Progressive Disclosure**: Beginne mit Überblick, dann Details

**WICHTIG:** Dies ist eine Dokumentation für Menschen, die verstehen wollen:
- Was wurde gemacht und warum
- Wie es funktioniert und zusammenhängt
- Wie man damit arbeitet und es erweitert
- Welche Auswirkungen es auf das Gesamtsystem hat

Erstelle eine umfassende, aber gut strukturierte Analyse die als Tutorial und Nachschlagewerk dient.
"""
    
    def _create_summary_document(self, summary_content: str, review_type: str, codebase_context: dict) -> Path:
        """
        Create the comprehensive summary document in the worktree
        """
        # Create summary file in worktree root
        summary_path = self.working_path / "IMPLEMENTATION_SUMMARY.md"
        
        # Create header with metadata
        document_content = f"""# 🎯 Implementation Summary & Tutorial

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Review Type:** {review_type.upper()}  
**Repository:** {self.repo_path}  
**Working Directory:** {self.working_path}  
**Branch:** {self.branch_name or 'Unknown'}  
"""
        
        if self.pr_url:
            document_content += f"**Pull Request:** {self.pr_url}  \n"
        
        # Add Jira ticket information if available
        if self.current_ticket:
            ticket = self.current_ticket
            document_content += f"""
---

## 🎫 Related Jira Ticket

- **Ticket ID:** [{ticket.ticket_id}]({self.jira_integration.server_url}/browse/{ticket.ticket_id})
- **Summary:** {ticket.summary}
- **Type:** {ticket.issue_type}
- **Status:** {ticket.status}
- **Priority:** {ticket.priority}
- **Assignee:** {ticket.assignee}

### 📋 Original Requirements
"""
            
            if ticket.acceptance_criteria:
                for i, criterion in enumerate(ticket.acceptance_criteria, 1):
                    document_content += f"{i}. {criterion}\n"
            else:
                document_content += "❌ No acceptance criteria defined\n"
        
        # Add codebase context summary
        document_content += f"""
---

## 🏗️ Codebase Context Summary

### Architectural Patterns Identified:
{chr(10).join(f"- {pattern}" for pattern in codebase_context.get('architectural_patterns', []))}

### Integration Points:
{chr(10).join(f"- {point}" for point in codebase_context.get('integration_points', []))}

### Business Domain Context:
- **Domain:** {codebase_context.get('business_domain', {}).get('domain', 'Unknown')}
- **User Impact:** {codebase_context.get('business_domain', {}).get('user_impact', 'Unknown')}
- **Business Value:** {codebase_context.get('business_domain', {}).get('business_value', 'Unknown')}

---

## 🎯 Comprehensive Implementation Analysis

{summary_content}

---

## 📈 Document Information

- **Generated by:** Enhanced Claude Code PR Reviewer - Phase 3: Summary & Explanation
- **Claude Code Version:** {self._get_claude_version_func() if self._get_claude_version_func else 'Unknown'}
- **Jira Integration:** {'✅ Enabled' if self.jira_integration and self.jira_integration.enabled else '❌ Disabled'}
- **Ticket Context:** {'✅ Available' if self.current_ticket else '❌ Not Available'}
- **Codebase Analysis:** ✅ Included
- **Working Directory:** `{self.working_path}`

---

## 🔧 Usage Notes

This document serves as a comprehensive tutorial and reference for understanding:

1. **What was implemented** - Business and technical overview
2. **Why it was implemented** - Context and justification  
3. **How it works** - Technical details and architecture
4. **How it integrates** - Relationship to existing codebase
5. **How to extend it** - Future development guidance

---

*This Implementation Summary was automatically generated as part of the Enhanced 3-Phase Code Review Process. For questions about implementation details, consult the development team or refer to the related Jira ticket.*
"""
        
        # Write the summary document
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(document_content)
        
        print(f"📊 Implementation Summary erstellt: {summary_path}")
        return summary_path