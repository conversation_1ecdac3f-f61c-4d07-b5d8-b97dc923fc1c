#!/usr/bin/env python3
"""
Enhanced Claude Code PR Reviewer using Claude Code CLI - FIXED VERSION
Uses subprocess.Popen to avoid asyncio issues in threaded contexts
"""

import json
import os
import subprocess
import sys
import uuid
import logging
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable

# Import base classes from existing implementation
try:
    from .jira_integration import JiraIntegration, JiraTicket
    from .prompt_manager import PromptManager, get_prompt_manager
    from .report_generator import ReportGenerator
except ImportError:
    # Fallback for when running as script
    from jira_integration import JiraIntegration, JiraTicket
    from prompt_manager import PromptManager, get_prompt_manager
    from report_generator import ReportGenerator

try:
    from core.utils.worktree_utils import generate_worktree_path, ensure_worktree_base_directory
except ImportError:
    from utils.worktree_utils import generate_worktree_path, ensure_worktree_base_directory

logger = logging.getLogger(__name__)


class ClaudeCodeCLIError(Exception):
    """Exception raised when Claude Code CLI fails"""
    pass


class EnhancedClaudeReviewerSDK:
    """Enhanced Claude Code Reviewer using Claude Code CLI with structured output"""
    
    def __init__(self, config: Dict, worktree_path=None, repo_path=".", pr_url=None, branch_name=None):
        """Initialize with same parameters as CLI version"""
        self.config = config

        # Use configured worktree path if not explicitly provided
        if worktree_path:
            self.worktree_path = Path(worktree_path)
        elif branch_name and repo_path and repo_path != ".":
            # Generate worktree path using master repo path from frontend
            configured_path = generate_worktree_path(branch_name, master_repo_path=repo_path)
            self.worktree_path = Path(configured_path)
            logger.info(f"📁 Generated worktree path: {configured_path}")
        else:
            self.worktree_path = None
            
        self.repo_path = Path(repo_path).resolve()
        self.working_path = self.worktree_path if self.worktree_path else self.repo_path
        self.pr_url = pr_url
        self.branch_name = branch_name
        
        # Initialize Prompt Manager for clean prompt handling
        self.prompt_manager = get_prompt_manager()
        
        # Initialize Jira integration (same as CLI)
        self.jira_integration = JiraIntegration(config)
        
        # Setup worktree if needed
        if self.worktree_path and self.branch_name:
            self._ensure_worktree_exists()
        
        # Session tracking
        self.session_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # Verify Claude Code CLI is available
        self._verify_claude_cli()
        
        logger.info(f"🚀 SDK Enhanced Reviewer initialized for {self.working_path}")
        logger.info(f"📋 Session ID: {self.session_id}")
    
    def _verify_claude_cli(self):
        """Verify that Claude Code CLI is installed and accessible"""
        try:
            # Add nvm path to environment
            env = os.environ.copy()
            nvm_path = "/Users/<USER>/.nvm/versions/node/v22.14.0/bin"
            if nvm_path not in env.get('PATH', ''):
                env['PATH'] = f"{nvm_path}:{env.get('PATH', '')}"
                
            result = subprocess.run(['claude', '--version'], 
                                  capture_output=True, text=True, timeout=10, env=env)
            if result.returncode == 0:
                logger.info(f"✅ Claude Code CLI found: {result.stdout.strip()}")
            else:
                raise ClaudeCodeCLIError("Claude Code CLI not responding properly")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            raise ClaudeCodeCLIError(
                "Claude Code CLI not found. Please install it with: npm install -g @anthropic-ai/claude-cli"
            )
    
    async def perform_enhanced_review_async(
        self, 
        review_type="comprehensive_with_ac", 
        include_summary=False,
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """
        Perform enhanced review using Claude Code CLI with structured JSON output
        
        Args:
            review_type: Type of review (comprehensive_with_ac, ac_focused, bug_analysis)
            include_summary: Whether to include Phase 3 tutorial summary
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict with structured review results matching CLI quality
        """
        
        logger.info(f"🔍 Starting perform_enhanced_review_async")
        
        # Send initial progress
        if progress_callback:
            try:
                import inspect
                if callable(progress_callback) and not inspect.iscoroutinefunction(progress_callback):
                    progress_callback("session_started", "Starting enhanced review session", {
                        "session_id": self.session_id,
                        "review_type": review_type,
                        "include_summary": include_summary
                    })
            except Exception as e:
                logger.warning(f"Progress callback error: {e}")
        
        try:
            # Get Jira ticket context if available
            jira_ticket = None
            
            # First, check if ticket data is provided in config (from frontend)
            frontend_ticket_data = self.config.get('jira_config', {}).get('ticket_data')
            if frontend_ticket_data:
                ticket_id = frontend_ticket_data.get('ticket_id')
                logger.info(f"🎫 Using Jira ticket from frontend: {ticket_id}")
                
                # Use the existing JiraIntegration to process the frontend data properly
                try:
                    # Create a temporary file with the frontend data in proper format
                    import tempfile
                    import json
                    
                    # Prepare the data in the format JiraIntegration expects
                    normalized_data = dict(frontend_ticket_data)
                    # JiraTicket expects 'key' field, but frontend sends 'ticket_id'
                    if 'ticket_id' in normalized_data and 'key' not in normalized_data:
                        normalized_data['key'] = normalized_data['ticket_id']
                    
                    # Handle acceptance_criteria - support both old format (string) and new format (array)
                    if 'acceptance_criteria' in normalized_data:
                        ac_value = normalized_data['acceptance_criteria']
                        if isinstance(ac_value, str) and ('Ticket has' in ac_value and 'acceptance criteria' in ac_value):
                            # Old format: just a count string - remove it, let JiraIntegration parse from description
                            del normalized_data['acceptance_criteria']
                            logger.info(f"🔄 Removed old placeholder AC string, will parse from description")
                        elif isinstance(ac_value, list) and len(ac_value) > 0:
                            # New format: actual array of criteria - keep it!
                            logger.info(f"✅ Frontend provided {len(ac_value)} acceptance criteria - using directly")
                        elif isinstance(ac_value, list) and len(ac_value) == 0:
                            # Empty array - remove and let JiraIntegration parse from description
                            del normalized_data['acceptance_criteria']
                            logger.info(f"🔄 Empty AC array, will parse from description")
                    
                    # Create temporary file with JSON data for JiraIntegration to parse
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as tmp:
                        json.dump(normalized_data, tmp, indent=2, ensure_ascii=False)
                        temp_file_path = tmp.name
                    
                    # Update the jira_integration config to use this temp file
                    temp_config = dict(self.config)
                    temp_config['jira_config']['ticket_extraction'] = {
                        'manual_ticket_file': temp_file_path
                    }
                    temp_config['jira_config']['enabled'] = False  # Don't use API, use the file
                    
                    # Create a new JiraIntegration instance with the temp config
                    try:
                        from .jira_integration import JiraIntegration
                    except ImportError:
                        from jira_integration import JiraIntegration
                    
                    temp_jira_integration = JiraIntegration(temp_config)
                    
                    # Use the proven JiraIntegration.get_ticket() method 
                    jira_ticket = temp_jira_integration.get_ticket(ticket_id)
                    
                    if jira_ticket:
                        logger.info(f"✅ Successfully processed frontend ticket via JiraIntegration: {jira_ticket.ticket_id}")
                        logger.info(f"📋 Ticket has {len(jira_ticket.acceptance_criteria)} acceptance criteria")
                        # Print first few AC for debugging
                        for i, ac in enumerate(jira_ticket.acceptance_criteria[:3], 1):
                            logger.info(f"   {i}. {ac[:100]}...")
                    else:
                        logger.warning(f"⚠️ JiraIntegration could not process frontend ticket data")
                    
                    # Clean up temp file
                    import os
                    try:
                        os.unlink(temp_file_path)
                    except:
                        pass
                        
                except Exception as e:
                    logger.warning(f"⚠️ Could not process frontend ticket via JiraIntegration: {e}")
                    import traceback
                    logger.warning(f"📍 Traceback: {traceback.format_exc()}")
            
            # Fallback: extract from branch name if no frontend data
            if not jira_ticket and self.branch_name:
                try:
                    ticket_id = self.jira_integration.extract_ticket_id_from_branch(self.branch_name)
                    if ticket_id:
                        jira_ticket = self.jira_integration.get_ticket(ticket_id)
                except Exception as e:
                    logger.warning(f"⚠️ Could not fetch Jira context from branch: {e}")
            
            # Build the enhanced prompt
            prompt = self._build_enhanced_prompt(review_type, jira_ticket)
            
            # Execute Claude Code CLI (synchronously to avoid asyncio issues)
            review_result = self._execute_claude_code_review_sync(prompt, progress_callback)
            
            # Structure the result
            structured_result = self._structure_review_result(
                review_result, review_type, jira_ticket
            )
            
            # Generate Phase 3 summary if requested
            if include_summary:
                logger.info(f"🔍 Generating Phase 3 summary for session {self.session_id}")
                tutorial_data = self._generate_phase3_summary_sync(structured_result, progress_callback)
                structured_result["phase3_summary"] = tutorial_data
            
            logger.info(f"✅ Enhanced review completed for session {self.session_id}")
            return structured_result
            
        except Exception as e:
            import traceback
            logger.error(f"❌ Enhanced review failed: {str(e)}")
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            
            error_result = {
                "session_id": self.session_id,
                "review_type": review_type,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "session_id": self.session_id,
                    "is_error": True,
                    "error_type": type(e).__name__
                }
            }
            
            if progress_callback:
                try:
                    # Only call if it's a regular function, not a coroutine
                    import inspect
                    if callable(progress_callback) and not inspect.iscoroutinefunction(progress_callback):
                        progress_callback("review_error", f"Review failed: {str(e)}", {
                            "session_id": self.session_id,
                            "error": str(e)
                        })
                except:
                    pass
            
            return error_result
    
    def _build_enhanced_prompt(self, review_type: str, jira_ticket: Optional[JiraTicket]) -> str:
        """Build enhanced prompt using German prompts from CLI for same quality"""
        
        # Get base prompt (German for quality)
        base_prompt = self.prompt_manager.get_enhanced_review_prompt(
            review_type=review_type,
            ticket_context=jira_ticket.summary if jira_ticket else None
        )
        
        # Add Jira ticket context if available
        if jira_ticket:
            jira_context = f"""
## Jira Ticket Context
**Ticket ID:** {jira_ticket.ticket_id}
**Summary:** {jira_ticket.summary}
**Status:** {jira_ticket.status}

**Acceptance Criteria:**
{chr(10).join(f"- {ac}" for ac in jira_ticket.acceptance_criteria)}

**Description:**
{jira_ticket.description}
"""
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", jira_context)
        else:
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", "")
        
        # Add repository context
        repo_context = f"""
## Repository Context
**Working Directory:** {self.working_path}
**Branch:** {self.branch_name or 'unknown'}
**PR URL:** {self.pr_url or 'N/A'}
"""
        base_prompt = base_prompt.replace("{{REPO_CONTEXT}}", repo_context)
        
        return base_prompt
    
    def _execute_claude_code_review_sync(self, prompt: str, progress_callback: Optional[Callable]) -> Dict:
        """Execute Claude Code CLI synchronously using subprocess.Popen"""
        
        logger.info("🔧 Executing Claude Code CLI synchronously")
        
        # Prepare environment with nvm path
        env = os.environ.copy()
        nvm_path = "/Users/<USER>/.nvm/versions/node/v22.14.0/bin"
        if nvm_path not in env.get('PATH', ''):
            env['PATH'] = f"{nvm_path}:{env.get('PATH', '')}"
        
        # Command to execute - USE TEXT FORMAT to preserve markdown formatting
        cmd = ['claude', '-p', prompt]
        
        # Use the working_path that was correctly set in constructor
        working_dir = str(self.working_path)
        logger.info(f"📂 Using working directory: {working_dir}")
        logger.info(f"🔧 Command: claude -p [PROMPT] (text format)")
        logger.info(f"🔧 Prompt length: {len(prompt)} characters")
        
        # Log prompt for debugging if it's causing issues
        if len(prompt) > 10000:
            logger.warning(f"⚠️ Very long prompt ({len(prompt)} chars) - might cause Claude CLI issues")
            logger.info(f"📝 Prompt preview (first 500 chars): {prompt[:500]}...")
        else:
            logger.info(f"📝 Full prompt: {prompt[:1000]}...")
        
        # Create process in correct directory - NO STDIN needed with -p parameter
        try:
            result = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=working_dir,
                timeout=600,  # 10 minutes like CLI version
                check=True
            )
            stdout_data = result.stdout
            stderr_data = result.stderr
            returncode = 0
        except subprocess.TimeoutExpired:
            logger.error("❌ Claude CLI timeout after 600 seconds")
            raise ClaudeCodeCLIError("Claude CLI timeout after 600 seconds")
        except subprocess.CalledProcessError as e:
            stdout_data = e.stdout
            stderr_data = e.stderr
            returncode = e.returncode
        
        if returncode != 0:
            logger.error(f"❌ Claude CLI failed with code {returncode}")
            logger.error(f"📍 stderr: {stderr_data}")
            logger.error(f"📍 stdout: {stdout_data}")
            raise ClaudeCodeCLIError(f"Claude CLI failed: {stderr_data}")
        
        # Check for execution errors in stdout
        if stdout_data and stdout_data.strip().startswith("Execution error"):
            logger.error(f"❌ Claude CLI execution error detected")
            logger.error(f"📍 Full error output: {stdout_data}")
            logger.error(f"📍 stderr: {stderr_data}")
            raise ClaudeCodeCLIError(f"Claude CLI execution error: {stdout_data}")
        
        # Parse JSON response from Claude Code CLI
        logger.info("✅ Claude Code CLI completed successfully")
        logger.info(f"🔥 Raw stdout length: {len(stdout_data)}")
        logger.info(f"🔥 First 200 chars: {stdout_data[:200]}...")
        
        # Return text output directly (no JSON parsing needed)
        logger.info(f"🔥 Using text output format")
        logger.info(f"🔥 Raw stdout length: {len(stdout_data)}")
        
        return {
            "result": stdout_data.strip(),  # Main content field
            "content": stdout_data.strip(),
            "raw_content": stdout_data.strip(),
            "success": True,
            "metadata": {
                "parsing_method": "text_format",
                "output_format": "text"
            }
        }
    
    def _generate_phase3_summary_sync(self, review_result: Dict, progress_callback: Optional[Callable]) -> Dict:
        """Generate Phase 3 tutorial summary using SummaryAnalyzer - identical to CLI version"""
        
        logger.info("🚀 Starting Phase 3 using SummaryAnalyzer (matching CLI version)...")
        
        try:
            # Import SummaryAnalyzer from core directory
            try:
                from .summary_analyzer import SummaryAnalyzer
            except ImportError:
                from summary_analyzer import SummaryAnalyzer
                
            # Initialize SummaryAnalyzer exactly like CLI version does
            summary_analyzer = SummaryAnalyzer(
                working_path=Path(self.worktree_path) if self.worktree_path else Path(self.repo_path),
                branch_name=self.branch_name,
                repo_path=Path(self.repo_path),
                pr_url=self.pr_url,
                current_ticket=self.current_ticket,
                jira_integration=self.jira_integration
            )
            
            # Extract review content from result
            review_content = review_result.get("raw_content", "")
            review_type = review_result.get("review_type", "comprehensive")
            
            logger.info(f"📚 Delegating to SummaryAnalyzer with {len(review_content)} chars of review content")
            
            # Call SummaryAnalyzer.perform_summary_phase() with function references like CLI version
            raw_content = summary_analyzer.perform_summary_phase(
                review_result=review_content,
                review_type=review_type,
                run_claude_command_func=self._run_claude_command_with_progress_for_summary,
                get_changed_files_func=self._get_changed_files_list,
                get_git_diff_summary_func=self._get_git_diff_summary,
                generate_ticket_context_func=self._generate_ticket_context,
                get_claude_version_func=self._get_claude_version
            )
            
            logger.info(f"📚 SummaryAnalyzer returned {len(raw_content) if raw_content else 0} characters")
            logger.info(f"📚 Content preview: {raw_content[:200] if raw_content else 'No content'}...")

            # Return ONLY raw markdown content like CLI version
            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": raw_content,
                "metadata": {
                    "session_id": self.session_id,
                    "generated_via": "SummaryAnalyzer"
                }
            }

        except Exception as e:
            logger.error(f"❌ Phase 3 SummaryAnalyzer failed: {e}")
            import traceback
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": f"# Phase 3 Tutorial Generation Failed\n\nError: {str(e)}\n\nPlease check the logs for more details.",
                "metadata": {
                    "error": str(e),
                    "session_id": self.session_id,
                    "generated_via": "SummaryAnalyzer"
                }
            }

    def _run_claude_command_with_progress_for_summary(self, prompt: str, timeout: int = 900, max_turns: int = 25):
        """
        Execute Claude Code command for summary generation - matches CLI behavior
        """
        logger.info(f"🤖 Running Claude Code for Phase 3 summary generation...")
        logger.info(f"📝 Prompt length: {len(prompt)} characters")
        logger.info(f"⏱️ Timeout: {timeout}s, Max turns: {max_turns}")
        
        import subprocess
        import tempfile
        import os
        
        try:
            # Create temp file for prompt
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as temp_file:
                temp_file.write(prompt)
                temp_file_path = temp_file.name
            
            # Build Claude Code command
            cmd = [
                "claude", 
                "-p", prompt,
                "--output-format", "text",
                "--max-turns", str(max_turns),
                "--allowedTools", "Read,Grep,Glob,LS",
                "--disallowedTools", "Write,Edit,MultiEdit,TodoWrite,NotebookEdit"
            ]
            
            # Execute in the worktree directory
            working_dir = self.worktree_path if self.worktree_path else self.repo_path
            logger.info(f"📂 Working directory: {working_dir}")
            
            result = subprocess.run(
                cmd,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            # Clean up temp file
            try:
                os.unlink(temp_file_path)
            except:
                pass
            
            logger.info(f"📊 Command completed with return code: {result.returncode}")
            logger.info(f"📊 Stdout length: {len(result.stdout)}")
            logger.info(f"📊 Stderr length: {len(result.stderr)}")
            
            if result.returncode == 0:
                logger.info(f"✅ Claude Code execution successful")
                return result.stdout
            else:
                logger.error(f"❌ Claude Code execution failed: {result.stderr}")
                return f"Error executing Claude Code: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ Claude Code execution timed out after {timeout} seconds")
            return "Error: Claude Code execution timed out"
        except Exception as e:
            logger.error(f"❌ Claude Code execution error: {e}")
            return f"Error: {str(e)}"

    def _get_changed_files_list(self):
        """Get list of changed files - matches CLI version"""
        try:
            import subprocess
            
            working_dir = self.worktree_path if self.worktree_path else self.repo_path
            
            result = subprocess.run(
                ["git", "diff", "--name-only", "origin/main...HEAD"],
                cwd=working_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.warning(f"⚠️ Git diff failed: {result.stderr}")
                return []
            
            files = [f.strip() for f in result.stdout.split('\n') if f.strip()]
            logger.info(f"📂 Found {len(files)} changed files")
            return files
            
        except Exception as e:
            logger.error(f"❌ Error getting changed files: {e}")
            return []

    def _get_git_diff_summary(self):
        """Get git diff summary - matches CLI version"""
        try:
            import subprocess
            
            working_dir = self.worktree_path if self.worktree_path else self.repo_path
            
            result = subprocess.run(
                ["git", "diff", "--stat", "origin/main...HEAD"],
                cwd=working_dir,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.warning(f"⚠️ Git diff stat failed: {result.stderr}")
                return "Git diff summary not available"
            
            return result.stdout
            
        except Exception as e:
            logger.error(f"❌ Error getting git diff summary: {e}")
            return "Git diff summary not available"

    def _generate_ticket_context(self) -> str:
        """Generate ticket context - matches CLI version"""
        if not self.current_ticket:
            return ""
        
        ticket = self.current_ticket
        
        context = f"""# 🎫 JIRA TICKET CONTEXT
        
## Ticket: {ticket.ticket_id}
**Summary:** {ticket.summary}

**Status:** {ticket.status}
**Priority:** {ticket.priority}
**Assignee:** {ticket.assignee}

**Description:**
{ticket.description}

## 📋 Acceptance Criteria ({len(ticket.acceptance_criteria)} items)
"""
        
        for i, ac in enumerate(ticket.acceptance_criteria, 1):
            context += f"\n### AC {i}: {ac}\n"
        
        return context

    def _get_claude_version(self):
        """Get Claude Code version - matches CLI version"""
        try:
            import subprocess
            
            result = subprocess.run(
                ["claude", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return "Claude Code version unknown"
                
        except Exception as e:
            logger.warning(f"⚠️ Could not get Claude version: {e}")
            return "Claude Code version unknown"

    async def _perform_phase3_summary(self, review_content: str, review_type: str, progress_callback: Optional[Callable] = None) -> Dict:
        """
        Perform Phase 3 summary generation (API compatibility method)
        """
        return self._generate_phase3_summary_sync(
            {"raw_content": review_content, "review_type": review_type},
            progress_callback
        )
    
    def _ensure_worktree_exists(self):
        """Ensure the worktree exists, create it if necessary"""
        import subprocess
        
        if not self.worktree_path or not self.branch_name:
            return
            
        worktree_path = Path(self.worktree_path)
        
        # If worktree already exists, we're done
        if worktree_path.exists() and (worktree_path / '.git').exists():
            logger.info(f"✅ Worktree already exists: {worktree_path}")
            return
            
        try:
            # Use the repo_path as master repository path (passed from frontend)
            master_repo_path = str(self.repo_path)
            master_path = Path(master_repo_path)

            if not master_path.exists():
                raise ValueError(f"Master repository not found: {master_repo_path}")

            # Ensure worktree base directory exists
            ensure_worktree_base_directory(master_repo_path)
            
            # Check and cleanup stale worktrees
            self._cleanup_stale_worktrees(master_path, worktree_path)
            
            # Fetch latest changes
            logger.info(f"📡 Fetching latest changes for {self.branch_name}")
            subprocess.run(["git", "fetch", "--all"], cwd=master_path, check=True)
            
            # Create the worktree
            logger.info(f"🔧 Creating worktree for {self.branch_name} at {worktree_path}")
            
            try:
                subprocess.run([
                    "git", "worktree", "add", str(worktree_path), self.branch_name
                ], cwd=master_path, check=True)
            except subprocess.CalledProcessError as e:
                # If normal creation fails, try with --force flag
                if "already registered" in str(e.stderr) or "already exists" in str(e.stderr):
                    logger.warning(f"⚠️ Worktree creation failed, trying with --force: {e}")
                    subprocess.run([
                        "git", "worktree", "add", "--force", str(worktree_path), self.branch_name
                    ], cwd=master_path, check=True)
                else:
                    raise  # Re-raise if it's a different error
            
            logger.info(f"✅ Worktree created successfully: {worktree_path}")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to create worktree: {e}")
            raise RuntimeError(f"Could not create worktree for {self.branch_name}: {e}")
        except Exception as e:
            logger.error(f"❌ Worktree setup failed: {e}")
            raise
    
    def _cleanup_stale_worktrees(self, master_path: Path, target_worktree_path: Path):
        """Clean up stale or conflicting worktrees"""
        import subprocess
        
        try:
            # First, prune any missing worktrees
            logger.info("🧹 Pruning missing worktrees...")
            subprocess.run([
                "git", "worktree", "prune"
            ], cwd=master_path, check=True, capture_output=True)
            
            # Get current worktree list
            result = subprocess.run([
                "git", "worktree", "list", "--porcelain"
            ], cwd=master_path, capture_output=True, text=True, check=True)
            
            # Parse worktree list to find conflicts
            lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
            worktrees = []
            current_worktree = {}
            
            for line in lines:
                if line.startswith('worktree '):
                    if current_worktree:
                        worktrees.append(current_worktree)
                    current_worktree = {'path': line.replace('worktree ', '')}
                elif line.startswith('branch '):
                    current_worktree['branch'] = line.replace('branch ', '').replace('refs/heads/', '')
                elif line.startswith('HEAD '):
                    current_worktree['head'] = line.replace('HEAD ', '')
            
            if current_worktree:
                worktrees.append(current_worktree)
            
            # Check for conflicts
            for worktree in worktrees:
                worktree_path = Path(worktree.get('path', ''))
                worktree_branch = worktree.get('branch', '')
                
                # Case 1: Same branch name, different location
                if worktree_branch == self.branch_name and worktree_path != target_worktree_path:
                    logger.warning(f"⚠️ Found worktree for {self.branch_name} at different location: {worktree_path}")
                    
                    # If the directory doesn't exist, just remove the worktree entry
                    if not worktree_path.exists():
                        logger.info(f"🗑️ Removing stale worktree entry (directory missing): {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                    else:
                        logger.info(f"🗑️ Removing worktree from wrong location: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                
                # Case 2: Same target path, different branch
                elif worktree_path == target_worktree_path and worktree_branch != self.branch_name:
                    logger.warning(f"⚠️ Target path {target_worktree_path} occupied by branch {worktree_branch}")
                    
                    if not worktree_path.exists():
                        logger.info(f"🗑️ Removing stale worktree entry: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                    else:
                        logger.info(f"🗑️ Removing conflicting worktree: {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                
                # Case 3: Same path and branch - worktree already exists correctly
                elif worktree_path == target_worktree_path and worktree_branch == self.branch_name:
                    if worktree_path.exists() and (worktree_path / '.git').exists():
                        logger.info(f"✅ Worktree already exists correctly: {worktree_path}")
                        return  # We're done, worktree is ready
                    else:
                        # Directory missing but worktree registered - remove and recreate
                        logger.info(f"🗑️ Removing incomplete worktree (directory missing): {worktree_path}")
                        subprocess.run([
                            "git", "worktree", "remove", str(worktree_path), "--force"
                        ], cwd=master_path, check=True)
                        
        except subprocess.CalledProcessError as e:
            logger.warning(f"⚠️ Worktree cleanup failed: {e}")
            # Continue anyway, creation might still work with --force
        except Exception as e:
            logger.warning(f"⚠️ Worktree cleanup error: {e}")
            # Continue anyway
    
    def _structure_review_result(
        self,
        claude_result: Dict,
        review_type: str,
        jira_ticket: Optional[JiraTicket]
    ) -> Dict:
        """Return Markdown report instead of parsed JSON - unified with CLI version"""

        end_time = datetime.now()
        duration_ms = int((end_time - self.start_time).total_seconds() * 1000)

        # Extract content from Claude CLI JSON response
        raw_content = (
            claude_result.get("result", "") or  # Claude CLI JSON format
            claude_result.get("content", "") or # Fallback format
            claude_result.get("raw_content", "")  # Legacy format
        )

        # Generate identical report as CLI using ReportGenerator
        report_generator = ReportGenerator(
            working_path=self.worktree_path or self.repo_path,
            repo_path=self.repo_path,
            pr_url=self.pr_url,
            branch_name=self.branch_name
        )

        markdown_report = report_generator.create_enhanced_report(
            review_content=raw_content,
            review_type=review_type,
            jira_ticket=jira_ticket,
            jira_integration_enabled=bool(jira_ticket)
        )

        # Return new API response format with Markdown
        return {
            "status": "success",
            "review_type": review_type,
            "markdown_report": markdown_report,
            "metadata": {
                "generated_at": end_time.isoformat(),
                "repository": str(self.repo_path),
                "pull_request": self.pr_url,
                "branch_name": self.branch_name,
                "cost_usd": claude_result.get("total_cost_usd", claude_result.get("cost_usd", 0.0)),
                "duration_ms": claude_result.get("duration_ms", duration_ms),
                "api_duration_ms": claude_result.get("duration_api_ms", duration_ms),
                "num_turns": claude_result.get("num_turns", 1),
                "session_id": self.session_id,
                "claude_session_id": claude_result.get("session_id"),
                "is_error": claude_result.get("is_error", False),
                "success": not claude_result.get("is_error", False),
                "worktree_path": str(self.worktree_path) if self.worktree_path else None
            }
        }
    
    def _get_changed_files(self) -> List[str]:
        """Get list of changed files in current branch"""
        try:
            result = subprocess.run(
                ['git', 'diff', '--name-only', 'HEAD^..HEAD'],
                capture_output=True, text=True, cwd=self.working_path
            )
            if result.returncode == 0:
                return [f.strip() for f in result.stdout.split('\n') if f.strip()]
        except:
            pass
        return []
    
    def _format_jira_ticket(self, jira_ticket: JiraTicket) -> Dict:
        """Format Jira ticket for API response"""
        return {
            "ticket_id": jira_ticket.ticket_id,
            "summary": jira_ticket.summary,
            "status": jira_ticket.status,
            "priority": jira_ticket.priority,
            "acceptance_criteria_count": len(jira_ticket.acceptance_criteria),
            "acceptance_criteria": jira_ticket.acceptance_criteria
        }


# Factory function for backward compatibility
def create_enhanced_reviewer_sdk(config: Dict, **kwargs) -> EnhancedClaudeReviewerSDK:
    """Factory function to create enhanced reviewer SDK instance"""
    return EnhancedClaudeReviewerSDK(config, **kwargs)


# Test function
if __name__ == "__main__":
    import asyncio
    
    async def test_review():
        config = {
            'pr_config': {
                'repository': {
                    'path': '.'
                }
            }
        }
        
        reviewer = EnhancedClaudeReviewerSDK(config, branch_name="test-branch")
        result = await reviewer.perform_enhanced_review_async(
            review_type="comprehensive_with_ac",
            include_summary=True
        )
        
        print(json.dumps(result, indent=2))
    
    asyncio.run(test_review())
