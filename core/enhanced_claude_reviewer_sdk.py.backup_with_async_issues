#!/usr/bin/env python3
"""
Enhanced Claude Code PR Reviewer using Claude Code CLI
Uses the official Claude Code CLI for native JSON output and streaming.
Maintains same quality as CLI version while providing structured API output.
"""

import asyncio
import json
import os
import subprocess
import sys
import uuid
import logging
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Callable, AsyncGenerator

# Import base classes from existing implementation
try:
    from .jira_integration import JiraIntegration, JiraTicket
    from .prompt_manager import PromptManager, get_prompt_manager
except ImportError:
    # Fallback for when running as script
    from jira_integration import JiraIntegration, JiraTicket
    from prompt_manager import PromptManager, get_prompt_manager

logger = logging.getLogger(__name__)


class ClaudeCodeCLIError(Exception):
    """Exception raised when Claude Code CLI fails"""
    pass


class EnhancedClaudeReviewerSDK:
    """Enhanced Claude Code Reviewer using Claude Code CLI with structured output"""
    
    def __init__(self, config: Dict, worktree_path=None, repo_path=".", pr_url=None, branch_name=None):
        """Initialize with same parameters as CLI version"""
        self.config = config
        self.worktree_path = Path(worktree_path) if worktree_path else None
        self.repo_path = Path(repo_path).resolve()
        self.working_path = self.worktree_path if self.worktree_path else self.repo_path
        self.pr_url = pr_url
        self.branch_name = branch_name
        
        # Initialize Prompt Manager for clean prompt handling
        self.prompt_manager = get_prompt_manager()
        
        # Initialize Jira integration (same as CLI)
        self.jira_integration = JiraIntegration(config)
        
        # Session tracking
        self.session_id = str(uuid.uuid4())
        self.start_time = datetime.now()
        
        # Verify Claude Code CLI is available
        self._verify_claude_cli()
        
        logger.info(f"🚀 SDK Enhanced Reviewer initialized for {self.working_path}")
        logger.info(f"📋 Session ID: {self.session_id}")
    
    def _verify_claude_cli(self):
        """Verify that Claude Code CLI is installed and accessible"""
        try:
            result = subprocess.run(['claude', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info(f"✅ Claude Code CLI found: {result.stdout.strip()}")
            else:
                raise ClaudeCodeCLIError("Claude Code CLI not responding properly")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            raise ClaudeCodeCLIError(
                "Claude Code CLI not found. Please install it with: npm install -g @anthropic-ai/claude-cli"
            )
    
    async def perform_enhanced_review_async(
        self, 
        review_type="comprehensive_with_ac", 
        include_summary=False,
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """
        Perform enhanced review using Claude Code CLI with structured JSON output
        
        Args:
            review_type: Type of review (comprehensive_with_ac, ac_focused, bug_analysis)
            include_summary: Whether to include Phase 3 tutorial summary
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dict with structured review results matching CLI quality
        """
        
        logger.info(f"🔍 Starting perform_enhanced_review_async with progress_callback: {progress_callback}")
        
        if progress_callback:
            if asyncio.iscoroutinefunction(progress_callback):
                await progress_callback("session_started", "Starting enhanced review session", {
                    "session_id": self.session_id,
                    "review_type": review_type,
                    "include_summary": include_summary
                })
            else:
                progress_callback("session_started", "Starting enhanced review session", {
                    "session_id": self.session_id,
                    "review_type": review_type,
                    "include_summary": include_summary
                })
        
        try:
            # Get Jira ticket context if available
            jira_ticket = None
            if self.branch_name:
                jira_ticket = await self._get_jira_context()
            
            # Build the enhanced prompt using German prompts for quality
            prompt = self._build_enhanced_prompt(review_type, jira_ticket)
            
            if progress_callback:
                if asyncio.iscoroutinefunction(progress_callback):
                    await progress_callback("claude_thinking", "Loading prompts and context", {
                        "session_id": self.session_id,
                        "prompt_length": len(prompt)
                    })
                else:
                    progress_callback("claude_thinking", "Loading prompts and context", {
                        "session_id": self.session_id,
                        "prompt_length": len(prompt)
                    })
            
            # Execute Claude Code CLI with structured output
            review_result = await self._execute_claude_code_review(prompt, progress_callback)
            
            # Structure the response to match CLI output format
            structured_result = self._structure_review_result(
                review_result, review_type, jira_ticket, include_summary
            )
            
            # Generate Phase 3 summary if requested
            if include_summary:
                if progress_callback:
                    if asyncio.iscoroutinefunction(progress_callback):
                        await progress_callback("phase3_started", "Generating implementation summary", {
                            "session_id": self.session_id
                        })
                    else:
                        progress_callback("phase3_started", "Generating implementation summary", {
                            "session_id": self.session_id
                        })
                
                tutorial_data = await self._generate_phase3_summary(structured_result, progress_callback)
                structured_result["phase3_summary"] = tutorial_data
                
                if progress_callback:
                    if asyncio.iscoroutinefunction(progress_callback):
                        await progress_callback("phase3_completed", "Tutorial generation complete", {
                            "session_id": self.session_id,
                            "tutorial_sections": len(tutorial_data.get("structured_sections", {}))
                        })
                    else:
                        progress_callback("phase3_completed", "Tutorial generation complete", {
                            "session_id": self.session_id,
                            "tutorial_sections": len(tutorial_data.get("structured_sections", {}))
                        })
            
            if progress_callback:
                if asyncio.iscoroutinefunction(progress_callback):
                    await progress_callback("structured_result", "Review analysis complete", {
                        "session_id": self.session_id,
                        "success": structured_result["success"]
                    })
                else:
                    progress_callback("structured_result", "Review analysis complete", {
                        "session_id": self.session_id,
                        "success": structured_result["success"]
                    })
            
            logger.info(f"✅ Enhanced review completed for session {self.session_id}")
            return structured_result
            
        except Exception as e:
            import traceback
            logger.error(f"❌ Enhanced review failed: {str(e)}")
            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            error_result = {
                "session_id": self.session_id,
                "review_type": review_type,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "metadata": {
                    "session_id": self.session_id,
                    "is_error": True,
                    "error_type": type(e).__name__
                }
            }
            
            if progress_callback:
                if asyncio.iscoroutinefunction(progress_callback):
                    await progress_callback("review_error", f"Review failed: {str(e)}", {
                        "session_id": self.session_id,
                        "error": str(e)
                    })
                else:
                    progress_callback("review_error", f"Review failed: {str(e)}", {
                        "session_id": self.session_id,
                        "error": str(e)
                    })
            
            return error_result
    
    async def _get_jira_context(self) -> Optional[JiraTicket]:
        """Get Jira ticket context same as CLI version"""
        if not self.branch_name:
            return None
            
        try:
            ticket_id = self.jira_integration.extract_ticket_id_from_branch(self.branch_name)
            if ticket_id:
                return self.jira_integration.get_ticket(ticket_id)
        except Exception as e:
            logger.warning(f"⚠️ Could not fetch Jira context: {e}")
        
        return None
    
    def _build_enhanced_prompt(self, review_type: str, jira_ticket: Optional[JiraTicket]) -> str:
        """Build enhanced prompt using German prompts from CLI for same quality"""
        
        # Get base prompt (German for quality)
        base_prompt = self.prompt_manager.get_enhanced_review_prompt(
            review_type, 
            jira_ticket.summary if jira_ticket else None
        )
        
        # Add Jira context if available
        if jira_ticket:
            jira_context = f"""
## Jira Ticket Context
**Ticket ID:** {jira_ticket.ticket_id}
**Summary:** {jira_ticket.summary}
**Status:** {jira_ticket.status}

**Acceptance Criteria:**
{chr(10).join(f"- {ac}" for ac in jira_ticket.acceptance_criteria)}

**Description:**
{jira_ticket.description}
"""
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", jira_context)
        else:
            base_prompt = base_prompt.replace("{{JIRA_CONTEXT}}", "")
        
        # Add repository context
        repo_context = f"""
## Repository Context
**Working Directory:** {self.working_path}
**Branch:** {self.branch_name or 'unknown'}
**PR URL:** {self.pr_url or 'N/A'}
"""
        base_prompt = base_prompt.replace("{{REPO_CONTEXT}}", repo_context)
        
        return base_prompt
    
    async def _execute_claude_code_review(self, prompt: str, progress_callback: Optional[Callable]) -> Dict:
        """Execute Claude Code CLI with structured JSON output"""
        
        logger.info(f"🔍 _execute_claude_code_review called with progress_callback type: {type(progress_callback)}")
        
        # Use synchronous subprocess as a workaround for asyncio issues in threaded context
        return await self._execute_claude_sync_wrapper(prompt, progress_callback)
    
    async def _execute_claude_sync_wrapper(self, prompt: str, progress_callback: Optional[Callable]) -> Dict:
        """Wrapper to run synchronous subprocess in executor"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._execute_claude_sync, prompt, progress_callback)
    
    def _execute_claude_sync(self, prompt: str, progress_callback: Optional[Callable]) -> Dict:
        """Execute Claude CLI synchronously to avoid asyncio subprocess issues"""
        import subprocess as sp
        import threading
        import queue
        
        try:
            # Change to working directory for Claude Code context
            original_cwd = os.getcwd()
            os.chdir(self.working_path)
            
            # Notify progress (sync only)
            if progress_callback and not asyncio.iscoroutinefunction(progress_callback):
                progress_callback("tool_usage", "Executing Claude Code CLI", {
                        "session_id": self.session_id,
                        "tool_name": "claude_code_cli",
                        "action": "structured_review"
                    })
                else:
                    progress_callback("tool_usage", "Executing Claude Code CLI", {
                        "session_id": self.session_id,
                        "tool_name": "claude_code_cli",
                        "action": "structured_review"
                    })
            
            # Execute Claude Code CLI with stdin prompt (like the old system)
            cmd = [
                'claude',
                '--print',
                '--verbose',
                '--output-format', 'stream-json'
            ]
            
            logger.info(f"🔧 Executing Claude Code CLI: {' '.join(cmd)}")
            logger.info(f"📂 Working directory: {self.working_path}")
            
            # Run with streaming output capture and stdin
            import sys
            
            # Check if we're running in a thread or asyncio context
            try:
                loop = asyncio.get_running_loop()
                logger.info(f"✅ Running in asyncio loop: {loop}")
            except RuntimeError:
                logger.warning("⚠️ No running asyncio loop detected")
            
            # Ensure PATH includes node/npm directories for claude CLI
            env = os.environ.copy()
            if '/Users/<USER>/.nvm/versions/node/v22.14.0/bin' not in env.get('PATH', ''):
                env['PATH'] = f"/Users/<USER>/.nvm/versions/node/v22.14.0/bin:{env.get('PATH', '')}"
            
            try:
                # Try to create subprocess with explicit environment
                process = await asyncio.create_subprocess_exec(
                    cmd[0],  # 'claude'
                    *cmd[1:],  # remaining args
                    stdin=asyncio.subprocess.PIPE,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=str(self.working_path),
                    env=env
                )
                
                if process is None:
                    raise Exception("create_subprocess_exec returned None")
                    
            except Exception as proc_error:
                logger.error(f"❌ Failed to create subprocess: {type(proc_error).__name__}: {proc_error}")
                logger.error(f"📍 CMD: {cmd}")
                logger.error(f"📍 CWD: {self.working_path}")
                
                # Try alternative approach using full path
                claude_path = "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/claude"
                if os.path.exists(claude_path):
                    cmd[0] = claude_path
                    process = await asyncio.create_subprocess_exec(
                        *cmd,
                        stdin=asyncio.subprocess.PIPE,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        cwd=str(self.working_path),
                        env=env
                    )
                else:
                    raise ClaudeCodeCLIError(f"Claude CLI not found at {claude_path}")
            
            logger.info(f"🔍 Process created - stdin: {process.stdin}, stdout: {process.stdout}, stderr: {process.stderr}")
            
            # Send prompt via stdin
            stdin_data = prompt.encode('utf-8')
            if process.stdin is not None:
                try:
                    process.stdin.write(stdin_data)
                    await process.stdin.drain()
                    process.stdin.close()
                    await process.stdin.wait_closed()
                except Exception as write_error:
                    logger.error(f"❌ Error writing to stdin: {write_error}")
                    # Try without await
                    try:
                        process.stdin.write_nowait(stdin_data)
                        process.stdin.close()
                    except:
                        raise ClaudeCodeCLIError(f"Failed to write to stdin: {write_error}")
            else:
                logger.error("❌ Process stdin is None - cannot send prompt to Claude CLI")
                raise ClaudeCodeCLIError("Failed to communicate with Claude CLI - stdin is None")
            
            # Capture streaming output
            stdout_data = []
            stderr_data = []
            
            async def read_stream(stream, collector, stream_name):
                if stream is None:
                    logger.warning(f"⚠️ Stream {stream_name} is None - skipping")
                    return
                while True:
                    line = await stream.readline()
                    if not line:
                        break
                    line_text = line.decode('utf-8')
                    collector.append(line_text)
                    
                    if progress_callback and stream_name == "stdout":
                        # Send streaming updates
                        if asyncio.iscoroutinefunction(progress_callback):
                            await progress_callback("claude_response_stream", "Processing...", {
                                "session_id": self.session_id,
                                "chunk": line_text.strip()[:100]  # First 100 chars
                            })
                        else:
                            progress_callback("claude_response_stream", "Processing...", {
                                "session_id": self.session_id,
                                "chunk": line_text.strip()[:100]  # First 100 chars
                            })
            
            # Read both streams concurrently
            if process.stdout is None or process.stderr is None:
                logger.error(f"❌ Process streams are None - stdout: {process.stdout}, stderr: {process.stderr}")
                raise ClaudeCodeCLIError("Failed to create process streams for Claude CLI")
            
            await asyncio.gather(
                read_stream(process.stdout, stdout_data, "stdout"),
                read_stream(process.stderr, stderr_data, "stderr")
            )
            
            # Wait for process completion
            await process.wait()
            
            stdout_text = ''.join(stdout_data)
            stderr_text = ''.join(stderr_data)
            
            if process.returncode != 0:
                raise ClaudeCodeCLIError(f"Claude Code CLI failed with code {process.returncode}: {stderr_text}")
            
            # Parse JSON response
            try:
                result = json.loads(stdout_text)
                logger.info(f"✅ Claude Code CLI completed successfully")
                return result
            except json.JSONDecodeError as e:
                # Fallback to raw text if JSON parsing fails
                logger.warning(f"⚠️ JSON parsing failed, using raw output: {e}")
                return {
                    "raw_content": stdout_text,
                    "success": True,
                    "metadata": {
                        "parsing_method": "fallback_raw",
                        "json_error": str(e)
                    }
                }
            
        finally:
            # Cleanup
            os.chdir(original_cwd)
    
    def _structure_review_result(
        self, 
        claude_result: Dict, 
        review_type: str, 
        jira_ticket: Optional[JiraTicket],
        include_summary: bool
    ) -> Dict:
        """Structure the Claude Code CLI result to match API expectations"""
        
        end_time = datetime.now()
        duration_ms = int((end_time - self.start_time).total_seconds() * 1000)
        
        # Base structured result
        structured_result = {
            "session_id": self.session_id,
            "review_type": review_type,
            "timestamp": end_time.isoformat(),
            "success": claude_result.get("success", True),
            "raw_content": claude_result.get("content", claude_result.get("raw_content", "")),
            
            # Enhanced structured data (try to parse from Claude's response)
            "structured_data": self._parse_structured_data(claude_result),
            
            # Enhanced metadata from Claude Code CLI
            "metadata": {
                "cost_usd": claude_result.get("cost_usd", 0.0),
                "duration_ms": duration_ms,
                "api_duration_ms": claude_result.get("api_duration_ms", duration_ms),
                "num_turns": claude_result.get("num_turns", 1),
                "session_id": self.session_id,
                "is_error": False,
                "success": True,
                "worktree_path": str(self.worktree_path) if self.worktree_path else None,
                "branch_name": self.branch_name,
                "pr_url": self.pr_url,
                "changed_files": self._get_changed_files(),
                "diff_summary": f"Analysis of {self.branch_name or 'current branch'}"
            },
            
            # Jira integration
            "jira_ticket": self._format_jira_ticket(jira_ticket) if jira_ticket else None
        }
        
        return structured_result
    
    def _parse_structured_data(self, claude_result: Dict) -> Dict:
        """Parse structured data from Claude's response"""
        
        raw_content = claude_result.get("content", claude_result.get("raw_content", ""))
        
        # Basic parsing - this would be enhanced based on actual Claude Code output format
        structured_data = {
            "executive_summary": {
                "critical_issues": 0,
                "warning_issues": 0,
                "has_ac_analysis": "acceptance" in raw_content.lower(),
                "has_code_analysis": "code quality" in raw_content.lower() or "bug" in raw_content.lower(),
                "ac_compliance": None,
                "code_quality_score": 8  # Default score
            },
            "acceptance_criteria": [],
            "code_quality_findings": [],
            "action_items": {
                "critical": [],
                "important": [],
                "suggestions": []
            },
            "questions": []
        }
        
        # Enhanced parsing logic would go here to extract specific sections
        # For now, providing basic structure for compatibility
        
        return structured_data
    
    def _get_changed_files(self) -> List[str]:
        """Get list of changed files in current branch"""
        try:
            result = subprocess.run(
                ['git', 'diff', '--name-only', 'HEAD^..HEAD'],
                capture_output=True, text=True, cwd=self.working_path
            )
            if result.returncode == 0:
                return [f.strip() for f in result.stdout.split('\n') if f.strip()]
        except:
            pass
        return []
    
    def _format_jira_ticket(self, jira_ticket: JiraTicket) -> Dict:
        """Format Jira ticket for API response"""
        return {
            "ticket_id": jira_ticket.ticket_id,
            "summary": jira_ticket.summary,
            "status": jira_ticket.status,
            "priority": jira_ticket.priority,
            "acceptance_criteria_count": len(jira_ticket.acceptance_criteria),
            "acceptance_criteria": jira_ticket.acceptance_criteria
        }
    
    async def _generate_phase3_summary(self, review_result: Dict, progress_callback: Optional[Callable]) -> Dict:
        """Generate Phase 3 tutorial summary using Claude Code CLI"""
        
        # Build Phase 3 prompt
        phase3_prompt = self.prompt_manager.get_phase3_tutorial_prompt(
            review_result.get("raw_content", ""),
            self.branch_name
        )
        
        try:
            # Execute Phase 3 generation
            tutorial_result = await self._execute_claude_code_review(phase3_prompt, progress_callback)
            
            # Extract the actual tutorial content
            raw_content = tutorial_result.get("content", tutorial_result.get("raw_content", ""))

            # Parse structured sections from the actual content
            structured_sections = self._parse_tutorial_sections(raw_content)

            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": raw_content,
                "structured_sections": structured_sections,
                "mermaid_diagrams": [],
                "code_examples": [],
                "metadata": {
                    "cost_usd": tutorial_result.get("cost_usd", 0.0),
                    "duration_ms": tutorial_result.get("duration_ms", 0),
                    "session_id": self.session_id
                }
            }
            
        except Exception as e:
            logger.warning(f"⚠️ Phase 3 generation failed: {e}")
            return {
                "tutorial_id": f"tutorial_{self.session_id}",
                "raw_content": "Phase 3 tutorial generation failed",
                "structured_sections": {},
                "mermaid_diagrams": [],
                "code_examples": [],
                "metadata": {
                    "error": str(e),
                    "session_id": self.session_id
                }
            }

    def _parse_tutorial_sections(self, content: str) -> Dict[str, str]:
        """Parse structured sections from tutorial content"""
        import re

        sections = {}

        if not content or not isinstance(content, str):
            return sections

        # Define section patterns to extract
        section_patterns = {
            'business_context': [
                r'(?:## 2\. 📊 BUSINESS CONTEXT & ZIELE|## 📊 BUSINESS CONTEXT|## BUSINESS CONTEXT)(.*?)(?=##|\Z)',
                r'(?:### Business Context|### BUSINESS CONTEXT)(.*?)(?=###|\Z)',
                r'(?:Business Context|BUSINESS CONTEXT)[^#]*?(?=##|###|\Z)'
            ],
            'architecture_overview': [
                r'(?:## 3\. 🏗️ ARCHITECTURE & DESIGN OVERVIEW|## 🏗️ ARCHITECTURE|## ARCHITECTURE)(.*?)(?=##|\Z)',
                r'(?:### Architecture Overview|### ARCHITECTURE OVERVIEW)(.*?)(?=###|\Z)',
                r'(?:Architecture Overview|ARCHITECTURE OVERVIEW)[^#]*?(?=##|###|\Z)'
            ],
            'implementation_guide': [
                r'(?:## 4\. 📁 DETAILLIERTE FILE-BY-FILE ANALYSE|## 📁 IMPLEMENTATION|## IMPLEMENTATION)(.*?)(?=##|\Z)',
                r'(?:### Implementation Guide|### IMPLEMENTATION GUIDE)(.*?)(?=###|\Z)',
                r'(?:Implementation Guide|IMPLEMENTATION GUIDE)[^#]*?(?=##|###|\Z)'
            ],
            'testing': [
                r'(?:## 6\. 🧪 TESTING STRATEGY|## 🧪 TESTING|## TESTING)(.*?)(?=##|\Z)',
                r'(?:### Testing Strategy|### TESTING STRATEGY)(.*?)(?=###|\Z)',
                r'(?:Testing Strategy|TESTING STRATEGY)[^#]*?(?=##|###|\Z)'
            ],
            'deployment': [
                r'(?:## 7\. 🚀 DEPLOYMENT & CONFIGURATION|## 🚀 DEPLOYMENT|## DEPLOYMENT)(.*?)(?=##|\Z)',
                r'(?:### Deployment Guide|### DEPLOYMENT GUIDE)(.*?)(?=###|\Z)',
                r'(?:Deployment Guide|DEPLOYMENT GUIDE)[^#]*?(?=##|###|\Z)'
            ]
        }

        # Extract each section using multiple patterns
        for section_name, patterns in section_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
                if match:
                    section_content = match.group(1).strip()
                    if section_content and len(section_content) > 50:  # Ensure meaningful content
                        sections[section_name] = section_content
                        break

        # If no structured sections found, try to extract meaningful content
        if not sections and content:
            # Look for any substantial content blocks
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip() and len(p.strip()) > 100]
            if paragraphs:
                sections['business_context'] = paragraphs[0] if len(paragraphs) > 0 else ""
                sections['architecture_overview'] = paragraphs[1] if len(paragraphs) > 1 else ""
                sections['implementation_guide'] = paragraphs[2] if len(paragraphs) > 2 else ""

        return sections

    async def _perform_phase3_summary(self, review_content: str, review_type: str, progress_callback: Optional[Callable] = None) -> Dict:
        """
        Perform Phase 3 summary generation (API compatibility method)
        
        Args:
            review_content: The completed review content
            review_type: Type of review that was performed
            progress_callback: Optional progress callback
            
        Returns:
            Dict with tutorial/summary results
        """
        return await self._generate_phase3_summary(
            {"raw_content": review_content, "review_type": review_type},
            progress_callback
        )


# Factory function for backward compatibility
def create_enhanced_reviewer_sdk(config: Dict, **kwargs) -> EnhancedClaudeReviewerSDK:
    """Factory function to create enhanced reviewer SDK instance"""
    return EnhancedClaudeReviewerSDK(config, **kwargs)


if __name__ == "__main__":
    # Example usage
    async def main():
        config = {
            "jira": {
                "url": "https://your-jira.atlassian.net",
                "api_token": "your_token",
                "email": "<EMAIL>"
            }
        }
        
        reviewer = EnhancedClaudeReviewerSDK(
            config=config,
            repo_path=".",
            branch_name="feature/test-branch"
        )
        
        result = await reviewer.perform_enhanced_review_async(
            review_type="comprehensive_with_ac",
            include_summary=True
        )
        
        print(json.dumps(result, indent=2))
    
    asyncio.run(main())