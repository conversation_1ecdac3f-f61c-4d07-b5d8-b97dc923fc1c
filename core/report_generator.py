"""
Unified Report Generator for Claude Code Review System
Provides consistent Markdown report generation for both CLI and API versions.
"""

import subprocess
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Union

# Handle imports for both standalone and package usage
try:
    from .jira_integration import JiraTicket
except ImportError:
    try:
        from jira_integration import JiraTicket
    except ImportError:
        # Fallback for testing - create a simple mock
        class JiraTicket:
            def __init__(self):
                self.ticket_id = ""
                self.summary = ""
                self.issue_type = ""
                self.status = ""
                self.priority = ""
                self.assignee = ""
                self.acceptance_criteria = []


class ReportGenerator:
    """
    Unified report generation class that creates identical Markdown reports
    for both CLI and API versions of the Claude Code Review System.
    """
    
    def __init__(self, working_path: Union[str, Path], repo_path: Union[str, Path], 
                 pr_url: Optional[str] = None, branch_name: Optional[str] = None):
        """
        Initialize the report generator.
        
        Args:
            working_path: Path to the working directory
            repo_path: Path to the repository root
            pr_url: Optional pull request URL
            branch_name: Optional branch name
        """
        self.working_path = Path(working_path)
        self.repo_path = Path(repo_path)
        self.pr_url = pr_url
        self.branch_name = branch_name
    
    def create_enhanced_report(self, review_content: str, review_type: str, 
                             jira_ticket: Optional[JiraTicket] = None,
                             jira_integration_enabled: bool = False) -> str:
        """
        Create enhanced report with Jira ticket information.
        This method generates identical reports as the CLI version.
        
        Args:
            review_content: The main review content from Claude
            review_type: Type of review (e.g., 'comprehensive_with_ac')
            jira_ticket: Optional Jira ticket information
            jira_integration_enabled: Whether Jira integration is enabled
            
        Returns:
            Complete Markdown report as string
        """
        # Git statistics
        diff_summary = self._get_git_diff_summary()
        changed_files = self._get_changed_files_list()
        
        # Report header
        report_content = f"""# Enhanced Code Review Report mit Jira Integration

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Review Type:** {review_type.upper()}  
**Repository:** {self.repo_path}  
**Working Directory:** {self.working_path}  
"""
        
        if self.pr_url:
            report_content += f"**Pull Request:** {self.pr_url}  \n"
        if self.branch_name:
            report_content += f"**Branch:** {self.branch_name}  \n"
        
        # Jira ticket information
        if jira_ticket:
            report_content += f"""
---

## 🎫 Jira Ticket Information

- **Ticket ID:** [{jira_ticket.ticket_id}](https://your-company.atlassian.net/browse/{jira_ticket.ticket_id})
- **Summary:** {jira_ticket.summary}
- **Type:** {jira_ticket.issue_type}
- **Status:** {jira_ticket.status}
- **Priority:** {jira_ticket.priority}
- **Assignee:** {jira_ticket.assignee}

### 📋 Acceptance Criteria ({len(jira_ticket.acceptance_criteria)} Items)
"""
            
            if jira_ticket.acceptance_criteria:
                for i, criterion in enumerate(jira_ticket.acceptance_criteria, 1):
                    report_content += f"{i}. {criterion}\n"
            else:
                report_content += "❌ Keine Acceptance Criteria definiert\n"
            
            if hasattr(jira_ticket, 'labels') and jira_ticket.labels:
                report_content += f"\n**Labels:** {', '.join(jira_ticket.labels)}  \n"
            if hasattr(jira_ticket, 'components') and jira_ticket.components:
                report_content += f"**Components:** {', '.join(jira_ticket.components)}  \n"
        else:
            report_content += f"""
---

## 🎫 Jira Ticket Information

⚠️  **Kein Jira Ticket verfügbar für diesen Branch**

**Empfehlung:** 
- Verwenden Sie Branch-Namen mit Ticket-ID (z.B. `feature/CMS20-1166-description`)
- Oder erstellen Sie eine `ticket.md` Datei mit Ticket-Informationen
- Oder konfigurieren Sie Jira API Zugang
"""
        
        # Change summary
        report_content += f"""
---

## 📊 Change Summary

```
{diff_summary}
```

**Geänderte Dateien:** {len(changed_files)}

"""
        
        if isinstance(changed_files, list) and changed_files:
            for file in changed_files:
                report_content += f"- `{file}`\n"
        
        # Review results
        report_content += f"""
---

## 🎯 Enhanced Review Results

{review_content}

---

## 📈 Report Information

- **Generated by:** Enhanced Claude Code PR Reviewer
- **Claude Code Version:** {self._get_claude_version()}
- **Jira Integration:** {'✅ Enabled' if jira_integration_enabled else '❌ Disabled'}
- **Ticket Loaded:** {'✅ Yes' if jira_ticket else '❌ No'}
- **AC Analysis:** {'✅ Included' if jira_ticket and jira_ticket.acceptance_criteria else '❌ Not Available'}
- **Working Directory:** `{self.working_path}`

---

## 🔧 Next Steps

### If AC Issues Found:
1. **Address Critical AC Failures** before merging
2. **Clarify Requirements** with Product Owner if needed
3. **Update Implementation** to match AC
4. **Re-run Review** after fixes

### Standard Process:
1. Review and address feedback
2. Update tests as needed
3. Update documentation
4. Request re-review if major changes

---

*Dieser Enhanced Review Report wurde automatisch mit Jira Integration generiert. Bei Fragen zu Requirements kontaktieren Sie den Product Owner oder Business Analyst.*
"""
        
        return report_content
    
    def _get_git_diff_summary(self) -> str:
        """Get a summary of Git changes"""
        try:
            # Git diff between target branch and current branch
            result = subprocess.run(
                ["git", "diff", "--stat", "origin/main...HEAD"],
                cwd=self.working_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                # Fallback to master if main doesn't exist
                result = subprocess.run(
                    ["git", "diff", "--stat", "origin/master...HEAD"],
                    cwd=self.working_path,
                    capture_output=True,
                    text=True
                )
            
            return result.stdout.strip() if result.returncode == 0 else "Keine Diff-Statistiken verfügbar"
            
        except Exception as e:
            return f"Fehler beim Abrufen der Git-Statistiken: {e}"
    
    def _get_changed_files_list(self) -> List[str]:
        """Get the list of changed files"""
        try:
            result = subprocess.run(
                ["git", "diff", "--name-only", "origin/main...HEAD"],
                cwd=self.working_path,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                # Fallback to master
                result = subprocess.run(
                    ["git", "diff", "--name-only", "origin/master...HEAD"],
                    cwd=self.working_path,
                    capture_output=True,
                    text=True
                )
            
            if result.stdout.strip():
                files = result.stdout.strip().split('\n')
                files = [f for f in files if f]  # Remove empty strings
                return files
            else:
                return []
            
        except Exception as e:
            print(f"⚠️  Fehler beim Abrufen der geänderten Dateien: {e}")
            return []
    
    def _get_claude_version(self) -> str:
        """Get the Claude Code version"""
        try:
            result = subprocess.run(
                ["claude", "--version"],
                capture_output=True,
                text=True
            )
            return result.stdout.strip()
        except:
            return "Unknown"
