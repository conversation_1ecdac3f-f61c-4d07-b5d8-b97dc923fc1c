# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is the `.devtools` directory of the RMA (Regionalmedien Austria) monorepo, containing a sophisticated PR review and analysis system. The system combines AI-powered code review with Jira integration, Git worktree management, and automated workflow orchestration.

## Essential Commands

### Development Environment Setup
```bash
# Activate Python virtual environment (required for all operations)
source venv/bin/activate

# Install dependencies (if needed)
pip install -r requirements.txt

# Verify dependencies
python3 -c "import requests, yaml, anthropic; print('✅ Dependencies OK')"

# Set up Jira integration (optional)
export JIRA_API_TOKEN="your_token_here"
```

### Core Review Commands

**Quick PR Review (3-5 minutes):**
```bash
./run_review.sh 'branch-name'
./run_review.sh 'branch-name' --quick
```

**Comprehensive Review (15-25 minutes):**
```bash
./run_review.sh 'branch-name' --full
./run_review.sh 'branch-name' --with-summary    # Includes Phase 3 documentation
```

**Specialized Reviews:**
```bash
./run_review.sh 'branch-name' --ac-only         # Acceptance Criteria only (8-12 min)
./run_review.sh 'branch-name' --bug-analysis    # Code quality focus (10-15 min)
./run_review.sh 'branch-name' --summary-only    # Documentation generation only
```

**Batch Processing:**
```bash
python3 cli-tools/multi_ticket_reviewer.py --config multi_ticket_config.json --mode quick
python3 cli-tools/multi_ticket_reviewer.py --config multi_ticket_config.json --mode full --parallel 3
```

### Direct Component Usage

**PR Analysis with Worktree Creation:**
```bash
python3 cli-tools/bitbucket_pr_analyzer.py --branch 'feature/CMS20-1234-description'
python3 cli-tools/bitbucket_pr_analyzer.py --pr-url 'https://bitbucket.org/workspace/repo/pull-requests/123'
```

**Claude Code Review:**
```bash
python3 core/enhanced_claude_reviewer.py --config pr_review_config.json
python3 core/enhanced_claude_reviewer.py --config pr_review_config.json --quick
```

**Jira Integration Testing:**
```bash
python3 core/jira_integration.py --branch 'feature/CMS20-1234-description'
python3 core/jira_integration.py --config pr_review_config.json --test-connection
```

## Architecture Overview

### Core System Components

**Review Engine (`core/`):**
- `enhanced_claude_reviewer.py` - Main orchestrator with 3-phase review system
- `summary_analyzer.py` - Phase 3 implementation and tutorial generation  
- `claude_code_reviewer.py` - Base Claude Code SDK integration
- `jira_integration.py` - Jira API integration and ticket processing
- `prompt_manager.py` - Prompt template management

**CLI Tools (`cli-tools/`):**
- `bitbucket_pr_analyzer.py` - Git worktree management and PR analysis
- `multi_ticket_reviewer.py` - Batch processing with parallel execution
- `pr_workflow_automation.py` - Complete workflow automation

**Entry Points:**
- `run_review.sh` - Primary entry point with auto-open functionality
- Configuration files: `pr_review_config.json`, `multi_ticket_config.json`

### Three-Phase Review System

**Phase 1: Acceptance Criteria Compliance (8-15 minutes)**
- Jira ticket integration with automatic branch pattern recognition
- Point-by-point AC validation with ✅/❌/⚠️ status tracking
- Business context discovery and requirement gap analysis
- Fallback mechanism: Jira API → local `ticket.md` → manual input

**Phase 2: Code Quality & Bug Detection (10-20 minutes)**
- Security vulnerability scanning with precise code locations
- Performance bottleneck identification  
- Code pattern analysis and architectural review
- Testing coverage assessment and recommendations

**Phase 3: Summary & Tutorial Documentation (5-10 minutes)**
- Tutorial-style implementation summaries with Mermaid diagrams
- Architecture documentation with decision rationale
- Onboarding guides for new team members
- Cross-component relationship mapping

### Git Worktree Management

The system creates isolated worktrees in `/Users/<USER>/dev/` with naming pattern: `{first-10-chars-of-branch}-review`

**Worktree Features:**
- Automatic creation outside main repository to avoid conflicts
- Persistent worktrees for manual review continuation
- Intelligent reuse logic (only reuses if in correct location)
- Automatic cleanup of misplaced worktrees from temp directories

### Jira Integration Patterns

**Automatic Ticket Extraction:**
- Branch patterns: `feature/CMS20-1234-description`, `bugfix/PROJ-567`, etc.
- Multiple pattern support with regex flexibility
- Custom field mapping for acceptance criteria extraction

**Authentication & Fallback:**
```bash
export JIRA_API_TOKEN="your_token_here"  # Primary method
# Fallback to ticket.md file if API unavailable
# Manual ticket ID specification in config
```

### Configuration Architecture

**Single PR Reviews:** Use `pr_review_config.json`
- Branch-specific configuration
- Review mode selection (quick/full/ac_only/bug_analysis)
- Jira integration settings
- Claude Code API parameters

**Batch Reviews:** Use `multi_ticket_config.json`  
- Parallel processing configuration (max 3 concurrent)
- Resource limits and monitoring
- Batch reporting and notification settings
- Performance optimization parameters

## Development Workflows

### Adding New Review Capabilities

1. **Extend Phase System:**
   - Add methods to `summary_analyzer.py` for Phase 3 extensions
   - Update `enhanced_claude_reviewer.py` orchestration logic
   - Modify prompt templates in the respective analyzer classes

2. **Custom Review Types:**
   - Define new focus areas in config `review_config.focus_areas`
   - Implement specialized prompts in the review engine
   - Add CLI argument support in `run_review.sh`

3. **Integration Extensions:**
   - Extend `jira_integration.py` for additional field support
   - Add notification channels in the integrations config
   - Implement new authentication methods as needed

### Performance Optimization

**Review Speed Optimization:**
- Use `--quick` mode for faster iteration (1-3 minutes)
- Leverage `--ac-only` or `--bug-analysis` for focused reviews
- Parallel batch processing for multiple PRs
- Configure shorter timeouts in config for faster feedback

**Resource Management:**
- Monitor with `resource_monitoring: true` in parallel config
- Adjust `max_parallel_reviews` based on system capacity
- Use `disk_space_check` for large repository operations

### Testing and Calibration

**System Testing:**
```bash
# Test individual components
python3 test_enhanced_integration.py               # Integration testing
python3 test_ac_extraction.py                      # Acceptance criteria extraction
python3 core/jira_integration.py --test-connection # Jira connectivity test
```

**ML Calibration System:**
```bash
python3 core/calibration_system.py --train-model    # Train quality prediction (if implemented)
python3 core/ml_calibration.py --optimize           # Optimize review parameters (if implemented)
```

## Important Notes

### Security & Authentication
- Never commit API tokens or credentials to repository
- Use environment variables for all sensitive configuration
- Jira tokens should have minimal required permissions
- Claude Code API calls are logged but not stored persistently

### Worktree Management
- Worktrees are created in `/Users/<USER>/dev/` and must be manually cleaned up
- Each worktree uses pattern: `{branch-short-name}-review`
- Old worktrees in temp directories are automatically removed
- Use `git worktree list` and `git worktree remove <path>` for manual cleanup

### Performance Considerations
- Comprehensive reviews can take 15-25 minutes for large PRs
- Quick reviews are recommended for iterative development
- Batch processing supports up to 3 parallel reviews optimally
- Large monorepos may require extended timeouts in configuration

### Output and Reports
- Main review reports: `enhanced_review_report.md`
- Phase 3 summaries: `{worktree-path}/IMPLEMENTATION_SUMMARY.md` 
- Auto-open functionality available with `--no-open` to disable
- JSON export available for CI/CD integration

## Troubleshooting

### Common Issues

**Python Environment:**
```bash
# If venv activation fails
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

**Jira Authentication:**
```bash
# Test Jira connection
python3 core/jira_integration.py --test-connection
# If fails, check JIRA_API_TOKEN environment variable
echo $JIRA_API_TOKEN
```

**Worktree Issues:**
```bash
# List existing worktrees
git worktree list
# Remove stuck worktree
git worktree remove /Users/<USER>/dev/{branch-name}-review
```

**Claude Code Issues:**
```bash
# Verify Claude Code is available
claude --version
# Check if authenticated
claude auth status
```

## Development Conventions

### Documentation Locations
- Use @docs/ to find the documentations on claude code sdk when developing on any claude code features in this codebase.