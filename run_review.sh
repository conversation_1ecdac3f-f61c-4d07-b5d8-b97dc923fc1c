#!/bin/bash
# Enhanced PR Review Activation Script

# Activate virtual environment
source venv/bin/activate

# Check if config and branch are provided
if [ -z "$1" ]; then
    echo "🚀 Enhanced PR Review System"
    echo ""
    echo "Usage:"
    echo "  $0 <branch-name>                    # Quick review"
    echo "  $0 <branch-name> --full            # Full review (AC + Code Quality)" 
    echo "  $0 <branch-name> --ac-only         # Nur Acceptance Criteria"
    echo "  $0 <branch-name> --bug-analysis    # Nur Bug Detection & Code Quality"
    echo "  $0 <branch-name> --with-summary    # Full review + Phase 3: Summary & Tutorial"
    echo "  $0 <branch-name> --summary-only    # Nur Phase 3: Summary & Tutorial"
    echo "  $0 <branch-name> --config <file>   # Custom config"
    echo "  $0 <branch-name> --no-open         # Don't auto-open generated files"
    echo ""
    echo "Examples:"
    echo "  $0 'feature/CMS20-1166-autorefresh-von-luftqualität-'"
    echo "  $0 'feature/CMS20-1166-autorefresh-von-luftqualität-' --full"
    echo "  $0 'feature/CMS20-1166-autorefresh-von-luftqualität-' --with-summary"
    echo "  $0 'feature/CMS20-1166-autorefresh-von-luftqualität-' --summary-only"
    echo "  $0 'feature/CMS20-1166-autorefresh-von-luftqualität-' --with-summary --no-open"
    echo ""
    exit 1
fi

BRANCH_NAME="$1"
CONFIG_FILE="pr_review_config.json"
QUICK_MODE="--quick"
EXTRA_ARGS=""
AUTO_OPEN="true"

# Parse additional arguments
shift
while [[ $# -gt 0 ]]; do
    case $1 in
        --full)
            QUICK_MODE=""
            shift
            ;;
        --ac-only)
            QUICK_MODE=""
            EXTRA_ARGS="--ac-only"
            shift
            ;;
        --bug-analysis)
            QUICK_MODE=""
            EXTRA_ARGS="--bug-analysis"
            shift
            ;;
        --with-summary)
            QUICK_MODE=""
            EXTRA_ARGS="--with-summary"
            shift
            ;;
        --summary-only)
            QUICK_MODE=""
            EXTRA_ARGS="--summary-only"
            shift
            ;;
        --config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        --no-open)
            AUTO_OPEN="false"
            shift
            ;;
        *)
            echo "❌ Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "🚀 Starting Enhanced PR Review..."
echo "📂 Branch: $BRANCH_NAME"
echo "⚙️  Config: $CONFIG_FILE"
if [ -n "$EXTRA_ARGS" ]; then
    case "$EXTRA_ARGS" in
        "--with-summary")
            echo "🎯 Mode: FULL REVIEW + PHASE 3 SUMMARY & TUTORIAL"
            ;;
        "--summary-only") 
            echo "🎯 Mode: PHASE 3 SUMMARY & TUTORIAL ONLY"
            ;;
        *)
            echo "🎯 Mode: $(echo $EXTRA_ARGS | sed 's/--//' | tr '-' ' ' | tr '[:lower:]' '[:upper:]')"
            ;;
    esac
else
    echo "⚡ Mode: $([ -z "$QUICK_MODE" ] && echo "Full Review (AC + Code Quality)" || echo "Quick Review")"
fi
echo ""

# Run the review
echo "🔄 Executing review..."
python3 core/enhanced_claude_reviewer.py --config "$CONFIG_FILE" --branch "$BRANCH_NAME" $QUICK_MODE $EXTRA_ARGS
REVIEW_EXIT_CODE=$?

# Check if review was successful and auto-open is enabled
if [ $REVIEW_EXIT_CODE -eq 0 ] && [ "$AUTO_OPEN" = "true" ]; then
    echo ""
    echo "🎉 Review completed successfully!"
    echo "📂 Opening generated files..."
    
    # Find and open the main review report
    REPORT_FILE="enhanced_review_report.md"
    if [ -f "$REPORT_FILE" ]; then
        echo "📄 Opening: $REPORT_FILE"
        open "$REPORT_FILE"
    fi
    
    # Find and open the Phase 3 summary (if with-summary or summary-only was used)
    if [[ "$EXTRA_ARGS" == *"--with-summary"* ]] || [[ "$EXTRA_ARGS" == *"--summary-only"* ]]; then
        # Calculate worktree name (first 10 chars of branch name + "-review")
        BRANCH_SHORT=$(echo "$BRANCH_NAME" | sed 's/[^a-zA-Z0-9-]/-/g' | cut -c1-10)
        WORKTREE_NAME="${BRANCH_SHORT}-review"
        WORKTREE_PATH="/Users/<USER>/dev/${WORKTREE_NAME}"
        SUMMARY_FILE="${WORKTREE_PATH}/IMPLEMENTATION_SUMMARY.md"
        
        if [ -f "$SUMMARY_FILE" ]; then
            echo "📊 Opening: $SUMMARY_FILE"
            open "$SUMMARY_FILE"
        else
            echo "⚠️  Summary file not found at: $SUMMARY_FILE"
        fi
    fi
    
    echo "✅ Files opened successfully!"
elif [ $REVIEW_EXIT_CODE -ne 0 ]; then
    echo ""
    echo "❌ Review failed with exit code: $REVIEW_EXIT_CODE"
    exit $REVIEW_EXIT_CODE
elif [ "$AUTO_OPEN" = "false" ]; then
    echo ""
    echo "✅ Review completed successfully!"
    echo "💡 Generated files are ready for manual review"
fi