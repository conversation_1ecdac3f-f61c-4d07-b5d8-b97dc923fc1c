-- Initial Database Setup for Multi-Agent Code Reviewer
-- This script runs automatically when PostgreSQL container starts

-- Create additional databases if needed
-- CREATE DATABASE code_reviewer_test;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For GIN indexes

-- Set timezone
SET timezone TO 'UTC';

-- Initial user privileges (already handled by POSTGRES_USER/POSTGRES_DB)
-- The main database and user are created automatically by the postgres image

-- Print completion message
SELECT 'Database initialization completed successfully' AS status;