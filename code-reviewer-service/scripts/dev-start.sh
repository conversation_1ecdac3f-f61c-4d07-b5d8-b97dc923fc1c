#!/bin/bash
# Development Server Start Script
set -e

echo "🚀 Starting Multi-Agent Code Reviewer Development Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Check if we're in the right directory
if [[ ! -f "pyproject.toml" ]]; then
    print_error "Please run this script from the code-reviewer-service directory"
    exit 1
fi

# Check if virtual environment exists
if [[ ! -d "venv" ]]; then
    print_error "Virtual environment not found. Run './scripts/setup.sh' first"
    exit 1
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Check if .env file exists
if [[ ! -f ".env" ]]; then
    print_warning ".env file not found, using default configuration"
fi

# Load environment variables
if [[ -f ".env" ]]; then
    print_status "Loading environment variables from .env"
    export $(grep -v '^#' .env | xargs)
fi

# Set default values if not set
export ENVIRONMENT=${ENVIRONMENT:-"development"}
export DEBUG=${DEBUG:-"true"}
export HOST=${HOST:-"0.0.0.0"}
export PORT=${PORT:-"8000"}
export LOG_LEVEL=${LOG_LEVEL:-"INFO"}

# Check Python path and dependencies
print_status "Checking Python environment..."
python3 -c "import fastapi, uvicorn, pydantic; print('✅ Core dependencies available')" || {
    print_error "Dependencies not installed. Run './scripts/setup.sh' first"
    exit 1
}

# Create necessary directories
print_status "Creating runtime directories..."
mkdir -p logs
mkdir -p ${DEFAULT_WORKING_DIRECTORY:-"/tmp/code-reviews"}

# Function to handle graceful shutdown
cleanup() {
    print_status "Shutting down development server..."
    exit 0
}

# Trap signals for graceful shutdown
trap cleanup SIGINT SIGTERM

# Start the development server
print_status "Starting FastAPI development server..."
print_status "Environment: $ENVIRONMENT"
print_status "Debug mode: $DEBUG"
print_status "Server will be available at: http://$HOST:$PORT"
print_status "API documentation at: http://$HOST:$PORT/docs"
print_status "Health check at: http://$HOST:$PORT/health"

echo ""
print_status "Press Ctrl+C to stop the server"
echo ""

# Start with hot reload enabled
if [[ "$DEBUG" == "true" ]]; then
    uvicorn src.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --reload \
        --reload-dir src \
        --log-level "$(echo "$LOG_LEVEL" | tr '[:upper:]' '[:lower:]')" \
        --access-log
else
    uvicorn src.main:app \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$(echo "$LOG_LEVEL" | tr '[:upper:]' '[:lower:]')" \
        --access-log
fi