#!/bin/bash
# Database Setup Script for Multi-Agent Code Reviewer Service

set -e

echo "🗄️  Setting up PostgreSQL database for Multi-Agent Code Reviewer..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration
DB_NAME="code_reviewer_dev"
DB_USER="code_reviewer"
DB_PASSWORD="dev_password"
DB_HOST="localhost"
DB_PORT="5432"

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}🐳 $1${NC}"
}

# Check if Docker is available first
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    print_header "Docker Development Environment Available"
    print_status "For faster setup, consider using Docker instead:"
    echo ""
    echo "  🐳 Docker Setup (Recommended):"
    echo "    ./scripts/docker-dev.sh up    # Start PostgreSQL & Redis"
    echo "    ./scripts/dev-start.sh        # Start the application"
    echo ""
    echo "  📚 Documentation:"
    echo "    See DOCKER_SETUP.md for complete Docker setup guide"
    echo ""
    
    read -p "Continue with local PostgreSQL setup instead? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Setup cancelled. Use './scripts/docker-dev.sh up' for Docker setup."
        exit 0
    fi
    print_status "Continuing with local PostgreSQL setup..."
    echo ""
fi

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL is not installed."
    echo "Install PostgreSQL:"
    echo "  macOS: brew install postgresql@15"
    echo "  Ubuntu: sudo apt-get install postgresql postgresql-contrib"
    echo "  Windows: Download from https://www.postgresql.org/download/"
    exit 1
fi

# Check if PostgreSQL service is running
if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
    print_warning "PostgreSQL service is not running."
    echo "Start PostgreSQL:"
    echo "  macOS: brew services start postgresql@15"
    echo "  Ubuntu: sudo systemctl start postgresql"
    echo "  Windows: Start via Services or pgAdmin"
    
    # Try to start automatically on macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_status "Attempting to start PostgreSQL..."
        brew services start postgresql@15 || brew services start postgresql
    fi
    
    # Wait a moment and check again
    sleep 2
    if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
        print_error "Could not start PostgreSQL. Please start it manually."
        exit 1
    fi
fi

print_status "PostgreSQL is running"

# Check if user exists, create if not
if ! sudo -u postgres psql -t -c "SELECT 1 FROM pg_user WHERE usename = '$DB_USER'" 2>/dev/null | grep -q 1; then
    print_status "Creating database user: $DB_USER"
    sudo -u postgres createuser -d -l -P $DB_USER || {
        # Try alternative method for macOS
        createuser -d -l -P $DB_USER 2>/dev/null || {
            print_warning "Could not create user with createuser, trying psql..."
            psql -d postgres -c "CREATE USER $DB_USER WITH CREATEDB LOGIN PASSWORD '$DB_PASSWORD';" || {
                print_error "Failed to create database user. You may need to create it manually:"
                echo "  psql -d postgres"
                echo "  CREATE USER $DB_USER WITH CREATEDB LOGIN PASSWORD '$DB_PASSWORD';"
                exit 1
            }
        }
    }
else
    print_status "Database user $DB_USER already exists"
fi

# Check if database exists, create if not
if ! psql -h $DB_HOST -p $DB_PORT -U $DB_USER -lqt 2>/dev/null | cut -d \| -f 1 | grep -qw $DB_NAME; then
    print_status "Creating database: $DB_NAME"
    createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME || {
        print_error "Failed to create database. You may need to create it manually:"
        echo "  psql -h $DB_HOST -p $DB_PORT -U postgres"
        echo "  CREATE DATABASE $DB_NAME OWNER $DB_USER;"
        exit 1
    }
else
    print_status "Database $DB_NAME already exists"
fi

# Test connection
print_status "Testing database connection..."
PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null || {
    print_error "Database connection test failed!"
    echo "Connection details:"
    echo "  Host: $DB_HOST"
    echo "  Port: $DB_PORT"
    echo "  Database: $DB_NAME"
    echo "  User: $DB_USER"
    exit 1
}

print_status "✅ Database setup completed successfully!"
echo ""
echo "Database connection details:"
echo "  URL: postgresql+psycopg://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
echo "  Host: $DB_HOST"
echo "  Port: $DB_PORT"
echo "  Database: $DB_NAME"
echo "  User: $DB_USER"
echo ""
echo "Next steps:"
echo "  1. Ensure .env file has correct DATABASE_URL"
echo "  2. Run './scripts/dev-start.sh' to start the service"
echo "  3. Run database migrations with Alembic"
echo ""
print_header "Note: Docker Alternative Available"
echo "For easier development, consider using:"
echo "  ./scripts/docker-dev.sh up    # No local PostgreSQL installation needed"
echo "  See DOCKER_SETUP.md for details"