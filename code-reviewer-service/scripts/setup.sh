#!/bin/bash
# Development Environment Setup Script
set -e

echo "🚀 Setting up Multi-Agent Code Reviewer Development Environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python 3.11+ is available
print_status "Checking Python version..."
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed"
    exit 1
fi

PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
print_status "Found Python $PYTHON_VERSION"

# Check if we're in the right directory
if [[ ! -f "pyproject.toml" ]]; then
    print_error "Please run this script from the code-reviewer-service directory"
    exit 1
fi

# Create virtual environment
print_status "Creating virtual environment..."
if [[ ! -d "venv" ]]; then
    python3 -m venv venv
    print_status "Virtual environment created"
else
    print_warning "Virtual environment already exists"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    print_status "Installing Poetry..."
    pip install poetry
else
    print_status "Poetry is already installed"
fi

# Install dependencies with Poetry
print_status "Installing project dependencies with Poetry..."
poetry install --with dev

# Alternative: Install with pip if Poetry fails
if [[ $? -ne 0 ]]; then
    print_warning "Poetry installation failed, falling back to pip..."
    pip install -r requirements.txt
fi

# Create .env file from template if it doesn't exist
if [[ ! -f ".env" ]]; then
    print_status "Creating .env file from template..."
    if [[ -f ".env.sample" ]]; then
        cp .env.sample .env
        print_status ".env file created from .env.sample template"
    else
        print_warning ".env.sample not found, creating basic .env file"
        cat > .env << 'EOF'
# Multi-Agent Code Reviewer Service Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
ALLOWED_ORIGINS=*
ENABLE_DATABASE=true
DATABASE_URL=postgresql+psycopg://code_reviewer:dev_password@localhost:5432/code_reviewer_dev
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
REDIS_URL=redis://:dev_password@localhost:6379
REDIS_DB=0
CLAUDE_API_KEY=
ANTHROPIC_API_KEY=
CLAUDE_MAX_TURNS=5
CLAUDE_TIMEOUT=600
CLAUDE_RATE_LIMIT_RPM=100
MAX_PARALLEL_AGENTS=7
AGENT_EXECUTION_TIMEOUT=900
WEBSOCKET_PING_INTERVAL=25
WEBSOCKET_PING_TIMEOUT=60
ENABLE_TELEMETRY=true
JWT_SECRET_KEY=dev-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
ENABLE_API_DOCS=true
ENABLE_DEBUG_ROUTES=true
MOCK_EXTERNAL_SERVICES=false
EOF
    fi
else
    print_warning ".env file already exists"
fi

# Create logs directory
print_status "Creating logs directory..."
mkdir -p logs

# Initialize git repository if not already done
if [[ ! -d ".git" ]]; then
    print_status "Initializing git repository..."
    git init
    git add .
    git commit -m "Initial commit: Multi-Agent Code Reviewer Service setup"
else
    print_warning "Git repository already exists"
fi

# Install pre-commit hooks if available
if command -v pre-commit &> /dev/null; then
    print_status "Installing pre-commit hooks..."
    pre-commit install
else
    print_warning "pre-commit not installed, skipping hooks setup"
fi

# Check Docker and services
print_status "Checking Docker and services..."

if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    print_status "Docker and docker-compose are available"
    
    # Check if containers are running
    if docker-compose ps | grep -q "Up"; then
        print_status "Docker containers are running"
        
        # Check PostgreSQL in container
        if docker-compose exec -T postgres pg_isready -U code_reviewer -d code_reviewer_dev &> /dev/null; then
            print_status "PostgreSQL container is ready"
        else
            print_warning "PostgreSQL container is not ready"
        fi
        
        # Check Redis in container
        if docker-compose exec -T redis redis-cli ping &> /dev/null; then
            print_status "Redis container is ready"
        else
            print_warning "Redis container is not ready"
        fi
    else
        print_warning "Docker containers are not running"
        print_status "Start with: ./scripts/docker-dev.sh up"
    fi
else
    print_warning "Docker not found - checking local services..."
    
    # Fallback to local service checks
    if command -v pg_isready &> /dev/null; then
        if pg_isready -h localhost -p 5432 &> /dev/null; then
            print_status "Local PostgreSQL is running"
        else
            print_warning "PostgreSQL is not running on localhost:5432"
        fi
    else
        print_warning "PostgreSQL client not found"
    fi
    
    if command -v redis-cli &> /dev/null; then
        if redis-cli -h localhost -p 6379 ping &> /dev/null; then
            print_status "Local Redis is running"
        else
            print_warning "Redis is not running on localhost:6379"
        fi
    else
        print_warning "Redis client not found"
    fi
fi

print_status "Setup complete! 🎉"
echo ""
echo "Next steps:"
echo "1. Update .env file with your actual configuration values (especially CLAUDE_API_KEY)"
echo ""
echo "🐳 Docker Development (Recommended):"
echo "  ./scripts/docker-dev.sh up          # Start PostgreSQL & Redis"
echo "  ./scripts/dev-start.sh              # Start the application"
echo ""
echo "🏠 Local Development:"
echo "  ./scripts/setup-database.sh         # Set up local PostgreSQL"
echo "  ./scripts/dev-start.sh              # Start the application"
echo ""
echo "📚 Documentation:"
echo "  http://localhost:8000/docs          # API documentation"
echo "  http://localhost:8000/health        # Health check"
echo "  See DOCKER_SETUP.md for Docker details"
echo ""
echo "To activate the virtual environment manually:"
echo "  source venv/bin/activate"