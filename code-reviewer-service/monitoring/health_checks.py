"""
Comprehensive Health Checks for Multi-Agent Code Reviewer
Monitors system health, performance, and functionality across all components.
"""

import asyncio
import aiohttp
import psutil
import psycopg2
import redis
import json
import time
import logging
import sys
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import subprocess
import socket
from contextlib import asynccontextmanager
import ssl
import certifi

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: str  # 'healthy', 'warning', 'critical', 'unknown'
    message: str
    response_time_ms: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'status': self.status,
            'message': self.message,
            'response_time_ms': self.response_time_ms,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class HealthSummary:
    """Summary of all health checks."""
    overall_status: str
    healthy_count: int
    warning_count: int
    critical_count: int
    total_checks: int
    check_results: List[HealthCheckResult]
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'overall_status': self.overall_status,
            'healthy_count': self.healthy_count,
            'warning_count': self.warning_count,
            'critical_count': self.critical_count,
            'total_checks': self.total_checks,
            'check_results': [result.to_dict() for result in self.check_results],
            'timestamp': self.timestamp.isoformat()
        }


class HealthChecker:
    """Comprehensive health checker for the multi-agent system."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._load_default_config()
        self.results: List[HealthCheckResult] = []
        
        # Service endpoints
        self.main_service_url = self.config.get('main_service_url', 'http://localhost:8000')
        self.dashboard_url = self.config.get('dashboard_url', 'http://localhost:8080')
        self.database_url = self.config.get('database_url', 'postgresql://localhost:5432/code_reviewer')
        self.redis_url = self.config.get('redis_url', 'redis://localhost:6379')
        
        # Thresholds
        self.response_time_threshold = self.config.get('response_time_threshold_ms', 5000)
        self.cpu_threshold = self.config.get('cpu_threshold_percent', 80)
        self.memory_threshold = self.config.get('memory_threshold_percent', 85)
        self.disk_threshold = self.config.get('disk_threshold_percent', 90)
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration."""
        return {
            'timeout_seconds': 30,
            'max_retries': 3,
            'retry_delay': 5,
            'parallel_checks': True,
            'detailed_logging': True
        }
    
    async def run_all_checks(self) -> HealthSummary:
        """Run all health checks and return summary."""
        logger.info("Starting comprehensive health checks...")
        
        self.results = []
        start_time = time.time()
        
        # Define all health checks
        health_checks = [
            self._check_main_service_health,
            self._check_database_connectivity,
            self._check_redis_connectivity,
            self._check_system_resources,
            self._check_docker_services,
            self._check_feature_flags,
            self._check_api_endpoints,
            self._check_websocket_connectivity,
            self._check_performance_dashboard,
            self._check_disk_space,
            self._check_network_connectivity,
            self._check_ssl_certificates,
            self._check_log_files,
            self._check_backup_systems,
            self._check_monitoring_systems
        ]
        
        # Run checks in parallel or sequential
        if self.config.get('parallel_checks', True):
            await self._run_checks_parallel(health_checks)
        else:
            await self._run_checks_sequential(health_checks)
        
        total_time = (time.time() - start_time) * 1000
        logger.info(f"Health checks completed in {total_time:.2f}ms")
        
        return self._generate_summary()
    
    async def _run_checks_parallel(self, checks: List):
        """Run health checks in parallel."""
        tasks = [asyncio.create_task(check()) for check in checks]
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _run_checks_sequential(self, checks: List):
        """Run health checks sequentially."""
        for check in checks:
            try:
                await check()
            except Exception as e:
                logger.error(f"Health check {check.__name__} failed: {e}")
    
    def _add_result(self, name: str, status: str, message: str, 
                   response_time_ms: float = 0.0, metadata: Optional[Dict] = None):
        """Add a health check result."""
        result = HealthCheckResult(
            name=name,
            status=status,
            message=message,
            response_time_ms=response_time_ms,
            metadata=metadata or {}
        )
        self.results.append(result)
        
        # Log result
        log_level = {
            'healthy': logging.INFO,
            'warning': logging.WARNING,
            'critical': logging.ERROR,
            'unknown': logging.WARNING
        }.get(status, logging.INFO)
        
        logger.log(log_level, f"Health Check [{name}]: {status.upper()} - {message}")
    
    async def _check_main_service_health(self):
        """Check main service health endpoint."""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(f"{self.main_service_url}/health") as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        data = await response.json()
                        
                        if response_time > self.response_time_threshold:
                            self._add_result(
                                "Main Service Health",
                                "warning",
                                f"Service healthy but slow response: {response_time:.2f}ms",
                                response_time,
                                data
                            )
                        else:
                            self._add_result(
                                "Main Service Health",
                                "healthy",
                                f"Service healthy: {response_time:.2f}ms",
                                response_time,
                                data
                            )
                    else:
                        self._add_result(
                            "Main Service Health",
                            "critical",
                            f"Service returned status {response.status}",
                            response_time
                        )
                        
        except asyncio.TimeoutError:
            self._add_result(
                "Main Service Health",
                "critical",
                "Service health check timed out",
                30000
            )
        except Exception as e:
            self._add_result(
                "Main Service Health",
                "critical",
                f"Service health check failed: {str(e)}"
            )
    
    async def _check_database_connectivity(self):
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            # Test connection
            conn = psycopg2.connect(self.database_url)
            cursor = conn.cursor()
            
            # Test query
            cursor.execute("SELECT version(), now(), pg_database_size(current_database())")
            result = cursor.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            # Get database size
            db_size_bytes = result[2]
            db_size_mb = db_size_bytes / (1024 * 1024)
            
            # Check connection pool
            cursor.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            active_connections = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            metadata = {
                'database_version': result[0],
                'database_time': result[1].isoformat(),
                'database_size_mb': round(db_size_mb, 2),
                'active_connections': active_connections,
                'response_time_ms': response_time
            }
            
            if response_time > 1000:  # 1 second threshold for DB
                self._add_result(
                    "Database Connectivity",
                    "warning",
                    f"Database slow to respond: {response_time:.2f}ms",
                    response_time,
                    metadata
                )
            else:
                self._add_result(
                    "Database Connectivity", 
                    "healthy",
                    f"Database connected: {response_time:.2f}ms",
                    response_time,
                    metadata
                )
                
        except Exception as e:
            self._add_result(
                "Database Connectivity",
                "critical",
                f"Database connection failed: {str(e)}"
            )
    
    async def _check_redis_connectivity(self):
        """Check Redis connectivity and performance."""
        start_time = time.time()
        
        try:
            r = redis.from_url(self.redis_url)
            
            # Test ping
            r.ping()
            
            # Test set/get
            test_key = f"health_check_{int(time.time())}"
            r.set(test_key, "test_value", ex=60)
            value = r.get(test_key)
            r.delete(test_key)
            
            response_time = (time.time() - start_time) * 1000
            
            # Get Redis info
            info = r.info()
            
            metadata = {
                'redis_version': info.get('redis_version'),
                'connected_clients': info.get('connected_clients'),
                'used_memory_mb': round(info.get('used_memory', 0) / (1024 * 1024), 2),
                'response_time_ms': response_time
            }
            
            if response_time > 500:  # 500ms threshold for Redis
                self._add_result(
                    "Redis Connectivity",
                    "warning", 
                    f"Redis slow to respond: {response_time:.2f}ms",
                    response_time,
                    metadata
                )
            else:
                self._add_result(
                    "Redis Connectivity",
                    "healthy",
                    f"Redis connected: {response_time:.2f}ms",
                    response_time,
                    metadata
                )
                
        except Exception as e:
            self._add_result(
                "Redis Connectivity",
                "critical",
                f"Redis connection failed: {str(e)}"
            )
    
    async def _check_system_resources(self):
        """Check system resource usage."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Load average (Unix-like systems)
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                load_avg = (0, 0, 0)  # Windows doesn't have load average
            
            metadata = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': round(memory.used / (1024**3), 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'disk_percent': round((disk.used / disk.total) * 100, 2),
                'disk_free_gb': round(disk.free / (1024**3), 2),
                'load_average': load_avg
            }
            
            # Determine status based on thresholds
            status = "healthy"
            messages = []
            
            if cpu_percent > self.cpu_threshold:
                status = "warning" if cpu_percent < 95 else "critical"
                messages.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if memory.percent > self.memory_threshold:
                status = "critical" if memory.percent > 95 else "warning"
                messages.append(f"High memory usage: {memory.percent:.1f}%")
            
            if (disk.used / disk.total) * 100 > self.disk_threshold:
                status = "critical"
                messages.append(f"Low disk space: {(disk.used / disk.total) * 100:.1f}%")
            
            message = "; ".join(messages) if messages else f"System resources normal: CPU {cpu_percent:.1f}%, Memory {memory.percent:.1f}%"
            
            self._add_result(
                "System Resources",
                status,
                message,
                0,
                metadata
            )
            
        except Exception as e:
            self._add_result(
                "System Resources",
                "unknown",
                f"Failed to check system resources: {str(e)}"
            )
    
    async def _check_docker_services(self):
        """Check Docker services status."""
        try:
            # Check if Docker is running
            result = subprocess.run(['docker', 'ps', '--format', 'json'], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                self._add_result(
                    "Docker Services",
                    "critical",
                    "Docker is not running or not accessible"
                )
                return
            
            # Parse container information
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    containers.append(json.loads(line))
            
            # Check specific services
            required_services = ['multi-agent-reviewer-prod', 'redis-prod']
            running_services = [c['Names'] for c in containers]
            
            missing_services = [svc for svc in required_services if not any(svc in name for name in running_services)]
            
            metadata = {
                'total_containers': len(containers),
                'running_services': running_services,
                'required_services': required_services,
                'missing_services': missing_services
            }
            
            if missing_services:
                self._add_result(
                    "Docker Services",
                    "critical",
                    f"Missing required services: {', '.join(missing_services)}",
                    0,
                    metadata
                )
            else:
                self._add_result(
                    "Docker Services",
                    "healthy",
                    f"All required services running ({len(containers)} containers)",
                    0,
                    metadata
                )
                
        except subprocess.TimeoutExpired:
            self._add_result(
                "Docker Services",
                "warning",
                "Docker command timed out"
            )
        except Exception as e:
            self._add_result(
                "Docker Services",
                "unknown",
                f"Failed to check Docker services: {str(e)}"
            )
    
    async def _check_feature_flags(self):
        """Check feature flags system."""
        try:
            # Try to import and test feature flags
            import sys
            sys.path.append('.')
            
            from src.config.feature_flags import get_feature_flag_manager
            
            manager = get_feature_flag_manager()
            flags = manager.get_all_flags_status()
            
            # Check critical flags
            critical_flags = ['multi_agent_execution', 'circuit_breaker']
            flag_status = {}
            
            for flag_name in critical_flags:
                if flag_name in flags:
                    flag_status[flag_name] = flags[flag_name]['status']
                else:
                    flag_status[flag_name] = 'missing'
            
            metadata = {
                'total_flags': len(flags),
                'flag_status': flag_status,
                'parallel_execution_enabled': flags.get('multi_agent_execution', {}).get('status') == 'enabled'
            }
            
            missing_flags = [f for f, s in flag_status.items() if s == 'missing']
            
            if missing_flags:
                self._add_result(
                    "Feature Flags",
                    "warning",
                    f"Missing critical flags: {', '.join(missing_flags)}",
                    0,
                    metadata
                )
            else:
                self._add_result(
                    "Feature Flags",
                    "healthy",
                    f"Feature flags system operational ({len(flags)} flags)",
                    0,
                    metadata
                )
                
        except ImportError:
            self._add_result(
                "Feature Flags",
                "warning",
                "Feature flags module not accessible"
            )
        except Exception as e:
            self._add_result(
                "Feature Flags",
                "warning",
                f"Feature flags check failed: {str(e)}"
            )
    
    async def _check_api_endpoints(self):
        """Check critical API endpoints."""
        endpoints = [
            ('/health', 'GET'),
            ('/api/v1/health', 'GET'),
            ('/api/v1/feature-flags', 'GET'),
        ]
        
        for endpoint, method in endpoints:
            await self._check_single_endpoint(endpoint, method)
    
    async def _check_single_endpoint(self, endpoint: str, method: str = 'GET'):
        """Check a single API endpoint."""
        start_time = time.time()
        endpoint_name = f"API {method} {endpoint}"
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                url = f"{self.main_service_url}{endpoint}"
                
                if method == 'GET':
                    async with session.get(url) as response:
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status == 200:
                            self._add_result(
                                endpoint_name,
                                "healthy",
                                f"Endpoint accessible: {response_time:.2f}ms",
                                response_time
                            )
                        else:
                            self._add_result(
                                endpoint_name,
                                "warning",
                                f"Endpoint returned status {response.status}",
                                response_time
                            )
                            
        except Exception as e:
            self._add_result(
                endpoint_name,
                "critical",
                f"Endpoint check failed: {str(e)}"
            )
    
    async def _check_websocket_connectivity(self):
        """Check WebSocket connectivity."""
        try:
            import websockets
            
            start_time = time.time()
            websocket_url = self.main_service_url.replace('http', 'ws') + '/ws'
            
            async with websockets.connect(websocket_url, timeout=10) as websocket:
                # Send a ping
                await websocket.send('{"type": "ping"}')
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                
                response_time = (time.time() - start_time) * 1000
                
                self._add_result(
                    "WebSocket Connectivity",
                    "healthy",
                    f"WebSocket connection successful: {response_time:.2f}ms",
                    response_time
                )
                
        except ImportError:
            self._add_result(
                "WebSocket Connectivity",
                "unknown",
                "WebSocket client not available"
            )
        except Exception as e:
            self._add_result(
                "WebSocket Connectivity",
                "warning",
                f"WebSocket connection failed: {str(e)}"
            )
    
    async def _check_performance_dashboard(self):
        """Check performance dashboard availability."""
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.get(f"{self.dashboard_url}/health") as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response.status == 200:
                        self._add_result(
                            "Performance Dashboard",
                            "healthy",
                            f"Dashboard accessible: {response_time:.2f}ms",
                            response_time
                        )
                    else:
                        self._add_result(
                            "Performance Dashboard",
                            "warning",
                            f"Dashboard returned status {response.status}",
                            response_time
                        )
                        
        except Exception as e:
            self._add_result(
                "Performance Dashboard",
                "warning",
                f"Dashboard check failed: {str(e)}"
            )
    
    async def _check_disk_space(self):
        """Check disk space for critical directories."""
        critical_paths = [
            '/',           # Root filesystem
            '/tmp',        # Temporary files
            '/var/log',    # Log files
        ]
        
        try:
            for path in critical_paths:
                if Path(path).exists():
                    disk = psutil.disk_usage(path)
                    free_percent = (disk.free / disk.total) * 100
                    
                    if free_percent < 10:  # Less than 10% free
                        self._add_result(
                            f"Disk Space {path}",
                            "critical",
                            f"Very low disk space: {free_percent:.1f}% free"
                        )
                    elif free_percent < 20:  # Less than 20% free
                        self._add_result(
                            f"Disk Space {path}",
                            "warning",
                            f"Low disk space: {free_percent:.1f}% free"
                        )
                    else:
                        self._add_result(
                            f"Disk Space {path}",
                            "healthy",
                            f"Sufficient disk space: {free_percent:.1f}% free"
                        )
            
        except Exception as e:
            self._add_result(
                "Disk Space",
                "unknown",
                f"Failed to check disk space: {str(e)}"
            )
    
    async def _check_network_connectivity(self):
        """Check network connectivity to external services."""
        hosts = [
            ('google.com', 80),
            ('api.anthropic.com', 443),
        ]
        
        for host, port in hosts:
            try:
                start_time = time.time()
                
                with socket.create_connection((host, port), timeout=10) as sock:
                    response_time = (time.time() - start_time) * 1000
                    
                    self._add_result(
                        f"Network {host}",
                        "healthy",
                        f"Network connectivity OK: {response_time:.2f}ms",
                        response_time
                    )
                    
            except Exception as e:
                self._add_result(
                    f"Network {host}",
                    "warning",
                    f"Network connectivity failed: {str(e)}"
                )
    
    async def _check_ssl_certificates(self):
        """Check SSL certificate validity."""
        # This is a placeholder for SSL certificate checking
        # In a real implementation, you would check certificate expiration dates
        self._add_result(
            "SSL Certificates",
            "healthy",
            "SSL certificate check not implemented"
        )
    
    async def _check_log_files(self):
        """Check log files for errors and disk usage."""
        log_dir = Path('./logs')
        
        try:
            if not log_dir.exists():
                self._add_result(
                    "Log Files",
                    "warning",
                    "Log directory does not exist"
                )
                return
            
            # Check log file sizes
            total_size = 0
            error_count = 0
            
            for log_file in log_dir.glob('*.log'):
                file_size = log_file.stat().st_size
                total_size += file_size
                
                # Check for recent errors (last 100 lines)
                try:
                    with open(log_file, 'r') as f:
                        lines = f.readlines()
                        recent_lines = lines[-100:] if len(lines) > 100 else lines
                        
                        for line in recent_lines:
                            if 'ERROR' in line.upper():
                                error_count += 1
                except:
                    pass  # Skip files we can't read
            
            total_size_mb = total_size / (1024 * 1024)
            
            metadata = {
                'total_log_size_mb': round(total_size_mb, 2),
                'recent_error_count': error_count
            }
            
            if total_size_mb > 1000:  # More than 1GB of logs
                self._add_result(
                    "Log Files",
                    "warning",
                    f"Large log files: {total_size_mb:.1f}MB, {error_count} recent errors",
                    0,
                    metadata
                )
            elif error_count > 10:
                self._add_result(
                    "Log Files",
                    "warning",
                    f"Many recent errors: {error_count} errors in logs",
                    0,
                    metadata
                )
            else:
                self._add_result(
                    "Log Files",
                    "healthy",
                    f"Logs healthy: {total_size_mb:.1f}MB, {error_count} recent errors",
                    0,
                    metadata
                )
                
        except Exception as e:
            self._add_result(
                "Log Files",
                "unknown",
                f"Failed to check log files: {str(e)}"
            )
    
    async def _check_backup_systems(self):
        """Check backup systems."""
        backup_dir = Path('./backups')
        
        try:
            if not backup_dir.exists():
                self._add_result(
                    "Backup Systems",
                    "warning",
                    "Backup directory does not exist"
                )
                return
            
            # Find recent backups (last 7 days)
            cutoff_date = datetime.now() - timedelta(days=7)
            recent_backups = []
            
            for backup_path in backup_dir.iterdir():
                if backup_path.is_dir():
                    backup_time = datetime.fromtimestamp(backup_path.stat().st_mtime)
                    if backup_time > cutoff_date:
                        recent_backups.append(backup_path.name)
            
            metadata = {
                'recent_backups': recent_backups,
                'backup_count': len(recent_backups)
            }
            
            if not recent_backups:
                self._add_result(
                    "Backup Systems",
                    "critical",
                    "No recent backups found (last 7 days)",
                    0,
                    metadata
                )
            elif len(recent_backups) < 3:
                self._add_result(
                    "Backup Systems",
                    "warning",
                    f"Few recent backups: {len(recent_backups)} in last 7 days",
                    0,
                    metadata
                )
            else:
                self._add_result(
                    "Backup Systems",
                    "healthy",
                    f"Backup systems healthy: {len(recent_backups)} recent backups",
                    0,
                    metadata
                )
                
        except Exception as e:
            self._add_result(
                "Backup Systems",
                "unknown",
                f"Failed to check backup systems: {str(e)}"
            )
    
    async def _check_monitoring_systems(self):
        """Check monitoring systems."""
        # This is a placeholder for monitoring system checks
        # In a real implementation, you would check monitoring agents, metrics collection, etc.
        self._add_result(
            "Monitoring Systems",
            "healthy",
            "Monitoring system check not implemented"
        )
    
    def _generate_summary(self) -> HealthSummary:
        """Generate health check summary."""
        healthy_count = sum(1 for r in self.results if r.status == 'healthy')
        warning_count = sum(1 for r in self.results if r.status == 'warning')
        critical_count = sum(1 for r in self.results if r.status == 'critical')
        
        # Overall status logic
        if critical_count > 0:
            overall_status = 'critical'
        elif warning_count > 2:  # More than 2 warnings
            overall_status = 'warning'
        elif warning_count > 0:
            overall_status = 'healthy_with_warnings'
        else:
            overall_status = 'healthy'
        
        return HealthSummary(
            overall_status=overall_status,
            healthy_count=healthy_count,
            warning_count=warning_count,
            critical_count=critical_count,
            total_checks=len(self.results),
            check_results=self.results
        )


async def main():
    """Main health check function."""
    parser = argparse.ArgumentParser(description='Multi-Agent Code Reviewer Health Checks')
    parser.add_argument('--environment', default='production', help='Environment to check')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--output', choices=['json', 'text'], default='text', help='Output format')
    parser.add_argument('--continuous', action='store_true', help='Run continuous health checks')
    parser.add_argument('--interval', type=int, default=60, help='Interval for continuous checks (seconds)')
    parser.add_argument('--quiet', action='store_true', help='Minimal output')
    
    args = parser.parse_args()
    
    # Load configuration
    config = {}
    if args.config and Path(args.config).exists():
        with open(args.config, 'r') as f:
            config = json.load(f)
    
    config['environment'] = args.environment
    
    # Create health checker
    checker = HealthChecker(config)
    
    if args.continuous:
        # Continuous monitoring
        logger.info(f"Starting continuous health checks (interval: {args.interval}s)")
        
        while True:
            try:
                summary = await checker.run_all_checks()
                
                if not args.quiet:
                    if args.output == 'json':
                        print(json.dumps(summary.to_dict(), indent=2))
                    else:
                        print(f"\n=== Health Check Summary - {summary.timestamp} ===")
                        print(f"Overall Status: {summary.overall_status.upper()}")
                        print(f"Healthy: {summary.healthy_count}, Warnings: {summary.warning_count}, Critical: {summary.critical_count}")
                        
                        if summary.critical_count > 0:
                            print("\nCRITICAL ISSUES:")
                            for result in summary.check_results:
                                if result.status == 'critical':
                                    print(f"  - {result.name}: {result.message}")
                
                await asyncio.sleep(args.interval)
                
            except KeyboardInterrupt:
                logger.info("Continuous health checks stopped")
                break
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(args.interval)
    else:
        # Single health check run
        summary = await checker.run_all_checks()
        
        if args.output == 'json':
            print(json.dumps(summary.to_dict(), indent=2))
        else:
            print(f"\n=== Multi-Agent Code Reviewer Health Check ===")
            print(f"Timestamp: {summary.timestamp}")
            print(f"Environment: {args.environment}")
            print(f"Overall Status: {summary.overall_status.upper()}")
            print(f"Total Checks: {summary.total_checks}")
            print(f"  ✅ Healthy: {summary.healthy_count}")
            print(f"  ⚠️  Warnings: {summary.warning_count}")
            print(f"  ❌ Critical: {summary.critical_count}")
            
            if not args.quiet:
                print(f"\n=== Detailed Results ===")
                for result in summary.check_results:
                    status_icon = {
                        'healthy': '✅',
                        'warning': '⚠️',
                        'critical': '❌',
                        'unknown': '❓'
                    }.get(result.status, '❓')
                    
                    print(f"{status_icon} {result.name}: {result.message}")
                    if result.response_time_ms > 0:
                        print(f"    Response Time: {result.response_time_ms:.2f}ms")
        
        # Exit with appropriate code
        if summary.overall_status == 'critical':
            sys.exit(2)
        elif summary.overall_status in ['warning', 'healthy_with_warnings']:
            sys.exit(1)
        else:
            sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())