"""
Performance Monitoring Dashboard
Real-time monitoring and visualization of multi-agent system performance.
"""

import asyncio
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from pathlib import Path
import psutil
from collections import deque, defaultdict
import threading
import sqlite3
from contextlib import asynccontextmanager

import plotly.graph_objs as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn


logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric data point."""
    timestamp: datetime
    metric_type: str  # 'execution_time', 'memory_usage', 'cpu_usage', etc.
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'timestamp': self.timestamp.isoformat(),
            'metric_type': self.metric_type,
            'value': self.value,
            'metadata': self.metadata
        }


@dataclass
class ReviewMetrics:
    """Metrics for a single review execution."""
    review_id: str
    execution_mode: str  # 'sequential' or 'parallel'
    start_time: datetime
    end_time: Optional[datetime] = None
    total_execution_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    
    # Agent-specific metrics
    agent_metrics: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Resource usage
    peak_memory_mb: float = 0.0
    avg_cpu_percent: float = 0.0
    
    # Quality metrics
    total_findings: int = 0
    critical_findings: int = 0
    
    # User context
    user_id: Optional[str] = None
    workspace_size_mb: float = 0.0
    file_count: int = 0
    
    def complete(self, end_time: datetime, success: bool = True, error_message: Optional[str] = None):
        """Mark review as completed and calculate final metrics."""
        self.end_time = end_time
        self.success = success
        self.error_message = error_message
        if self.end_time:
            self.total_execution_time = (self.end_time - self.start_time).total_seconds()
    
    def add_agent_metric(self, agent_type: str, execution_time: float, success: bool, findings: int = 0):
        """Add metrics for a specific agent."""
        self.agent_metrics[agent_type] = {
            'execution_time': execution_time,
            'success': success,
            'findings': findings,
            'timestamp': datetime.now().isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class MetricsCollector:
    """Collects and stores performance metrics."""
    
    def __init__(self, db_path: str = "performance_metrics.db", max_memory_points: int = 10000):
        self.db_path = db_path
        self.max_memory_points = max_memory_points
        
        # In-memory storage for real-time metrics
        self.metrics_buffer: deque = deque(maxlen=max_memory_points)
        self.active_reviews: Dict[str, ReviewMetrics] = {}
        self.completed_reviews: deque = deque(maxlen=1000)
        
        # Aggregated metrics
        self.hourly_aggregates: defaultdict = defaultdict(list)
        self.daily_aggregates: defaultdict = defaultdict(list)
        
        # System monitoring
        self.system_metrics: deque = deque(maxlen=max_memory_points)
        
        # Initialize database
        self._init_database()
        
        # Start background tasks
        self._monitoring_task = None
        self._aggregation_task = None
        self._start_background_tasks()
    
    def _init_database(self):
        """Initialize SQLite database for persistent storage."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    metric_type TEXT NOT NULL,
                    value REAL NOT NULL,
                    metadata TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS review_metrics (
                    review_id TEXT PRIMARY KEY,
                    execution_mode TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    total_execution_time REAL DEFAULT 0,
                    success BOOLEAN DEFAULT 1,
                    error_message TEXT,
                    agent_metrics TEXT,
                    peak_memory_mb REAL DEFAULT 0,
                    avg_cpu_percent REAL DEFAULT 0,
                    total_findings INTEGER DEFAULT 0,
                    critical_findings INTEGER DEFAULT 0,
                    user_id TEXT,
                    workspace_size_mb REAL DEFAULT 0,
                    file_count INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    cpu_percent REAL,
                    memory_percent REAL,
                    memory_used_mb REAL,
                    disk_usage_percent REAL,
                    active_reviews INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON performance_metrics(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_metrics_type ON performance_metrics(metric_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_reviews_mode ON review_metrics(execution_mode)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_reviews_start_time ON review_metrics(start_time)")
    
    def _start_background_tasks(self):
        """Start background monitoring and aggregation tasks."""
        self._monitoring_task = asyncio.create_task(self._system_monitoring_loop())
        self._aggregation_task = asyncio.create_task(self._metrics_aggregation_loop())
    
    async def _system_monitoring_loop(self):
        """Continuously monitor system metrics."""
        while True:
            try:
                # Collect system metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                timestamp = datetime.now()
                
                # Store in memory buffer
                system_metric = {
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_mb': memory.used / 1024 / 1024,
                    'disk_usage_percent': disk.percent,
                    'active_reviews': len(self.active_reviews)
                }
                
                self.system_metrics.append(system_metric)
                
                # Store in database periodically
                if len(self.system_metrics) % 60 == 0:  # Every minute
                    await self._persist_system_metrics()
                
                await asyncio.sleep(5)  # Collect every 5 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System monitoring error: {e}")
                await asyncio.sleep(5)
    
    async def _metrics_aggregation_loop(self):
        """Aggregate metrics hourly and daily."""
        while True:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._aggregate_metrics()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Metrics aggregation error: {e}")
    
    async def _persist_system_metrics(self):
        """Persist system metrics to database."""
        try:
            recent_metrics = list(self.system_metrics)[-60:]  # Last minute
            
            with sqlite3.connect(self.db_path) as conn:
                for metric in recent_metrics:
                    conn.execute("""
                        INSERT INTO system_metrics 
                        (timestamp, cpu_percent, memory_percent, memory_used_mb, 
                         disk_usage_percent, active_reviews)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        metric['timestamp'].isoformat(),
                        metric['cpu_percent'],
                        metric['memory_percent'],
                        metric['memory_used_mb'],
                        metric['disk_usage_percent'],
                        metric['active_reviews']
                    ))
                
        except Exception as e:
            logger.error(f"Failed to persist system metrics: {e}")
    
    async def _aggregate_metrics(self):
        """Aggregate metrics for reporting."""
        now = datetime.now()
        hour_key = now.strftime("%Y-%m-%d %H:00")
        day_key = now.strftime("%Y-%m-%d")
        
        # Aggregate review metrics
        completed_last_hour = [
            review for review in self.completed_reviews
            if review.end_time and (now - review.end_time).total_seconds() < 3600
        ]
        
        if completed_last_hour:
            sequential_reviews = [r for r in completed_last_hour if r.execution_mode == "sequential"]
            parallel_reviews = [r for r in completed_last_hour if r.execution_mode == "parallel"]
            
            hourly_summary = {
                'timestamp': hour_key,
                'total_reviews': len(completed_last_hour),
                'sequential_count': len(sequential_reviews),
                'parallel_count': len(parallel_reviews),
                'avg_execution_time': sum(r.total_execution_time for r in completed_last_hour) / len(completed_last_hour),
                'success_rate': sum(1 for r in completed_last_hour if r.success) / len(completed_last_hour) * 100,
                'total_findings': sum(r.total_findings for r in completed_last_hour),
                'critical_findings': sum(r.critical_findings for r in completed_last_hour)
            }
            
            if sequential_reviews and parallel_reviews:
                seq_avg = sum(r.total_execution_time for r in sequential_reviews) / len(sequential_reviews)
                par_avg = sum(r.total_execution_time for r in parallel_reviews) / len(parallel_reviews)
                hourly_summary['speedup_factor'] = seq_avg / par_avg if par_avg > 0 else 0
            
            self.hourly_aggregates[hour_key] = hourly_summary
    
    def add_metric(self, metric_type: str, value: float, metadata: Optional[Dict[str, Any]] = None):
        """Add a performance metric."""
        metric = PerformanceMetric(
            timestamp=datetime.now(),
            metric_type=metric_type,
            value=value,
            metadata=metadata or {}
        )
        
        self.metrics_buffer.append(metric)
        
        # Persist to database periodically
        if len(self.metrics_buffer) % 100 == 0:
            asyncio.create_task(self._persist_metrics())
    
    async def _persist_metrics(self):
        """Persist metrics to database."""
        try:
            recent_metrics = list(self.metrics_buffer)[-100:]  # Last 100 metrics
            
            with sqlite3.connect(self.db_path) as conn:
                for metric in recent_metrics:
                    conn.execute("""
                        INSERT INTO performance_metrics (timestamp, metric_type, value, metadata)
                        VALUES (?, ?, ?, ?)
                    """, (
                        metric.timestamp.isoformat(),
                        metric.metric_type,
                        metric.value,
                        json.dumps(metric.metadata)
                    ))
                
        except Exception as e:
            logger.error(f"Failed to persist metrics: {e}")
    
    def start_review(self, review_id: str, execution_mode: str, user_id: Optional[str] = None, 
                    workspace_size_mb: float = 0.0, file_count: int = 0) -> ReviewMetrics:
        """Start tracking a new review."""
        review_metrics = ReviewMetrics(
            review_id=review_id,
            execution_mode=execution_mode,
            start_time=datetime.now(),
            user_id=user_id,
            workspace_size_mb=workspace_size_mb,
            file_count=file_count
        )
        
        self.active_reviews[review_id] = review_metrics
        self.add_metric("review_started", 1.0, {"execution_mode": execution_mode})
        
        return review_metrics
    
    def complete_review(self, review_id: str, success: bool = True, error_message: Optional[str] = None):
        """Complete tracking of a review."""
        if review_id in self.active_reviews:
            review_metrics = self.active_reviews[review_id]
            review_metrics.complete(datetime.now(), success, error_message)
            
            # Move to completed reviews
            self.completed_reviews.append(review_metrics)
            del self.active_reviews[review_id]
            
            # Add completion metric
            self.add_metric(
                "review_completed",
                review_metrics.total_execution_time,
                {
                    "execution_mode": review_metrics.execution_mode,
                    "success": success,
                    "total_findings": review_metrics.total_findings
                }
            )
            
            # Persist to database
            asyncio.create_task(self._persist_review_metrics(review_metrics))
    
    async def _persist_review_metrics(self, review_metrics: ReviewMetrics):
        """Persist review metrics to database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO review_metrics 
                    (review_id, execution_mode, start_time, end_time, total_execution_time,
                     success, error_message, agent_metrics, peak_memory_mb, avg_cpu_percent,
                     total_findings, critical_findings, user_id, workspace_size_mb, file_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    review_metrics.review_id,
                    review_metrics.execution_mode,
                    review_metrics.start_time.isoformat(),
                    review_metrics.end_time.isoformat() if review_metrics.end_time else None,
                    review_metrics.total_execution_time,
                    review_metrics.success,
                    review_metrics.error_message,
                    json.dumps(review_metrics.agent_metrics),
                    review_metrics.peak_memory_mb,
                    review_metrics.avg_cpu_percent,
                    review_metrics.total_findings,
                    review_metrics.critical_findings,
                    review_metrics.user_id,
                    review_metrics.workspace_size_mb,
                    review_metrics.file_count
                ))
                
        except Exception as e:
            logger.error(f"Failed to persist review metrics: {e}")
    
    def get_metrics(self, metric_type: Optional[str] = None, 
                   start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None) -> List[PerformanceMetric]:
        """Get metrics from memory buffer with optional filtering."""
        metrics = list(self.metrics_buffer)
        
        if metric_type:
            metrics = [m for m in metrics if m.metric_type == metric_type]
        
        if start_time:
            metrics = [m for m in metrics if m.timestamp >= start_time]
        
        if end_time:
            metrics = [m for m in metrics if m.timestamp <= end_time]
        
        return metrics
    
    def get_recent_reviews(self, limit: int = 50) -> List[ReviewMetrics]:
        """Get recent completed reviews."""
        return list(self.completed_reviews)[-limit:]
    
    def get_active_reviews(self) -> List[ReviewMetrics]:
        """Get currently active reviews."""
        return list(self.active_reviews.values())
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics."""
        now = datetime.now()
        recent_reviews = [
            r for r in self.completed_reviews
            if r.end_time and (now - r.end_time).total_seconds() < 3600
        ]
        
        if not recent_reviews:
            return {"error": "No recent reviews available"}
        
        sequential_reviews = [r for r in recent_reviews if r.execution_mode == "sequential"]
        parallel_reviews = [r for r in recent_reviews if r.execution_mode == "parallel"]
        
        summary = {
            "last_hour": {
                "total_reviews": len(recent_reviews),
                "sequential_count": len(sequential_reviews),
                "parallel_count": len(parallel_reviews),
                "avg_execution_time": sum(r.total_execution_time for r in recent_reviews) / len(recent_reviews),
                "success_rate": sum(1 for r in recent_reviews if r.success) / len(recent_reviews) * 100,
                "active_reviews": len(self.active_reviews)
            }
        }
        
        if sequential_reviews and parallel_reviews:
            seq_avg = sum(r.total_execution_time for r in sequential_reviews) / len(sequential_reviews)
            par_avg = sum(r.total_execution_time for r in parallel_reviews) / len(parallel_reviews)
            summary["performance_comparison"] = {
                "sequential_avg": seq_avg,
                "parallel_avg": par_avg,
                "speedup_factor": seq_avg / par_avg if par_avg > 0 else 0,
                "improvement_percent": ((seq_avg - par_avg) / seq_avg) * 100 if seq_avg > 0 else 0
            }
        
        # System metrics
        if self.system_metrics:
            recent_system = list(self.system_metrics)[-12:]  # Last minute (5s intervals)
            summary["system"] = {
                "avg_cpu_percent": sum(m['cpu_percent'] for m in recent_system) / len(recent_system),
                "avg_memory_percent": sum(m['memory_percent'] for m in recent_system) / len(recent_system),
                "memory_used_mb": recent_system[-1]['memory_used_mb'],
                "disk_usage_percent": recent_system[-1]['disk_usage_percent']
            }
        
        return summary


class PerformanceDashboard:
    """Web-based performance dashboard."""
    
    def __init__(self, metrics_collector: MetricsCollector, port: int = 8080):
        self.metrics_collector = metrics_collector
        self.port = port
        self.app = FastAPI(title="Multi-Agent Performance Dashboard")
        self.websocket_connections: List[WebSocket] = []
        
        self._setup_routes()
        
        # Start metrics broadcasting
        self._broadcast_task = None
    
    def _setup_routes(self):
        """Setup FastAPI routes."""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard():
            return self._get_dashboard_html()
        
        @self.app.get("/api/metrics/summary")
        async def metrics_summary():
            return self.metrics_collector.get_performance_summary()
        
        @self.app.get("/api/metrics/{metric_type}")
        async def get_metrics(metric_type: str, hours: int = 1):
            start_time = datetime.now() - timedelta(hours=hours)
            metrics = self.metrics_collector.get_metrics(metric_type, start_time)
            return [m.to_dict() for m in metrics]
        
        @self.app.get("/api/reviews/recent")
        async def recent_reviews(limit: int = 50):
            reviews = self.metrics_collector.get_recent_reviews(limit)
            return [r.to_dict() for r in reviews]
        
        @self.app.get("/api/reviews/active")
        async def active_reviews():
            reviews = self.metrics_collector.get_active_reviews()
            return [r.to_dict() for r in reviews]
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                while True:
                    await websocket.receive_text()  # Keep connection alive
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
        
        @self.app.get("/api/charts/performance_trend")
        async def performance_trend_chart():
            """Generate performance trend chart."""
            return await self._generate_performance_trend_chart()
        
        @self.app.get("/api/charts/comparison")
        async def comparison_chart():
            """Generate sequential vs parallel comparison chart."""
            return await self._generate_comparison_chart()
        
        @self.app.get("/api/charts/system_health")
        async def system_health_chart():
            """Generate system health chart."""
            return await self._generate_system_health_chart()
    
    def _get_dashboard_html(self) -> str:
        """Generate dashboard HTML."""
        return """
<!DOCTYPE html>
<html>
<head>
    <title>Multi-Agent Performance Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .metric-label { font-size: 0.9em; color: #666; text-transform: uppercase; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 5px; }
        .status-good { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        .refresh-btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Multi-Agent Performance Dashboard</h1>
        <p>Real-time monitoring of parallel vs sequential execution performance</p>
        <button class="refresh-btn" onclick="refreshDashboard()">🔄 Refresh</button>
    </div>
    
    <div class="metrics-grid" id="metricsGrid">
        <!-- Metrics will be loaded here -->
    </div>
    
    <div class="chart-container">
        <h3>📊 Performance Trend</h3>
        <div id="performanceTrendChart"></div>
    </div>
    
    <div class="chart-container">
        <h3>⚡ Sequential vs Parallel Comparison</h3>
        <div id="comparisonChart"></div>
    </div>
    
    <div class="chart-container">
        <h3>🖥️ System Health</h3>
        <div id="systemHealthChart"></div>
    </div>
    
    <div class="chart-container">
        <h3>📋 Recent Reviews</h3>
        <div id="recentReviews"></div>
    </div>

    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket('ws://localhost:8080/ws');
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            updateDashboard(data);
        };
        
        async function loadMetrics() {
            try {
                const response = await fetch('/api/metrics/summary');
                const data = await response.json();
                updateMetricsGrid(data);
            } catch (error) {
                console.error('Error loading metrics:', error);
            }
        }
        
        function updateMetricsGrid(data) {
            const grid = document.getElementById('metricsGrid');
            
            if (data.error) {
                grid.innerHTML = '<div class="metric-card"><p>No recent data available</p></div>';
                return;
            }
            
            const lastHour = data.last_hour || {};
            const comparison = data.performance_comparison || {};
            const system = data.system || {};
            
            grid.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value">${lastHour.total_reviews || 0}</div>
                    <div class="metric-label">Reviews (Last Hour)</div>
                    <div style="margin-top: 10px;">
                        <span class="status-indicator status-good"></span> ${lastHour.parallel_count || 0} Parallel
                        <br>
                        <span class="status-indicator status-warning"></span> ${lastHour.sequential_count || 0} Sequential
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${(comparison.speedup_factor || 0).toFixed(1)}x</div>
                    <div class="metric-label">Speed Improvement</div>
                    <div style="margin-top: 10px;">
                        <small>Target: 7.0x</small>
                        <div style="background: #eee; height: 8px; border-radius: 4px; margin-top: 5px;">
                            <div style="background: ${comparison.speedup_factor >= 7 ? '#4CAF50' : comparison.speedup_factor >= 5 ? '#FF9800' : '#F44336'}; 
                                        height: 100%; width: ${Math.min(100, (comparison.speedup_factor / 7) * 100)}%; border-radius: 4px;"></div>
                        </div>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${(lastHour.success_rate || 0).toFixed(1)}%</div>
                    <div class="metric-label">Success Rate</div>
                    <div style="margin-top: 10px;">
                        <span class="status-indicator ${lastHour.success_rate >= 95 ? 'status-good' : lastHour.success_rate >= 90 ? 'status-warning' : 'status-error'}"></span>
                        ${lastHour.success_rate >= 95 ? 'Excellent' : lastHour.success_rate >= 90 ? 'Good' : 'Needs Attention'}
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${lastHour.active_reviews || 0}</div>
                    <div class="metric-label">Active Reviews</div>
                    <div style="margin-top: 10px;">
                        <small>Avg: ${(lastHour.avg_execution_time || 0).toFixed(1)}s</small>
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${(system.avg_cpu_percent || 0).toFixed(1)}%</div>
                    <div class="metric-label">CPU Usage</div>
                    <div style="margin-top: 10px;">
                        <span class="status-indicator ${system.avg_cpu_percent < 70 ? 'status-good' : system.avg_cpu_percent < 85 ? 'status-warning' : 'status-error'}"></span>
                        ${(system.memory_used_mb || 0).toFixed(0)}MB RAM
                    </div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-value">${(comparison.improvement_percent || 0).toFixed(1)}%</div>
                    <div class="metric-label">Performance Gain</div>
                    <div style="margin-top: 10px;">
                        <small>Sequential: ${(comparison.sequential_avg || 0).toFixed(1)}s</small><br>
                        <small>Parallel: ${(comparison.parallel_avg || 0).toFixed(1)}s</small>
                    </div>
                </div>
            `;
        }
        
        async function loadCharts() {
            // Load performance trend chart
            try {
                const response = await fetch('/api/charts/performance_trend');
                const chartData = await response.json();
                Plotly.newPlot('performanceTrendChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('Error loading performance trend chart:', error);
            }
            
            // Load comparison chart
            try {
                const response = await fetch('/api/charts/comparison');
                const chartData = await response.json();
                Plotly.newPlot('comparisonChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('Error loading comparison chart:', error);
            }
            
            // Load system health chart
            try {
                const response = await fetch('/api/charts/system_health');
                const chartData = await response.json();
                Plotly.newPlot('systemHealthChart', chartData.data, chartData.layout, {responsive: true});
            } catch (error) {
                console.error('Error loading system health chart:', error);
            }
        }
        
        async function loadRecentReviews() {
            try {
                const response = await fetch('/api/reviews/recent?limit=10');
                const reviews = await response.json();
                
                const container = document.getElementById('recentReviews');
                
                if (reviews.length === 0) {
                    container.innerHTML = '<p>No recent reviews</p>';
                    return;
                }
                
                const reviewsHtml = reviews.map(review => `
                    <div style="border-left: 4px solid ${review.execution_mode === 'parallel' ? '#4CAF50' : '#FF9800'}; 
                                padding: 10px; margin: 10px 0; background: #f9f9f9;">
                        <strong>${review.review_id}</strong> 
                        <span style="color: ${review.success ? '#4CAF50' : '#F44336'};">
                            ${review.success ? '✅' : '❌'}
                        </span>
                        <br>
                        <small>
                            Mode: ${review.execution_mode} | 
                            Time: ${review.total_execution_time.toFixed(1)}s | 
                            Findings: ${review.total_findings}
                        </small>
                    </div>
                `).join('');
                
                container.innerHTML = reviewsHtml;
                
            } catch (error) {
                console.error('Error loading recent reviews:', error);
            }
        }
        
        function refreshDashboard() {
            loadMetrics();
            loadCharts();
            loadRecentReviews();
        }
        
        // Initial load
        refreshDashboard();
        
        // Auto-refresh every 30 seconds
        setInterval(refreshDashboard, 30000);
    </script>
</body>
</html>
        """
    
    async def _generate_performance_trend_chart(self) -> Dict[str, Any]:
        """Generate performance trend chart data."""
        # Get recent metrics
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=2)
        
        execution_metrics = self.metrics_collector.get_metrics("review_completed", start_time, end_time)
        
        if not execution_metrics:
            return {
                "data": [],
                "layout": {
                    "title": "Performance Trend (Last 2 Hours)",
                    "xaxis": {"title": "Time"},
                    "yaxis": {"title": "Execution Time (seconds)"},
                    "annotations": [{"text": "No data available", "x": 0.5, "y": 0.5, "showarrow": False}]
                }
            }
        
        # Separate by execution mode
        sequential_times = []
        parallel_times = []
        sequential_timestamps = []
        parallel_timestamps = []
        
        for metric in execution_metrics:
            if metric.metadata.get("execution_mode") == "sequential":
                sequential_times.append(metric.value)
                sequential_timestamps.append(metric.timestamp)
            elif metric.metadata.get("execution_mode") == "parallel":
                parallel_times.append(metric.value)
                parallel_timestamps.append(metric.timestamp)
        
        traces = []
        
        if sequential_times:
            traces.append({
                "x": sequential_timestamps,
                "y": sequential_times,
                "mode": "lines+markers",
                "name": "Sequential",
                "line": {"color": "#FF9800"},
                "marker": {"size": 6}
            })
        
        if parallel_times:
            traces.append({
                "x": parallel_timestamps,
                "y": parallel_times,
                "mode": "lines+markers",
                "name": "Parallel",
                "line": {"color": "#4CAF50"},
                "marker": {"size": 6}
            })
        
        return {
            "data": traces,
            "layout": {
                "title": "Performance Trend (Last 2 Hours)",
                "xaxis": {"title": "Time"},
                "yaxis": {"title": "Execution Time (seconds)"},
                "hovermode": "x unified",
                "showlegend": True
            }
        }
    
    async def _generate_comparison_chart(self) -> Dict[str, Any]:
        """Generate sequential vs parallel comparison chart."""
        recent_reviews = self.metrics_collector.get_recent_reviews(100)
        
        if not recent_reviews:
            return {
                "data": [],
                "layout": {
                    "title": "Sequential vs Parallel Comparison",
                    "annotations": [{"text": "No data available", "x": 0.5, "y": 0.5, "showarrow": False}]
                }
            }
        
        sequential_times = [r.total_execution_time for r in recent_reviews if r.execution_mode == "sequential"]
        parallel_times = [r.total_execution_time for r in recent_reviews if r.execution_mode == "parallel"]
        
        traces = []
        
        if sequential_times:
            traces.append({
                "x": sequential_times,
                "type": "histogram",
                "name": "Sequential",
                "opacity": 0.7,
                "marker": {"color": "#FF9800"},
                "nbinsx": 20
            })
        
        if parallel_times:
            traces.append({
                "x": parallel_times,
                "type": "histogram",
                "name": "Parallel",
                "opacity": 0.7,
                "marker": {"color": "#4CAF50"},
                "nbinsx": 20
            })
        
        return {
            "data": traces,
            "layout": {
                "title": "Execution Time Distribution",
                "xaxis": {"title": "Execution Time (seconds)"},
                "yaxis": {"title": "Frequency"},
                "barmode": "overlay",
                "showlegend": True
            }
        }
    
    async def _generate_system_health_chart(self) -> Dict[str, Any]:
        """Generate system health chart."""
        recent_system = list(self.metrics_collector.system_metrics)[-120:]  # Last 10 minutes
        
        if not recent_system:
            return {
                "data": [],
                "layout": {
                    "title": "System Health",
                    "annotations": [{"text": "No system data available", "x": 0.5, "y": 0.5, "showarrow": False}]
                }
            }
        
        timestamps = [m['timestamp'] for m in recent_system]
        cpu_usage = [m['cpu_percent'] for m in recent_system]
        memory_usage = [m['memory_percent'] for m in recent_system]
        active_reviews = [m['active_reviews'] for m in recent_system]
        
        traces = [
            {
                "x": timestamps,
                "y": cpu_usage,
                "mode": "lines",
                "name": "CPU %",
                "line": {"color": "#2196F3"}
            },
            {
                "x": timestamps,
                "y": memory_usage,
                "mode": "lines",
                "name": "Memory %",
                "line": {"color": "#9C27B0"},
                "yaxis": "y"
            },
            {
                "x": timestamps,
                "y": active_reviews,
                "mode": "lines+markers",
                "name": "Active Reviews",
                "line": {"color": "#4CAF50"},
                "yaxis": "y2",
                "marker": {"size": 4}
            }
        ]
        
        return {
            "data": traces,
            "layout": {
                "title": "System Health (Last 10 Minutes)",
                "xaxis": {"title": "Time"},
                "yaxis": {"title": "Percentage (%)", "range": [0, 100]},
                "yaxis2": {
                    "title": "Active Reviews",
                    "overlaying": "y",
                    "side": "right"
                },
                "hovermode": "x unified",
                "showlegend": True
            }
        }
    
    async def start_broadcasting(self):
        """Start broadcasting metrics to WebSocket connections."""
        self._broadcast_task = asyncio.create_task(self._broadcast_loop())
    
    async def _broadcast_loop(self):
        """Broadcast loop for real-time updates."""
        while True:
            try:
                if self.websocket_connections:
                    summary = self.metrics_collector.get_performance_summary()
                    
                    # Broadcast to all connected clients
                    disconnected = []
                    for websocket in self.websocket_connections:
                        try:
                            await websocket.send_text(json.dumps(summary))
                        except:
                            disconnected.append(websocket)
                    
                    # Remove disconnected clients
                    for ws in disconnected:
                        self.websocket_connections.remove(ws)
                
                await asyncio.sleep(10)  # Broadcast every 10 seconds
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Broadcast error: {e}")
    
    async def run(self):
        """Run the dashboard server."""
        await self.start_broadcasting()
        
        config = uvicorn.Config(
            app=self.app,
            host="0.0.0.0",
            port=self.port,
            log_level="info"
        )
        server = uvicorn.Server(config)
        
        logger.info(f"Performance Dashboard starting on http://localhost:{self.port}")
        await server.serve()


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


async def main():
    """Run the performance dashboard."""
    metrics_collector = get_metrics_collector()
    dashboard = PerformanceDashboard(metrics_collector)
    
    try:
        await dashboard.run()
    except KeyboardInterrupt:
        logger.info("Dashboard shutdown requested")
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())