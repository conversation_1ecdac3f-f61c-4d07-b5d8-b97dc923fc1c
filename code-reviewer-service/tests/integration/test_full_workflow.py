"""
Integration tests for complete multi-agent review workflow.
Tests the full end-to-end integration between all system components.
"""

import pytest
import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from uuid import uuid4
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

from src.orchestrator.parallel_orchestrator import ParallelMultiAgentOrchestrator
from src.agents.factories import AgentFactory
from src.api.websockets.multi_agent_manager import MultiAgentWebSocketManager
from src.services.claude_service import ClaudeService
from src.services.report_generator import ReportGenerator
from src.models.context import CodebaseContext, AcceptanceCriteriaContext
from src.models.api_models import ReviewMode, AgentType, ReviewStatus
from src.config.settings import Settings


class TestFullWorkflowIntegration:
    """Integration tests for complete review workflow."""
    
    @pytest.fixture
    async def real_workspace(self):
        """Create realistic workspace with actual code files."""
        temp_dir = tempfile.mkdtemp(prefix="integration_workspace_")
        workspace = Path(temp_dir)
        
        # Create realistic project structure
        src_dir = workspace / "src"
        src_dir.mkdir()
        tests_dir = workspace / "tests"
        tests_dir.mkdir()
        docs_dir = workspace / "docs"
        docs_dir.mkdir()
        
        # Create Python files with realistic content
        (src_dir / "__init__.py").write_text("")
        (src_dir / "main.py").write_text("""
import os
import sys
from typing import Optional, List, Dict

class UserManager:
    def __init__(self, database_url: str):
        self.db_url = database_url
        self.users = {}
    
    def create_user(self, username: str, password: str, email: str) -> Optional[Dict]:
        # Potential security issue: no password validation
        if username in self.users:
            return None
        
        user_id = len(self.users) + 1
        user = {
            "id": user_id,
            "username": username,
            "password": password,  # Security issue: plain text password
            "email": email,
            "created_at": "2024-01-01"  # Should use datetime
        }
        self.users[username] = user
        return user
    
    def authenticate(self, username: str, password: str) -> bool:
        # Logic issue: vulnerable to timing attacks
        user = self.users.get(username)
        if user and user["password"] == password:
            return True
        return False
    
    def get_user_by_id(self, user_id: int):
        # Bug: potential null pointer issue
        for user in self.users.values():
            if user["id"] == user_id:
                return user
        return None  # Should handle this case properly

def main():
    manager = UserManager("sqlite:///users.db")
    # Missing error handling
    user = manager.create_user("admin", "123", "<EMAIL>")
    print(f"Created user: {user}")

if __name__ == "__main__":
    main()
        """)
        
        (src_dir / "utils.py").write_text("""
import re
from typing import List

def validate_email(email: str) -> bool:
    # Simplified regex - could be improved
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def process_data(data: List) -> List:
    # Performance issue: inefficient list processing
    result = []
    for item in data:
        if item:  # Potential issue with falsy values
            processed = str(item).upper()
            result.append(processed)
    return result

# Architectural issue: global variable
USER_CACHE = {}

def cache_user(user_id: str, user_data: dict):
    # Thread safety issue in multi-threaded environment
    USER_CACHE[user_id] = user_data
        """)
        
        # Create test files
        (tests_dir / "test_main.py").write_text("""
import unittest
from src.main import UserManager

class TestUserManager(unittest.TestCase):
    def setUp(self):
        self.manager = UserManager("test.db")
    
    def test_create_user(self):
        user = self.manager.create_user("test", "pass", "<EMAIL>")
        self.assertIsNotNone(user)
        self.assertEqual(user["username"], "test")
    
    # Missing test cases for edge conditions
    # Missing test for security vulnerabilities
        """)
        
        # Create documentation
        (docs_dir / "requirements.md").write_text("""
# User Management Requirements

## Acceptance Criteria
- AC1: Users can be created with username, password, and email
- AC2: Passwords must be securely hashed and stored
- AC3: Email validation must be implemented
- AC4: User authentication must be secure
- AC5: System must handle concurrent access safely
        """)
        
        # Create additional files to test code quality
        (workspace / "README.md").write_text("""
# User Management System
A simple user management system for testing purposes.
        """)
        
        (workspace / "requirements.txt").write_text("""
flask==2.0.1
pytest==6.2.4
        """)
        
        yield workspace
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    async def integration_orchestrator(self):
        """Create orchestrator with real components (but mocked external calls)."""
        settings = Settings()
        settings.agent_timeout = 60
        settings.max_concurrent_agents = 7
        settings.enable_circuit_breaker = True
        
        # Mock external services but keep internal logic
        with patch('src.services.claude_service.query') as mock_claude:
            mock_claude.return_value = self._create_mock_claude_response()
            
            agent_factory = AgentFactory(settings)
            websocket_manager = MultiAgentWebSocketManager()
            
            orchestrator = ParallelMultiAgentOrchestrator(
                agent_factory=agent_factory,
                websocket_manager=websocket_manager,
                settings=settings
            )
            
            yield orchestrator
    
    def _create_mock_claude_response(self):
        """Create realistic Claude response for different agents."""
        responses = {
            "acceptance_criteria": """
# Acceptance Criteria Analysis

## ✅ Implemented Criteria
- AC1: Users can be created with username, password, and email - IMPLEMENTED
- AC3: Email validation implemented in utils.py - IMPLEMENTED

## ❌ Missing Criteria  
- AC2: Passwords are stored in plain text (CRITICAL SECURITY ISSUE)
- AC4: Authentication vulnerable to timing attacks
- AC5: No thread safety for concurrent access

## Recommendations
1. Implement proper password hashing (bcrypt/scrypt)
2. Add rate limiting for authentication attempts
3. Use thread-safe data structures or database
            """,
            "bug_detection": """
# Bug Detection Analysis

## 🐛 Critical Bugs Found
1. **Plain Text Password Storage** (main.py:25)
   - Severity: CRITICAL
   - Passwords stored without hashing

2. **Null Pointer Risk** (main.py:45) 
   - get_user_by_id can return None without proper handling

## ⚠️ Logic Issues
1. **Timing Attack Vulnerability** (main.py:35)
   - Authentication comparison vulnerable to timing attacks

## Recommendations
- Implement secure password hashing
- Add proper null checks and error handling
- Use constant-time comparison for authentication
            """,
            "security_analysis": """
# Security Analysis Report

## 🔴 Critical Security Issues
1. **Plain Text Password Storage**
   - Impact: CRITICAL - All user passwords compromised if DB accessed
   - Location: main.py:25
   - Fix: Implement bcrypt/scrypt hashing

2. **Timing Attack Vulnerability** 
   - Impact: HIGH - Allows username enumeration
   - Location: main.py:35-38
   - Fix: Use constant-time comparison

## 🟡 Medium Security Issues
1. **No Input Validation**
   - Missing validation for user inputs
   - Could lead to injection attacks

## Recommendations
- Implement proper authentication security
- Add input validation and sanitization
- Use secure coding practices
            """
        }
        
        async def mock_response_generator(prompt, **kwargs):
            # Determine agent type from prompt content
            agent_type = "bug_detection"  # default
            if "acceptance criteria" in prompt.lower():
                agent_type = "acceptance_criteria"
            elif "security" in prompt.lower():
                agent_type = "security_analysis"
            
            yield responses.get(agent_type, "Analysis completed successfully")
        
        return mock_response_generator
    
    @pytest.mark.asyncio
    async def test_complete_parallel_workflow(self, real_workspace, integration_orchestrator):
        """Test complete workflow from context creation to final report."""
        
        # Create realistic execution context
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(real_workspace),
            "branch_name": "feature/user-management",
            "commit_hash": "abc123def456",
            "review_mode": ReviewMode.FULL,
            "include_context": True
        }
        
        # Execute the full workflow
        result = await integration_orchestrator.execute_review(context)
        
        # Verify orchestration succeeded
        assert result.success == True
        assert result.session_id is not None
        assert len(result.agent_results) == 7  # All 7 agents executed
        
        # Verify agent execution
        for agent_type in AgentType:
            agent_result = result.agent_results.get(agent_type.value)
            assert agent_result is not None
            assert agent_result.success == True
            assert agent_result.execution_time > 0
        
        # Verify parallel execution (should be faster than sequential)
        assert result.total_execution_time < 300  # Should complete in under 5 minutes
        
        # Verify findings aggregation
        assert "aggregated_findings" in result.metadata
        assert result.overall_recommendations is not None
        assert len(result.overall_recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_workflow_with_acceptance_criteria_context(self, real_workspace, integration_orchestrator):
        """Test workflow with acceptance criteria context integration."""
        
        # Create AC context from documentation
        ac_context = AcceptanceCriteriaContext(
            context_id=str(uuid4()),
            criteria=[
                {"id": "AC1", "description": "Users can be created", "priority": "high"},
                {"id": "AC2", "description": "Passwords must be secure", "priority": "critical"},
                {"id": "AC3", "description": "Email validation", "priority": "medium"}
            ],
            business_requirements="Secure user management system",
            user_stories=["As a user, I want secure authentication"]
        )
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(real_workspace),
            "branch_name": "feature/user-management",
            "review_mode": ReviewMode.FULL,
            "acceptance_criteria_context": ac_context,
            "include_context": True
        }
        
        result = await integration_orchestrator.execute_review(context)
        
        # Verify AC agent had proper context
        ac_result = result.agent_results.get("acceptance_criteria")
        assert ac_result is not None
        assert ac_result.success == True
        
        # Verify AC-specific findings
        assert "criteria_met" in ac_result.result_data or ac_result.findings
    
    @pytest.mark.asyncio
    async def test_workflow_error_handling(self, real_workspace, integration_orchestrator):
        """Test workflow handles individual agent errors gracefully."""
        
        # Simulate agent failure
        with patch('src.agents.factories.AgentFactory.create_agent') as mock_create:
            async def failing_agent(*args, **kwargs):
                if "security" in str(args):
                    raise Exception("Simulated security agent failure")
                return AsyncMock()
            
            mock_create.side_effect = failing_agent
            
            context = {
                "review_id": str(uuid4()),
                "workspace_path": str(real_workspace),
                "branch_name": "feature/test-errors",
                "review_mode": ReviewMode.FULL,
                "include_context": True
            }
            
            result = await integration_orchestrator.execute_review(context)
            
            # Verify orchestrator handles errors gracefully
            assert result is not None
            # Should have some successful agents even if one failed
            successful_agents = sum(1 for r in result.agent_results.values() if r.success)
            assert successful_agents > 0
    
    @pytest.mark.asyncio
    async def test_websocket_integration_during_workflow(self, real_workspace, integration_orchestrator):
        """Test WebSocket notifications during workflow execution."""
        
        websocket_events = []
        
        # Mock WebSocket manager to capture events
        original_broadcast = integration_orchestrator.websocket_manager.broadcast_agent_status
        async def mock_broadcast(*args, **kwargs):
            websocket_events.append({"type": "agent_status", "args": args, "kwargs": kwargs})
            
        integration_orchestrator.websocket_manager.broadcast_agent_status = mock_broadcast
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(real_workspace),
            "branch_name": "feature/websocket-test",
            "review_mode": ReviewMode.FULL,
            "websocket_session_id": "test-session-123"
        }
        
        await integration_orchestrator.execute_review(context)
        
        # Verify WebSocket events were sent
        assert len(websocket_events) > 0
        # Should have status updates for multiple agents
        agent_updates = [e for e in websocket_events if e["type"] == "agent_status"]
        assert len(agent_updates) >= 7  # At least one per agent
    
    @pytest.mark.asyncio
    async def test_report_generation_integration(self, real_workspace, integration_orchestrator):
        """Test report generation after workflow completion."""
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(real_workspace),
            "branch_name": "feature/report-test",
            "review_mode": ReviewMode.FULL,
            "generate_reports": True
        }
        
        result = await integration_orchestrator.execute_review(context)
        
        # Verify reports are generated
        report_generator = ReportGenerator()
        
        # Generate Phase 2 report (Bug Detection)
        phase2_report = await report_generator.generate_phase2_report(
            result.agent_results,
            context
        )
        assert phase2_report is not None
        assert len(phase2_report) > 100  # Substantial report content
        
        # Generate Phase 3 report (Summary)
        phase3_report = await report_generator.generate_phase3_report(
            result,
            context
        )
        assert phase3_report is not None
        assert "# Implementation Summary" in phase3_report or "# Summary" in phase3_report
    
    @pytest.mark.asyncio
    async def test_performance_tracking_integration(self, real_workspace, integration_orchestrator):
        """Test performance metrics collection during workflow."""
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(real_workspace),
            "branch_name": "feature/performance-test",
            "review_mode": ReviewMode.FULL,
            "track_performance": True
        }
        
        start_time = datetime.now()
        result = await integration_orchestrator.execute_review(context)
        end_time = datetime.now()
        
        # Verify performance metrics
        assert result.total_execution_time > 0
        assert result.total_execution_time < (end_time - start_time).total_seconds() + 1
        
        # Verify individual agent performance
        for agent_result in result.agent_results.values():
            assert agent_result.execution_time > 0
            assert agent_result.execution_time < result.total_execution_time
        
        # Verify performance metadata
        assert "performance_metrics" in result.metadata or hasattr(result, 'performance_metrics')
    
    @pytest.mark.asyncio 
    async def test_different_review_modes_integration(self, real_workspace, integration_orchestrator):
        """Test integration with different review modes."""
        
        review_modes = [ReviewMode.FULL, ReviewMode.QUICK, ReviewMode.AC_ONLY, ReviewMode.BUG_ANALYSIS, ReviewMode.SUMMARY_ONLY]
        
        for mode in review_modes:
            context = {
                "review_id": str(uuid4()),
                "workspace_path": str(real_workspace),
                "branch_name": f"feature/test-{mode.value}",
                "review_mode": mode,
                "include_context": True
            }
            
            result = await integration_orchestrator.execute_review(context)
            
            # All modes should succeed
            assert result.success == True
            assert result.review_mode == mode
            
            # Verify mode-specific behavior
            if mode == ReviewMode.QUICK:
                # Quick mode might skip some agents or have faster execution
                assert result.total_execution_time < 180  # Under 3 minutes
            elif mode == ReviewMode.FULL:
                # Full mode should include all agents
                assert len(result.agent_results) == 7
    
    @pytest.mark.asyncio
    async def test_concurrent_reviews_integration(self, real_workspace, integration_orchestrator):
        """Test handling multiple concurrent reviews."""
        
        # Create multiple concurrent review tasks
        review_tasks = []
        for i in range(3):
            context = {
                "review_id": str(uuid4()),
                "workspace_path": str(real_workspace),
                "branch_name": f"feature/concurrent-{i}",
                "review_mode": ReviewMode.FULL,
                "include_context": True
            }
            task = asyncio.create_task(integration_orchestrator.execute_review(context))
            review_tasks.append(task)
        
        # Wait for all reviews to complete
        results = await asyncio.gather(*review_tasks, return_exceptions=True)
        
        # Verify all reviews completed successfully
        for result in results:
            assert not isinstance(result, Exception)
            assert result.success == True
        
        # Verify each review has unique session ID
        session_ids = [r.session_id for r in results]
        assert len(set(session_ids)) == 3  # All unique