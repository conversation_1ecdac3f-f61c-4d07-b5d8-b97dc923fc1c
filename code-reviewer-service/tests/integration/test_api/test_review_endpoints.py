"""
Integration tests for Review API endpoints
Tests the complete review lifecycle with real orchestrator
"""

import pytest
import asyncio
from uuid import UUID
from datetime import datetime
from typing import Dict, Any
from unittest.mock import AsyncMock, patch, MagicMock

from fastapi.testclient import TestClient
from httpx import AsyncClient

from src.main import app
from src.models.api_models import (
    ReviewRequest, ReviewResponse, ReviewStatusResponse, ReviewResult,
    ReviewMode, AgentType, AgentStatus, ReviewStatus
)
from src.orchestrator.result_aggregator import OrchestrationResult


@pytest.fixture
def client():
    """Test client fixture"""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """Async test client fixture"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_orchestration_result():
    """Mock orchestration result for testing"""
    return OrchestrationResult(
        success=True,
        agent_results={
            "acceptance_criteria": MagicMock(
                success=True,
                execution_time=45.0,
                result_data={"criteria_met": 8, "criteria_total": 10},
                findings=[{"type": "missing_criteria", "description": "Test coverage missing"}],
                recommendations=["Add unit tests for new features"],
                confidence_score=0.85,
                metadata={"agent_version": "1.0.0"}
            ),
            "bug_detection": MagicMock(
                success=True,
                execution_time=50.0,
                result_data={"bugs_found": 3, "severity_high": 1},
                findings=[{"type": "null_pointer", "line": 42, "file": "test.py"}],
                recommendations=["Fix null pointer dereference"],
                confidence_score=0.92,
                metadata={"scan_type": "static"}
            )
        },
        summary={
            "overall_summary": "Review completed successfully",
            "total_findings": 2,
            "high_priority": 1
        },
        priority_findings=[
            {"priority": "high", "type": "bug", "description": "Null pointer dereference"}
        ],
        context_metadata={"repository": "test-repo", "branch": "feature/test"},
        performance_metrics={"total_time": 95.0, "parallel_efficiency": 0.95},
        reports={"markdown": "# Review Report\nTest results..."}
    )


class TestReviewEndpoints:
    """Test suite for review API endpoints"""
    
    def test_start_review_success(self, client):
        """Test successful review start"""
        request_data = {
            "branch_name": "feature/test-branch",
            "repository_path": "/path/to/repo",
            "review_mode": "full",
            "jira_ticket_id": "TEST-123",
            "include_summary": True
        }
        
        response = client.post("/api/v1/review/start", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "review_id" in data
        assert UUID(data["review_id"])  # Validate UUID format
        assert data["status"] == "started"
        assert "Multi-agent review started" in data["message"]
        assert data["estimated_completion_time"] > 0
        assert "websocket_url" in data
        assert "created_at" in data
    
    def test_start_review_validation_error(self, client):
        """Test review start with validation errors"""
        # Missing required fields
        request_data = {
            "branch_name": "",  # Invalid empty string
            "repository_path": "/path/to/repo"
        }
        
        response = client.post("/api/v1/review/start", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_start_review_invalid_jira_ticket(self, client):
        """Test review start with invalid JIRA ticket format"""
        request_data = {
            "branch_name": "feature/test",
            "repository_path": "/path/to/repo",
            "jira_ticket_id": "invalid-format"  # Should be ABC-123 format
        }
        
        response = client.post("/api/v1/review/start", json=request_data)
        assert response.status_code == 422  # Validation error
    
    def test_get_review_status_success(self, client):
        """Test successful review status retrieval"""
        # First start a review
        request_data = {
            "branch_name": "feature/status-test",
            "repository_path": "/path/to/repo",
            "review_mode": "quick"
        }
        
        start_response = client.post("/api/v1/review/start", json=request_data)
        review_id = start_response.json()["review_id"]
        
        # Then get status
        status_response = client.get(f"/api/v1/review/status/{review_id}")
        
        assert status_response.status_code == 200
        data = status_response.json()
        
        assert data["review_id"] == review_id
        assert data["status"] in ["started", "running", "completed", "failed"]
        assert "progress" in data
        assert "agent_statuses" in data
        assert "started_at" in data
        assert "active_agents" in data
        assert "completed_agents" in data
        assert "failed_agents" in data
    
    def test_get_review_status_not_found(self, client):
        """Test review status retrieval for non-existent review"""
        fake_uuid = "12345678-1234-5678-9012-123456789012"
        
        response = client.get(f"/api/v1/review/status/{fake_uuid}")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
    
    def test_get_review_results_not_found(self, client):
        """Test review results retrieval for non-existent review"""
        fake_uuid = "12345678-1234-5678-9012-123456789012"
        
        response = client.get(f"/api/v1/review/results/{fake_uuid}")
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()
    
    def test_cancel_review_success(self, client):
        """Test successful review cancellation"""
        # Start a review
        request_data = {
            "branch_name": "feature/cancel-test",
            "repository_path": "/path/to/repo",
            "review_mode": "full"
        }
        
        start_response = client.post("/api/v1/review/start", json=request_data)
        review_id = start_response.json()["review_id"]
        
        # Cancel the review
        cancel_response = client.delete(f"/api/v1/review/cancel/{review_id}")
        
        assert cancel_response.status_code == 200
        data = cancel_response.json()
        
        assert data["review_id"] == review_id
        assert data["status"] == "cancelled"
        assert "cancelled successfully" in data["message"]
        assert "cancelled_at" in data
        assert "cancelled_agents" in data
    
    def test_cancel_review_not_found(self, client):
        """Test cancellation of non-existent review"""
        fake_uuid = "12345678-1234-5678-9012-123456789012"
        
        response = client.delete(f"/api/v1/review/cancel/{fake_uuid}")
        assert response.status_code == 404
    
    def test_review_mode_agent_selection(self, client):
        """Test that different review modes select appropriate agents"""
        test_cases = [
            {
                "mode": "quick",
                "expected_agents": ["acceptance_criteria", "bug_detection", "code_quality"]
            },
            {
                "mode": "ac_only",
                "expected_agents": ["acceptance_criteria"]
            },
            {
                "mode": "bug_analysis",
                "expected_agents": ["bug_detection", "security_analysis", "code_quality"]
            },
            {
                "mode": "full",
                "expected_agents": [
                    "acceptance_criteria", "bug_detection", "security_analysis",
                    "logic_analysis", "code_quality", "architectural"
                ]
            }
        ]
        
        for test_case in test_cases:
            request_data = {
                "branch_name": f"feature/test-{test_case['mode']}",
                "repository_path": "/path/to/repo",
                "review_mode": test_case["mode"],
                "include_summary": False  # Don't include summary for this test
            }
            
            start_response = client.post("/api/v1/review/start", json=request_data)
            review_id = start_response.json()["review_id"]
            
            # Get status to check agent selection
            status_response = client.get(f"/api/v1/review/status/{review_id}")
            agent_statuses = status_response.json()["agent_statuses"]
            
            # Check that expected agents are not skipped
            for expected_agent in test_case["expected_agents"]:
                assert expected_agent in agent_statuses
                assert agent_statuses[expected_agent]["status"] != "skipped"
            
            # Check that other agents are skipped (except for full mode)
            if test_case["mode"] != "full":
                all_agents = [
                    "acceptance_criteria", "bug_detection", "security_analysis",
                    "logic_analysis", "code_quality", "architectural", "summary"
                ]
                for agent in all_agents:
                    if agent not in test_case["expected_agents"]:
                        if agent in agent_statuses:
                            assert agent_statuses[agent]["status"] == "skipped"


@pytest.mark.asyncio
class TestReviewEndpointsAsync:
    """Async test suite for review endpoints"""
    
    @patch('src.api.routers.review.orchestrator.orchestrate_parallel_review')
    @patch('src.api.routers.review.websocket_manager.broadcast_review_event')
    @patch('src.api.routers.review.websocket_manager.broadcast_agent_event')
    async def test_complete_review_workflow(
        self,
        mock_broadcast_agent,
        mock_broadcast_review,
        mock_orchestrate,
        async_client,
        mock_orchestration_result
    ):
        """Test complete review workflow from start to completion"""
        
        # Setup mocks
        mock_orchestrate.return_value = mock_orchestration_result
        mock_broadcast_review.return_value = None
        mock_broadcast_agent.return_value = None
        
        # Start review
        request_data = {
            "branch_name": "feature/integration-test",
            "repository_path": "/path/to/repo",
            "review_mode": "full",
            "jira_ticket_id": "TEST-456",
            "include_summary": True
        }
        
        start_response = await async_client.post("/api/v1/review/start", json=request_data)
        assert start_response.status_code == 200
        
        review_id = start_response.json()["review_id"]
        
        # Wait a moment for background task to start
        await asyncio.sleep(0.1)
        
        # Check status
        status_response = await async_client.get(f"/api/v1/review/status/{review_id}")
        assert status_response.status_code == 200
        
        # Wait for review to complete (mock should complete quickly)
        max_wait = 10  # seconds
        completed = False
        
        for _ in range(max_wait * 10):  # Check every 100ms
            status_response = await async_client.get(f"/api/v1/review/status/{review_id}")
            status_data = status_response.json()
            
            if status_data["status"] in ["completed", "failed"]:
                completed = True
                break
            
            await asyncio.sleep(0.1)
        
        assert completed, "Review did not complete within timeout"
        
        # Get final results
        results_response = await async_client.get(f"/api/v1/review/results/{review_id}")
        
        if results_response.status_code == 200:  # Results available
            results_data = results_response.json()
            
            assert results_data["review_id"] == review_id
            assert results_data["status"] in ["completed", "failed"]
            assert "execution_time" in results_data
            assert "agent_results" in results_data
            assert "overall_results" in results_data
        
        # Verify orchestrator was called
        mock_orchestrate.assert_called_once()
        
        # Verify WebSocket events were broadcasted
        assert mock_broadcast_review.call_count >= 1
    
    async def test_concurrent_reviews(self, async_client):
        """Test handling of multiple concurrent reviews"""
        
        # Start multiple reviews concurrently
        review_requests = []
        for i in range(3):
            request_data = {
                "branch_name": f"feature/concurrent-test-{i}",
                "repository_path": "/path/to/repo",
                "review_mode": "quick"
            }
            review_requests.append(request_data)
        
        # Start all reviews
        start_tasks = [
            async_client.post("/api/v1/review/start", json=req)
            for req in review_requests
        ]
        
        start_responses = await asyncio.gather(*start_tasks)
        
        # Verify all started successfully
        review_ids = []
        for response in start_responses:
            assert response.status_code == 200
            review_ids.append(response.json()["review_id"])
        
        # Verify all have unique IDs
        assert len(set(review_ids)) == len(review_ids)
        
        # Check status of all reviews
        status_tasks = [
            async_client.get(f"/api/v1/review/status/{review_id}")
            for review_id in review_ids
        ]
        
        status_responses = await asyncio.gather(*status_tasks)
        
        for response in status_responses:
            assert response.status_code == 200
            data = response.json()
            assert data["status"] in ["started", "running", "completed", "failed"]


@pytest.mark.integration
class TestReviewEndpointsIntegration:
    """Full integration tests with real components (if available)"""
    
    @pytest.mark.skip(reason="Requires real repository and JIRA setup")
    def test_real_repository_review(self, client):
        """Test with real repository (requires setup)"""
        request_data = {
            "branch_name": "main",
            "repository_path": "/path/to/real/repo",
            "review_mode": "quick",
            "jira_ticket_id": "REAL-123"
        }
        
        response = client.post("/api/v1/review/start", json=request_data)
        assert response.status_code == 200
        
        # Monitor review progress
        review_id = response.json()["review_id"]
        
        # Poll status until completion
        import time
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status_response = client.get(f"/api/v1/review/status/{review_id}")
            status_data = status_response.json()
            
            if status_data["status"] in ["completed", "failed"]:
                break
            
            time.sleep(5)
        
        # Verify final results
        results_response = client.get(f"/api/v1/review/results/{review_id}")
        if results_response.status_code == 200:
            results_data = results_response.json()
            assert "agent_results" in results_data
            assert len(results_data["agent_results"]) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])