"""
Performance tests for parallel multi-agent execution.
Tests and validates the 7x speed-up improvement from sequential to parallel execution.
"""

import pytest
import asyncio
import time
import psutil
import statistics
from datetime import datetime, timedelta
from uuid import uuid4
from unittest.mock import patch, AsyncMock
from typing import Dict, List, Any
import tempfile
import shutil
from pathlib import Path

from src.orchestrator.parallel_orchestrator import ParallelMultiAgentOrchestrator
from src.agents.factories import AgentFactory
from src.api.websockets.multi_agent_manager import MultiAgentWebSocketManager
from src.config.settings import Settings
from src.models.api_models import ReviewMode, AgentType


class PerformanceMetrics:
    """Helper class to collect performance metrics."""
    
    def __init__(self):
        self.execution_times: List[float] = []
        self.memory_usage: List[float] = []
        self.cpu_usage: List[float] = []
        self.agent_times: Dict[str, List[float]] = {}
        self.concurrent_agents: int = 0
        self.start_time: float = 0
        self.end_time: float = 0
    
    def start_measurement(self):
        """Start performance measurement."""
        self.start_time = time.time()
    
    def end_measurement(self):
        """End performance measurement."""
        self.end_time = time.time()
        self.execution_times.append(self.end_time - self.start_time)
    
    def record_agent_time(self, agent_type: str, execution_time: float):
        """Record individual agent execution time."""
        if agent_type not in self.agent_times:
            self.agent_times[agent_type] = []
        self.agent_times[agent_type].append(execution_time)
    
    def record_system_metrics(self):
        """Record current system metrics."""
        process = psutil.Process()
        self.memory_usage.append(process.memory_info().rss / 1024 / 1024)  # MB
        self.cpu_usage.append(process.cpu_percent())
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            "execution_times": {
                "min": min(self.execution_times) if self.execution_times else 0,
                "max": max(self.execution_times) if self.execution_times else 0,
                "mean": statistics.mean(self.execution_times) if self.execution_times else 0,
                "median": statistics.median(self.execution_times) if self.execution_times else 0,
                "std_dev": statistics.stdev(self.execution_times) if len(self.execution_times) > 1 else 0
            },
            "memory_usage": {
                "min": min(self.memory_usage) if self.memory_usage else 0,
                "max": max(self.memory_usage) if self.memory_usage else 0,
                "mean": statistics.mean(self.memory_usage) if self.memory_usage else 0
            },
            "cpu_usage": {
                "mean": statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                "max": max(self.cpu_usage) if self.cpu_usage else 0
            },
            "agent_times": {
                agent: {
                    "mean": statistics.mean(times),
                    "min": min(times),
                    "max": max(times)
                } for agent, times in self.agent_times.items()
            }
        }


class TestParallelPerformance:
    """Performance tests for parallel multi-agent execution."""
    
    @pytest.fixture
    def performance_metrics(self):
        """Performance metrics collector."""
        return PerformanceMetrics()
    
    @pytest.fixture
    async def performance_workspace(self):
        """Create workspace optimized for performance testing."""
        temp_dir = tempfile.mkdtemp(prefix="perf_workspace_")
        workspace = Path(temp_dir)
        
        # Create medium-sized codebase for realistic testing
        src_dir = workspace / "src"
        src_dir.mkdir()
        
        # Generate multiple files with different types of issues
        for i in range(10):
            (src_dir / f"module_{i}.py").write_text(f"""
# Module {i} - Performance test file
import time
import threading
from typing import List, Dict

class Service{i}:
    def __init__(self):
        self.cache = {{}}
        self.users = []
    
    def process_data(self, data: List) -> List:
        # Intentional performance issue for testing
        result = []
        for item in data:
            for other in data:  # O(n²) complexity
                if item.get('id') == other.get('related_id'):
                    result.append(item)
        return result
    
    def authenticate_user(self, username: str, password: str) -> bool:
        # Security issue for testing
        time.sleep(0.01)  # Simulate processing
        return username == "admin" and password == "password"
    
    def handle_request(self, request):
        # Bug for testing
        data = request['data']  # Potential KeyError
        return self.process_data(data)

# Global variable - architectural issue
GLOBAL_CACHE_{i} = {{}}
            """)
        
        # Add test files
        tests_dir = workspace / "tests"
        tests_dir.mkdir()
        for i in range(5):
            (tests_dir / f"test_module_{i}.py").write_text(f"""
import unittest
from src.module_{i} import Service{i}

class TestService{i}(unittest.TestCase):
    def test_process_data(self):
        service = Service{i}()
        # Incomplete test
        pass
            """)
        
        yield workspace
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_claude_with_timing(self):
        """Mock Claude service with configurable timing."""
        
        def create_timed_mock(base_delay: float = 0.5):
            async def mock_query(prompt, **kwargs):
                # Simulate realistic Claude API response time
                await asyncio.sleep(base_delay)
                
                # Return agent-specific response based on prompt
                if "acceptance criteria" in prompt.lower():
                    yield "# Acceptance Criteria Analysis\nAnalysis completed successfully"
                elif "bug detection" in prompt.lower():
                    yield "# Bug Detection\nFound 5 bugs in the codebase"
                elif "security" in prompt.lower():
                    yield "# Security Analysis\nFound 3 security vulnerabilities"
                elif "performance" in prompt.lower():
                    yield "# Performance Analysis\nFound performance bottlenecks"
                elif "code quality" in prompt.lower():
                    yield "# Code Quality\nCode quality issues identified"
                elif "architectural" in prompt.lower():
                    yield "# Architectural Analysis\nArchitectural improvements needed"
                elif "summary" in prompt.lower():
                    yield "# Summary\nReview completed successfully"
                else:
                    yield "# Analysis\nGeneric analysis completed"
            
            return mock_query
        
        return create_timed_mock
    
    @pytest.mark.asyncio
    async def test_parallel_vs_sequential_performance(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test performance improvement of parallel vs sequential execution."""
        
        settings = Settings()
        settings.agent_timeout = 120
        settings.max_concurrent_agents = 7
        
        agent_factory = AgentFactory(settings)
        websocket_manager = MultiAgentWebSocketManager()
        
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=agent_factory,
            websocket_manager=websocket_manager,
            settings=settings
        )
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(performance_workspace),
            "branch_name": "feature/performance-test",
            "review_mode": ReviewMode.PARALLEL,
            "include_context": True
        }
        
        # Test with fast mock (0.1s per agent)
        with patch('src.services.claude_service.query', mock_claude_with_timing(0.1)):
            
            # Measure parallel execution
            performance_metrics.start_measurement()
            performance_metrics.record_system_metrics()
            
            parallel_result = await orchestrator.execute_review(context)
            
            performance_metrics.end_measurement()
            performance_metrics.record_system_metrics()
            
            parallel_time = performance_metrics.execution_times[-1]
            
            # Verify parallel execution
            assert parallel_result.success == True
            assert len(parallel_result.agent_results) == 7
            
            # With 0.1s per agent, parallel should be close to 0.1s + overhead
            # Sequential would be 7 * 0.1 = 0.7s
            assert parallel_time < 0.5, f"Parallel execution too slow: {parallel_time}s"
            
            # Calculate theoretical sequential time
            agent_times = [result.execution_time for result in parallel_result.agent_results.values()]
            theoretical_sequential_time = sum(agent_times)
            
            # Verify speed improvement
            speedup = theoretical_sequential_time / parallel_time
            assert speedup > 3.0, f"Speed improvement insufficient: {speedup}x"
            
            print(f"Parallel execution: {parallel_time:.2f}s")
            print(f"Theoretical sequential: {theoretical_sequential_time:.2f}s") 
            print(f"Speed improvement: {speedup:.1f}x")
    
    @pytest.mark.asyncio
    async def test_realistic_performance_benchmark(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test performance with realistic Claude API response times."""
        
        settings = Settings()
        agent_factory = AgentFactory(settings)
        websocket_manager = MultiAgentWebSocketManager()
        
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=agent_factory,
            websocket_manager=websocket_manager,
            settings=settings
        )
        
        # Test with realistic timing (2-3 seconds per agent)
        realistic_delays = [2.0, 2.5, 3.0, 2.2, 2.8, 2.1, 2.6]  # Different for each agent
        
        async def realistic_mock_query(prompt, **kwargs):
            # Use different delays based on agent type
            agent_delays = {
                "acceptance": 2.0, "bug": 2.5, "security": 3.0,
                "performance": 2.2, "quality": 2.8, "architectural": 2.1, "summary": 2.6
            }
            
            delay = 2.5  # default
            for agent_type, agent_delay in agent_delays.items():
                if agent_type in prompt.lower():
                    delay = agent_delay
                    break
            
            await asyncio.sleep(delay)
            yield f"# Analysis completed in {delay}s"
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(performance_workspace),
            "branch_name": "feature/realistic-performance",
            "review_mode": ReviewMode.PARALLEL
        }
        
        with patch('src.services.claude_service.query', realistic_mock_query):
            
            performance_metrics.start_measurement()
            
            result = await orchestrator.execute_review(context)
            
            performance_metrics.end_measurement()
            
            execution_time = performance_metrics.execution_times[-1]
            
            # Verify results
            assert result.success == True
            assert len(result.agent_results) == 7
            
            # With realistic delays, parallel should complete in ~3s (max delay + overhead)
            # Sequential would be sum of all delays (~17s)
            assert execution_time < 5.0, f"Realistic parallel execution too slow: {execution_time}s"
            
            # Calculate actual speedup
            agent_times = [result.execution_time for result in result.agent_results.values()]
            sequential_time = sum(agent_times)
            speedup = sequential_time / execution_time
            
            # Should achieve significant speedup
            assert speedup > 5.0, f"Realistic speedup insufficient: {speedup:.1f}x"
            
            print(f"Realistic parallel: {execution_time:.2f}s")
            print(f"Would be sequential: {sequential_time:.2f}s")
            print(f"Realistic speedup: {speedup:.1f}x")
    
    @pytest.mark.asyncio
    async def test_memory_usage_performance(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test memory usage during parallel execution."""
        
        settings = Settings()
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=AgentFactory(settings),
            websocket_manager=MultiAgentWebSocketManager(),
            settings=settings
        )
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(performance_workspace),
            "branch_name": "feature/memory-test",
            "review_mode": ReviewMode.PARALLEL
        }
        
        # Monitor memory usage during execution
        memory_measurements = []
        
        async def memory_monitoring_task():
            while True:
                process = psutil.Process()
                memory_mb = process.memory_info().rss / 1024 / 1024
                memory_measurements.append(memory_mb)
                await asyncio.sleep(0.1)
        
        with patch('src.services.claude_service.query', mock_claude_with_timing(0.5)):
            
            # Start memory monitoring
            monitor_task = asyncio.create_task(memory_monitoring_task())
            
            try:
                # Execute review
                result = await orchestrator.execute_review(context)
                
                # Stop monitoring
                monitor_task.cancel()
                
                # Analyze memory usage
                initial_memory = memory_measurements[0] if memory_measurements else 0
                peak_memory = max(memory_measurements) if memory_measurements else 0
                final_memory = memory_measurements[-1] if memory_measurements else 0
                
                memory_increase = peak_memory - initial_memory
                
                # Verify reasonable memory usage
                assert memory_increase < 500, f"Memory usage too high: {memory_increase}MB"
                
                # Memory should return to reasonable level after completion
                memory_cleanup = peak_memory - final_memory
                assert memory_cleanup > memory_increase * 0.5, "Poor memory cleanup"
                
                print(f"Memory usage - Initial: {initial_memory:.1f}MB, Peak: {peak_memory:.1f}MB, Final: {final_memory:.1f}MB")
                print(f"Memory increase: {memory_increase:.1f}MB")
                
            except asyncio.CancelledError:
                pass
            finally:
                if not monitor_task.done():
                    monitor_task.cancel()
    
    @pytest.mark.asyncio
    async def test_concurrent_reviews_performance(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test performance with multiple concurrent reviews."""
        
        settings = Settings()
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=AgentFactory(settings),
            websocket_manager=MultiAgentWebSocketManager(),
            settings=settings
        )
        
        # Create multiple concurrent review contexts
        contexts = []
        for i in range(3):
            contexts.append({
                "review_id": str(uuid4()),
                "workspace_path": str(performance_workspace),
                "branch_name": f"feature/concurrent-{i}",
                "review_mode": ReviewMode.PARALLEL
            })
        
        with patch('src.services.claude_service.query', mock_claude_with_timing(1.0)):
            
            performance_metrics.start_measurement()
            
            # Execute concurrent reviews
            tasks = [orchestrator.execute_review(context) for context in contexts]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            performance_metrics.end_measurement()
            
            concurrent_time = performance_metrics.execution_times[-1]
            
            # Verify all reviews completed successfully
            successful_results = [r for r in results if not isinstance(r, Exception)]
            assert len(successful_results) == 3
            
            for result in successful_results:
                assert result.success == True
                assert len(result.agent_results) == 7
            
            # Concurrent execution should not be much slower than single review
            # Should be significantly faster than sequential execution of 3 reviews
            assert concurrent_time < 5.0, f"Concurrent execution too slow: {concurrent_time}s"
            
            print(f"3 concurrent reviews completed in: {concurrent_time:.2f}s")
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test performance under high load conditions."""
        
        settings = Settings()
        settings.max_concurrent_agents = 7
        
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=AgentFactory(settings),
            websocket_manager=MultiAgentWebSocketManager(),
            settings=settings
        )
        
        # Test with varying loads
        load_tests = [1, 2, 3]  # Number of concurrent reviews
        results = {}
        
        for load in load_tests:
            contexts = []
            for i in range(load):
                contexts.append({
                    "review_id": str(uuid4()),
                    "workspace_path": str(performance_workspace),
                    "branch_name": f"feature/load-test-{load}-{i}",
                    "review_mode": ReviewMode.PARALLEL
                })
            
            with patch('src.services.claude_service.query', mock_claude_with_timing(0.8)):
                
                start_time = time.time()
                
                tasks = [orchestrator.execute_review(context) for context in contexts]
                load_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                # Verify all completed successfully
                successful = [r for r in load_results if not isinstance(r, Exception)]
                assert len(successful) == load
                
                results[load] = {
                    "execution_time": execution_time,
                    "reviews_per_second": load / execution_time,
                    "successful_reviews": len(successful)
                }
                
                print(f"Load {load}: {execution_time:.2f}s, {results[load]['reviews_per_second']:.2f} reviews/sec")
        
        # Verify performance scaling
        # Performance should not degrade dramatically with increased load
        single_review_time = results[1]["execution_time"]
        triple_review_time = results[3]["execution_time"]
        
        # Triple load should not take more than 3x time (good parallelization)
        assert triple_review_time < single_review_time * 4, f"Poor scaling: 3x load took {triple_review_time/single_review_time:.1f}x time"
    
    @pytest.mark.asyncio
    async def test_agent_timeout_performance(self, performance_workspace, performance_metrics):
        """Test performance when agents timeout."""
        
        settings = Settings()
        settings.agent_timeout = 2  # Short timeout for testing
        
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=AgentFactory(settings),
            websocket_manager=MultiAgentWebSocketManager(),
            settings=settings
        )
        
        # Mock with long delay to trigger timeout
        async def slow_mock_query(*args, **kwargs):
            await asyncio.sleep(5)  # Longer than timeout
            yield "This should timeout"
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(performance_workspace),
            "branch_name": "feature/timeout-test",
            "review_mode": ReviewMode.PARALLEL
        }
        
        with patch('src.services.claude_service.query', slow_mock_query):
            
            performance_metrics.start_measurement()
            
            result = await orchestrator.execute_review(context)
            
            performance_metrics.end_measurement()
            
            execution_time = performance_metrics.execution_times[-1]
            
            # Should complete quickly due to timeouts, not wait for all agents
            assert execution_time < 10, f"Timeout handling too slow: {execution_time}s"
            
            # Should handle timeouts gracefully
            assert result is not None
            
            # Some agents may have timed out
            timed_out_agents = sum(1 for r in result.agent_results.values() if not r.success)
            print(f"Agents timed out: {timed_out_agents}/7")
            print(f"Timeout handling completed in: {execution_time:.2f}s")
    
    @pytest.mark.asyncio
    async def test_websocket_performance_impact(self, performance_workspace, mock_claude_with_timing, performance_metrics):
        """Test performance impact of WebSocket notifications."""
        
        settings = Settings()
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=AgentFactory(settings),
            websocket_manager=MultiAgentWebSocketManager(),
            settings=settings
        )
        
        context = {
            "review_id": str(uuid4()),
            "workspace_path": str(performance_workspace),
            "branch_name": "feature/websocket-performance",
            "review_mode": ReviewMode.PARALLEL,
            "websocket_session_id": str(uuid4())
        }
        
        websocket_events = []
        
        # Mock WebSocket broadcasting with timing
        async def timed_broadcast(*args, **kwargs):
            start = time.time()
            # Simulate network delay
            await asyncio.sleep(0.01)
            websocket_events.append(time.time() - start)
        
        with patch('src.services.claude_service.query', mock_claude_with_timing(0.5)):
            with patch.object(orchestrator.websocket_manager, 'broadcast_agent_status', timed_broadcast):
                
                performance_metrics.start_measurement()
                
                result = await orchestrator.execute_review(context)
                
                performance_metrics.end_measurement()
                
                execution_time = performance_metrics.execution_times[-1]
                
                # Verify WebSocket events occurred
                assert len(websocket_events) > 0
                
                # WebSocket overhead should be minimal
                total_websocket_time = sum(websocket_events)
                websocket_overhead = (total_websocket_time / execution_time) * 100
                
                assert websocket_overhead < 10, f"WebSocket overhead too high: {websocket_overhead:.1f}%"
                
                print(f"WebSocket events: {len(websocket_events)}")
                print(f"WebSocket overhead: {websocket_overhead:.2f}%")
    
    def test_performance_metrics_collection(self, performance_metrics):
        """Test performance metrics collection functionality."""
        
        # Test basic metrics collection
        performance_metrics.start_measurement()
        time.sleep(0.1)
        performance_metrics.end_measurement()
        
        performance_metrics.record_agent_time("test_agent", 0.05)
        performance_metrics.record_system_metrics()
        
        stats = performance_metrics.get_statistics()
        
        # Verify metrics structure
        assert "execution_times" in stats
        assert "memory_usage" in stats
        assert "cpu_usage" in stats
        assert "agent_times" in stats
        
        # Verify execution time recorded
        assert len(performance_metrics.execution_times) == 1
        assert performance_metrics.execution_times[0] >= 0.1
        
        # Verify agent time recorded
        assert "test_agent" in stats["agent_times"]
        assert stats["agent_times"]["test_agent"]["mean"] == 0.05