"""
Shared test fixtures and configuration for unit tests.
Provides common mocks, fixtures and utilities for all unit tests.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.agents.models import (
    AgentResult, MultiAgentResult, AgentExecutionStatus,
    AgentType, ReviewMode, ReviewStatus
)
from src.models.context import (
    BaseContext, CodebaseContext, AcceptanceCriteriaContext,
    SecurityContext, PerformanceContext
)
from src.orchestrator.parallel_orchestrator import ParallelMultiAgentOrchestrator
from src.agents.factories import AgentFactory
from src.api.websockets.multi_agent_manager import MultiAgentWebSocketManager
from src.config.settings import Settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_workspace():
    """Create temporary workspace directory for tests."""
    temp_dir = tempfile.mkdtemp(prefix="test_workspace_")
    workspace_path = Path(temp_dir)
    
    # Create typical project structure
    (workspace_path / "src").mkdir()
    (workspace_path / "tests").mkdir()
    (workspace_path / "docs").mkdir()
    
    # Create sample files
    (workspace_path / "src" / "main.py").write_text("# Sample main file\nprint('Hello World')")
    (workspace_path / "tests" / "test_main.py").write_text("# Sample test file\nimport unittest")
    (workspace_path / "README.md").write_text("# Test Project\nThis is a test project.")
    
    yield workspace_path
    
    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def mock_settings():
    """Create mock settings with realistic configuration."""
    settings = MagicMock(spec=Settings)
    settings.agent_timeout = 300
    settings.max_concurrent_agents = 7
    settings.enable_circuit_breaker = True
    settings.circuit_breaker_failure_threshold = 3
    settings.circuit_breaker_recovery_timeout = 60
    settings.performance_monitoring = True
    settings.websocket_enabled = True
    settings.claude_api_key = "test-api-key"
    settings.review_output_format = "markdown"
    return settings


@pytest.fixture
def mock_agent_factory():
    """Create mock agent factory with typical behavior."""
    factory = AsyncMock(spec=AgentFactory)
    factory.health_check.return_value = {
        "factory_status": "healthy",
        "available_agents": 7,
        "sdk_connection": "active"
    }
    return factory


@pytest.fixture
def mock_websocket_manager():
    """Create mock WebSocket manager."""
    manager = AsyncMock(spec=MultiAgentWebSocketManager)
    manager.get_connection_count.return_value = 0
    manager.broadcast_agent_status = AsyncMock()
    manager.broadcast_orchestration_status = AsyncMock()
    return manager


@pytest.fixture
def sample_codebase_context(temp_workspace):
    """Create sample codebase context for testing."""
    return CodebaseContext(
        context_id=str(uuid4()),
        workspace_path=temp_workspace,
        branch_name="feature/test-branch",
        commit_hash="abc123def456",
        files_changed=["src/main.py", "tests/test_main.py"],
        total_lines=150,
        programming_languages=["python"]
    )


@pytest.fixture
def sample_ac_context():
    """Create sample acceptance criteria context."""
    return AcceptanceCriteriaContext(
        context_id=str(uuid4()),
        criteria=[
            {"id": "AC1", "description": "User can login with email", "priority": "high"},
            {"id": "AC2", "description": "Password must be secure", "priority": "medium"},
            {"id": "AC3", "description": "Remember me functionality", "priority": "low"}
        ],
        business_requirements="Implement secure user authentication",
        user_stories=["As a user, I want to login securely"]
    )


@pytest.fixture
def sample_security_context():
    """Create sample security context."""
    return SecurityContext(
        context_id=str(uuid4()),
        security_requirements=["authentication", "authorization", "input_validation"],
        compliance_standards=["OWASP", "GDPR"],
        threat_model={"sql_injection": "high", "xss": "medium"}
    )


@pytest.fixture
def mock_agent_result():
    """Create mock agent result with typical data."""
    return AgentResult(
        agent_type=AgentType.BUG_DETECTION,
        success=True,
        execution_time=45.2,
        result_data={
            "bugs_found": 3,
            "critical_issues": 1,
            "warnings": 5
        },
        findings=[
            {
                "type": "null_pointer_exception",
                "severity": "high",
                "file": "src/main.py",
                "line": 42,
                "description": "Potential null pointer exception"
            }
        ],
        recommendations=[
            "Add null checks before object access",
            "Implement proper error handling"
        ],
        confidence_score=0.87,
        metadata={
            "agent_version": "1.0.0",
            "execution_mode": "parallel"
        }
    )


@pytest.fixture
def sample_multi_agent_result(mock_agent_result):
    """Create sample multi-agent result with all 7 agents."""
    agent_results = {}
    
    for agent_type in AgentType:
        result = AgentResult(
            agent_type=agent_type,
            success=True,
            execution_time=30.0 + (hash(agent_type.value) % 30),  # Vary execution times
            result_data={"issues_found": hash(agent_type.value) % 10},
            findings=[f"Sample finding for {agent_type.value}"],
            recommendations=[f"Sample recommendation for {agent_type.value}"],
            confidence_score=0.8 + (hash(agent_type.value) % 20) / 100,
            metadata={"agent_version": "1.0.0"}
        )
        agent_results[agent_type.value] = result
    
    return MultiAgentResult(
        session_id=str(uuid4()),
        review_mode=ReviewMode.PARALLEL,
        overall_success=True,
        total_execution_time=125.5,
        agent_results=agent_results,
        aggregated_findings={
            "critical": 2,
            "high": 5,
            "medium": 8,
            "low": 12
        },
        overall_recommendations=[
            "Focus on critical security issues first",
            "Improve test coverage to 90%+",
            "Refactor complex methods for better maintainability"
        ],
        quality_metrics={
            "code_quality_score": 0.82,
            "security_score": 0.78,
            "performance_score": 0.85
        },
        metadata={
            "orchestrator_version": "1.0.0",
            "execution_mode": "parallel"
        }
    )


@pytest.fixture
def mock_claude_service_response():
    """Mock typical Claude service response."""
    return {
        "success": True,
        "response": "Detailed analysis completed successfully",
        "metadata": {
            "tokens_used": 1500,
            "response_time": 2.3,
            "model": "claude-3-sonnet"
        }
    }


@pytest.fixture
def orchestrator_with_mocks(mock_agent_factory, mock_websocket_manager, mock_settings):
    """Create orchestrator instance with all mocks."""
    return ParallelMultiAgentOrchestrator(
        agent_factory=mock_agent_factory,
        websocket_manager=mock_websocket_manager,
        settings=mock_settings
    )


@pytest.fixture
def sample_review_request():
    """Create sample review request data."""
    return {
        "review_id": str(uuid4()),
        "branch_name": "feature/test-implementation",
        "workspace_path": "/tmp/test-workspace",
        "review_mode": ReviewMode.PARALLEL,
        "include_context": True,
        "websocket_session_id": str(uuid4())
    }


# Performance test fixtures
@pytest.fixture
def performance_metrics_collector():
    """Collector for performance metrics during tests."""
    metrics = {
        "execution_times": [],
        "memory_usage": [],
        "cpu_usage": [],
        "api_calls": []
    }
    return metrics


@pytest.fixture
def mock_parallel_execution_context():
    """Mock context for parallel execution testing."""
    return {
        "agent_count": 7,
        "expected_speedup": 7.0,
        "timeout_per_agent": 300,
        "total_timeout": 600,
        "resource_limits": {
            "max_memory_mb": 2048,
            "max_cpu_percent": 80
        }
    }


# Async test utilities
@pytest.fixture
def async_test_timeout():
    """Standard timeout for async tests."""
    return 30.0  # 30 seconds


# Error simulation fixtures
@pytest.fixture
def agent_failure_scenarios():
    """Different agent failure scenarios for testing."""
    return {
        "timeout": {"agent": "security_analysis", "error": "TimeoutError"},
        "api_limit": {"agent": "bug_detection", "error": "RateLimitError"},
        "network": {"agent": "code_quality", "error": "NetworkError"},
        "validation": {"agent": "acceptance_criteria", "error": "ValidationError"}
    }


@pytest.fixture(autouse=True)
def cleanup_async_tasks():
    """Cleanup any remaining async tasks after each test."""
    yield
    
    # Cancel any remaining tasks
    try:
        loop = asyncio.get_running_loop()
        tasks = [t for t in asyncio.all_tasks(loop) if not t.done()]
        if tasks:
            for task in tasks:
                task.cancel()
            asyncio.gather(*tasks, return_exceptions=True)
    except RuntimeError:
        # No running loop
        pass


# Mock patches for common external dependencies
@pytest.fixture
def mock_claude_sdk():
    """Mock the Claude SDK calls."""
    with patch('src.services.claude_service.query') as mock_query:
        mock_query.return_value = AsyncMock()
        yield mock_query


@pytest.fixture
def mock_file_operations():
    """Mock file system operations."""
    with patch('pathlib.Path.exists', return_value=True), \
         patch('pathlib.Path.is_file', return_value=True), \
         patch('pathlib.Path.read_text', return_value="mock file content"):
        yield


@pytest.fixture
def mock_git_operations():
    """Mock git operations."""
    with patch('subprocess.run') as mock_run:
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="mock git output",
            stderr=""
        )
        yield mock_run