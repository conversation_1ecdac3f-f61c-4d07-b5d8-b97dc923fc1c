"""
Unit tests for AcceptanceCriteriaAgent

Tests the first concrete agent implementation for acceptance criteria validation.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
from typing import Dict, Any

from src.agents.strategies.acceptance_criteria_strategy import AcceptanceCriteriaAgent
from src.agents.models import AgentR<PERSON>ult, AgentExecutionStatus
from src.services.claude_service import ClaudeService
from src.config.settings import Settings


class TestAcceptanceCriteriaAgent:
    """Test suite for AcceptanceCriteriaAgent."""
    
    @pytest.fixture
    def mock_claude_service(self):
        """Create mock Claude service."""
        service = AsyncMock(spec=ClaudeService)
        service.health_check.return_value = True
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock(spec=Settings)
        settings.claude_api_key = "test-key"
        return settings
    
    @pytest.fixture
    def sample_context(self, tmp_path):
        """Create sample analysis context."""
        return {
            "working_path": str(tmp_path),
            "git_branch": "feature/CMS20-1234-user-auth",
            "jira_ticket_id": "CMS20-1234",
            "acceptance_criteria": [
                {
                    "id": "AC1",
                    "description": "User should be able to login with email and password",
                    "priority": "high",
                    "category": "authentication"
                },
                {
                    "id": "AC2", 
                    "description": "System should validate password strength",
                    "priority": "medium",
                    "category": "security"
                }
            ]
        }
    
    @pytest.fixture
    def acceptance_criteria_agent(self, mock_claude_service, mock_settings):
        """Create AcceptanceCriteriaAgent instance."""
        return AcceptanceCriteriaAgent(
            agent_id="ac_agent_test",
            agent_type="acceptance_criteria",
            claude_service=mock_claude_service,
            settings=mock_settings,
            timeout_seconds=60
        )
    
    def test_agent_initialization(self, acceptance_criteria_agent):
        """Test agent initialization."""
        agent = acceptance_criteria_agent
        
        assert agent.agent_id == "ac_agent_test"
        assert agent.agent_type == "acceptance_criteria"
        assert agent.timeout_seconds == 60
        assert "requirement_compliance" in agent.focus_areas
        assert "acceptance_testing" in agent.focus_areas
        assert len(agent.jira_ticket_patterns) > 0
    
    def test_extract_ticket_id_from_branch(self, acceptance_criteria_agent):
        """Test Jira ticket ID extraction from branch names."""
        agent = acceptance_criteria_agent
        
        # Test various branch patterns
        test_cases = [
            ("feature/CMS20-1234-description", "CMS20-1234"),
            ("bugfix/PROJ-567-fix", "PROJ-567"),
            ("CMS20-1234", "CMS20-1234"),
            ("feature/no-ticket-here", None),
            ("", None)
        ]
        
        for branch_name, expected in test_cases:
            result = agent._extract_ticket_id_from_branch(branch_name)
            assert result == expected, f"Failed for branch: {branch_name}"
    
    @pytest.mark.asyncio
    async def test_extract_jira_context(self, acceptance_criteria_agent, sample_context, tmp_path):
        """Test Jira context extraction."""
        agent = acceptance_criteria_agent
        
        # Test with context containing Jira info
        jira_info = await agent._extract_jira_context(sample_context)
        
        assert "ticket_id" in jira_info
        assert jira_info["ticket_id"] == "CMS20-1234"
        
        # Test with ticket.md file
        ticket_file = tmp_path / "ticket.md"
        ticket_file.write_text("""
# CMS20-1234: User Authentication

## Description
Implement user authentication system

## Acceptance Criteria
- AC1: User login functionality
- AC2: Password validation
        """)
        
        jira_info = await agent._extract_jira_context(sample_context)
        assert "ticket_content" in jira_info
        assert "User Authentication" in jira_info["ticket_content"]
    
    def test_parse_criteria_string(self, acceptance_criteria_agent):
        """Test parsing acceptance criteria from string format."""
        agent = acceptance_criteria_agent
        
        criteria_string = """
1. User should be able to login
2. System should validate passwords
- Password must be 8+ characters
- Must contain special characters
        """
        
        parsed = agent._parse_criteria_string(criteria_string)
        
        assert len(parsed) >= 2
        assert any("login" in ac["description"] for ac in parsed)
        assert any("validate passwords" in ac["description"] for ac in parsed)
        assert all("id" in ac for ac in parsed)
        assert all("priority" in ac for ac in parsed)
    
    def test_parse_criteria_from_ticket_content(self, acceptance_criteria_agent):
        """Test parsing AC from ticket content."""
        agent = acceptance_criteria_agent
        
        ticket_content = """
# Ticket: User Authentication

## Description
Implement authentication system

## Acceptance Criteria

1. User login with email/password
2. Password strength validation
3. Session management

## Technical Notes
Implementation details...
        """
        
        parsed = agent._parse_criteria_from_ticket_content(ticket_content)
        
        assert len(parsed) >= 3
        assert any("login" in ac["description"] for ac in parsed)
        assert any("validation" in ac["description"] for ac in parsed)
        assert any("session" in ac["description"] for ac in parsed)
    
    @pytest.mark.asyncio
    async def test_get_acceptance_criteria(self, acceptance_criteria_agent, sample_context):
        """Test acceptance criteria extraction from various sources."""
        agent = acceptance_criteria_agent
        
        # Mock Jira info
        jira_info = {
            "ticket_id": "CMS20-1234",
            "acceptance_criteria": ["Additional AC from Jira"]
        }
        
        criteria = await agent._get_acceptance_criteria(sample_context, jira_info)
        
        # Should include criteria from context and Jira
        assert len(criteria) >= 2
        
        # Check standardization
        for ac in criteria:
            assert "id" in ac
            assert "description" in ac
            assert "priority" in ac
            assert "category" in ac
    
    def test_get_system_prompt(self, acceptance_criteria_agent):
        """Test system prompt generation."""
        agent = acceptance_criteria_agent
        
        system_prompt = agent.get_system_prompt()
        
        assert "Acceptance Criteria Validation" in system_prompt
        assert "AC-Compliance Analyse" in system_prompt
        assert "Business Logic Validation" in system_prompt
        assert "umsetzbare Empfehlungen" in system_prompt
    
    def test_get_prompt(self, acceptance_criteria_agent, sample_context):
        """Test prompt generation with context."""
        agent = acceptance_criteria_agent
        
        # Prepare context with AC and Jira info
        context = {
            **sample_context,
            "jira_info": {"ticket_id": "CMS20-1234", "summary": "User Auth"},
            "acceptance_criteria": sample_context["acceptance_criteria"]
        }
        
        prompt = agent.get_prompt(context)
        
        # Check German AC prompt structure
        assert "fokussierte Acceptance Criteria Review" in prompt
        assert "Read tool" in prompt
        assert "ANALYSE-STRUKTUR" in prompt
        assert "AC MAPPING & VALIDATION" in prompt
        
        # Check context injection
        assert "CMS20-1234" in prompt
        assert "User should be able to login" in prompt
        assert str(sample_context["working_path"]) in prompt
    
    def test_format_context_for_prompt(self, acceptance_criteria_agent, sample_context):
        """Test context formatting for prompt."""
        agent = acceptance_criteria_agent
        
        context = {
            **sample_context,
            "jira_info": {"ticket_id": "CMS20-1234", "summary": "User Auth"},
            "acceptance_criteria": sample_context["acceptance_criteria"]
        }
        
        formatted = agent._format_context_for_prompt(context)
        
        assert "TICKET KONTEXT" in formatted
        assert "ACCEPTANCE CRITERIA ZU VALIDIEREN" in formatted
        assert "ARBEITSVERZEICHNIS" in formatted
        assert "ANALYSE-FOKUS" in formatted
        assert "CMS20-1234" in formatted
        assert "AC1" in formatted
        assert "AC2" in formatted
    
    def test_extract_ac_status_from_response(self, acceptance_criteria_agent):
        """Test AC status extraction from Claude response."""
        agent = acceptance_criteria_agent
        
        # Test response with status indicators
        response = """
        ## AC-ANALYSE
        
        ### ✅ AC1: User login functionality
        **Status**: VOLLSTÄNDIG ERFÜLLT
        Implementation found at auth.service.ts:45-67
        
        ### ⚠️ AC2: Password validation  
        **Status**: TEILWEISE ERFÜLLT
        Basic validation exists but missing strength check
        
        ### ❌ AC3: Session management
        **Status**: NICHT ERFÜLLT
        No session handling found
        """
        
        # Test different status extractions
        assert agent._extract_ac_status_from_response(response, "AC1", "login") == "fully_implemented"
        assert agent._extract_ac_status_from_response(response, "AC2", "validation") == "partially_implemented"
        assert agent._extract_ac_status_from_response(response, "AC3", "session") == "not_implemented"
        assert agent._extract_ac_status_from_response(response, "AC99", "missing") == "not_implemented"
    
    def test_extract_issues_from_response(self, acceptance_criteria_agent):
        """Test issue extraction from Claude response."""
        agent = acceptance_criteria_agent
        
        response = """
        ## Probleme
        
        🔴 Kritisch: No authentication middleware found
        🟠 Wichtig: Password strength validation missing  
        ⚠️ Problem: Session timeout not configured
        ❌ Fehlt: Multi-factor authentication
        """
        
        issues = agent._extract_issues_from_response(response)
        
        assert len(issues) > 0
        assert any("authentication" in issue["description"] for issue in issues)
        assert any("password" in issue["description"].lower() for issue in issues)
        
        # Check issue structure
        for issue in issues:
            assert "type" in issue
            assert "severity" in issue
            assert "description" in issue
            assert "category" in issue
    
    @pytest.mark.asyncio
    async def test_process_claude_response(self, acceptance_criteria_agent, sample_context):
        """Test processing of Claude response into structured results."""
        agent = acceptance_criteria_agent
        
        # Mock Claude response
        claude_response = """
        ## AC-ANALYSE
        
        ### ✅ AC1: User should be able to login with email and password
        **Status**: VOLLSTÄNDIG ERFÜLLT
        Implementation at auth.service.ts:45-67
        
        ### ⚠️ AC2: System should validate password strength
        **Status**: TEILWEISE ERFÜLLT
        Basic validation exists but strength check missing
        """
        
        acceptance_criteria = sample_context["acceptance_criteria"]
        
        results = await agent._process_claude_response(claude_response, acceptance_criteria)
        
        # Check result structure
        assert "compliance_rate" in results
        assert "fully_implemented" in results
        assert "partially_implemented" in results
        assert "not_implemented" in results
        assert "issues" in results
        
        # Check compliance calculation
        assert results["compliance_rate"] > 0
        assert len(results["fully_implemented"]) > 0
        assert len(results["partially_implemented"]) >= 0
    
    @pytest.mark.asyncio
    async def test_analyze_success(self, acceptance_criteria_agent, sample_context):
        """Test successful analysis execution."""
        agent = acceptance_criteria_agent
        
        # Mock Claude service response
        mock_response = """
        ## 🎯 AC COMPLIANCE EXECUTIVE SUMMARY
        
        **Ticket**: CMS20-1234 - User Authentication
        **AC Compliance Rate**: 2/2 (100%)
        **Business Impact**: High
        **Ready for Production**: Yes
        
        ### ✅ AC1: User should be able to login with email and password
        **Status**: VOLLSTÄNDIG ERFÜLLT
        Implementation found at auth.service.ts:23-45
        
        ### ✅ AC2: System should validate password strength  
        **Status**: VOLLSTÄNDIG ERFÜLLT
        Password validation at validators.ts:67-89
        """
        
        agent.claude_service.execute_agent_query.return_value = mock_response
        
        result = await agent.analyze(sample_context)
        
        # Check result structure
        assert isinstance(result, AgentResult)
        assert result.agent_id == "ac_agent_test"
        assert result.agent_type == "acceptance_criteria"
        assert result.success == True
        assert result.raw_output == mock_response
        assert "structured_output" in result.__dict__
        assert "result_data" in result.__dict__
        
        # Check result data
        assert "acceptance_criteria_count" in result.result_data
        assert "compliance_rate" in result.result_data
        assert result.result_data["acceptance_criteria_count"] == 2
        
        # Check quality metrics
        assert result.quality_metrics is not None
        assert "compliance_score" in result.quality_metrics
    
    @pytest.mark.asyncio
    async def test_analyze_failure(self, acceptance_criteria_agent, sample_context):
        """Test analysis failure handling."""
        agent = acceptance_criteria_agent
        
        # Mock Claude service failure
        agent.claude_service.execute_agent_query.side_effect = Exception("Claude API error")
        
        result = await agent.analyze(sample_context)
        
        # Check failure handling
        assert isinstance(result, AgentResult)
        assert result.success == False
        assert result.status == AgentExecutionStatus.FAILED
        assert "Claude API error" in result.error_message
        assert result.exception_type == "Exception"
    
    def test_configure(self, acceptance_criteria_agent):
        """Test agent configuration."""
        agent = acceptance_criteria_agent
        
        # Test configuration update
        config = {
            "focus_areas": ["custom_focus", "test_area"],
            "jira_patterns": [r"CUSTOM-\d+"]
        }
        
        agent.configure(config)
        
        assert agent.focus_areas == ["custom_focus", "test_area"]
        assert agent.jira_ticket_patterns == [r"CUSTOM-\d+"]
    
    @pytest.mark.asyncio
    async def test_integration_with_ticket_file(self, acceptance_criteria_agent, tmp_path):
        """Test integration with ticket.md file."""
        # Create ticket.md file
        ticket_content = """
# CMS20-1234: User Authentication System

## Description
Implement comprehensive user authentication

## Acceptance Criteria

1. User login with email and password
2. Password strength validation (8+ chars, special chars)
3. Session management with timeout
4. Password reset functionality

## Technical Requirements
- JWT tokens
- Bcrypt hashing
        """
        
        ticket_file = tmp_path / "ticket.md"
        ticket_file.write_text(ticket_content)
        
        context = {
            "working_path": str(tmp_path),
            "git_branch": "feature/CMS20-1234-auth"
        }
        
        # Mock Claude response
        mock_response = "AC analysis completed successfully"
        acceptance_criteria_agent.claude_service.execute_agent_query.return_value = mock_response
        
        result = await acceptance_criteria_agent.analyze(context)
        
        # Check that ticket.md was processed
        assert result.success == True
        assert "jira_info" in result.context_data
        assert result.context_data["jira_info"]["ticket_id"] == "CMS20-1234"
        assert "ticket_content" in result.context_data["jira_info"]
        
        # Check that AC were extracted from ticket
        assert result.result_data["acceptance_criteria_count"] >= 4


# Integration Tests

class TestAcceptanceCriteriaAgentIntegration:
    """Integration tests for AcceptanceCriteriaAgent."""
    
    @pytest.fixture
    def real_settings(self):
        """Create real settings for integration tests."""
        return Settings(
            claude_api_key="test-key",
            database_url="sqlite:///test.db"
        )
    
    @pytest.mark.integration 
    @pytest.mark.asyncio
    async def test_full_workflow(self, tmp_path, real_settings):
        """Test full workflow with mock Claude service."""
        # Create test files
        auth_file = tmp_path / "auth.service.ts"
        auth_file.write_text("""
export class AuthService {
    async login(email: string, password: string) {
        // Login implementation
        return this.validateCredentials(email, password);
    }
    
    validatePassword(password: string): boolean {
        return password.length >= 8;
    }
}
        """)
        
        ticket_file = tmp_path / "ticket.md"
        ticket_file.write_text("""
# CMS20-1234: Authentication

## Acceptance Criteria
1. User login with email/password
2. Password validation (8+ characters)
        """)
        
        # Mock Claude service
        claude_service = AsyncMock(spec=ClaudeService)
        claude_service.health_check.return_value = True
        claude_service.execute_agent_query.return_value = """
        ## AC-ANALYSE
        ### ✅ AC1: User login with email/password
        **Status**: VOLLSTÄNDIG ERFÜLLT
        Found at auth.service.ts:3-6
        
        ### ✅ AC2: Password validation (8+ characters)  
        **Status**: VOLLSTÄNDIG ERFÜLLT  
        Found at auth.service.ts:8-10
        """
        
        # Create agent
        agent = AcceptanceCriteriaAgent(
            agent_id="integration_test",
            agent_type="acceptance_criteria", 
            claude_service=claude_service,
            settings=real_settings
        )
        
        # Execute analysis
        context = {
            "working_path": str(tmp_path),
            "git_branch": "feature/CMS20-1234-auth"
        }
        
        result = await agent.analyze(context)
        
        # Verify results
        assert result.success == True
        assert result.result_data["acceptance_criteria_count"] == 2
        assert result.result_data["compliance_rate"] > 90
        assert len(result.result_data["fully_implemented"]) >= 2
        
        # Verify Claude was called correctly
        claude_service.execute_agent_query.assert_called_once()
        call_args = claude_service.execute_agent_query.call_args
        assert "fokussierte Acceptance Criteria Review" in call_args[1]["prompt"]
        assert call_args[1]["system_prompt"] is not None
        assert call_args[1]["working_path"] == tmp_path