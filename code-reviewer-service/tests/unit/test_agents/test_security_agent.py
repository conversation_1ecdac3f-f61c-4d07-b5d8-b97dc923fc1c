"""
Unit tests for SecurityAnalysisAgent
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
from uuid import UUID

from src.agents.strategies.security_analysis_strategy import SecurityAnalysisAgent
from src.agents.models.agent_result import Agent<PERSON><PERSON><PERSON>, AgentExecutionStatus


class TestSecurityAnalysisAgent:
    """Test suite for SecurityAnalysisAgent."""
    
    @pytest.fixture
    def mock_claude_service(self):
        """Mock Claude service."""
        service = Mock()
        service.query_async = AsyncMock()
        service.health_check = AsyncMock(return_value=True)
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings."""
        settings = Mock()
        settings.claude_config = {"max_turns": 5, "timeout": 300}
        return settings
    
    @pytest.fixture
    def security_agent(self, mock_claude_service, mock_settings):
        """Create SecurityAnalysisAgent instance."""
        return SecurityAnalysisAgent(
            agent_id="test_security_agent",
            agent_type="security_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings,
            timeout_seconds=300
        )
    
    @pytest.fixture
    def sample_context(self):
        """Sample analysis context."""
        return {
            "changed_files": [
                "src/services/userService.ts",
                "src/controllers/authController.py",
                "src/config/database.js",
                ".env.example"
            ],
            "working_path": "/test/project",
            "branch_name": "feature/security-update",
            "ticket_id": "SEC-123",
            "jira_info": {
                "summary": "Fix security vulnerabilities",
                "description": "Address SQL injection and XSS issues"
            }
        }
    
    def test_init(self, security_agent):
        """Test SecurityAnalysisAgent initialization."""
        assert security_agent.agent_type == "security_analysis"
        assert security_agent.display_name == "Security Analysis Agent"
        assert "security vulnerabilities" in security_agent.description.lower()
        assert security_agent.timeout_seconds == 300
    
    def test_init_with_config(self, mock_claude_service, mock_settings):
        """Test SecurityAnalysisAgent initialization with custom config."""
        config = {
            "security": {
                "severity_threshold": "high",
                "check_dependencies": False
            }
        }
        
        agent = SecurityAnalysisAgent(
            agent_id="test_agent",
            agent_type="security_analysis", 
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        assert agent.severity_threshold == "high"
        assert agent.check_dependencies is False
    
    @pytest.mark.asyncio
    async def test_extract_security_context(self, security_agent, sample_context):
        """Test security context extraction."""
        security_context = await security_agent._extract_security_context(sample_context)
        
        assert security_context["changed_files"] == sample_context["changed_files"]
        assert security_context["working_path"] == sample_context["working_path"]
        assert security_context["branch_name"] == sample_context["branch_name"]
        assert security_context["ticket_id"] == sample_context["ticket_id"]
        
        # Check file filtering
        security_files = security_context["security_relevant_files"]
        assert "src/services/userService.ts" in security_files
        assert "src/controllers/authController.py" in security_files
        assert "src/config/database.js" in security_files
        # .env.example should not be in security_relevant_files but in config_files
        
        config_files = security_context["config_files"]
        assert ".env.example" in config_files
    
    @pytest.mark.asyncio
    async def test_build_security_prompt(self, security_agent, sample_context):
        """Test security prompt building."""
        security_context = await security_agent._extract_security_context(sample_context)
        prompt = await security_agent._build_security_prompt(security_context)
        
        # Check prompt contains key elements
        assert "Security Analysis Review Prompt" in prompt
        assert "SQL Injection" in prompt
        assert "XSS" in prompt
        assert "Authentication" in prompt
        assert "feature/security-update" in prompt
        assert "SEC-123" in prompt
        assert "Read tool" in prompt
        assert "Datei:Zeile" in prompt
    
    @pytest.mark.asyncio
    async def test_process_security_analysis(self, security_agent, sample_context):
        """Test security analysis result processing."""
        security_context = await security_agent._extract_security_context(sample_context)
        analysis_result = "Test security analysis result"
        
        processed_result = await security_agent._process_security_analysis(analysis_result, security_context)
        
        assert "SECURITY ANALYSIS REPORT" in processed_result
        assert "Security Analysis Agent" in processed_result
        assert "test_security_agent" in processed_result
        assert "Files Analyzed:" in processed_result
        assert analysis_result in processed_result
    
    @pytest.mark.asyncio
    async def test_analyze_success(self, security_agent, sample_context, mock_claude_service):
        """Test successful security analysis."""
        # Mock Claude service response
        mock_claude_service.query_async.return_value = "Security analysis completed successfully"
        
        with patch.object(security_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Mocked security analysis result"
            
            result = await security_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.agent_id == "test_security_agent"
            assert result.agent_type == "security_analysis"
            assert result.status == AgentExecutionStatus.SUCCESS
            assert "Security Analysis Agent" in result.result
            assert "SECURITY ANALYSIS REPORT" in result.result
            
            # Check metadata
            metadata = result.metadata
            assert metadata["files_analyzed"] == len(sample_context["changed_files"])
            assert "security_categories_checked" in metadata
            assert "input_validation" in metadata["security_categories_checked"]
    
    @pytest.mark.asyncio
    async def test_analyze_failure(self, security_agent, sample_context):
        """Test security analysis failure handling."""
        with patch.object(security_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Security analysis failed")
            
            result = await security_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.status == AgentExecutionStatus.FAILED
            assert "Security analysis failed" in result.result
            assert result.error_message == "Security analysis failed"
    
    @pytest.mark.asyncio
    async def test_analyze_timeout(self, security_agent, sample_context):
        """Test security analysis timeout handling."""
        with patch.object(security_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = asyncio.TimeoutError("Analysis timed out")
            
            result = await security_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.status == AgentExecutionStatus.FAILED
            assert "Security analysis failed" in result.result
    
    def test_get_agent_info(self, security_agent):
        """Test agent info retrieval."""
        info = security_agent.get_agent_info()
        
        assert info["agent_type"] == "security_analysis"
        assert info["display_name"] == "Security Analysis Agent"
        assert "security vulnerabilities" in info["description"].lower()
        assert "parallel_compatible" in info
        assert info["parallel_compatible"] is True
        
        capabilities = info["capabilities"]
        assert "SQL Injection Detection" in capabilities
        assert "XSS Vulnerability Analysis" in capabilities
        assert "Authentication Security Review" in capabilities
        
        file_types = info["supported_file_types"]
        assert ".py" in file_types
        assert ".js" in file_types
        assert ".ts" in file_types
    
    @pytest.mark.asyncio
    async def test_context_extraction_file_filtering(self, security_agent):
        """Test file filtering in context extraction."""
        context = {
            "changed_files": [
                "src/auth.py",
                "config.yaml", 
                "test.txt",
                "app.js",
                "style.css",
                ".env",
                "secrets.json"
            ],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        security_context = await security_agent._extract_security_context(context)
        
        # Security relevant files should include code files
        security_files = security_context["security_relevant_files"]
        assert "src/auth.py" in security_files
        assert "app.js" in security_files
        assert "style.css" not in security_files  # CSS not security relevant
        assert "test.txt" not in security_files   # Text files not security relevant
        
        # Config files should include configuration and secrets
        config_files = security_context["config_files"]
        assert ".env" in config_files
        assert "secrets.json" in config_files
        assert "config.yaml" in config_files
    
    @pytest.mark.asyncio
    async def test_empty_context(self, security_agent):
        """Test handling of empty context."""
        empty_context = {
            "changed_files": [],
            "working_path": "/test",
            "branch_name": "",
            "ticket_id": ""
        }
        
        result = await security_agent.analyze(empty_context)
        
        # Should still complete successfully even with empty context
        assert isinstance(result, AgentResult)
        assert result.agent_id == "test_security_agent"
        assert result.metadata["files_analyzed"] == 0
    
    def test_agent_configuration(self, mock_claude_service, mock_settings):
        """Test agent configuration options."""
        config = {
            "security": {
                "severity_threshold": "critical",
                "check_dependencies": True
            }
        }
        
        agent = SecurityAnalysisAgent(
            agent_id="config_test",
            agent_type="security_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        assert agent.severity_threshold == "critical"
        assert agent.check_dependencies is True
        
        # Test default values
        default_agent = SecurityAnalysisAgent(
            agent_id="default_test", 
            agent_type="security_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings
        )
        
        assert default_agent.severity_threshold == "medium"
        assert default_agent.check_dependencies is True
    
    @pytest.mark.asyncio
    async def test_prompt_customization_based_on_context(self, security_agent):
        """Test that prompt is customized based on context."""
        # Context with many security files
        security_heavy_context = {
            "changed_files": [
                "src/auth/login.py",
                "src/auth/register.py", 
                "src/security/validator.py",
                ".env.production",
                "config/secrets.yaml"
            ],
            "working_path": "/test",
            "branch_name": "security-overhaul",
            "ticket_id": "SEC-456"
        }
        
        context = await security_agent._extract_security_context(security_heavy_context)
        prompt = await security_agent._build_security_prompt(context)
        
        # Should include all security files in prompt
        assert "src/auth/login.py" in prompt
        assert "src/auth/register.py" in prompt
        assert "src/security/validator.py" in prompt
        assert ".env.production" in prompt
        assert "config/secrets.yaml" in prompt
        assert "security-overhaul" in prompt
        assert "SEC-456" in prompt