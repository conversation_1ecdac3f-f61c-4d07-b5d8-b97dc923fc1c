"""
Unit tests for BugDetectionAgent
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.agents.strategies.bug_detection_strategy import BugDetectionAgent
from src.agents.models.agent_result import AgentR<PERSON><PERSON>, AgentExecutionStatus


class TestBugDetectionAgent:
    """Test suite for BugDetectionAgent."""
    
    @pytest.fixture
    def mock_claude_service(self):
        """Mock Claude service."""
        service = Mock()
        service.query_async = AsyncMock()
        service.health_check = AsyncMock(return_value=True)
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings."""
        settings = Mock()
        settings.claude_config = {"max_turns": 5, "timeout": 300}
        return settings
    
    @pytest.fixture
    def bug_detection_agent(self, mock_claude_service, mock_settings):
        """Create BugDetectionAgent instance."""
        return BugDetectionAgent(
            agent_id="test_bug_agent",
            agent_type="bug_detection",
            claude_service=mock_claude_service,
            settings=mock_settings,
            timeout_seconds=400
        )
    
    @pytest.fixture
    def sample_context(self):
        """Sample analysis context."""
        return {
            "changed_files": [
                "src/services/paymentService.ts",
                "src/components/AsyncComponent.tsx",
                "src/utils/dataProcessor.py",
                "tests/payment.test.js",
                "src/async/worker.js"
            ],
            "working_path": "/test/project",
            "branch_name": "feature/bug-fixes",
            "ticket_id": "BUG-456",
            "jira_info": {
                "summary": "Fix race conditions and memory leaks",
                "description": "Address async/await issues and event listener cleanup"
            }
        }
    
    def test_init(self, bug_detection_agent):
        """Test BugDetectionAgent initialization."""
        assert bug_detection_agent.agent_type == "bug_detection"
        assert bug_detection_agent.display_name == "Bug Detection Agent"
        assert "logic bugs" in bug_detection_agent.description.lower()
        assert bug_detection_agent.timeout_seconds == 400
    
    def test_init_with_config(self, mock_claude_service, mock_settings):
        """Test BugDetectionAgent initialization with custom config."""
        config = {
            "bug_detection": {
                "complexity_threshold": "high",
                "check_performance": False,
                "analyze_concurrency": False
            }
        }
        
        agent = BugDetectionAgent(
            agent_id="test_agent",
            agent_type="bug_detection",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        assert agent.complexity_threshold == "high"
        assert agent.check_performance is False
        assert agent.analyze_concurrency is False
    
    @pytest.mark.asyncio
    async def test_extract_bug_detection_context(self, bug_detection_agent, sample_context):
        """Test bug detection context extraction."""
        bug_context = await bug_detection_agent._extract_bug_detection_context(sample_context)
        
        assert bug_context["changed_files"] == sample_context["changed_files"]
        assert bug_context["working_path"] == sample_context["working_path"]
        assert bug_context["branch_name"] == sample_context["branch_name"]
        
        # Check file categorization
        bug_prone_files = bug_context["bug_prone_files"]
        assert "src/services/paymentService.ts" in bug_prone_files
        assert "src/components/AsyncComponent.tsx" in bug_prone_files
        assert "src/utils/dataProcessor.py" in bug_prone_files
        
        # Check async file detection
        async_files = bug_context["async_files"]
        assert "src/components/AsyncComponent.tsx" in async_files
        assert "src/async/worker.js" in async_files
        
        # Check test file detection
        test_files = bug_context["test_files"]
        assert "tests/payment.test.js" in test_files
    
    @pytest.mark.asyncio
    async def test_build_bug_detection_prompt(self, bug_detection_agent, sample_context):
        """Test bug detection prompt building."""
        bug_context = await bug_detection_agent._extract_bug_detection_context(sample_context)
        prompt = await bug_detection_agent._build_bug_detection_prompt(bug_context)
        
        # Check prompt contains key elements
        assert "Bug Analysis Review Prompt" in prompt
        assert "Logic Bugs" in prompt
        assert "Race Conditions" in prompt
        assert "Memory Leaks" in prompt
        assert "Performance Issues" in prompt
        assert "feature/bug-fixes" in prompt
        assert "BUG-456" in prompt
        assert "Read tool" in prompt
    
    @pytest.mark.asyncio
    async def test_process_bug_detection_analysis(self, bug_detection_agent, sample_context):
        """Test bug detection analysis result processing."""
        bug_context = await bug_detection_agent._extract_bug_detection_context(sample_context)
        analysis_result = "Test bug detection analysis result"
        
        processed_result = await bug_detection_agent._process_bug_detection_analysis(analysis_result, bug_context)
        
        assert "BUG DETECTION ANALYSIS REPORT" in processed_result
        assert "Bug Detection Agent" in processed_result
        assert "test_bug_agent" in processed_result
        assert "Files Analyzed:" in processed_result
        assert analysis_result in processed_result
    
    @pytest.mark.asyncio
    async def test_analyze_success(self, bug_detection_agent, sample_context):
        """Test successful bug detection analysis."""
        with patch.object(bug_detection_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Mocked bug detection analysis result"
            
            result = await bug_detection_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.agent_id == "test_bug_agent"
            assert result.agent_type == "bug_detection"
            assert result.status == AgentExecutionStatus.SUCCESS
            assert "Bug Detection Agent" in result.result
            assert "BUG DETECTION ANALYSIS REPORT" in result.result
            
            # Check metadata
            metadata = result.metadata
            assert metadata["files_analyzed"] == len(sample_context["changed_files"])
            assert "bug_categories_checked" in metadata
            assert "logic_bugs" in metadata["bug_categories_checked"]
            assert "race_conditions" in metadata["bug_categories_checked"]
    
    @pytest.mark.asyncio
    async def test_analyze_failure(self, bug_detection_agent, sample_context):
        """Test bug detection analysis failure handling."""
        with patch.object(bug_detection_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Bug analysis failed")
            
            result = await bug_detection_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.status == AgentExecutionStatus.FAILED
            assert "Bug detection analysis failed" in result.result
            assert result.error_message == "Bug analysis failed"
    
    def test_get_agent_info(self, bug_detection_agent):
        """Test agent info retrieval."""
        info = bug_detection_agent.get_agent_info()
        
        assert info["agent_type"] == "bug_detection"
        assert info["display_name"] == "Bug Detection Agent"
        assert "logic bugs" in info["description"].lower()
        assert info["parallel_compatible"] is True
        
        capabilities = info["capabilities"]
        assert "Logic Bug Detection" in capabilities
        assert "Race Condition Analysis" in capabilities
        assert "Memory Leak Detection" in capabilities
        assert "Performance Issue Identification" in capabilities
        
        file_types = info["supported_file_types"]
        assert ".py" in file_types
        assert ".js" in file_types
        assert ".ts" in file_types
        assert ".tsx" in file_types
    
    @pytest.mark.asyncio
    async def test_file_categorization(self, bug_detection_agent):
        """Test file categorization for bug detection."""
        context = {
            "changed_files": [
                "src/async_handler.py",
                "components/parallel_processor.tsx",
                "tests/unit_test.spec.js",
                "docs/README.md",
                "styles/app.css",
                "src/concurrent_worker.js",
                "tests/integration.test.ts"
            ],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        bug_context = await bug_detection_agent._extract_bug_detection_context(context)
        
        # Bug prone files should include code files
        bug_prone_files = bug_context["bug_prone_files"]
        assert "src/async_handler.py" in bug_prone_files
        assert "components/parallel_processor.tsx" in bug_prone_files
        assert "src/concurrent_worker.js" in bug_prone_files
        assert "docs/README.md" not in bug_prone_files
        assert "styles/app.css" not in bug_prone_files
        
        # Async files should be detected by patterns
        async_files = bug_context["async_files"]
        assert "src/async_handler.py" in async_files
        assert "components/parallel_processor.tsx" in async_files
        assert "src/concurrent_worker.js" in async_files
        
        # Test files should be detected
        test_files = bug_context["test_files"]
        assert "tests/unit_test.spec.js" in test_files
        assert "tests/integration.test.ts" in test_files
    
    @pytest.mark.asyncio
    async def test_configuration_impact_on_prompt(self, mock_claude_service, mock_settings):
        """Test that configuration affects prompt generation."""
        # Agent with performance analysis disabled
        config_no_perf = {
            "bug_detection": {
                "check_performance": False,
                "analyze_concurrency": True
            }
        }
        
        agent_no_perf = BugDetectionAgent(
            agent_id="no_perf",
            agent_type="bug_detection",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config_no_perf
        )
        
        context = {
            "changed_files": ["src/test.py"],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        bug_context = await agent_no_perf._extract_bug_detection_context(context)
        prompt = await agent_no_perf._build_bug_detection_prompt(bug_context)
        
        # Should reflect configuration in context section
        assert "Performance Analysis: ❌ Disabled" in prompt
        assert "Concurrency Analysis: ✅ Enabled" in prompt
    
    @pytest.mark.asyncio
    async def test_empty_file_context(self, bug_detection_agent):
        """Test handling of context with no files."""
        empty_context = {
            "changed_files": [],
            "working_path": "/test",
            "branch_name": "empty",
            "ticket_id": "EMPTY-1"
        }
        
        result = await bug_detection_agent.analyze(empty_context)
        
        assert isinstance(result, AgentResult)
        assert result.metadata["files_analyzed"] == 0
        # Should still complete successfully
        assert result.status in [AgentExecutionStatus.SUCCESS, AgentExecutionStatus.FAILED]
    
    def test_default_configuration(self, mock_claude_service, mock_settings):
        """Test default configuration values."""
        agent = BugDetectionAgent(
            agent_id="default_test",
            agent_type="bug_detection",
            claude_service=mock_claude_service,
            settings=mock_settings
        )
        
        assert agent.complexity_threshold == "medium"
        assert agent.check_performance is True
        assert agent.analyze_concurrency is True
    
    @pytest.mark.asyncio
    async def test_max_turns_configuration(self, bug_detection_agent, sample_context):
        """Test that max_turns is correctly set for bug detection."""
        with patch.object(bug_detection_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Bug analysis result"
            
            await bug_detection_agent.analyze(sample_context)
            
            # Verify that execute_claude_query was called with max_turns=10
            mock_execute.assert_called_once()
            call_args = mock_execute.call_args
            assert call_args.kwargs["max_turns"] == 10
    
    @pytest.mark.asyncio
    async def test_concurrent_file_analysis(self, bug_detection_agent):
        """Test analysis of files with concurrent/parallel patterns."""
        concurrent_context = {
            "changed_files": [
                "src/thread_pool.py",
                "src/async_queue.js",
                "src/parallel_map.go",
                "workers/background_job.py"
            ],
            "working_path": "/concurrent_project",
            "branch_name": "concurrency-fixes",
            "ticket_id": "CONC-789"
        }
        
        bug_context = await bug_detection_agent._extract_bug_detection_context(concurrent_context)
        
        # All files should be detected as async/concurrent
        async_files = bug_context["async_files"]
        assert "src/thread_pool.py" in async_files
        assert "src/async_queue.js" in async_files
        assert "src/parallel_map.go" in async_files
        assert "workers/background_job.py" in async_files
        
        # Prompt should include concurrency section
        prompt = await bug_detection_agent._build_bug_detection_prompt(bug_context)
        assert "CONCURRENCY-KRITISCHE DATEIEN" in prompt
        assert "Race Conditions" in prompt