"""
Unit tests for ArchitecturalAgent
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.agents.strategies.architectural_strategy import ArchitecturalAgent
from src.agents.models.agent_result import AgentR<PERSON>ult, AgentExecutionStatus


class TestArchitecturalAgent:
    """Test suite for ArchitecturalAgent."""
    
    @pytest.fixture
    def mock_claude_service(self):
        """Mock Claude service."""
        service = Mock()
        service.query_async = AsyncMock()
        service.health_check = AsyncMock(return_value=True)
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings."""
        settings = Mock()
        settings.claude_config = {"max_turns": 5, "timeout": 300}
        return settings
    
    @pytest.fixture
    def architectural_agent(self, mock_claude_service, mock_settings):
        """Create ArchitecturalAgent instance."""
        return ArchitecturalAgent(
            agent_id="test_arch_agent",
            agent_type="architectural",
            claude_service=mock_claude_service,
            settings=mock_settings,
            timeout_seconds=400
        )
    
    @pytest.fixture
    def sample_context(self):
        """Sample analysis context."""
        return {
            "changed_files": [
                "src/services/PaymentService.ts",
                "src/components/UserComponent.tsx", 
                "src/repositories/UserRepository.py",
                "src/models/OrderModel.java",
                "src/api/endpoints/userApi.js",
                "docker-compose.yml",
                "kubernetes/deployment.yaml",
                "src/config/database.py",
                "openapi/user-api.yaml"
            ],
            "working_path": "/test/project",
            "branch_name": "feature/architecture-improvements",
            "ticket_id": "ARCH-456",
            "jira_info": {
                "summary": "Improve system architecture and design patterns",
                "description": "Refactor to better separation of concerns and implement proper design patterns"
            }
        }
    
    def test_init(self, architectural_agent):
        """Test ArchitecturalAgent initialization."""
        assert architectural_agent.agent_type == "architectural"
        assert architectural_agent.display_name == "Architectural Agent"
        assert "system architecture" in architectural_agent.description.lower()
        assert architectural_agent.timeout_seconds == 400
    
    def test_init_with_config(self, mock_claude_service, mock_settings):
        """Test ArchitecturalAgent initialization with custom config."""
        config = {
            "architectural": {
                "design_patterns": False,
                "scalability": True,
                "technical_debt": False,
                "coupling_analysis": True,
                "layer_validation": False
            }
        }
        
        agent = ArchitecturalAgent(
            agent_id="test_agent",
            agent_type="architectural",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        assert agent.pattern_analysis is False
        assert agent.scalability_assessment is True
        assert agent.technical_debt_tracking is False
        assert agent.coupling_analysis is True
        assert agent.layer_validation is False
    
    @pytest.mark.asyncio
    async def test_extract_architectural_context(self, architectural_agent, sample_context):
        """Test architectural context extraction."""
        arch_context = await architectural_agent._extract_architectural_context(sample_context)
        
        assert arch_context["changed_files"] == sample_context["changed_files"]
        assert arch_context["working_path"] == sample_context["working_path"]
        assert arch_context["branch_name"] == sample_context["branch_name"]
        
        # Check file categorization
        architecture_files = arch_context["architecture_files"]
        assert "src/services/PaymentService.ts" in architecture_files
        assert "src/components/UserComponent.tsx" in architecture_files
        assert "src/repositories/UserRepository.py" in architecture_files
        assert "src/models/OrderModel.java" in architecture_files
        
        # Check component file detection
        component_files = arch_context["component_files"]
        assert "src/services/PaymentService.ts" in component_files
        assert "src/components/UserComponent.tsx" in component_files
        assert "src/repositories/UserRepository.py" in component_files
        
        # Check infrastructure file detection
        infrastructure_files = arch_context["infrastructure_files"]
        assert "docker-compose.yml" in infrastructure_files
        assert "kubernetes/deployment.yaml" in infrastructure_files
        
        # Check API file detection
        api_files = arch_context["api_files"]
        assert "src/api/endpoints/userApi.js" in api_files
        assert "openapi/user-api.yaml" in api_files
        
        # Check data layer file detection
        data_files = arch_context["data_files"]
        assert "src/repositories/UserRepository.py" in data_files
        assert "src/models/OrderModel.java" in data_files
    
    @pytest.mark.asyncio
    async def test_build_architectural_prompt(self, architectural_agent, sample_context):
        """Test architectural prompt building."""
        arch_context = await architectural_agent._extract_architectural_context(sample_context)
        prompt = await architectural_agent._build_architectural_prompt(arch_context)
        
        # Check prompt contains key elements
        assert "Architectural Analysis Review Prompt" in prompt
        assert "System Architecture" in prompt
        assert "Design Patterns" in prompt
        assert "Scalability" in prompt
        assert "Technical Debt" in prompt
        assert "Component Coupling" in prompt
        assert "feature/architecture-improvements" in prompt
        assert "ARCH-456" in prompt
        assert "Read tool" in prompt
    
    @pytest.mark.asyncio
    async def test_process_architectural_analysis(self, architectural_agent, sample_context):
        """Test architectural analysis result processing."""
        arch_context = await architectural_agent._extract_architectural_context(sample_context)
        analysis_result = "Test architectural analysis result"
        
        processed_result = await architectural_agent._process_architectural_analysis(analysis_result, arch_context)
        
        assert "ARCHITECTURAL ANALYSIS REPORT" in processed_result
        assert "Architectural Agent" in processed_result
        assert "test_arch_agent" in processed_result
        assert "Files Analyzed:" in processed_result
        assert analysis_result in processed_result
    
    @pytest.mark.asyncio
    async def test_analyze_success(self, architectural_agent, sample_context):
        """Test successful architectural analysis."""
        with patch.object(architectural_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Mocked architectural analysis result"
            
            result = await architectural_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.agent_id == "test_arch_agent"
            assert result.agent_type == "architectural"
            assert result.status == AgentExecutionStatus.SUCCESS
            assert "Architectural Agent" in result.result
            assert "ARCHITECTURAL ANALYSIS REPORT" in result.result
            
            # Check metadata
            metadata = result.metadata
            assert metadata["files_analyzed"] == len(sample_context["changed_files"])
            assert "architecture_categories_checked" in metadata
            assert "design_patterns" in metadata["architecture_categories_checked"]
            assert "scalability" in metadata["architecture_categories_checked"]
    
    @pytest.mark.asyncio
    async def test_analyze_failure(self, architectural_agent, sample_context):
        """Test architectural analysis failure handling."""
        with patch.object(architectural_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Architectural analysis failed")
            
            result = await architectural_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.status == AgentExecutionStatus.FAILED
            assert "Architectural analysis failed" in result.result
            assert result.error_message == "Architectural analysis failed"
    
    def test_get_agent_info(self, architectural_agent):
        """Test agent info retrieval."""
        info = architectural_agent.get_agent_info()
        
        assert info["agent_type"] == "architectural"
        assert info["display_name"] == "Architectural Agent"
        assert "system architecture" in info["description"].lower()
        assert info["parallel_compatible"] is True
        
        capabilities = info["capabilities"]
        assert "System Architecture Analysis" in capabilities
        assert "Design Pattern Recognition" in capabilities
        assert "Scalability Assessment" in capabilities
        assert "Technical Debt Identification" in capabilities
        
        file_types = info["supported_file_types"]
        assert ".py" in file_types
        assert ".js" in file_types
        assert ".ts" in file_types
        assert ".java" in file_types
    
    @pytest.mark.asyncio
    async def test_comprehensive_file_categorization(self, architectural_agent):
        """Test comprehensive file categorization for architecture analysis."""
        context = {
            "changed_files": [
                # Component files
                "src/components/UserComponent.tsx",
                "src/services/PaymentService.py",
                "src/controllers/ApiController.js",
                "src/middleware/AuthMiddleware.ts",
                "src/handlers/EventHandler.java",
                "src/processors/DataProcessor.cs",
                "src/managers/SessionManager.rb",
                "src/factories/ComponentFactory.go",
                "src/builders/QueryBuilder.rs",
                "src/adapters/DatabaseAdapter.php",
                "src/facades/ApiFacade.scala",
                "src/proxies/ServiceProxy.kt",
                "src/decorators/LoggingDecorator.py",
                # Infrastructure files
                "docker-compose.yml",
                "Dockerfile",
                "kubernetes/service.yaml",
                "kubernetes/deployment.yaml",
                "helm/chart.yaml",
                "terraform/main.tf",
                "deployment/pipeline.yml",
                "workflow/ci.yml",
                "Makefile",
                # API files
                "src/api/routes/userRoutes.js",
                "src/endpoints/paymentEndpoint.py",
                "src/interfaces/UserInterface.ts",
                "src/contracts/PaymentContract.java",
                "openapi/api-spec.yaml",
                "swagger/user-api.yml",
                "graphql/schema.graphql",
                "grpc/service.proto",
                # Data layer files
                "src/models/UserModel.py",
                "src/entities/OrderEntity.java",
                "src/repositories/PaymentRepository.ts",
                "src/dao/UserDao.js",
                "database/migrations/001_create_users.sql",
                "database/schema/user_schema.sql",
                "orm/mappings/user.xml",
                "src/queries/userQueries.py",
                "src/store/userStore.js",
                "src/persistence/DataPersistence.java"
            ],
            "working_path": "/comprehensive_arch",
            "branch_name": "architecture-overhaul",
            "ticket_id": "COMP-789"
        }
        
        arch_context = await architectural_agent._extract_architectural_context(context)
        
        # Component files should include all architectural component patterns
        component_files = arch_context["component_files"]
        expected_components = [
            "src/components/UserComponent.tsx",
            "src/services/PaymentService.py",
            "src/controllers/ApiController.js",
            "src/middleware/AuthMiddleware.ts",
            "src/handlers/EventHandler.java",
            "src/processors/DataProcessor.cs",
            "src/managers/SessionManager.rb",
            "src/factories/ComponentFactory.go",
            "src/builders/QueryBuilder.rs",
            "src/adapters/DatabaseAdapter.php",
            "src/facades/ApiFacade.scala",
            "src/proxies/ServiceProxy.kt",
            "src/decorators/LoggingDecorator.py"
        ]
        for component in expected_components:
            assert component in component_files
        
        # Infrastructure files should include deployment and config files
        infrastructure_files = arch_context["infrastructure_files"]
        expected_infra = [
            "docker-compose.yml",
            "Dockerfile", 
            "kubernetes/service.yaml",
            "kubernetes/deployment.yaml",
            "helm/chart.yaml",
            "terraform/main.tf",
            "deployment/pipeline.yml",
            "workflow/ci.yml",
            "Makefile"
        ]
        for infra in expected_infra:
            assert infra in infrastructure_files
        
        # API files should include all API-related files
        api_files = arch_context["api_files"]
        expected_api = [
            "src/api/routes/userRoutes.js",
            "src/endpoints/paymentEndpoint.py",
            "src/interfaces/UserInterface.ts",
            "src/contracts/PaymentContract.java",
            "openapi/api-spec.yaml",
            "swagger/user-api.yml",
            "graphql/schema.graphql",
            "grpc/service.proto"
        ]
        for api in expected_api:
            assert api in api_files
        
        # Data files should include all data layer files
        data_files = arch_context["data_files"]
        expected_data = [
            "src/models/UserModel.py",
            "src/entities/OrderEntity.java",
            "src/repositories/PaymentRepository.ts",
            "src/queries/userQueries.py",
            "src/store/userStore.js",
            "src/persistence/DataPersistence.java"
        ]
        for data in expected_data:
            assert data in data_files
    
    @pytest.mark.asyncio
    async def test_configuration_impact_on_prompt(self, mock_claude_service, mock_settings):
        """Test that configuration affects prompt generation."""
        config = {
            "architectural": {
                "design_patterns": False,
                "scalability": True,
                "technical_debt": False,
                "coupling_analysis": True,
                "layer_validation": False
            }
        }
        
        agent = ArchitecturalAgent(
            agent_id="config_test",
            agent_type="architectural",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        context = {
            "changed_files": ["src/test.py"],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        arch_context = await agent._extract_architectural_context(context)
        prompt = await agent._build_architectural_prompt(arch_context)
        
        # Should reflect configuration in context section
        assert "Design Pattern Analysis: ❌ Disabled" in prompt
        assert "Scalability Assessment: ✅ Enabled" in prompt
        assert "Technical Debt Tracking: ❌ Disabled" in prompt
        assert "Coupling Analysis: ✅ Enabled" in prompt
        assert "Layer Validation: ❌ Disabled" in prompt
    
    def test_default_configuration(self, mock_claude_service, mock_settings):
        """Test default configuration values."""
        agent = ArchitecturalAgent(
            agent_id="default_test",
            agent_type="architectural",
            claude_service=mock_claude_service,
            settings=mock_settings
        )
        
        assert agent.pattern_analysis is True
        assert agent.scalability_assessment is True
        assert agent.technical_debt_tracking is True
        assert agent.coupling_analysis is True
        assert agent.layer_validation is True
    
    @pytest.mark.asyncio
    async def test_max_turns_configuration(self, architectural_agent, sample_context):
        """Test that max_turns is correctly set for architectural analysis."""
        with patch.object(architectural_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Architectural analysis result"
            
            await architectural_agent.analyze(sample_context)
            
            # Verify that execute_claude_query was called with max_turns=15
            mock_execute.assert_called_once()
            call_args = mock_execute.call_args
            assert call_args.kwargs["max_turns"] == 15
    
    @pytest.mark.asyncio
    async def test_empty_context_handling(self, architectural_agent):
        """Test handling of empty context."""
        empty_context = {
            "changed_files": [],
            "working_path": "/empty",
            "branch_name": "empty",
            "ticket_id": "EMPTY-1"
        }
        
        result = await architectural_agent.analyze(empty_context)
        
        assert isinstance(result, AgentResult)
        assert result.metadata["files_analyzed"] == 0
        # Should still complete successfully or fail gracefully
        assert result.status in [AgentExecutionStatus.SUCCESS, AgentExecutionStatus.FAILED]
    
    @pytest.mark.asyncio
    async def test_microservices_architecture_detection(self, architectural_agent):
        """Test detection of microservices architecture patterns."""
        microservices_context = {
            "changed_files": [
                "services/user-service/src/main.py",
                "services/payment-service/src/app.js",
                "services/order-service/src/server.ts",
                "api-gateway/src/gateway.py",
                "docker-compose.yml",
                "kubernetes/user-service.yaml",
                "kubernetes/payment-service.yaml",
                "kubernetes/order-service.yaml",
                "config/service-mesh.yaml"
            ],
            "working_path": "/microservices",
            "branch_name": "microservices-refactor",
            "ticket_id": "MICRO-123"
        }
        
        arch_context = await architectural_agent._extract_architectural_context(microservices_context)
        
        # Should categorize microservice files appropriately
        component_files = arch_context["component_files"]
        infrastructure_files = arch_context["infrastructure_files"]
        
        # Service files should be detected as components
        assert "services/user-service/src/main.py" in component_files
        assert "services/payment-service/src/app.js" in component_files
        assert "services/order-service/src/server.ts" in component_files
        assert "api-gateway/src/gateway.py" in component_files
        
        # Kubernetes and Docker files should be infrastructure
        assert "docker-compose.yml" in infrastructure_files
        assert "kubernetes/user-service.yaml" in infrastructure_files
        assert "config/service-mesh.yaml" in infrastructure_files
    
    @pytest.mark.asyncio
    async def test_monolith_to_microservices_pattern(self, architectural_agent):
        """Test analysis of monolith to microservices migration patterns."""
        migration_context = {
            "changed_files": [
                # Monolithic structure
                "src/monolith/UserModule.java",
                "src/monolith/PaymentModule.java",
                "src/monolith/OrderModule.java",
                # New microservice structure
                "microservices/user/UserService.java",
                "microservices/payment/PaymentService.java",
                "microservices/order/OrderService.java",
                # Shared components
                "shared/common/EventBus.java",
                "shared/common/ApiGateway.java",
                # Infrastructure
                "docker/user-service.dockerfile",
                "kubernetes/services.yaml"
            ],
            "working_path": "/migration",
            "branch_name": "monolith-to-microservices",
            "ticket_id": "MIGRATION-456"
        }
        
        arch_context = await architectural_agent._extract_architectural_context(migration_context)
        prompt = await architectural_agent._build_architectural_prompt(arch_context)
        
        # Should detect both monolithic and microservice patterns
        assert "src/monolith/UserModule.java" in prompt
        assert "microservices/user/UserService.java" in prompt
        assert "shared/common/EventBus.java" in prompt
        assert "monolith-to-microservices" in prompt
    
    @pytest.mark.asyncio
    async def test_architectural_decision_context(self, architectural_agent):
        """Test architectural decision making context."""
        decision_context = {
            "changed_files": [
                "src/legacy/OldPaymentProcessor.java",
                "src/new/NewPaymentProcessor.java",
                "docs/architecture/payment-refactor.md",
                "docs/decisions/ADR-001-payment-architecture.md"
            ],
            "working_path": "/decisions",
            "branch_name": "payment-architecture-decision",
            "ticket_id": "ADR-001",
            "jira_info": {
                "summary": "Refactor payment processing architecture",
                "description": "Move from legacy synchronous to new asynchronous payment processing"
            }
        }
        
        arch_context = await architectural_agent._extract_architectural_context(decision_context)
        
        # Should analyze both old and new architecture
        architecture_files = arch_context["architecture_files"]
        assert "src/legacy/OldPaymentProcessor.java" in architecture_files
        assert "src/new/NewPaymentProcessor.java" in architecture_files
        
        # Context should include decision information
        assert arch_context["ticket_id"] == "ADR-001"
        assert "payment processing architecture" in arch_context["jira_info"]["summary"].lower()