"""
Unit tests for LogicAnalysisAgent
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path

from src.agents.strategies.logic_analysis_strategy import LogicAnalysisAgent
from src.agents.models.agent_result import Agent<PERSON><PERSON><PERSON>, AgentExecutionStatus


class TestLogicAnalysisAgent:
    """Test suite for LogicAnalysisAgent."""
    
    @pytest.fixture
    def mock_claude_service(self):
        """Mock Claude service."""
        service = Mock()
        service.query_async = AsyncMock()
        service.health_check = AsyncMock(return_value=True)
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings."""
        settings = Mock()
        settings.claude_config = {"max_turns": 5, "timeout": 300}
        return settings
    
    @pytest.fixture
    def logic_analysis_agent(self, mock_claude_service, mock_settings):
        """Create LogicAnalysisAgent instance."""
        return LogicAnalysisAgent(
            agent_id="test_logic_agent",
            agent_type="logic_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings,
            timeout_seconds=300
        )
    
    @pytest.fixture
    def sample_context(self):
        """Sample analysis context."""
        return {
            "changed_files": [
                "src/services/calculationService.ts",
                "src/algorithms/sortingAlgorithm.py",
                "src/business/orderProcessor.js",
                "src/store/userReducer.ts",
                "src/domain/paymentModel.java"
            ],
            "working_path": "/test/project",
            "branch_name": "feature/logic-improvements",
            "ticket_id": "LOGIC-789",
            "jira_info": {
                "summary": "Improve business logic and algorithms",
                "description": "Optimize calculation algorithms and fix business rule violations"
            }
        }
    
    def test_init(self, logic_analysis_agent):
        """Test LogicAnalysisAgent initialization."""
        assert logic_analysis_agent.agent_type == "logic_analysis"
        assert logic_analysis_agent.display_name == "Logic Analysis Agent"
        assert "algorithmic logic" in logic_analysis_agent.description.lower()
        assert logic_analysis_agent.timeout_seconds == 300
    
    def test_init_with_config(self, mock_claude_service, mock_settings):
        """Test LogicAnalysisAgent initialization with custom config."""
        config = {
            "logic_analysis": {
                "algorithm_complexity": False,
                "business_rules": True,
                "state_flow": False,
                "math_verification": True
            }
        }
        
        agent = LogicAnalysisAgent(
            agent_id="test_agent",
            agent_type="logic_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        assert agent.algorithm_complexity_check is False
        assert agent.business_rule_validation is True
        assert agent.state_flow_analysis is False
        assert agent.mathematical_verification is True
    
    @pytest.mark.asyncio
    async def test_extract_logic_analysis_context(self, logic_analysis_agent, sample_context):
        """Test logic analysis context extraction."""
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(sample_context)
        
        assert logic_context["changed_files"] == sample_context["changed_files"]
        assert logic_context["working_path"] == sample_context["working_path"]
        assert logic_context["branch_name"] == sample_context["branch_name"]
        
        # Check file categorization
        logic_files = logic_context["logic_files"]
        assert "src/services/calculationService.ts" in logic_files
        assert "src/algorithms/sortingAlgorithm.py" in logic_files
        assert "src/business/orderProcessor.js" in logic_files
        
        # Check algorithm file detection
        algorithm_files = logic_context["algorithm_files"]
        assert "src/services/calculationService.ts" in algorithm_files
        assert "src/algorithms/sortingAlgorithm.py" in algorithm_files
        assert "src/business/orderProcessor.js" in algorithm_files
        
        # Check business logic file detection
        business_files = logic_context["business_files"]
        assert "src/services/calculationService.ts" in business_files
        assert "src/business/orderProcessor.js" in business_files
        assert "src/domain/paymentModel.java" in business_files
        
        # Check state management file detection
        state_files = logic_context["state_files"]
        assert "src/store/userReducer.ts" in state_files
    
    @pytest.mark.asyncio
    async def test_build_logic_analysis_prompt(self, logic_analysis_agent, sample_context):
        """Test logic analysis prompt building."""
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(sample_context)
        prompt = await logic_analysis_agent._build_logic_analysis_prompt(logic_context)
        
        # Check prompt contains key elements
        assert "Logic Analysis Review Prompt" in prompt
        assert "Algorithm Analysis" in prompt
        assert "Business Logic" in prompt
        assert "State Management" in prompt
        assert "Data Flow" in prompt
        assert "feature/logic-improvements" in prompt
        assert "LOGIC-789" in prompt
        assert "Read tool" in prompt
    
    @pytest.mark.asyncio
    async def test_process_logic_analysis(self, logic_analysis_agent, sample_context):
        """Test logic analysis result processing."""
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(sample_context)
        analysis_result = "Test logic analysis result"
        
        processed_result = await logic_analysis_agent._process_logic_analysis(analysis_result, logic_context)
        
        assert "LOGIC ANALYSIS REPORT" in processed_result
        assert "Logic Analysis Agent" in processed_result
        assert "test_logic_agent" in processed_result
        assert "Files Analyzed:" in processed_result
        assert analysis_result in processed_result
    
    @pytest.mark.asyncio
    async def test_analyze_success(self, logic_analysis_agent, sample_context):
        """Test successful logic analysis."""
        with patch.object(logic_analysis_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Mocked logic analysis result"
            
            result = await logic_analysis_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.agent_id == "test_logic_agent"
            assert result.agent_type == "logic_analysis"
            assert result.status == AgentExecutionStatus.SUCCESS
            assert "Logic Analysis Agent" in result.result
            assert "LOGIC ANALYSIS REPORT" in result.result
            
            # Check metadata
            metadata = result.metadata
            assert metadata["files_analyzed"] == len(sample_context["changed_files"])
            assert "logic_categories_checked" in metadata
            assert "algorithms" in metadata["logic_categories_checked"]
            assert "business_rules" in metadata["logic_categories_checked"]
    
    @pytest.mark.asyncio
    async def test_analyze_failure(self, logic_analysis_agent, sample_context):
        """Test logic analysis failure handling."""
        with patch.object(logic_analysis_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Logic analysis failed")
            
            result = await logic_analysis_agent.analyze(sample_context)
            
            assert isinstance(result, AgentResult)
            assert result.status == AgentExecutionStatus.FAILED
            assert "Logic analysis failed" in result.result
            assert result.error_message == "Logic analysis failed"
    
    def test_get_agent_info(self, logic_analysis_agent):
        """Test agent info retrieval."""
        info = logic_analysis_agent.get_agent_info()
        
        assert info["agent_type"] == "logic_analysis"
        assert info["display_name"] == "Logic Analysis Agent"
        assert "algorithmic logic" in info["description"].lower()
        assert info["parallel_compatible"] is True
        
        capabilities = info["capabilities"]
        assert "Algorithm Correctness Analysis" in capabilities
        assert "Business Logic Validation" in capabilities
        assert "State Management Review" in capabilities
        assert "Data Flow Analysis" in capabilities
        
        file_types = info["supported_file_types"]
        assert ".py" in file_types
        assert ".js" in file_types
        assert ".ts" in file_types
        assert ".java" in file_types
    
    @pytest.mark.asyncio
    async def test_file_categorization_algorithm_detection(self, logic_analysis_agent):
        """Test algorithm file detection patterns."""
        context = {
            "changed_files": [
                "src/algorithms/quicksort.py",
                "src/utils/calculator.js",
                "src/processors/dataProcessor.ts",
                "src/transforms/dataTransform.java",
                "src/views/UserView.jsx",  # Should not be algorithm
                "styles/main.css"  # Should not be algorithm
            ],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(context)
        
        algorithm_files = logic_context["algorithm_files"]
        assert "src/algorithms/quicksort.py" in algorithm_files
        assert "src/utils/calculator.js" in algorithm_files
        assert "src/processors/dataProcessor.ts" in algorithm_files
        assert "src/transforms/dataTransform.java" in algorithm_files
        assert "src/views/UserView.jsx" not in algorithm_files
        assert "styles/main.css" not in algorithm_files
    
    @pytest.mark.asyncio
    async def test_business_logic_file_detection(self, logic_analysis_agent):
        """Test business logic file detection."""
        context = {
            "changed_files": [
                "src/services/paymentService.py",
                "src/business/orderBusiness.js",
                "src/domain/userDomain.ts",
                "src/models/productModel.java",
                "src/repositories/userRepository.py",
                "src/controllers/apiController.js",
                "test/unit.test.js"  # Should not be business logic
            ],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(context)
        
        business_files = logic_context["business_files"]
        assert "src/services/paymentService.py" in business_files
        assert "src/business/orderBusiness.js" in business_files
        assert "src/domain/userDomain.ts" in business_files
        assert "src/models/productModel.java" in business_files
        assert "src/repositories/userRepository.py" in business_files
        assert "src/controllers/apiController.js" in business_files
        assert "test/unit.test.js" not in business_files
    
    @pytest.mark.asyncio
    async def test_state_management_file_detection(self, logic_analysis_agent):
        """Test state management file detection."""
        context = {
            "changed_files": [
                "src/store/userStore.js",
                "src/reducers/appReducer.ts",
                "src/actions/userActions.py",
                "src/slices/dataSlice.js",
                "src/context/AppContext.tsx",
                "src/providers/StateProvider.js",
                "src/hooks/useCustomHook.ts",
                "src/mutations/updateUser.js",
                "src/queries/getUser.graphql"
            ],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(context)
        
        state_files = logic_context["state_files"]
        assert "src/store/userStore.js" in state_files
        assert "src/reducers/appReducer.ts" in state_files
        assert "src/actions/userActions.py" in state_files
        assert "src/slices/dataSlice.js" in state_files
        assert "src/context/AppContext.tsx" in state_files
        assert "src/providers/StateProvider.js" in state_files
        assert "src/hooks/useCustomHook.ts" in state_files
        assert "src/mutations/updateUser.js" in state_files
        assert "src/queries/getUser.graphql" in state_files
    
    @pytest.mark.asyncio
    async def test_configuration_impact_on_prompt(self, mock_claude_service, mock_settings):
        """Test that configuration affects prompt generation."""
        config = {
            "logic_analysis": {
                "algorithm_complexity": False,
                "business_rules": True,
                "state_flow": False,
                "math_verification": True
            }
        }
        
        agent = LogicAnalysisAgent(
            agent_id="config_test",
            agent_type="logic_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings,
            config=config
        )
        
        context = {
            "changed_files": ["src/test.py"],
            "working_path": "/test",
            "branch_name": "test",
            "ticket_id": "TEST-1"
        }
        
        logic_context = await agent._extract_logic_analysis_context(context)
        prompt = await agent._build_logic_analysis_prompt(logic_context)
        
        # Should reflect configuration in context section
        assert "Algorithm Complexity Check: ❌ Disabled" in prompt
        assert "Business Rule Validation: ✅ Enabled" in prompt
        assert "State Flow Analysis: ❌ Disabled" in prompt
        assert "Mathematical Verification: ✅ Enabled" in prompt
    
    def test_default_configuration(self, mock_claude_service, mock_settings):
        """Test default configuration values."""
        agent = LogicAnalysisAgent(
            agent_id="default_test",
            agent_type="logic_analysis",
            claude_service=mock_claude_service,
            settings=mock_settings
        )
        
        assert agent.algorithm_complexity_check is True
        assert agent.business_rule_validation is True
        assert agent.state_flow_analysis is True
        assert agent.mathematical_verification is True
    
    @pytest.mark.asyncio
    async def test_max_turns_configuration(self, logic_analysis_agent, sample_context):
        """Test that max_turns is correctly set for logic analysis."""
        with patch.object(logic_analysis_agent, 'execute_claude_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = "Logic analysis result"
            
            await logic_analysis_agent.analyze(sample_context)
            
            # Verify that execute_claude_query was called with max_turns=12
            mock_execute.assert_called_once()
            call_args = mock_execute.call_args
            assert call_args.kwargs["max_turns"] == 12
    
    @pytest.mark.asyncio
    async def test_empty_context_handling(self, logic_analysis_agent):
        """Test handling of empty context."""
        empty_context = {
            "changed_files": [],
            "working_path": "/empty",
            "branch_name": "empty",
            "ticket_id": "EMPTY-1"
        }
        
        result = await logic_analysis_agent.analyze(empty_context)
        
        assert isinstance(result, AgentResult)
        assert result.metadata["files_analyzed"] == 0
        # Should still complete successfully or fail gracefully
        assert result.status in [AgentExecutionStatus.SUCCESS, AgentExecutionStatus.FAILED]
    
    @pytest.mark.asyncio
    async def test_mixed_file_types_categorization(self, logic_analysis_agent):
        """Test categorization with mixed file types."""
        context = {
            "changed_files": [
                "src/algorithms/search.py",  # Algorithm
                "src/business/validation.js",  # Business + Algorithm
                "src/store/cart.ts",  # State
                "src/domain/user.java",  # Business
                "src/utils/sort.cpp",  # Algorithm (C++)
                "docs/README.md",  # Documentation (should be excluded)
                "tests/logic.test.js"  # Test file (should be excluded from logic files)
            ],
            "working_path": "/mixed",
            "branch_name": "mixed-logic",
            "ticket_id": "MIX-123"
        }
        
        logic_context = await logic_analysis_agent._extract_logic_analysis_context(context)
        
        # Logic files should include supported extensions only
        logic_files = logic_context["logic_files"]
        assert "src/algorithms/search.py" in logic_files
        assert "src/business/validation.js" in logic_files
        assert "src/store/cart.ts" in logic_files
        assert "src/domain/user.java" in logic_files
        assert "src/utils/sort.cpp" in logic_files
        assert "docs/README.md" not in logic_files
        assert "tests/logic.test.js" in logic_files  # JS files are included in logic_files
        
        # Check proper categorization
        algorithm_files = logic_context["algorithm_files"]
        assert "src/algorithms/search.py" in algorithm_files
        assert "src/utils/sort.cpp" in algorithm_files
        
        business_files = logic_context["business_files"]
        assert "src/business/validation.js" in business_files
        assert "src/domain/user.java" in business_files
        
        state_files = logic_context["state_files"]
        assert "src/store/cart.ts" in state_files