"""
Unit Tests for Claude Service
Tests for native Claude Code SDK integration with mock scenarios
"""

import asyncio
import pytest
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List, Dict, Any

from src.services.claude_service import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>r, RateLimitError


class MockMessage:
    """Mock Claude SDK Message for testing"""
    def __init__(self, content: str, message_type: str = "text"):
        self.content = content
        self.type = message_type
        self.text = content


class TestClaudeService:
    """Test suite for ClaudeService"""
    
    @pytest.fixture
    def claude_service(self):
        """Create ClaudeService instance for testing"""
        return ClaudeService()
    
    @pytest.fixture
    def mock_messages(self):
        """Mock Claude SDK messages"""
        return [
            MockMessage("Starting analysis..."),
            MockMessage("Found 3 potential issues"),
            MockMessage("Analysis complete. Here are the findings:\n1. Security issue in auth.py\n2. Performance concern in database.py\n3. Code smell in utils.py")
        ]
    
    @pytest.mark.asyncio
    async def test_execute_agent_query_success(self, claude_service, mock_messages):
        """Test successful agent query execution"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to return messages
            async def mock_query_generator(*args, **kwargs):
                for message in mock_messages:
                    yield message
            
            mock_query.return_value = mock_query_generator()
            
            # Execute query
            result = await claude_service.execute_agent_query(
                prompt="Analyze this code for security issues",
                agent_type="security_analysis",
                working_directory=Path("/tmp/test"),
                max_turns=3
            )
            
            # Assertions
            assert result["success"] is True
            assert result["agent_type"] == "security_analysis"
            assert len(result["messages"]) == 3
            assert "Security issue in auth.py" in result["response"]
            assert result["metadata"]["working_directory"] == "/tmp/test"
            assert result["metadata"]["max_turns"] == 3
            assert "duration_ms" in result
    
    @pytest.mark.asyncio
    async def test_execute_agent_query_timeout(self, claude_service):
        """Test agent query timeout handling"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to timeout
            async def mock_timeout_generator(*args, **kwargs):
                await asyncio.sleep(2)  # Longer than test timeout
                yield MockMessage("This should not be reached")
            
            mock_query.return_value = mock_timeout_generator()
            
            # Execute query with short timeout
            with patch('src.services.claude_service.settings') as mock_settings:
                mock_settings.claude_timeout = 0.1  # Very short timeout
                
                result = await claude_service.execute_agent_query(
                    prompt="Test prompt",
                    agent_type="test_agent",
                    timeout=0.1
                )
            
            # Assertions
            assert result["success"] is False
            assert "timed out" in result["error"]
            assert result["agent_type"] == "test_agent"
    
    @pytest.mark.asyncio
    async def test_execute_agent_query_rate_limit(self, claude_service):
        """Test rate limit handling"""
        
        # Fill up rate limit tracker
        with patch('src.services.claude_service.settings') as mock_settings:
            mock_settings.claude_rate_limit_rpm = 2  # Very low limit
            
            # Execute multiple requests to trigger rate limit
            await claude_service.execute_agent_query("test1", "agent1")
            await claude_service.execute_agent_query("test2", "agent2")
            
            # This should be rate limited
            result = await claude_service.execute_agent_query("test3", "agent3")
            
            assert result["success"] is False
            assert "Rate limit exceeded" in result["error"]
            assert "retry_after" in result
    
    @pytest.mark.asyncio
    async def test_execute_parallel_agents_success(self, claude_service, mock_messages):
        """Test successful parallel agent execution"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to return messages for all agents
            async def mock_query_generator(*args, **kwargs):
                for message in mock_messages:
                    yield message
            
            mock_query.return_value = mock_query_generator()
            
            # Prepare agent configurations
            agent_configs = [
                {
                    "prompt": "Analyze acceptance criteria",
                    "agent_type": "acceptance_criteria",
                    "working_directory": Path("/tmp/test"),
                    "max_turns": 3
                },
                {
                    "prompt": "Find security vulnerabilities",
                    "agent_type": "security_analysis",
                    "working_directory": Path("/tmp/test"),
                    "max_turns": 5
                },
                {
                    "prompt": "Detect bugs and issues",
                    "agent_type": "bug_detection",
                    "working_directory": Path("/tmp/test"),
                    "max_turns": 4
                }
            ]
            
            # Execute parallel agents
            result = await claude_service.execute_parallel_agents(agent_configs)
            
            # Assertions
            assert result["success"] is True
            assert len(result["agent_results"]) == 3
            assert "acceptance_criteria" in result["agent_results"]
            assert "security_analysis" in result["agent_results"]
            assert "bug_detection" in result["agent_results"]
            
            stats = result["execution_stats"]
            assert stats["total_agents"] == 3
            assert stats["successful_agents"] == 3
            assert stats["failed_agents"] == 0
            assert stats["parallel_speedup"] == "3x"
    
    @pytest.mark.asyncio
    async def test_execute_parallel_agents_partial_failure(self, claude_service, mock_messages):
        """Test parallel execution with some agent failures"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to fail for specific agents
            async def mock_query_generator(*args, **kwargs):
                if "security" in str(args) or "security" in str(kwargs):
                    raise Exception("Security agent failed")
                for message in mock_messages:
                    yield message
            
            mock_query.return_value = mock_query_generator()
            
            agent_configs = [
                {"prompt": "AC analysis", "agent_type": "acceptance_criteria"},
                {"prompt": "Security check", "agent_type": "security_analysis"},  # This will fail
                {"prompt": "Bug detection", "agent_type": "bug_detection"}
            ]
            
            result = await claude_service.execute_parallel_agents(agent_configs)
            
            # Should still be successful if at least one agent succeeds
            assert result["success"] is True
            
            stats = result["execution_stats"]
            assert stats["total_agents"] == 3
            assert stats["successful_agents"] == 2
            assert stats["failed_agents"] == 1
            
            # Check that security agent failed
            assert result["agent_results"]["security_analysis"]["success"] is False
            assert "failed" in result["agent_results"]["security_analysis"]["error"]
    
    @pytest.mark.asyncio
    async def test_execute_parallel_agents_all_fail(self, claude_service):
        """Test parallel execution when all agents fail"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to always fail
            async def mock_failing_generator(*args, **kwargs):
                raise Exception("All agents failed")
            
            mock_query.return_value = mock_failing_generator()
            
            agent_configs = [
                {"prompt": "Test1", "agent_type": "agent1"},
                {"prompt": "Test2", "agent_type": "agent2"}
            ]
            
            result = await claude_service.execute_parallel_agents(agent_configs)
            
            assert result["success"] is False  # No successful agents
            
            stats = result["execution_stats"]
            assert stats["successful_agents"] == 0
            assert stats["failed_agents"] == 2
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, claude_service):
        """Test successful health check"""
        
        with patch('src.services.claude_service.query') as mock_query:
            async def mock_health_query(*args, **kwargs):
                yield MockMessage("Hello! I'm available and ready to help.")
            
            mock_query.return_value = mock_health_query()
            
            result = await claude_service.health_check()
            
            assert result["healthy"] is True
            assert result["sdk_available"] is True
            assert "available and responding" in result["message"]
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, claude_service):
        """Test health check failure"""
        
        with patch('src.services.claude_service.query') as mock_query:
            # Setup mock to raise exception
            async def mock_failing_health_query(*args, **kwargs):
                raise Exception("Claude SDK not available")
            
            mock_query.return_value = mock_failing_health_query()
            
            result = await claude_service.health_check()
            
            assert result["healthy"] is False
            assert result["sdk_available"] is False
            assert "Health check failed" in result["error"]
    
    def test_create_claude_options(self, claude_service):
        """Test Claude options creation"""
        
        options = claude_service._create_claude_options(
            max_turns=5,
            system_prompt="You are a security expert",
            working_directory=Path("/tmp/test"),
            allowed_tools=["Read", "Grep"],
            timeout=300
        )
        
        assert options.max_turns == 5
        assert options.system_prompt == "You are a security expert"
        assert options.cwd == Path("/tmp/test")
        assert options.allowed_tools == ["Read", "Grep"]
        assert options.permission_mode == "acceptEdits"
    
    def test_extract_response_text(self, claude_service, mock_messages):
        """Test response text extraction from messages"""
        
        response_text = claude_service._extract_response_text(mock_messages)
        
        assert "Analysis complete" in response_text
        assert "Security issue in auth.py" in response_text
    
    def test_extract_response_text_empty(self, claude_service):
        """Test response text extraction with empty messages"""
        
        response_text = claude_service._extract_response_text([])
        
        assert response_text == ""
    
    def test_message_to_dict(self, claude_service):
        """Test message conversion to dictionary"""
        
        message = MockMessage("Test content", "text")
        result = claude_service._message_to_dict(message)
        
        assert result["content"] == "Test content"
        assert result["type"] == "text"
        assert "timestamp" in result
    
    @pytest.mark.asyncio
    async def test_rate_limit_cleanup(self, claude_service):
        """Test rate limit tracker cleanup"""
        
        import time
        
        # Add old entries
        claude_service._rate_limit_tracker = {
            "old_request_1": time.time() - 120,  # 2 minutes ago
            "old_request_2": time.time() - 90,   # 1.5 minutes ago
            "recent_request": time.time() - 30,  # 30 seconds ago
        }
        
        # This should clean up old entries
        await claude_service._check_rate_limits("test_agent")
        
        # Only recent request should remain
        assert len(claude_service._rate_limit_tracker) == 2  # recent + new request
    
    @pytest.mark.parametrize("agent_type,expected_in_prompt", [
        ("security_analysis", "security"),
        ("bug_detection", "bug"),
        ("acceptance_criteria", "acceptance"),
        ("code_quality", "quality")
    ])
    @pytest.mark.asyncio
    async def test_different_agent_types(self, claude_service, mock_messages, agent_type, expected_in_prompt):
        """Test different agent types are handled correctly"""
        
        with patch('src.services.claude_service.query') as mock_query:
            async def mock_query_generator(*args, **kwargs):
                for message in mock_messages:
                    yield message
            
            mock_query.return_value = mock_query_generator()
            
            result = await claude_service.execute_agent_query(
                prompt=f"Perform {expected_in_prompt} analysis",
                agent_type=agent_type
            )
            
            assert result["success"] is True
            assert result["agent_type"] == agent_type


# Integration-style tests that test the actual workflow
class TestClaudeServiceIntegration:
    """Integration tests for Claude Service workflows"""
    
    @pytest.mark.asyncio
    async def test_full_multi_agent_workflow(self):
        """Test complete multi-agent workflow simulation"""
        
        service = ClaudeService()
        
        with patch('src.services.claude_service.query') as mock_query:
            # Simulate different responses for different agents
            async def mock_workflow_generator(*args, **kwargs):
                prompt = str(args[0]) if args else str(kwargs.get('prompt', ''))
                
                if 'acceptance' in prompt.lower():
                    yield MockMessage("✅ All acceptance criteria are met")
                elif 'security' in prompt.lower():
                    yield MockMessage("🔒 Found 2 security vulnerabilities")
                elif 'bug' in prompt.lower():
                    yield MockMessage("🐛 Detected 1 potential bug")
                else:
                    yield MockMessage("✨ Analysis complete")
            
            mock_query.return_value = mock_workflow_generator()
            
            # Simulate full 7-agent review
            agent_configs = [
                {"prompt": "Check acceptance criteria", "agent_type": "acceptance_criteria"},
                {"prompt": "Find security issues", "agent_type": "security_analysis"},
                {"prompt": "Detect bugs", "agent_type": "bug_detection"},
                {"prompt": "Analyze logic", "agent_type": "logic_analysis"},
                {"prompt": "Check code quality", "agent_type": "code_quality"},
                {"prompt": "Review architecture", "agent_type": "architectural"},
                {"prompt": "Generate summary", "agent_type": "summary"}
            ]
            
            result = await service.execute_parallel_agents(agent_configs)
            
            # Verify all 7 agents executed
            assert result["success"] is True
            assert len(result["agent_results"]) == 7
            
            # Verify specific agent responses
            assert "acceptance criteria are met" in result["agent_results"]["acceptance_criteria"]["response"]
            assert "security vulnerabilities" in result["agent_results"]["security_analysis"]["response"]
            assert "potential bug" in result["agent_results"]["bug_detection"]["response"]
            
            # Verify performance benefits
            stats = result["execution_stats"]
            assert stats["parallel_speedup"] == "7x"
            assert stats["successful_agents"] == 7
    
    @pytest.mark.asyncio 
    async def test_performance_comparison_simulation(self):
        """Simulate performance comparison between sequential and parallel execution"""
        
        service = ClaudeService()
        
        with patch('src.services.claude_service.query') as mock_query:
            # Simulate agent execution time
            async def mock_timed_generator(*args, **kwargs):
                await asyncio.sleep(0.1)  # Simulate 100ms per agent
                yield MockMessage("Agent work complete")
            
            mock_query.return_value = mock_timed_generator()
            
            # Test parallel execution (should be ~100ms total)
            import time
            start_time = time.time()
            
            agent_configs = [
                {"prompt": f"Agent {i} task", "agent_type": f"agent_{i}"}
                for i in range(5)
            ]
            
            result = await service.execute_parallel_agents(agent_configs)
            
            parallel_duration = time.time() - start_time
            
            # Parallel execution should be roughly equal to single agent time
            # (not 5x the single agent time)
            assert parallel_duration < 0.5  # Should be much less than 500ms (5 * 100ms)
            assert result["success"] is True
            assert len(result["agent_results"]) == 5


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])