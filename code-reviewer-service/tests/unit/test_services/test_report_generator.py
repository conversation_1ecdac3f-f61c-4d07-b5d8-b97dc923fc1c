# tests/unit/test_services/test_report_generator.py
"""
Unit Tests für ReportGenerator - Report Generation System
Tests Enhanced Review + Implementation Summary Report Generation
"""

import pytest
import asyncio
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.services.report_generator import (
    ReportGenerator, 
    ReportFormat, 
    ReportType, 
    ReportMetadata
)


class TestReportGenerator:
    """Test ReportGenerator Report Generation System"""

    @pytest.fixture
    def temp_directories(self):
        """Create temporary directories for testing"""
        temp_dir = Path(tempfile.mkdtemp())
        template_dir = temp_dir / "templates"
        output_dir = temp_dir / "reports"
        
        template_dir.mkdir(parents=True)
        output_dir.mkdir(parents=True)
        
        yield {
            'temp_dir': temp_dir,
            'template_dir': template_dir,
            'output_dir': output_dir
        }
        
        # Cleanup
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def report_config(self, temp_directories):
        """Test configuration for ReportGenerator"""
        return {
            'template_directory': str(temp_directories['template_dir']),
            'output_directory': str(temp_directories['output_dir']),
            'include_metadata': True,
            'include_timestamps': True,
            'include_performance_stats': True
        }

    @pytest.fixture
    def report_generator(self, report_config):
        """Create ReportGenerator instance for testing"""
        return ReportGenerator(report_config)

    @pytest.fixture
    def sample_agent_results(self):
        """Sample agent results for testing"""
        return {
            'security_analysis': {
                'status': 'completed',
                'processing_time': 2.5,
                'findings': [
                    {
                        'severity': 'high',
                        'message': 'Potential SQL injection vulnerability',
                        'file': 'database.py',
                        'line': 45
                    },
                    {
                        'severity': 'medium',
                        'message': 'Weak password validation',
                        'file': 'auth.py',
                        'line': 23
                    }
                ],
                'summary': 'Security analysis found 2 issues requiring attention',
                'recommendations': [
                    'Use parameterized queries to prevent SQL injection',
                    'Implement stronger password validation rules'
                ]
            },
            'code_quality': {
                'status': 'completed',
                'processing_time': 1.8,
                'findings': [
                    {
                        'severity': 'low',
                        'message': 'Function complexity too high',
                        'file': 'processor.py',
                        'line': 67
                    }
                ],
                'summary': 'Code quality analysis identified improvement opportunities',
                'recommendations': [
                    'Refactor complex functions into smaller methods'
                ]
            },
            'bug_detection': {
                'status': 'completed',
                'processing_time': 3.2,
                'findings': [
                    {
                        'severity': 'critical',
                        'message': 'Null pointer dereference risk',
                        'file': 'handler.py',
                        'line': 12
                    }
                ],
                'summary': 'Critical bug detected requiring immediate attention',
                'recommendations': [
                    'Add null checks before object dereferencing'
                ]
            }
        }

    @pytest.fixture
    def sample_summary_result(self):
        """Sample summary agent result for testing"""
        return {
            'overview': 'Implementation of multi-agent code review system with parallel execution',
            'key_changes': [
                'Added parallel agent orchestration',
                'Implemented facade pattern for context management',
                'Created comprehensive report generation system'
            ],
            'architecture_overview': 'The system uses a multi-agent architecture with parallel execution',
            'implementation_approach': 'Strategy pattern for agents, facade pattern for contexts',
            'tutorial_sections': [
                {
                    'title': 'Getting Started',
                    'content': 'This section explains how to set up the system'
                },
                {
                    'title': 'Advanced Usage',
                    'content': 'Advanced configuration and customization options'
                }
            ],
            'diagrams': [
                {
                    'title': 'System Architecture',
                    'content': 'graph TD\n    A[Input] --> B[Agents]\n    B --> C[Reports]'
                }
            ],
            'code_examples': [
                {
                    'title': 'Basic Usage',
                    'language': 'python',
                    'code': 'generator = ReportGenerator()\nreport = await generator.generate_report()'
                }
            ],
            'next_steps': [
                'Deploy to production environment',
                'Set up monitoring and alerting',
                'Train team on new system'
            ],
            'resources': [
                'Internal documentation wiki',
                'Training materials repository',
                'Best practices guide'
            ]
        }

    # ==================== INITIALIZATION TESTS ====================

    def test_report_generator_initialization(self, report_config):
        """Test ReportGenerator initialization"""
        generator = ReportGenerator(report_config)
        
        assert generator.config == report_config
        assert generator.include_metadata == True
        assert generator.include_timestamps == True
        assert generator.include_performance_stats == True
        assert generator.generation_stats['reports_generated'] == 0

    def test_report_generator_default_config(self, temp_directories):
        """Test ReportGenerator with default configuration"""
        generator = ReportGenerator()
        
        assert generator.include_metadata == True
        assert generator.include_timestamps == True
        assert generator.include_performance_stats == True
        assert generator.output_directory.exists()

    def test_template_loading(self, report_generator):
        """Test template loading functionality"""
        # Templates should be loaded during initialization
        assert 'enhanced_review_template' in report_generator.templates
        assert 'implementation_summary_template' in report_generator.templates
        
        # Template metadata should be available
        assert 'enhanced_review_template' in report_generator.template_metadata
        assert 'implementation_summary_template' in report_generator.template_metadata

    # ==================== ENHANCED REVIEW REPORT TESTS ====================

    @pytest.mark.asyncio
    async def test_generate_enhanced_review_markdown(self, report_generator, sample_agent_results):
        """Test Enhanced Review Report generation in Markdown format"""
        metadata = await report_generator.generate_enhanced_review_report(
            agent_results=sample_agent_results,
            format=ReportFormat.MARKDOWN
        )
        
        assert isinstance(metadata, ReportMetadata)
        assert metadata.report_type == ReportType.ENHANCED_REVIEW
        assert metadata.format == ReportFormat.MARKDOWN
        assert metadata.agent_results_count == 3
        assert metadata.output_path is not None
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        assert output_path.suffix == '.md'
        
        # Check content
        content = output_path.read_text()
        assert 'Enhanced Code Review Report' in content
        assert 'security_analysis' in content
        assert 'CRITICAL' in content  # From bug_detection

    @pytest.mark.asyncio
    async def test_generate_enhanced_review_html(self, report_generator, sample_agent_results):
        """Test Enhanced Review Report generation in HTML format"""
        metadata = await report_generator.generate_enhanced_review_report(
            agent_results=sample_agent_results,
            format=ReportFormat.HTML
        )
        
        assert metadata.format == ReportFormat.HTML
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        assert output_path.suffix == '.html'
        
        # Check HTML content
        content = output_path.read_text()
        assert '<html>' in content
        assert '<body>' in content
        assert 'Enhanced Code Review Report' in content

    @pytest.mark.asyncio
    async def test_generate_enhanced_review_json(self, report_generator, sample_agent_results):
        """Test Enhanced Review Report generation in JSON format"""
        metadata = await report_generator.generate_enhanced_review_report(
            agent_results=sample_agent_results,
            format=ReportFormat.JSON
        )
        
        assert metadata.format == ReportFormat.JSON
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        assert output_path.suffix == '.json'
        
        # Check JSON content
        import json
        with open(output_path) as f:
            json_data = json.load(f)
        
        assert json_data['report_type'] == 'enhanced_review'
        assert 'content' in json_data
        assert 'generated_at' in json_data

    @pytest.mark.asyncio
    async def test_enhanced_review_custom_filename(self, report_generator, sample_agent_results):
        """Test Enhanced Review Report with custom filename"""
        custom_filename = "custom_review_report.md"
        
        metadata = await report_generator.generate_enhanced_review_report(
            agent_results=sample_agent_results,
            format=ReportFormat.MARKDOWN,
            output_filename=custom_filename
        )
        
        assert Path(metadata.output_path).name == custom_filename

    # ==================== IMPLEMENTATION SUMMARY REPORT TESTS ====================

    @pytest.mark.asyncio
    async def test_generate_implementation_summary_markdown(self, report_generator, sample_summary_result):
        """Test Implementation Summary Report generation in Markdown format"""
        metadata = await report_generator.generate_implementation_summary_report(
            summary_agent_result=sample_summary_result,
            format=ReportFormat.MARKDOWN
        )
        
        assert isinstance(metadata, ReportMetadata)
        assert metadata.report_type == ReportType.IMPLEMENTATION_SUMMARY
        assert metadata.format == ReportFormat.MARKDOWN
        assert metadata.agent_results_count == 1
        assert metadata.output_path is not None
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        assert output_path.suffix == '.md'
        
        # Check content
        content = output_path.read_text()
        assert 'Implementation Summary Report' in content
        assert 'Getting Started' in content  # From tutorial sections
        assert 'System Architecture' in content  # From diagrams

    @pytest.mark.asyncio
    async def test_generate_implementation_summary_html(self, report_generator, sample_summary_result):
        """Test Implementation Summary Report generation in HTML format"""
        metadata = await report_generator.generate_implementation_summary_report(
            summary_agent_result=sample_summary_result,
            format=ReportFormat.HTML
        )
        
        assert metadata.format == ReportFormat.HTML
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        assert output_path.suffix == '.html'
        
        # Check HTML content
        content = output_path.read_text()
        assert '<html>' in content
        assert 'Implementation Summary Report' in content

    # ==================== COMBINED REPORT TESTS ====================

    @pytest.mark.asyncio
    async def test_generate_combined_report(self, report_generator, sample_agent_results, sample_summary_result):
        """Test Combined Report generation"""
        metadata = await report_generator.generate_combined_report(
            agent_results=sample_agent_results,
            summary_agent_result=sample_summary_result,
            format=ReportFormat.MARKDOWN
        )
        
        assert isinstance(metadata, ReportMetadata)
        assert metadata.report_type == ReportType.COMBINED
        assert metadata.format == ReportFormat.MARKDOWN
        assert metadata.agent_results_count == 4  # 3 agents + 1 summary
        
        # Check file was created
        output_path = Path(metadata.output_path)
        assert output_path.exists()
        
        # Check content contains both reports
        content = output_path.read_text()
        assert 'Enhanced Code Review Report' in content
        assert 'Implementation Summary' in content
        assert 'security_analysis' in content
        assert 'Getting Started' in content

    # ==================== DATA PREPARATION TESTS ====================

    @pytest.mark.asyncio
    async def test_prepare_enhanced_review_data(self, report_generator, sample_agent_results):
        """Test enhanced review data preparation"""
        data = await report_generator._prepare_enhanced_review_data(sample_agent_results)
        
        assert 'metadata' in data
        assert 'agent_results' in data
        assert 'summary' in data
        
        # Check metadata
        assert data['metadata']['total_agents'] == 3
        assert data['metadata']['total_findings'] == 4
        
        # Check severity summary
        severity_summary = data['metadata']['severity_summary']
        assert severity_summary['critical'] == 1
        assert severity_summary['high'] == 1
        assert severity_summary['medium'] == 1
        assert severity_summary['low'] == 1

    @pytest.mark.asyncio
    async def test_prepare_implementation_summary_data(self, report_generator, sample_summary_result):
        """Test implementation summary data preparation"""
        data = await report_generator._prepare_implementation_summary_data(sample_summary_result)
        
        assert 'metadata' in data
        assert 'summary' in data
        assert 'tutorial_sections' in data
        assert 'diagrams' in data
        
        # Check metadata
        assert data['metadata']['has_diagrams'] == True
        assert data['metadata']['has_tutorial'] == True
        
        # Check sections
        assert len(data['tutorial_sections']) == 2
        assert len(data['diagrams']) == 1

    # ==================== TEMPLATE PROCESSING TESTS ====================

    @pytest.mark.asyncio
    async def test_template_rendering(self, report_generator):
        """Test template rendering with data"""
        test_data = {
            'metadata': {
                'generated_at': '2024-01-01T12:00:00',
                'total_agents': 3
            },
            'summary': {
                'overall_status': 'HIGH PRIORITY ISSUES'
            }
        }
        
        rendered = await report_generator._render_template(
            template_name='enhanced_review_template',
            data=test_data
        )
        
        assert '2024-01-01T12:00:00' in rendered
        assert '3' in rendered
        assert 'HIGH PRIORITY ISSUES' in rendered

    def test_flatten_dict(self, report_generator):
        """Test dictionary flattening for template variables"""
        nested_dict = {
            'metadata': {
                'generated_at': '2024-01-01',
                'counts': {
                    'total': 5
                }
            },
            'status': 'active'
        }
        
        flattened = report_generator._flatten_dict(nested_dict)
        
        assert 'metadata.generated_at' in flattened
        assert 'metadata.counts.total' in flattened
        assert 'status' in flattened
        assert flattened['metadata.generated_at'] == '2024-01-01'
        assert flattened['metadata.counts.total'] == '5'

    # ==================== UTILITY TESTS ====================

    def test_calculate_severity_counts(self, report_generator):
        """Test severity count calculation"""
        findings = [
            {'severity': 'critical'},
            {'severity': 'high'},
            {'severity': 'high'},
            {'severity': 'medium'},
            {'severity': 'low'},
            {'severity': 'info'}
        ]
        
        counts = report_generator._calculate_severity_counts(findings)
        
        assert counts['critical'] == 1
        assert counts['high'] == 2
        assert counts['medium'] == 1
        assert counts['low'] == 1
        assert counts['info'] == 1

    def test_determine_overall_status(self, report_generator):
        """Test overall status determination"""
        # Critical issues
        severity_critical = {'critical': 1, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        assert report_generator._determine_overall_status(severity_critical) == "CRITICAL ISSUES FOUND"
        
        # High priority issues
        severity_high = {'critical': 0, 'high': 2, 'medium': 0, 'low': 0, 'info': 0}
        assert report_generator._determine_overall_status(severity_high) == "HIGH PRIORITY ISSUES"
        
        # No significant issues
        severity_clean = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 1}
        assert report_generator._determine_overall_status(severity_clean) == "NO SIGNIFICANT ISSUES"

    def test_extract_key_issues(self, report_generator, sample_agent_results):
        """Test key issues extraction"""
        key_issues = report_generator._extract_key_issues(sample_agent_results)
        
        assert len(key_issues) == 2  # high and critical issues
        assert any('SQL injection' in issue for issue in key_issues)
        assert any('Null pointer' in issue for issue in key_issues)

    # ==================== FORMAT CONVERSION TESTS ====================

    def test_markdown_to_html_conversion(self, report_generator):
        """Test markdown to HTML conversion"""
        markdown_content = "# Test Header\n\nThis is **bold** text."
        
        html_content = report_generator._convert_markdown_to_html(markdown_content)
        
        assert '<html>' in html_content
        assert '<h1>Test Header</h1>' in html_content
        assert '<strong>bold</strong>' in html_content

    # ==================== STATISTICS TESTS ====================

    def test_generation_stats_tracking(self, report_generator):
        """Test generation statistics tracking"""
        initial_stats = report_generator.get_generation_stats()
        assert initial_stats['reports_generated'] == 0
        
        # Create a metadata object to simulate report generation
        metadata = ReportMetadata(
            report_id="test_123",
            report_type=ReportType.ENHANCED_REVIEW,
            format=ReportFormat.MARKDOWN,
            generated_at=datetime.now(),
            agent_results_count=3,
            template_version="1.0",
            processing_time_seconds=2.5
        )
        
        report_generator._update_generation_stats(metadata)
        
        updated_stats = report_generator.get_generation_stats()
        assert updated_stats['reports_generated'] == 1
        assert updated_stats['by_format']['md'] == 1
        assert updated_stats['by_type']['enhanced_review'] == 1

    def test_list_available_templates(self, report_generator):
        """Test template listing"""
        templates = report_generator.list_available_templates()
        
        assert 'enhanced_review_template' in templates
        assert 'implementation_summary_template' in templates
        
        # Check metadata structure
        template_info = templates['enhanced_review_template']
        assert 'path' in template_info
        assert 'loaded_at' in template_info
        assert 'size' in template_info

    def test_get_supported_formats(self, report_generator):
        """Test supported formats listing"""
        formats = report_generator.get_supported_formats()
        
        assert 'md' in formats
        assert 'html' in formats
        assert 'json' in formats
        # PDF might not be available depending on dependencies

    def test_clear_output_directory(self, report_generator):
        """Test output directory clearing"""
        # Create some test files
        output_dir = report_generator.output_directory
        test_file1 = output_dir / "test1.md"
        test_file2 = output_dir / "test2.html"
        
        test_file1.write_text("test content")
        test_file2.write_text("test content")
        
        # Clear directory
        removed_count = report_generator.clear_output_directory()
        
        assert removed_count == 2
        assert not test_file1.exists()
        assert not test_file2.exists()

    # ==================== ERROR HANDLING TESTS ====================

    @pytest.mark.asyncio
    async def test_invalid_template_name(self, report_generator):
        """Test handling of invalid template name"""
        with pytest.raises(ValueError, match="Template not found"):
            await report_generator._render_template("nonexistent_template", {})

    @pytest.mark.asyncio
    async def test_unsupported_format(self, report_generator, sample_agent_results):
        """Test handling of unsupported format"""
        # This would require modifying the enum, so we'll test the export method directly
        with pytest.raises(ValueError, match="Unsupported format"):
            await report_generator._export_report(
                content="test",
                format="unsupported",  # This would need to be passed as a string
                filename="test.txt",
                report_type=ReportType.ENHANCED_REVIEW
            )

    @pytest.mark.asyncio
    async def test_empty_agent_results(self, report_generator):
        """Test handling of empty agent results"""
        empty_results = {}
        
        metadata = await report_generator.generate_enhanced_review_report(
            agent_results=empty_results,
            format=ReportFormat.MARKDOWN
        )
        
        assert metadata.agent_results_count == 0
        
        # Check file was still created
        output_path = Path(metadata.output_path)
        assert output_path.exists()


if __name__ == "__main__":
    pytest.main([__file__])