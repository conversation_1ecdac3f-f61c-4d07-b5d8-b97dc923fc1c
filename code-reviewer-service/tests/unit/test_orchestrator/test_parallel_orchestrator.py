"""
Unit tests for ParallelMultiAgentOrchestrator

Tests the core orchestration logic for parallel agent execution.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
from pathlib import Path

from src.orchestrator.parallel_orchestrator import (
    ParallelMultiAgentOrchestrator,
    CircuitBreakerError,
    OrchestratorError
)
from src.agents.models import AgentResult, MultiAgentResult, AgentExecutionStatus
from src.agents.factories import AgentFactory
from src.api.websockets.multi_agent_manager import MultiAgentWebSocketManager
from src.config.settings import Settings


class TestParallelMultiAgentOrchestrator:
    """Test suite for ParallelMultiAgentOrchestrator."""
    
    @pytest.fixture
    def mock_agent_factory(self):
        """Create mock agent factory."""
        factory = AsyncMock(spec=AgentFactory)
        factory.health_check.return_value = {"factory_status": "healthy"}
        return factory
    
    @pytest.fixture  
    def mock_websocket_manager(self):
        """Create mock WebSocket manager."""
        manager = AsyncMock(spec=MultiAgentWebSocketManager)
        manager.get_connection_count.return_value = 0
        return manager
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock(spec=Settings)
        return settings
    
    @pytest.fixture
    def sample_context(self, tmp_path):
        """Create sample execution context."""
        return {
            "working_path": str(tmp_path),
            "git_branch": "feature/test-branch",
            "jira_ticket_id": "TEST-123"
        }
    
    @pytest.fixture
    def orchestrator(self, mock_agent_factory, mock_websocket_manager, mock_settings):
        """Create orchestrator instance."""
        return ParallelMultiAgentOrchestrator(
            agent_factory=mock_agent_factory,
            websocket_manager=mock_websocket_manager,
            settings=mock_settings,
            max_concurrent_agents=7,
            global_timeout_seconds=600
        )
    
    def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization."""
        assert orchestrator.max_concurrent_agents == 7
        assert orchestrator.global_timeout_seconds == 600
        assert orchestrator._circuit_breaker_failures == 0
        assert orchestrator._circuit_breaker_open == False
        assert orchestrator._is_running == False
    
    def test_get_orchestrator_status(self, orchestrator):
        """Test status reporting."""
        status = orchestrator.get_orchestrator_status()
        
        assert "is_running" in status
        assert "circuit_breaker_open" in status
        assert "max_concurrent_agents" in status
        assert status["max_concurrent_agents"] == 7
        assert status["is_running"] == False
    
    def test_validate_context_success(self, orchestrator, sample_context):
        """Test context validation success."""
        # Should not raise exception
        orchestrator._validate_context(sample_context)
    
    def test_validate_context_missing_working_path(self, orchestrator):
        """Test context validation with missing working path."""
        invalid_context = {"git_branch": "test"}
        
        with pytest.raises(OrchestratorError, match="Missing required context keys"):
            orchestrator._validate_context(invalid_context)
    
    def test_validate_context_nonexistent_path(self, orchestrator):
        """Test context validation with non-existent path."""
        invalid_context = {"working_path": "/nonexistent/path"}
        
        with pytest.raises(OrchestratorError, match="Working path does not exist"):
            orchestrator._validate_context(invalid_context)
    
    def test_create_agents(self, orchestrator, mock_agent_factory):
        """Test agent creation."""
        # Mock factory methods
        mock_agent = MagicMock()
        mock_agent.agent_id = "test_agent"
        mock_agent.agent_type = "acceptance_criteria"
        
        mock_agent_factory.is_registered.return_value = True
        mock_agent_factory.create_agent.return_value = mock_agent
        
        agents = orchestrator._create_agents()
        
        # Should attempt to create 7 agents
        expected_agent_types = [
            "acceptance_criteria", "bug_detection", "security_analysis",
            "logic_analysis", "quality_analysis", "architecture_analysis", "summary"
        ]
        
        assert mock_agent_factory.is_registered.call_count == len(expected_agent_types)
        
        # Verify each agent type was checked
        for agent_type in expected_agent_types:
            mock_agent_factory.is_registered.assert_any_call(agent_type)
    
    def test_circuit_breaker_open(self, orchestrator):
        """Test circuit breaker functionality."""
        # Trigger circuit breaker
        orchestrator._circuit_breaker_failures = 3
        orchestrator._circuit_breaker_open = True
        
        assert orchestrator._is_circuit_breaker_open() == True
    
    def test_circuit_breaker_reset(self, orchestrator):
        """Test circuit breaker reset."""
        # Set failure state
        orchestrator._circuit_breaker_failures = 2
        orchestrator._circuit_breaker_open = True
        
        # Reset
        orchestrator._reset_circuit_breaker()
        
        assert orchestrator._circuit_breaker_failures == 0
        assert orchestrator._circuit_breaker_open == False
    
    @pytest.mark.asyncio
    async def test_execute_single_agent_success(self, orchestrator):
        """Test successful single agent execution."""
        # Create mock agent
        mock_agent = AsyncMock()
        mock_agent.agent_type = "acceptance_criteria"
        mock_agent.agent_id = "test_agent"
        
        # Mock successful execution
        expected_result = AgentResult(
            agent_id="test_agent",
            agent_type="acceptance_criteria"
        )
        expected_result.mark_completed(success=True)
        mock_agent.execute.return_value = expected_result
        
        context = {"working_path": "/test"}
        execution_id = uuid4()
        
        result = await orchestrator._execute_single_agent(mock_agent, context, execution_id)
        
        assert isinstance(result, AgentResult)
        assert result.success == True
        assert result.agent_type == "acceptance_criteria"
        assert str(execution_id) in result.context_data["execution_id"]
    
    @pytest.mark.asyncio
    async def test_execute_single_agent_failure(self, orchestrator):
        """Test single agent execution failure."""
        # Create mock agent that fails
        mock_agent = AsyncMock()
        mock_agent.agent_type = "acceptance_criteria"
        mock_agent.agent_id = "test_agent"
        mock_agent.execute.side_effect = Exception("Agent failed")
        
        context = {"working_path": "/test"}
        execution_id = uuid4()
        
        result = await orchestrator._execute_single_agent(mock_agent, context, execution_id)
        
        assert isinstance(result, AgentResult)
        assert result.success == False
        assert "Agent failed" in result.error_message
        assert result.agent_type == "acceptance_criteria"
    
    @pytest.mark.asyncio
    async def test_execute_parallel_review_circuit_breaker_open(self, orchestrator, sample_context):
        """Test execution with circuit breaker open."""
        # Open circuit breaker
        orchestrator._circuit_breaker_open = True
        
        with pytest.raises(CircuitBreakerError):
            await orchestrator.execute_parallel_review(sample_context)
    
    @pytest.mark.asyncio
    async def test_execute_parallel_review_success(self, orchestrator, sample_context, mock_agent_factory, mock_websocket_manager):
        """Test successful parallel review execution."""
        # Setup successful agent creation
        mock_agent = AsyncMock()
        mock_agent.agent_type = "acceptance_criteria"
        mock_agent.agent_id = "test_agent"
        
        successful_result = AgentResult(
            agent_id="test_agent",
            agent_type="acceptance_criteria"
        )
        successful_result.mark_completed(success=True)
        mock_agent.execute.return_value = successful_result
        
        mock_agent_factory.is_registered.return_value = True
        mock_agent_factory.create_agent.return_value = mock_agent
        
        # Execute
        result = await orchestrator.execute_parallel_review(sample_context)
        
        # Verify result
        assert isinstance(result, MultiAgentResult)
        assert result.overall_status in [AgentExecutionStatus.SUCCESS, AgentExecutionStatus.FAILED]
        
        # Verify WebSocket calls were made
        assert mock_websocket_manager.broadcast_agent_event.call_count >= 2  # Start and end events
    
    @pytest.mark.asyncio
    async def test_execute_agents_parallel_timeout(self, orchestrator, mock_websocket_manager):
        """Test parallel execution with global timeout."""
        # Create agents that will timeout
        slow_agent = AsyncMock()
        slow_agent.agent_type = "acceptance_criteria"
        slow_agent.agent_id = "slow_agent"
        
        # Make agent execution hang
        async def slow_execute(*args, **kwargs):
            await asyncio.sleep(10)  # Longer than test timeout
            return AgentResult(agent_id="slow_agent", agent_type="acceptance_criteria")
        
        slow_agent.execute = slow_execute
        
        agents = {"acceptance_criteria": slow_agent}
        context = {"working_path": "/test"}
        execution_id = uuid4()
        
        # Set short timeout for test
        original_timeout = orchestrator.global_timeout_seconds
        orchestrator.global_timeout_seconds = 0.1
        
        try:
            results = await orchestrator._execute_agents_parallel(agents, context, execution_id)
            
            # Should return timeout results
            assert "acceptance_criteria" in results
            result = results["acceptance_criteria"]
            assert result.status == AgentExecutionStatus.TIMEOUT
            
        finally:
            orchestrator.global_timeout_seconds = original_timeout
    
    @pytest.mark.asyncio
    async def test_health_check(self, orchestrator, mock_agent_factory, mock_websocket_manager):
        """Test health check functionality."""
        # Setup mocks
        mock_agent_factory.health_check.return_value = {"factory_status": "healthy"}
        mock_websocket_manager.get_connection_count.return_value = 5
        
        health = await orchestrator.health_check()
        
        assert health["orchestrator_status"] == "healthy"
        assert health["agent_factory_healthy"] == True
        assert health["websocket_connections"] == 5
        assert "timestamp" in health
    
    @pytest.mark.asyncio
    async def test_shutdown(self, orchestrator):
        """Test graceful shutdown."""
        # Add mock running agent
        mock_agent = AsyncMock()
        orchestrator._running_agents["test"] = mock_agent
        
        await orchestrator.shutdown()
        
        # Verify cleanup
        assert len(orchestrator._running_agents) == 0
        assert orchestrator._is_running == False
        mock_agent.cleanup.assert_called_once()


# Integration Tests

class TestParallelOrchestratorIntegration:
    """Integration tests for parallel orchestrator."""
    
    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_end_to_end_execution(self, tmp_path):
        """Test end-to-end orchestrator execution."""
        # Setup components
        settings = MagicMock(spec=Settings)
        
        # Mock agent factory with working agent
        agent_factory = AsyncMock(spec=AgentFactory)
        agent_factory.health_check.return_value = {"factory_status": "healthy"}
        agent_factory.is_registered.return_value = True
        
        # Create mock agent that returns success
        mock_agent = AsyncMock()
        mock_agent.agent_type = "acceptance_criteria"
        mock_agent.agent_id = "integration_test_agent"
        
        successful_result = AgentResult(
            agent_id="integration_test_agent",
            agent_type="acceptance_criteria"
        )
        successful_result.mark_completed(success=True)
        successful_result.result_data = {"test": "data"}
        
        mock_agent.execute.return_value = successful_result
        agent_factory.create_agent.return_value = mock_agent
        
        # Mock WebSocket manager
        websocket_manager = AsyncMock(spec=MultiAgentWebSocketManager)
        websocket_manager.get_connection_count.return_value = 0
        
        # Create orchestrator
        orchestrator = ParallelMultiAgentOrchestrator(
            agent_factory=agent_factory,
            websocket_manager=websocket_manager,
            settings=settings,
            max_concurrent_agents=1,  # Test with just one agent
            global_timeout_seconds=10
        )
        
        # Execute
        context = {"working_path": str(tmp_path)}
        result = await orchestrator.execute_parallel_review(context)
        
        # Verify results
        assert isinstance(result, MultiAgentResult)
        assert result.total_agents >= 1
        assert result.is_completed()
        
        # Verify WebSocket events were sent
        assert websocket_manager.broadcast_agent_event.call_count >= 2