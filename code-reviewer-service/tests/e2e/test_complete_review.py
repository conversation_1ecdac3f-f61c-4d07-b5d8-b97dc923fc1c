"""
End-to-End tests for complete review process.
Tests the entire system from API request to final report generation.
"""

import pytest
import asyncio
import json
import tempfile
import shutil
from pathlib import Path
from uuid import uuid4
from datetime import datetime
from unittest.mock import patch, MagicMock
import httpx
from fastapi.testclient import TestClient

from src.main import app
from src.models.api_models import (
    ReviewRequest, ReviewMode, AgentType, ReviewStatus
)


class TestCompleteReviewE2E:
    """End-to-End tests for complete review process."""
    
    @pytest.fixture
    def client(self):
        """FastAPI test client."""
        return TestClient(app)
    
    @pytest.fixture
    async def async_client(self):
        """Async FastAPI test client."""
        async with httpx.AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    async def e2e_workspace(self):
        """Create comprehensive workspace for E2E testing."""
        temp_dir = tempfile.mkdtemp(prefix="e2e_workspace_")
        workspace = Path(temp_dir)
        
        # Create complex project structure
        src_dir = workspace / "src"
        src_dir.mkdir()
        (src_dir / "__init__.py").write_text("")
        
        # API module
        api_dir = src_dir / "api"
        api_dir.mkdir()
        (api_dir / "__init__.py").write_text("")
        (api_dir / "routes.py").write_text("""
from flask import Flask, request, jsonify
import sqlite3
import hashlib

app = Flask(__name__)

@app.route('/api/users', methods=['POST'])
def create_user():
    data = request.get_json()
    
    # Security issue: No input validation
    username = data['username']
    password = data['password']
    email = data['email']
    
    # Security issue: Plain text password storage
    # Should use proper hashing
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # SQL injection vulnerability
    query = f"INSERT INTO users (username, password, email) VALUES ('{username}', '{password}', '{email}')"
    cursor.execute(query)
    
    conn.commit()
    conn.close()
    
    return jsonify({"status": "success", "user_id": cursor.lastrowid})

@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    # Logic error: No rate limiting
    # Security issue: Timing attack vulnerability
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    cursor.execute("SELECT password FROM users WHERE username = ?", (username,))
    result = cursor.fetchone()
    
    if result and result[0] == password:
        return jsonify({"status": "success", "token": "fake-jwt-token"})
    else:
        return jsonify({"status": "error", "message": "Invalid credentials"}), 401

# Performance issue: No connection pooling
# Architectural issue: Direct DB access in routes
        """)
        
        # Models module
        models_dir = src_dir / "models"
        models_dir.mkdir()
        (models_dir / "__init__.py").write_text("")
        (models_dir / "user.py").write_text("""
from dataclasses import dataclass
from typing import Optional
import re

@dataclass
class User:
    username: str
    email: str
    password: str  # Security issue: Plain text password in model
    id: Optional[int] = None
    
    def validate_email(self) -> bool:
        # Code quality issue: Weak email validation
        return '@' in self.email and '.' in self.email
    
    def validate_password(self) -> bool:
        # Security issue: Weak password validation
        return len(self.password) > 3
    
    def hash_password(self):
        # Bug: This method doesn't actually hash anything
        # Critical security flaw
        return self.password
    
    # Missing: Proper validation methods
    # Missing: Security considerations
    # Missing: Input sanitization
        """)
        
        # Utils module with performance issues
        utils_dir = src_dir / "utils"
        utils_dir.mkdir()
        (utils_dir / "__init__.py").write_text("")
        (utils_dir / "helpers.py").write_text("""
import time
import json
from typing import List, Dict, Any

# Global variables - architectural issue
CACHE = {}
CONFIG = None

def expensive_operation(data: List[Dict]) -> List[Dict]:
    # Performance issue: Inefficient algorithm
    result = []
    for item in data:
        # Nested loops causing O(n²) complexity
        for other_item in data:
            if item.get('id') == other_item.get('related_id'):
                result.append({**item, 'related': other_item})
                break
    
    # Performance issue: Blocking sleep in main thread
    time.sleep(0.1)  # Simulating slow operation
    return result

def process_user_data(user_data: Dict) -> Dict:
    # Logic error: No error handling
    processed = {}
    processed['username'] = user_data['username'].lower()
    processed['email'] = user_data['email'].strip()
    
    # Bug: Potential KeyError if 'preferences' doesn't exist
    processed['settings'] = user_data['preferences']['settings']
    
    return processed

# Memory leak: Growing cache without cleanup
def cache_data(key: str, value: Any):
    CACHE[key] = value
    # Missing: Cache size limits
    # Missing: Cache expiration

def get_cached_data(key: str) -> Any:
    # Thread safety issue
    return CACHE.get(key)

# Code duplication
def validate_input_v1(data):
    if not data:
        return False
    if not isinstance(data, dict):
        return False
    return True

def validate_input_v2(data):
    if not data:
        return False
    if not isinstance(data, dict):
        return False
    return True
        """)
        
        # Tests directory
        tests_dir = workspace / "tests"
        tests_dir.mkdir()
        (tests_dir / "__init__.py").write_text("")
        (tests_dir / "test_api.py").write_text("""
import unittest
from unittest.mock import patch
import json

# Incomplete test coverage
class TestAPI(unittest.TestCase):
    
    def test_create_user_success(self):
        # Basic happy path test only
        # Missing: Error cases, validation tests, security tests
        pass
    
    # Missing: Authentication tests
    # Missing: Input validation tests  
    # Missing: Security vulnerability tests
    # Missing: Performance tests
        """)
        
        # Configuration files
        (workspace / "requirements.txt").write_text("""
flask==2.0.1
sqlite3
pytest==6.2.4
# Missing: Security dependencies like bcrypt
# Missing: Production dependencies
        """)
        
        (workspace / "README.md").write_text("""
# User Management API

## Acceptance Criteria
- AC1: Users can register with username, email, password
- AC2: Passwords must be securely hashed and stored  
- AC3: API must validate all input data
- AC4: Authentication must be secure against common attacks
- AC5: System must handle 1000+ concurrent users
- AC6: All endpoints must have comprehensive test coverage
- AC7: API must follow REST best practices
- AC8: Security vulnerabilities must be addressed

## Known Issues
- Passwords stored in plain text (CRITICAL)
- SQL injection vulnerabilities (HIGH)
- No rate limiting (MEDIUM)
- Performance bottlenecks (MEDIUM)
        """)
        
        # Docker file with issues
        (workspace / "Dockerfile").write_text("""
FROM python:3.9

# Security issue: Running as root
USER root

# Performance issue: Not using multi-stage build
COPY . /app
WORKDIR /app

# Security issue: Installing as root, no version pinning
RUN pip install -r requirements.txt

# Security issue: Exposing sensitive port
EXPOSE 5000

# No health check
CMD ["python", "src/api/routes.py"]
        """)
        
        yield workspace
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_claude_responses(self):
        """Mock Claude SDK responses for different agents."""
        responses = {
            "acceptance_criteria": """
# Acceptance Criteria Analysis - User Management API

## ✅ Implemented Requirements
- AC1: User registration endpoint exists (/api/users POST)
- AC7: Basic REST structure implemented

## ❌ Critical Missing Requirements
- AC2: Passwords stored in plain text - FAILS SECURITY REQUIREMENT
- AC3: No input validation - FAILS SAFETY REQUIREMENT  
- AC4: Multiple security vulnerabilities identified
- AC5: No performance optimization for concurrent users
- AC6: Test coverage is minimal and incomplete
- AC8: Multiple security vulnerabilities not addressed

## Severity Assessment
- CRITICAL: 3 requirements failed (AC2, AC4, AC8)
- HIGH: 2 requirements partially met (AC3, AC6)  
- MEDIUM: 1 requirement at risk (AC5)

## Recommendations
1. URGENT: Implement password hashing (bcrypt/scrypt)
2. URGENT: Fix SQL injection vulnerabilities
3. HIGH: Add comprehensive input validation
4. HIGH: Implement rate limiting and security headers
5. MEDIUM: Add performance monitoring and optimization
            """,
            
            "bug_detection": """
# Bug Detection Analysis - Critical Issues Found

## 🚨 Critical Bugs
1. **SQL Injection Vulnerability** (api/routes.py:23)
   - Severity: CRITICAL
   - Direct string interpolation in SQL query
   - Allows database manipulation by attackers

2. **Plain Text Password Storage** (api/routes.py:25 & models/user.py:15)
   - Severity: CRITICAL  
   - Passwords stored without hashing
   - Complete credential compromise risk

3. **KeyError Exception** (utils/helpers.py:32)
   - Severity: HIGH
   - Unhandled KeyError when 'preferences' key missing
   - Will cause runtime crashes

## ⚠️ Logic Errors
1. **Fake Password Hashing** (models/user.py:21)
   - Method returns plain text instead of hash
   - Creates false security assumption

2. **Memory Leak** (utils/helpers.py:45)
   - Unbounded cache growth
   - Will cause memory exhaustion

## 🐛 Code Quality Issues
1. **O(n²) Algorithm** (utils/helpers.py:15)
   - Inefficient nested loops
   - Performance degrades with data size

2. **Code Duplication** (utils/helpers.py:55-65)
   - Identical validation functions
   - Maintenance overhead

## Recommendations
- IMMEDIATE: Fix SQL injection and password storage
- HIGH: Add proper error handling and input validation
- MEDIUM: Optimize algorithms and reduce code duplication
            """,
            
            "security_analysis": """
# Security Analysis - Multiple Critical Vulnerabilities

## 🔴 Critical Security Issues

### 1. SQL Injection (OWASP A03)
- **Location**: api/routes.py:23
- **Risk**: Complete database compromise
- **CVSS Score**: 9.8 (Critical)
- **Fix**: Use parameterized queries

### 2. Plain Text Password Storage
- **Location**: Multiple files
- **Risk**: Mass credential theft
- **CVSS Score**: 9.1 (Critical)  
- **Fix**: Implement bcrypt hashing

### 3. Missing Authentication/Authorization
- **Location**: All endpoints
- **Risk**: Unauthorized access
- **CVSS Score**: 8.2 (High)
- **Fix**: Add JWT token validation

## 🟡 High Security Issues

### 4. Timing Attack Vulnerability
- **Location**: api/routes.py:45
- **Risk**: Username enumeration
- **Fix**: Constant-time comparison

### 5. Missing Input Validation  
- **Location**: All API endpoints
- **Risk**: Various injection attacks
- **Fix**: Add comprehensive validation

### 6. No Rate Limiting
- **Location**: All endpoints
- **Risk**: Brute force attacks
- **Fix**: Implement rate limiting

## 🔵 Medium Security Issues

### 7. Docker Security Issues
- **Location**: Dockerfile
- **Risk**: Container compromise
- **Fixes**: Use non-root user, pin versions

### 8. Missing Security Headers
- **Risk**: XSS, clickjacking
- **Fix**: Add security headers

## Security Score: 2.3/10 (CRITICAL)

## Immediate Actions Required
1. Fix SQL injection vulnerabilities
2. Implement password hashing
3. Add input validation  
4. Implement proper authentication
5. Add rate limiting
            """,
            
            "code_quality": """
# Code Quality Analysis

## 📊 Quality Metrics
- **Maintainability Index**: 32/100 (Poor)
- **Cyclomatic Complexity**: High in multiple functions
- **Code Duplication**: 15% (Above threshold)  
- **Test Coverage**: 10% (Critical)

## 🔧 Code Quality Issues

### 1. Poor Separation of Concerns
- Database logic mixed with API logic
- No service layer abstraction
- Tight coupling between components

### 2. Missing Error Handling
- No try-catch blocks for database operations
- Unhandled exceptions can crash application
- No graceful error responses

### 3. Performance Anti-patterns
- O(n²) algorithms in utils/helpers.py
- No connection pooling
- Blocking operations in main thread

### 4. Code Duplication
- Duplicate validation functions
- Repeated database connection patterns
- Should use DRY principles

### 5. Poor Variable Naming
- Generic names like 'data', 'result'
- No type hints consistently applied
- Magic numbers without constants

### 6. Missing Documentation
- No docstrings for functions
- No API documentation
- No code comments for complex logic

## 🧪 Testing Issues
- Minimal test coverage (10%)
- No integration tests
- No security tests
- No performance tests

## Recommendations
1. Refactor into layered architecture  
2. Add comprehensive error handling
3. Improve algorithm efficiency
4. Eliminate code duplication
5. Add proper type hints and documentation
6. Achieve 90%+ test coverage
            """,
            
            "performance_analysis": """
# Performance Analysis

## ⚡ Performance Issues Identified

### 1. Database Performance
- **Issue**: No connection pooling
- **Impact**: Connection overhead on each request
- **Location**: api/routes.py
- **Fix**: Implement connection pooling

### 2. Algorithm Efficiency  
- **Issue**: O(n²) nested loops
- **Impact**: Performance degrades with data size
- **Location**: utils/helpers.py:15
- **Fix**: Use hash maps for O(n) lookup

### 3. Blocking Operations
- **Issue**: time.sleep() in main thread
- **Impact**: Blocks entire application
- **Location**: utils/helpers.py:25
- **Fix**: Use async operations

### 4. Memory Leaks
- **Issue**: Unbounded cache growth
- **Impact**: Memory exhaustion over time
- **Location**: utils/helpers.py:45
- **Fix**: Implement cache size limits and expiration

## 📈 Performance Metrics
- **Expected Response Time**: <100ms
- **Current Response Time**: ~500ms (5x slower)
- **Memory Usage**: Growing (leak detected)
- **CPU Usage**: High due to inefficient algorithms

## Load Testing Results (Simulated)
- **100 concurrent users**: Response time degradation
- **1000 concurrent users**: System failure likely
- **Current capacity**: ~50 concurrent users

## Recommendations
1. URGENT: Fix memory leak in cache
2. HIGH: Optimize O(n²) algorithms  
3. HIGH: Implement database connection pooling
4. MEDIUM: Add async operations for I/O
5. MEDIUM: Implement caching strategy
6. LOW: Add performance monitoring
            """,
            
            "architectural_analysis": """
# Architectural Analysis

## 🏗️ Current Architecture Assessment

### Architecture Violations
1. **Monolithic Structure**
   - All logic in single layer
   - No separation of concerns
   - Difficult to test and maintain

2. **No Dependency Injection**
   - Hard-coded dependencies
   - Difficult to unit test
   - Tight coupling

3. **Missing Abstraction Layers**
   - Direct database access in routes
   - No service layer
   - No repository pattern

### 🎯 Recommended Architecture

```
┌─────────────────┐
│   API Layer     │ ← Routes & Controllers
├─────────────────┤
│  Service Layer  │ ← Business Logic
├─────────────────┤
│Repository Layer │ ← Data Access
├─────────────────┤
│  Database       │ ← Persistence
└─────────────────┘
```

### 🔧 Refactoring Recommendations

1. **Implement Layered Architecture**
   - Separate API, service, and data layers
   - Use dependency injection
   - Apply SOLID principles

2. **Add Proper Error Handling**
   - Global exception handlers
   - Structured error responses
   - Logging and monitoring

3. **Implement Design Patterns**
   - Repository pattern for data access
   - Factory pattern for object creation
   - Observer pattern for events

4. **Security Architecture**
   - Authentication middleware
   - Authorization guards
   - Input validation layer

### 📊 Technical Debt
- **High**: Monolithic structure
- **High**: No testing architecture
- **Medium**: Missing configuration management
- **Medium**: No logging strategy

## Migration Strategy
1. Phase 1: Add service layer
2. Phase 2: Implement repository pattern  
3. Phase 3: Add proper error handling
4. Phase 4: Security hardening
            """,
            
            "summary_agent": """
# Implementation Summary - User Management API

## 🎯 Project Overview
A Flask-based user management API with critical security vulnerabilities and architectural issues requiring immediate attention.

## 📋 Key Findings Summary

### Critical Issues (Immediate Action Required)
1. **SQL Injection Vulnerabilities** - Complete database compromise risk
2. **Plain Text Password Storage** - All credentials at risk
3. **Missing Authentication** - No access control
4. **Memory Leaks** - System stability at risk

### High Priority Issues  
1. **Poor Architecture** - Monolithic, tightly coupled
2. **Missing Input Validation** - Various attack vectors
3. **Performance Bottlenecks** - Cannot scale
4. **Inadequate Testing** - 10% coverage only

### Implementation Quality Score: 2.8/10

## 🔧 Recommended Implementation Approach

### Phase 1: Security Hardening (Week 1)
```python
# 1. Fix SQL Injection
cursor.execute("INSERT INTO users (username, password, email) VALUES (?, ?, ?)", 
               (username, hashed_password, email))

# 2. Implement Password Hashing  
import bcrypt
hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

# 3. Add Input Validation
from marshmallow import Schema, fields
class UserSchema(Schema):
    username = fields.Str(required=True, validate=length(min=3, max=50))
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=length(min=8))
```

### Phase 2: Architecture Refactoring (Week 2)
- Implement service layer pattern
- Add repository pattern for data access
- Separate business logic from API routes
- Add dependency injection

### Phase 3: Performance Optimization (Week 3)  
- Fix O(n²) algorithms
- Implement connection pooling
- Add caching strategy
- Remove memory leaks

### Phase 4: Testing & Quality (Week 4)
- Achieve 90%+ test coverage
- Add integration and security tests
- Implement CI/CD pipeline
- Add monitoring and logging

## 📈 Expected Outcomes
- **Security Score**: 2.3/10 → 9.5/10
- **Performance**: 500ms → <100ms response time
- **Maintainability**: Poor → Good
- **Test Coverage**: 10% → 90%+

## 🎓 Learning Resources
1. OWASP Security Guidelines
2. Flask Security Best Practices  
3. Database Security Patterns
4. Performance Optimization Techniques

This implementation requires immediate security attention before any production deployment.
            """
        }
        return responses
    
    @pytest.mark.asyncio
    async def test_complete_review_workflow_e2e(self, async_client, e2e_workspace, mock_claude_responses):
        """Test complete end-to-end review workflow via API."""
        
        with patch('src.services.claude_service.query') as mock_claude:
            # Setup mock responses
            async def mock_query_response(prompt, **kwargs):
                # Determine agent type from prompt
                for agent_type, response in mock_claude_responses.items():
                    if agent_type.replace('_', ' ') in prompt.lower():
                        yield response
                        return
                # Default response
                yield "Analysis completed successfully"
            
            mock_claude.side_effect = mock_query_response
            
            # Create review request
            review_request = {
                "branch_name": "feature/user-management-api",
                "workspace_path": str(e2e_workspace),
                "review_mode": "full",
                "include_context": True,
                "websocket_session_id": str(uuid4())
            }
            
            # Start review
            response = await async_client.post("/api/v1/review/start", json=review_request)
            assert response.status_code == 200
            
            start_data = response.json()
            review_id = start_data["review_id"]
            assert review_id is not None
            
            # Wait for review to complete (with timeout)
            max_wait = 60  # 60 seconds timeout
            wait_time = 0
            status = "running"
            
            while status in ["pending", "running"] and wait_time < max_wait:
                await asyncio.sleep(2)
                wait_time += 2
                
                status_response = await async_client.get(f"/api/v1/review/{review_id}/status")
                assert status_response.status_code == 200
                
                status_data = status_response.json()
                status = status_data["status"]
                
                # Check agent progress
                if "agent_status" in status_data:
                    agent_statuses = status_data["agent_status"]
                    completed_agents = sum(1 for s in agent_statuses.values() if s == "completed")
                    print(f"Progress: {completed_agents}/7 agents completed")
            
            # Verify review completed
            assert status == "completed", f"Review did not complete within {max_wait}s, status: {status}"
            
            # Get final results
            results_response = await async_client.get(f"/api/v1/review/{review_id}/results")
            assert results_response.status_code == 200
            
            results_data = results_response.json()
            
            # Verify comprehensive results
            assert results_data["success"] == True
            assert results_data["review_mode"] == "full"
            assert len(results_data["agent_results"]) == 7
            
            # Verify each agent completed successfully
            expected_agents = [
                "acceptance_criteria", "bug_detection", "security_analysis",
                "code_quality", "performance_analysis", "architectural_analysis", "summary_agent"
            ]
            
            for agent in expected_agents:
                assert agent in results_data["agent_results"]
                agent_result = results_data["agent_results"][agent]
                assert agent_result["success"] == True
                assert agent_result["execution_time"] > 0
                assert len(agent_result["findings"]) > 0
    
    @pytest.mark.asyncio 
    async def test_review_with_websocket_updates_e2e(self, async_client, e2e_workspace, mock_claude_responses):
        """Test E2E review with WebSocket real-time updates."""
        
        websocket_events = []
        session_id = str(uuid4())
        
        with patch('src.services.claude_service.query') as mock_claude:
            # Setup mock with delays to test real-time updates
            async def mock_query_with_delay(prompt, **kwargs):
                await asyncio.sleep(0.5)  # Simulate processing time
                
                for agent_type, response in mock_claude_responses.items():
                    if agent_type.replace('_', ' ') in prompt.lower():
                        yield response
                        return
                yield "Analysis completed"
            
            mock_claude.side_effect = mock_query_with_delay
            
            # Mock WebSocket manager to capture events
            with patch('src.api.websockets.multi_agent_manager.MultiAgentWebSocketManager.broadcast_agent_status') as mock_broadcast:
                async def capture_broadcast(*args, **kwargs):
                    websocket_events.append({"type": "agent_status", "args": args, "kwargs": kwargs})
                
                mock_broadcast.side_effect = capture_broadcast
                
                # Start review with WebSocket session
                review_request = {
                    "branch_name": "feature/websocket-test",
                    "workspace_path": str(e2e_workspace),
                    "review_mode": "full",
                    "websocket_session_id": session_id
                }
                
                response = await async_client.post("/api/v1/review/start", json=review_request)
                assert response.status_code == 200
                
                review_id = response.json()["review_id"]
                
                # Wait for completion and check WebSocket events
                await self._wait_for_completion(async_client, review_id)
                
                # Verify WebSocket events were triggered
                assert len(websocket_events) > 0
                
                # Verify events contain agent status updates
                agent_events = [e for e in websocket_events if e["type"] == "agent_status"]
                assert len(agent_events) >= 7  # At least one per agent
    
    @pytest.mark.asyncio
    async def test_error_handling_e2e(self, async_client, e2e_workspace):
        """Test E2E error handling scenarios."""
        
        # Test invalid workspace path
        invalid_request = {
            "branch_name": "feature/invalid-test", 
            "workspace_path": "/non/existent/path",
            "review_mode": "full"
        }
        
        response = await async_client.post("/api/v1/review/start", json=invalid_request)
        assert response.status_code == 400
        
        error_data = response.json()
        assert "error" in error_data
        assert "workspace" in error_data["error"].lower()
        
        # Test invalid review mode
        invalid_mode_request = {
            "branch_name": "feature/invalid-mode",
            "workspace_path": str(e2e_workspace),
            "review_mode": "invalid_mode"
        }
        
        response = await async_client.post("/api/v1/review/start", json=invalid_mode_request)
        assert response.status_code == 422  # Validation error
        
        # Test agent failure handling
        with patch('src.agents.factories.AgentFactory.create_agent') as mock_create:
            # Simulate agent creation failure
            mock_create.side_effect = Exception("Agent creation failed")
            
            failing_request = {
                "branch_name": "feature/agent-failure",
                "workspace_path": str(e2e_workspace),
                "review_mode": "full"
            }
            
            response = await async_client.post("/api/v1/review/start", json=failing_request)
            
            # Should handle gracefully, not crash
            if response.status_code == 200:
                review_id = response.json()["review_id"]
                
                # Wait a moment for processing
                await asyncio.sleep(2)
                
                status_response = await async_client.get(f"/api/v1/review/{review_id}/status")
                status_data = status_response.json()
                
                # Review should handle failure gracefully
                assert status_data["status"] in ["failed", "completed_with_errors"]
    
    @pytest.mark.asyncio
    async def test_review_cancellation_e2e(self, async_client, e2e_workspace, mock_claude_responses):
        """Test review cancellation workflow."""
        
        with patch('src.services.claude_service.query') as mock_claude:
            # Setup slow mock to allow cancellation
            async def slow_mock_query(*args, **kwargs):
                await asyncio.sleep(10)  # Long delay
                yield "Slow analysis"
            
            mock_claude.side_effect = slow_mock_query
            
            # Start review
            review_request = {
                "branch_name": "feature/cancellation-test",
                "workspace_path": str(e2e_workspace),
                "review_mode": "full"
            }
            
            response = await async_client.post("/api/v1/review/start", json=review_request)
            assert response.status_code == 200
            
            review_id = response.json()["review_id"]
            
            # Wait a moment for review to start
            await asyncio.sleep(1)
            
            # Cancel the review
            cancel_response = await async_client.delete(f"/api/v1/review/{review_id}")
            assert cancel_response.status_code == 200
            
            cancel_data = cancel_response.json()
            assert cancel_data["status"] == "cancelled"
            
            # Verify status reflects cancellation
            status_response = await async_client.get(f"/api/v1/review/{review_id}/status")
            status_data = status_response.json()
            assert status_data["status"] == "cancelled"
    
    @pytest.mark.asyncio
    async def test_performance_requirements_e2e(self, async_client, e2e_workspace, mock_claude_responses):
        """Test that E2E workflow meets performance requirements."""
        
        with patch('src.services.claude_service.query') as mock_claude:
            # Setup realistic mock responses
            async def timed_mock_query(*args, **kwargs):
                await asyncio.sleep(0.1)  # Simulate realistic API call time
                
                for agent_type, response in mock_claude_responses.items():
                    if agent_type.replace('_', ' ') in str(args[0]).lower():
                        yield response
                        return
                yield "Analysis completed"
            
            mock_claude.side_effect = timed_mock_query
            
            # Start timer
            start_time = datetime.now()
            
            # Execute review
            review_request = {
                "branch_name": "feature/performance-test",
                "workspace_path": str(e2e_workspace),
                "review_mode": "full"
            }
            
            response = await async_client.post("/api/v1/review/start", json=review_request)
            assert response.status_code == 200
            
            review_id = response.json()["review_id"]
            
            # Wait for completion
            await self._wait_for_completion(async_client, review_id)
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            # Verify performance requirements
            assert total_time < 300, f"Review took {total_time}s, should be under 5 minutes"
            
            # Verify parallel execution benefit (should be much faster than sequential)
            # With 7 agents at 0.1s each, sequential would be 0.7s+
            # Parallel should be close to 0.1s+ overhead
            assert total_time < 30, f"Parallel execution not efficient: {total_time}s"
    
    async def _wait_for_completion(self, client, review_id: str, max_wait: int = 60):
        """Helper to wait for review completion."""
        wait_time = 0
        status = "running"
        
        while status in ["pending", "running"] and wait_time < max_wait:
            await asyncio.sleep(1)
            wait_time += 1
            
            status_response = await client.get(f"/api/v1/review/{review_id}/status")
            if status_response.status_code == 200:
                status_data = status_response.json()
                status = status_data["status"]
        
        assert status in ["completed", "completed_with_errors"], f"Review did not complete: {status}"
        return status