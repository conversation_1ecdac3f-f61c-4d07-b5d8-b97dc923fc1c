"""
Review API Endpoints
Multi-agent parallel review orchestration with real orchestrator integration
"""

from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4
import asyncio
from datetime import datetime

import structlog
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, status
from fastapi.responses import JSONResponse

from src.config.settings import get_settings
from src.models.api_models import (
    ReviewRequest, ReviewResponse, ReviewStatusResponse, ReviewResult,
    ReviewCancellationResponse, AgentType, AgentStatus, ReviewStatus,
    AgentStatusInfo, AgentResult, ReviewMode
)
from src.orchestrator.parallel_orchestrator import ParallelMultiAgentOrchestrator
from src.agents.factories.agent_factory import AgentFactory
from src.services.claude_service import ClaudeService
from src.models.context.context_provider import ContextProvider, ContextType
from src.api.websockets.multi_agent_manager import websocket_manager, WebSocketEventType

logger = structlog.get_logger()
router = APIRouter()
settings = get_settings()

# Global instances
context_provider = ContextProvider()
claude_service = ClaudeService()
agent_factory = AgentFactory(claude_service=claude_service, settings=settings)
orchestrator = ParallelMultiAgentOrchestrator(
    agent_factory=agent_factory,
    websocket_manager=websocket_manager,
    settings=settings
)

# In-memory storage for demo (TODO: Replace with database)
active_reviews: Dict[UUID, ReviewStatusResponse] = {}
review_results: Dict[UUID, ReviewResult] = {}


@router.post("/start", response_model=ReviewResponse)
async def start_review(
    request: ReviewRequest,
    background_tasks: BackgroundTasks
):
    """Start a new multi-agent code review"""
    
    review_id = uuid4()
    
    logger.info(
        "Starting new multi-agent review request",
        review_id=str(review_id),
        branch_name=request.branch_name,
        review_mode=request.review_mode.value,
        repository_path=request.repository_path,
        jira_ticket_id=request.jira_ticket_id
    )
    
    # Determine which agents to run based on review mode
    active_agents = _get_agents_for_mode(request.review_mode, request.include_summary)
    
    # Create initial agent status information
    agent_statuses = {}
    for agent_type in AgentType:
        if agent_type in active_agents:
            agent_statuses[agent_type] = AgentStatusInfo(
                agent_type=agent_type,
                status=AgentStatus.PENDING
            )
        else:
            agent_statuses[agent_type] = AgentStatusInfo(
                agent_type=agent_type,
                status=AgentStatus.SKIPPED
            )
    
    # Create initial review status
    initial_status = ReviewStatusResponse(
        review_id=review_id,
        status=ReviewStatus.STARTED,
        progress=0.0,
        agent_statuses=agent_statuses,
        started_at=datetime.utcnow(),
        active_agents=[],
        completed_agents=[],
        failed_agents=[]
    )
    
    active_reviews[review_id] = initial_status
    
    # Start background task for review execution
    background_tasks.add_task(execute_multi_agent_review, review_id, request)
    
    # Estimate completion time based on mode and number of agents
    base_time_per_agent = 45  # seconds
    estimated_time = len(active_agents) * base_time_per_agent
    
    # Mode-specific adjustments
    mode_multiplier = {
        "quick": 0.6,
        "full": 1.0,
        "ac_only": 0.5,
        "bug_analysis": 0.8,
        "summary_only": 0.3
    }.get(request.review_mode.value, 1.0)
    
    estimated_time = int(estimated_time * mode_multiplier)
    
    # Broadcast review started event
    await websocket_manager.broadcast_review_event(
        event_type=WebSocketEventType.REVIEW_STARTED,
        review_id=review_id,
        data={
            "branch_name": request.branch_name,
            "review_mode": request.review_mode.value,
            "active_agents": [agent.value for agent in active_agents],
            "estimated_time": estimated_time
        },
        message=f"Multi-agent review started for branch '{request.branch_name}'"
    )
    
    return ReviewResponse(
        review_id=review_id,
        status=ReviewStatus.STARTED,
        message=f"Multi-agent review started for branch '{request.branch_name}'",
        estimated_completion_time=estimated_time,
        websocket_url=f"ws://localhost:{settings.port}/ws",
        created_at=datetime.utcnow()
    )


@router.get("/status/{review_id}", response_model=ReviewStatusResponse)
async def get_review_status(review_id: UUID):
    """Get review status and progress"""
    
    if review_id not in active_reviews:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Review {review_id} not found"
        )
    
    review_status = active_reviews[review_id]
    
    # Update calculated fields
    active_agents = [
        agent_type for agent_type, info in review_status.agent_statuses.items()
        if info.status == AgentStatus.RUNNING
    ]
    completed_agents = [
        agent_type for agent_type, info in review_status.agent_statuses.items()
        if info.status == AgentStatus.COMPLETED
    ]
    failed_agents = [
        agent_type for agent_type, info in review_status.agent_statuses.items()
        if info.status == AgentStatus.FAILED
    ]
    
    review_status.active_agents = active_agents
    review_status.completed_agents = completed_agents
    review_status.failed_agents = failed_agents
    
    return review_status


@router.get("/results/{review_id}", response_model=ReviewResult)
async def get_review_results(review_id: UUID):
    """Get completed review results"""
    
    if review_id not in review_results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Review results for {review_id} not found"
        )
    
    return review_results[review_id]


@router.delete("/cancel/{review_id}", response_model=ReviewCancellationResponse)
async def cancel_review(review_id: UUID):
    """Cancel an active review"""
    
    if review_id not in active_reviews:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Review {review_id} not found"
        )
    
    review_status = active_reviews[review_id]
    
    # Only allow cancellation of running or started reviews
    if review_status.status not in [ReviewStatus.STARTED, ReviewStatus.RUNNING]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot cancel review in status: {review_status.status.value}"
        )
    
    # Get currently active agents
    cancelled_agents = [
        agent_type for agent_type, info in review_status.agent_statuses.items()
        if info.status in [AgentStatus.RUNNING, AgentStatus.PENDING]
    ]
    
    # Update status to cancelled
    review_status.status = ReviewStatus.CANCELLED
    review_status.completed_at = datetime.utcnow()
    
    # Mark all pending/running agents as cancelled
    for agent_type, info in review_status.agent_statuses.items():
        if info.status in [AgentStatus.RUNNING, AgentStatus.PENDING]:
            info.status = AgentStatus.FAILED  # Use FAILED to indicate cancellation
            info.completed_at = datetime.utcnow()
            info.error_message = "Review cancelled by user"
    
    # Broadcast cancellation event
    await websocket_manager.broadcast_review_event(
        event_type=WebSocketEventType.REVIEW_CANCELLED,
        review_id=review_id,
        data={"cancelled_agents": [agent.value for agent in cancelled_agents]},
        message=f"Review {review_id} cancelled successfully"
    )
    
    logger.info(
        "Review cancelled",
        review_id=str(review_id),
        cancelled_agents=[agent.value for agent in cancelled_agents]
    )
    
    return ReviewCancellationResponse(
        review_id=review_id,
        status=ReviewStatus.CANCELLED,
        message=f"Review {review_id} cancelled successfully",
        cancelled_at=datetime.utcnow(),
        cancelled_agents=cancelled_agents
    )


async def execute_multi_agent_review(review_id: UUID, request: ReviewRequest):
    """Execute multi-agent review using real orchestrator (background task)"""
    
    start_time = datetime.utcnow()
    
    try:
        logger.info("Starting multi-agent review execution", review_id=str(review_id))
        
        # Update status to running
        review_status = active_reviews[review_id]
        review_status.status = ReviewStatus.RUNNING
        
        # Broadcast running event
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_STARTED,
            review_id=review_id,
            message="Multi-agent orchestration started"
        )
        
        # Prepare context for agents
        context_data = {
            "branch_name": request.branch_name,
            "repository_path": request.repository_path,
            "working_directory": request.working_directory,
            "jira_ticket_id": request.jira_ticket_id,
            "review_mode": request.review_mode.value,
            "agent_config": request.agent_config or {},
            "context_config": request.context_config or {}
        }
        
        # Set up progress callback for WebSocket updates
        async def progress_callback(agent_type: str, event_type: str, data: Dict[str, Any] = None):
            """Callback for agent progress updates"""
            
            agent_enum = AgentType(agent_type)
            
            # Update agent status
            if agent_enum in review_status.agent_statuses:
                agent_info = review_status.agent_statuses[agent_enum]
                
                if event_type == "started":
                    agent_info.status = AgentStatus.RUNNING
                    agent_info.started_at = datetime.utcnow()
                    
                    # Broadcast agent started event
                    await websocket_manager.broadcast_agent_event(
                        event_type=WebSocketEventType.AGENT_STARTED,
                        review_id=review_id,
                        agent_type=agent_type,
                        data=data,
                        message=f"{agent_type} agent started"
                    )
                    
                elif event_type == "progress":
                    progress = data.get("progress", 0) if data else 0
                    agent_info.progress = progress
                    
                    # Broadcast agent progress event
                    await websocket_manager.broadcast_agent_event(
                        event_type=WebSocketEventType.AGENT_PROGRESS,
                        review_id=review_id,
                        agent_type=agent_type,
                        data=data,
                        message=f"{agent_type} agent: {progress:.1f}% complete"
                    )
                    
                elif event_type == "completed":
                    agent_info.status = AgentStatus.COMPLETED
                    agent_info.completed_at = datetime.utcnow()
                    agent_info.progress = 100.0
                    
                    # Broadcast agent completed event
                    await websocket_manager.broadcast_agent_event(
                        event_type=WebSocketEventType.AGENT_COMPLETED,
                        review_id=review_id,
                        agent_type=agent_type,
                        data=data,
                        message=f"{agent_type} agent completed successfully"
                    )
                    
                elif event_type == "failed":
                    agent_info.status = AgentStatus.FAILED
                    agent_info.completed_at = datetime.utcnow()
                    agent_info.error_message = data.get("error", "Unknown error") if data else "Unknown error"
                    
                    # Broadcast agent failed event
                    await websocket_manager.broadcast_agent_event(
                        event_type=WebSocketEventType.AGENT_FAILED,
                        review_id=review_id,
                        agent_type=agent_type,
                        data=data,
                        message=f"{agent_type} agent failed: {agent_info.error_message}"
                    )
                
                # Update overall progress
                _update_overall_progress(review_status)
        
        # Execute parallel orchestration
        orchestration_result = await orchestrator.orchestrate_parallel_review(
            context_data=context_data,
            review_mode=request.review_mode.value,
            progress_callback=progress_callback
        )
        
        # Convert orchestration result to API format
        agent_results = {}
        reports = {}
        
        for agent_type_str, result in orchestration_result.agent_results.items():
            agent_type = AgentType(agent_type_str)
            
            agent_results[agent_type] = AgentResult(
                agent_type=agent_type,
                status=AgentStatus.COMPLETED if result.success else AgentStatus.FAILED,
                execution_time=result.execution_time,
                result_data=result.result_data,
                findings=result.findings,
                recommendations=result.recommendations,
                confidence_score=result.confidence_score,
                metadata=result.metadata
            )
        
        # Get reports from orchestration result
        if hasattr(orchestration_result, 'reports') and orchestration_result.reports:
            reports = orchestration_result.reports
        
        # Calculate total execution time
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        # Create final result
        final_result = ReviewResult(
            review_id=review_id,
            status=ReviewStatus.COMPLETED if orchestration_result.success else ReviewStatus.FAILED,
            overall_results=orchestration_result.summary,
            reports=reports,
            execution_time=execution_time,
            agent_results=agent_results,
            context_metadata=orchestration_result.context_metadata,
            performance_metrics=orchestration_result.performance_metrics,
            created_at=datetime.utcnow(),
            summary=orchestration_result.summary.get("overall_summary", "Review completed"),
            priority_findings=orchestration_result.priority_findings
        )
        
        # Store results
        review_results[review_id] = final_result
        
        # Update final status
        review_status.status = ReviewStatus.COMPLETED if orchestration_result.success else ReviewStatus.FAILED
        review_status.completed_at = datetime.utcnow()
        review_status.progress = 100.0
        
        # Broadcast completion event
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_COMPLETED,
            review_id=review_id,
            data={
                "execution_time": execution_time,
                "total_findings": len(orchestration_result.priority_findings),
                "success": orchestration_result.success
            },
            message="Multi-agent review completed successfully"
        )
        
        logger.info(
            "Multi-agent review execution completed",
            review_id=str(review_id),
            execution_time=execution_time,
            success=orchestration_result.success,
            total_agents=len(orchestration_result.agent_results)
        )
        
    except Exception as e:
        logger.error(
            "Multi-agent review execution failed",
            review_id=str(review_id),
            error=str(e),
            exc_info=True
        )
        
        # Update status to failed
        review_status = active_reviews[review_id]
        review_status.status = ReviewStatus.FAILED
        review_status.completed_at = datetime.utcnow()
        
        # Mark all pending/running agents as failed
        for agent_type, info in review_status.agent_statuses.items():
            if info.status in [AgentStatus.RUNNING, AgentStatus.PENDING]:
                info.status = AgentStatus.FAILED
                info.completed_at = datetime.utcnow()
                info.error_message = str(e)
        
        # Broadcast failure event
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_FAILED,
            review_id=review_id,
            data={"error": str(e)},
            message=f"Multi-agent review failed: {str(e)}"
        )


def _get_agents_for_mode(review_mode: ReviewMode, include_summary: bool) -> List[AgentType]:
    """Get list of agents to run based on review mode"""
    
    if review_mode == ReviewMode.QUICK:
        agents = [AgentType.ACCEPTANCE_CRITERIA, AgentType.BUG_DETECTION, AgentType.CODE_QUALITY]
    elif review_mode == ReviewMode.AC_ONLY:
        agents = [AgentType.ACCEPTANCE_CRITERIA]
    elif review_mode == ReviewMode.BUG_ANALYSIS:
        agents = [AgentType.BUG_DETECTION, AgentType.SECURITY_ANALYSIS, AgentType.CODE_QUALITY]
    elif review_mode == ReviewMode.SUMMARY_ONLY:
        agents = [AgentType.SUMMARY]
    else:  # FULL mode
        agents = [
            AgentType.ACCEPTANCE_CRITERIA,
            AgentType.BUG_DETECTION,
            AgentType.SECURITY_ANALYSIS,
            AgentType.LOGIC_ANALYSIS,
            AgentType.CODE_QUALITY,
            AgentType.ARCHITECTURAL
        ]
    
    if include_summary and review_mode != ReviewMode.SUMMARY_ONLY:
        agents.append(AgentType.SUMMARY)
    
    return agents


def _update_overall_progress(review_status: ReviewStatusResponse) -> None:
    """Update overall progress based on individual agent progress"""
    
    total_progress = 0.0
    active_agent_count = 0
    
    for agent_info in review_status.agent_statuses.values():
        if agent_info.status != AgentStatus.SKIPPED:
            total_progress += agent_info.progress
            active_agent_count += 1
    
    if active_agent_count > 0:
        review_status.progress = total_progress / active_agent_count
    else:
        review_status.progress = 0.0