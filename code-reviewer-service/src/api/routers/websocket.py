"""
WebSocket API Routes
Real-time communication endpoints for multi-agent progress
"""

import json
import time
from typing import Optional
from uuid import UUID

import structlog
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Query, Depends
from fastapi.responses import JSONResponse

from src.api.websockets.multi_agent_manager import websocket_manager
from src.api.websockets.events import event_emitter, EventEmitter
from src.config.settings import get_settings

logger = structlog.get_logger()
router = APIRouter()
settings = get_settings()


# WebSocket authentication (simplified for demo)
async def get_websocket_user(
    token: Optional[str] = Query(None),
    session_id: Optional[str] = Query(None)
) -> dict:
    """
    Simple WebSocket authentication
    In production, implement proper JWT/token validation
    """
    
    # For demo purposes, allow anonymous connections
    # In production, validate token and extract user info
    return {
        "user_id": f"user_{session_id}" if session_id else "anonymous",
        "session_id": session_id or "default_session",
        "authenticated": token is not None
    }


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    user_info: dict = Depends(get_websocket_user)
):
    """
    Main WebSocket endpoint for multi-agent progress updates
    
    Query Parameters:
        - token: Authentication token (optional for demo)
        - session_id: Unique session identifier
        
    Usage:
        const ws = new WebSocket('ws://localhost:8000/api/v1/websocket/ws?session_id=my_session&token=my_token');
    """
    
    session_id = user_info["session_id"]
    user_id = user_info.get("user_id")
    
    try:
        # Accept connection
        await websocket_manager.connect(
            websocket=websocket,
            session_id=session_id,
            user_id=user_id
        )
        
        logger.info(
            "WebSocket connection established",
            session_id=session_id,
            user_id=user_id
        )
        
        # Handle incoming messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle client messages
                await handle_client_message(session_id, message)
                
            except WebSocketDisconnect:
                logger.info("WebSocket client disconnected", session_id=session_id)
                break
            except json.JSONDecodeError as e:
                logger.warning(
                    "Invalid JSON received from WebSocket client",
                    session_id=session_id,
                    error=str(e)
                )
                # Send error back to client
                await EventEmitter.system_error(
                    review_id=UUID("00000000-0000-0000-0000-000000000000"),  # System error
                    error_message="Invalid JSON format",
                    error_details={"error": str(e)}
                )
            except Exception as e:
                logger.error(
                    "Error handling WebSocket message",
                    session_id=session_id,
                    error=str(e),
                    exc_info=True
                )
                
    except Exception as e:
        logger.error(
            "WebSocket connection error",
            session_id=session_id,
            error=str(e),
            exc_info=True
        )
    finally:
        # Clean up connection
        await websocket_manager.disconnect(session_id)


async def handle_client_message(session_id: str, message: dict) -> None:
    """
    Handle messages from WebSocket clients
    
    Args:
        session_id: Client session identifier
        message: Parsed JSON message from client
    """
    
    try:
        action = message.get("action")
        data = message.get("data", {})
        
        logger.debug(
            "Handling WebSocket client message",
            session_id=session_id,
            action=action,
            data_keys=list(data.keys()) if isinstance(data, dict) else None
        )
        
        if action == "subscribe_review":
            # Subscribe to review progress updates
            review_id = UUID(data.get("review_id"))
            await websocket_manager.join_review_room(session_id, review_id)
            
        elif action == "unsubscribe_review":
            # Unsubscribe from review progress updates
            review_id = UUID(data.get("review_id"))
            await websocket_manager.leave_review_room(session_id, review_id)
            
        elif action == "heartbeat":
            # Client heartbeat - connection manager handles this automatically
            pass
            
        elif action == "get_connection_info":
            # Send connection information back to client
            stats = websocket_manager.get_connection_stats()
            await EventEmitter.system_error(  # Reusing system event for info
                review_id=UUID("00000000-0000-0000-0000-000000000000"),
                error_message="Connection info",
                error_details=stats
            )
            
        else:
            logger.warning(
                "Unknown WebSocket action",
                session_id=session_id,
                action=action
            )
            
    except ValueError as e:
        logger.error(
            "Invalid UUID in WebSocket message",
            session_id=session_id,
            error=str(e)
        )
    except Exception as e:
        logger.error(
            "Error handling WebSocket client message",
            session_id=session_id,
            error=str(e),
            exc_info=True
        )


@router.get("/stats")
async def get_websocket_stats():
    """
    Get WebSocket connection statistics
    
    Returns:
        Connection statistics and health information
    """
    
    try:
        stats = websocket_manager.get_connection_stats()
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "stats": stats,
                "timestamp": time.time()
            }
        )
        
    except Exception as e:
        logger.error("Failed to get WebSocket stats", error=str(e), exc_info=True)
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get WebSocket statistics: {str(e)}"
        )


@router.post("/broadcast/{review_id}")
async def broadcast_test_event(
    review_id: UUID,
    event_data: dict
):
    """
    Test endpoint for broadcasting events to WebSocket clients
    (For development/testing purposes)
    
    Args:
        review_id: Review identifier to broadcast to
        event_data: Event data to broadcast
    """
    
    if not settings.debug:
        raise HTTPException(
            status_code=403,
            detail="Test endpoints only available in debug mode"
        )
    
    try:
        # Extract event information
        event_type = event_data.get("event_type", "test_event")
        agent_type = event_data.get("agent_type", "test_agent")
        message = event_data.get("message", "Test event from API")
        data = event_data.get("data", {})
        
        # Broadcast event
        await websocket_manager.broadcast_agent_event(
            event_type=event_type,
            review_id=review_id,
            agent_type=agent_type,
            data=data,
            message=message
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": f"Event broadcasted to review {review_id}",
                "event_type": event_type,
                "subscribers": len(websocket_manager.review_subscribers.get(review_id, set()))
            }
        )
        
    except Exception as e:
        logger.error(
            "Failed to broadcast test event",
            review_id=str(review_id),
            error=str(e),
            exc_info=True
        )
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to broadcast event: {str(e)}"
        )


@router.get("/health")
async def websocket_health_check():
    """
    WebSocket service health check
    
    Returns:
        Health status of WebSocket service
    """
    
    try:
        stats = websocket_manager.get_connection_stats()
        
        # Simple health check - service is healthy if manager is responsive
        healthy = True
        
        return JSONResponse(
            status_code=200 if healthy else 503,
            content={
                "healthy": healthy,
                "service": "websocket",
                "connections": stats["total_connections"],
                "active_reviews": stats["active_reviews"],
                "heartbeat_active": stats["heartbeat_active"],
                "timestamp": time.time()
            }
        )
        
    except Exception as e:
        logger.error("WebSocket health check failed", error=str(e), exc_info=True)
        
        return JSONResponse(
            status_code=503,
            content={
                "healthy": False,
                "service": "websocket",
                "error": str(e),
                "timestamp": time.time()
            }
        )