"""
Health Check API Endpoints
Service health monitoring and readiness checks
"""

import asyncio
import time
from typing import Dict, Any

import structlog
from fastapi import APIRouter, HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from src.config.settings import get_settings

logger = structlog.get_logger()
router = APIRouter()
settings = get_settings()


class HealthResponse(BaseModel):
    """Health check response model"""
    status: str
    timestamp: float
    version: str
    environment: str
    uptime: float
    checks: Dict[str, Any]


class ServiceStatus(BaseModel):
    """Individual service status"""
    healthy: bool
    response_time_ms: float
    details: Dict[str, Any] = {}


# Track service start time
START_TIME = time.time()


@router.get("/", response_model=HealthResponse)
async def health_check():
    """Basic health check endpoint"""
    
    current_time = time.time()
    uptime = current_time - START_TIME
    
    # Run all health checks in parallel
    checks = await asyncio.gather(
        check_database(),
        check_redis(),
        check_claude_sdk(),
        return_exceptions=True
    )
    
    # Process check results
    database_check, redis_check, claude_check = checks
    
    health_checks = {
        "database": database_check if isinstance(database_check, dict) else {"healthy": False, "error": str(database_check)},
        "redis": redis_check if isinstance(redis_check, dict) else {"healthy": False, "error": str(redis_check)},
        "claude_sdk": claude_check if isinstance(claude_check, dict) else {"healthy": False, "error": str(claude_check)}
    }
    
    # Determine overall health
    overall_healthy = all(
        check.get("healthy", False) for check in health_checks.values()
    )
    
    status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE
    
    response = HealthResponse(
        status="healthy" if overall_healthy else "unhealthy",
        timestamp=current_time,
        version="0.1.0",
        environment=settings.environment,
        uptime=uptime,
        checks=health_checks
    )
    
    logger.info(
        "Health check performed",
        overall_status=response.status,
        uptime=uptime,
        check_results=health_checks
    )
    
    return JSONResponse(
        status_code=status_code,
        content=response.dict()
    )


@router.get("/ready")
async def readiness_check():
    """Kubernetes readiness probe endpoint"""
    
    # Check if all critical services are ready
    checks = await asyncio.gather(
        check_database(),
        check_redis(),
        return_exceptions=True
    )
    
    database_ready = isinstance(checks[0], dict) and checks[0].get("healthy", False)
    redis_ready = isinstance(checks[1], dict) and checks[1].get("healthy", False)
    
    if database_ready and redis_ready:
        return {"status": "ready", "timestamp": time.time()}
    else:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@router.get("/live")
async def liveness_check():
    """Kubernetes liveness probe endpoint"""
    
    # Simple liveness check - service is running
    return {
        "status": "alive",
        "timestamp": time.time(),
        "uptime": time.time() - START_TIME
    }


async def check_database() -> Dict[str, Any]:
    """Check database connectivity"""
    start_time = time.time()
    
    try:
        # Import database manager
        from src.database.connection import db_manager
        
        # Perform actual database health check
        result = await db_manager.health_check(force=True)
        
        response_time = (time.time() - start_time) * 1000
        
        if result.get("healthy", False):
            return {
                "healthy": True,
                "response_time_ms": round(response_time, 2),
                "details": {
                    "connection": "active",
                    "database_version": result.get("database_version", "Unknown"),
                    "pool_status": result.get("pool_status", {}),
                    "connection_attempts": result.get("connection_attempts", 0)
                }
            }
        else:
            return {
                "healthy": False,
                "response_time_ms": round(response_time, 2),
                "details": {
                    "error": result.get("error", "Unknown database error")
                }
            }
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Database health check failed", error=str(e))
        
        return {
            "healthy": False,
            "response_time_ms": round(response_time, 2),
            "details": {
                "error": str(e)
            }
        }


async def check_redis() -> Dict[str, Any]:
    """Check Redis connectivity"""
    start_time = time.time()
    
    try:
        # TODO: Implement actual Redis connectivity check
        # For now, simulate a Redis check
        await asyncio.sleep(0.005)  # Simulate Redis ping
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            "healthy": True,
            "response_time_ms": round(response_time, 2),
            "details": {
                "connection": "active"
            }
        }
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Redis health check failed", error=str(e))
        
        return {
            "healthy": False,
            "response_time_ms": round(response_time, 2),
            "details": {
                "error": str(e)
            }
        }


async def check_claude_sdk() -> Dict[str, Any]:
    """Check Claude SDK availability"""
    start_time = time.time()
    
    try:
        # TODO: Implement actual Claude SDK health check
        # For now, simulate SDK availability check
        await asyncio.sleep(0.02)  # Simulate SDK check
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            "healthy": True,
            "response_time_ms": round(response_time, 2),
            "details": {
                "sdk_version": "1.0.0",  # TODO: Get actual version
                "api_available": True
            }
        }
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Claude SDK health check failed", error=str(e))
        
        return {
            "healthy": False,
            "response_time_ms": round(response_time, 2),
            "details": {
                "error": str(e)
            }
        }