"""
WebSocket Event Definitions and Helpers
Event types and utilities for multi-agent WebSocket communication
"""

import time
from typing import Dict, Any, Optional
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field

from src.api.websockets.multi_agent_manager import WebSocketEventType, websocket_manager


class AgentStatus(str, Enum):
    """Agent execution status"""
    PENDING = "pending"
    STARTING = "starting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReviewStatus(str, Enum):
    """Review execution status"""
    PENDING = "pending"
    STARTED = "started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AgentProgressData(BaseModel):
    """Data payload for agent progress events"""
    
    agent_type: str
    status: AgentStatus
    progress_percentage: float = Field(ge=0, le=100)
    current_step: str
    total_steps: int
    completed_steps: int
    estimated_remaining_ms: Optional[int] = None
    error_message: Optional[str] = None


class AgentCompletedData(BaseModel):
    """Data payload for agent completion events"""
    
    agent_type: str
    execution_time_ms: int
    success: bool
    result_summary: str
    findings_count: int = 0
    error_message: Optional[str] = None
    output_file_path: Optional[str] = None


class ReviewProgressData(BaseModel):
    """Data payload for overall review progress events"""
    
    review_id: UUID
    status: ReviewStatus
    overall_progress: float = Field(ge=0, le=100)
    completed_agents: int
    total_agents: int
    current_phase: str
    agents_status: Dict[str, AgentStatus]
    estimated_completion_ms: Optional[int] = None


class EventEmitter:
    """
    Helper class for emitting WebSocket events
    Provides convenient methods for different event types
    """
    
    @staticmethod
    async def agent_started(
        review_id: UUID,
        agent_type: str,
        estimated_duration_ms: Optional[int] = None
    ) -> None:
        """
        Emit agent started event
        
        Args:
            review_id: Review identifier
            agent_type: Type of agent starting
            estimated_duration_ms: Estimated execution time
        """
        
        await websocket_manager.broadcast_agent_event(
            event_type=WebSocketEventType.AGENT_STARTED,
            review_id=review_id,
            agent_type=agent_type,
            data={
                "status": AgentStatus.STARTING.value,
                "estimated_duration_ms": estimated_duration_ms,
                "started_at": time.time()
            },
            message=f"Agent {agent_type} started execution"
        )
    
    @staticmethod
    async def agent_progress(
        review_id: UUID,
        agent_type: str,
        progress_data: AgentProgressData
    ) -> None:
        """
        Emit agent progress event
        
        Args:
            review_id: Review identifier
            agent_type: Type of agent
            progress_data: Progress information
        """
        
        await websocket_manager.broadcast_agent_event(
            event_type=WebSocketEventType.AGENT_PROGRESS,
            review_id=review_id,
            agent_type=agent_type,
            data=progress_data.model_dump(),
            message=f"Agent {agent_type}: {progress_data.current_step} ({progress_data.progress_percentage:.1f}%)"
        )
    
    @staticmethod
    async def agent_completed(
        review_id: UUID,
        agent_type: str,
        completion_data: AgentCompletedData
    ) -> None:
        """
        Emit agent completion event
        
        Args:
            review_id: Review identifier
            agent_type: Type of agent
            completion_data: Completion information
        """
        
        event_type = WebSocketEventType.AGENT_COMPLETED if completion_data.success else WebSocketEventType.AGENT_FAILED
        status = AgentStatus.COMPLETED if completion_data.success else AgentStatus.FAILED
        
        await websocket_manager.broadcast_agent_event(
            event_type=event_type,
            review_id=review_id,
            agent_type=agent_type,
            data={
                **completion_data.model_dump(),
                "status": status.value,
                "completed_at": time.time()
            },
            message=f"Agent {agent_type} {'completed successfully' if completion_data.success else 'failed'} in {completion_data.execution_time_ms}ms"
        )
    
    @staticmethod
    async def review_started(
        review_id: UUID,
        agent_types: list[str],
        estimated_duration_ms: Optional[int] = None
    ) -> None:
        """
        Emit review started event
        
        Args:
            review_id: Review identifier
            agent_types: List of agents to be executed
            estimated_duration_ms: Estimated total duration
        """
        
        agents_status = {agent_type: AgentStatus.PENDING.value for agent_type in agent_types}
        
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_STARTED,
            review_id=review_id,
            data={
                "status": ReviewStatus.STARTED.value,
                "total_agents": len(agent_types),
                "agent_types": agent_types,
                "agents_status": agents_status,
                "estimated_duration_ms": estimated_duration_ms,
                "started_at": time.time()
            },
            message=f"Review {review_id} started with {len(agent_types)} agents"
        )
    
    @staticmethod
    async def review_progress_update(
        review_id: UUID,
        progress_data: ReviewProgressData
    ) -> None:
        """
        Emit review progress update event
        
        Args:
            review_id: Review identifier
            progress_data: Progress information
        """
        
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_STARTED,  # Using started for progress updates
            review_id=review_id,
            data=progress_data.model_dump(),
            message=f"Review progress: {progress_data.overall_progress:.1f}% ({progress_data.completed_agents}/{progress_data.total_agents} agents completed)"
        )
    
    @staticmethod
    async def review_completed(
        review_id: UUID,
        success: bool,
        execution_time_ms: int,
        agent_results: Dict[str, Any],
        error_message: Optional[str] = None
    ) -> None:
        """
        Emit review completion event
        
        Args:
            review_id: Review identifier
            success: Whether review completed successfully
            execution_time_ms: Total execution time
            agent_results: Results from all agents
            error_message: Error message if failed
        """
        
        event_type = WebSocketEventType.REVIEW_COMPLETED if success else WebSocketEventType.REVIEW_FAILED
        status = ReviewStatus.COMPLETED if success else ReviewStatus.FAILED
        
        # Count successful/failed agents
        successful_agents = sum(1 for result in agent_results.values() if result.get("success", False))
        failed_agents = len(agent_results) - successful_agents
        
        await websocket_manager.broadcast_review_event(
            event_type=event_type,
            review_id=review_id,
            data={
                "status": status.value,
                "success": success,
                "execution_time_ms": execution_time_ms,
                "total_agents": len(agent_results),
                "successful_agents": successful_agents,
                "failed_agents": failed_agents,
                "error_message": error_message,
                "completed_at": time.time(),
                "agent_results": agent_results
            },
            message=f"Review {review_id} {'completed successfully' if success else 'failed'} in {execution_time_ms}ms"
        )
    
    @staticmethod
    async def review_cancelled(
        review_id: UUID,
        reason: str = "User requested cancellation"
    ) -> None:
        """
        Emit review cancellation event
        
        Args:
            review_id: Review identifier
            reason: Cancellation reason
        """
        
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.REVIEW_CANCELLED,
            review_id=review_id,
            data={
                "status": ReviewStatus.CANCELLED.value,
                "reason": reason,
                "cancelled_at": time.time()
            },
            message=f"Review {review_id} cancelled: {reason}"
        )
    
    @staticmethod
    async def system_error(
        review_id: UUID,
        error_message: str,
        error_details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Emit system error event
        
        Args:
            review_id: Review identifier
            error_message: Error message
            error_details: Additional error details
        """
        
        await websocket_manager.broadcast_review_event(
            event_type=WebSocketEventType.ERROR,
            review_id=review_id,
            data={
                "error": error_message,
                "details": error_details or {},
                "timestamp": time.time()
            },
            message=f"System error in review {review_id}: {error_message}"
        )


# Convenience functions for common event patterns
async def emit_agent_lifecycle(
    review_id: UUID,
    agent_type: str,
    status: AgentStatus,
    **kwargs
) -> None:
    """
    Emit appropriate event based on agent status
    
    Args:
        review_id: Review identifier
        agent_type: Type of agent
        status: Current agent status
        **kwargs: Additional event data
    """
    
    if status == AgentStatus.STARTING:
        await EventEmitter.agent_started(
            review_id=review_id,
            agent_type=agent_type,
            estimated_duration_ms=kwargs.get("estimated_duration_ms")
        )
    
    elif status == AgentStatus.RUNNING:
        if "progress_data" in kwargs:
            await EventEmitter.agent_progress(
                review_id=review_id,
                agent_type=agent_type,
                progress_data=kwargs["progress_data"]
            )
    
    elif status in [AgentStatus.COMPLETED, AgentStatus.FAILED]:
        if "completion_data" in kwargs:
            await EventEmitter.agent_completed(
                review_id=review_id,
                agent_type=agent_type,
                completion_data=kwargs["completion_data"]
            )


async def emit_review_lifecycle(
    review_id: UUID,
    status: ReviewStatus,
    **kwargs
) -> None:
    """
    Emit appropriate event based on review status
    
    Args:
        review_id: Review identifier
        status: Current review status
        **kwargs: Additional event data
    """
    
    if status == ReviewStatus.STARTED:
        await EventEmitter.review_started(
            review_id=review_id,
            agent_types=kwargs.get("agent_types", []),
            estimated_duration_ms=kwargs.get("estimated_duration_ms")
        )
    
    elif status == ReviewStatus.RUNNING:
        if "progress_data" in kwargs:
            await EventEmitter.review_progress_update(
                review_id=review_id,
                progress_data=kwargs["progress_data"]
            )
    
    elif status in [ReviewStatus.COMPLETED, ReviewStatus.FAILED]:
        await EventEmitter.review_completed(
            review_id=review_id,
            success=status == ReviewStatus.COMPLETED,
            execution_time_ms=kwargs.get("execution_time_ms", 0),
            agent_results=kwargs.get("agent_results", {}),
            error_message=kwargs.get("error_message")
        )
    
    elif status == ReviewStatus.CANCELLED:
        await EventEmitter.review_cancelled(
            review_id=review_id,
            reason=kwargs.get("reason", "User requested cancellation")
        )


# Export main event emitter for easy access
event_emitter = EventEmitter()