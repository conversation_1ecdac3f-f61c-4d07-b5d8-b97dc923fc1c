"""
Socket.IO Adapter for Multi-Agent WebSocket Manager
Bridges Socket.IO clients with existing WebSocket infrastructure
"""

import asyncio
import time
from typing import Dict, Any, Optional
from uuid import UUID
import structlog
import socketio

from .multi_agent_manager import websocket_manager, WebSocketEventType, WebSocketMessage
from ..routers.websocket import sio

logger = structlog.get_logger()


class SocketIOAdapter:
    """
    Adapter to bridge Socket.IO events with the existing Multi-Agent WebSocket Manager
    """
    
    def __init__(self):
        self.session_mapping: Dict[str, str] = {}  # socket_id -> session_id
        
    async def handle_connect(self, sid: str, environ: dict) -> None:
        """Handle Socket.IO client connection"""
        try:
            # Extract session info from query parameters or headers
            query_string = environ.get('QUERY_STRING', '')
            session_id = self._extract_session_id(query_string) or f"socketio_{sid}"
            
            # Store mapping
            self.session_mapping[sid] = session_id
            
            logger.info("Socket.IO client connected", 
                       socket_id=sid, session_id=session_id)
            
            # Send connection confirmation
            await sio.emit('connected', {
                'status': 'connected',
                'session_id': session_id,
                'socket_id': sid,
                'timestamp': time.time()
            }, room=sid)
            
        except Exception as e:
            logger.error("Error handling Socket.IO connection", 
                        socket_id=sid, error=str(e), exc_info=True)
    
    async def handle_disconnect(self, sid: str) -> None:
        """Handle Socket.IO client disconnection"""
        try:
            session_id = self.session_mapping.pop(sid, None)
            logger.info("Socket.IO client disconnected", 
                       socket_id=sid, session_id=session_id)
        except Exception as e:
            logger.error("Error handling Socket.IO disconnection", 
                        socket_id=sid, error=str(e), exc_info=True)
    
    async def handle_join_review_room(self, sid: str, data: dict) -> None:
        """Handle joining a review room"""
        try:
            review_id = data.get('review_id')
            if not review_id:
                await sio.emit('error', {'message': 'review_id required'}, room=sid)
                return
            
            # Join Socket.IO room
            await sio.enter_room(sid, review_id)
            
            # Track subscription
            session_id = self.session_mapping.get(sid)
            if session_id:
                # Note: We could integrate with websocket_manager here if needed
                logger.info("Client joined review room", 
                           socket_id=sid, session_id=session_id, review_id=review_id)
            
            await sio.emit('joined_review_room', {
                'review_id': review_id,
                'timestamp': time.time()
            }, room=sid)
            
        except Exception as e:
            logger.error("Error joining review room", 
                        socket_id=sid, error=str(e), exc_info=True)
            await sio.emit('error', {'message': f'Failed to join room: {str(e)}'}, room=sid)
    
    async def handle_leave_review_room(self, sid: str, data: dict) -> None:
        """Handle leaving a review room"""
        try:
            review_id = data.get('review_id')
            if not review_id:
                await sio.emit('error', {'message': 'review_id required'}, room=sid)
                return
            
            # Leave Socket.IO room
            await sio.leave_room(sid, review_id)
            
            session_id = self.session_mapping.get(sid)
            logger.info("Client left review room", 
                       socket_id=sid, session_id=session_id, review_id=review_id)
            
            await sio.emit('left_review_room', {
                'review_id': review_id,
                'timestamp': time.time()
            }, room=sid)
            
        except Exception as e:
            logger.error("Error leaving review room", 
                        socket_id=sid, error=str(e), exc_info=True)
            await sio.emit('error', {'message': f'Failed to leave room: {str(e)}'}, room=sid)
    
    async def handle_ping(self, sid: str, data: dict) -> None:
        """Handle ping for latency tracking"""
        await sio.emit('pong', {
            'timestamp': data.get('timestamp', time.time()),
            'server_time': time.time()
        }, room=sid)
    
    async def broadcast_agent_event(
        self, 
        event_type: str, 
        review_id: str, 
        agent_type: str, 
        data: Dict[str, Any],
        message: Optional[str] = None
    ) -> None:
        """Broadcast agent event to Socket.IO clients"""
        try:
            event_data = {
                'event_type': event_type,
                'review_id': review_id,
                'agent_type': agent_type,
                'timestamp': time.time(),
                'data': data,
                'message': message
            }
            
            # Broadcast to all clients in the review room
            await sio.emit(event_type, event_data, room=review_id)
            
            logger.debug("Broadcasted agent event via Socket.IO", 
                        event_type=event_type, review_id=review_id, agent_type=agent_type)
                        
        except Exception as e:
            logger.error("Error broadcasting agent event via Socket.IO", 
                        event_type=event_type, review_id=review_id, error=str(e), exc_info=True)
    
    async def broadcast_review_event(
        self, 
        event_type: str, 
        review_id: str, 
        data: Dict[str, Any],
        message: Optional[str] = None
    ) -> None:
        """Broadcast review event to Socket.IO clients"""
        await self.broadcast_agent_event(event_type, review_id, "system", data, message)
    
    def _extract_session_id(self, query_string: str) -> Optional[str]:
        """Extract session_id from query string"""
        try:
            params = {}
            for param in query_string.split('&'):
                if '=' in param:
                    key, value = param.split('=', 1)
                    params[key] = value
            return params.get('session_id')
        except Exception:
            return None


# Global adapter instance
socketio_adapter = SocketIOAdapter()