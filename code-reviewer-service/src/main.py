"""
Multi-Agent Code Reviewer Microservice
FastAPI Application Entry Point
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

from src.api.middleware.logging import LoggingMiddleware
from src.api.routers import health, review, websocket
from src.config.settings import get_settings
from src.utils.monitoring import setup_monitoring, setup_structured_logging

logger = structlog.get_logger()
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager"""
    logger.info("Starting Multi-Agent Code Reviewer Service", 
                version="0.1.0", environment=settings.environment)
    
    # Initialize monitoring
    if settings.enable_telemetry:
        setup_monitoring()
    
    # Initialize structured logging
    setup_structured_logging(settings.log_level)
    
    # Initialize database connection pool
    try:
        from src.database.connection import init_db
        await init_db()
        logger.info("Database connection initialized")
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        # Continue startup - database will be marked as unhealthy
    
    # TODO: Initialize Redis connection
    # TODO: Initialize Claude SDK client
    
    logger.info("Service startup complete")
    
    yield
    
    # Cleanup
    logger.info("Shutting down Multi-Agent Code Reviewer Service")
    
    # Close database connections
    try:
        from src.database.connection import close_db
        await close_db()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error("Error closing database connections", error=str(e))
    
    # TODO: Close Redis connections
    # TODO: Cleanup Claude SDK resources


def create_app() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title=settings.app_name,
        description="Multi-Agent Code Review System with Claude Code SDK",
        version="0.1.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan
    )
    
    # CORS Middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Compression Middleware
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Custom Logging Middleware
    app.add_middleware(LoggingMiddleware)
    
    # Health Check Router (no auth required)
    app.include_router(health.router, prefix="/health", tags=["health"])
    
    # Main API Routers
    app.include_router(review.router, prefix="/api/v1/review", tags=["review"])
    app.include_router(websocket.router, prefix="/api/v1/websocket", tags=["websocket"])
    
    # Initialize OpenTelemetry instrumentation
    if settings.enable_telemetry:
        FastAPIInstrumentor.instrument_app(app)
    
    return app


# Create application instance
app = create_app()


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": settings.app_name,
        "version": "0.1.0",
        "environment": settings.environment,
        "status": "ready"
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True
    )