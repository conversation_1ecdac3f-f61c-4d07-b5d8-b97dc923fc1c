"""
Database Session Management
Session lifecycle, transactions, and utilities
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON>ator, Optional, Any, Dict
import functools
import time

import structlog
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, DataError
from sqlalchemy import text

from src.database.connection import db_manager
from src.utils.monitoring import get_tracer, log_performance_metrics

logger = structlog.get_logger()


class TransactionError(Exception):
    """Custom exception for transaction-related errors"""
    pass


class SessionManager:
    """
    Advanced session management with transaction handling
    """
    
    def __init__(self):
        self.tracer = get_tracer(__name__)
    
    @asynccontextmanager
    async def transaction(
        self,
        session: Optional[AsyncSession] = None,
        rollback_on_exception: bool = True,
        isolation_level: Optional[str] = None
    ) -> AsyncGenerator[AsyncSession, None]:
        """
        Database transaction context manager
        
        Args:
            session: Existing session to use (creates new if None)
            rollback_on_exception: Whether to rollback on exception
            isolation_level: Transaction isolation level (SERI<PERSON><PERSON><PERSON>ABLE, REPEATABLE READ, etc.)
            
        Usage:
            async with session_manager.transaction() as session:
                await session.execute(query)
                # Automatic commit on success, rollback on exception
        """
        
        with self.tracer.start_as_current_span("database_transaction") as span:
            start_time = time.time()
            created_session = session is None
            
            try:
                # Use existing session or create new one
                if session is None:
                    async with db_manager.get_session() as new_session:
                        session = new_session
                
                # Set isolation level if specified
                if isolation_level:
                    await session.execute(
                        text(f"SET TRANSACTION ISOLATION LEVEL {isolation_level}")
                    )
                    span.set_attribute("isolation_level", isolation_level)
                
                # Begin transaction
                if created_session:
                    async with session.begin():
                        yield session
                else:
                    # Use existing transaction
                    yield session
                
                # Transaction is automatically committed by async with session.begin()
                duration = time.time() - start_time
                
                span.set_attribute("transaction_success", True)
                span.set_attribute("duration_ms", duration * 1000)
                
                log_performance_metrics(
                    operation="database_transaction",
                    duration_ms=duration * 1000,
                    success=True,
                    additional_data={"created_session": created_session}
                )
                
                logger.debug(
                    "Transaction completed successfully",
                    duration_ms=round(duration * 1000, 2),
                    created_session=created_session
                )
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Rollback is handled automatically by async with session.begin()
                if rollback_on_exception and session:
                    try:
                        await session.rollback()
                        logger.info("Transaction rolled back due to exception")
                    except Exception as rollback_error:
                        logger.error(
                            "Failed to rollback transaction",
                            rollback_error=str(rollback_error)
                        )
                
                span.set_attribute("transaction_success", False)
                span.set_attribute("error", str(e))
                span.record_exception(e)
                
                log_performance_metrics(
                    operation="database_transaction",
                    duration_ms=duration * 1000,
                    success=False,
                    additional_data={"error": str(e)}
                )
                
                logger.error(
                    "Transaction failed",
                    error=str(e),
                    duration_ms=round(duration * 1000, 2),
                    exc_info=True
                )
                
                # Re-raise with context
                if isinstance(e, IntegrityError):
                    raise TransactionError(f"Data integrity violation: {str(e)}")
                elif isinstance(e, DataError):
                    raise TransactionError(f"Data validation error: {str(e)}")
                else:
                    raise TransactionError(f"Transaction failed: {str(e)}")
    
    async def execute_in_transaction(
        self,
        operation_func,
        *args,
        isolation_level: Optional[str] = None,
        retry_attempts: int = 3,
        **kwargs
    ) -> Any:
        """
        Execute operation in transaction with retry logic
        
        Args:
            operation_func: Function to execute in transaction (must accept session as first arg)
            *args: Arguments to pass to operation_func
            isolation_level: Transaction isolation level
            retry_attempts: Number of retry attempts for recoverable errors
            **kwargs: Keyword arguments to pass to operation_func
            
        Returns:
            Result of operation_func
        """
        
        last_exception = None
        
        for attempt in range(retry_attempts):
            try:
                async with self.transaction(isolation_level=isolation_level) as session:
                    return await operation_func(session, *args, **kwargs)
                    
            except (SQLAlchemyError, TransactionError) as e:
                last_exception = e
                
                # Check if error is recoverable
                if self._is_recoverable_error(e) and attempt < retry_attempts - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    
                    logger.warning(
                        "Recoverable transaction error, retrying",
                        attempt=attempt + 1,
                        max_attempts=retry_attempts,
                        wait_time=wait_time,
                        error=str(e)
                    )
                    
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # Non-recoverable error or max attempts reached
                    break
        
        # Re-raise the last exception
        raise last_exception
    
    def _is_recoverable_error(self, error: Exception) -> bool:
        """Check if error is recoverable (worth retrying)"""
        
        error_str = str(error).lower()
        
        # Recoverable errors (connection issues, deadlocks, etc.)
        recoverable_patterns = [
            "connection",
            "timeout",
            "deadlock",
            "serialization failure",
            "could not serialize access",
            "connection reset"
        ]
        
        return any(pattern in error_str for pattern in recoverable_patterns)
    
    async def bulk_insert(
        self,
        session: AsyncSession,
        model_class,
        data: list[dict],
        batch_size: int = 1000,
        on_conflict: str = "ignore"  # "ignore", "update", "error"
    ) -> Dict[str, Any]:
        """
        Perform bulk insert operation with batching
        
        Args:
            session: Database session
            model_class: SQLAlchemy model class
            data: List of dictionaries to insert
            batch_size: Number of records per batch
            on_conflict: How to handle conflicts
            
        Returns:
            Insert statistics
        """
        
        with self.tracer.start_as_current_span("bulk_insert") as span:
            span.set_attribute("model", model_class.__name__)
            span.set_attribute("total_records", len(data))
            span.set_attribute("batch_size", batch_size)
            
            start_time = time.time()
            inserted_count = 0
            batch_count = 0
            
            try:
                # Process in batches
                for i in range(0, len(data), batch_size):
                    batch = data[i:i + batch_size]
                    batch_count += 1
                    
                    # Create model instances
                    instances = [model_class(**item) for item in batch]
                    
                    # Bulk insert
                    session.add_all(instances)
                    await session.flush()  # Get IDs without committing
                    
                    inserted_count += len(instances)
                    
                    logger.debug(
                        "Bulk insert batch completed",
                        batch=batch_count,
                        batch_size=len(instances),
                        total_inserted=inserted_count
                    )
                
                duration = time.time() - start_time
                
                span.set_attribute("inserted_count", inserted_count)
                span.set_attribute("batch_count", batch_count)
                
                log_performance_metrics(
                    operation="bulk_insert",
                    duration_ms=duration * 1000,
                    success=True,
                    additional_data={
                        "model": model_class.__name__,
                        "inserted_count": inserted_count,
                        "batch_count": batch_count
                    }
                )
                
                logger.info(
                    "Bulk insert completed",
                    model=model_class.__name__,
                    inserted_count=inserted_count,
                    batch_count=batch_count,
                    duration_ms=round(duration * 1000, 2)
                )
                
                return {
                    "success": True,
                    "inserted_count": inserted_count,
                    "batch_count": batch_count,
                    "duration_ms": round(duration * 1000, 2)
                }
                
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Bulk insert failed",
                    model=model_class.__name__,
                    error=str(e),
                    inserted_count=inserted_count,
                    duration_ms=round(duration * 1000, 2),
                    exc_info=True
                )
                
                span.record_exception(e)
                
                return {
                    "success": False,
                    "error": str(e),
                    "inserted_count": inserted_count,
                    "duration_ms": round(duration * 1000, 2)
                }
    
    async def execute_batch_queries(
        self,
        session: AsyncSession,
        queries: list[str],
        params: list[dict] = None
    ) -> Dict[str, Any]:
        """
        Execute multiple queries in a single transaction
        
        Args:
            session: Database session
            queries: List of SQL queries
            params: List of parameter dictionaries (one per query)
            
        Returns:
            Execution results
        """
        
        with self.tracer.start_as_current_span("batch_queries") as span:
            span.set_attribute("query_count", len(queries))
            
            start_time = time.time()
            results = []
            
            try:
                params = params or [{}] * len(queries)
                
                for i, (query, query_params) in enumerate(zip(queries, params)):
                    result = await session.execute(text(query), query_params)
                    
                    if result.returns_rows:
                        rows = result.fetchall()
                        results.append({
                            "query_index": i,
                            "row_count": len(rows),
                            "data": [dict(row._mapping) for row in rows]
                        })
                    else:
                        results.append({
                            "query_index": i,
                            "affected_rows": result.rowcount
                        })
                
                duration = time.time() - start_time
                
                span.set_attribute("executed_queries", len(results))
                
                logger.info(
                    "Batch queries completed",
                    query_count=len(queries),
                    duration_ms=round(duration * 1000, 2)
                )
                
                return {
                    "success": True,
                    "results": results,
                    "query_count": len(queries),
                    "duration_ms": round(duration * 1000, 2)
                }
                
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Batch queries failed",
                    error=str(e),
                    executed_queries=len(results),
                    duration_ms=round(duration * 1000, 2),
                    exc_info=True
                )
                
                span.record_exception(e)
                
                return {
                    "success": False,
                    "error": str(e),
                    "executed_queries": len(results),
                    "duration_ms": round(duration * 1000, 2)
                }


# Global session manager instance
session_manager = SessionManager()


# Decorator for automatic transaction handling
def transactional(
    isolation_level: Optional[str] = None,
    retry_attempts: int = 3
):
    """
    Decorator to wrap function in database transaction
    
    Usage:
        @transactional()
        async def my_operation(session: AsyncSession, arg1, arg2):
            await session.execute(query)
    """
    
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await session_manager.execute_in_transaction(
                func,
                *args,
                isolation_level=isolation_level,
                retry_attempts=retry_attempts,
                **kwargs
            )
        return wrapper
    
    return decorator


# Convenience functions
async def get_session() -> AsyncSession:
    """Get database session - convenience function"""
    async with db_manager.get_session() as session:
        return session


@asynccontextmanager
async def transaction() -> AsyncGenerator[AsyncSession, None]:
    """Create transaction context - convenience function"""
    async with session_manager.transaction() as session:
        yield session