"""
Alembic Migration Environment
Database schema migration configuration
"""

import asyncio
import os
import sys
from logging.config import fileConfig
from pathlib import Path

from sqlalchemy import pool
from sqlalchemy.engine import Connection
from sqlalchemy.ext.asyncio import create_async_engine

from alembic import context

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent.parent
sys.path.insert(0, str(src_dir))

# Import models and settings
from src.models.database.base import Base
from src.config.settings import get_settings

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config
settings = get_settings()

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the SQLAlchemy URL from settings
config.set_main_option('sqlalchemy.url', settings.database_url)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """
    Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well. By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
        compare_server_default=True,
        render_as_batch=True,  # For SQLite compatibility if needed
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection: Connection) -> None:
    """Run migrations with database connection"""
    
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        compare_type=True,
        compare_server_default=True,
        render_as_batch=True,
        # Include custom migration options
        process_revision_directives=process_revision_directives,
        # User-defined options
        user_module_prefix='code_reviewer_',
        include_schemas=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def process_revision_directives(context, revision, directives):
    """
    Custom revision processing
    Add custom logic for migration generation
    """
    
    # Skip empty migrations
    migration = directives[0]
    if getattr(migration, 'upgrade_ops', None) is not None:
        ops = migration.upgrade_ops.ops
        if not ops:
            directives[:] = []
            print("Skipping empty migration")
            return
    
    # Add custom migration metadata
    if migration.message:
        migration.message = f"[{settings.environment}] {migration.message}"


async def run_async_migrations() -> None:
    """
    Run migrations in async mode.
    
    This is the main function used when running migrations
    with the async engine.
    """
    
    # Get database URL
    database_url = settings.database_url
    
    # Create async engine
    connectable = create_async_engine(
        database_url,
        poolclass=pool.NullPool,  # Don't use connection pooling for migrations
        echo=settings.debug,
        future=True
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


def run_migrations_online() -> None:
    """
    Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    
    # Run async migrations
    asyncio.run(run_async_migrations())


# Determine which mode to run in
if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()