# code-reviewer-service/src/models/context/context_provider.py
"""
ContextProvider - Facade Pattern für einheitliche Context-Bereitstellung
Implementiert Unified Interface für Context Access durch alle Agents
"""

import asyncio
from typing import Dict, Any, List, Optional, Union, Type
from datetime import datetime, timedelta
from pathlib import Path
import logging
import json

from .base_context import (
    BaseContext, 
    ContextType, 
    ContextFactory, 
    ContextStatus,
    ContextObserver,
    ContextChange
)
from .codebase_context import CodebaseContext
from .jira_context import JiraContext
from .prompt_context import PromptContext
from .web_search_context import WebSearchContext


class ContextProvider(ContextObserver):
    """
    Facade Pattern für Context Management
    Provides unified interface für Context Access durch alle Agents
    
    Verantwortlichkeiten:
    - Unified Context Creation
    - Context Caching und Performance Optimization
    - Context Validation und Error Handling
    - Integration mit OOP Context System
    - Observer Pattern für Context Change Notifications
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        
        # Context storage
        self._contexts: Dict[str, BaseContext] = {}
        self._context_cache: Dict[str, Dict[str, Any]] = {}
        
        # Configuration
        self.cache_enabled = self.config.get('cache_enabled', True)
        self.cache_ttl = timedelta(minutes=self.config.get('cache_ttl_minutes', 30))
        self.auto_refresh = self.config.get('auto_refresh', True)
        self.max_contexts = self.config.get('max_contexts', 50)
        
        # Performance tracking
        self.performance_stats = {
            'contexts_created': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'validation_errors': 0,
            'refresh_operations': 0,
            'total_requests': 0
        }
        
        # Error handling
        self.error_log: List[Dict[str, Any]] = []
        self.max_error_log_size = 100
        
        self._logger = logging.getLogger(__name__)
        self._logger.info("Initialized ContextProvider with Facade Pattern")

    # ==================== FACADE PATTERN - UNIFIED INTERFACE ====================

    async def get_context(
        self,
        context_type: ContextType,
        context_id: str,
        source_data: Any = None,
        config: Optional[Dict[str, Any]] = None,
        force_refresh: bool = False
    ) -> Optional[BaseContext]:
        """
        Unified Interface für Context Access
        Main entry point for all agents to get contexts
        """
        self.performance_stats['total_requests'] += 1
        
        try:
            # Check cache first (if enabled and not forcing refresh)
            if not force_refresh and self.cache_enabled:
                cached_context = await self._get_cached_context(context_id)
                if cached_context:
                    self.performance_stats['cache_hits'] += 1
                    return cached_context
            
            self.performance_stats['cache_misses'] += 1
            
            # Create or refresh context
            context = await self._create_or_get_context(
                context_type, context_id, source_data, config
            )
            
            if context:
                # Cache the context
                if self.cache_enabled:
                    await self._cache_context(context)
                
                # Register as observer for context changes
                context.attach(self)
                
                return context
            
            return None
            
        except Exception as e:
            await self._log_error(f"Failed to get context {context_id}", e)
            return None

    async def get_multiple_contexts(
        self,
        requests: List[Dict[str, Any]],
        parallel: bool = True
    ) -> Dict[str, Optional[BaseContext]]:
        """
        Get multiple contexts efficiently
        Supports parallel loading for performance
        """
        if parallel:
            return await self._get_contexts_parallel(requests)
        else:
            return await self._get_contexts_sequential(requests)

    async def create_review_context(
        self,
        repository_path: str,
        branch_name: Optional[str] = None,
        ticket_id: Optional[str] = None,
        additional_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Optional[BaseContext]]:
        """
        Convenience method to create complete review context
        Creates all necessary contexts for a code review
        """
        self._logger.info(f"Creating complete review context for repository: {repository_path}")
        
        contexts = {}
        
        # Context requests
        context_requests = []
        
        # 1. Codebase Context (always required)
        context_requests.append({
            'context_type': ContextType.CODEBASE,
            'context_id': f"codebase_{hash(repository_path)}",
            'source_data': {
                'repository_path': repository_path,
                'branch_name': branch_name
            },
            'config': additional_config
        })
        
        # 2. Jira Context (if ticket_id provided)
        if ticket_id:
            context_requests.append({
                'context_type': ContextType.JIRA,
                'context_id': f"jira_{ticket_id}",
                'source_data': ticket_id,
                'config': additional_config
            })
        
        # 3. Prompt Context (for German prompts)
        prompt_source = additional_config.get('prompt_templates') if additional_config else None
        if prompt_source:
            context_requests.append({
                'context_type': ContextType.PROMPT,
                'context_id': "prompts_german",
                'source_data': prompt_source,
                'config': {'language': 'de'}
            })
        
        # 4. Web Search Context (if enabled)
        if additional_config and additional_config.get('enable_web_search'):
            search_queries = additional_config.get('search_queries', [])
            if search_queries:
                context_requests.append({
                    'context_type': ContextType.WEB_SEARCH,
                    'context_id': "web_search",
                    'source_data': search_queries,
                    'config': additional_config.get('web_search_config')
                })
        
        # Get all contexts in parallel
        return await self.get_multiple_contexts(context_requests, parallel=True)

    async def get_agent_context_data(
        self,
        agent_id: str,
        required_contexts: List[ContextType]
    ) -> Dict[str, Any]:
        """
        Get processed context data für specific agent
        Returns agent-ready context data
        """
        self._logger.debug(f"Getting agent context data for: {agent_id}")
        
        agent_context = {
            'agent_id': agent_id,
            'timestamp': datetime.now().isoformat(),
            'contexts': {}
        }
        
        # Get each required context
        for context_type in required_contexts:
            context_id = self._generate_context_id(context_type)
            context = await self._get_context_by_type(context_type)
            
            if context:
                try:
                    context_data = await context.to_agent_context()
                    agent_context['contexts'][context_type.value] = context_data
                except Exception as e:
                    self._logger.error(f"Failed to get agent context for {context_type.value}: {str(e)}")
                    agent_context['contexts'][context_type.value] = {
                        'error': str(e),
                        'status': 'failed'
                    }
            else:
                agent_context['contexts'][context_type.value] = {
                    'status': 'not_available'
                }
        
        return agent_context

    # ==================== CONTEXT MANAGEMENT ====================

    async def _create_or_get_context(
        self,
        context_type: ContextType,
        context_id: str,
        source_data: Any = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Optional[BaseContext]:
        """Create or get existing context"""
        
        # Check if context already exists
        if context_id in self._contexts:
            existing_context = self._contexts[context_id]
            
            # Refresh if auto_refresh is enabled and context is outdated
            if self.auto_refresh and await self._is_context_outdated(existing_context):
                await existing_context.refresh()
            
            return existing_context
        
        # Create new context
        try:
            context = ContextFactory.create_context(
                context_type=context_type,
                context_id=context_id,
                source_data=source_data,
                config=config
            )
            
            # Validate context
            is_valid = await context.validate()
            if not is_valid:
                self.performance_stats['validation_errors'] += 1
                await self._log_error(f"Context validation failed: {context_id}", None)
                return None
            
            # Store context
            self._contexts[context_id] = context
            self.performance_stats['contexts_created'] += 1
            
            # Clean up old contexts if needed
            await self._cleanup_contexts()
            
            self._logger.info(f"Created and validated context: {context_id} ({context_type.value})")
            return context
            
        except Exception as e:
            await self._log_error(f"Failed to create context {context_id}", e)
            return None

    async def _get_contexts_parallel(
        self,
        requests: List[Dict[str, Any]]
    ) -> Dict[str, Optional[BaseContext]]:
        """Get multiple contexts in parallel"""
        self._logger.debug(f"Getting {len(requests)} contexts in parallel")
        
        # Create tasks for parallel execution
        tasks = []
        context_ids = []
        
        for request in requests:
            context_type = request['context_type']
            context_id = request['context_id']
            source_data = request.get('source_data')
            config = request.get('config')
            
            task = self.get_context(context_type, context_id, source_data, config)
            tasks.append(task)
            context_ids.append(context_id)
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Build result dictionary
        contexts = {}
        for i, result in enumerate(results):
            context_id = context_ids[i]
            if isinstance(result, Exception):
                self._logger.error(f"Parallel context loading failed for {context_id}: {str(result)}")
                contexts[context_id] = None
            else:
                contexts[context_id] = result
        
        return contexts

    async def _get_contexts_sequential(
        self,
        requests: List[Dict[str, Any]]
    ) -> Dict[str, Optional[BaseContext]]:
        """Get multiple contexts sequentially"""
        contexts = {}
        
        for request in requests:
            context_type = request['context_type']
            context_id = request['context_id']
            source_data = request.get('source_data')
            config = request.get('config')
            
            context = await self.get_context(context_type, context_id, source_data, config)
            contexts[context_id] = context
        
        return contexts

    # ==================== CACHING SYSTEM ====================

    async def _get_cached_context(self, context_id: str) -> Optional[BaseContext]:
        """Get context from cache if available and valid"""
        if not self.cache_enabled:
            return None
        
        if context_id not in self._contexts:
            return None
        
        context = self._contexts[context_id]
        
        # Check if context is still valid
        if await self._is_context_outdated(context):
            return None
        
        return context

    async def _cache_context(self, context: BaseContext) -> None:
        """Cache context data"""
        if not self.cache_enabled:
            return
        
        cache_data = {
            'context_id': context.context_id,
            'context_type': context.context_type.value,
            'cached_at': datetime.now(),
            'status': context.status.value,
            'last_updated': context.last_updated
        }
        
        self._context_cache[context.context_id] = cache_data

    async def _is_context_outdated(self, context: BaseContext) -> bool:
        """Check if context is outdated and needs refresh"""
        if not context:
            return True
        
        # Check if context is older than cache TTL
        age = datetime.now() - context.last_updated
        if age > self.cache_ttl:
            return True
        
        # Check context status
        if context.status in [ContextStatus.INVALID, ContextStatus.ERROR, ContextStatus.OUTDATED]:
            return True
        
        return False

    async def _cleanup_contexts(self) -> None:
        """Clean up old contexts to maintain performance"""
        if len(self._contexts) <= self.max_contexts:
            return
        
        # Sort contexts by last_updated (oldest first)
        sorted_contexts = sorted(
            self._contexts.items(),
            key=lambda x: x[1].last_updated
        )
        
        # Remove oldest contexts
        contexts_to_remove = len(self._contexts) - self.max_contexts
        for i in range(contexts_to_remove):
            context_id, context = sorted_contexts[i]
            
            # Detach observer
            context.detach(self)
            
            # Remove from storage
            del self._contexts[context_id]
            if context_id in self._context_cache:
                del self._context_cache[context_id]
        
        self._logger.info(f"Cleaned up {contexts_to_remove} old contexts")

    # ==================== UTILITY METHODS ====================

    def _generate_context_id(self, context_type: ContextType) -> str:
        """Generate context ID for type"""
        timestamp = int(datetime.now().timestamp())
        return f"{context_type.value}_{timestamp}"

    async def _get_context_by_type(self, context_type: ContextType) -> Optional[BaseContext]:
        """Get first available context of specific type"""
        for context in self._contexts.values():
            if context.context_type == context_type:
                return context
        return None

    # ==================== OBSERVER PATTERN IMPLEMENTATION ====================

    async def on_context_changed(self, change: ContextChange) -> None:
        """Handle context change notifications"""
        self._logger.debug(f"Context change notification: {change.context_id} -> {change.new_status.value}")
        
        # Update cache
        if change.context_id in self._context_cache:
            self._context_cache[change.context_id]['status'] = change.new_status.value
            self._context_cache[change.context_id]['last_change'] = change.timestamp
        
        # Handle specific status changes
        if change.new_status == ContextStatus.ERROR:
            await self._log_error(f"Context error: {change.context_id}", Exception(change.message))
        elif change.new_status == ContextStatus.OUTDATED:
            # Trigger refresh if auto_refresh is enabled
            if self.auto_refresh and change.context_id in self._contexts:
                context = self._contexts[change.context_id]
                await context.refresh()
                self.performance_stats['refresh_operations'] += 1

    # ==================== ERROR HANDLING ====================

    async def _log_error(self, message: str, exception: Optional[Exception]) -> None:
        """Log error with context"""
        error_entry = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'exception': str(exception) if exception else None,
            'exception_type': type(exception).__name__ if exception else None
        }
        
        self.error_log.append(error_entry)
        
        # Limit error log size
        if len(self.error_log) > self.max_error_log_size:
            self.error_log = self.error_log[-self.max_error_log_size:]
        
        # Log to system logger
        if exception:
            self._logger.error(f"{message}: {str(exception)}")
        else:
            self._logger.error(message)

    # ==================== PUBLIC API METHODS ====================

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return {
            **self.performance_stats,
            'active_contexts': len(self._contexts),
            'cached_contexts': len(self._context_cache),
            'cache_hit_rate': (
                self.performance_stats['cache_hits'] / 
                max(1, self.performance_stats['total_requests'])
            ) * 100,
            'error_count': len(self.error_log)
        }

    def get_context_summary(self) -> Dict[str, Any]:
        """Get summary of all contexts"""
        contexts_by_type = {}
        for context in self._contexts.values():
            context_type = context.context_type.value
            if context_type not in contexts_by_type:
                contexts_by_type[context_type] = []
            
            contexts_by_type[context_type].append({
                'id': context.context_id,
                'status': context.status.value,
                'last_updated': context.last_updated.isoformat(),
                'creation_time': context.creation_time.isoformat(),
                'observer_count': len(context._observers)
            })
        
        return {
            'total_contexts': len(self._contexts),
            'contexts_by_type': contexts_by_type,
            'cache_enabled': self.cache_enabled,
            'auto_refresh': self.auto_refresh,
            'performance_stats': self.get_performance_stats()
        }

    async def refresh_all_contexts(self) -> Dict[str, bool]:
        """Refresh all active contexts"""
        self._logger.info("Refreshing all active contexts")
        
        refresh_results = {}
        
        # Refresh all contexts in parallel
        tasks = []
        context_ids = []
        
        for context_id, context in self._contexts.items():
            tasks.append(context.refresh())
            context_ids.append(context_id)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            context_id = context_ids[i]
            if isinstance(result, Exception):
                refresh_results[context_id] = False
                await self._log_error(f"Failed to refresh context {context_id}", result)
            else:
                refresh_results[context_id] = result
        
        self.performance_stats['refresh_operations'] += len(refresh_results)
        
        return refresh_results

    async def validate_all_contexts(self) -> Dict[str, bool]:
        """Validate all active contexts"""
        validation_results = {}
        
        for context_id, context in self._contexts.items():
            try:
                is_valid = await context.validate()
                validation_results[context_id] = is_valid
                if not is_valid:
                    self.performance_stats['validation_errors'] += 1
            except Exception as e:
                validation_results[context_id] = False
                await self._log_error(f"Validation failed for context {context_id}", e)
        
        return validation_results

    def clear_cache(self) -> None:
        """Clear all cached contexts"""
        self._context_cache.clear()
        self._logger.info("Context cache cleared")

    def get_error_log(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get recent error log entries"""
        if limit:
            return self.error_log[-limit:]
        return self.error_log.copy()


# ==================== GLOBAL CONTEXT PROVIDER INSTANCE ====================

# Global singleton instance for easy access
_global_context_provider: Optional[ContextProvider] = None

def get_context_provider(config: Optional[Dict[str, Any]] = None) -> ContextProvider:
    """Get global context provider instance"""
    global _global_context_provider
    
    if _global_context_provider is None:
        _global_context_provider = ContextProvider(config)
    
    return _global_context_provider

def reset_context_provider() -> None:
    """Reset global context provider (for testing)"""
    global _global_context_provider
    _global_context_provider = None