# code-reviewer-service/src/models/context/codebase_context.py
"""
CodebaseContext - Specialized Context für File System und Git Operations
Erweitert BaseContext für Codebase-spezifische Funktionalität
"""

import os
import subprocess
import mimetypes
import fnmatch
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from datetime import datetime
import logging
import asyncio

from .base_context import BaseContext, ContextType, ContextFactory


class CodebaseContext(BaseContext):
    """
    CodebaseContext für File System und Git Operations
    
    Verantwortlichkeiten:
    - Git Repository Information
    - File System Analysis
    - Code File Categorization
    - Change Detection
    - Dependency Analysis
    """
    
    def __init__(
        self,
        context_id: str,
        repository_path: str,
        branch_name: Optional[str] = None,
        validation_strategy=None,
        extraction_strategy=None
    ):
        super().__init__(
            context_id=context_id,
            context_type=ContextType.CODEBASE,
            validation_strategy=validation_strategy,
            extraction_strategy=extraction_strategy
        )
        
        self.repository_path = Path(repository_path).resolve()
        self.branch_name = branch_name
        
        # Codebase-specific configuration
        self.supported_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.vue', '.svelte'
        }
        
        self.ignore_patterns = {
            '*.pyc', '__pycache__', '.git', 'node_modules', '.venv', 'venv',
            '.env', '*.log', '*.tmp', '.DS_Store', '*.min.js', '*.min.css'
        }
        
        # File categorization
        self.file_categories = {
            'source_files': [],
            'test_files': [],
            'config_files': [],
            'documentation': [],
            'build_files': [],
            'other_files': []
        }
        
        self._logger.info(f"Initialized CodebaseContext for: {self.repository_path}")

    @classmethod
    def create(
        cls,
        context_id: str,
        source_data: Any,
        config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> 'CodebaseContext':
        """Factory method für CodebaseContext creation"""
        
        # source_data kann ein Pfad (str/Path) oder Dict mit Details sein
        if isinstance(source_data, (str, Path)):
            repository_path = str(source_data)
            branch_name = kwargs.get('branch_name')
        elif isinstance(source_data, dict):
            repository_path = source_data.get('repository_path')
            branch_name = source_data.get('branch_name')
        else:
            raise ValueError(f"Invalid source_data type for CodebaseContext: {type(source_data)}")
        
        if not repository_path:
            raise ValueError("repository_path is required for CodebaseContext")
        
        # Extract additional parameters from config
        if config:
            branch_name = branch_name or config.get('branch_name')
        
        return cls(
            context_id=context_id,
            repository_path=repository_path,
            branch_name=branch_name,
            **kwargs
        )

    async def _validate_specific(self) -> bool:
        """Spezifische Validation für CodebaseContext"""
        try:
            # Check if repository path exists
            if not self.repository_path.exists():
                self._logger.error(f"Repository path does not exist: {self.repository_path}")
                return False
            
            # Check if it's a directory
            if not self.repository_path.is_dir():
                self._logger.error(f"Repository path is not a directory: {self.repository_path}")
                return False
            
            # Check if it's a git repository
            git_dir = self.repository_path / '.git'
            if not git_dir.exists():
                self._logger.warning(f"Not a git repository: {self.repository_path}")
                # Not a git repo is valid, just log warning
            
            # Validate branch if specified
            if self.branch_name:
                current_branch = await self._get_current_branch()
                if current_branch != self.branch_name:
                    self._logger.warning(f"Current branch ({current_branch}) differs from specified branch ({self.branch_name})")
            
            return True
            
        except Exception as e:
            self._logger.error(f"CodebaseContext validation error: {str(e)}")
            return False

    async def _build_specific_agent_context(self) -> Dict[str, Any]:
        """Build spezifischen Agent Context für Codebase"""
        
        # Ensure we have current data
        await self._analyze_codebase()
        
        return {
            "repository_info": {
                "path": str(self.repository_path),
                "branch": self.branch_name or await self._get_current_branch(),
                "is_git_repo": await self._is_git_repository(),
                "total_files": len(self._raw_data.get('all_files', [])),
                "code_files": len(self._raw_data.get('code_files', []))
            },
            "file_analysis": {
                "categories": self.file_categories,
                "extensions_found": self._raw_data.get('extensions_found', []),
                "languages_detected": self._raw_data.get('languages_detected', [])
            },
            "git_info": self._raw_data.get('git_info', {}),
            "file_structure": self._raw_data.get('file_structure', {}),
            "change_summary": self._raw_data.get('change_summary', {})
        }

    async def refresh(self) -> bool:
        """Refresh codebase data from repository"""
        try:
            self._logger.info(f"Refreshing codebase context: {self.context_id}")
            
            # Clear previous data
            self._raw_data.clear()
            self._processed_data.clear()
            
            # Re-analyze codebase
            await self._analyze_codebase()
            
            # Update timestamp
            self.last_updated = datetime.now()
            
            return True
            
        except Exception as e:
            self._logger.error(f"Failed to refresh codebase context: {str(e)}")
            return False

    # ==================== CODEBASE ANALYSIS METHODS ====================

    async def _analyze_codebase(self) -> None:
        """Comprehensive codebase analysis"""
        self._logger.debug("Starting codebase analysis")
        
        # Run analysis tasks in parallel
        tasks = [
            self._scan_files(),
            self._get_git_info(),
            self._analyze_file_structure(),
            self._detect_languages(),
            self._analyze_changes()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Categorize files
        await self._categorize_files()
        
        self._logger.debug("Codebase analysis completed")

    async def _scan_files(self) -> None:
        """Scan all files in repository"""
        all_files = []
        code_files = []
        extensions_found = set()
        
        for root, dirs, files in os.walk(self.repository_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not any(fnmatch.fnmatch(d, pattern) for pattern in self.ignore_patterns)]
            
            for file in files:
                # Skip ignored files
                if any(fnmatch.fnmatch(file, pattern) for pattern in self.ignore_patterns):
                    continue
                
                file_path = Path(root) / file
                relative_path = file_path.relative_to(self.repository_path)
                
                all_files.append(str(relative_path))
                
                # Track extensions
                extension = file_path.suffix.lower()
                if extension:
                    extensions_found.add(extension)
                
                # Identify code files
                if extension in self.supported_extensions:
                    code_files.append(str(relative_path))
        
        self._raw_data['all_files'] = all_files
        self._raw_data['code_files'] = code_files
        self._raw_data['extensions_found'] = sorted(list(extensions_found))

    async def _get_git_info(self) -> None:
        """Get git repository information"""
        git_info = {}
        
        try:
            if await self._is_git_repository():
                # Current branch
                git_info['current_branch'] = await self._get_current_branch()
                
                # Remote URL
                git_info['remote_url'] = await self._get_remote_url()
                
                # Last commit
                git_info['last_commit'] = await self._get_last_commit_info()
                
                # Repository status
                git_info['status'] = await self._get_git_status()
                
        except Exception as e:
            self._logger.warning(f"Failed to get git info: {str(e)}")
            git_info['error'] = str(e)
        
        self._raw_data['git_info'] = git_info

    async def _analyze_file_structure(self) -> None:
        """Analyze repository file structure"""
        structure = {}
        
        for root, dirs, files in os.walk(self.repository_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not any(fnmatch.fnmatch(d, pattern) for pattern in self.ignore_patterns)]
            
            relative_root = Path(root).relative_to(self.repository_path)
            if str(relative_root) == '.':
                key = 'root'
            else:
                key = str(relative_root)
            
            structure[key] = {
                'directories': [d for d in dirs],
                'files': [f for f in files if not any(fnmatch.fnmatch(f, pattern) for pattern in self.ignore_patterns)],
                'file_count': len([f for f in files if not any(fnmatch.fnmatch(f, pattern) for pattern in self.ignore_patterns)])
            }
        
        self._raw_data['file_structure'] = structure

    async def _detect_languages(self) -> None:
        """Detect programming languages in codebase"""
        language_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React JSX',
            '.tsx': 'React TSX',
            '.java': 'Java',
            '.cpp': 'C++',
            '.c': 'C',
            '.h': 'C Header',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.vue': 'Vue.js',
            '.svelte': 'Svelte'
        }
        
        languages_detected = set()
        extensions_found = self._raw_data.get('extensions_found', [])
        
        for ext in extensions_found:
            if ext in language_map:
                languages_detected.add(language_map[ext])
        
        self._raw_data['languages_detected'] = sorted(list(languages_detected))

    async def _analyze_changes(self) -> None:
        """Analyze recent changes if git repository"""
        change_summary = {}
        
        try:
            if await self._is_git_repository():
                # Get modified files
                result = await self._run_git_command(['status', '--porcelain'])
                if result:
                    modified_files = []
                    for line in result.strip().split('\n'):
                        if line.strip():
                            status = line[:2].strip()
                            file_path = line[3:].strip()
                            modified_files.append({
                                'status': status,
                                'file': file_path
                            })
                    change_summary['modified_files'] = modified_files
                    change_summary['has_changes'] = len(modified_files) > 0
                else:
                    change_summary['has_changes'] = False
                    change_summary['modified_files'] = []
        
        except Exception as e:
            self._logger.warning(f"Failed to analyze changes: {str(e)}")
            change_summary['error'] = str(e)
        
        self._raw_data['change_summary'] = change_summary

    async def _categorize_files(self) -> None:
        """Categorize files by type and purpose"""
        all_files = self._raw_data.get('all_files', [])
        
        # Reset categories
        for category in self.file_categories:
            self.file_categories[category] = []
        
        for file_path in all_files:
            file_path_lower = file_path.lower()
            file_name = Path(file_path).name.lower()
            
            # Test files
            if ('test' in file_path_lower or 'spec' in file_path_lower or 
                file_name.startswith('test_') or file_name.endswith('_test.py') or
                file_name.endswith('.test.js') or file_name.endswith('.spec.js')):
                self.file_categories['test_files'].append(file_path)
            
            # Configuration files
            elif (file_name in ['package.json', 'requirements.txt', 'setup.py', 'pyproject.toml',
                               'tsconfig.json', 'webpack.config.js', 'babel.config.js',
                               'docker-compose.yml', 'dockerfile', '.env', '.env.example'] or
                  file_name.endswith('.config.js') or file_name.endswith('.config.ts') or
                  file_name.endswith('.yml') or file_name.endswith('.yaml')):
                self.file_categories['config_files'].append(file_path)
            
            # Documentation
            elif (file_name.endswith('.md') or file_name.endswith('.rst') or
                  file_name.endswith('.txt') or file_name in ['readme', 'changelog']):
                self.file_categories['documentation'].append(file_path)
            
            # Build files
            elif (file_name in ['makefile', 'build.gradle', 'pom.xml', 'cargo.toml'] or
                  file_path_lower.startswith('build/') or file_path_lower.startswith('dist/')):
                self.file_categories['build_files'].append(file_path)
            
            # Source files (code files that aren't tests)
            elif Path(file_path).suffix.lower() in self.supported_extensions:
                self.file_categories['source_files'].append(file_path)
            
            # Everything else
            else:
                self.file_categories['other_files'].append(file_path)

    # ==================== GIT HELPER METHODS ====================

    async def _is_git_repository(self) -> bool:
        """Check if the path is a git repository"""
        return (self.repository_path / '.git').exists()

    async def _get_current_branch(self) -> Optional[str]:
        """Get current git branch"""
        try:
            result = await self._run_git_command(['branch', '--show-current'])
            return result.strip() if result else None
        except Exception:
            return None

    async def _get_remote_url(self) -> Optional[str]:
        """Get remote origin URL"""
        try:
            result = await self._run_git_command(['config', '--get', 'remote.origin.url'])
            return result.strip() if result else None
        except Exception:
            return None

    async def _get_last_commit_info(self) -> Dict[str, Any]:
        """Get information about the last commit"""
        try:
            # Get commit hash
            hash_result = await self._run_git_command(['rev-parse', 'HEAD'])
            commit_hash = hash_result.strip() if hash_result else None
            
            # Get commit message
            msg_result = await self._run_git_command(['log', '-1', '--pretty=format:%s'])
            commit_message = msg_result.strip() if msg_result else None
            
            # Get commit author and date
            author_result = await self._run_git_command(['log', '-1', '--pretty=format:%an <%ae>'])
            commit_author = author_result.strip() if author_result else None
            
            date_result = await self._run_git_command(['log', '-1', '--pretty=format:%ci'])
            commit_date = date_result.strip() if date_result else None
            
            return {
                'hash': commit_hash,
                'message': commit_message,
                'author': commit_author,
                'date': commit_date
            }
        except Exception as e:
            return {'error': str(e)}

    async def _get_git_status(self) -> Dict[str, Any]:
        """Get git repository status"""
        try:
            result = await self._run_git_command(['status', '--porcelain'])
            
            if not result:
                return {'clean': True, 'changes': []}
            
            changes = []
            for line in result.strip().split('\n'):
                if line.strip():
                    status = line[:2]
                    file_path = line[3:]
                    changes.append({'status': status, 'file': file_path})
            
            return {'clean': False, 'changes': changes}
            
        except Exception as e:
            return {'error': str(e)}

    async def _run_git_command(self, args: List[str]) -> Optional[str]:
        """Run git command and return output"""
        try:
            process = await asyncio.create_subprocess_exec(
                'git', *args,
                cwd=self.repository_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode('utf-8')
            else:
                self._logger.warning(f"Git command failed: git {' '.join(args)}, stderr: {stderr.decode('utf-8')}")
                return None
                
        except Exception as e:
            self._logger.error(f"Error running git command: {str(e)}")
            return None

    # ==================== PUBLIC UTILITY METHODS ====================

    def get_files_by_category(self, category: str) -> List[str]:
        """Get files by category"""
        return self.file_categories.get(category, [])

    def get_files_by_extension(self, extension: str) -> List[str]:
        """Get files by extension"""
        if not extension.startswith('.'):
            extension = '.' + extension
        
        all_files = self._raw_data.get('all_files', [])
        return [f for f in all_files if Path(f).suffix.lower() == extension.lower()]

    def is_code_file(self, file_path: str) -> bool:
        """Check if file is a code file"""
        return Path(file_path).suffix.lower() in self.supported_extensions

    def get_repository_summary(self) -> Dict[str, Any]:
        """Get comprehensive repository summary"""
        return {
            "path": str(self.repository_path),
            "total_files": len(self._raw_data.get('all_files', [])),
            "code_files": len(self._raw_data.get('code_files', [])),
            "languages": self._raw_data.get('languages_detected', []),
            "has_git": self._raw_data.get('git_info', {}).get('current_branch') is not None,
            "categories": {k: len(v) for k, v in self.file_categories.items()},
            "has_changes": self._raw_data.get('change_summary', {}).get('has_changes', False)
        }


# Register CodebaseContext with Factory
ContextFactory.register_context_class(ContextType.CODEBASE, CodebaseContext)