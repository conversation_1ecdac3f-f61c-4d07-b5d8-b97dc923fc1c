"""
Base Database Models
SQLAlchemy base classes and common model patterns
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import structlog
from sqlalchemy import DateTime, String, Text, <PERSON><PERSON><PERSON>, Integer, JSON
from sqlalchemy import event
from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID

logger = structlog.get_logger()


class Base(AsyncAttrs, DeclarativeBase):
    """
    Base class for all database models
    Provides common functionality and patterns
    """
    
    # Enable async support for SQLAlchemy 2.0
    __abstract__ = True
    
    @declared_attr
    def __tablename__(cls) -> str:
        """Generate table name from class name"""
        # Convert CamelCase to snake_case
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model instance to dictionary"""
        result = {}
        
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            
            # Handle special types
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            elif isinstance(value, uuid.UUID):
                result[column.name] = str(value)
            else:
                result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update model instance from dictionary"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of model"""
        attrs = []
        for column in self.__table__.columns:
            if column.primary_key:
                value = getattr(self, column.name, None)
                if value is not None:
                    attrs.append(f"{column.name}={value}")
        
        attr_str = ', '.join(attrs)
        return f"<{self.__class__.__name__}({attr_str})>"


class TimestampMixin:
    """Mixin for created_at and updated_at timestamps"""
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Record creation timestamp"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Record last update timestamp"
    )


class UUIDMixin:
    """Mixin for UUID primary key"""
    
    id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        nullable=False,
        comment="Unique identifier"
    )


class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    
    deleted_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="Soft delete timestamp"
    )
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Soft delete flag"
    )
    
    def soft_delete(self) -> None:
        """Mark record as deleted"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """Restore soft deleted record"""
        self.is_deleted = False
        self.deleted_at = None


class AuditMixin:
    """Mixin for audit trail functionality"""
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="User who created the record"
    )
    
    updated_by: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        comment="User who last updated the record"
    )
    
    version: Mapped[int] = mapped_column(
        Integer,
        default=1,
        nullable=False,
        comment="Record version for optimistic locking"
    )


class BaseModel(Base, UUIDMixin, TimestampMixin):
    """
    Standard base model with UUID primary key and timestamps
    Use this for most models
    """
    
    __abstract__ = True


class BaseAuditModel(BaseModel, AuditMixin, SoftDeleteMixin):
    """
    Full-featured base model with audit trail and soft delete
    Use this for important business entities
    """
    
    __abstract__ = True
    
    metadata_: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Additional metadata as JSON"
    )
    
    notes: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Additional notes or comments"
    )


# Event listeners for automatic audit tracking
@event.listens_for(BaseAuditModel, 'before_insert', propagate=True)
def receive_before_insert(mapper, connection, target):
    """Set audit fields on insert"""
    # TODO: Get current user from context
    # target.created_by = get_current_user_id()
    target.version = 1


@event.listens_for(BaseAuditModel, 'before_update', propagate=True)
def receive_before_update(mapper, connection, target):
    """Set audit fields on update"""
    # TODO: Get current user from context
    # target.updated_by = get_current_user_id()
    target.version += 1


# Review-specific models
class ReviewModel(BaseAuditModel):
    """Base model for review-related entities"""
    
    __abstract__ = True
    
    review_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        nullable=False,
        comment="Associated review identifier"
    )
    
    status: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="pending",
        comment="Current status"
    )


class AgentModel(BaseModel):
    """Base model for agent-related entities"""
    
    __abstract__ = True
    
    agent_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="Type of agent"
    )
    
    agent_version: Mapped[str] = mapped_column(
        String(50),
        nullable=True,
        default="1.0.0",
        comment="Agent version"
    )
    
    execution_time_ms: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Execution time in milliseconds"
    )
    
    success: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="Execution success flag"
    )
    
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Error message if execution failed"
    )


# Table creation utilities
async def create_all_tables(engine):
    """Create all database tables"""
    
    logger.info("Creating database tables")
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("Database tables created successfully")


async def drop_all_tables(engine):
    """Drop all database tables (use with caution!)"""
    
    logger.warning("Dropping all database tables")
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    logger.warning("All database tables dropped")


# Migration utilities
def get_table_names() -> list[str]:
    """Get list of all table names"""
    return [table.name for table in Base.metadata.tables.values()]


def get_model_registry() -> Dict[str, type]:
    """Get registry of all model classes"""
    registry = {}
    
    def collect_models(cls):
        if hasattr(cls, '__tablename__') and not cls.__abstract__:
            registry[cls.__tablename__] = cls
        
        for subclass in cls.__subclasses__():
            collect_models(subclass)
    
    collect_models(Base)
    return registry


# Health check model for database testing
class HealthCheck(BaseModel):
    """Simple model for database health checks"""
    
    __tablename__ = "health_checks"
    
    service_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="Service performing health check"
    )
    
    check_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="connection",
        comment="Type of health check"
    )
    
    status: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="Health check status"
    )
    
    response_time_ms: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Response time in milliseconds"
    )
    
    details: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="Additional health check details"
    )