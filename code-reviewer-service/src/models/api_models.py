"""
API Models for Multi-Agent Review System
Pydantic models for request/response validation
"""

from typing import Dict, Any, Optional, List
from uuid import UUID
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict


class ReviewMode(str, Enum):
    """Review mode enumeration"""
    QUICK = "quick"
    FULL = "full"  
    AC_ONLY = "ac_only"
    BUG_ANALYSIS = "bug_analysis"
    SUMMARY_ONLY = "summary_only"


class AgentType(str, Enum):
    """Agent type enumeration for multi-agent orchestration"""
    ACCEPTANCE_CRITERIA = "acceptance_criteria"
    BUG_DETECTION = "bug_detection"
    SECURITY_ANALYSIS = "security_analysis"
    LOGIC_ANALYSIS = "logic_analysis"
    CODE_QUALITY = "code_quality"
    ARCHITECTURAL = "architectural"
    SUMMARY = "summary"


class AgentStatus(str, Enum):
    """Agent execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ReviewStatus(str, Enum):
    """Review execution status"""
    STARTED = "started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReviewRequest(BaseModel):
    """Review request model"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True
    )
    
    branch_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Git branch name to review"
    )
    repository_path: str = Field(
        ...,
        min_length=1,
        description="Path to the repository"
    )
    working_directory: Optional[str] = Field(
        None,
        description="Optional working directory override"
    )
    review_mode: ReviewMode = Field(
        ReviewMode.FULL,
        description="Review mode: quick, full, ac_only, bug_analysis, summary_only"
    )
    jira_ticket_id: Optional[str] = Field(
        None,
        pattern=r'^[A-Z]+-\d+$',
        description="JIRA ticket ID (e.g., CMS-1234)"
    )
    include_summary: bool = Field(
        True,
        description="Whether to include summary agent execution"
    )
    agent_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Optional configuration for specific agents"
    )
    context_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Optional context configuration"
    )


class ReviewResponse(BaseModel):
    """Review response model"""
    model_config = ConfigDict(use_enum_values=True)
    
    review_id: UUID = Field(
        ...,
        description="Unique review identifier"
    )
    status: ReviewStatus = Field(
        ...,
        description="Initial review status"
    )
    message: str = Field(
        ...,
        description="Response message"
    )
    estimated_completion_time: Optional[int] = Field(
        None,
        ge=0,
        description="Estimated completion time in seconds"
    )
    websocket_url: Optional[str] = Field(
        None,
        description="WebSocket URL for real-time updates"
    )
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Review creation timestamp"
    )


class AgentStatusInfo(BaseModel):
    """Individual agent status information"""
    model_config = ConfigDict(use_enum_values=True)
    
    agent_type: AgentType = Field(
        ...,
        description="Type of agent"
    )
    status: AgentStatus = Field(
        ...,
        description="Current agent status"
    )
    progress: float = Field(
        0.0,
        ge=0.0,
        le=100.0,
        description="Agent progress percentage (0-100)"
    )
    started_at: Optional[datetime] = Field(
        None,
        description="Agent start timestamp"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="Agent completion timestamp"
    )
    estimated_remaining_time: Optional[int] = Field(
        None,
        ge=0,
        description="Estimated remaining time in seconds"
    )
    error_message: Optional[str] = Field(
        None,
        description="Error message if agent failed"
    )


class ReviewStatusResponse(BaseModel):
    """Detailed review status response"""
    model_config = ConfigDict(use_enum_values=True)
    
    review_id: UUID = Field(
        ...,
        description="Review identifier"
    )
    status: ReviewStatus = Field(
        ...,
        description="Overall review status"
    )
    progress: float = Field(
        0.0,
        ge=0.0,
        le=100.0,
        description="Overall progress percentage (0-100)"
    )
    agent_statuses: Dict[AgentType, AgentStatusInfo] = Field(
        default_factory=dict,
        description="Individual agent status information"
    )
    started_at: Optional[datetime] = Field(
        None,
        description="Review start timestamp"
    )
    completed_at: Optional[datetime] = Field(
        None,
        description="Review completion timestamp"
    )
    estimated_remaining_time: Optional[int] = Field(
        None,
        ge=0,
        description="Estimated remaining time in seconds"
    )
    active_agents: List[AgentType] = Field(
        default_factory=list,
        description="Currently active agent types"
    )
    completed_agents: List[AgentType] = Field(
        default_factory=list,
        description="Completed agent types"
    )
    failed_agents: List[AgentType] = Field(
        default_factory=list,
        description="Failed agent types"
    )
    context_status: Optional[Dict[str, str]] = Field(
        None,
        description="Context preparation status"
    )


class AgentResult(BaseModel):
    """Individual agent execution result"""
    model_config = ConfigDict(use_enum_values=True)
    
    agent_type: AgentType = Field(
        ...,
        description="Type of agent"
    )
    status: AgentStatus = Field(
        ...,
        description="Final agent status"
    )
    execution_time: float = Field(
        0.0,
        ge=0.0,
        description="Agent execution time in seconds"
    )
    result_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="Agent-specific result data"
    )
    findings: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="List of findings/issues discovered"
    )
    recommendations: List[str] = Field(
        default_factory=list,
        description="Agent recommendations"
    )
    confidence_score: Optional[float] = Field(
        None,
        ge=0.0,
        le=1.0,
        description="Confidence score for the analysis (0-1)"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Additional metadata"
    )


class ReviewResult(BaseModel):
    """Complete review execution result"""
    model_config = ConfigDict(use_enum_values=True)
    
    review_id: UUID = Field(
        ...,
        description="Review identifier"
    )
    status: ReviewStatus = Field(
        ...,
        description="Final review status"
    )
    overall_results: Dict[str, Any] = Field(
        default_factory=dict,
        description="Overall review results summary"
    )
    reports: Dict[str, str] = Field(
        default_factory=dict,
        description="Generated reports (markdown, html, etc.)"
    )
    execution_time: float = Field(
        0.0,
        ge=0.0,
        description="Total execution time in seconds"
    )
    agent_results: Dict[AgentType, AgentResult] = Field(
        default_factory=dict,
        description="Individual agent results"
    )
    context_metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Context metadata used during review"
    )
    performance_metrics: Optional[Dict[str, Any]] = Field(
        None,
        description="Performance metrics and statistics"
    )
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Result creation timestamp"
    )
    summary: Optional[str] = Field(
        None,
        description="High-level summary of the review"
    )
    priority_findings: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="High-priority findings requiring attention"
    )


class ReviewCancellationResponse(BaseModel):
    """Review cancellation response"""
    model_config = ConfigDict(use_enum_values=True)
    
    review_id: UUID = Field(
        ...,
        description="Cancelled review identifier"
    )
    status: ReviewStatus = Field(
        ReviewStatus.CANCELLED,
        description="Cancellation status"
    )
    message: str = Field(
        ...,
        description="Cancellation confirmation message"
    )
    cancelled_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Cancellation timestamp"
    )
    cancelled_agents: List[AgentType] = Field(
        default_factory=list,
        description="Agents that were cancelled"
    )


class HealthCheckResponse(BaseModel):
    """Health check response model"""
    
    status: str = Field(
        "healthy",
        description="Service health status"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Health check timestamp"
    )
    version: str = Field(
        "1.0.0",
        description="API version"
    )
    services: Dict[str, str] = Field(
        default_factory=dict,
        description="Status of individual services"
    )
    active_reviews: int = Field(
        0,
        ge=0,
        description="Number of currently active reviews"
    )
    websocket_connections: int = Field(
        0,
        ge=0,
        description="Number of active WebSocket connections"
    )


class ErrorResponse(BaseModel):
    """Error response model"""
    
    error: str = Field(
        ...,
        description="Error message"
    )
    error_code: Optional[str] = Field(
        None,
        description="Error code for programmatic handling"
    )
    details: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional error details"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Error timestamp"
    )
    request_id: Optional[str] = Field(
        None,
        description="Request identifier for tracing"
    )


# Type aliases for convenience
ReviewStatusDict = Dict[AgentType, AgentStatusInfo]
AgentResultDict = Dict[AgentType, AgentResult]