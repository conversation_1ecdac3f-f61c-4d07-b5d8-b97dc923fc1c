"""
Application Settings with Pydantic
Environment-based configuration management
"""

from functools import lru_cache
from typing import List, Optional, Union

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    app_name: str = "Multi-Agent Code Reviewer"
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    allowed_origins: Union[str, List[str]] = Field(default="*", env="ALLOWED_ORIGINS")
    
    # Database (PostgreSQL with psycopg3 async driver)
    database_url: str = Field(default="postgresql+psycopg://code_reviewer:dev_password@localhost:5432/code_reviewer_dev", env="DATABASE_URL")
    database_pool_size: int = Field(default=10, env="DATABASE_POOL_SIZE")
    database_max_overflow: int = Field(default=20, env="DATABASE_MAX_OVERFLOW")
    enable_database: bool = Field(default=True, env="ENABLE_DATABASE")  # Enable DB by default
    
    # Redis
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # Claude Code SDK
    claude_api_key: Optional[str] = Field(default=None, env="CLAUDE_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    claude_max_turns: int = Field(default=5, env="CLAUDE_MAX_TURNS")
    claude_timeout: int = Field(default=600, env="CLAUDE_TIMEOUT")
    claude_rate_limit_rpm: int = Field(default=100, env="CLAUDE_RATE_LIMIT_RPM")
    claude_use_bedrock: bool = Field(default=False, env="CLAUDE_CODE_USE_BEDROCK")
    claude_use_vertex: bool = Field(default=False, env="CLAUDE_CODE_USE_VERTEX")
    
    # Multi-Agent Configuration
    max_parallel_agents: int = Field(default=7, env="MAX_PARALLEL_AGENTS")
    agent_execution_timeout: int = Field(default=900, env="AGENT_EXECUTION_TIMEOUT")  # 15 minutes
    
    # WebSocket
    websocket_ping_interval: int = Field(default=25, env="WEBSOCKET_PING_INTERVAL")
    websocket_ping_timeout: int = Field(default=60, env="WEBSOCKET_PING_TIMEOUT")
    
    # Monitoring
    enable_telemetry: bool = Field(default=True, env="ENABLE_TELEMETRY")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Security
    secret_key: str = Field(default="development-secret-key", env="SECRET_KEY")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # Review Configuration
    default_working_directory: str = Field(default="/tmp/code-reviews", env="DEFAULT_WORKING_DIRECTORY")
    max_file_size_mb: int = Field(default=10, env="MAX_FILE_SIZE_MB")
    max_files_per_review: int = Field(default=100, env="MAX_FILES_PER_REVIEW")
    
    @validator("allowed_origins", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("allowed_origins")
    def parse_allowed_origins(cls, v):
        if isinstance(v, str):
            # Handle single wildcard
            if v == "*":
                return ["*"]
            # Handle comma-separated list
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level. Must be one of: {valid_levels}")
        return v.upper()
    
    @validator("environment")
    def validate_environment(cls, v):
        valid_envs = ["development", "testing", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"Invalid environment. Must be one of: {valid_envs}")
        return v.lower()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get application settings (cached)"""
    return Settings()


# Development convenience
settings = get_settings()