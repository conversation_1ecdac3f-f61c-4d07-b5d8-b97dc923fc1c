"""
Parallel Multi-Agent Orchestrator

Core orchestrator for executing all 7 code review agents in parallel using asyncio.gather().
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID, uuid4

from ..agents import BaseAgent, AgentResult, MultiAgentResult, AgentExecutionStatus
from ..agents.factories import AgentFactory
from ..api.websockets.multi_agent_manager import MultiAgentWebSocketManager
from ..services.claude_service import ClaudeService
from ..config.settings import Settings


logger = logging.getLogger(__name__)


class CircuitBreakerError(Exception):
    """Raised when circuit breaker is triggered."""
    pass


class OrchestratorError(Exception):
    """Raised when orchestrator encounters a critical error."""
    pass


class ParallelMultiAgentOrchestrator:
    """
    Orchestrates parallel execution of all 7 code review agents.
    
    Features:
    - Parallel execution using asyncio.gather()
    - WebSocket progress updates
    - Circuit breaker pattern for resilience
    - Per-agent timeout handling
    - Error isolation (one agent failure doesn't stop others)
    """
    
    def __init__(
        self,
        agent_factory: AgentFactory,
        websocket_manager: MultiAgentWebSocketManager,
        settings: Settings,
        max_concurrent_agents: int = 7,
        global_timeout_seconds: int = 1800  # 30 minutes
    ):
        """
        Initialize the parallel orchestrator.
        
        Args:
            agent_factory: Factory for creating agents
            websocket_manager: WebSocket manager for progress updates
            settings: Application settings
            max_concurrent_agents: Maximum number of concurrent agents
            global_timeout_seconds: Global timeout for all agents
        """
        self.agent_factory = agent_factory
        self.websocket_manager = websocket_manager
        self.settings = settings
        self.max_concurrent_agents = max_concurrent_agents
        self.global_timeout_seconds = global_timeout_seconds
        
        self.logger = logging.getLogger(__name__)
        
        # Circuit breaker state
        self._circuit_breaker_failures = 0
        self._circuit_breaker_threshold = 3
        self._circuit_breaker_reset_time = 300  # 5 minutes
        self._circuit_breaker_last_failure: Optional[datetime] = None
        self._circuit_breaker_open = False
        
        # Execution tracking
        self._current_execution_id: Optional[UUID] = None
        self._is_running = False
        self._running_agents: Dict[str, BaseAgent] = {}
    
    async def execute_parallel_review(
        self,
        context: Dict[str, Any],
        agent_config_overrides: Optional[Dict[str, Dict[str, Any]]] = None,
        review_id: Optional[UUID] = None
    ) -> MultiAgentResult:
        """
        Execute all 7 agents in parallel for code review.
        
        Args:
            context: Review context (code, working_path, etc.)
            agent_config_overrides: Per-agent configuration overrides
            review_id: Optional review ID for tracking
            
        Returns:
            MultiAgentResult with all agent results
        """
        # Check circuit breaker
        if self._is_circuit_breaker_open():
            raise CircuitBreakerError("Circuit breaker is open due to repeated failures")
        
        # Initialize execution
        execution_id = review_id or uuid4()
        self._current_execution_id = execution_id
        self._is_running = True
        
        result = MultiAgentResult(execution_id=execution_id)
        
        try:
            self.logger.info(f"Starting parallel review execution: {execution_id}")
            
            # Validate context
            self._validate_context(context)
            
            # Create all agents
            agents = self._create_agents(agent_config_overrides)
            
            # Send WebSocket update - execution started
            await self.websocket_manager.broadcast_agent_event(
                event_type="execution_started",
                review_id=execution_id,
                agent_type="orchestrator",
                data={
                    "total_agents": len(agents),
                    "agent_types": list(agents.keys()),
                    "started_at": datetime.utcnow().isoformat()
                }
            )
            
            # Execute all agents in parallel
            agent_results = await self._execute_agents_parallel(
                agents, context, execution_id
            )
            
            # Process results
            for agent_type, agent_result in agent_results.items():
                result.add_agent_result(agent_result)
            
            # Mark execution as completed
            result.mark_completed()
            
            # Send final WebSocket update
            await self.websocket_manager.broadcast_agent_event(
                event_type="execution_completed",
                review_id=execution_id,
                agent_type="orchestrator",
                data={
                    "successful_agents": result.successful_agents,
                    "failed_agents": result.failed_agents,
                    "total_execution_time": result.total_execution_time_seconds,
                    "completed_at": datetime.utcnow().isoformat()
                }
            )
            
            # Reset circuit breaker on success
            self._reset_circuit_breaker()
            
            self.logger.info(
                f"Parallel review completed: {result.successful_agents}/{result.total_agents} "
                f"agents successful in {result.total_execution_time_seconds:.2f}s"
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Parallel review failed: {str(e)}", exc_info=True)
            
            # Update circuit breaker
            self._record_failure()
            
            # Send error WebSocket update
            await self.websocket_manager.broadcast_agent_event(
                event_type="execution_error",
                review_id=execution_id,
                agent_type="orchestrator",
                data={
                    "error_message": str(e),
                    "error_type": type(e).__name__
                }
            )
            
            # Mark result as failed and return partial results
            result.overall_status = AgentExecutionStatus.FAILED
            result.mark_completed()
            
            return result
            
        finally:
            self._is_running = False
            self._current_execution_id = None
            self._running_agents.clear()
    
    async def _execute_agents_parallel(
        self,
        agents: Dict[str, BaseAgent],
        context: Dict[str, Any],
        execution_id: UUID
    ) -> Dict[str, AgentResult]:
        """
        Execute all agents in parallel using asyncio.gather().
        
        Args:
            agents: Dictionary of agent_type -> agent_instance
            context: Execution context
            execution_id: Execution ID for tracking
            
        Returns:
            Dictionary of agent_type -> AgentResult
        """
        self.logger.info(f"Starting parallel execution of {len(agents)} agents")
        
        # Create tasks for all agents
        tasks = []
        agent_types = []
        
        for agent_type, agent in agents.items():
            self._running_agents[agent_type] = agent
            
            # Create the agent execution task
            task = self._execute_single_agent(agent, context, execution_id)
            tasks.append(task)
            agent_types.append(agent_type)
            
            # Send WebSocket update - agent started
            await self.websocket_manager.broadcast_agent_event(
                event_type="agent_started",
                review_id=execution_id,
                agent_type=agent_type,
                data={"agent_id": agent.agent_id}
            )
        
        # Execute all agents in parallel with global timeout
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*tasks, return_exceptions=True),
                timeout=self.global_timeout_seconds
            )
            
            # Process results
            agent_results = {}
            for i, result in enumerate(results):
                agent_type = agent_types[i]
                
                if isinstance(result, Exception):
                    # Create failed result for exception
                    agent_result = AgentResult(
                        agent_id=agents[agent_type].agent_id,
                        agent_type=agent_type
                    )
                    agent_result.mark_failed(
                        error_message=str(result),
                        error_details={"exception_type": type(result).__name__}
                    )
                    agent_results[agent_type] = agent_result
                    
                    # Send WebSocket update - agent failed
                    await self.websocket_manager.broadcast_agent_event(
                        event_type="agent_failed",
                        review_id=execution_id,
                        agent_type=agent_type,
                        data={
                            "error_message": str(result),
                            "agent_id": agents[agent_type].agent_id
                        }
                    )
                    
                elif isinstance(result, AgentResult):
                    agent_results[agent_type] = result
                    
                    # Send WebSocket update - agent completed
                    await self.websocket_manager.broadcast_agent_event(
                        event_type="agent_completed" if result.success else "agent_failed",
                        review_id=execution_id,
                        agent_type=agent_type,
                        data={
                            "success": result.success,
                            "execution_time": result.execution_time_seconds,
                            "agent_id": result.agent_id
                        }
                    )
                
                else:
                    # Unexpected result type
                    self.logger.warning(f"Unexpected result type from {agent_type}: {type(result)}")
                    agent_result = AgentResult(
                        agent_id=agents[agent_type].agent_id,
                        agent_type=agent_type
                    )
                    agent_result.mark_failed("Unexpected result type")
                    agent_results[agent_type] = agent_result
            
            return agent_results
            
        except asyncio.TimeoutError:
            self.logger.error(f"Global timeout of {self.global_timeout_seconds}s exceeded")
            
            # Create timeout results for all agents
            agent_results = {}
            for agent_type, agent in agents.items():
                agent_result = AgentResult(
                    agent_id=agent.agent_id,
                    agent_type=agent_type
                )
                agent_result.mark_timeout()
                agent_results[agent_type] = agent_result
                
                # Send WebSocket update - agent timeout
                await self.websocket_manager.broadcast_agent_event(
                    event_type="agent_timeout",
                    review_id=execution_id,
                    agent_type=agent_type,
                    data={"agent_id": agent.agent_id}
                )
            
            return agent_results
    
    async def _execute_single_agent(
        self,
        agent: BaseAgent,
        context: Dict[str, Any],
        execution_id: UUID
    ) -> AgentResult:
        """
        Execute a single agent with monitoring.
        
        Args:
            agent: Agent to execute
            context: Execution context
            execution_id: Execution ID for tracking
            
        Returns:
            AgentResult from the agent execution
        """
        try:
            self.logger.debug(f"Executing agent: {agent.agent_type}")
            
            # Execute the agent
            result = await agent.execute(context)
            
            # Add execution metadata
            result.context_data["execution_id"] = str(execution_id)
            result.context_data["orchestrator_version"] = "1.0.0"
            
            return result
            
        except Exception as e:
            self.logger.error(f"Agent {agent.agent_type} execution failed: {str(e)}")
            
            # Create error result
            result = AgentResult(
                agent_id=agent.agent_id,
                agent_type=agent.agent_type
            )
            result.mark_failed(
                error_message=str(e),
                error_details={
                    "exception_type": type(e).__name__,
                    "execution_id": str(execution_id)
                }
            )
            
            return result
    
    def _create_agents(
        self,
        config_overrides: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> Dict[str, BaseAgent]:
        """
        Create all required agents using the factory.
        
        Args:
            config_overrides: Per-agent configuration overrides
            
        Returns:
            Dictionary of agent_type -> agent_instance
        """
        try:
            # Define the 7 agent types for parallel execution
            agent_types = [
                "acceptance_criteria",
                "bug_detection", 
                "security_analysis",
                "logic_analysis",
                "quality_analysis",
                "architecture_analysis",
                "summary"  # Summary agent runs IN PARALLEL (not sequential)
            ]
            
            agents = {}
            config_overrides = config_overrides or {}
            
            for agent_type in agent_types:
                # Check if factory can create this agent type
                if not self.agent_factory.is_registered(agent_type):
                    self.logger.warning(f"Agent type {agent_type} not registered, skipping")
                    continue
                
                # Get configuration override for this agent
                agent_config = config_overrides.get(agent_type, {})
                
                # Create the agent
                agent = self.agent_factory.create_agent(
                    agent_type=agent_type,
                    config_override=agent_config
                )
                
                agents[agent_type] = agent
            
            self.logger.info(f"Created {len(agents)} agents for parallel execution")
            return agents
            
        except Exception as e:
            raise OrchestratorError(f"Failed to create agents: {str(e)}")
    
    def _validate_context(self, context: Dict[str, Any]) -> None:
        """
        Validate the execution context.
        
        Args:
            context: Context to validate
            
        Raises:
            OrchestratorError: If context is invalid
        """
        required_keys = ["working_path"]
        missing_keys = [key for key in required_keys if key not in context]
        
        if missing_keys:
            raise OrchestratorError(f"Missing required context keys: {missing_keys}")
        
        # Validate working path exists
        working_path = Path(context["working_path"])
        if not working_path.exists():
            raise OrchestratorError(f"Working path does not exist: {working_path}")
    
    # Circuit Breaker Implementation
    
    def _is_circuit_breaker_open(self) -> bool:
        """Check if circuit breaker is open."""
        if not self._circuit_breaker_open:
            return False
        
        # Check if enough time has passed to reset
        if (self._circuit_breaker_last_failure and 
            (datetime.utcnow() - self._circuit_breaker_last_failure).total_seconds() 
            >= self._circuit_breaker_reset_time):
            self._reset_circuit_breaker()
            return False
        
        return True
    
    def _record_failure(self) -> None:
        """Record a failure for circuit breaker."""
        self._circuit_breaker_failures += 1
        self._circuit_breaker_last_failure = datetime.utcnow()
        
        if self._circuit_breaker_failures >= self._circuit_breaker_threshold:
            self._circuit_breaker_open = True
            self.logger.warning(
                f"Circuit breaker opened after {self._circuit_breaker_failures} failures"
            )
    
    def _reset_circuit_breaker(self) -> None:
        """Reset circuit breaker state."""
        self._circuit_breaker_failures = 0
        self._circuit_breaker_open = False
        self._circuit_breaker_last_failure = None
        self.logger.info("Circuit breaker reset")
    
    # Status and Monitoring
    
    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get current orchestrator status."""
        return {
            "is_running": self._is_running,
            "current_execution_id": str(self._current_execution_id) if self._current_execution_id else None,
            "running_agents": list(self._running_agents.keys()),
            "circuit_breaker_open": self._circuit_breaker_open,
            "circuit_breaker_failures": self._circuit_breaker_failures,
            "max_concurrent_agents": self.max_concurrent_agents,
            "global_timeout_seconds": self.global_timeout_seconds
        }
    
    def is_running(self) -> bool:
        """Check if orchestrator is currently running."""
        return self._is_running
    
    def get_running_agents(self) -> List[str]:
        """Get list of currently running agent types."""
        return list(self._running_agents.keys())
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on orchestrator and dependencies."""
        factory_health = await self.agent_factory.health_check()
        websocket_health = self.websocket_manager.get_connection_count()
        
        return {
            "orchestrator_status": "healthy",
            "is_running": self._is_running,
            "circuit_breaker_status": "open" if self._circuit_breaker_open else "closed",
            "agent_factory_healthy": factory_health.get("factory_status") == "healthy",
            "websocket_connections": websocket_health,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def shutdown(self) -> None:
        """Shutdown the orchestrator gracefully."""
        self.logger.info("Shutting down parallel orchestrator")
        
        if self._is_running:
            self.logger.warning("Orchestrator is still running during shutdown")
        
        # Cleanup running agents
        for agent in self._running_agents.values():
            try:
                await agent.cleanup()
            except Exception as e:
                self.logger.error(f"Error cleaning up agent: {str(e)}")
        
        self._running_agents.clear()
        self._is_running = False
        self._current_execution_id = None