"""
Result Aggregator

Processes and aggregates results from parallel agent executions into structured reports.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from ..agents import AgentResult, MultiAgentResult, AgentExecutionStatus


logger = logging.getLogger(__name__)


class ResultAggregationError(Exception):
    """Raised when result aggregation fails."""
    pass


class ResultAggregator:
    """
    Aggregates results from multiple agents into structured reports.
    
    Creates two separate reports:
    1. Enhanced Review Report (from 6 review agents)
    2. Implementation Summary (from Summary agent)
    """
    
    def __init__(self):
        """Initialize the result aggregator."""
        self.logger = logging.getLogger(__name__)
        
        # Define agent categories
        self.review_agent_types = [
            "acceptance_criteria",
            "bug_detection",
            "security_analysis", 
            "logic_analysis",
            "quality_analysis",
            "architecture_analysis"
        ]
        
        self.summary_agent_type = "summary"
    
    def process_parallel_results(
        self,
        multi_agent_result: MultiAgentResult,
        output_directory: Optional[Path] = None
    ) -> Tuple[str, str]:
        """
        Process parallel agent results into two separate reports.
        
        Args:
            multi_agent_result: Results from all agents
            output_directory: Optional directory to save reports
            
        Returns:
            Tuple of (enhanced_review_report, implementation_summary)
        """
        try:
            self.logger.info(f"Processing results from {multi_agent_result.total_agents} agents")
            
            # Generate Enhanced Review Report
            enhanced_review_report = self._generate_enhanced_review_report(
                multi_agent_result
            )
            
            # Generate Implementation Summary
            implementation_summary = self._generate_implementation_summary(
                multi_agent_result
            )
            
            # Save reports if output directory provided
            if output_directory:
                self._save_reports(
                    enhanced_review_report,
                    implementation_summary, 
                    output_directory,
                    multi_agent_result.execution_id
                )
            
            # Update multi-agent result with generated reports
            multi_agent_result.enhanced_review_report = enhanced_review_report
            multi_agent_result.implementation_summary = implementation_summary
            
            self.logger.info("Successfully processed parallel agent results")
            
            return enhanced_review_report, implementation_summary
            
        except Exception as e:
            self.logger.error(f"Failed to process parallel results: {str(e)}", exc_info=True)
            raise ResultAggregationError(f"Result aggregation failed: {str(e)}")
    
    def _generate_enhanced_review_report(
        self,
        multi_agent_result: MultiAgentResult
    ) -> str:
        """
        Generate the Enhanced Review Report from 6 review agents.
        
        Args:
            multi_agent_result: Results from all agents
            
        Returns:
            Formatted Enhanced Review Report as markdown
        """
        report_sections = []
        
        # Header
        report_sections.append(self._generate_report_header(
            "Enhanced Code Review Report",
            multi_agent_result
        ))
        
        # Executive Summary
        report_sections.append(self._generate_executive_summary(multi_agent_result))
        
        # Performance Metrics
        report_sections.append(self._generate_performance_metrics(multi_agent_result))
        
        # Review Agent Results
        for agent_type in self.review_agent_types:
            if agent_type in multi_agent_result.agent_results:
                agent_result = multi_agent_result.agent_results[agent_type]
                section = self._generate_agent_section(agent_result)
                report_sections.append(section)
            else:
                # Agent was not executed or failed
                section = self._generate_missing_agent_section(agent_type)
                report_sections.append(section)
        
        # Error Summary (if any failures)
        failed_agents = multi_agent_result.get_failed_agents()
        if failed_agents:
            report_sections.append(self._generate_error_summary(failed_agents))
        
        # Footer
        report_sections.append(self._generate_report_footer())
        
        return "\n\n".join(report_sections)
    
    def _generate_implementation_summary(
        self,
        multi_agent_result: MultiAgentResult
    ) -> str:
        """
        Generate the Implementation Summary from Summary agent.
        
        Args:
            multi_agent_result: Results from all agents
            
        Returns:
            Implementation Summary as markdown
        """
        summary_sections = []
        
        # Header
        summary_sections.append(self._generate_report_header(
            "Implementation Summary & Tutorial",
            multi_agent_result
        ))
        
        # Summary Agent Results
        if self.summary_agent_type in multi_agent_result.agent_results:
            summary_result = multi_agent_result.agent_results[self.summary_agent_type]
            
            if summary_result.success:
                # Use summary agent's output
                if summary_result.structured_output:
                    summary_sections.append(self._format_structured_summary(
                        summary_result.structured_output
                    ))
                elif summary_result.raw_output:
                    summary_sections.append(summary_result.raw_output)
                else:
                    summary_sections.append("## Implementation Summary\n\nNo detailed summary available.")
            else:
                # Summary agent failed
                summary_sections.append(f"""## Summary Agent Failed

**Error:** {summary_result.error_message}

**Fallback Summary:** {self._generate_fallback_summary(multi_agent_result)}""")
        else:
            # Summary agent was not executed
            summary_sections.append(f"""## Implementation Summary

**Note:** Summary agent was not executed.

**Basic Summary:** {self._generate_fallback_summary(multi_agent_result)}""")
        
        # Cross-Reference to Review Report
        summary_sections.append(f"""## Related Reports

- **Enhanced Review Report:** See detailed findings from all review agents
- **Execution ID:** `{multi_agent_result.execution_id}`
- **Analysis Time:** {multi_agent_result.total_execution_time_seconds:.2f} seconds""")
        
        return "\n\n".join(summary_sections)
    
    def _generate_report_header(
        self,
        title: str,
        multi_agent_result: MultiAgentResult
    ) -> str:
        """Generate report header with metadata."""
        return f"""# {title}

**Execution ID:** `{multi_agent_result.execution_id}`  
**Generated:** {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}  
**Total Agents:** {multi_agent_result.total_agents}  
**Successful:** {multi_agent_result.successful_agents}  
**Failed:** {multi_agent_result.failed_agents}  
**Execution Time:** {multi_agent_result.total_execution_time_seconds:.2f} seconds  
**Status:** {multi_agent_result.overall_status.value.upper()}  

---"""
    
    def _generate_executive_summary(
        self,
        multi_agent_result: MultiAgentResult
    ) -> str:
        """Generate executive summary of the review."""
        successful_agents = multi_agent_result.get_successful_agents()
        failed_agents = multi_agent_result.get_failed_agents()
        
        # Count issues by category
        total_issues = 0
        critical_issues = 0
        
        for agent in successful_agents:
            if agent.result_data:
                issues = agent.result_data.get("issues", [])
                total_issues += len(issues)
                critical_issues += len([
                    issue for issue in issues 
                    if issue.get("severity") in ["critical", "high"]
                ])
        
        status_icon = "✅" if multi_agent_result.overall_status == AgentExecutionStatus.SUCCESS else "❌"
        
        return f"""## Executive Summary {status_icon}

**Review Status:** {multi_agent_result.overall_status.value.title()}  
**Performance:** {multi_agent_result.successful_agents}/{multi_agent_result.total_agents} agents completed successfully  
**Execution Time:** {multi_agent_result.total_execution_time_seconds:.1f}s  

**Key Findings:**
- **Total Issues Found:** {total_issues}
- **Critical/High Severity:** {critical_issues}
- **Agents Completed:** {multi_agent_result.successful_agents}
- **Agents Failed:** {multi_agent_result.failed_agents}

{self._generate_status_summary(successful_agents, failed_agents)}"""
    
    def _generate_performance_metrics(
        self,
        multi_agent_result: MultiAgentResult
    ) -> str:
        """Generate performance metrics section."""
        metrics = []
        
        for agent_type in self.review_agent_types:
            if agent_type in multi_agent_result.agent_results:
                result = multi_agent_result.agent_results[agent_type]
                status_icon = "✅" if result.success else "❌"
                execution_time = result.execution_time_seconds or 0
                
                metrics.append(
                    f"| {agent_type.replace('_', ' ').title()} | {status_icon} | "
                    f"{execution_time:.1f}s | {result.status.value} |"
                )
            else:
                metrics.append(
                    f"| {agent_type.replace('_', ' ').title()} | ❌ | N/A | not_executed |"
                )
        
        metrics_table = "\n".join(metrics)
        
        return f"""## Performance Metrics

| Agent | Status | Time | Result |
|-------|--------|------|--------|
{metrics_table}

**Total Parallel Execution Time:** {multi_agent_result.total_execution_time_seconds:.2f} seconds"""
    
    def _generate_agent_section(self, agent_result: AgentResult) -> str:
        """Generate section for a specific agent's results."""
        status_icon = "✅" if agent_result.success else "❌"
        agent_title = agent_result.agent_type.replace("_", " ").title()
        
        section = f"""## {agent_title} Analysis {status_icon}

**Agent ID:** `{agent_result.agent_id}`  
**Execution Time:** {agent_result.execution_time_seconds:.2f}s  
**Status:** {agent_result.status.value}  """
        
        if agent_result.success:
            # Add agent-specific results
            if agent_result.structured_output:
                section += f"\n\n### Findings\n\n{self._format_agent_findings(agent_result.structured_output)}"
            elif agent_result.raw_output:
                section += f"\n\n### Analysis Results\n\n{agent_result.raw_output}"
            elif agent_result.result_data:
                section += f"\n\n### Results\n\n{self._format_result_data(agent_result.result_data)}"
            else:
                section += "\n\n*No detailed results available.*"
                
            # Add quality metrics if available
            if agent_result.quality_metrics:
                section += f"\n\n### Quality Metrics\n\n{self._format_quality_metrics(agent_result.quality_metrics)}"
        else:
            # Add error information
            section += f"\n\n### Error Details\n\n**Error:** {agent_result.error_message}"
            if agent_result.error_details:
                section += f"\n\n**Details:** {agent_result.error_details}"
        
        return section
    
    def _generate_missing_agent_section(self, agent_type: str) -> str:
        """Generate section for missing/failed agent."""
        agent_title = agent_type.replace("_", " ").title()
        
        return f"""## {agent_title} Analysis ❌

**Status:** Not executed or failed  
**Impact:** This analysis was not performed

*This agent was not available during execution. Consider re-running the review.*"""
    
    def _generate_error_summary(self, failed_agents: List[AgentResult]) -> str:
        """Generate error summary for failed agents."""
        errors = []
        
        for agent in failed_agents:
            errors.append(f"- **{agent.agent_type}:** {agent.error_message}")
        
        error_list = "\n".join(errors)
        
        return f"""## Error Summary ⚠️

The following agents encountered errors during execution:

{error_list}

**Recommendations:**
- Check system logs for detailed error information
- Verify Claude Code SDK configuration
- Consider re-running the review for failed agents
- Check network connectivity and timeout settings"""
    
    def _generate_fallback_summary(self, multi_agent_result: MultiAgentResult) -> str:
        """Generate a basic fallback summary when Summary agent fails."""
        return f"""Based on the parallel execution of {multi_agent_result.successful_agents} agents:

- Review completed with {multi_agent_result.successful_agents}/{multi_agent_result.total_agents} agents successful
- Total execution time: {multi_agent_result.total_execution_time_seconds:.2f} seconds
- Status: {multi_agent_result.overall_status.value}

For detailed findings, see the individual agent sections above."""
    
    def _format_agent_findings(self, structured_output: Dict[str, Any]) -> str:
        """Format structured agent findings."""
        if "issues" in structured_output:
            issues = structured_output["issues"]
            if issues:
                formatted_issues = []
                for issue in issues:
                    severity = issue.get("severity", "medium")
                    title = issue.get("title", "Untitled Issue")
                    description = issue.get("description", "No description")
                    
                    severity_icon = {
                        "critical": "🔴",
                        "high": "🟠", 
                        "medium": "🟡",
                        "low": "🟢"
                    }.get(severity, "⚪")
                    
                    formatted_issues.append(f"**{severity_icon} {title}** ({severity})\n{description}")
                
                return "\n\n".join(formatted_issues)
            else:
                return "No issues found."
        
        # Fallback: format as key-value pairs
        formatted = []
        for key, value in structured_output.items():
            formatted.append(f"**{key.title()}:** {value}")
        
        return "\n".join(formatted) if formatted else "No structured output available."
    
    def _format_result_data(self, result_data: Dict[str, Any]) -> str:
        """Format general result data."""
        formatted = []
        for key, value in result_data.items():
            if isinstance(value, list):
                formatted.append(f"**{key.title()}:** {len(value)} items")
            elif isinstance(value, dict):
                formatted.append(f"**{key.title()}:** {len(value)} entries")
            else:
                formatted.append(f"**{key.title()}:** {value}")
        
        return "\n".join(formatted) if formatted else "No result data available."
    
    def _format_quality_metrics(self, quality_metrics: Dict[str, Any]) -> str:
        """Format quality metrics."""
        formatted = []
        for key, value in quality_metrics.items():
            if isinstance(value, float):
                formatted.append(f"- **{key.title()}:** {value:.2f}")
            else:
                formatted.append(f"- **{key.title()}:** {value}")
        
        return "\n".join(formatted) if formatted else "No quality metrics available."
    
    def _format_structured_summary(self, structured_output: Dict[str, Any]) -> str:
        """Format structured summary output."""
        if "tutorial" in structured_output:
            return structured_output["tutorial"]
        elif "summary" in structured_output:
            return structured_output["summary"]
        else:
            # Format as sections
            formatted_sections = []
            for key, value in structured_output.items():
                section_title = key.replace("_", " ").title()
                formatted_sections.append(f"## {section_title}\n\n{value}")
            
            return "\n\n".join(formatted_sections)
    
    def _generate_status_summary(
        self,
        successful_agents: List[AgentResult],
        failed_agents: List[AgentResult]
    ) -> str:
        """Generate status summary for agents."""
        if not failed_agents:
            return "**Overall Status:** ✅ All review agents completed successfully."
        elif successful_agents:
            return (f"**Overall Status:** ⚠️ Partial success - "
                   f"{len(successful_agents)} agents completed, {len(failed_agents)} failed.")
        else:
            return "**Overall Status:** ❌ All agents failed - review could not be completed."
    
    def _generate_report_footer(self) -> str:
        """Generate report footer."""
        return f"""---

*Generated by Multi-Agent Code Review System*  
*Report generated at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}*"""
    
    def _save_reports(
        self,
        enhanced_review_report: str, 
        implementation_summary: str,
        output_directory: Path,
        execution_id: UUID
    ) -> None:
        """Save reports to files."""
        try:
            output_directory.mkdir(parents=True, exist_ok=True)
            
            # Save Enhanced Review Report
            review_file = output_directory / f"enhanced_review_report_{execution_id}.md"
            review_file.write_text(enhanced_review_report, encoding="utf-8")
            
            # Save Implementation Summary
            summary_file = output_directory / f"implementation_summary_{execution_id}.md"
            summary_file.write_text(implementation_summary, encoding="utf-8")
            
            self.logger.info(f"Reports saved to {output_directory}")
            
        except Exception as e:
            self.logger.error(f"Failed to save reports: {str(e)}")
            # Don't raise exception - reports are still returned in memory
    
    def get_aggregation_metrics(
        self,
        multi_agent_result: MultiAgentResult
    ) -> Dict[str, Any]:
        """Get metrics about the aggregation process."""
        review_agents_executed = len([
            agent_type for agent_type in self.review_agent_types
            if agent_type in multi_agent_result.agent_results
        ])
        
        summary_agent_executed = self.summary_agent_type in multi_agent_result.agent_results
        
        return {
            "review_agents_executed": review_agents_executed,
            "review_agents_total": len(self.review_agent_types),
            "summary_agent_executed": summary_agent_executed,
            "aggregation_timestamp": datetime.utcnow().isoformat(),
            "reports_generated": 2 if multi_agent_result.enhanced_review_report and multi_agent_result.implementation_summary else 0
        }