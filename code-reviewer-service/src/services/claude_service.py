"""
Claude Code SDK Service Integration
Native Python SDK with async/await pattern, replacing subprocess calls
"""

import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, AsyncGenerator
import json

import structlog
from claude_code_sdk import query, ClaudeCodeOptions, Message

from src.config.settings import get_settings
from src.utils.monitoring import get_tracer, log_performance_metrics

logger = structlog.get_logger()
settings = get_settings()


class ClaudeSDKError(Exception):
    """Exception raised for Claude SDK specific errors"""
    pass


class RateLimitError(ClaudeSDKError):
    """Exception raised when Claude SDK rate limits are exceeded"""
    pass


class ClaudeService:
    """
    Native Claude Code SDK Service with async/await pattern
    Replaces subprocess.run() calls with native Python SDK integration
    """
    
    def __init__(self):
        self.tracer = get_tracer(__name__)
        self._rate_limit_tracker = {}  # Simple rate limiting tracker
    
    async def execute_agent_query(
        self,
        prompt: str,
        agent_type: str,
        working_directory: Optional[Path] = None,
        max_turns: int = None,
        system_prompt: Optional[str] = None,
        allowed_tools: Optional[List[str]] = None,
        timeout: int = None
    ) -> Dict[str, Any]:
        """
        Execute Claude Code query for a specific agent with native SDK
        
        Args:
            prompt: The user prompt to send to Claude
            agent_type: Type of agent (acceptance_criteria, bug_detection, etc.)
            working_directory: Working directory for Claude operations (replaces cwd parameter)
            max_turns: Maximum conversation turns
            system_prompt: System prompt for agent specialization
            allowed_tools: List of allowed Claude Code tools
            timeout: Query timeout in seconds
            
        Returns:
            Dict with success, messages, response, and metadata
        """
        
        with self.tracer.start_as_current_span("claude_agent_query") as span:
            span.set_attribute("agent_type", agent_type)
            span.set_attribute("prompt_length", len(prompt))
            
            start_time = time.time()
            
            try:
                # Check rate limits
                await self._check_rate_limits(agent_type)
                
                # Prepare Claude Code options
                options = self._create_claude_options(
                    max_turns=max_turns or settings.claude_max_turns,
                    system_prompt=system_prompt,
                    working_directory=working_directory,
                    allowed_tools=allowed_tools,
                    timeout=timeout or settings.claude_timeout
                )
                
                # Execute native SDK query
                messages = await self._execute_native_query(prompt, options, agent_type)
                
                # Process results
                response_text = self._extract_response_text(messages)
                
                duration = time.time() - start_time
                
                # Log performance metrics
                log_performance_metrics(
                    operation=f"claude_agent_{agent_type}",
                    duration_ms=duration * 1000,
                    success=True,
                    additional_data={
                        "message_count": len(messages),
                        "response_length": len(response_text),
                        "working_dir": str(working_directory) if working_directory else None
                    }
                )
                
                logger.info(
                    "Claude agent query completed",
                    agent_type=agent_type,
                    duration_ms=round(duration * 1000, 2),
                    message_count=len(messages),
                    response_length=len(response_text)
                )
                
                return {
                    "success": True,
                    "messages": [self._message_to_dict(msg) for msg in messages],
                    "response": response_text,
                    "agent_type": agent_type,
                    "duration_ms": round(duration * 1000, 2),
                    "metadata": {
                        "working_directory": str(working_directory) if working_directory else None,
                        "max_turns": options.max_turns,
                        "message_count": len(messages)
                    }
                }
                
            except asyncio.TimeoutError:
                duration = time.time() - start_time
                error_msg = f"Claude agent query timed out after {timeout or settings.claude_timeout}s"
                
                logger.error(
                    "Claude agent query timeout",
                    agent_type=agent_type,
                    timeout=timeout or settings.claude_timeout,
                    duration_ms=round(duration * 1000, 2)
                )
                
                return {
                    "success": False,
                    "error": error_msg,
                    "agent_type": agent_type,
                    "duration_ms": round(duration * 1000, 2)
                }
                
            except RateLimitError as e:
                duration = time.time() - start_time
                
                logger.warning(
                    "Claude agent query rate limited",
                    agent_type=agent_type,
                    error=str(e),
                    duration_ms=round(duration * 1000, 2)
                )
                
                return {
                    "success": False,
                    "error": f"Rate limit exceeded: {str(e)}",
                    "agent_type": agent_type,
                    "duration_ms": round(duration * 1000, 2),
                    "retry_after": 60  # Suggest retry after 1 minute
                }
                
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Claude agent query failed",
                    agent_type=agent_type,
                    error=str(e),
                    duration_ms=round(duration * 1000, 2),
                    exc_info=True
                )
                
                log_performance_metrics(
                    operation=f"claude_agent_{agent_type}",
                    duration_ms=duration * 1000,
                    success=False,
                    additional_data={"error": str(e)}
                )
                
                return {
                    "success": False,
                    "error": f"Claude agent query failed: {str(e)}",
                    "agent_type": agent_type,
                    "duration_ms": round(duration * 1000, 2)
                }
    
    async def execute_parallel_agents(
        self,
        agent_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Execute multiple agents in parallel using asyncio.gather()
        This is the key innovation enabling 7x performance improvement
        
        Args:
            agent_configs: List of agent configuration dictionaries
            
        Returns:
            Dict with agent results and overall execution metrics
        """
        
        with self.tracer.start_as_current_span("claude_parallel_agents") as span:
            span.set_attribute("agent_count", len(agent_configs))
            
            start_time = time.time()
            
            logger.info(
                "Starting parallel agent execution",
                agent_count=len(agent_configs),
                agent_types=[config.get("agent_type") for config in agent_configs]
            )
            
            try:
                # Create parallel tasks for all agents
                tasks = []
                for config in agent_configs:
                    task = self.execute_agent_query(
                        prompt=config["prompt"],
                        agent_type=config["agent_type"],
                        working_directory=config.get("working_directory"),
                        max_turns=config.get("max_turns"),
                        system_prompt=config.get("system_prompt"),
                        allowed_tools=config.get("allowed_tools"),
                        timeout=config.get("timeout")
                    )
                    tasks.append(task)
                
                # Execute all agents in parallel
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                agent_results = {}
                successful_agents = 0
                failed_agents = 0
                
                for i, result in enumerate(results):
                    agent_type = agent_configs[i]["agent_type"]
                    
                    if isinstance(result, Exception):
                        logger.error(
                            "Agent execution failed with exception",
                            agent_type=agent_type,
                            error=str(result)
                        )
                        agent_results[agent_type] = {
                            "success": False,
                            "error": f"Exception: {str(result)}",
                            "agent_type": agent_type
                        }
                        failed_agents += 1
                    else:
                        agent_results[agent_type] = result
                        if result.get("success", False):
                            successful_agents += 1
                        else:
                            failed_agents += 1
                
                duration = time.time() - start_time
                
                # Log overall performance
                log_performance_metrics(
                    operation="claude_parallel_execution",
                    duration_ms=duration * 1000,
                    success=successful_agents > 0,
                    additional_data={
                        "total_agents": len(agent_configs),
                        "successful_agents": successful_agents,
                        "failed_agents": failed_agents,
                        "parallel_speedup": f"{len(agent_configs)}x"
                    }
                )
                
                logger.info(
                    "Parallel agent execution completed",
                    total_agents=len(agent_configs),
                    successful_agents=successful_agents,
                    failed_agents=failed_agents,
                    duration_ms=round(duration * 1000, 2),
                    parallel_speedup=f"{len(agent_configs)}x"
                )
                
                return {
                    "success": successful_agents > 0,
                    "agent_results": agent_results,
                    "execution_stats": {
                        "total_agents": len(agent_configs),
                        "successful_agents": successful_agents,
                        "failed_agents": failed_agents,
                        "total_duration_ms": round(duration * 1000, 2),
                        "parallel_speedup": f"{len(agent_configs)}x"
                    }
                }
                
            except Exception as e:
                duration = time.time() - start_time
                
                logger.error(
                    "Parallel agent execution failed",
                    error=str(e),
                    duration_ms=round(duration * 1000, 2),
                    exc_info=True
                )
                
                return {
                    "success": False,
                    "error": f"Parallel execution failed: {str(e)}",
                    "execution_stats": {
                        "total_agents": len(agent_configs),
                        "successful_agents": 0,
                        "failed_agents": len(agent_configs),
                        "total_duration_ms": round(duration * 1000, 2)
                    }
                }
    
    def _create_claude_options(
        self,
        max_turns: int,
        system_prompt: Optional[str],
        working_directory: Optional[Path],
        allowed_tools: Optional[List[str]],
        timeout: int
    ) -> ClaudeCodeOptions:
        """Create ClaudeCodeOptions with proper configuration"""
        
        return ClaudeCodeOptions(
            max_turns=max_turns,
            system_prompt=system_prompt,
            cwd=working_directory,  # This replaces the subprocess cwd parameter
            allowed_tools=allowed_tools or ["Read", "Grep", "Bash", "Write"],
            permission_mode="acceptEdits"  # Allow Claude to make edits
        )
    
    async def _execute_native_query(
        self,
        prompt: str,
        options: ClaudeCodeOptions,
        agent_type: str
    ) -> List[Message]:
        """Execute native Claude SDK query with timeout handling"""
        
        messages = []
        
        try:
            # Apply timeout using asyncio.wait_for
            async def query_with_timeout():
                async for message in query(prompt=prompt, options=options):
                    messages.append(message)
                return messages
            
            return await asyncio.wait_for(
                query_with_timeout(),
                timeout=settings.claude_timeout
            )
            
        except asyncio.TimeoutError:
            logger.error(
                "Native Claude query timed out",
                agent_type=agent_type,
                timeout=settings.claude_timeout
            )
            raise
        except Exception as e:
            logger.error(
                "Native Claude query failed",
                agent_type=agent_type,
                error=str(e),
                exc_info=True
            )
            raise ClaudeSDKError(f"Native query failed: {str(e)}")
    
    def _extract_response_text(self, messages: List[Message]) -> str:
        """Extract the final response text from Claude messages"""
        if not messages:
            return ""
        
        # Get the last message content
        last_message = messages[-1]
        
        # Handle different message types
        if hasattr(last_message, 'content') and last_message.content:
            return str(last_message.content)
        elif hasattr(last_message, 'text') and last_message.text:
            return str(last_message.text)
        else:
            # Fallback: convert entire message to string
            return str(last_message)
    
    def _message_to_dict(self, message: Message) -> Dict[str, Any]:
        """Convert Claude SDK Message to dictionary for JSON serialization"""
        return {
            "content": str(message.content) if hasattr(message, 'content') else str(message),
            "type": getattr(message, 'type', 'unknown'),
            "timestamp": time.time()
        }
    
    async def _check_rate_limits(self, agent_type: str) -> None:
        """Simple rate limit checking (can be enhanced with Redis)"""
        current_time = time.time()
        
        # Clean old entries (older than 1 minute)
        cutoff_time = current_time - 60
        self._rate_limit_tracker = {
            k: v for k, v in self._rate_limit_tracker.items() 
            if v > cutoff_time
        }
        
        # Count recent requests for this agent type
        recent_requests = len([
            t for t in self._rate_limit_tracker.values() 
            if t > cutoff_time
        ])
        
        if recent_requests >= settings.claude_rate_limit_rpm:
            raise RateLimitError(
                f"Rate limit exceeded: {recent_requests}/{settings.claude_rate_limit_rpm} RPM"
            )
        
        # Record this request
        request_id = f"{agent_type}_{current_time}"
        self._rate_limit_tracker[request_id] = current_time
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for Claude SDK availability"""
        try:
            # Simple test query to verify Claude SDK is working
            test_options = ClaudeCodeOptions(max_turns=1)
            
            messages = []
            async for message in query(prompt="Hello, are you available?", options=test_options):
                messages.append(message)
                break  # Only need first response for health check
            
            if messages:
                return {
                    "healthy": True,
                    "sdk_available": True,
                    "response_time_ms": 0,  # TODO: Measure actual response time
                    "message": "Claude SDK is available and responding"
                }
            else:
                return {
                    "healthy": False,
                    "sdk_available": False,
                    "error": "No response from Claude SDK"
                }
                
        except Exception as e:
            logger.error("Claude SDK health check failed", error=str(e))
            return {
                "healthy": False,
                "sdk_available": False,
                "error": f"Health check failed: {str(e)}"
            }


# Global service instance (can be replaced with dependency injection)
claude_service = ClaudeService()