"""
Agent Result Models

Defines the standard data structures for agent execution results and status tracking.
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4


class AgentExecutionStatus(Enum):
    """Execution status for agent operations."""
    
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class AgentResult:
    """Standard result structure for all agent operations."""
    
    # Identification
    agent_id: str
    agent_type: str
    execution_id: UUID = field(default_factory=uuid4)
    
    # Status and Timing
    status: AgentExecutionStatus = AgentExecutionStatus.PENDING
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time_seconds: Optional[float] = None
    
    # Results
    success: bool = False
    result_data: Dict[str, Any] = field(default_factory=dict)
    raw_output: Optional[str] = None
    structured_output: Optional[Dict[str, Any]] = None
    
    # Error Handling
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    exception_type: Optional[str] = None
    
    # Progress and Context
    progress_updates: List[Dict[str, Any]] = field(default_factory=list)
    context_data: Dict[str, Any] = field(default_factory=dict)
    
    # Quality Metrics
    confidence_score: Optional[float] = None
    quality_metrics: Dict[str, Any] = field(default_factory=dict)
    
    # Integration Data
    jira_ticket_id: Optional[str] = None
    git_branch: Optional[str] = None
    pr_number: Optional[int] = None
    
    def mark_started(self) -> None:
        """Mark the agent execution as started."""
        self.status = AgentExecutionStatus.RUNNING
        self.started_at = datetime.utcnow()
    
    def mark_completed(self, success: bool = True) -> None:
        """Mark the agent execution as completed."""
        self.completed_at = datetime.utcnow()
        self.success = success
        self.status = AgentExecutionStatus.SUCCESS if success else AgentExecutionStatus.FAILED
        
        if self.started_at:
            self.execution_time_seconds = (
                self.completed_at - self.started_at
            ).total_seconds()
    
    def mark_failed(self, error_message: str, error_details: Optional[Dict[str, Any]] = None) -> None:
        """Mark the agent execution as failed."""
        self.status = AgentExecutionStatus.FAILED
        self.success = False
        self.error_message = error_message
        self.error_details = error_details or {}
        self.mark_completed(success=False)
    
    def mark_timeout(self) -> None:
        """Mark the agent execution as timed out."""
        self.status = AgentExecutionStatus.TIMEOUT
        self.success = False
        self.error_message = "Agent execution timed out"
        self.mark_completed(success=False)
    
    def add_progress_update(self, message: str, progress_percentage: Optional[float] = None) -> None:
        """Add a progress update to the execution."""
        update = {
            "timestamp": datetime.utcnow().isoformat(),
            "message": message,
            "progress_percentage": progress_percentage
        }
        self.progress_updates.append(update)
    
    def set_result_data(self, data: Dict[str, Any]) -> None:
        """Set the main result data."""
        self.result_data = data
    
    def set_structured_output(self, output: Dict[str, Any]) -> None:
        """Set structured output data."""
        self.structured_output = output
    
    def set_quality_metrics(self, metrics: Dict[str, Any]) -> None:
        """Set quality metrics for the execution."""
        self.quality_metrics = metrics
    
    def is_completed(self) -> bool:
        """Check if the agent execution is completed (success or failure)."""
        return self.status in [
            AgentExecutionStatus.SUCCESS,
            AgentExecutionStatus.FAILED,
            AgentExecutionStatus.TIMEOUT,
            AgentExecutionStatus.CANCELLED
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the result to a dictionary for serialization."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "execution_id": str(self.execution_id),
            "status": self.status.value,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "execution_time_seconds": self.execution_time_seconds,
            "success": self.success,
            "result_data": self.result_data,
            "raw_output": self.raw_output,
            "structured_output": self.structured_output,
            "error_message": self.error_message,
            "error_details": self.error_details,
            "exception_type": self.exception_type,
            "progress_updates": self.progress_updates,
            "context_data": self.context_data,
            "confidence_score": self.confidence_score,
            "quality_metrics": self.quality_metrics,
            "jira_ticket_id": self.jira_ticket_id,
            "git_branch": self.git_branch,
            "pr_number": self.pr_number
        }


@dataclass
class MultiAgentResult:
    """Result container for multiple agent executions."""
    
    execution_id: UUID = field(default_factory=uuid4)
    started_at: datetime = field(default_factory=datetime.utcnow)
    completed_at: Optional[datetime] = None
    
    # Agent Results
    agent_results: Dict[str, AgentResult] = field(default_factory=dict)
    
    # Aggregated Results
    enhanced_review_report: Optional[str] = None
    implementation_summary: Optional[str] = None
    
    # Overall Status
    overall_status: AgentExecutionStatus = AgentExecutionStatus.PENDING
    total_execution_time_seconds: Optional[float] = None
    
    # Statistics
    successful_agents: int = 0
    failed_agents: int = 0
    total_agents: int = 0
    
    def add_agent_result(self, result: AgentResult) -> None:
        """Add an agent result to the collection."""
        self.agent_results[result.agent_type] = result
        self._update_statistics()
    
    def mark_completed(self) -> None:
        """Mark the multi-agent execution as completed."""
        self.completed_at = datetime.utcnow()
        self.total_execution_time_seconds = (
            self.completed_at - self.started_at
        ).total_seconds()
        
        # Determine overall status
        if self.failed_agents == 0:
            self.overall_status = AgentExecutionStatus.SUCCESS
        elif self.successful_agents > 0:
            self.overall_status = AgentExecutionStatus.SUCCESS  # Partial success
        else:
            self.overall_status = AgentExecutionStatus.FAILED
    
    def _update_statistics(self) -> None:
        """Update execution statistics."""
        self.total_agents = len(self.agent_results)
        self.successful_agents = sum(
            1 for result in self.agent_results.values() 
            if result.success
        )
        self.failed_agents = self.total_agents - self.successful_agents
    
    def get_failed_agents(self) -> List[AgentResult]:
        """Get list of failed agent results."""
        return [
            result for result in self.agent_results.values()
            if not result.success
        ]
    
    def get_successful_agents(self) -> List[AgentResult]:
        """Get list of successful agent results."""
        return [
            result for result in self.agent_results.values()
            if result.success
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "execution_id": str(self.execution_id),
            "started_at": self.started_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "agent_results": {
                agent_type: result.to_dict()
                for agent_type, result in self.agent_results.items()
            },
            "enhanced_review_report": self.enhanced_review_report,
            "implementation_summary": self.implementation_summary,
            "overall_status": self.overall_status.value,
            "total_execution_time_seconds": self.total_execution_time_seconds,
            "successful_agents": self.successful_agents,
            "failed_agents": self.failed_agents,
            "total_agents": self.total_agents
        }