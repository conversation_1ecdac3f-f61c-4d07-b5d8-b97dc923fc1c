"""
Agent Factory

Factory pattern implementation for creating different types of code review agents.
"""

from typing import Any, Dict, Type, Optional, List
import logging
from uuid import uuid4

from ..base_agent import BaseAgent
from ..models import AgentExecutionStatus
from ...services.claude_service import ClaudeService
from ...config.settings import Settings


logger = logging.getLogger(__name__)


class AgentRegistrationError(Exception):
    """Raised when agent registration fails."""
    pass


class AgentCreationError(Exception):
    """Raised when agent creation fails."""
    pass


class AgentFactory:
    """
    Factory for creating code review agents using the Strategy Pattern.
    
    Supports dynamic registration of new agent types and configuration injection.
    """
    
    def __init__(self, claude_service: ClaudeService, settings: Settings):
        """
        Initialize the agent factory.
        
        Args:
            claude_service: Claude Code SDK service instance
            settings: Application settings
        """
        self.claude_service = claude_service
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Registry of agent types -> agent classes
        self._agent_registry: Dict[str, Type[BaseAgent]] = {}
        
        # Default configuration for each agent type
        self._agent_configs: Dict[str, Dict[str, Any]] = {}
        
        # Agent type metadata
        self._agent_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Initialize with default agent types
        self._register_default_agents()
    
    def register_agent(
        self,
        agent_type: str,
        agent_class: Type[BaseAgent],
        default_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Register a new agent type with the factory.
        
        Args:
            agent_type: Unique identifier for the agent type
            agent_class: The agent class (must inherit from BaseAgent)
            default_config: Default configuration for this agent type
            metadata: Additional metadata about the agent
            
        Raises:
            AgentRegistrationError: If registration fails
        """
        try:
            # Validate agent class
            if not issubclass(agent_class, BaseAgent):
                raise AgentRegistrationError(
                    f"Agent class {agent_class.__name__} must inherit from BaseAgent"
                )
            
            # Check for duplicate registration
            if agent_type in self._agent_registry:
                self.logger.warning(f"Overriding existing agent type: {agent_type}")
            
            # Register the agent
            self._agent_registry[agent_type] = agent_class
            self._agent_configs[agent_type] = default_config or {}
            self._agent_metadata[agent_type] = metadata or {}
            
            self.logger.info(f"Registered agent type: {agent_type} -> {agent_class.__name__}")
            
        except Exception as e:
            raise AgentRegistrationError(f"Failed to register agent {agent_type}: {str(e)}")
    
    def create_agent(
        self,
        agent_type: str,
        agent_id: Optional[str] = None,
        config_override: Optional[Dict[str, Any]] = None
    ) -> BaseAgent:
        """
        Create an agent instance of the specified type.
        
        Args:
            agent_type: Type of agent to create
            agent_id: Optional custom agent ID (generates UUID if not provided)
            config_override: Optional configuration overrides
            
        Returns:
            Configured agent instance
            
        Raises:
            AgentCreationError: If agent creation fails
        """
        try:
            # Validate agent type is registered
            if agent_type not in self._agent_registry:
                available_types = list(self._agent_registry.keys())
                raise AgentCreationError(
                    f"Unknown agent type: {agent_type}. "
                    f"Available types: {available_types}"
                )
            
            # Generate agent ID if not provided
            if agent_id is None:
                agent_id = f"{agent_type}_{str(uuid4())[:8]}"
            
            # Get agent class
            agent_class = self._agent_registry[agent_type]
            
            # Merge configuration
            config = self._agent_configs[agent_type].copy()
            if config_override:
                config.update(config_override)
            
            # Extract timeout from config (with default)
            timeout_seconds = config.get("timeout_seconds", 300)
            
            # Create agent instance
            agent = agent_class(
                agent_id=agent_id,
                agent_type=agent_type,
                claude_service=self.claude_service,
                settings=self.settings,
                timeout_seconds=timeout_seconds
            )
            
            # Apply additional configuration
            agent.configure(config)
            
            self.logger.info(f"Created agent: {agent_id} of type {agent_type}")
            return agent
            
        except Exception as e:
            raise AgentCreationError(f"Failed to create agent {agent_type}: {str(e)}")
    
    def create_all_agents(
        self,
        config_overrides: Optional[Dict[str, Dict[str, Any]]] = None
    ) -> Dict[str, BaseAgent]:
        """
        Create instances of all registered agent types.
        
        Args:
            config_overrides: Optional per-agent configuration overrides
            
        Returns:
            Dictionary mapping agent_type -> agent_instance
        """
        agents = {}
        config_overrides = config_overrides or {}
        
        for agent_type in self._agent_registry.keys():
            try:
                override_config = config_overrides.get(agent_type, {})
                agent = self.create_agent(
                    agent_type=agent_type,
                    config_override=override_config
                )
                agents[agent_type] = agent
                
            except Exception as e:
                self.logger.error(f"Failed to create agent {agent_type}: {str(e)}")
                # Continue creating other agents even if one fails
        
        self.logger.info(f"Created {len(agents)} agents out of {len(self._agent_registry)} registered types")
        return agents
    
    def get_registered_types(self) -> List[str]:
        """Get list of all registered agent types."""
        return list(self._agent_registry.keys())
    
    def get_agent_metadata(self, agent_type: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific agent type."""
        return self._agent_metadata.get(agent_type)
    
    def get_all_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Get metadata for all registered agent types."""
        return self._agent_metadata.copy()
    
    def is_registered(self, agent_type: str) -> bool:
        """Check if an agent type is registered."""
        return agent_type in self._agent_registry
    
    def unregister_agent(self, agent_type: str) -> bool:
        """
        Unregister an agent type.
        
        Args:
            agent_type: Type to unregister
            
        Returns:
            True if unregistered, False if not found
        """
        if agent_type in self._agent_registry:
            del self._agent_registry[agent_type]
            del self._agent_configs[agent_type]
            del self._agent_metadata[agent_type]
            self.logger.info(f"Unregistered agent type: {agent_type}")
            return True
        return False
    
    def get_factory_status(self) -> Dict[str, Any]:
        """Get status information about the factory."""
        return {
            "registered_agent_types": len(self._agent_registry),
            "agent_types": list(self._agent_registry.keys()),
            "factory_healthy": True,
            "claude_service_healthy": self.claude_service is not None
        }
    
    def _register_default_agents(self) -> None:
        """Register default agent types. This will be expanded as agents are implemented."""
        
        # Default agent configurations
        default_configs = {
            "acceptance_criteria": {
                "timeout_seconds": 300,
                "max_turns": 5,
                "focus_areas": ["requirement_compliance", "acceptance_testing"]
            },
            "bug_detection": {
                "timeout_seconds": 400,
                "max_turns": 7,
                "focus_areas": ["null_checks", "error_handling", "edge_cases"]
            },
            "security_analysis": {
                "timeout_seconds": 350,
                "max_turns": 6,
                "focus_areas": ["vulnerability_scan", "authentication", "data_validation"]
            },
            "logic_analysis": {
                "timeout_seconds": 300,
                "max_turns": 5,
                "focus_areas": ["algorithmic_correctness", "business_logic"]
            },
            "quality_analysis": {
                "timeout_seconds": 250,
                "max_turns": 4,
                "focus_areas": ["code_style", "maintainability", "documentation"]
            },
            "architecture_analysis": {
                "timeout_seconds": 400,
                "max_turns": 6,
                "focus_areas": ["design_patterns", "coupling", "scalability"]
            },
            "summary": {
                "timeout_seconds": 200,
                "max_turns": 3,
                "focus_areas": ["implementation_summary", "tutorial_generation"]
            }
        }
        
        # Agent metadata
        agent_metadata = {
            "acceptance_criteria": {
                "description": "Validates code against acceptance criteria and requirements",
                "priority": 1,
                "category": "requirements"
            },
            "bug_detection": {
                "description": "Identifies potential bugs and error conditions",
                "priority": 2,
                "category": "quality"
            },
            "security_analysis": {
                "description": "Performs security vulnerability analysis",
                "priority": 2,
                "category": "security"
            },
            "logic_analysis": {
                "description": "Analyzes business logic and algorithmic correctness",
                "priority": 3,
                "category": "logic"
            },
            "quality_analysis": {
                "description": "Evaluates code quality and maintainability",
                "priority": 4,
                "category": "quality"
            },
            "architecture_analysis": {
                "description": "Reviews architectural decisions and design patterns",
                "priority": 3,
                "category": "architecture"
            },
            "summary": {
                "description": "Generates implementation summaries and tutorials",
                "priority": 5,
                "category": "documentation"
            }
        }
        
        # Store configurations
        self._agent_configs.update(default_configs)
        self._agent_metadata.update(agent_metadata)
        
        # Register implemented agent classes
        self._register_implemented_agents()
        
        self.logger.info("Initialized default agent configurations")
    
    def _register_implemented_agents(self) -> None:
        """Register concrete agent classes that have been implemented."""
        try:
            # Import and register AcceptanceCriteriaAgent (from E2)
            from ..strategies.acceptance_criteria_strategy import AcceptanceCriteriaAgent
            
            self.register_agent(
                agent_type="acceptance_criteria",
                agent_class=AcceptanceCriteriaAgent,
                default_config=self._agent_configs.get("acceptance_criteria", {}),
                metadata=self._agent_metadata.get("acceptance_criteria", {})
            )
            
            # Import and register SecurityAnalysisAgent (E3.1)
            from ..strategies.security_analysis_strategy import SecurityAnalysisAgent
            
            self.register_agent(
                agent_type="security_analysis",
                agent_class=SecurityAnalysisAgent,
                default_config=self._agent_configs.get("security_analysis", {}),
                metadata=self._agent_metadata.get("security_analysis", {})
            )
            
            # Import and register BugDetectionAgent (E3.2)
            from ..strategies.bug_detection_strategy import BugDetectionAgent
            
            self.register_agent(
                agent_type="bug_detection",
                agent_class=BugDetectionAgent,
                default_config=self._agent_configs.get("bug_detection", {}),
                metadata=self._agent_metadata.get("bug_detection", {})
            )
            
            # Import and register LogicAnalysisAgent (E3.3)
            from ..strategies.logic_analysis_strategy import LogicAnalysisAgent
            
            self.register_agent(
                agent_type="logic_analysis",
                agent_class=LogicAnalysisAgent,
                default_config=self._agent_configs.get("logic_analysis", {}),
                metadata=self._agent_metadata.get("logic_analysis", {})
            )
            
            # Import and register CodeQualityAgent (E3.4)
            from ..strategies.code_quality_strategy import CodeQualityAgent
            
            self.register_agent(
                agent_type="quality_analysis",
                agent_class=CodeQualityAgent,
                default_config=self._agent_configs.get("quality_analysis", {}),
                metadata=self._agent_metadata.get("quality_analysis", {})
            )
            
            # Import and register ArchitecturalAgent (E3.5)
            from ..strategies.architectural_strategy import ArchitecturalAgent
            
            self.register_agent(
                agent_type="architecture_analysis",
                agent_class=ArchitecturalAgent,
                default_config=self._agent_configs.get("architecture_analysis", {}),
                metadata=self._agent_metadata.get("architecture_analysis", {})
            )
            
            # Import and register SummaryAgent (E3.6) - Runs PARALLEL
            from ..strategies.summary_strategy import SummaryAgent
            
            self.register_agent(
                agent_type="summary",
                agent_class=SummaryAgent,
                default_config=self._agent_configs.get("summary", {}),
                metadata=self._agent_metadata.get("summary", {})
            )
            
            self.logger.info("Successfully registered all 7 implemented agents for parallel execution")
            
        except ImportError as e:
            self.logger.warning(f"Some agent classes not available for registration: {str(e)}")
        except Exception as e:
            self.logger.error(f"Failed to register implemented agents: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the factory and its dependencies."""
        claude_healthy = await self.claude_service.health_check() if self.claude_service else False
        
        return {
            "factory_status": "healthy",
            "registered_agents": len(self._agent_registry),
            "claude_service_healthy": claude_healthy,
            "agent_types": list(self._agent_registry.keys())
        }