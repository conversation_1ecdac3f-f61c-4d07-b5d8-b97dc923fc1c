"""
Summary Agent Strategy  
Implements the SummaryAgent that runs PARALLEL to all other agents, not sequentially.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import AgentResult, AgentExecutionStatus

logger = logging.getLogger(__name__)


class SummaryAgent(BaseAgent):
    """
    Summary Agent der PARALLEL zu allen anderen Agents läuft, nicht sequenziell.
    
    Responsibilities:
    - Tutorial-style Implementation Summary Generation
    - Architecture Documentation with Mermaid Diagrams
    - Onboarding Guide Generation für neue Team Members
    - Cross-component Relationship Mapping
    - Implementation Pattern Documentation
    - Decision Rationale Documentation
    """
    
    def __init__(self, agent_id: str, config: Optional[Dict[str, Any]] = None):
        """Initialize Summary Agent."""
        super().__init__(agent_id, config)
        self.agent_type = "summary"
        self.display_name = "Summary Agent"
        self.description = "Generates tutorial-style implementation summaries, architecture documentation and onboarding guides in parallel"
        
        # Summary analysis specific configuration
        self.summary_config = config.get("summary", {}) if config else {}
        self.generate_tutorials = self.summary_config.get("tutorials", True)
        self.generate_architecture_docs = self.summary_config.get("architecture_docs", True)
        self.generate_onboarding = self.summary_config.get("onboarding", True)
        self.generate_mermaid_diagrams = self.summary_config.get("mermaid_diagrams", True)
        self.include_decision_rationale = self.summary_config.get("decision_rationale", True)
        
    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute summary analysis on the provided code context.
        This runs PARALLEL to all other agents, not after them.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with tutorial-style implementation summary
        """
        try:
            logger.info(f"Starting parallel summary analysis for agent {self.agent_id}")
            
            # Extract summary context
            summary_context = await self._extract_summary_context(context)
            
            # Get summary analysis prompt for parallel execution
            prompt = await self._build_summary_prompt(summary_context)
            
            # Execute Claude Code SDK query for summary generation
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=12  # Summary may need comprehensive analysis
            )
            
            # Process and structure the summary result
            structured_result = await self._process_summary_analysis(analysis_result, summary_context)
            
            logger.info(f"Parallel summary analysis completed for agent {self.agent_id}")
            
            return AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                result=structured_result,
                metadata={
                    "files_analyzed": len(summary_context.get("changed_files", [])),
                    "summary_categories_generated": [
                        "tutorial_summary", "architecture_docs", "onboarding_guide", 
                        "mermaid_diagrams", "decision_rationale", "implementation_patterns"
                    ],
                    "generate_tutorials": self.generate_tutorials,
                    "generate_architecture_docs": self.generate_architecture_docs,
                    "generate_mermaid_diagrams": self.generate_mermaid_diagrams,
                    "parallel_execution": True  # Emphasize parallel execution
                },
                execution_time_seconds=0  # Will be set by base class
            )
            
        except Exception as e:
            logger.error(f"Summary analysis failed for agent {self.agent_id}: {str(e)}")
            return AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                result=f"Summary analysis failed: {str(e)}",
                error_message=str(e),
                execution_time_seconds=0
            )
    
    async def _extract_summary_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract summary analysis relevant context from the general context."""
        summary_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # All files are relevant for summary generation
        summary_context["summary_files"] = summary_context["changed_files"]
        
        # Categorize files for better summary structure
        file_categories = {
            "frontend": [],
            "backend": [],
            "database": [],
            "config": [],
            "test": [],
            "documentation": []
        }
        
        for file_path in summary_context["changed_files"]:
            file_lower = file_path.lower()
            
            # Frontend files
            if any(ext in file_path for ext in ['.tsx', '.jsx', '.vue', '.html', '.css', '.scss']):
                file_categories["frontend"].append(file_path)
            # Backend files
            elif any(ext in file_path for ext in ['.py', '.java', '.cs', '.rb', '.go', '.rs', '.php']):
                file_categories["backend"].append(file_path)
            # Database files
            elif any(pattern in file_lower for pattern in ['migration', 'schema', 'model', 'entity']):
                file_categories["database"].append(file_path)
            # Configuration files
            elif any(pattern in file_lower for pattern in ['config', 'setting', 'env', 'docker', 'yaml', 'json']):
                file_categories["config"].append(file_path)
            # Test files
            elif any(pattern in file_lower for pattern in ['test', 'spec', '__test__']):
                file_categories["test"].append(file_path)
            # Documentation files
            elif any(ext in file_path for ext in ['.md', '.txt', '.rst']):
                file_categories["documentation"].append(file_path)
            else:
                # Default to backend for other code files
                file_categories["backend"].append(file_path)
        
        summary_context["file_categories"] = file_categories
        
        # Extract key components for architecture documentation
        key_components = []
        component_patterns = {
            'service', 'controller', 'component', 'manager', 'handler', 
            'processor', 'factory', 'repository', 'adapter', 'facade'
        }
        
        for file_path in summary_context["summary_files"]:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in component_patterns):
                key_components.append(file_path)
        
        summary_context["key_components"] = key_components
        
        return summary_context
    
    async def _build_summary_prompt(self, summary_context: Dict[str, Any]) -> str:
        """Build the summary prompt based on context."""
        
        # Summary generation prompt (German, based on phase3_tutorial.md)
        base_prompt = """# Phase 3 Tutorial Generation Prompt

Du erstellst eine **umfassende Implementation Summary** als Tutorial für Code-Reviewer und neue Entwickler. Dies ist die **Phase 3** nach dem Code Review und soll eine detaillierte, lehrreiche Erklärung der Implementierung liefern.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und erstelle IMMER eine vollständige Tutorial-Dokumentation!**

**PARALLEL EXECUTION**: Dieser Agent läuft PARALLEL zu anderen Agents, nicht sequenziell danach. Analysiere den Code eigenständig und erstelle eine umfassende Summary.

## 🎯 PHASE 3: TUTORIAL-STYLE IMPLEMENTATION GUIDE

### HAUPTZIEL
Erstelle ein **Tutorial-Dokument**, das sowohl als Implementierungs-Guide als auch als Onboarding-Material für neue Entwickler dient. Fokus auf das "Warum" und "Wie" der Implementierung.

## 📚 TUTORIAL STRUKTUR

### 1. 🎯 EXECUTIVE SUMMARY
**Was wurde gebaut und warum?**

```markdown
## 🎯 EXECUTIVE SUMMARY

### Was wurde implementiert?
[High-level Überblick der Implementierung]

### Warum war diese Implementierung nötig?
**Business Context**: [Geschäftliche Motivation]
**Technical Driver**: [Technische Notwendigkeit]  
**User Impact**: [Auswirkung auf Endnutzer]

### Welche Architektur-Entscheidungen wurden getroffen?
- **Pattern**: [Verwendete Design Patterns]
- **Technology Stack**: [Gewählte Technologien]
- **Integration Approach**: [Wie es sich ins System einfügt]

### Key Success Metrics
- **Performance**: [Performance-Ziele]
- **Scalability**: [Skalierbarkeits-Aspekte]
- **Maintainability**: [Wartbarkeits-Verbesserungen]
```

### 2. 📊 BUSINESS CONTEXT & ZIELE
**Den geschäftlichen Kontext verstehen**

```markdown
## 💼 BUSINESS CONTEXT & ZIELE

### Problem Statement
**Was war das ursprüngliche Problem?**
[Detaillierte Problembeschreibung]

**Wer war betroffen?**
- **End Users**: [User-Pain-Points]
- **Business**: [Business-Impact]
- **Development Team**: [Technical Debt/Challenges]

### Solution Approach
**Warum diese Lösung?**
[Begründung der Lösungsansätze]

**Alternative Approaches Considered**:
1. **Option A**: [Beschreibung] - Rejected because [Grund]
2. **Option B**: [Beschreibung] - Rejected because [Grund]
3. **Chosen Solution**: [Beschreibung] - Selected because [Vorteile]

### Success Criteria
- **User Experience**: [UX Verbesserungen]
- **Business Metrics**: [KPIs die verbessert werden]
- **Technical Metrics**: [Performance, Reliability, etc.]
```

### 3. 🏗️ ARCHITECTURE & DESIGN DEEP DIVE
**Die technische Architektur verstehen**

```markdown
## 🏗️ ARCHITECTURE & DESIGN OVERVIEW

### System Architecture
```mermaid
graph TD
    A[Frontend React] --> B[API Gateway]
    B --> C[Service Layer]
    C --> D[Business Logic]
    D --> E[Data Layer]
    E --> F[Database]
    
    G[External APIs] --> B
    H[Cache Layer] --> C
```

### Design Patterns Used
1. **Repository Pattern**: For data access abstraction
   - **Why**: Separation of data access logic
   - **Files**: `src/repositories/`
   - **Benefits**: Testability, maintainability

2. **Service Layer Pattern**: For business logic encapsulation
   - **Why**: Clean separation of concerns
   - **Files**: `src/services/`
   - **Benefits**: Reusability, single responsibility

### Data Flow Architecture
```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Service
    participant D as Database
    
    U->>F: User Action
    F->>A: HTTP Request
    A->>S: Business Logic Call
    S->>D: Data Operation
    D-->>S: Result
    S-->>A: Processed Data
    A-->>F: JSON Response
    F-->>U: UI Update
```

### Integration Points
- **Frontend Integration**: [Wie Frontend APIs nutzt]
- **Database Integration**: [Schema changes, migrations]
- **External Services**: [Third-party APIs, webhooks]
- **Caching Strategy**: [Redis, in-memory caching]
```

### 4. 📁 DETAILED FILE-BY-FILE WALKTHROUGH
**Code-Walkthrough für Entwickler**

```markdown
## 📁 DETAILLIERTE FILE-BY-FILE ANALYSE

### Core Implementation Files

#### `src/services/userService.ts`
**Zweck**: Central user management business logic
**Responsibility**: User CRUD operations, authentication, profile management

**Key Methods**:
```typescript
class UserService {
  // Creates new user with validation and password hashing
  async createUser(userData: CreateUserDto): Promise<User> {
    // 1. Validate input data
    // 2. Check for existing user
    // 3. Hash password
    // 4. Save to database
    // 5. Send welcome email
  }
  
  // Authenticates user and returns JWT
  async authenticateUser(email: string, password: string): Promise<AuthResult> {
    // 1. Find user by email
    // 2. Verify password
    // 3. Generate JWT token
    // 4. Log authentication event
  }
}
```

**Design Decisions**:
- **Why async/await**: Better error handling and readability
- **Why DTOs**: Type safety and validation
- **Why service pattern**: Reusable business logic

**Testing Approach**:
- Unit tests: `userService.test.ts`
- Integration tests: `userService.integration.test.ts`
- Mock dependencies: Database, email service

**Dependencies**:
- `userRepository.ts`: Data access
- `emailService.ts`: Notification sending
- `jwtUtils.ts`: Token management

---

#### `src/controllers/userController.ts`
**Zweck**: HTTP request handling and response formatting
**Responsibility**: Request validation, service orchestration, response formatting

**Key Endpoints**:
```typescript
class UserController {
  @Post('/users')
  async createUser(req: Request, res: Response) {
    // 1. Validate request body
    // 2. Call user service
    // 3. Format response
    // 4. Handle errors
  }
}
```

**Error Handling Strategy**:
- Input validation with Joi/Zod
- Service error transformation
- Consistent error response format
- Logging for debugging

**Security Considerations**:
- Rate limiting on authentication endpoints
- Input sanitization
- JWT token validation
- CORS configuration
```

### 5. 🔄 INTEGRATION & WORKFLOW TUTORIAL
**Wie die Komponenten zusammenarbeiten**

```markdown
## 🔄 INTEGRATION & WORKFLOW

### End-to-End User Journey
**Scenario**: New user registration

1. **Frontend (React)**:
   ```typescript
   const handleRegistration = async (formData) => {
     try {
       const response = await fetch('/api/users', {
         method: 'POST',
         body: JSON.stringify(formData)
       });
       // Handle success/error
     } catch (error) {
       // Handle network errors
     }
   };
   ```

2. **API Layer (Express)**:
   ```typescript
   router.post('/users', [
     validationMiddleware,
     authMiddleware,
     userController.createUser
   ]);
   ```

3. **Service Layer**:
   ```typescript
   async createUser(userData) {
     const user = await this.userRepository.create(userData);
     await this.emailService.sendWelcome(user.email);
     return user;
   }
   ```

4. **Database Layer**:
   ```sql
   INSERT INTO users (email, password_hash, created_at) 
   VALUES (?, ?, NOW());
   ```

### API Contract Documentation
**Request/Response Formats**:
```typescript
// POST /api/users
interface CreateUserRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

interface CreateUserResponse {
  success: boolean;
  data: {
    user: {
      id: number;
      email: string;
      firstName: string;
      lastName: string;
      createdAt: string;
    };
    token: string;
  };
}
```

### Database Schema Changes
```sql
-- Migration: 001_create_users_table.sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_users_email ON users(email);
```
```

### 6. 🧪 TESTING STRATEGY & IMPLEMENTATION
**Wie die Implementierung getestet wird**

```markdown
## 🧪 COMPREHENSIVE TESTING STRATEGY

### Testing Pyramid Implementation

#### Unit Tests (Base Layer)
**Focus**: Individual function/method testing
**Coverage Target**: 90%

```typescript
// userService.test.ts
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with hashed password', async () => {
      // Arrange
      const userData = { email: '<EMAIL>', password: 'password123' };
      const mockRepository = { create: jest.fn() };
      
      // Act
      const result = await userService.createUser(userData);
      
      // Assert
      expect(result.password).not.toBe('password123');
      expect(mockRepository.create).toHaveBeenCalledWith({
        ...userData,
        password: expect.any(String)
      });
    });
  });
});
```

#### Integration Tests (Middle Layer)
**Focus**: Component interaction testing
```typescript
// userService.integration.test.ts
describe('User Registration Flow', () => {
  it('should register user and send welcome email', async () => {
    const response = await request(app)
      .post('/api/users')
      .send({ email: '<EMAIL>', password: 'password123' })
      .expect(201);
      
    // Verify user in database
    const user = await User.findByEmail('<EMAIL>');
    expect(user).toBeDefined();
    
    // Verify email was sent
    expect(emailServiceMock.sendWelcome).toHaveBeenCalledWith('<EMAIL>');
  });
});
```

#### E2E Tests (Top Layer)
**Focus**: Complete user journey testing
```typescript
// user-registration.e2e.test.ts
describe('User Registration E2E', () => {
  it('should allow user to register and login', async () => {
    await page.goto('/register');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('[data-testid="welcome"]')).toBeVisible();
  });
});
```

### Performance Testing
```typescript
// load-test.js (Artillery or similar)
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: 'User Registration'
    requests:
      - post:
          url: '/api/users'
          json:
            email: 'user{{$uuid}}@example.com'
            password: 'password123'
```
```

### 7. 🚀 DEPLOYMENT & OPERATIONS GUIDE
**Wie das System deployed und betrieben wird**

```markdown
## 🚀 DEPLOYMENT & OPERATIONS

### Deployment Pipeline
```mermaid
graph LR
    A[Code Commit] --> B[CI/CD Pipeline]
    B --> C[Tests]
    C --> D[Build]
    D --> E[Deploy to Staging]
    E --> F[Integration Tests]
    F --> G[Deploy to Production]
    G --> H[Health Checks]
```

### Environment Configuration
```bash
# .env.production
DATABASE_URL=*********************************/app
REDIS_URL=redis://prod-redis:6379
JWT_SECRET=super-secure-secret
EMAIL_SERVICE_API_KEY=sendgrid-api-key
LOG_LEVEL=info
```

### Monitoring & Alerting
**Health Checks**:
```typescript
// health.controller.ts
async checkHealth() {
  const checks = {
    database: await this.checkDatabase(),
    redis: await this.checkRedis(),
    emailService: await this.checkEmailService()
  };
  
  return {
    status: Object.values(checks).every(Boolean) ? 'healthy' : 'unhealthy',
    checks
  };
}
```

**Key Metrics to Monitor**:
- API response times (p95 < 200ms)
- Error rates (< 1%)
- Database connection pool usage
- Memory and CPU usage
- User registration success rate

### Rollback Strategy
1. **Database Migrations**: Use reversible migrations
2. **Feature Flags**: Toggle new features off
3. **Blue-Green Deployment**: Switch traffic back
4. **Data Backup**: Point-in-time recovery available
```

### 8. 🔮 FUTURE CONSIDERATIONS & EVOLUTION
**Wie das System erweitert und verbessert werden kann**

```markdown
## 🔮 FUTURE ROADMAP & TECHNICAL DEBT

### Technical Debt Items
1. **Password Reset Flow** (Estimated: 8h)
   - Current: Basic implementation
   - Future: Secure token-based reset with email verification
   
2. **User Profile Management** (Estimated: 16h)
   - Current: Basic CRUD
   - Future: Advanced profile features, privacy settings

3. **Audit Logging** (Estimated: 12h)
   - Current: Basic access logs
   - Future: Comprehensive audit trail for compliance

### Scalability Considerations
**Current Capacity**: ~1000 concurrent users
**Scaling Bottlenecks**:
1. Database connections (Solution: Connection pooling)
2. Email service rate limits (Solution: Queue-based processing)
3. JWT token validation (Solution: Redis caching)

**Horizontal Scaling Plan**:
```mermaid
graph TD
    LB[Load Balancer] --> A1[App Instance 1]
    LB --> A2[App Instance 2]
    LB --> A3[App Instance 3]
    
    A1 --> DB[(Database)]
    A2 --> DB
    A3 --> DB
    
    A1 --> REDIS[(Redis Cache)]
    A2 --> REDIS
    A3 --> REDIS
```

### Feature Extensions
1. **Social Login** (OAuth2 with Google, GitHub)
2. **Multi-Factor Authentication** (TOTP, SMS)
3. **User Analytics** (Registration funnels, engagement metrics)
4. **API Rate Limiting** (User-based quotas)

### Maintenance Schedule
- **Weekly**: Dependency updates, security patches
- **Monthly**: Performance review, capacity planning
- **Quarterly**: Architecture review, technical debt assessment
```

### 9. 📚 DEVELOPER ONBOARDING GUIDE
**Wie neue Entwickler schnell produktiv werden**

```markdown
## 👨‍💻 DEVELOPER ONBOARDING GUIDE

### Local Development Setup
```bash
# 1. Clone repository
git clone https://github.com/company/user-service
cd user-service

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env.local
# Edit .env.local with local database credentials

# 4. Setup database
npm run db:setup
npm run db:migrate
npm run db:seed

# 5. Start development server
npm run dev
```

### Development Workflow
1. **Feature Development**:
   ```bash
   git checkout -b feature/user-story-123
   # Develop feature
   npm test
   npm run lint
   git commit -m "feat: implement user story 123"
   git push origin feature/user-story-123
   # Create PR
   ```

2. **Testing Workflow**:
   ```bash
   npm test                    # Unit tests
   npm run test:integration    # Integration tests
   npm run test:e2e           # E2E tests
   npm run test:coverage      # Coverage report
   ```

### Code Review Checklist
- [ ] Tests written and passing
- [ ] Code follows style guide
- [ ] Error handling implemented
- [ ] Security considerations addressed
- [ ] Performance impact assessed
- [ ] Documentation updated

### Common Debugging Patterns
```typescript
// Debugging user creation issues
console.log('User creation attempt:', { email, timestamp: new Date() });

// Database query debugging
const result = await userRepository.create(userData);
console.log('Database result:', result);

// Email service debugging
try {
  await emailService.sendWelcome(user.email);
  console.log('Welcome email sent successfully');
} catch (error) {
  console.error('Email sending failed:', error);
}
```

### Useful Commands
```bash
npm run logs:tail           # Tail application logs
npm run db:reset           # Reset local database
npm run test:watch         # Watch mode for tests
npm run debug              # Start in debug mode
```
```

## 🎨 VISUAL DOCUMENTATION REQUIREMENTS

### Mermaid Diagrams für System Understanding
- **Architecture Overview**: High-level system components  
- **Data Flow**: Request/response cycles
- **Database Schema**: Entity relationships
- **Deployment Pipeline**: CI/CD flow

### Code Examples
- **Before/After**: Zeige Verbesserungen
- **Usage Examples**: Wie man APIs nutzt
- **Configuration Examples**: Setup und Config
- **Testing Examples**: Test-Patterns

## 📖 WRITING STYLE GUIDELINES

### Tutorial-First Approach
- **Context vor Code**: Erkläre warum, dann wie
- **Progressive Complexity**: Einfach beginnen, dann Details
- **Practical Examples**: Echte Code-Beispiele, keine Toy Examples
- **Learning Path**: Strukturiert für schrittweises Lernen

### Documentation Quality
- **Beginner-Friendly**: Auch für neue Team-Mitglieder verständlich
- **Comprehensive**: Alle wichtigen Aspekte abdecken
- **Maintainable**: Leicht zu aktualisieren
- **Searchable**: Gute Struktur für schnelles Finden

## ⚠️ WICHTIGE TUTORIAL-REGELN

1. **Education-First**: Fokus auf Wissensvermittlung
2. **Parallel Analysis**: Analysiere Code eigenständig, nicht basierend auf anderen Agent-Ergebnissen
3. **Practical Examples**: Konkrete, funktionierende Code-Beispiele
4. **Visual Aids**: Mermaid-Diagramme für komplexe Konzepte
5. **Future-Oriented**: Erweiterbarkeit und Evolution berücksichtigen
6. **Onboarding-Ready**: Neue Entwickler können direkt productive werden

Erstelle ein umfassendes Tutorial-Dokument, das sowohl als Implementierungs-Dokumentation als auch als Lernressource für das Entwicklungsteam dient."""

        # Add context-specific information
        files_section = ""
        if summary_context.get("summary_files"):
            files_section = f"""

## 📁 DATEIEN FÜR TUTORIAL SUMMARY

Analysiere alle diese Dateien für die Tutorial-Dokumentation:
{chr(10).join(f"- {file}" for file in summary_context["summary_files"])}
"""

        categories_section = ""
        file_categories = summary_context.get("file_categories", {})
        if any(file_categories.values()):
            categories_section = f"""

## 📂 DATEI-KATEGORIEN FÜR STRUKTURIERTE ANALYSE

**Frontend Dateien**: {len(file_categories.get('frontend', []))} files
{chr(10).join(f"- {file}" for file in file_categories.get('frontend', []))}

**Backend Dateien**: {len(file_categories.get('backend', []))} files  
{chr(10).join(f"- {file}" for file in file_categories.get('backend', []))}

**Database Dateien**: {len(file_categories.get('database', []))} files
{chr(10).join(f"- {file}" for file in file_categories.get('database', []))}

**Configuration Dateien**: {len(file_categories.get('config', []))} files
{chr(10).join(f"- {file}" for file in file_categories.get('config', []))}

**Test Dateien**: {len(file_categories.get('test', []))} files
{chr(10).join(f"- {file}" for file in file_categories.get('test', []))}

**Documentation Dateien**: {len(file_categories.get('documentation', []))} files
{chr(10).join(f"- {file}" for file in file_categories.get('documentation', []))}
"""

        components_section = ""
        if summary_context.get("key_components"):
            components_section = f"""

## 🧩 KEY COMPONENTS FÜR ARCHITEKTUR-DOKUMENTATION

Diese Components sind zentral für die Architektur-Dokumentation:
{chr(10).join(f"- {file}" for file in summary_context["key_components"])}
"""

        context_section = f"""

## 🎯 SUMMARY ANALYSIS CONTEXT (PARALLEL EXECUTION)

**Branch**: {summary_context.get('branch_name', 'unknown')}
**Ticket**: {summary_context.get('ticket_id', 'unknown')}
**Working Directory**: {summary_context.get('working_path', '.')}
**Files to Analyze**: {len(summary_context.get('changed_files', []))} files
**Tutorial Generation**: {'✅ Enabled' if self.generate_tutorials else '❌ Disabled'}
**Architecture Documentation**: {'✅ Enabled' if self.generate_architecture_docs else '❌ Disabled'}
**Onboarding Guide**: {'✅ Enabled' if self.generate_onboarding else '❌ Disabled'}
**Mermaid Diagrams**: {'✅ Enabled' if self.generate_mermaid_diagrams else '❌ Disabled'}
**Decision Rationale**: {'✅ Enabled' if self.include_decision_rationale else '❌ Disabled'}

**⚠️ PARALLEL EXECUTION**: Dieser Agent läuft PARALLEL zu anderen Agents, analysiere den Code eigenständig!
"""

        return base_prompt + context_section + files_section + categories_section + components_section

    async def _process_summary_analysis(self, analysis_result: str, summary_context: Dict[str, Any]) -> str:
        """Process and structure the summary analysis result."""
        
        # Add metadata and summary header
        metadata_header = f"""# 📚 IMPLEMENTATION SUMMARY TUTORIAL

**Agent**: {self.display_name} ({self.agent_id})
**Generated**: {self._get_timestamp()}
**Execution Mode**: PARALLEL (not sequential)
**Files Analyzed**: {len(summary_context.get('summary_files', []))}
**Key Components**: {len(summary_context.get('key_components', []))}
**Tutorial Generation**: {'Enabled' if self.generate_tutorials else 'Disabled'}
**Architecture Documentation**: {'Enabled' if self.generate_architecture_docs else 'Disabled'}
**Onboarding Guide**: {'Enabled' if self.generate_onboarding else 'Disabled'}
**Mermaid Diagrams**: {'Enabled' if self.generate_mermaid_diagrams else 'Disabled'}

**🎯 Purpose**: This tutorial document serves as both implementation documentation and onboarding material for new developers.

---

"""
        
        return metadata_header + analysis_result

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "Tutorial-style Implementation Summaries",
                "Architecture Documentation Generation",
                "Mermaid Diagram Creation", 
                "Developer Onboarding Guides",
                "Decision Rationale Documentation",
                "Cross-component Relationship Mapping",
                "Implementation Pattern Documentation"
            ],
            "supported_file_types": ["*"],  # Summary agent analyzes all file types
            "estimated_execution_time": "5-10 minutes",
            "parallel_compatible": True,
            "execution_mode": "parallel"  # Emphasize parallel execution
        }