"""
Logic Analysis Agent Strategy
Implements the LogicAnalysisAgent for complex logic validation and algorithm review.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import AgentR<PERSON>ult, AgentExecutionStatus

logger = logging.getLogger(__name__)


class LogicAnalysisAgent(BaseAgent):
    """
    Logic Analysis Agent für komplexe Logik-Validierung und Algorithmus-Review.
    
    Responsibilities:
    - Algorithm and Business Logic Analysis
    - Data Flow and State Management Validation
    - Complex Conditional Logic Review  
    - Mathematical Correctness Verification
    - Business Rule Implementation Validation
    - Control Flow Analysis
    """
    
    def __init__(self, agent_id: str, config: Optional[Dict[str, Any]] = None):
        """Initialize Logic Analysis Agent."""
        super().__init__(agent_id, config)
        self.agent_type = "logic_analysis"
        self.display_name = "Logic Analysis Agent"
        self.description = "Analyzes algorithmic logic, business rules, data flow, state management and complex conditional logic"
        
        # Logic analysis specific configuration
        self.logic_config = config.get("logic_analysis", {}) if config else {}
        self.algorithm_complexity_check = self.logic_config.get("algorithm_complexity", True)
        self.business_rule_validation = self.logic_config.get("business_rules", True)
        self.state_flow_analysis = self.logic_config.get("state_flow", True)
        self.mathematical_verification = self.logic_config.get("math_verification", True)
        
    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute logic analysis on the provided code context.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with logic analysis findings
        """
        try:
            logger.info(f"Starting logic analysis for agent {self.agent_id}")
            
            # Extract logic analysis context
            logic_context = await self._extract_logic_analysis_context(context)
            
            # Get logic analysis prompt with comprehensive analysis
            prompt = await self._build_logic_analysis_prompt(logic_context)
            
            # Execute Claude Code SDK query for logic analysis
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=12  # Logic analysis may need deep investigation
            )
            
            # Process and structure the logic analysis result
            structured_result = await self._process_logic_analysis(analysis_result, logic_context)
            
            logger.info(f"Logic analysis completed for agent {self.agent_id}")
            
            return AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                result=structured_result,
                metadata={
                    "files_analyzed": len(logic_context.get("changed_files", [])),
                    "logic_categories_checked": [
                        "algorithms", "business_rules", "state_management", "data_flow",
                        "conditional_logic", "mathematical_operations", "control_flow"
                    ],
                    "algorithm_complexity_check": self.algorithm_complexity_check,
                    "business_rule_validation": self.business_rule_validation,
                    "state_flow_analysis": self.state_flow_analysis
                },
                execution_time_seconds=0  # Will be set by base class
            )
            
        except Exception as e:
            logger.error(f"Logic analysis failed for agent {self.agent_id}: {str(e)}")
            return AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                result=f"Logic analysis failed: {str(e)}",
                error_message=str(e),
                execution_time_seconds=0
            )
    
    async def _extract_logic_analysis_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract logic analysis relevant context from the general context."""
        logic_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # Filter for logic-heavy file types
        relevant_files = []
        logic_extensions = {'.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.cpp', '.c', '.cs', '.rb', '.go', '.rs', '.scala', '.kt'}
        
        for file_path in logic_context["changed_files"]:
            if Path(file_path).suffix.lower() in logic_extensions:
                relevant_files.append(file_path)
        
        logic_context["logic_files"] = relevant_files
        
        # Identify algorithm/computation heavy files
        algorithm_files = []
        algorithm_patterns = {
            'algorithm', 'calculate', 'compute', 'process', 'transform', 'parse', 
            'sort', 'search', 'filter', 'reduce', 'aggregate', 'optimize'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in algorithm_patterns):
                algorithm_files.append(file_path)
        
        logic_context["algorithm_files"] = algorithm_files
        
        # Identify business logic files
        business_files = []
        business_patterns = {
            'service', 'business', 'domain', 'model', 'entity', 'repository',
            'controller', 'handler', 'processor', 'manager', 'validator'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in business_patterns):
                business_files.append(file_path)
        
        logic_context["business_files"] = business_files
        
        # Identify state management files
        state_files = []
        state_patterns = {
            'state', 'store', 'reducer', 'action', 'slice', 'context', 
            'provider', 'hook', 'mutation', 'query'
        }
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in state_patterns):
                state_files.append(file_path)
        
        logic_context["state_files"] = state_files
        
        return logic_context
    
    async def _build_logic_analysis_prompt(self, logic_context: Dict[str, Any]) -> str:
        """Build the logic analysis prompt based on context."""
        
        # Logic analysis prompt (German)
        base_prompt = """# Logic Analysis Review Prompt

Du führst eine **tiefgreifende Logic und Algorithm Analysis** durch mit Fokus auf Business Logic, Data Flow und komplexe Algorithmen.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien
4. **Logic Flow:** Verfolge den Logic-Flow durch mehrere Funktionen/Klassen

## 🎯 REVIEW-FOKUS: LOGIC & ALGORITHM ANALYSIS

### HAUPTZIEL
Systematische Analyse von Algorithmus-Logic, Business Rules, Data Flow und State Management mit Fokus auf Korrektheit und Effizienz.

## 🔍 LOGIC-ANALYSE-KATEGORIEN

### 1. 🧮 ALGORITHMIC LOGIC & COMPLEXITY
**Schwerpunkt**: Algorithm-Korrektheit und Performance

- **Algorithm Correctness**: Logische Korrektheit der Implementierung
- **Time Complexity**: O(n) Analysis, Performance Bottlenecks
- **Space Complexity**: Memory Usage, Data Structure Efficiency
- **Edge Cases**: Boundary conditions, Empty inputs, Extreme values
- **Mathematical Correctness**: Formulas, calculations, rounding errors
- **Sorting & Searching**: Algorithm choice, implementation correctness
- **Data Structure Usage**: Appropriate data structure selection
- **Recursion Logic**: Base cases, termination conditions, stack overflow

### 2. 📋 BUSINESS LOGIC VALIDATION
**Schwerpunkt**: Business Rule Implementation

- **Business Rule Compliance**: Requirements vs. implementation
- **Domain Logic**: Correct modeling of business domains
- **Workflow Logic**: Process flows, state transitions
- **Validation Logic**: Input validation, business constraints
- **Business Calculations**: Pricing, taxes, discounts, commissions
- **Authorization Logic**: Permission checks, role-based access
- **Data Integrity**: Consistency rules, referential integrity
- **Business Exception Handling**: Domain-specific error scenarios

### 3. 🔄 STATE MANAGEMENT & DATA FLOW
**Schwerpunkt**: State Consistency und Data Flow

- **State Transitions**: Valid state changes, state machine logic
- **Data Flow Analysis**: Data transformation through system
- **State Synchronization**: Frontend/backend state consistency
- **Immutability**: State mutation vs. immutable updates
- **Event Handling**: Event flow, event ordering, side effects
- **Cache Consistency**: Cache invalidation, data staleness
- **Transaction Logic**: ACID properties, rollback scenarios
- **Concurrency Control**: Shared state access, race conditions

### 4. 🎛️ CONDITIONAL LOGIC & CONTROL FLOW
**Schwerpunkt**: Complex Conditional Logic

- **Boolean Logic**: Complex AND/OR conditions, De Morgan's laws
- **Conditional Complexity**: Nested if-statements, switch cases
- **Guard Clauses**: Early returns, defensive programming
- **Loop Logic**: Iteration conditions, termination, infinite loops
- **Exception Flow**: Try-catch logic, error propagation
- **Branching Logic**: Code paths, unreachable code
- **Pattern Matching**: Switch expressions, polymorphism
- **Control Flow Integrity**: Consistent flow through different paths

### 5. 📊 DATA TRANSFORMATION & PROCESSING
**Schwerpunkt**: Data Processing Logic

- **Data Mapping**: Object transformations, field mappings
- **Data Validation**: Schema validation, type checking
- **Data Aggregation**: Summaries, grouping, statistical calculations
- **Data Filtering**: Complex filter logic, query building
- **Data Serialization**: JSON/XML processing, format conversions
- **Data Migration**: Schema changes, data transformation logic
- **Stream Processing**: Real-time data processing, event streams
- **Batch Processing**: Large dataset processing, chunking logic

### 6. 🎯 INTEGRATION LOGIC & API CONTRACTS
**Schwerpunkt**: System Integration Logic

- **API Contract Validation**: Request/response validation
- **Service Integration**: External service calls, fallback logic
- **Message Processing**: Queue handling, message routing
- **Protocol Logic**: HTTP, WebSocket, gRPC logic implementation
- **Data Synchronization**: Multi-system data consistency
- **Error Recovery**: Retry logic, circuit breakers, degradation
- **Configuration Logic**: Feature flags, environment-specific logic
- **Dependency Injection**: Service wiring, lifecycle management

## 📊 DETAILLIERTES OUTPUT FORMAT

### LOGIC ANALYSIS DASHBOARD
```markdown
## 🧮 LOGIC ANALYSIS EXECUTIVE DASHBOARD

**Overall Logic Quality**: [EXCELLENT/GOOD/FAIR/POOR]
**Algorithm Complexity**: [OPTIMAL/ACCEPTABLE/SUBOPTIMAL/PROBLEMATIC]
**Business Logic Accuracy**: [FULLY_COMPLIANT/MOSTLY_COMPLIANT/GAPS_FOUND/NON_COMPLIANT]
**State Management**: [CONSISTENT/MOSTLY_CONSISTENT/INCONSISTENCIES/BROKEN]

### Logic Issue Summary
- 🚨 Critical Logic Errors: X (Must fix before merge)
- ❌ High Priority: Y (Should fix before merge)  
- ⚠️ Medium Priority: Z (Fix in next sprint)
- 💡 Optimizations: W (Performance improvements)

### Analysis Categories
- 🧮 Algorithm Issues: X
- 📋 Business Logic: Y
- 🔄 State Management: Z
- 🎛️ Control Flow: W
- 📊 Data Processing: V
- 🎯 Integration Logic: U
```

### CRITICAL LOGIC ERRORS SECTION
```markdown
## 🚨 CRITICAL LOGIC ERRORS

### 1. Incorrect Algorithm Implementation
**File**: `src/services/calculationService.ts:89-134`
**Severity**: CRITICAL
**Logic Type**: Mathematical Algorithm
**Impact**: Incorrect financial calculations, potential financial loss

**Problematic Algorithm**:
```typescript
function calculateCompoundInterest(principal, rate, time, compounding) {
  // WRONG: Missing conversion and incorrect formula
  const amount = principal * Math.pow(1 + rate, time * compounding);
  return amount - principal;
}
```

**Problems**:
1. **Rate Conversion**: Rate should be converted from percentage to decimal
2. **Formula Error**: Compound interest formula incorrectly applied
3. **Type Safety**: No validation of input types
4. **Edge Cases**: No handling of negative values or zero

**Test Cases that Fail**:
```javascript
// Expected: $610.51, Actual: $15,625
calculateCompoundInterest(1000, 5, 2, 12) 
```

**Correct Implementation**:
```typescript
function calculateCompoundInterest(
  principal: number, 
  annualRate: number, 
  years: number, 
  compoundingFrequency: number
): number {
  // Input validation
  if (principal <= 0 || annualRate < 0 || years < 0 || compoundingFrequency <= 0) {
    throw new Error('Invalid input parameters for compound interest calculation');
  }
  
  // Convert percentage to decimal
  const decimalRate = annualRate / 100;
  
  // Compound interest formula: A = P(1 + r/n)^(nt)
  const amount = principal * Math.pow(
    1 + (decimalRate / compoundingFrequency), 
    compoundingFrequency * years
  );
  
  // Return interest earned (amount - principal)
  return Number((amount - principal).toFixed(2));
}
```

**Verification**:
- Unit tests with known financial calculations
- Edge case testing (zero values, negative inputs)
- Cross-validation with financial libraries

---

### 2. Race Condition in State Management
**File**: `src/store/orderSlice.ts:67-89`
**Severity**: CRITICAL
**Logic Type**: State Management
**Impact**: Order state corruption, inventory inconsistencies

**Problematic State Logic**:
```typescript
// Redux Toolkit slice with race condition
const orderSlice = createSlice({
  name: 'order',
  initialState: { items: [], total: 0 },
  reducers: {
    addItem: (state, action) => {
      const item = action.payload;
      state.items.push(item);
      // Race condition: async calculation
      calculateTotal(state.items).then(total => {
        state.total = total; // State mutation after async operation!
      });
    }
  }
});
```

**Problems**:
1. **Async State Mutation**: State modified after async operation
2. **Race Conditions**: Multiple rapid actions cause inconsistent state
3. **Stale Closures**: Async callback may reference old state
4. **No Error Handling**: Failed calculation leaves state inconsistent

**Scenarios that Break**:
```javascript
// Rapid successive actions
dispatch(addItem(item1));
dispatch(addItem(item2));
dispatch(addItem(item3));
// Total may be calculated from partial state
```

**Correct Implementation**:
```typescript
const orderSlice = createSlice({
  name: 'order',
  initialState: { items: [], total: 0 },
  reducers: {
    addItem: (state, action) => {
      const item = action.payload;
      state.items.push(item);
      // Synchronous total calculation
      state.total = state.items.reduce((sum, item) => sum + item.price, 0);
    }
  },
  extraReducers: (builder) => {
    // For complex async calculations, use async thunks
    builder.addCase(calculateComplexTotal.fulfilled, (state, action) => {
      state.total = action.payload;
    });
  }
});

// Separate async thunk for complex calculations
const calculateComplexTotal = createAsyncThunk(
  'order/calculateComplexTotal',
  async (items: OrderItem[]) => {
    // Complex calculation with taxes, discounts, etc.
    const baseTotal = items.reduce((sum, item) => sum + item.price, 0);
    const taxes = await calculateTaxes(items);
    const discounts = await calculateDiscounts(items);
    return baseTotal + taxes - discounts;
  }
);
```

---

### 3. Business Logic Violation
**File**: `src/domain/subscriptionService.ts:123-167`
**Severity**: CRITICAL  
**Logic Type**: Business Rule Violation
**Impact**: Revenue loss, customer billing errors

**Flawed Business Logic**:
```typescript
function upgradeSubscription(userId, newPlan, effectiveDate) {
  const currentSub = getUserSubscription(userId);
  
  // WRONG: No prorating logic for mid-cycle upgrades
  return {
    userId,
    plan: newPlan,
    nextBillingDate: effectiveDate,
    amount: newPlan.price
  };
}
```

**Business Rule Violations**:
1. **No Prorating**: Should prorate charges for mid-cycle upgrades
2. **No Credit Calculation**: Existing subscription credit not handled
3. **Immediate Billing**: Should handle billing cycle alignment
4. **No Validation**: Plan compatibility not checked

**Correct Business Logic**:
```typescript
function upgradeSubscription(
  userId: string, 
  newPlan: SubscriptionPlan, 
  effectiveDate: Date
): SubscriptionChange {
  const currentSub = getUserSubscription(userId);
  
  if (!currentSub.isActive()) {
    throw new BusinessLogicError('Cannot upgrade inactive subscription');
  }
  
  if (newPlan.tier <= currentSub.plan.tier) {
    throw new BusinessLogicError('New plan must be higher tier than current');
  }
  
  // Calculate prorated credit for remaining period
  const remainingDays = calculateRemainingDays(currentSub, effectiveDate);
  const creditAmount = (currentSub.plan.price / 30) * remainingDays;
  
  // Calculate prorated charge for new plan
  const upgradeDays = 30 - remainingDays;
  const upgradeCharge = (newPlan.price / 30) * upgradeDays;
  
  // Net amount (upgrade charge minus credit)
  const netAmount = Math.max(0, upgradeCharge - creditAmount);
  
  return {
    userId,
    currentPlan: currentSub.plan,
    newPlan,
    effectiveDate,
    creditAmount,
    upgradeCharge,
    netAmount,
    nextBillingDate: calculateNextBillingDate(currentSub, effectiveDate)
  };
}
```
```

### HIGH PRIORITY LOGIC ISSUES
```markdown
## ❌ HIGH PRIORITY LOGIC ISSUES

### 1. Inefficient Algorithm (O(n²) instead of O(n))
**File**: `src/utils/dataProcessor.ts:45-78`
**Severity**: HIGH
**Logic Type**: Algorithm Performance
**Impact**: Performance degradation with large datasets

**Inefficient Implementation**:
```typescript
function findDuplicates(items: string[]): string[] {
  const duplicates = [];
  
  // O(n²) nested loops - inefficient!
  for (let i = 0; i < items.length; i++) {
    for (let j = i + 1; j < items.length; j++) {
      if (items[i] === items[j] && !duplicates.includes(items[i])) {
        duplicates.push(items[i]);
      }
    }
  }
  
  return duplicates;
}
```

**Performance Impact**:
- 100 items: 10,000 operations
- 1,000 items: 1,000,000 operations  
- 10,000 items: 100,000,000 operations

**Optimized O(n) Solution**:
```typescript
function findDuplicates(items: string[]): string[] {
  const seen = new Set<string>();
  const duplicates = new Set<string>();
  
  // O(n) single pass
  for (const item of items) {
    if (seen.has(item)) {
      duplicates.add(item);
    } else {
      seen.add(item);
    }
  }
  
  return Array.from(duplicates);
}
```

---

### 2. Complex Conditional Logic (Cyclomatic Complexity > 10)
**File**: `src/validators/formValidator.ts:89-156`
**Severity**: HIGH
**Logic Type**: Control Flow Complexity
**Impact**: Maintainability issues, hidden bugs

**Overly Complex Logic**:
```typescript
function validateUserInput(data) {
  if (data.type === 'email') {
    if (data.value && data.value.length > 0) {
      if (data.value.includes('@')) {
        if (data.value.split('@').length === 2) {
          if (data.value.split('@')[1].includes('.')) {
            return { valid: true };
          } else {
            return { valid: false, error: 'Invalid domain' };
          }
        } else {
          return { valid: false, error: 'Multiple @ symbols' };
        }
      } else {
        return { valid: false, error: 'Missing @ symbol' };
      }
    } else {
      return { valid: false, error: 'Empty email' };
    }
  } else if (data.type === 'phone') {
    // More nested conditions...
  }
  // ... more conditions
}
```

**Refactored with Guard Clauses**:
```typescript
function validateUserInput(data: InputData): ValidationResult {
  if (data.type === 'email') {
    return validateEmail(data.value);
  } else if (data.type === 'phone') {
    return validatePhone(data.value);
  }
  
  return { valid: false, error: 'Unknown input type' };
}

function validateEmail(email: string): ValidationResult {
  if (!email || email.length === 0) {
    return { valid: false, error: 'Empty email' };
  }
  
  if (!email.includes('@')) {
    return { valid: false, error: 'Missing @ symbol' };
  }
  
  const parts = email.split('@');
  if (parts.length !== 2) {
    return { valid: false, error: 'Multiple @ symbols' };
  }
  
  if (!parts[1].includes('.')) {
    return { valid: false, error: 'Invalid domain' };
  }
  
  return { valid: true };
}
```
```

### MEDIUM PRIORITY & OPTIMIZATIONS
```markdown
## ⚠️ MEDIUM PRIORITY LOGIC ISSUES

### 1. Missing Edge Case Handling
**Files**: Multiple utility functions
**Impact**: Runtime errors with unexpected inputs
**Fix**: Add comprehensive input validation and edge case handling

### 2. Inconsistent Data Flow
**Files**: Service layer communication
**Impact**: Data transformation inconsistencies
**Fix**: Standardize data transformation patterns

### 3. Complex State Dependencies
**Files**: State management modules
**Impact**: Difficult to maintain and debug
**Fix**: Reduce state coupling, implement cleaner state architecture

## 💡 LOGIC OPTIMIZATION OPPORTUNITIES

### Algorithm Improvements
1. **Caching**: Implement memoization for expensive calculations
2. **Data Structures**: Use more appropriate data structures (Map vs Object)
3. **Batch Processing**: Group operations for better performance
4. **Lazy Evaluation**: Defer expensive operations until needed

### Business Logic Enhancements
1. **Rule Engine**: Implement configurable business rule engine
2. **Validation Framework**: Centralized validation with schema definitions
3. **State Machines**: Use formal state machines for complex workflows
4. **Domain Events**: Implement event-driven architecture for business logic

### Code Organization
1. **Single Responsibility**: Break down complex functions
2. **Pure Functions**: Increase functional programming patterns
3. **Type Safety**: Leverage TypeScript for better logic validation
4. **Documentation**: Document complex algorithms and business rules
```

## 🔍 LOGIC ANALYSIS METHODOLOGY

### Algorithm Analysis Framework
1. **Correctness Verification**: Does the algorithm produce correct results?
2. **Complexity Analysis**: Time and space complexity assessment
3. **Edge Case Coverage**: Boundary conditions and extreme inputs
4. **Mathematical Validation**: Formula correctness and numerical stability

### Business Logic Validation
1. **Requirements Traceability**: Logic implementation vs. business requirements
2. **Domain Model Consistency**: Proper domain modeling and relationships
3. **Business Rule Compliance**: Adherence to business constraints
4. **Exception Scenario Handling**: Business exception and error cases

### State Management Assessment
1. **State Consistency**: Data consistency across system components
2. **State Transitions**: Valid state changes and flow
3. **Concurrency Safety**: Thread-safe state modifications
4. **Event Handling**: Proper event flow and side effect management

## ⚠️ WICHTIGE LOGIC-ANALYSE-REGELN

1. **Algorithm First**: Correctness vor Performance optimieren
2. **Business Rules**: Fachliche Anforderungen korrekt umsetzen
3. **Edge Cases**: Grenzfälle und Ausnahmen berücksichtigen
4. **State Safety**: State Management sicher und konsistent
5. **Testability**: Logic muss unit-testbar sein
6. **Maintainability**: Komplexe Logic dokumentieren und strukturieren

Führe eine systematische, tiefgreifende Logic-Analyse durch mit Fokus auf Algorithm-Korrektheit, Business Logic und State Management."""

        # Add context-specific information
        files_section = ""
        if logic_context.get("logic_files"):
            files_section = f"""

## 📁 LOGIC-RELEVANTE DATEIEN FÜR ANALYSE

Analysiere diese Dateien auf komplexe Logic-Patterns:
{chr(10).join(f"- {file}" for file in logic_context["logic_files"])}
"""

        algorithm_section = ""
        if logic_context.get("algorithm_files") and self.algorithm_complexity_check:
            algorithm_section = f"""

## 🧮 ALGORITHM-INTENSIVE DATEIEN

Prüfe diese Dateien besonders auf Algorithm-Korrektheit und Performance:
{chr(10).join(f"- {file}" for file in logic_context["algorithm_files"])}
"""

        business_section = ""
        if logic_context.get("business_files") and self.business_rule_validation:
            business_section = f"""

## 📋 BUSINESS LOGIC DATEIEN

Validiere diese Dateien gegen Business Requirements:
{chr(10).join(f"- {file}" for file in logic_context["business_files"])}
"""

        state_section = ""
        if logic_context.get("state_files") and self.state_flow_analysis:
            state_section = f"""

## 🔄 STATE MANAGEMENT DATEIEN

Analysiere State Flow und Data Consistency:
{chr(10).join(f"- {file}" for file in logic_context["state_files"])}
"""

        context_section = f"""

## 🎯 LOGIC ANALYSIS CONTEXT

**Branch**: {logic_context.get('branch_name', 'unknown')}
**Ticket**: {logic_context.get('ticket_id', 'unknown')}
**Working Directory**: {logic_context.get('working_path', '.')}
**Files to Analyze**: {len(logic_context.get('changed_files', []))} files
**Algorithm Complexity Check**: {'✅ Enabled' if self.algorithm_complexity_check else '❌ Disabled'}
**Business Rule Validation**: {'✅ Enabled' if self.business_rule_validation else '❌ Disabled'}
**State Flow Analysis**: {'✅ Enabled' if self.state_flow_analysis else '❌ Disabled'}
**Mathematical Verification**: {'✅ Enabled' if self.mathematical_verification else '❌ Disabled'}
"""

        return base_prompt + context_section + files_section + algorithm_section + business_section + state_section

    async def _process_logic_analysis(self, analysis_result: str, logic_context: Dict[str, Any]) -> str:
        """Process and structure the logic analysis result."""
        
        # Add metadata and summary to the result
        metadata_header = f"""# 🧮 LOGIC ANALYSIS REPORT

**Agent**: {self.display_name} ({self.agent_id})
**Analysis Date**: {self._get_timestamp()}
**Files Analyzed**: {len(logic_context.get('logic_files', []))}
**Algorithm Files**: {len(logic_context.get('algorithm_files', []))}
**Business Logic Files**: {len(logic_context.get('business_files', []))}
**State Management Files**: {len(logic_context.get('state_files', []))}
**Algorithm Complexity Check**: {'Enabled' if self.algorithm_complexity_check else 'Disabled'}
**Business Rule Validation**: {'Enabled' if self.business_rule_validation else 'Disabled'}
**State Flow Analysis**: {'Enabled' if self.state_flow_analysis else 'Disabled'}

---

"""
        
        return metadata_header + analysis_result

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "Algorithm Correctness Analysis",
                "Business Logic Validation",
                "State Management Review",
                "Data Flow Analysis", 
                "Complex Conditional Logic Review",
                "Mathematical Verification",
                "Control Flow Analysis"
            ],
            "supported_file_types": [".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".cpp", ".c", ".cs", ".rb", ".go", ".rs", ".scala", ".kt"],
            "estimated_execution_time": "12-18 minutes",
            "parallel_compatible": True
        }