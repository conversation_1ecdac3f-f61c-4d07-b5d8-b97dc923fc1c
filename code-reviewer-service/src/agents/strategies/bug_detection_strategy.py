"""
Bug Detection Agent Strategy
Implements the BugDetectionAgent for parallel bug analysis with specialized prompts.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..base_agent import BaseAgent
from ..models.agent_result import AgentResult, AgentExecutionStatus

logger = logging.getLogger(__name__)


class BugDetectionAgent(BaseAgent):
    """
    Bug Detection Agent für parallele Bug-Analyse mit spezialisierten Prompts.
    
    Responsibilities:
    - Logic Errors and Race Conditions
    - Memory Leaks and Performance Issues  
    - Error Handling Gaps
    - Null/Undefined Checks
    - Edge Case Detection
    - Runtime Error Analysis
    """
    
    def __init__(self, agent_id: str, agent_type: str, claude_service, settings, timeout_seconds: int = 300, config: Optional[Dict[str, Any]] = None):
        """Initialize Bug Detection Agent."""
        super().__init__(agent_id, agent_type, claude_service, settings, timeout_seconds)
        self.agent_type = "bug_detection"
        self.display_name = "Bug Detection Agent"
        self.description = "Analyzes code for logic bugs, runtime errors, race conditions, memory leaks and edge cases"
        
        # Bug detection specific configuration
        self.bug_config = config.get("bug_detection", {}) if config else {}
        self.complexity_threshold = self.bug_config.get("complexity_threshold", "medium")
        self.check_performance = self.bug_config.get("check_performance", True)
        self.analyze_concurrency = self.bug_config.get("analyze_concurrency", True)
        
    async def analyze(self, context: Dict[str, Any]) -> AgentResult:
        """
        Execute bug detection analysis on the provided code context.
        
        Args:
            context: Analysis context containing changed files, working directory, etc.
            
        Returns:
            AgentResult with bug detection findings
        """
        try:
            logger.info(f"Starting bug detection analysis for agent {self.agent_id}")
            
            # Extract bug detection context
            bug_context = await self._extract_bug_detection_context(context)
            
            # Get bug detection prompt with file analysis
            prompt = await self._build_bug_detection_prompt(bug_context)
            
            # Execute Claude Code SDK query for bug detection
            analysis_result = await self.execute_claude_query(
                prompt=prompt,
                context=context,
                max_turns=10  # Bug detection may need thorough investigation
            )
            
            # Process and structure the bug detection result
            structured_result = await self._process_bug_detection_analysis(analysis_result, bug_context)
            
            logger.info(f"Bug detection analysis completed for agent {self.agent_id}")
            
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.SUCCESS,
                success=True
            )
            result.set_result_data({"analysis": structured_result})
            result.context_data = {
                "files_analyzed": len(bug_context.get("changed_files", [])),
                "bug_categories_checked": [
                    "logic_bugs", "runtime_errors", "race_conditions", "memory_leaks",
                    "performance_issues", "error_handling", "edge_cases"
                ],
                "complexity_threshold": self.complexity_threshold,
                "performance_analysis": self.check_performance,
                "concurrency_analysis": self.analyze_concurrency
            }
            return result
            
        except Exception as e:
            logger.error(f"Bug detection analysis failed for agent {self.agent_id}: {str(e)}")
            result = AgentResult(
                agent_id=self.agent_id,
                agent_type=self.agent_type,
                status=AgentExecutionStatus.FAILED,
                success=False,
                error_message=str(e)
            )
            result.set_result_data({"error": f"Bug detection analysis failed: {str(e)}"})
            return result
    
    async def _extract_bug_detection_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract bug detection relevant context from the general context."""
        bug_context = {
            "changed_files": context.get("changed_files", []),
            "working_path": context.get("working_path", "."),
            "branch_name": context.get("branch_name", ""),
            "ticket_id": context.get("ticket_id", ""),
            "jira_info": context.get("jira_info", {}),
        }
        
        # Filter for bug-prone file types
        relevant_files = []
        bug_prone_extensions = {'.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.cpp', '.c', '.cs', '.rb', '.go', '.rs', '.php'}
        
        for file_path in bug_context["changed_files"]:
            if Path(file_path).suffix.lower() in bug_prone_extensions:
                relevant_files.append(file_path)
        
        bug_context["bug_prone_files"] = relevant_files
        
        # Identify async/concurrent files for race condition analysis
        async_files = []
        async_patterns = {'async', 'await', 'promise', 'thread', 'concurrent', 'parallel', 'worker'}
        
        for file_path in relevant_files:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in async_patterns):
                async_files.append(file_path)
        
        bug_context["async_files"] = async_files
        
        # Identify test files (to ensure proper test coverage)
        test_files = []
        test_patterns = {'test', 'spec', '__test__', '.test.', '.spec.'}
        
        for file_path in bug_context["changed_files"]:
            file_lower = file_path.lower()
            if any(pattern in file_lower for pattern in test_patterns):
                test_files.append(file_path)
        
        bug_context["test_files"] = test_files
        
        return bug_context
    
    async def _build_bug_detection_prompt(self, bug_context: Dict[str, Any]) -> str:
        """Build the bug detection prompt based on context."""
        
        # Load base bug detection prompt (German, based on existing bug_analysis_review.md)
        base_prompt = """# Bug Analysis Review Prompt

Du führst eine **tiefgreifende Bug Detection und Code Quality Analyse** durch mit Fokus auf Logic Bugs, Performance und Runtime Errors.

**⚠️ WICHTIGE ANWEISUNG: Verwende das Read tool um Code zu analysieren und zeige IMMER konkrete Code-Snippets mit exakten Datei:Zeile Referenzen!**

**ANALYSE-VORGEHEN:**
1. **Read verwenden:** Lies alle relevanten Dateien mit dem Read tool
2. **Exakte Locations:** Gib IMMER präzise Datei:Zeile Referenzen an
3. **Code zeigen:** Kopiere den tatsächlichen Code aus den Dateien
4. **Context bieten:** Zeige 2-3 Zeilen vor/nach für Verständnis

## 🎯 REVIEW-FOKUS: BUG DETECTION & QUALITY

### HAUPTZIEL
Systematische Identifikation von Bugs, Logic-Errors, Performance-Issues und Runtime-Problemen mit konkreten Fix-Empfehlungen.

## 🔍 BUG-ANALYSE-KATEGORIEN

### 1. 🐛 LOGIC BUGS & RUNTIME ERRORS
**Schwerpunkt**: Funktionale Korrektheit und Error Handling

- **Null/Undefined Access**: Fehlende Null-Checks, Optional-Handling
- **Array/Object Bounds**: Index-out-of-bounds, Property-Access auf undefined
- **Type Errors**: Implicit conversions, Type mismatches, Wrong assumptions
- **Conditional Logic**: Falsche Boolean-Logic, Missing edge cases
- **Loop Logic**: Infinite loops, wrong break conditions, Off-by-one errors
- **Exception Handling**: Try/catch gaps, unhandled exceptions, Error propagation
- **State Management**: Inconsistent state updates, State mutation bugs
- **Variable Scope**: Closure bugs, Variable shadowing, Hoisting issues

### 2. 🏃‍♂️ CONCURRENCY & RACE CONDITIONS
**Schwerpunkt**: Async/Await und Multi-Threading Issues

- **Race Conditions**: Time-of-check vs time-of-use, Shared resource access
- **Async/Await Issues**: Missing await, Promise handling, Callback hell
- **Deadlocks**: Resource locking conflicts, Circular dependencies
- **Memory Consistency**: Cache coherence, Memory visibility issues
- **Event Handling**: Event listener leaks, Missing cleanup
- **Thread Safety**: Non-atomic operations, Shared mutable state
- **Promise Chains**: Unhandled rejections, Promise anti-patterns
- **Concurrent Access**: Database transactions, File system conflicts

### 3. ⚡ PERFORMANCE & MEMORY ISSUES
**Schwerpunkt**: Performance Bottlenecks und Memory Management

- **Memory Leaks**: Event listener leaks, Object retention, Closure references
- **Performance Bottlenecks**: O(n²) algorithms, Inefficient data structures
- **Database Issues**: N+1 queries, Missing indexes, Slow query patterns
- **Network Performance**: Unnecessary API calls, Large payloads, Missing caching
- **Frontend Performance**: Bundle size, Render performance, Re-render cycles  
- **Resource Usage**: File handle leaks, Connection pool exhaustion
- **Garbage Collection**: GC pressure, Large object heap issues
- **Caching Problems**: Cache invalidation bugs, Memory usage in caches

### 4. 🚫 ERROR HANDLING & EDGE CASES
**Schwerpunkt**: Robustheit und Fehlerbehandlung

- **Missing Error Handling**: Unhandled exceptions, Silent failures
- **Error Propagation**: Swallowed errors, Inappropriate error handling
- **Input Validation**: Missing validation, Edge case inputs
- **Boundary Conditions**: Min/max values, Empty collections, Null inputs
- **Network Errors**: Timeout handling, Connection failures, Retry logic
- **File System Errors**: Permission issues, Disk space, Path problems
- **External API Errors**: API downtime, Rate limiting, Response format changes
- **Configuration Errors**: Missing config, Invalid values, Environment issues

### 5. 🔄 DATA CONSISTENCY & INTEGRITY
**Schwerpunkt**: Data Consistency und Business Logic

- **Data Corruption**: Partial updates, Transaction boundaries
- **Validation Logic**: Business rule violations, Data constraint failures
- **State Synchronization**: Frontend/Backend state mismatches
- **Database Integrity**: Foreign key violations, Constraint violations
- **Cache Consistency**: Stale cache data, Cache invalidation failures
- **Event Ordering**: Event sequence issues, Out-of-order processing
- **Data Migration**: Schema changes, Data transformation bugs
- **Audit Trail**: Missing audit logs, Incomplete change tracking

### 6. 🧪 TESTING & QUALITY ASSURANCE
**Schwerpunkt**: Test Coverage und Quality Metrics

- **Missing Tests**: Untested code paths, Edge case coverage
- **Flaky Tests**: Non-deterministic tests, Timing-dependent tests
- **Test Quality**: Poor assertions, Incomplete mocks
- **Integration Issues**: API contract changes, Service dependencies
- **Test Data**: Realistic test scenarios, Data setup/teardown
- **Regression Testing**: Breaking changes, Backward compatibility
- **Performance Testing**: Load testing gaps, Scalability limits
- **Error Scenario Testing**: Failure mode testing, Chaos engineering

## 📊 DETAILLIERTES OUTPUT FORMAT

### BUG ANALYSIS DASHBOARD
```markdown
## 🐛 BUG ANALYSIS EXECUTIVE DASHBOARD

**Overall Risk Level**: [CRITICAL/HIGH/MEDIUM/LOW]
**Code Quality Score**: X/10
**Bug Probability**: [HIGH/MEDIUM/LOW]
**Performance Impact**: [SEVERE/MODERATE/MINOR]

### Issue Summary
- 🚨 Critical Bugs: X (Must fix before merge)
- ❌ High Priority: Y (Should fix before merge)  
- ⚠️ Medium Priority: Z (Fix in next sprint)
- 💡 Improvements: W (Nice to have)

### Bug Categories
- 🐛 Logic Bugs: X
- 🏃‍♂️ Race Conditions: Y
- ⚡ Performance Issues: Z
- 🚫 Error Handling: W
- 🔄 Data Issues: V
- 🧪 Testing Gaps: U
```

### CRITICAL BUGS SECTION
```markdown
## 🚨 CRITICAL BUGS (Must Fix)

### 1. Race Condition in Payment Processing
**File**: `src/controllers/paymentController.ts:89-123`
**Severity**: CRITICAL
**Bug Type**: Race Condition
**Risk**: Double charges, financial loss, data corruption

**Problematic Code**:
```typescript
async function processPayment(userId, amount) {
  const balance = await getBalance(userId);        // Time-of-check
  if (balance >= amount) {
    await deductBalance(userId, amount);           // Time-of-use
    await createTransaction(userId, amount);
  }
}
```

**Problem**: Time-of-check vs time-of-use race condition
**Scenario**: Concurrent requests can bypass balance check
```
Request 1: getBalance(user1) -> 100€ ✓
Request 2: getBalance(user1) -> 100€ ✓
Request 1: deductBalance(user1, 80€) -> Balance: 20€
Request 2: deductBalance(user1, 50€) -> Balance: -30€ ❌
```

**Impact**: User can spend more than available balance
**Root Cause**: Non-atomic read-modify-write operation

**Fix**: Use database transactions with row locking
```typescript
async function processPayment(userId, amount) {
  const transaction = await db.transaction();
  try {
    // SELECT FOR UPDATE prevents concurrent access
    const balance = await getBalance(userId, { 
      transaction, 
      lock: transaction.LOCK.UPDATE 
    });
    
    if (balance >= amount) {
      await deductBalance(userId, amount, { transaction });
      await createTransaction(userId, amount, { transaction });
    } else {
      throw new Error('Insufficient balance');
    }
    
    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

**Testing**: 
- Concurrent payment tests
- Load testing with multiple users
- Stress testing payment scenarios

---

### 2. Memory Leak in Event Listeners
**File**: `src/components/DataVisualization.tsx:45-78`
**Severity**: CRITICAL
**Bug Type**: Memory Leak
**Impact**: Browser memory growth, tab crashes, performance degradation

**Problem**: Event listeners not cleaned up in useEffect
```typescript
useEffect(() => {
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleScroll);
  document.addEventListener('keydown', handleKeyDown);
  // Missing cleanup! Memory leak on component remount
}, []);
```

**Memory Growth**: Each component mount adds listeners without cleanup
**Symptoms**: 
- Browser memory usage increases over time
- Performance degradation after navigation
- Potential browser tab crashes

**Fix**: Proper cleanup in useEffect
```typescript
useEffect(() => {
  const handleResize = () => { /* resize logic */ };
  const handleScroll = () => { /* scroll logic */ };
  const handleKeyDown = (e) => { /* keydown logic */ };
  
  window.addEventListener('resize', handleResize);
  window.addEventListener('scroll', handleScroll);
  document.addEventListener('keydown', handleKeyDown);
  
  // Cleanup function prevents memory leaks
  return () => {
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('scroll', handleScroll);
    document.removeEventListener('keydown', handleKeyDown);
  };
}, []);
```

**Testing**: 
- React DevTools Profiler for memory usage
- Component mount/unmount stress testing
- Memory leak detection with Chrome DevTools

---

### 3. Unhandled Promise Rejection
**File**: `src/services/dataService.ts:156-178`
**Severity**: CRITICAL
**Bug Type**: Error Handling
**Impact**: Application crashes, data loss, poor user experience

**Problematic Code**:
```typescript
async function syncUserData() {
  const users = await fetchUsers();
  
  users.forEach(async (user) => {
    // Promise not awaited! Unhandled rejections possible
    updateUserProfile(user);
    sendNotification(user.email);
  });
  
  console.log('Sync completed'); // False! Async operations still running
}
```

**Issues**:
1. `forEach` doesn't wait for async operations
2. Unhandled promise rejections crash Node.js applications
3. Race conditions between operations
4. No error handling for individual user updates

**Fix**: Proper async handling with error management
```typescript
async function syncUserData() {
  const users = await fetchUsers();
  
  // Process users in parallel with error handling
  const results = await Promise.allSettled(
    users.map(async (user) => {
      try {
        await updateUserProfile(user);
        await sendNotification(user.email);
        return { success: true, userId: user.id };
      } catch (error) {
        logger.error(`Failed to sync user ${user.id}:`, error);
        return { success: false, userId: user.id, error };
      }
    })
  );
  
  const successful = results.filter(r => r.value?.success).length;
  const failed = results.length - successful;
  
  logger.info(`Sync completed: ${successful} successful, ${failed} failed`);
  
  if (failed > 0) {
    throw new Error(`${failed} user syncs failed`);
  }
}
```

**Testing**:
- Error injection testing
- Network failure scenarios
- Concurrent sync testing
```

### HIGH PRIORITY BUGS SECTION
```markdown
## ❌ HIGH PRIORITY BUGS

### 1. N+1 Query Problem
**File**: `src/services/articleService.ts:134-156`
**Severity**: HIGH
**Bug Type**: Performance Issue
**Impact**: Database overload, slow API responses, scalability issues

**Problem**: Loading related data in loop causes N+1 queries
```typescript
async function getArticlesWithAuthors() {
  const articles = await Article.findAll();        // 1 query
  
  for (const article of articles) {
    article.author = await User.findById(article.authorId);  // N queries
  }
  
  return articles;
}
```

**Performance Impact**: 
- 1 article = 2 queries  
- 100 articles = 101 queries
- 1000 articles = 1001 queries

**Fix**: Use eager loading or joins
```typescript
// Option 1: Eager loading
async function getArticlesWithAuthors() {
  return await Article.findAll({
    include: [{ model: User, as: 'author' }]
  });
}

// Option 2: Separate queries with mapping
async function getArticlesWithAuthors() {
  const articles = await Article.findAll();
  const authorIds = [...new Set(articles.map(a => a.authorId))];
  const authors = await User.findAll({ where: { id: authorIds } });
  
  const authorMap = new Map(authors.map(a => [a.id, a]));
  return articles.map(article => ({
    ...article,
    author: authorMap.get(article.authorId)
  }));
}
```

---

### 2. SQL Injection in Dynamic Query
**File**: `src/repositories/searchRepository.ts:67-89`
**Severity**: HIGH
**Bug Type**: Security & Logic Bug
**Impact**: Data breach, database corruption, unauthorized access

**Vulnerable Code**:
```typescript
async function searchProducts(filters) {
  let query = 'SELECT * FROM products WHERE 1=1';
  
  if (filters.category) {
    query += ` AND category = '${filters.category}'`;  // SQL Injection!
  }
  
  if (filters.priceRange) {
    query += ` AND price BETWEEN ${filters.priceRange.min} AND ${filters.priceRange.max}`;
  }
  
  return await db.query(query);
}
```

**Attack Vector**:
```javascript
// Malicious input
filters = {
  category: "'; DROP TABLE products; --"
}
// Results in: SELECT * FROM products WHERE 1=1 AND category = ''; DROP TABLE products; --'
```

**Fix**: Use parameterized queries
```typescript
async function searchProducts(filters) {
  const conditions = [];
  const params = [];
  
  if (filters.category) {
    conditions.push('category = ?');
    params.push(filters.category);
  }
  
  if (filters.priceRange) {
    conditions.push('price BETWEEN ? AND ?');
    params.push(filters.priceRange.min, filters.priceRange.max);
  }
  
  const whereClause = conditions.length > 0 ? 
    'WHERE ' + conditions.join(' AND ') : '';
  
  const query = `SELECT * FROM products ${whereClause}`;
  return await db.query(query, params);
}
```
```

### MEDIUM PRIORITY & SUGGESTIONS
```markdown
## ⚠️ MEDIUM PRIORITY BUGS

### 1. State Mutation Bug in Redux
**File**: `src/store/userSlice.ts:45-67`
**Impact**: Unpredictable UI behavior, state corruption
**Fix**: Use immutable updates with immer or spread operators

### 2. Missing Error Boundaries
**Files**: React component tree
**Impact**: Entire app crashes on component errors
**Fix**: Implement error boundaries for graceful error handling

### 3. Inconsistent Date Handling
**Files**: Multiple service files
**Impact**: Timezone bugs, incorrect date calculations
**Fix**: Standardize on UTC with proper timezone conversion

## 💡 BUG PREVENTION & QUALITY IMPROVEMENTS

### Code Quality Improvements
1. **Static Analysis**: Implement ESLint rules for common bug patterns
2. **Type Safety**: Increase TypeScript strict mode usage
3. **Unit Testing**: Improve test coverage for edge cases
4. **Integration Testing**: Add more realistic scenario testing

### Performance Monitoring
1. **APM Integration**: Application Performance Monitoring
2. **Database Monitoring**: Query performance tracking
3. **Memory Profiling**: Regular memory usage analysis
4. **Error Tracking**: Comprehensive error logging and alerting

### Development Process
1. **Code Reviews**: Mandatory peer reviews for bug-prone areas
2. **Testing Standards**: Minimum test coverage requirements
3. **Documentation**: Document known edge cases and workarounds
4. **Monitoring**: Real-time bug detection and alerting
```

### BUG ANALYSIS METHODOLOGY
```markdown
## 🔬 SYSTEMATIC BUG ANALYSIS METHODOLOGY

### Static Analysis Patterns
1. **Control Flow Analysis**: Dead code, unreachable paths
2. **Data Flow Analysis**: Uninitialized variables, type mismatches
3. **Dependency Analysis**: Circular dependencies, missing imports
4. **Pattern Matching**: Known anti-patterns, common bug signatures

### Dynamic Analysis Considerations
1. **Runtime Behavior**: How code behaves under different conditions
2. **Error Scenarios**: What happens when things go wrong
3. **Load Testing**: Performance under stress
4. **Edge Case Testing**: Boundary conditions and limits

### Risk Assessment Framework
**Critical**: Immediate system failure, data loss, security breach
**High**: Significant impact on functionality or performance
**Medium**: Moderate impact, workarounds available
**Low**: Minor issues, cosmetic problems

### Verification Strategy
1. **Automated Testing**: Unit tests for each bug fix
2. **Manual Testing**: Exploratory testing for edge cases
3. **Performance Testing**: Load testing for performance fixes
4. **Security Testing**: Penetration testing for security fixes
```

## 🔍 ANALYSE-RICHTLINIEN

### Deep Bug Investigation
- **Root Cause Analysis**: Nicht nur Symptome, sondern Ursachen finden
- **Impact Assessment**: Business und Technical Impact bewerten
- **Reproducibility**: Schritte zur Bug-Reproduktion dokumentieren
- **Testing Strategy**: Wie werden Fixes validiert?

### Code Pattern Analysis
- **Anti-Pattern Detection**: Bekannte problematische Patterns identifizieren
- **Logic Flow Review**: Control Flow und Data Flow analysieren
- **Error Path Analysis**: Was passiert bei Fehlern?
- **Performance Hotspots**: Kritische Performance-Bereiche identifizieren

### Fix Quality Assessment
- **Completeness**: Löst der Fix das eigentliche Problem?
- **Side Effects**: Können neue Bugs entstehen?
- **Performance Impact**: Auswirkungen auf Performance?
- **Maintainability**: Ist der Fix langfristig wartbar?

## ⚠️ WICHTIGE BUG-ANALYSE-REGELN

1. **Evidence-Based**: Konkrete Code-Stellen und Line Numbers
2. **Reproducible**: Reproduzierbare Bug-Szenarien mit Beispielen
3. **Root Cause Focus**: Nicht nur Symptome, sondern Ursachen beheben
4. **Fix Verification**: Testbare Lösungen mit Validierung
5. **Impact Assessment**: Business und Technical Impact bewerten
6. **Prevention Mindset**: Wie können ähnliche Bugs verhindert werden?

Führe eine systematische, tiefgreifende Bug-Analyse durch mit Fokus auf kritische Logic-Bugs, Performance-Issues und praktische Lösungsansätze."""

        # Add context-specific information
        files_section = ""
        if bug_context.get("bug_prone_files"):
            files_section = f"""

## 📁 BUG-PRONE DATEIEN FÜR ANALYSE

Analysiere besonders diese fehleranfälligen Dateien:
{chr(10).join(f"- {file}" for file in bug_context["bug_prone_files"])}
"""

        async_section = ""
        if bug_context.get("async_files") and self.analyze_concurrency:
            async_section = f"""

## 🏃‍♂️ CONCURRENCY-KRITISCHE DATEIEN

Prüfe diese Dateien besonders auf Race Conditions und Async-Bugs:
{chr(10).join(f"- {file}" for file in bug_context["async_files"])}
"""

        test_section = ""
        if bug_context.get("test_files"):
            test_section = f"""

## 🧪 TEST-DATEIEN (COVERAGE-ANALYSE)

Prüfe diese Test-Dateien auf Vollständigkeit:
{chr(10).join(f"- {file}" for file in bug_context["test_files"])}
"""

        context_section = f"""

## 🎯 BUG ANALYSIS CONTEXT

**Branch**: {bug_context.get('branch_name', 'unknown')}
**Ticket**: {bug_context.get('ticket_id', 'unknown')}
**Working Directory**: {bug_context.get('working_path', '.')}
**Files to Analyze**: {len(bug_context.get('changed_files', []))} files
**Performance Analysis**: {'✅ Enabled' if self.check_performance else '❌ Disabled'}
**Concurrency Analysis**: {'✅ Enabled' if self.analyze_concurrency else '❌ Disabled'}
**Complexity Threshold**: {self.complexity_threshold}
"""

        return base_prompt + context_section + files_section + async_section + test_section

    async def _process_bug_detection_analysis(self, analysis_result: str, bug_context: Dict[str, Any]) -> str:
        """Process and structure the bug detection analysis result."""
        
        # Add metadata and summary to the result
        metadata_header = f"""# 🐛 BUG DETECTION ANALYSIS REPORT

**Agent**: {self.display_name} ({self.agent_id})
**Analysis Date**: {datetime.now().isoformat()}
**Files Analyzed**: {len(bug_context.get('bug_prone_files', []))}
**Async/Concurrent Files**: {len(bug_context.get('async_files', []))}
**Test Files Found**: {len(bug_context.get('test_files', []))}
**Complexity Threshold**: {self.complexity_threshold}
**Performance Analysis**: {'Enabled' if self.check_performance else 'Disabled'}
**Concurrency Analysis**: {'Enabled' if self.analyze_concurrency else 'Disabled'}

---

"""
        
        return metadata_header + analysis_result

    def get_agent_info(self) -> Dict[str, Any]:
        """Return agent information for factory registration."""
        return {
            "agent_type": self.agent_type,
            "display_name": self.display_name,
            "description": self.description,
            "capabilities": [
                "Logic Bug Detection",
                "Race Condition Analysis",
                "Memory Leak Detection", 
                "Performance Issue Identification",
                "Error Handling Gap Analysis",
                "Edge Case Detection",
                "Runtime Error Prevention"
            ],
            "supported_file_types": [".py", ".js", ".ts", ".tsx", ".jsx", ".java", ".cpp", ".c", ".cs", ".rb", ".go", ".rs", ".php"],
            "estimated_execution_time": "10-20 minutes",
            "parallel_compatible": True
        }