"""
Monitoring and Observability Setup
OpenTelemetry configuration and structured logging
"""

import os
import logging
from typing import Optional

import structlog
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.sdk.resources import Resource


def setup_monitoring() -> None:
    """Initialize OpenTelemetry monitoring"""
    
    # Create resource information
    resource = Resource.create({
        "service.name": "code-reviewer-service",
        "service.version": "0.1.0",
        "deployment.environment": os.getenv("ENVIRONMENT", "development")
    })
    
    # Initialize tracer provider
    trace.set_tracer_provider(TracerProvider(resource=resource))
    tracer = trace.get_tracer(__name__)
    
    # Configure span exporters
    if os.getenv("OTLP_ENDPOINT"):
        # Production: Export to OTLP collector
        otlp_exporter = OTLPSpanExporter(
            endpoint=os.getenv("OTLP_ENDPOINT"),
            headers={"api-key": os.getenv("OTLP_API_KEY", "")},
        )
        span_processor = BatchSpanProcessor(otlp_exporter)
    else:
        # Development: Console output
        console_exporter = ConsoleSpanExporter()
        span_processor = BatchSpanProcessor(console_exporter)
    
    trace.get_tracer_provider().add_span_processor(span_processor)
    
    # Instrument logging
    LoggingInstrumentor().instrument(set_logging_format=True)


def setup_structured_logging(log_level: str = "INFO") -> None:
    """Configure structured logging with structlog"""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=None,
        level=getattr(logging, log_level.upper()),
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            # Use JSON formatting in production
            structlog.processors.JSONRenderer() if os.getenv("ENVIRONMENT") == "production" 
            else structlog.dev.ConsoleRenderer(colors=True)
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_tracer(name: str) -> trace.Tracer:
    """Get OpenTelemetry tracer instance"""
    return trace.get_tracer(name)


def log_performance_metrics(
    operation: str,
    duration_ms: float,
    success: bool = True,
    additional_data: Optional[dict] = None
) -> None:
    """Log performance metrics for monitoring"""
    logger = structlog.get_logger()
    
    log_data = {
        "operation": operation,
        "duration_ms": duration_ms,
        "success": success,
        "metric_type": "performance"
    }
    
    if additional_data:
        log_data.update(additional_data)
    
    if success:
        logger.info("Operation completed", **log_data)
    else:
        logger.warning("Operation failed", **log_data)