#!/bin/bash

# Production Deployment Script for Multi-Agent Code Reviewer
# Handles safe deployment with health checks, rollback capabilities, and monitoring

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
DEPLOYMENT_DATE=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${PROJECT_ROOT}/logs/deployment_${DEPLOYMENT_DATE}.log"

# Deployment configuration
ENVIRONMENT="${ENVIRONMENT:-production}"
DOCKER_REGISTRY="${DOCKER_REGISTRY:-localhost:5000}"
SERVICE_NAME="${SERVICE_NAME:-multi-agent-code-reviewer}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
ROLLBACK_ON_FAILURE="${ROLLBACK_ON_FAILURE:-true}"

# Feature flag configuration
ENABLE_PARALLEL_EXECUTION="${ENABLE_PARALLEL_EXECUTION:-false}"
MIGRATION_MODE="${MIGRATION_MODE:-canary}"
CANARY_PERCENTAGE="${CANARY_PERCENTAGE:-5}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "${LOG_FILE}"
}

log_success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] ✅ $1${NC}" | tee -a "${LOG_FILE}"
}

log_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] ⚠️ $1${NC}" | tee -a "${LOG_FILE}"
}

log_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ❌ $1${NC}" | tee -a "${LOG_FILE}"
}

# Error handling
handle_error() {
    local line_number=$1
    local error_code=$2
    log_error "Deployment failed at line ${line_number} with exit code ${error_code}"
    
    if [ "${ROLLBACK_ON_FAILURE}" = "true" ]; then
        log_warning "Initiating automatic rollback..."
        rollback_deployment
    fi
    
    exit "${error_code}"
}

trap 'handle_error ${LINENO} $?' ERR

# Cleanup function
cleanup() {
    log "Cleaning up temporary files..."
    rm -f /tmp/deployment_backup_*.sql 2>/dev/null || true
    rm -f /tmp/service_config_*.yaml 2>/dev/null || true
}

trap cleanup EXIT

# Pre-deployment checks
pre_deployment_checks() {
    log "🔍 Running pre-deployment checks..."
    
    # Check if required tools are installed
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed"; exit 1; }
    command -v docker-compose >/dev/null 2>&1 || { log_error "Docker Compose is required but not installed"; exit 1; }
    command -v python3 >/dev/null 2>&1 || { log_error "Python 3 is required but not installed"; exit 1; }
    command -v psql >/dev/null 2>&1 || { log_error "PostgreSQL client is required but not installed"; exit 1; }
    
    # Check environment variables
    if [ -z "${DATABASE_URL:-}" ]; then
        log_error "DATABASE_URL environment variable is required"
        exit 1
    fi
    
    if [ -z "${CLAUDE_API_KEY:-}" ] && [ -z "${ANTHROPIC_API_KEY:-}" ]; then
        log_error "CLAUDE_API_KEY or ANTHROPIC_API_KEY environment variable is required"
        exit 1
    fi
    
    # Check disk space (minimum 5GB free)
    available_space=$(df "${PROJECT_ROOT}" | awk 'NR==2 {print $4}')
    required_space=5242880  # 5GB in KB
    
    if [ "${available_space}" -lt "${required_space}" ]; then
        log_error "Insufficient disk space. Required: 5GB, Available: $((available_space / 1024 / 1024))GB"
        exit 1
    fi
    
    # Check if services are reachable
    if ! curl -f "${DATABASE_URL%/*}/health" >/dev/null 2>&1; then
        log_warning "Database health check failed - continuing with deployment"
    fi
    
    log_success "Pre-deployment checks passed"
}

# Create deployment backup
create_backup() {
    log "📦 Creating deployment backup..."
    
    local backup_dir="${PROJECT_ROOT}/backups/${DEPLOYMENT_DATE}"
    mkdir -p "${backup_dir}"
    
    # Backup database
    log "Backing up database..."
    pg_dump "${DATABASE_URL}" > "${backup_dir}/database_backup.sql"
    
    # Backup current configuration
    log "Backing up configuration..."
    cp -r "${PROJECT_ROOT}/config" "${backup_dir}/" 2>/dev/null || true
    
    # Backup feature flags
    if [ -f "${PROJECT_ROOT}/feature_flags.json" ]; then
        cp "${PROJECT_ROOT}/feature_flags.json" "${backup_dir}/"
    fi
    
    # Store current service version
    if command -v docker >/dev/null 2>&1; then
        docker images "${SERVICE_NAME}:*" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" > "${backup_dir}/current_images.txt" 2>/dev/null || true
    fi
    
    log_success "Backup created at ${backup_dir}"
    echo "${backup_dir}" > "${PROJECT_ROOT}/.last_backup"
}

# Build Docker images
build_images() {
    log "🔨 Building Docker images..."
    
    cd "${PROJECT_ROOT}"
    
    # Build main service image
    log "Building main service image..."
    docker build -t "${SERVICE_NAME}:${DEPLOYMENT_DATE}" \
                 -t "${SERVICE_NAME}:latest" \
                 -f Dockerfile .
    
    # Build monitoring dashboard image if exists
    if [ -f "monitoring/Dockerfile" ]; then
        log "Building monitoring dashboard image..."
        docker build -t "${SERVICE_NAME}-dashboard:${DEPLOYMENT_DATE}" \
                     -t "${SERVICE_NAME}-dashboard:latest" \
                     -f monitoring/Dockerfile ./monitoring/
    fi
    
    log_success "Docker images built successfully"
}

# Update database schema
update_database() {
    log "🗄️ Updating database schema..."
    
    # Check if migrations directory exists
    if [ -d "${PROJECT_ROOT}/migrations" ] || [ -d "${PROJECT_ROOT}/src/database/migrations" ]; then
        log "Running database migrations..."
        
        # Use Alembic if available
        if [ -f "${PROJECT_ROOT}/alembic.ini" ]; then
            cd "${PROJECT_ROOT}"
            python3 -m alembic upgrade head
        else
            log_warning "No migration system found - skipping database updates"
        fi
    else
        log_warning "No migrations directory found - skipping database updates"
    fi
    
    log_success "Database schema updated"
}

# Deploy application
deploy_application() {
    log "🚀 Deploying application..."
    
    cd "${PROJECT_ROOT}"
    
    # Generate docker-compose.prod.yml if it doesn't exist
    if [ ! -f "docker-compose.prod.yml" ]; then
        log "Generating production docker-compose configuration..."
        generate_production_compose
    fi
    
    # Update environment configuration
    update_environment_config
    
    # Deploy with docker-compose
    log "Starting services with docker-compose..."
    docker-compose -f docker-compose.prod.yml up -d --remove-orphans
    
    # Wait for services to start
    log "Waiting for services to start..."
    sleep 10
    
    log_success "Application deployed"
}

# Generate production docker-compose configuration
generate_production_compose() {
    cat > docker-compose.prod.yml << EOF
version: '3.8'

services:
  multi-agent-reviewer:
    image: ${SERVICE_NAME}:latest
    container_name: multi-agent-reviewer-prod
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=\${DATABASE_URL}
      - CLAUDE_API_KEY=\${CLAUDE_API_KEY}
      - ANTHROPIC_API_KEY=\${ANTHROPIC_API_KEY}
      - REDIS_URL=\${REDIS_URL:-redis://redis:6379}
      - LOG_LEVEL=\${LOG_LEVEL:-INFO}
      - ENABLE_TELEMETRY=true
      - FEATURE_FLAGS_FILE=/app/config/feature_flags.json
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G

  performance-dashboard:
    image: ${SERVICE_NAME}-dashboard:latest
    container_name: performance-dashboard-prod
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=\${DATABASE_URL}
      - REDIS_URL=\${REDIS_URL:-redis://redis:6379}
    volumes:
      - ./monitoring/data:/app/data
      - ./logs:/app/logs:ro
    depends_on:
      - multi-agent-reviewer
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 60s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: redis-prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - multi-agent-reviewer
      - performance-dashboard
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

volumes:
  redis_data:
    driver: local

networks:
  default:
    name: multi-agent-network
EOF
    
    log "Production docker-compose configuration generated"
}

# Update environment configuration
update_environment_config() {
    log "📝 Updating environment configuration..."
    
    # Create production environment file
    cat > .env.production << EOF
# Production Environment Configuration
ENVIRONMENT=production
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Database
DATABASE_URL=${DATABASE_URL}
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=40

# Redis
REDIS_URL=${REDIS_URL:-redis://redis:6379}
REDIS_DB=0

# Claude API
CLAUDE_API_KEY=${CLAUDE_API_KEY:-}
ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
CLAUDE_MAX_TURNS=5
CLAUDE_TIMEOUT=600
CLAUDE_RATE_LIMIT_RPM=100

# Multi-Agent Configuration
MAX_PARALLEL_AGENTS=7
AGENT_EXECUTION_TIMEOUT=900

# Feature Flags
FEATURE_FLAGS_FILE=/app/config/feature_flags.json
USE_REDIS_FEATURE_FLAGS=true
ENABLE_AUTOMATIC_ROLLOUT=${ENABLE_AUTOMATIC_ROLLOUT:-false}

# Security
SECRET_KEY=${SECRET_KEY:-$(openssl rand -hex 32)}
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Monitoring
ENABLE_TELEMETRY=true
LOG_LEVEL=INFO

# Performance
DEFAULT_WORKING_DIRECTORY=/tmp/code-reviews
MAX_FILE_SIZE_MB=50
MAX_FILES_PER_REVIEW=500
EOF
    
    # Create feature flags configuration for production
    mkdir -p config
    cat > config/feature_flags.json << EOF
{
  "multi_agent_execution": {
    "name": "multi_agent_execution",
    "description": "Enable parallel multi-agent execution",
    "status": "${ENABLE_PARALLEL_EXECUTION:-disabled}",
    "execution_mode": "parallel",
    "rollout_config": {
      "strategy": "${MIGRATION_MODE:-disabled}",
      "percentage": ${CANARY_PERCENTAGE:-0},
      "target_percentage": 100.0,
      "increment_step": 10.0,
      "increment_interval_minutes": 60
    },
    "enabled_for_environments": ["production"],
    "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "updated_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  },
  "websocket_progress_updates": {
    "name": "websocket_progress_updates",
    "description": "Enable real-time WebSocket progress updates",
    "status": "enabled",
    "execution_mode": "parallel",
    "enabled_for_environments": ["production"]
  },
  "performance_monitoring": {
    "name": "performance_monitoring",
    "description": "Enable detailed performance monitoring",
    "status": "enabled",
    "execution_mode": "parallel"
  },
  "circuit_breaker": {
    "name": "circuit_breaker",
    "description": "Enable circuit breaker for agent failures",
    "status": "enabled",
    "execution_mode": "parallel"
  }
}
EOF
    
    log_success "Environment configuration updated"
}

# Run health checks
run_health_checks() {
    log "🏥 Running health checks..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        log "Health check attempt ${attempt}/${max_attempts}..."
        
        # Check main service
        if curl -f http://localhost:8000/health >/dev/null 2>&1; then
            log_success "Main service health check passed"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            log_error "Health checks failed after ${max_attempts} attempts"
            return 1
        fi
        
        sleep 30
        ((attempt++))
    done
    
    # Run comprehensive health checks using Python script
    if [ -f "${PROJECT_ROOT}/monitoring/health_checks.py" ]; then
        log "Running comprehensive health checks..."
        cd "${PROJECT_ROOT}"
        python3 monitoring/health_checks.py --environment production
    fi
    
    log_success "All health checks passed"
}

# Configure monitoring and alerting
setup_monitoring() {
    log "📊 Setting up monitoring and alerting..."
    
    # Start performance dashboard if not already running
    if ! docker ps | grep -q performance-dashboard-prod; then
        log "Starting performance dashboard..."
        docker-compose -f docker-compose.prod.yml up -d performance-dashboard
    fi
    
    # Configure log rotation
    setup_log_rotation
    
    # Setup monitoring alerts (placeholder for integration with monitoring systems)
    setup_alerts
    
    log_success "Monitoring and alerting configured"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    # Create logrotate configuration
    sudo tee /etc/logrotate.d/multi-agent-reviewer << EOF
${PROJECT_ROOT}/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        docker kill -s HUP multi-agent-reviewer-prod 2>/dev/null || true
    endscript
}
EOF
    
    log "Log rotation configured"
}

# Setup monitoring alerts
setup_alerts() {
    log "Setting up monitoring alerts..."
    
    # Create alert configuration (adapt based on your monitoring system)
    mkdir -p config/alerts
    
    cat > config/alerts/performance_alerts.yaml << EOF
alerts:
  - name: high_error_rate
    condition: error_rate > 5%
    duration: 5m
    severity: critical
    action: rollback
    
  - name: slow_response_time
    condition: avg_response_time > 300s
    duration: 10m
    severity: warning
    action: notify
    
  - name: low_success_rate
    condition: success_rate < 95%
    duration: 5m
    severity: critical
    action: rollback
    
  - name: high_memory_usage
    condition: memory_usage > 90%
    duration: 15m
    severity: warning
    action: notify
    
  - name: service_down
    condition: service_health == false
    duration: 1m
    severity: critical
    action: restart
EOF
    
    log "Monitoring alerts configured"
}

# Post-deployment validation
post_deployment_validation() {
    log "✅ Running post-deployment validation..."
    
    # Validate service endpoints
    log "Validating service endpoints..."
    
    local endpoints=(
        "http://localhost:8000/health"
        "http://localhost:8000/api/v1/health"
        "http://localhost:8080/health"  # Performance dashboard
    )
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f "${endpoint}" >/dev/null 2>&1; then
            log_success "✅ ${endpoint} is responding"
        else
            log_error "❌ ${endpoint} is not responding"
            return 1
        fi
    done
    
    # Test feature flags
    log "Testing feature flags..."
    cd "${PROJECT_ROOT}"
    python3 -c "
from src.config.feature_flags import get_feature_flag_manager
manager = get_feature_flag_manager()
flags = manager.get_all_flags_status()
print(f'✅ Feature flags loaded: {len(flags)} flags')
for name, status in flags.items():
    print(f'  - {name}: {status[\"status\"]}')
" 2>/dev/null || log_warning "Feature flags test failed"
    
    # Test database connectivity
    log "Testing database connectivity..."
    if python3 -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    conn.close()
    print('✅ Database connection successful')
except Exception as e:
    print(f'❌ Database connection failed: {e}')
    exit(1)
" 2>/dev/null; then
        log_success "Database connectivity verified"
    else
        log_error "Database connectivity test failed"
        return 1
    fi
    
    # Performance validation
    log "Running performance validation..."
    if [ -f "${PROJECT_ROOT}/benchmarks/performance_comparison.py" ]; then
        log "Running quick performance test..."
        timeout 300 python3 benchmarks/performance_comparison.py --quick-test || {
            log_warning "Performance test failed or timed out"
        }
    fi
    
    log_success "Post-deployment validation completed"
}

# Rollback deployment
rollback_deployment() {
    log_error "🔄 Initiating deployment rollback..."
    
    local backup_path
    if [ -f "${PROJECT_ROOT}/.last_backup" ]; then
        backup_path=$(cat "${PROJECT_ROOT}/.last_backup")
    else
        log_error "No backup path found - cannot rollback"
        return 1
    fi
    
    if [ ! -d "${backup_path}" ]; then
        log_error "Backup directory not found: ${backup_path}"
        return 1
    fi
    
    log "Rolling back to backup: ${backup_path}"
    
    # Stop current services
    log "Stopping current services..."
    docker-compose -f docker-compose.prod.yml down || true
    
    # Restore database
    if [ -f "${backup_path}/database_backup.sql" ]; then
        log "Restoring database..."
        psql "${DATABASE_URL}" < "${backup_path}/database_backup.sql"
    fi
    
    # Restore configuration
    if [ -d "${backup_path}/config" ]; then
        log "Restoring configuration..."
        cp -r "${backup_path}/config"/* "${PROJECT_ROOT}/config/" 2>/dev/null || true
    fi
    
    # Restore feature flags
    if [ -f "${backup_path}/feature_flags.json" ]; then
        log "Restoring feature flags..."
        cp "${backup_path}/feature_flags.json" "${PROJECT_ROOT}/"
    fi
    
    # Restart services with previous configuration
    log "Restarting services..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait and verify rollback
    sleep 30
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "Rollback completed successfully"
    else
        log_error "Rollback verification failed"
        return 1
    fi
}

# Deployment summary
deployment_summary() {
    log "📋 Deployment Summary"
    echo "=================================="
    echo "Deployment Date: ${DEPLOYMENT_DATE}"
    echo "Environment: ${ENVIRONMENT}"
    echo "Service Name: ${SERVICE_NAME}"
    echo "Migration Mode: ${MIGRATION_MODE}"
    echo "Parallel Execution: ${ENABLE_PARALLEL_EXECUTION}"
    echo "Canary Percentage: ${CANARY_PERCENTAGE}%"
    echo "=================================="
    
    # Service status
    echo ""
    log "Service Status:"
    docker-compose -f docker-compose.prod.yml ps
    
    # Resource usage
    echo ""
    log "Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    # Log locations
    echo ""
    log "Important Files:"
    echo "  - Deployment Log: ${LOG_FILE}"
    echo "  - Service Logs: ${PROJECT_ROOT}/logs/"
    echo "  - Configuration: ${PROJECT_ROOT}/config/"
    echo "  - Backup: $(cat "${PROJECT_ROOT}/.last_backup" 2>/dev/null || echo 'None')"
    
    # Access URLs
    echo ""
    log "Access URLs:"
    echo "  - Main Service: http://localhost:8000"
    echo "  - Performance Dashboard: http://localhost:8080"
    echo "  - Health Check: http://localhost:8000/health"
}

# Main deployment function
main() {
    log "🚀 Starting Multi-Agent Code Reviewer Production Deployment"
    log "Deployment ID: ${DEPLOYMENT_DATE}"
    
    # Create logs directory
    mkdir -p "${PROJECT_ROOT}/logs"
    
    # Run deployment steps
    pre_deployment_checks
    create_backup
    build_images
    update_database
    deploy_application
    run_health_checks
    setup_monitoring
    post_deployment_validation
    
    # Success
    log_success "🎉 Deployment completed successfully!"
    deployment_summary
    
    # Final instructions
    echo ""
    log "Next Steps:"
    echo "1. Monitor the deployment: http://localhost:8080"
    echo "2. Review logs: tail -f ${LOG_FILE}"
    echo "3. Test the service: curl http://localhost:8000/health"
    echo "4. Check feature flags: python3 -c 'from src.config.feature_flags import get_feature_flag_manager; print(get_feature_flag_manager().get_all_flags_status())'"
    
    if [ "${ENABLE_PARALLEL_EXECUTION}" = "true" ]; then
        echo ""
        log_warning "⚠️ Parallel execution is ENABLED with ${MIGRATION_MODE} strategy (${CANARY_PERCENTAGE}% rollout)"
        echo "Monitor performance metrics closely and be prepared to rollback if needed."
        echo "Rollback command: ${SCRIPT_DIR}/rollback.sh"
    fi
}

# Command line interface
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback_deployment
        ;;
    "health-check")
        run_health_checks
        ;;
    "status")
        deployment_summary
        ;;
    "help")
        echo "Usage: $0 [deploy|rollback|health-check|status|help]"
        echo ""
        echo "Commands:"
        echo "  deploy       - Run full production deployment (default)"
        echo "  rollback     - Rollback to previous deployment"
        echo "  health-check - Run health checks only"
        echo "  status       - Show deployment status"
        echo "  help         - Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  ENVIRONMENT                - Deployment environment (default: production)"
        echo "  ENABLE_PARALLEL_EXECUTION  - Enable parallel execution (default: false)"
        echo "  MIGRATION_MODE             - Migration strategy (canary|gradual|instant)"
        echo "  CANARY_PERCENTAGE          - Canary rollout percentage (default: 5)"
        echo "  ROLLBACK_ON_FAILURE        - Auto rollback on failure (default: true)"
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac