# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
.tox/
.coverage
.pytest_cache/
htmlcov/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Logs
*.log
logs/
*.out

# Database
*.db
*.sqlite3
*.sqlite

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Poetry
poetry.lock

# Temporary files
tmp/
temp/
*.tmp
*.temp

# FastAPI
.openapi_cache/

# Alembic
alembic/versions/*.pyc