# TASK-004 JSON Processing System

## Overview

This documentation describes the complete Claude Code integration solution for processing TASK-004 specifications and generating structured JSON output. The system provides programmatic analysis of task requirements with strict typing, error handling, and real-time progress updates.

## Architecture

### Core Components

```
📁 schemas/
  └── task_004_json_schema.py     # JSON structure definitions and validation schema

📁 core/
  ├── task_004_json_processor.py  # Claude Code integration and prompt processing
  └── task_004_validation.py      # Validation engine and error handling

📁 examples/
  └── task_004_demo.py            # Demonstration and usage examples

📁 docs/
  └── task_004_json_processing.md # This documentation
```

### Integration Points

The system integrates with existing Claude Code patterns from:
- `claude_code_reviewer.py` - Prompt generation and streaming patterns
- `claude-code-reviewer-api.py` - API endpoint patterns and WebSocket integration
- `enhanced_claude_reviewer.py` - Configuration and team-specific customization

## JSON Schema Definition

### Task004ProcessedOutput Structure

```python
@dataclass
class Task004ProcessedOutput:
    # Task Metadata
    task_id: str = "TASK-004"
    task_title: str = "Dynamic Quality Metrics Implementation"
    processing_timestamp: str
    claude_code_version: str
    processing_duration_ms: int
    
    # Parsed Requirements
    requirements: List[TaskRequirement]
    technical_specifications: Dict[str, Any]
    acceptance_criteria: List[str]
    definition_of_done: List[str]
    
    # Implementation Analysis
    implementation_steps: List[ImplementationStep]
    effort_calculation_algorithm: Dict[str, Any]
    complexity_analysis_framework: ComplexityMetrics
    historical_tracking_design: HistoricalData
    
    # Configuration & Customization
    team_configurations: List[TeamConfig]
    configurable_parameters: Dict[str, Any]
    effort_multiplier_matrix: Dict[str, float]
    
    # Testing & Validation
    performance_tests: List[PerformanceTest]
    accuracy_tests: List[AccuracyTest]
    validation_criteria: Dict[str, Any]
    
    # Code Review Integration
    claude_code_integration: Dict[str, Any]
    structured_output_format: Dict[str, Any]
    api_endpoints: List[Dict[str, Any]]
    
    # Quality Metrics
    findings_analysis: List[Finding]
    effort_estimates: EffortEstimate
    confidence_metrics: Dict[str, float]
    
    # Status & Progress
    implementation_status: Literal["not_started", "in_progress", "completed", "blocked"]
    completion_percentage: float
    blockers: List[str]
    next_steps: List[str]
    
    # Error Handling
    validation_errors: List[str]
    processing_warnings: List[str]
    success: bool
    error_message: Optional[str]
```

### Key Data Structures

#### TaskRequirement
```python
@dataclass
class TaskRequirement:
    id: str
    description: str
    acceptance_criteria: List[str]
    priority: Literal["low", "medium", "high", "critical"]
    status: Literal["pending", "in_progress", "completed", "blocked"]
    estimated_story_points: int
    dependencies: List[str]
```

#### ImplementationStep
```python
@dataclass
class ImplementationStep:
    step_id: str
    title: str
    description: str
    implementation_details: str
    affected_files: List[str]
    estimated_hours: float
    prerequisites: List[str]
    success_criteria: List[str]
```

#### PerformanceTest
```python
@dataclass
class PerformanceTest:
    test_name: str
    execution_time_ms: float
    target_time_ms: float
    passed: bool
    iterations: int
    average_time_ms: float
    details: Dict[str, Any]
```

## Usage Examples

### Basic Processing

```python
from task_004_json_processor import Task004JsonProcessor

# Initialize processor
processor = Task004JsonProcessor(
    task_file_path="/path/to/TASK-004.md",
    worktree_path="/path/to/worktree",
    config={
        'team_name': 'backend_team',
        'effort_multipliers': {
            'critical_bugs': 4.0,
            'high_security': 3.5
        }
    }
)

# Process with progress callback
def progress_callback(message):
    print(f"Progress: {message.get('type')} - {message.get('message')}")

result = processor.process_with_claude_code(websocket_callback=progress_callback)

# Check results
if result.success:
    print(f"Requirements found: {len(result.requirements)}")
    print(f"Implementation steps: {len(result.implementation_steps)}")
    print(f"Completion: {result.completion_percentage}%")
else:
    print(f"Processing failed: {result.error_message}")
```

### Streaming Processing

```python
# Process with real-time streaming updates
def streaming_callback(message):
    if message.get('type') == 'claude_progress':
        preview = message.get('preview', '')
        print(f"Claude: {preview[:100]}...")
    elif message.get('type') == 'streaming_completed':
        print("Analysis completed!")

result = processor.process_streaming(websocket_callback=streaming_callback)
```

### Validation and Error Handling

```python
from task_004_validation import validate_and_handle_errors

# Validate output with comprehensive error analysis
validation_result, error_context = validate_and_handle_errors(
    output_data=result,
    context={'task_file_path': '/path/to/TASK-004.md'}
)

print(f"Valid: {validation_result.is_valid}")
print(f"Confidence: {validation_result.confidence_score:.2%}")

if validation_result.errors:
    print("Validation errors:")
    for error in validation_result.errors:
        print(f"  - {error}")

if error_context:
    print(f"Error type: {error_context.error_type}")
    print(f"Suggested fix: {error_context.suggested_fix}")
    print(f"Recoverable: {error_context.recoverable}")
```

## API Integration

### Flask Endpoints

The system provides three main API endpoints that can be integrated into `claude-code-reviewer-api.py`:

#### 1. Standard Processing Endpoint

```python
@app.route('/api/task-004/process', methods=['POST'])
def process_task_004():
    """Process TASK-004 with standard JSON output"""
    # Request: {"task_file_path": "...", "worktree_path": "...", "config": {...}}
    # Response: {"success": true, "result": {...}, "processing_duration_ms": 2500}
```

#### 2. Streaming Processing Endpoint

```python
@app.route('/api/task-004/stream', methods=['POST'])
def stream_task_004():
    """Process TASK-004 with streaming WebSocket updates"""
    # Provides real-time updates via socketio.emit('task_004_update', ...)
```

#### 3. Validation Endpoint

```python
@app.route('/api/task-004/validate', methods=['POST'])
def validate_task_004_output():
    """Validate TASK-004 JSON output structure"""
    # Request: {"output": {...}}
    # Response: {"success": true, "validation_errors": [], "confidence": 0.95}
```

### WebSocket Events

The streaming system emits the following WebSocket events:

- `task_004_update` - Progress updates during processing
- `task_004_complete` - Final completion with results
- `task_004_error` - Error notifications with context

## Claude Code Integration

### Prompt Generation

The system generates structured prompts that:

1. **Parse Task Content**: Extract requirements from German task description
2. **Analyze Implementation**: Map to existing effort calculation code
3. **Generate JSON**: Return strictly typed JSON response
4. **Include Context**: Use existing implementation context for accuracy

### Example Prompt Structure

```
TASK SPECIFICATION ANALYSIS AND JSON OUTPUT GENERATION

**Context**: You are analyzing TASK-004 "Dynamic Quality Metrics Implementation"...

**Task Content**:
```
[TASK-004.md content]
```

**Existing Implementation Context**:
```python
[_calculate_effort_estimate function and related code]
```

**Your Mission**: Generate strictly typed JSON response using this structure...

**Required JSON Structure**:
{
  "task_id": "TASK-004",
  "requirements": [...],
  "implementation_steps": [...],
  ...
}

**Critical Instructions**:
1. RETURN ONLY JSON - No markdown, no explanations
2. Fill in all placeholders with actual analysis
3. Extract ALL requirements from German description
4. Generate realistic effort estimates
```

### Claude Code Execution

The system uses Claude Code CLI with these parameters:

```bash
claude -p "prompt" \
  --output-format json \
  --verbose \
  --max-turns 5 \
  --allowedTools "Read,Grep,Glob,LS" \
  --disallowedTools "Write,Edit,MultiEdit,TodoWrite"
```

## Validation System

### Comprehensive Validation Rules

The validation system checks:

1. **Structure Validation**: Required fields, data types
2. **Constraint Validation**: Value ranges, business rules
3. **Array Validation**: Individual item validation
4. **Business Logic**: Effort consistency, performance targets
5. **JSON Schema**: Formal schema compliance

### Error Classification

Errors are classified by:

- **Type**: claude_code_timeout, json_parse_error, validation_error, etc.
- **Severity**: low, medium, high, critical
- **Recoverability**: true/false with suggested fixes
- **Context**: Location and circumstances of error

### Recovery Mechanisms

The system provides:

- **Contextual Fix Suggestions**: Based on error type and context
- **Partial Output Recovery**: Save partial results when possible
- **Graceful Degradation**: Fallback responses for critical failures
- **Retry Strategies**: Guidance for recoverable errors

## Performance Characteristics

### Target Performance

- **Processing Time**: < 5 minutes for complete analysis
- **Validation Time**: < 1 second for output validation
- **Memory Usage**: < 100MB for typical processing
- **JSON Size**: ~10-50KB for typical output

### Optimization Features

- **Streaming Output**: Real-time progress updates
- **Incremental Validation**: Early error detection
- **Caching**: JSON schema and validation rule caching
- **Timeout Handling**: Configurable timeouts with graceful degradation

## Configuration Options

### Team-Specific Configuration

```python
config = {
    'team_name': 'backend_team',
    'effort_multipliers': {
        'critical_bugs': 4.5,      # Hours per critical bug
        'high_security': 4.0,      # Hours per high security issue
        'medium_bugs': 2.0,        # Hours per medium bug
        'performance': 2.5,        # Hours per performance issue
        'code_quality': 1.0,       # Hours per quality issue
        'suggestions': 0.5         # Hours per suggestion
    },
    'complexity_multiplier': 1.3,  # Multiplier for high complexity
    'confidence_base': 30,         # Base confidence interval (±minutes)
    'team_velocity_factor': 0.9    # Team velocity adjustment
}
```

### Processing Configuration

```python
processor_config = {
    'timeout_seconds': 300,        # 5 minutes
    'max_turns': 5,               # Claude Code turns
    'streaming_enabled': True,     # Real-time updates
    'validation_enabled': True,    # Output validation
    'error_recovery': True         # Error handling
}
```

## Error Handling Patterns

### Common Error Scenarios

1. **Claude Code Not Available**
   - Error: "claude command not found"
   - Fix: Install Claude Code CLI
   - Recoverable: No

2. **JSON Parsing Failed**
   - Error: "JSON decode error"
   - Fix: Clean markdown formatting, retry parsing
   - Recoverable: Yes

3. **Processing Timeout**
   - Error: "timeout after 300 seconds"
   - Fix: Reduce complexity, increase timeout
   - Recoverable: Yes

4. **Validation Errors**
   - Error: "missing required field"
   - Fix: Review output structure, retry
   - Recoverable: Yes

### Error Response Format

```json
{
  "success": false,
  "error_message": "Processing timeout after 300 seconds",
  "error_type": "claude_code_timeout",
  "error_context": {
    "severity": "high",
    "recoverable": true,
    "suggested_fix": "Increase timeout value; Reduce prompt complexity",
    "location": "claude_processing"
  },
  "validation_errors": ["Processing timeout"],
  "processing_warnings": ["Error severity: high", "Recoverable: true"],
  "next_steps": ["Error recovery: Increase timeout value; Reduce prompt complexity"]
}
```

## Testing and Quality Assurance

### Validation Tests

```python
# Test validation system
from task_004_validation import Task004Validator

validator = Task004Validator()
result = validator.validate_complete_output(output)

assert result.is_valid
assert result.confidence_score > 0.8
assert len(result.errors) == 0
```

### Performance Tests

```python
# Test processing performance
import time

start_time = time.time()
result = processor.process_with_claude_code()
processing_time = time.time() - start_time

assert processing_time < 300  # 5 minutes
assert result.processing_duration_ms < 300000
```

### Integration Tests

```python
# Test complete workflow
def test_complete_workflow():
    # 1. Process task file
    result = processor.process_with_claude_code()
    
    # 2. Validate output
    validation_result, error_context = validate_and_handle_errors(result)
    
    # 3. Check requirements
    assert result.success
    assert validation_result.is_valid
    assert len(result.requirements) > 0
    assert len(result.implementation_steps) > 0
```

## Monitoring and Observability

### Metrics Collection

The system collects:

- Processing duration and performance metrics
- Validation error rates and types
- Claude Code API usage and costs
- Success/failure rates by error type

### Logging

Structured logging includes:

```python
logger.info("TASK-004 processing started", extra={
    'task_file': task_file_path,
    'worktree': worktree_path,
    'config': config
})

logger.info("TASK-004 processing completed", extra={
    'success': result.success,
    'duration_ms': result.processing_duration_ms,
    'requirements_count': len(result.requirements),
    'validation_errors': len(result.validation_errors)
})
```

### Health Checks

API health check endpoint:

```python
@app.route('/api/task-004/health', methods=['GET'])
def task_004_health():
    return {
        'status': 'healthy',
        'claude_code_available': check_claude_code(),
        'schema_version': '1.0',
        'last_processing': get_last_processing_time()
    }
```

## Future Enhancements

### Planned Features

1. **Machine Learning Integration**: Train models on historical effort data
2. **Advanced Caching**: Cache parsed task specifications
3. **Batch Processing**: Process multiple tasks simultaneously
4. **Custom Prompts**: Team-specific prompt templates
5. **Integration Testing**: Automated E2E testing framework

### Extensibility Points

1. **Custom Validators**: Add domain-specific validation rules
2. **Output Formats**: Support additional output formats (XML, YAML)
3. **Claude Models**: Support different Claude model versions
4. **Processing Strategies**: Alternative processing approaches

## Conclusion

The TASK-004 JSON Processing System provides a comprehensive solution for programmatic analysis of task specifications using Claude Code. It combines structured JSON output, comprehensive validation, error handling, and real-time progress updates to create a robust and reliable system for automated task processing.

The system follows existing patterns from the RMA codebase while introducing new capabilities for structured output generation and validation. It's designed to be extensible, maintainable, and production-ready for integration into existing development workflows.