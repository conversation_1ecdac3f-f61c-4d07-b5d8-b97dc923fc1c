# Enhanced PR Review System mit Jira Integration

Ein umfassendes System für automatisierte Pull Request Reviews mit **Claude Code**, **Jira Acceptance Criteria Analysis** und **Live Progress Tracking**.

## 🚀 Neue Features

### ✅ **Implementiert**
- **Automatische PR-Analyse** mit Git Worktree-Isolation
- **Claude Code Integration** mit Live-Progress Updates
- **Jira Ticket Integration** mit Acceptance Criteria Compliance
- **Config-basierte Workflows** statt CLI-Parameter-Chaos
- **Enhanced Reporting** mit Business-Context

### 🎫 **Jira Integration** 
- **Flexible Ticket-Quellen**: Jira API oder lokale Dateien
- **Automatische Ticket-Extraktion** aus Branch Namen
- **Acceptance Criteria Parsing** (verschiedene Formate)
- **Punkt-für-Punkt AC-Compliance-Check**
- **Business Context Discovery**
- **Themenverfehlung Detection**
- **Fallback-Mechanismus**: API → lokale Dateien → deaktiviert

## 📁 Neue Dateien

```
enhanced-pr-review-system/
├── jira_integration.py               # Jira API Integration
├── enhanced_claude_reviewer.py       # Enhanced Reviewer mit Jira
├── enhanced_workflow_automation.py   # Main Workflow Automation (kaputt - siehe Clean Version)
├── enhanced_workflow_automation_clean.py # Clean Version für Sample Creation
├── pr_analyzer.py                    # Alias für Kompatibilität
├── pr_review_config.json            # Sample-Konfiguration
├── ticket.md                         # Sample-Ticket-Datei
└── README_ENHANCED_SYSTEM.md         # Diese Datei
```

## 🛠 Installation & Setup

### 1. **Dependencies installieren**
```bash
# Claude Code installieren (falls noch nicht vorhanden)
npm install -g @anthropic-ai/claude-code

# Python Dependencies für Jira Integration
pip install requests pyyaml

# Bestehende Dependencies prüfen
python3 -c "import requests, yaml; print('✅ Dependencies OK')"
```

### 2. **Ticket-Informationen bereitstellen**

#### **Option A: Lokale Ticket-Datei (einfach, ohne API)**
```bash
# Sample Ticket bereits erstellt: ticket.md
# Bearbeiten Sie die Datei mit Ihren Ticket-Informationen
nano ticket.md
```

#### **Option B: Jira API (für Teams mit Jira)**
```bash
# Jira API Token erstellen (Jira → Profile → Security → API Tokens)
export JIRA_API_TOKEN="your_jira_api_token_here"

# Konfiguration in pr_review_config.json anpassen
nano pr_review_config.json
```

### 3. **Konfiguration anpassen**
```bash
# Sample Config bereits erstellt: pr_review_config.json
# Anpassen an Ihre Umgebung
nano pr_review_config.json
```

**Wichtige Einstellungen:**
- `pr_config.repository.path`: Pfad zu Ihrem Repository
- `pr_config.source.value`: Branch Name
- `jira_config.server_url`: Ihr Jira Server
- `jira_config.credentials`: Email und API Token

## 📋 Verwendung

### **1. Enhanced Review mit vorhandenem Branch**

Für Ihren aktuellen Branch:
```bash
# 1. Config anpassen
nano pr_review_config.json
# Setzen Sie: "value": "feature/CMS20-1166-autorefresh-von-luftqualität-"

# 2. Enhanced Review starten
python3 enhanced_claude_reviewer.py --config pr_review_config.json

# 3. Quick Review (falls das vollständige zu lange dauert)
python3 enhanced_claude_reviewer.py --config pr_review_config.json --quick
```

### **2. Nur Jira Integration testen**
```bash
# Test ohne API Token (lokale Dateien)
python3 jira_integration.py --branch "feature/CMS20-1166-autorefresh-von-luftqualität-"

# Test mit API Token
export JIRA_API_TOKEN="your_token"
python3 jira_integration.py --branch "feature/CMS20-1166-autorefresh-von-luftqualität-" --config pr_review_config.json
```

### **3. Standard Workflow (falls Enhanced problematisch)**
```bash
# Bestehender Workflow funktioniert weiterhin
python3 pr_workflow_automation.py --branch "feature/CMS20-1166-autorefresh-von-luftqualität-"
```

## 🎯 Kern-Features

### **1. Acceptance Criteria Analysis**
- Lädt Ticket aus Jira oder lokaler Datei
- Analysiert jede AC punkt-für-punkt
- Markiert: ✅ ERFÜLLT / ❌ NICHT ERFÜLLT / ⚠️ TEILWEISE
- Gibt konkrete Code-Stellen und Empfehlungen

### **2. Business Context Discovery**
- Versteht was das Feature bewirken soll
- Identifiziert fehlende Requirements
- Stellt Rückfragen bei Unklarheiten
- Erkennt Themenverfehlung

### **3. Enhanced Reporting**
- Kombiniert technisches Review mit AC-Compliance
- Executive Summary mit Business Impact
- Action Items kategorisiert nach Priorität
- Links zu Jira Tickets

## 📊 Output Beispiel

```markdown
# Enhanced Code Review Report mit Jira Integration

## 🎫 Jira Ticket Information
- **Ticket ID:** [CMS20-1166](https://company.atlassian.net/browse/CMS20-1166)
- **Summary:** Auto-refresh for air quality articles
- **Status:** In Progress

### 📋 Acceptance Criteria (4 Items)
1. System should automatically refresh air quality articles every morning at 6 AM
2. New articles must include current air quality index for major cities  
3. Users should receive notification when articles are updated
4. System should handle API failures gracefully and retry

## Acceptance Criteria Analysis

### ❌ AC 1: Auto-refresh at 6 AM
**Status:** NICHT ERFÜLLT  
**Problem:** Code setzt 7:00 statt 6:00 Uhr  
**Code-Stelle:** `apps/articleupdate-service/src/updateAirQualityArticles.ts:45`  
**Fix:** Ändere zu `now.setHours(6)`

### ✅ AC 2: Air Quality Index Integration  
**Status:** ERFÜLLT  
**Implementiert:** API-Calls für Luftqualitätsdaten

[... weitere AC-Analyse ...]

## Questions & Clarifications
1. **Business Logic:** Was definiert "major cities"? Top 10? Nach Einwohnerzahl?
2. **Timezone:** Welche Timezone für 6 AM? UTC oder lokale Zeit?

## Action Items
### Must-Fix vor Merge:
- [ ] Timezone Bug in getRefreshDateString() reparieren

**Gesamtbewertung:** 6/10 - Funktional solide, aber kritische AC-Issues
```

## 🔧 Troubleshooting

### **"ModuleNotFoundError: No module named 'requests'"**
```bash
pip install requests pyyaml
```

### **"Jira connection failed"**
```bash
# Prüfe API Token
echo $JIRA_API_TOKEN

# Teste manuell
curl -u "email:$JIRA_API_TOKEN" "https://your-server.atlassian.net/rest/api/3/myself"
```

### **"Branch existiert nicht"**
```bash
# Prüfe verfügbare Branches
git branch -a | grep -i luftqualität

# Oder verwende den existierenden Worktree
ls /var/folders/*/T/pr_analysis_*
```

### **"Enhanced Workflow Automation korrupt"**
```bash
# Nutze die Clean Version für Sample Creation
python3 enhanced_workflow_automation_clean.py --create-sample-config

# Oder nutze bestehende Scripts einzeln
python3 enhanced_claude_reviewer.py --config pr_review_config.json
```

## 📈 Nächste Schritte

### **Für Ihren aktuellen Branch:**
1. **Config anpassen** an Ihren Branch
2. **Enhanced Review starten** 
3. **AC-Compliance prüfen**
4. **Fixes implementieren**

### **System-Integration:**
1. **API Token konfigurieren** für echte Jira Integration
2. **Team-Config erstellen** für andere Branches/PRs
3. **CI/CD Integration** für automatische Reviews

## 🎉 Erfolg!

Das Enhanced PR Review System ist implementiert und einsatzbereit!

**Was funktioniert:**
- ✅ Jira Integration (API + lokale Dateien)
- ✅ Enhanced Claude Reviewer mit AC-Analysis
- ✅ Config-basierte Workflows
- ✅ Sample-Konfigurationen und Templates
- ✅ Backward-Kompatibilität mit bestehendem System

**Sofort nutzbar:**
```bash
python3 enhanced_claude_reviewer.py --config pr_review_config.json --quick
```

**Happy Enhanced Code Reviewing!** 🎉