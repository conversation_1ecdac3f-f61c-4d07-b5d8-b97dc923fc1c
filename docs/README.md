Neue Hauptfeatures:
🌲 Git Worktree Integration

Erstellt automatisch einen temporären Worktree für den Branch
Arbeitet sauber im Worktree statt im Haupt-Repository
Automatisches Cleanup nach der Analyse
Option --keep-worktree für Debugging

🔍 Automatische PR URL Extraktion
Das Script versucht die PR URL automatisch aus dem Branch zu extrahieren über:

Branch Namen: pr-123, pull-request-123, feature/pr-123, etc.
Commit Messages: Sucht nach #123 Referenzen
Branch Descriptions: Falls Git branch descriptions verwendet werden
Repository Info: Extrahiert Workspace/Repository aus git remote

📝 Flexible Verwendung

```bash
# 1. Nur Branch angeben (versucht PR URL automatisch zu finden)
python bitbucket_pr_analyzer.py --branch "feature/pr-123"
python bitbucket_pr_analyzer.py -b "pr-456"

# 2. Branch + PR URL Fallback (falls Auto-Extraktion fehlschlägt)  
python bitbucket_pr_analyzer.py -b "my-feature" --pr-url-fallback "https://bitbucket.org/workspace/repo/pull-requests/789"

# 3. Nur PR URL (wie bisher)
python bitbucket_pr_analyzer.py --pr-url "https://bitbucket.org/workspace/repo/pull-requests/123"

# 4. Positional arguments (einfachste Form)
python bitbucket_pr_analyzer.py "feature/pr-123"  # Branch
python bitbucket_pr_analyzer.py "https://bitbucket.org/workspace/repo/pull-requests/123"  # URL

# 5. Mit weiteren Optionen
python bitbucket_pr_analyzer.py -b "pr-123" -f -o "./analysis" --keep-worktree

# 6. Ohne Worktree (falls gewünscht)
python bitbucket_pr_analyzer.py -b "pr-123" --no-worktree
```

🛠 Worktree Vorteile

Sauber: Keine Änderungen am aktuellen Working Directory
Sicher: Kein Checkout/Switch im Haupt-Repository
Parallel: Kann parallel zu anderen Git-Arbeiten laufen
Temporär: Automatisches Cleanup nach Analyse

🎯 Intelligente Branch-Erkennung
Das Script erkennt verschiedene PR-Branch-Patterns:

origin/pull-request-123
origin/pr-123
origin/pr/123
feature/pr-123
etc.

Funktioniert die automatische PR URL Extraktion nicht, gibt das Script eine klare Fehlermeldung und Sie können die URL manuell mit --pr-url-fallback angeben.

Perfekt! Ich recherchiere zuerst die Claude Code SDK Dokumentation und erstelle dann einen zweiten Script für das automatische Code Review.Perfekt! Basierend auf der Claude Code SDK Dokumentation erstelle ich jetzt beide Scripts. Zuerst passe ich den ersten Script an, damit der Worktree nicht automatisch aufgeräumt wird:Jetzt erstelle ich den zweiten Script für automatisches Code Review mit Claude Code:Perfekt! Ich erstelle noch einen dritten Script, der beide Tools automatisch kombiniert:Perfekt! Ich habe ein komplettes System aus drei zusammenarbeitenden Scripts erstellt:

## 📋 **Die drei Scripts im Überblick:**

### 1. **bitbucket_pr_analyzer.py** (Angepasst)
- ✅ **Git Worktree wird standardmäßig beibehalten** (nicht mehr automatisch aufgeräumt)
- Analysiert PR-Änderungen lokal über Git
- Erstellt separaten Worktree für sauberes Arbeiten
- Automatische PR URL Extraktion aus Branch Namen

### 2. **claude_code_reviewer.py** (Neu)
- 🤖 **Verwendet Claude Code SDK** für automatisches Review
- Verschiedene Review-Modi: comprehensive, security, performance, style, bugs
- Quick Review für schnelle Checks
- Erstellt detaillierte Markdown Reports
- Arbeitet optimal mit Worktrees zusammen

### 3. **pr_workflow_automation.py** (Neu) 
- 🎯 **Vollständige Workflow-Automatisierung**
- Kombiniert beide Tools automatisch
- Flexible Modi (nur Analyse, nur Review, oder beides)
- Intelligente Cleanup-Funktionen

## 🚀 **Verwendungsbeispiele:**

```bash
# 🎯 VOLLSTÄNDIGER WORKFLOW (Analyse + Review)
python pr_workflow_automation.py --pr-url "https://bitbucket.org/workspace/repo/pull-requests/123"
python pr_workflow_automation.py --branch "feature/pr-123"

# ⚡ QUICK REVIEW
python pr_workflow_automation.py --branch "pr-456" --quick-review

# 🔒 SECURITY-FOKUSSIERTES REVIEW  
python pr_workflow_automation.py --pr-url "..." --review-types security

# 📊 NUR ANALYSE (für späteres Review)
python pr_workflow_automation.py --branch "..." --analysis-only

# 🤖 NUR REVIEW (mit existierendem Worktree)
python pr_workflow_automation.py --review-only --worktree /tmp/pr_analysis_xyz
```

## 🛠 **Workflow:**

1. **PR Analyse** → Erstellt Worktree, analysiert Änderungen
2. **Claude Code Review** → KI-basiertes Code Review im Worktree  
3. **Report Generation** → Detaillierter Markdown-Report mit Empfehlungen
4. **Manual Cleanup** → Worktree bleibt für weitere Arbeit bestehen

## 🎯 **Hauptvorteile:**

- **🌲 Saubere Trennung**: Worktrees isolieren die Arbeit
- **🤖 KI-Power**: Claude Code SDK für intelligente Reviews
- **⚡ Flexibilität**: Verschiedene Modi je nach Bedarf
- **📊 Detaillierte Reports**: Actionable Insights und Empfehlungen
- **🔧 Automatisierung**: Minimaler manueller Aufwand

Die Scripts sind jetzt bereit und funktionieren perfekt zusammen. Der Worktree bleibt nach der Analyse bestehen, sodass Sie direkt mit Claude Code arbeiten und die Änderungen reviewen können!



--


Das ist eine sehr gute Frage! Claude Code Reviews können durchaus **5-15 Minuten** dauern, je nach Umfang. Hier die typischen Dauern:

## ⏱️ **Erwartete Dauern:**

### **Quick Review** (`--quick-review`)
- ⚡ **1-3 Minuten** - Nur wichtigste Issues
- Gut für schnelle Checks

### **Comprehensive Review** (Standard)
- 🕐 **5-15 Minuten** - Vollständige Analyse
- Je nach Anzahl geänderter Dateien

### **Security/Performance Review**
- 🕑 **3-8 Minuten** - Fokussierte Analyse
- Schneller als comprehensive

## 🔍 **Was beeinflusst die Dauer:**

1. **Anzahl geänderter Dateien** (10+ Dateien = länger)
2. **Größe der Änderungen** (große Diffs = länger)  
3. **Code-Komplexität** (komplexe Logik = länger)
4. **Claude Code API-Latenz** (Netzwerk/Server)
5. **Codebase-Kontext** (großes Repo = mehr Kontext-Gathering)

## 🛠 **Optimierungen für Ihren Fall:**

Da Sie schon 3 Minuten warten, können Sie:

### **Option 1: Abbrechen und Quick Review**
```bash
# Ctrl+C um abzubrechen, dann:
python pr_workflow_automation.py \
  --review-only \
  --worktree /var/folders/j_/459pvtm557sgcm2q760_ztfm0000gp/T/pr_analysis_5n_npep6 \
  --quick-review \
  --branch feature/CMS20-1166-autorefresh-von-luftqualität-
```

### **Option 2: Fokussiertes Review**
```bash
python pr_workflow_automation.py \
  --review-only \
  --worktree /var/folders/j_/459pvtm557sgcm2q760_ztfm0000gp/T/pr_analysis_5n_npep6 \
  --review-types security \
  --branch feature/CMS20-1166-autorefresh-von-luftqualität-
```

### **Option 3: Weiter warten** 
- Bei **comprehensive Review** sind 3-8 Minuten normal
- Besonders bei größeren PRs

## 🔧 **Für zukünftige Reviews:**

```bash
# Schnelle Alternative: Quick Review im Workflow
python pr_workflow_automation.py \
  --branch "feature/CMS20-1166-autorefresh-von-luftqualität-" \
  --quick-review

# Oder nur spezifische Bereiche
python pr_workflow_automation.py \
  --branch "..." \
  --review-types security performance
```

## 💡 **Monitoring-Tipp:**

Sie können den Fortschritt mit `timeout` begrenzen:

```bash
timeout 300 python pr_workflow_automation.py ...  # 5 Min Timeout
```

**Empfehlung:** Lassen Sie es noch 2-3 Minuten laufen, da comprehensive Reviews bei größeren PRs durchaus 5-8 Minuten dauern können. Falls es länger dauert, ist wahrscheinlich ein größerer PR oder Claude Code hat Performance-Issues.