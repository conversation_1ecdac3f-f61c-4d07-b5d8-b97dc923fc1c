#!/bin/bash
# Multi-Ticket Batch Review Script

# Activate virtual environment
source venv/bin/activate

# Check if tickets are provided
if [ -z "$1" ]; then
    echo "🎯 Multi-Ticket Parallel Review System"
    echo ""
    echo "Usage:"
    echo "  $0 \"branch1,branch2,branch3\"                    # Quick review"
    echo "  $0 \"branch1,branch2\" --mode full                # Full review"
    echo "  $0 \"branch1:ticket1,branch2:ticket2\" --mode ac_only  # AC-only with ticket IDs"
    echo "  $0 \"branch1,branch2\" --mode bug_analysis --parallel 2  # Bug analysis, 2 parallel"
    echo ""
    echo "Examples:"
    echo "  $0 \"feature/CMS20-1166-air-quality,feature/CMS20-1167-fuel-prices\""
    echo "  $0 \"feature/branch1,feature/branch2,feature/branch3\" --mode full"
    echo "  $0 \"main,develop\" --mode bug_analysis --parallel 4"
    echo ""
    echo "Modes:"
    echo "  - quick        : Fast AC + basic quality (default)"
    echo "  - full         : AC + detailed bug analysis" 
    echo "  - ac_only      : Only acceptance criteria"
    echo "  - bug_analysis : Only bug detection & code quality"
    echo ""
    echo "Options:"
    echo "  --parallel N   : Max parallel reviews (default: 3)"
    echo "  --config FILE  : Custom config file"
    echo ""
    exit 1
fi

TICKETS="$1"
shift

echo "🚀 Starting Multi-Ticket Parallel Review..."
echo "📋 Tickets: $TICKETS"
echo ""

# Run the multi-ticket reviewer
python3 cli-tools/multi_ticket_reviewer.py --tickets "$TICKETS" "$@"

RESULT=$?

if [ $RESULT -eq 0 ]; then
    echo ""
    echo "🎉 Multi-Ticket Review successful!"
    echo "📄 Check the generated multi_ticket_review_report_*.md for details"
else
    echo ""
    echo "❌ Multi-Ticket Review completed with issues"
    echo "📄 Check the generated report for details"
fi

exit $RESULT
