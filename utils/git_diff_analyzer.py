#!/usr/bin/env python3
import os
import subprocess
import argparse
from datetime import datetime

class GitDiffAnalyzer:
    def __init__(self, output_dir="./git-analysis"):
        self.output_dir = output_dir
        self.memory_bank_dir = f"{output_dir}/memory-bank"
        self.report_path = f"{output_dir}/report.md"
        
        # Create output directories
        os.makedirs(self.memory_bank_dir, exist_ok=True)
        
        # Initialize report
        with open(self.report_path, 'w') as f:
            f.write(f"# Git Diff Analysis Report\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def run_git_command(self, command):
        """Execute git command and return output"""
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error executing: {command}")
            print(f"Error: {result.stderr}")
            return ""
        return result.stdout
    
    def get_file_content_at_commit(self, commit_hash, file_path, before=False):
        """Get file content at specific commit"""
        # Use commit^ to get content before the commit
        target_commit = f"{commit_hash}^" if before else commit_hash
        content = self.run_git_command(f"git show {target_commit}:{file_path} 2>/dev/null")
        return content
    
    def analyze_commit(self, commit_hash, service):
        """Analyze a specific commit for a service"""
        print(f"Analyzing {commit_hash} for {service}...")
        
        # Get commit info
        commit_info = self.run_git_command(f"git show -s --format=%B {commit_hash}")
        commit_author = self.run_git_command(f"git show -s --format=%an {commit_hash}")
        commit_date = self.run_git_command(f"git show -s --format=%ad {commit_hash}")
        
        # Get changed files - using raw diff name status
        changed_files_raw = self.run_git_command(
            f"git diff --name-status {commit_hash}^ {commit_hash}"
        ).strip()
        
        # Filter only files in the target service
        changed_files = []
        for line in changed_files_raw.split('\n'):
            if not line.strip():
                continue
            
            parts = line.split('\t')
            if len(parts) >= 2:
                change_type = parts[0]
                file_path = parts[1]
                
                if file_path.startswith(service):
                    changed_files.append((change_type, file_path))
        
        if not changed_files:  # Skip if no changes
            print(f"No changes found for {service} in commit {commit_hash}")
            return None
        
        # Create commit directory in memory bank
        commit_dir = f"{self.memory_bank_dir}/{commit_hash}_{service.replace('/', '_')}"
        os.makedirs(commit_dir, exist_ok=True)
        
        # Write commit metadata
        with open(f"{commit_dir}/metadata.md", 'w') as f:
            f.write(f"# Commit {commit_hash}\n\n")
            f.write(f"**Service:** {service}\n")
            f.write(f"**Author:** {commit_author.strip()}\n")
            f.write(f"**Date:** {commit_date.strip()}\n")
            f.write(f"**Message:**\n\n```\n{commit_info}\n```\n\n")
            f.write(f"**Changed Files:**\n\n")
        
        changes_summary = []
        
        # Process each changed file
        for change_type, file_path in changed_files:
            # Clean filename for saving
            clean_filename = file_path.replace('/', '_')
            
            # Get file diff
            file_diff = self.run_git_command(
                f"git diff {commit_hash}^ {commit_hash} -- {file_path}"
            )
            
            # Get file content before and after
            before_content = self.get_file_content_at_commit(commit_hash, file_path, before=True)
            after_content = self.get_file_content_at_commit(commit_hash, file_path)
            
            # Save file contents and diff to memory bank
            with open(f"{commit_dir}/{clean_filename}.md", 'w') as f:
                f.write(f"# Changes to {file_path}\n\n")
                f.write(f"**Change Type:** {change_type}\n\n")
                
                # Diff section
                f.write("## Changes with Diff\n\n")
                f.write("```diff\n")
                f.write(file_diff)
                f.write("\n```\n\n")
                
                # Before section
                f.write("## Original File (Before)\n\n")
                f.write("```\n")
                f.write(before_content if before_content else "File did not exist before this commit")
                f.write("\n```\n\n")
                
                # After section
                f.write("## Modified File (After)\n\n")
                f.write("```\n")
                f.write(after_content if after_content else "File was deleted in this commit")
                f.write("\n```\n")
            
            # Append to metadata
            with open(f"{commit_dir}/metadata.md", 'a') as f:
                f.write(f"- **{change_type}** [{file_path}](./{clean_filename}.md)\n")
            
            # Track change for summary
            changes_summary.append({
                'change_type': change_type,
                'filename': file_path,
                'commit': commit_hash,
                'service': service
            })
        
        return changes_summary
    
    def analyze_all_commits(self, commits, services):
        """Analyze all commits for all services"""
        all_changes = []
        
        for service in services:
            print(f"Analyzing service: {service}")
            for commit in commits:
                changes = self.analyze_commit(commit, service)
                if changes:
                    all_changes.extend(changes)
        
        # Generate final report
        self.generate_report(all_changes, commits, services)
        
        return all_changes
    
    def generate_report(self, all_changes, commits, services):
        """Generate final report with links to memory bank"""
        
        with open(self.report_path, 'a') as f:
            f.write(f"## Analysis Summary\n\n")
            f.write(f"- **Commits Analyzed:** {len(commits)}\n")
            f.write(f"- **Services Analyzed:** {', '.join(services)}\n")
            f.write(f"- **Total File Changes:** {len(all_changes)}\n\n")
            
            if not all_changes:
                f.write("No changes were found matching the specified criteria.\n\n")
                return
            
            # Categorize changes by service and file
            services_dict = {}
            for change in all_changes:
                service = change['service']
                if service not in services_dict:
                    services_dict[service] = {}
                
                filename = change['filename']
                if filename not in services_dict[service]:
                    services_dict[service][filename] = []
                
                services_dict[service][filename].append(change)
            
            # Add service summaries
            f.write("## Changes by Service\n\n")
            for service, files in services_dict.items():
                f.write(f"### {service}\n\n")
                f.write(f"Files changed: {len(files)}\n\n")
                
                # Add file details
                for filename, changes in files.items():
                    f.write(f"#### {filename}\n\n")
                    f.write(f"Changes in {len(changes)} commits:\n\n")
                    
                    for change in changes:
                        commit = change['commit']
                        change_type = change['change_type']
                        clean_filename = filename.replace('/', '_')
                        service_dir = service.replace('/', '_')
                        
                        link_path = f"memory-bank/{commit}_{service_dir}/{clean_filename}.md"
                        f.write(f"- [{change_type} in {commit[:7]}]({link_path})\n")
                    
                    f.write("\n")
            
            # Create commit table
            f.write("## Changes by Commit\n\n")
            f.write("| Commit | Service | Files Changed |\n")
            f.write("|--------|---------|---------------|\n")
            
            for commit in commits:
                for service in services:
                    # Count files changed in this commit
                    count = len([c for c in all_changes if c['commit'] == commit and c['service'] == service])
                    if count > 0:
                        dir_path = f"memory-bank/{commit}_{service.replace('/', '_')}"
                        f.write(f"| [{commit[:7]}]({dir_path}/metadata.md) | {service} | {count} |\n")
            
            f.write("\n## How to Use This Report\n\n")
            f.write("1. Use the links above to navigate to specific file changes\n")
            f.write("2. Each file change page contains:\n")
            f.write("   - The diff showing what changed\n")
            f.write("   - Original file content (before the commit)\n")
            f.write("   - Modified file content (after the commit)\n")
            f.write("3. The memory-bank directory contains all changes organized by commit\n")

def read_commits_from_file(file_path):
    """Read commit hashes from a file, skipping empty lines and comments"""
    if not os.path.exists(file_path):
        print(f"Error: Commits file not found: {file_path}")
        exit(1)
        
    commits = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                # Skip comments and empty lines
                line = line.strip()
                if line and not line.startswith('#'):
                    commits.append(line)
    except Exception as e:
        print(f"Error reading commits file: {e}")
        exit(1)
        
    if not commits:
        print(f"No valid commits found in {file_path}")
        exit(1)
        
    return commits

def read_services_from_file(file_path):
    """Read services from a file, skipping empty lines and comments"""
    if not os.path.exists(file_path):
        print(f"Error: Services file not found: {file_path}")
        exit(1)
        
    services = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                # Skip comments and empty lines
                line = line.strip()
                if line and not line.startswith('#'):
                    services.append(line)
    except Exception as e:
        print(f"Error reading services file: {e}")
        exit(1)
        
    if not services:
        print(f"No valid services found in {file_path}")
        exit(1)
        
    return services

def main():
    # Create argument parser
    parser = argparse.ArgumentParser(description='Analyze git commits and generate reports')
    
    # Add arguments
    parser.add_argument('--output-dir', default='.devtools/git-analysis', help='Output directory')
    parser.add_argument('--repo-path', default='.', help='Path to git repository')
    parser.add_argument('--commits-file', help='File containing commit hashes (one per line)')
    parser.add_argument('--services-file', help='File containing services to analyze (one per line)')
    parser.add_argument('--services', nargs='+', help='Services to analyze (space-separated)')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Change to repository directory
    os.chdir(args.repo_path)
    
    # Read commits from file or use defaults
    if args.commits_file:
        commits = read_commits_from_file(args.commits_file)
    else:
        print("No commits file specified. Please provide with --commits-file")
        exit(1)
    
    # Get services from file or command line
    if args.services_file:
        services = read_services_from_file(args.services_file)
    elif args.services:
        services = args.services
    else:
        # Default services if none specified
        services = ['apps/content-service', 'apps/rma-widgets']
    
    # Run analysis
    analyzer = GitDiffAnalyzer(output_dir=args.output_dir)
    analyzer.analyze_all_commits(commits, services)
    
    print(f"Analysis complete. Report available at: {analyzer.report_path}")

if __name__ == "__main__":
    main()

    