#!/usr/bin/env python3
"""
Advanced Ticket Discovery and Mapping System
Automatically maps branches to tickets from various sources.
"""

import json
import re
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class TicketMapping:
    branch: str
    ticket_id: str
    ticket_file: Optional[str] = None
    mapping_method: str = "unknown"
    confidence: float = 0.0

class AdvancedTicketDiscovery:
    """Advanced ticket discovery and mapping system"""
    
    def __init__(self, repo_path: str = "/Users/<USER>/dev/rma-mono"):
        self.repo_path = Path(repo_path)
        self.tickets_dir = self.repo_path / ".devtools" / "tickets"
        self.root_tickets_dir = self.repo_path / "tickets"
        
        # Ensure directories exist
        self.tickets_dir.mkdir(parents=True, exist_ok=True)
        self.root_tickets_dir.mkdir(parents=True, exist_ok=True)
        
        # Ticket ID patterns
        self.ticket_patterns = [
            r'([A-Z]+\d+-\d+)',  # CMS20-1166
            r'([A-Z]+-\d+)',     # CMS-1166  
            r'([A-Z]+_\d+)',     # CMS_1166
            r'([A-Z]{2,}\d+)',   # CMS1166
        ]
        
        # Branch to ticket mapping cache
        self.mapping_cache = {}
    
    def discover_ticket_mappings(self, branches: List[str]) -> List[TicketMapping]:
        """
        Discover ticket mappings for multiple branches using various strategies
        """
        mappings = []
        
        print(f"🔍 Discovering tickets for {len(branches)} branches...")
        print(f"📁 Searching in:")
        print(f"   - {self.tickets_dir}")
        print(f"   - {self.root_tickets_dir}")
        print(f"   - Branch names")
        print(f"   - Git commit messages")
        
        for branch in branches:
            mapping = self._discover_single_branch_ticket(branch)
            mappings.append(mapping)
            
            # Log discovery result
            if mapping.ticket_id:
                print(f"✅ {branch} → {mapping.ticket_id} (via {mapping.mapping_method}, confidence: {mapping.confidence:.1f})")
            else:
                print(f"❌ {branch} → No ticket found")
        
        return mappings
    
    def _discover_single_branch_ticket(self, branch: str) -> TicketMapping:
        """Discover ticket for a single branch using multiple strategies"""
        
        # Strategy 1: Extract from branch name (highest confidence)
        ticket_id = self._extract_ticket_from_branch(branch)
        if ticket_id:
            ticket_file = self._find_ticket_file(ticket_id)
            return TicketMapping(
                branch=branch,
                ticket_id=ticket_id,
                ticket_file=ticket_file,
                mapping_method="branch_name",
                confidence=0.9
            )
        
        # Strategy 2: Look for branch-specific ticket files
        branch_ticket_file = self._find_branch_specific_ticket_file(branch)
        if branch_ticket_file:
            ticket_id = self._extract_ticket_from_file(branch_ticket_file)
            return TicketMapping(
                branch=branch,
                ticket_id=ticket_id or "unknown",
                ticket_file=str(branch_ticket_file),
                mapping_method="branch_file",
                confidence=0.8
            )
        
        # Strategy 3: Search commit messages for ticket references
        ticket_id = self._extract_ticket_from_commits(branch)
        if ticket_id:
            ticket_file = self._find_ticket_file(ticket_id)
            return TicketMapping(
                branch=branch,
                ticket_id=ticket_id,
                ticket_file=ticket_file,
                mapping_method="commit_message",
                confidence=0.7
            )
        
        # Strategy 4: Look for generic ticket files that might match
        generic_ticket = self._find_generic_ticket_file()
        if generic_ticket:
            ticket_id = self._extract_ticket_from_file(generic_ticket)
            return TicketMapping(
                branch=branch,
                ticket_id=ticket_id or "generic",
                ticket_file=str(generic_ticket),
                mapping_method="generic_file",
                confidence=0.5
            )
        
        # Strategy 5: Create placeholder mapping
        return TicketMapping(
            branch=branch,
            ticket_id=None,
            ticket_file=None,
            mapping_method="none",
            confidence=0.0
        )
    
    def _extract_ticket_from_branch(self, branch: str) -> Optional[str]:
        """Extract ticket ID from branch name"""
        for pattern in self.ticket_patterns:
            match = re.search(pattern, branch, re.IGNORECASE)
            if match:
                return match.group(1).upper()
        return None
    
    def _find_ticket_file(self, ticket_id: str) -> Optional[str]:
        """Find ticket file for specific ticket ID"""
        
        # Possible file locations and formats
        possible_files = [
            # In .devtools/tickets/
            self.tickets_dir / f"{ticket_id}.md",
            self.tickets_dir / f"{ticket_id}.json", 
            self.tickets_dir / f"{ticket_id}.yaml",
            self.tickets_dir / f"{ticket_id.lower()}.md",
            
            # In root/tickets/
            self.root_tickets_dir / f"{ticket_id}.md",
            self.root_tickets_dir / f"{ticket_id}.json",
            self.root_tickets_dir / f"{ticket_id}.yaml",
            self.root_tickets_dir / f"{ticket_id.lower()}.md",
            
            # Generic files that might contain this ticket
            self.tickets_dir / "ticket.md",
            self.root_tickets_dir / "ticket.md",
            Path(self.repo_path) / "ticket.md",
        ]
        
        for file_path in possible_files:
            if file_path.exists():
                # Check if file actually contains this ticket
                if self._file_contains_ticket(file_path, ticket_id):
                    return str(file_path)
        
        return None
    
    def _find_branch_specific_ticket_file(self, branch: str) -> Optional[Path]:
        """Find ticket file specific to branch name"""
        
        # Convert branch to safe filename
        safe_branch = branch.replace('/', '_').replace('-', '_')
        
        # Also try some common branch-to-ticket mappings
        branch_mappings = {
            'main': ['MAIN-001', 'main', 'master', 'production'],
            'master': ['MAIN-001', 'master', 'main', 'production'],
            'develop': ['DEV-001', 'develop', 'development'],
            'staging': ['STAGE-001', 'staging', 'stage'],
            'production': ['PROD-001', 'production', 'prod']
        }
        
        possible_names = [safe_branch, branch.split('/')[-1]]  # Original logic
        
        # Add mapped names for common branches
        if branch.lower() in branch_mappings:
            possible_names.extend(branch_mappings[branch.lower()])
        
        possible_files = []
        for name in possible_names:
            possible_files.extend([
                self.tickets_dir / f"{name}.md",
                self.tickets_dir / f"{name}.json",
                self.tickets_dir / f"{name}.yaml",
                self.root_tickets_dir / f"{name}.md",
                self.root_tickets_dir / f"{name}.json",
                self.root_tickets_dir / f"{name}.yaml",
            ])
        
        for file_path in possible_files:
            if file_path.exists():
                return file_path
        
        return None
    
    def _extract_ticket_from_commits(self, branch: str) -> Optional[str]:
        """Extract ticket ID from recent commit messages"""
        try:
            import subprocess
            
            # Get recent commits for this branch
            cmd = ["git", "log", "--oneline", "-10", branch]
            result = subprocess.run(
                cmd, 
                cwd=self.repo_path,
                capture_output=True, 
                text=True
            )
            
            if result.returncode == 0:
                commit_messages = result.stdout
                
                # Search for ticket patterns in commit messages
                for pattern in self.ticket_patterns:
                    matches = re.findall(pattern, commit_messages, re.IGNORECASE)
                    if matches:
                        return matches[0].upper()  # Return first match
            
        except Exception as e:
            print(f"⚠️  Could not search commits for {branch}: {e}")
        
        return None
    
    def _find_generic_ticket_file(self) -> Optional[Path]:
        """Find generic ticket file that could be used as fallback"""
        
        generic_files = [
            self.tickets_dir / "ticket.md",
            self.root_tickets_dir / "ticket.md", 
            Path(self.repo_path) / "ticket.md",
            self.tickets_dir / "default.md",
            self.root_tickets_dir / "default.md",
        ]
        
        for file_path in generic_files:
            if file_path.exists():
                return file_path
        
        return None
    
    def _file_contains_ticket(self, file_path: Path, ticket_id: str) -> bool:
        """Check if file contains specific ticket ID"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Look for ticket ID in content
                return ticket_id.lower() in content.lower()
        except:
            return False
    
    def _extract_ticket_from_file(self, file_path: Path) -> Optional[str]:
        """Extract ticket ID from ticket file content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to find ticket ID in various formats
            for pattern in self.ticket_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1).upper()
            
            # Look for YAML frontmatter ticket_id
            if content.startswith('---'):
                try:
                    import yaml
                    parts = content.split('---', 2)
                    if len(parts) >= 3:
                        frontmatter = yaml.safe_load(parts[1])
                        if isinstance(frontmatter, dict):
                            ticket_id = frontmatter.get('ticket_id')
                            if ticket_id:
                                return str(ticket_id).upper()
                except:
                    pass
            
            # Look for JSON ticket_id field
            try:
                data = json.loads(content)
                if isinstance(data, dict) and 'ticket_id' in data:
                    return str(data['ticket_id']).upper()
            except:
                pass
                
        except Exception as e:
            print(f"⚠️  Could not read {file_path}: {e}")
        
        return None
    
    def create_sample_tickets(self, mappings: List[TicketMapping]) -> List[str]:
        """Create sample ticket files for branches without tickets"""
        
        created_files = []
        
        for mapping in mappings:
            if not mapping.ticket_id or not mapping.ticket_file:
                # Create sample ticket file
                ticket_file = self._create_sample_ticket_for_branch(mapping.branch)
                if ticket_file:
                    created_files.append(ticket_file)
                    print(f"📝 Created sample ticket: {ticket_file}")
        
        return created_files
    
    def _create_sample_ticket_for_branch(self, branch: str) -> Optional[str]:
        """Create a sample ticket file for a branch"""
        
        # Extract possible ticket ID from branch
        ticket_id = self._extract_ticket_from_branch(branch) or "UNKNOWN-001"
        
        # Create safe filename
        safe_branch = branch.replace('/', '_').replace('-', '_')
        ticket_file = self.tickets_dir / f"{ticket_id}.md"
        
        # Sample ticket content
        sample_content = f"""---
ticket_id: {ticket_id}
summary: Auto-generated ticket for {branch}
issue_type: Story
status: In Progress
priority: Medium
assignee: Developer
components: ["general"]
labels: ["auto-generated"]
acceptance_criteria:
  - "Implementation should be working correctly"
  - "Code should follow coding standards"
  - "Tests should be included"
  - "Documentation should be updated"
---

# Auto-Generated Ticket for {branch}

This ticket was automatically generated for branch: `{branch}`

## Description

Please update this ticket with:
1. Actual business requirements
2. Detailed acceptance criteria
3. Technical specifications
4. Definition of done

## Acceptance Criteria

1. Implementation should be working correctly
2. Code should follow coding standards  
3. Tests should be included
4. Documentation should be updated

## Technical Notes

- Branch: `{branch}`
- Auto-generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**⚠️ IMPORTANT: Please update this auto-generated ticket with real requirements!**
"""
        
        try:
            with open(ticket_file, 'w', encoding='utf-8') as f:
                f.write(sample_content)
            return str(ticket_file)
        except Exception as e:
            print(f"❌ Could not create sample ticket: {e}")
            return None
    
    def generate_mapping_report(self, mappings: List[TicketMapping]) -> str:
        """Generate a report of all ticket mappings"""
        
        report_path = "ticket_mapping_report.md"
        
        total = len(mappings)
        with_tickets = len([m for m in mappings if m.ticket_id])
        success_rate = (with_tickets / total * 100) if total > 0 else 0
        
        report_content = f"""# Ticket Discovery and Mapping Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Branches:** {total}
**Mapped Tickets:** {with_tickets}
**Success Rate:** {success_rate:.1f}%

---

## Mapping Results

| Branch | Ticket ID | Method | Confidence | Ticket File |
|--------|-----------|--------|------------|-------------|
"""
        
        for mapping in mappings:
            ticket_display = mapping.ticket_id or "❌ None"
            confidence_display = f"{mapping.confidence:.1f}" if mapping.confidence > 0 else "0.0"
            file_display = mapping.ticket_file or "None"
            
            report_content += f"| `{mapping.branch}` | {ticket_display} | {mapping.mapping_method} | {confidence_display} | {file_display} |\n"
        
        report_content += f"""
---

## Discovery Methods Used

1. **branch_name** (0.9): Extract ticket ID from branch name pattern
2. **branch_file** (0.8): Find branch-specific ticket file
3. **commit_message** (0.7): Search recent commit messages
4. **generic_file** (0.5): Use generic ticket.md file
5. **none** (0.0): No ticket found

---

## Recommendations

### High Confidence Mappings (≥0.8)
"""
        
        high_conf = [m for m in mappings if m.confidence >= 0.8]
        for mapping in high_conf:
            report_content += f"- ✅ `{mapping.branch}` → `{mapping.ticket_id}` ({mapping.mapping_method})\n"
        
        report_content += f"""
### Low Confidence or Missing (≤0.5)
"""
        
        low_conf = [m for m in mappings if m.confidence <= 0.5]
        for mapping in low_conf:
            if mapping.ticket_id:
                report_content += f"- ⚠️ `{mapping.branch}` → `{mapping.ticket_id}` ({mapping.mapping_method}) - Verify accuracy\n"
            else:
                report_content += f"- ❌ `{mapping.branch}` → No ticket found - Create ticket file\n"
        
        report_content += f"""
---

## Next Steps

1. **Verify Low Confidence Mappings:** Review and confirm ticket assignments
2. **Create Missing Tickets:** Add ticket files for unmapped branches  
3. **Improve Branch Naming:** Use ticket IDs in branch names (e.g., `feature/CMS20-1166-description`)
4. **Standardize Ticket Files:** Store tickets in consistent location with predictable names

---

*Generated by Advanced Ticket Discovery System*
"""
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📊 Mapping report created: {report_path}")
        except Exception as e:
            print(f"❌ Could not create mapping report: {e}")
        
        return report_path


def main():
    """CLI interface for ticket discovery"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Advanced Ticket Discovery and Mapping')
    parser.add_argument('branches', nargs='+', help='Branch names to discover tickets for')
    parser.add_argument('--repo', '-r', default='/Users/<USER>/dev/rma-mono', help='Repository path')
    parser.add_argument('--create-samples', action='store_true', help='Create sample tickets for unmapped branches')
    parser.add_argument('--report', action='store_true', help='Generate mapping report')
    
    args = parser.parse_args()
    
    # Initialize discovery system
    discovery = AdvancedTicketDiscovery(args.repo)
    
    # Discover mappings
    mappings = discovery.discover_ticket_mappings(args.branches)
    
    # Create samples if requested
    if args.create_samples:
        created = discovery.create_sample_tickets(mappings)
        if created:
            print(f"\n📝 Created {len(created)} sample ticket files")
    
    # Generate report if requested
    if args.report:
        discovery.generate_mapping_report(mappings)
    
    # Print summary
    print(f"\n📊 Summary:")
    total = len(mappings)
    mapped = len([m for m in mappings if m.ticket_id])
    print(f"   Total Branches: {total}")
    print(f"   Mapped Tickets: {mapped}")
    print(f"   Success Rate: {(mapped/total*100):.1f}%")
    
    return mappings


if __name__ == "__main__":
    main()
