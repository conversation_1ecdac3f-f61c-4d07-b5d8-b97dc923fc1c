{"permissions": {"allow": ["Bash(npm test)", "Bash(npm test:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(timeout 10 npm run dev)", "<PERSON><PERSON>(curl:*)", "Bash(npm uninstall:*)", "Bash(find:*)", "Bash(npm run dev:*)", "Bash(grep:*)", "Bash(rg:*)", "Write", "MultiEdit", "Edit", "<PERSON><PERSON>"], "deny": ["Bash(rm:*)", "Bash(rm -rf:*)", "Bash(rm -rf:*)"]}, "hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/pre_tool_use.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/post_tool_use.py"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/notification.py --notify"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/stop.py --chat"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/subagent_stop.py"}]}], "UserPromptSubmit": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run ~/.devtools/.claude/hooks/prompt_enhancer.py"}]}]}}