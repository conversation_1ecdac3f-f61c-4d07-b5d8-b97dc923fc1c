#!/usr/bin/env python3
"""
Claude Code Hook: Technical Prompt Converter
Converts user prompts into more technical, detailed prompts before execution.
"""

import json
import argparse
import sys
import re
from typing import Dict, Any
import subprocess
from pathlib import Path
import os

def convert_to_technical_prompt(prompt: str) -> str:
    """
    Convert a user prompt into a technical prompt using the specialized LLM gateway system.
    Uses the German system prompt for transformation.
    """
    
    # System prompt for prompt transformation (German)
    system_prompt = """### **System Prompt**

**Rolle:**
Du agierst als ein spezialisiertes LLM-Gateway. Deine primäre Funktion ist die Transformation von unspezifischen, von Benutzern eingegebenen Prompts in hochgradig strukturierte, technische Anweisungen. Das Zielsystem ist ein weiteres KI-LLM, das auf Code-Generierung, Software-Architektur und komplexe Problemlösungen im Bereich Software Engineering trainiert ist.

**Aufgabe:**
Analysiere den eingehenden Benutzer-Prompt, um die zugrunde liegende Absicht, die impliziten Anforderungen und die gewünschten Ergebnisse zu identifizieren. Übersetze diesen Prompt in eine detaillierte technische Spezifikation.

**Anweisungen für die Transformation:**

1.  **Dekonstruktion des Prompts:**
    *   **Identifiziere das Kernziel:** Was ist das primäre Ergebnis, das der Benutzer erwartet (z.B. Code-Snippet, Architekturentwurf, Debugging-Hilfe, API-Design)?
    *   **Extrahiere Entitäten und Constraints:** Identifiziere alle genannten Technologien, Programmiersprachen, Frameworks, Plattformen, Design-Patterns oder spezifische Einschränkungen (z.B. "muss performant sein", "soll skalierbar sein", "ohne externe Abhängigkeiten").

2.  **Anreicherung und Spezifizierung:**
    *   **Implizite Annahmen explizit machen:** Wandle vage Begriffe in konkrete technische Anforderungen um.
        *   "Schnell" -> "Definiere Latenz-Anforderungen (z.B. p99 < 100ms)."
        *   "Sicher" -> "Spezifiziere Sicherheitsaspekte (z.B. Schutz vor SQL-Injection, Verwendung von HTTPS, Authentifizierungsmechanismus wie OAuth2)."
        *   "Skalierbar" -> "Beschreibe die erwartete Last (z.B. 10.000 Anfragen pro Sekunde) und die Skalierungsstrategie (horizontal/vertikal)."
    *   **Fehlende Informationen ergänzen:** Füge Standard-Annahmen für eine robuste Implementierung hinzu, falls der Benutzer sie nicht spezifiziert hat (z.B. Fehlerbehandlung, Logging, Metriken, Testbarkeit).
    *   **Kontextualisierung:** Gib den Kontext für die Anfrage an. Handelt es sich um eine Greenfield-Implementierung, die Refaktorierung von Legacy-Code, ein Prototyp oder produktiven Code?

3.  **Strukturierung des Outputs:**
    *   Formuliere den neuen, technischen Prompt in einer klaren, strukturierten Form. Verwende Markdown mit Sektionen wie `# Ziel`, `# Anforderungen`, `# Technologie-Stack`, `# Akzeptanzkriterien`, `# Kontext`, `# Output-Format`.
    *   Definiere das erwartete Output-Format des Ziel-LLMs präzise (z.B. "vollständiger, kompilierbarer Java-Code", "mermaid.js-Diagramm für die Architektur", "YAML-Konfigurationsdatei", "JSON-Schema").

**Finale Ausgabe-Regel:**
Der Output darf **ausschließlich** den neu generierten, technischen Prompt enthalten. Jegliche Einleitungen, Erklärungen, Kommentare oder Schlussfolgerungen sind strikt zu unterlassen. Die Antwort beginnt direkt mit der ersten Zeile des technischen Prompts.

**Devtools Monorepo Kontext:**
check CLAUDE.md für mehr Kontext bezüglich der Devtools Monorepo.
"""
    
    # Skip transformation for very simple prompts (single words or short phrases)
    if len(prompt.split()) <= 3 and not any(keyword in prompt.lower() for keyword in ['create', 'make', 'add', 'build', 'implement', 'fix', 'refactor', 'optimize']):
        return prompt
    
    # For coding-related prompts, apply the transformation logic
    # This is a simplified implementation - in a full version, you'd call an LLM API
    # with the system prompt and user prompt to get the technical transformation
    
    # Basic technical enhancement based on common patterns
    enhanced_prompt = f"""{system_prompt}

**Benutzer-Prompt:** {prompt}

"""
    
    return enhanced_prompt

def main():
    """Main hook function that processes the prompt."""
    try:
        # Parse command line arguments
        parser = argparse.ArgumentParser(description="Prompt Enhancer")
        parser.add_argument("--enhance", action="store_true", help="Enhance the prompt")
        args = parser.parse_args()

        # Read input data from stdin
        input_data = json.load(sys.stdin)
        original_prompt = input_data.get("prompt", "")
        
        # Convert to technical prompt
        if args.enhance:
            technical_prompt = convert_to_technical_prompt(original_prompt)
        else:
            technical_prompt = original_prompt
        
        # If the prompt was enhanced, modify it
        if technical_prompt != original_prompt:
            output = {
                "decision": "modify",
                "prompt": technical_prompt,
                "reason": "Enhanced with technical implementation details"
            }
            print(json.dumps(output))
            cmd = [
                "claude", 
                "-p", technical_prompt,
                "--output-format", "text"
            ]
            
            print(f"🤖 Enhancing prompt...")
            print(f"📍 Arbeitsverzeichnis: {Path.cwd()}")
            
            try:
                result = subprocess.run(
                    cmd,
                    cwd=Path.cwd(),
                    capture_output=True,
                    text=True,
                    check=True,
                    timeout=120
                )
                
                print(result.stdout)
                return result.stdout.strip()
                    
            except subprocess.TimeoutExpired:
                raise Exception(f"Claude Code Timeout nach 120 Sekunden")
            except subprocess.CalledProcessError as e:
                error_msg = f"Claude Code Fehler (Exit Code: {e.returncode})"
                if e.stderr:
                    error_msg += f"\nStderr: {e.stderr}"
                if e.stdout:
                    error_msg += f"\nStdout: {e.stdout}"
                raise Exception(error_msg)
        else:
            # No modification needed, let it proceed
            output = {
                "decision": "allow"
            }
            print(json.dumps(output))
        
        sys.exit(0)
        
    except json.JSONDecodeError as e:
        print(f"JSON decode error: {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Hook error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()