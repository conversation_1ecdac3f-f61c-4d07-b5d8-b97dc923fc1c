#!/bin/bash
# <PERSON><PERSON>t to check if prompt enhancer is working

echo "🔍 Checking Prompt Enhancer Status..."
echo "================================="

# Check if log file exists
LOG_FILE="/Users/<USER>/.devtools/.claude/hooks/prompt_enhancer.log"

if [ -f "$LOG_FILE" ]; then
    echo "✅ Log file exists"
    echo ""
    echo "📋 Last 5 entries:"
    echo "-----------------"
    tail -n 30 "$LOG_FILE"
else
    echo "❌ No log file found at $LOG_FILE"
    echo "   The prompt enhancer might not have run yet."
fi

echo ""
echo "🔧 Hook Configuration:"
echo "---------------------"
grep -A 10 "UserPromptSubmit" /Users/<USER>/.devtools/.claude/settings.json

echo ""
echo "💡 To test the enhancer, run Claude Code with a prompt like:"
echo "   'create a function to calculate fibonacci'"
echo ""
echo "Then check this log again to see if it was enhanced."