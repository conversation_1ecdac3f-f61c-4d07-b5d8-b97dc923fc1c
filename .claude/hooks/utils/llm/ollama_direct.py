#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.8"
# dependencies = [
#     "requests",
#     "python-dotenv",
# ]
# ///

import os
import sys
import requests
import json
from dotenv import load_dotenv


def prompt_llm_direct_api(prompt_text, model="qwen2.5:latest"):
    """Direkte REST API Anfrage an Ollama."""
    load_dotenv()
    
    ollama_host = os.getenv("OLLAMA_HOST")
    if not ollama_host:
        ollama_host = "http://localhost:11434"
    
    try:
        # Direkte REST API Anfrage
        response = requests.post(
            f"{ollama_host}/api/generate",
            json={
                "model": model,
                "prompt": prompt_text,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "num_predict": 100
                }
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '').strip()
        else:
            print(f"HTTP Error {response.status_code}: {response.text}", file=sys.stderr)
            return None
            
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return None


def generate_completion_message(model=None):
    """
    Generate a completion message using Ollama LLM.

    Args:
        model (str): The Ollama model to use (default from env or qwen2.5:latest)

    Returns:
        str: A natural language completion message, or None if error
    """
    # Get model from env or use default
    if model is None:
        model = os.getenv("OLLAMA_MODEL", "qwen2.5:latest")
    
    engineer_name = os.getenv("ENGINEER_NAME", "").strip()

    if engineer_name:
        name_instruction = f"Sometimes (about 30% of the time) include the engineer's name '{engineer_name}' in a natural way."
        examples = f"""Examples of the style: 
- Standard: "Work complete!", "All done!", "Task finished!", "Ready for your next move!"
- Personalized: "{engineer_name}, all set!", "Ready for you, {engineer_name}!", "Complete, {engineer_name}!", "{engineer_name}, we're done!" """
    else:
        name_instruction = ""
        examples = """Examples of the style: "Work complete!", "All done!", "Task finished!", "Ready for your next move!" """

    prompt = f"""Generate a short, friendly completion message for when an AI coding assistant finishes a task. 

Requirements:
- Keep it under 10 words
- Make it positive and future focused
- Use natural, conversational language
- Focus on completion/readiness
- Do NOT include quotes, formatting, or explanations
- Return ONLY the completion message text
{name_instruction}

{examples}

Generate ONE completion message:"""

    response = prompt_llm_direct_api(prompt, model)

    # Clean up response - remove quotes and extra formatting
    if response:
        response = response.strip().strip('"').strip("'").strip()
        # Take first line if multiple lines
        response = response.split("\n")[0].strip()

    return response


def list_models():
    """List available Ollama models via REST API."""
    load_dotenv()
    
    ollama_host = os.getenv("OLLAMA_HOST")
    if not ollama_host:
        ollama_host = "http://localhost:11434"
    
    try:
        response = requests.get(f"{ollama_host}/api/tags", timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print("Available Ollama models:")
            for model in models.get('models', []):
                size_gb = model.get('size', 0) / (1024**3)
                print(f"  - {model['name']} ({size_gb:.1f}GB)")
        else:
            print(f"Error listing models: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"Error listing models: {e}", file=sys.stderr)


def main():
    """Command line interface for testing."""
    load_dotenv()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--completion":
            # Check if model specified
            model = sys.argv[2] if len(sys.argv) > 2 else None
            message = generate_completion_message(model)
            if message:
                print(message)
            else:
                print("Error generating completion message", file=sys.stderr)
                sys.exit(1)
        elif sys.argv[1] == "--list":
            list_models()
        elif sys.argv[1] == "--help":
            print("""Usage: 
  ./ollama_direct.py 'your prompt here'              # Use default model
  ./ollama_direct.py 'your prompt here' --model phi3 # Use specific model
  ./ollama_direct.py --completion                    # Generate completion message
  ./ollama_direct.py --completion qwen2.5:latest       # Use specific model for completion
  ./ollama_direct.py --list                          # List available models
  ./ollama_direct.py --help                          # Show this help

Environment variables:
  OLLAMA_HOST   - Ollama server URL (default: http://localhost:11434)
  OLLAMA_MODEL  - Default model to use (default: qwen2.5:latest)
  ENGINEER_NAME - Engineer name for personalized messages""")
        else:
            # Check if --model flag is present
            if "--model" in sys.argv:
                model_idx = sys.argv.index("--model")
                if model_idx + 1 < len(sys.argv):
                    model = sys.argv[model_idx + 1]
                    # Remove --model and model name from args
                    sys.argv.pop(model_idx)
                    sys.argv.pop(model_idx)
                else:
                    print("Error: --model requires a model name", file=sys.stderr)
                    sys.exit(1)
            else:
                model = os.getenv("OLLAMA_MODEL", "qwen2.5:latest")
            
            prompt_text = " ".join(sys.argv[1:])
            response = prompt_llm_direct_api(prompt_text, model)
            if response:
                print(response)
            else:
                print("Error calling Ollama API", file=sys.stderr)
                sys.exit(1)
    else:
        print("Usage: ./ollama_direct.py 'your prompt here' or ./ollama_direct.py --help")


if __name__ == "__main__":
    main()
