#!/usr/bin/env python3
"""
Claude Code Hook: Technical Prompt Converter
Converts user prompts into more technical, detailed prompts before execution.
"""

import json
import sys
from typing import Dict, Any
from datetime import datetime

def convert_to_technical_prompt(prompt: str) -> str:
    """
    Convert a user prompt into a technical prompt using the specialized LLM gateway system.
    """
    
    # System prompt for prompt transformation (German)
    system_prompt = """### **System Prompt**

**Rolle:**
Du agierst als ein spezialisiertes LLM-Gateway. Deine primäre Funktion ist die Transformation von unspezifischen, von Benutzern eingegebenen Prompts in hochgradig strukturierte, technische Anweisungen.

**Aufgabe:**
Analysiere den eingehenden Benutzer-Prompt und übersetze ihn in eine detaillierte technische Spezifikation.

**Anweisungen für die Transformation:**

1. **Dekonstruktion des Prompts:**
   - Identifiziere das Kernziel
   - Extrahiere Entitäten und Constraints

2. **Anreicherung und Spezifizierung:**
   - Implizite Annahmen explizit machen
   - Fehlende Informationen ergänzen
   - Kontextualisierung

3. **Strukturierung des Outputs:**
   - Verwende Markdown mit klaren Sektionen
   - Definiere das erwartete Output-Format präzise

**Devtools Monorepo Kontext:**
check CLAUDE.md für mehr Kontext bezüglich der Devtools Monorepo.
"""
    
    # Skip transformation only for very simple prompts (1-2 words) without action verbs
    if len(prompt.split()) <= 2 and not any(keyword in prompt.lower() for keyword in 
        ['create', 'make', 'add', 'build', 'implement', 'fix', 'refactor', 'optimize', 'help', 'show', 'list']):
        return prompt
    
    # Create enhanced prompt with technical context
    enhanced_prompt = f"""🤖 [TECHNISCH ERWEITERTER PROMPT] 🤖

Transformiere den folgenden Benutzer-Prompt in eine technische Spezifikation:

**Original-Prompt:** {prompt}

**Technische Spezifikation:**

# Ziel
[Extrahiere und spezifiziere das Hauptziel]

# Anforderungen
[Liste die technischen Anforderungen]

# Technologie-Stack
[Identifiziere relevante Technologien aus dem .devtools Kontext]

# Implementierungsdetails
[Beschreibe die technische Umsetzung]

# Akzeptanzkriterien
[Definiere messbare Erfolgskriterien]

Führe nun die Aufgabe basierend auf dieser technischen Spezifikation aus."""
    
    return enhanced_prompt

def main():
    """Main hook function that processes the prompt."""
    try:
        # Read input data from stdin
        input_data = json.load(sys.stdin)
        original_prompt = input_data.get("prompt", "")
        
        # Convert to technical prompt
        technical_prompt = convert_to_technical_prompt(original_prompt)
        
        # Log to file for debugging
        with open("/Users/<USER>/.devtools/.claude/hooks/prompt_enhancer.log", "a") as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write(f"Original: {original_prompt[:100]}...\n")
            f.write(f"Enhanced: {technical_prompt[:100]}...\n")
            f.write(f"Modified: {technical_prompt != original_prompt}\n")
        
        # Prepare output based on whether prompt was modified
        if technical_prompt != original_prompt:
            output = {
                "decision": "modify",
                "prompt": technical_prompt,
                "reason": "Enhanced with technical implementation details"
            }
        else:
            output = {
                "decision": "allow"
            }
        
        # Print only the JSON output
        print(json.dumps(output))
        
    except json.JSONDecodeError as e:
        # In case of error, allow the prompt to proceed unchanged
        output = {"decision": "allow"}
        print(json.dumps(output))
    except Exception as e:
        # In case of any error, allow the prompt to proceed unchanged
        output = {"decision": "allow"}
        print(json.dumps(output))

if __name__ == "__main__":
    main()