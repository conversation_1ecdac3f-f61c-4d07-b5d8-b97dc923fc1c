{"prompt_mappings": {"comprehensive": "comprehensive_review.md", "comprehensive_with_ac": "comprehensive_review.md", "ac_only": "ac_focused_review.md", "ac_focused": "ac_focused_review.md", "bug_analysis": "bug_analysis_review.md", "code_quality": "bug_analysis_review.md", "phase3_summary": "phase3_tutorial.md", "system_prompt": "system_prompt.md"}, "variables": {"language": "de", "output_format": "markdown", "include_examples": true, "company_name": "Regionalmedien Austria", "monorepo_name": "rma-mono", "primary_technologies": ["Node.js", "TypeScript", "React", "Express", "Directus"]}, "prompt_settings": {"max_length": 50000, "include_metadata": true, "variable_substitution": true, "append_instructions": true}, "review_type_config": {"comprehensive": {"description": "Vollständige Review mit AC-Analyse und Code-Qualität", "estimated_duration_minutes": 15, "phases": ["acceptance_criteria", "code_quality", "security"], "priority": "high"}, "comprehensive_with_ac": {"description": "Umfassende Review inklusive detaillierte AC-Compliance", "estimated_duration_minutes": 20, "phases": ["acceptance_criteria", "code_quality", "security", "performance"], "priority": "high"}, "ac_only": {"description": "Fokussierte Acceptance Criteria Compliance Review", "estimated_duration_minutes": 8, "phases": ["acceptance_criteria"], "priority": "medium"}, "ac_focused": {"description": "Detaillierte AC-Analyse mit Business-Kontext", "estimated_duration_minutes": 12, "phases": ["acceptance_criteria", "business_validation"], "priority": "medium"}, "bug_analysis": {"description": "Tiefgreifende Bug Detection und Code Quality Analyse", "estimated_duration_minutes": 18, "phases": ["code_quality", "security", "performance", "maintainability"], "priority": "high"}, "code_quality": {"description": "Code-Qualität und Maintainability Fokus", "estimated_duration_minutes": 15, "phases": ["code_quality", "maintainability"], "priority": "medium"}}, "jira_integration": {"auto_extract_ticket": true, "include_ac_in_prompt": true, "include_business_context": true, "fallback_to_manual": true}, "claude_code_options": {"default_max_turns": 25, "quick_mode_max_turns": 15, "allowed_tools": ["Read", "Grep", "Glob", "LS", "<PERSON><PERSON>"], "disallowed_tools": ["Write", "Edit", "MultiEdit", "TodoWrite", "NotebookEdit"], "enable_thinking": true, "enable_streaming": true}, "output_customization": {"include_cost_info": true, "include_duration_info": true, "include_session_metadata": true, "structured_parsing": true, "markdown_formatting": true}, "phase3_config": {"auto_generate": false, "include_mermaid_diagrams": true, "include_code_examples": true, "tutorial_style": "comprehensive", "target_audience": "developers", "include_onboarding_guide": true}, "quality_settings": {"minimum_ac_compliance_rate": 80, "minimum_code_quality_score": 7, "critical_issues_threshold": 0, "security_scan_enabled": true, "performance_analysis_enabled": true}, "localization": {"primary_language": "de", "fallback_language": "en", "use_german_prompts": true, "technical_terms_language": "en", "business_terms_language": "de"}, "debugging": {"log_prompt_loading": true, "log_variable_substitution": false, "validate_prompts_on_startup": true, "enable_prompt_caching": true}}