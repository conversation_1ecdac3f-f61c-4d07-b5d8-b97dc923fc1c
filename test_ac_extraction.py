#!/usr/bin/env python3
"""
Test script to demonstrate the fixed AC extraction functionality.
Shows that it extracts existing AC instead of generating new ones.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from services.acceptance_criteria_service import AcceptanceCriteriaService

def test_ac_extraction():
    """Test AC extraction with the exact format from the user's Jira ticket"""
    ac_service = AcceptanceCriteriaService()
    
    # This is the format from the user's Jira ticket (Bild #4)
    test_description = """
Beschreibung des Copyright-Services...

Akzeptanzkriterien:
• Implementierung der statischen Regeln: Der Service filtert Bilder basierend auf dynamisch geladene Listen von Redakteurs- und Regionauten-Namen. Ein Bild darf importiert werden, wenn sein Copyright-Name exakt (unter Berücksichtigung der Reihenfolge und Groß-/Kleinschreibung) oder nach Normalisierung (Ignorieren von Reihenfolge und Groß-/Kleinschreibung) einem dieser Namen entspricht. Oder der Bigram Wert einen schwellenwert überschreitet (Beispiel: <PERSON> vs. <PERSON> Benedikt)

• Integration der dynamischen Regeln: Der Service greift auf die legal_regulations-Collection im ContentHub zu, um die Liste der erlaubten copyright_name(s) dynamisch abzurufen.

• Filterung nach dynamischen Regeln: Ein Bild darf importiert werden, wenn sein Copyright-Feld einen String enthält, der einem copyright_name aus den Published legal_regulations des ContentHubs entspricht UND für diesen Eintrag import_allowed auf true gesetzt ist. Die Prüfung sollte auf Übereinstimmung von Teil-Strings oder exakten Strings (je nach Definition im legal_regulations Feld) erfolgen.

• Regelwerk-Priorität/Kombination: Ein Bild darf importiert werden, wenn es entweder einer statischen Regel oder einer dynamischen Regel entspricht.

• Ausschluss nicht konformer Bilder: Bilder, die weder den statischen noch den dynamischen Regeln entsprechen, werden nicht weiterverarbeitet oder in BigQuery importiert.

• Fehlerbehandlung und Logging bei Regel-Verletzung: Das System protokolliert im Google Cloud Logging, wenn ein Bild aufgrund der Copyright-Regelwerke vom Import ausgeschlossen wurde, inklusive Angabe des Copyright-Namens des Bildes und der Regel, die verletzt wurde.

Implementation Details:
Weitere technische Details...
"""
    
    print("🧪 Testing AC Extraction with German Jira Ticket Format")
    print("=" * 60)
    
    # Test extraction
    result = ac_service.extract_or_generate_acceptance_criteria(
        ticket_key="TEST-123",
        summary="Copyright Service Implementation", 
        description_text=test_description,
        custom_fields={}
    )
    
    print(f"🎯 Result: {result.source.upper()} AC via {result.method}")
    print(f"📊 Count: {len(result.criteria)} acceptance criteria")
    print()
    
    if result.source == 'existing':
        print("✅ SUCCESS: Extracted existing AC (no generation needed!)")
    else:
        print("❌ FAILED: Generated AC instead of extracting existing ones")
    
    print("\n📋 Extracted Acceptance Criteria:")
    for i, ac in enumerate(result.criteria, 1):
        print(f"  {i}. {ac[:100]}...")
    
    print("\n" + "=" * 60)
    
    # Test with empty description (should generate)
    print("🧪 Testing with empty description (should generate)...")
    
    result_empty = ac_service.extract_or_generate_acceptance_criteria(
        ticket_key="TEST-456",
        summary="Simple feature without AC in description",
        description_text="Just a basic description without AC section.",
        custom_fields={}
    )
    
    print(f"🎯 Result: {result_empty.source.upper()} AC via {result_empty.method}")
    print(f"📊 Count: {len(result_empty.criteria)} acceptance criteria")
    
    if result_empty.source == 'generated':
        print("✅ SUCCESS: Generated AC when none existed")
    else:
        print("❌ FAILED: Should have generated AC")
    
    return result.source == 'existing' and result_empty.source == 'generated'

if __name__ == "__main__":
    success = test_ac_extraction()
    if success:
        print("\n🎉 ALL TESTS PASSED: AC extraction works correctly!")
        print("   - Extracts existing AC when present")
        print("   - Generates AC only when none exist")
    else:
        print("\n❌ TESTS FAILED: AC extraction needs fixing")
        sys.exit(1)