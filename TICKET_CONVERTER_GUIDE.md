# 🤖 AI-Powered Ticket Converter

## ✅ **IMPLEMENTIERT: Automatische Ticket-Text Konvertierung**

Konvertiert rohen Ticket-Text automatisch in strukturiertes Format mit intelligenter Component/Label-Erkennung aus der Codebase!

---

## 🎯 **Input → Output Transformation**

### **Input (Raw Text):**
```
Autorefresh von Luftqualität-Beiträgen 

Eine Logik überprüft täglich, in welchen Beiträgen das Spritpreis und Apotheken Widget verbaut ist. Bei diesen Beiträgen wird dann automatisch das "zuletzt aktualisiert am" auf den aktuellen Tag 6.00 Uhr aktualisiert.
Dieselbe Logik wird nun auch für die Beiträge benötigt, bei denen das Luftqualität-Widget verbaut ist.

Der ursprüngliche Gedanke war, dass die Beiträge, die in den letzten 24h (plus eventuellen puffer) aktualisiert/erstellt wurden, geladen werden und auf ein definiertes HTML Widget geprüft werden.

Vorsicht: Es kann und wird sein, dass unterschiedliche Widgets zu unterschiedlichen Zeiten upgedatet werden müssen!
```

### **Output (Structured Format):**
```yaml
---
ticket_id: CMS20-1166
summary: Autorefresh von Luftqualität-Beiträgen
issue_type: Story
status: In Progress
priority: Medium
assignee: Developer
components: ["articleupdate-service", "widgets"]
labels: ["auto-refresh", "widgets"]
acceptance_criteria:
  - "Logik überprüft täglich um 6:00 Uhr Widget-Beiträge"
  - "Automatisches Update des 'zuletzt aktualisiert am' Timestamps"
  - "HTML-Pattern basierte Widget-Erkennung"
  - "Flexible Update-Zeiten für verschiedene Widgets"
---

# Autorefresh von Luftqualität-Beiträgen

Eine Logik überprüft täglich, in welchen Beiträgen das Spritpreis und Apotheken Widget verbaut ist...

## Acceptance Criteria

1. **Daily Update**: Logik überprüft täglich um 6:00 Uhr Widget-Beiträge
2. **Timestamp Update**: Automatisches Update des 'zuletzt aktualisiert am' Timestamps
3. **Widget Detection**: HTML-Pattern basierte Widget-Erkennung
4. **Flexible Timing**: Flexible Update-Zeiten für verschiedene Widgets
```

---

## 🚀 **Verwendung**

### **1. Einfache Text-Konvertierung**
```bash
# Direkt von Text
./convert_ticket.sh "Autorefresh von Luftqualität-Beiträgen..."

# Von File
./convert_ticket.sh --file raw_ticket.txt --output CMS20-1166.md

# Mit spezifischer Ticket-ID
./convert_ticket.sh --file ticket.txt --ticket-id CMS20-1166
```

### **2. Advanced Python Interface**
```bash
# Mit Codebase-Analyse (Claude Code)
python ticket_converter.py --input ticket.txt --analyze-codebase

# Schnell ohne Analyse
python ticket_converter.py --input ticket.txt --no-analysis

# Von stdin
echo "Ticket text..." | python ticket_converter.py --stdin
```

### **3. Integration in Workflow**
```bash
# Konvertiere und erstelle direkt im tickets/ Ordner
./convert_ticket.sh --file raw.txt --output tickets/CMS20-1166.md

# Dann direkt für Multi-Review verwenden
./run_multi_review.sh "feature/CMS20-1166-air-quality" --mode full
```

---

## 🧠 **Intelligente Features**

### **1. Automatische Component-Erkennung**
Der Converter analysiert den Ticket-Text und erkennt automatisch:

**Aus "Widget" + "Article" Text:**
- ✅ `components: ["articleupdate-service", "widgets"]`

**Aus "API" + "Service" Text:**
- ✅ `components: ["api", "backend"]`

**Aus "Frontend" + "UI" Text:**
- ✅ `components: ["frontend", "ui"]`

### **2. Smart Label-Generation**
Basierend auf Keywords im Text:

**Text enthält "refresh", "update":**
- ✅ `labels: ["auto-refresh", "scheduled"]`

**Text enthält "widget", "component":**
- ✅ `labels: ["widgets", "frontend"]`

**Text enthält "daily", "cron":**
- ✅ `labels: ["scheduled", "backend"]`

### **3. Codebase-Analyse mit Claude Code**
```bash
# Aktiviere intelligente Codebase-Analyse
python ticket_converter.py --input ticket.txt --analyze-codebase

# Claude analysiert:
# 1. Repository-Struktur
# 2. Vorhandene Services/Module  
# 3. Ähnliche Code-Patterns
# 4. Schlägt passende Components vor
```

### **4. Ticket-ID Extraktion**
Automatische Erkennung von Ticket-IDs:
- `CMS20-1166` (Standard-Format)
- `ABC-123` (Simple Format)
- `PROJECT_456` (Underscore Format)

### **5. Acceptance Criteria Parsing**
Smart-Parsing von AC aus Text:
```
Input:
1. Daily check at 6AM
2. Update timestamp automatically  
3. Use HTML pattern detection

Output:
acceptance_criteria:
  - "Daily check at 6AM"
  - "Update timestamp automatically"
  - "Use HTML pattern detection"
```

---

## 📊 **Conversion Features**

### **Automatische Kategorisierung:**

**Issue Type Detection:**
- `Story`: Keywords wie "feature", "implement", "new"
- `Bug`: Keywords wie "fix", "error", "problem"  
- `Task`: Keywords wie "update", "refactor", "improve"

**Priority Detection:**
- `Critical`: "urgent", "critical", "hotfix"
- `High`: "important", "asap", "priority"
- `Medium`: "normal", "standard" (default)
- `Low`: "minor", "nice-to-have"

**Smart Title Extraction:**
- Nimmt erste Zeile als Summary
- Bereinigt Markdown-Zeichen
- Limitiert auf sinnvolle Länge

---

## 🎯 **Workflow Integration**

### **Complete Pipeline:**
```bash
# 1. Konvertiere Raw-Ticket
./convert_ticket.sh --file raw_ticket.txt --output tickets/CMS20-1166.md

# 2. Führe Multi-Review durch
./run_multi_review.sh "feature/CMS20-1166-air-quality,main" --mode full

# 3. System erkennt automatisch:
#    - feature/CMS20-1166-* → tickets/CMS20-1166.md
#    - main → tickets/MAIN-001.md
```

### **Team Workflow:**
```bash
# Product Owner erstellt Raw-Tickets
echo "Feature XYZ Implementation..." > raw_tickets/story-123.txt

# Developer konvertiert zu strukturiert
./convert_ticket.sh --file raw_tickets/story-123.txt --output tickets/STORY-123.md

# Automated Review Pipeline
./run_multi_review.sh "feature/STORY-123-implementation" --mode ac_only
```

---

## 🔧 **Configuration & Customization**

### **Component Mapping (anpassbar):**
```python
component_map = {
    'articleupdate-service': ['article', 'widget', 'update', 'refresh'],
    'content-service': ['content', 'cms', 'article'],
    'api': ['api', 'endpoint', 'service'],
    'frontend': ['ui', 'frontend', 'interface', 'user'],
    'widgets': ['widget', 'component', 'element'],
    'database': ['database', 'data', 'db', 'query']
}
```

### **Label Mapping (anpassbar):**
```python
label_map = {
    'auto-refresh': ['refresh', 'update', 'automatic'],
    'widgets': ['widget', 'component'],
    'backend': ['service', 'api', 'logic'],
    'scheduled': ['daily', 'cron', 'schedule', 'time'],
    'content': ['article', 'content', 'cms']
}
```

---

## 💡 **Best Practices**

### **Für optimale Ergebnisse:**

1. **Strukturierter Input:**
   ```
   Title (erste Zeile)
   
   Beschreibung...
   
   Hintergrund/Background...
   
   Requirements:
   1. Requirement 1
   2. Requirement 2
   ```

2. **Keywords verwenden:**
   - Technical: "service", "api", "widget", "database"
   - Domain: "article", "content", "user", "admin"
   - Process: "daily", "refresh", "update", "schedule"

3. **Ticket-ID einbetten:**
   ```
   "CMS20-1166: Autorefresh von Luftqualität..."
   ```

---

## 🎉 **Live Examples**

### **Test the System:**
```bash
# 1. Test mit Ihrem Beispiel
./convert_ticket.sh --file test_raw_ticket.txt --ticket-id CMS20-1166

# 2. Siehe das Ergebnis
cat CMS20-1166-converted.md

# 3. Verwende es direkt
cp CMS20-1166-converted.md tickets/CMS20-1166.md
./run_multi_review.sh "feature/CMS20-1166-autorefresh-von-luftqualität-" --mode ac_only
```

### **Production Usage:**
```bash
# Sprint-Tickets konvertieren
for file in raw_tickets/*.txt; do
    ticket_id=$(basename "$file" .txt)
    ./convert_ticket.sh --file "$file" --output "tickets/${ticket_id}.md"
done

# Alle Sprint-Tickets reviewen
./run_multi_review.sh "$(ls tickets/*.md | sed 's/tickets\///g' | sed 's/\.md//g' | tr '\n' ',')" --mode full
```

---

## 📈 **Benefits**

### **Für Product Owners:**
- ✅ **Schnelle Ticket-Erstellung** ohne Format-Knowledge
- ✅ **Standardisierte Struktur** automatisch generiert
- ✅ **Acceptance Criteria Parsing** aus freiem Text

### **Für Entwickler:**
- ✅ **Automatische Component-Zuordnung** basierend auf Codebase
- ✅ **Smart Label-Generation** für bessere Kategorisierung
- ✅ **Direct Integration** in Review-Workflow

### **Für Teams:**
- ✅ **Consistent Ticket Format** über alle Projekte
- ✅ **Automated Workflow** von Raw-Text zu Review
- ✅ **Reduced Manual Work** bei Ticket-Erstellung

---

**Der AI-Powered Ticket Converter ist ready! 🚀**

Jetzt können Sie **rohen Ticket-Text automatisch konvertieren** und direkt in den **Multi-Ticket Review Workflow** integrieren!
