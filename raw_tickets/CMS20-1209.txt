Implementierung der Bildrechte-Regelwerke für den Import von Hero-Bildern


Warum:

Als Entwickler möchte ich den Cloud Run Service zur Extraktion von Hero-Bild- und Beitragsmetadaten so erweitern, dass er vor dem Import der Daten in BigQuery die statischen und dynamischen Regeln aus dem legal_regulation-Datenmodell des ContentHubs anwendet, damit nur Bilder importiert werden, die den festgelegten Copyright-Bestimmungen entsprechen.

Input:

Bestehender Cloud Run Service: Der Service, der Hero-Bild- und Beitragsmetadaten aus der meinbezirk.at-API extrahiert.

Definition der zu importierenden Bildinformationen: (Uploader, Copyright, Description, URL, ID des Bildes).

Statische Regeln:

Bilder dürfen importiert werden, wenn ihr Copyright-Name einem Namen eines unserer Redakteure oder Freien Redakteure entspricht.

Bilder dürfen importiert werden, wenn ihr Copyright-Name einem Namen eines unserer Regionauten entspricht.

Bei der Namensüberprüfung soll die Reihenfolge von Vor- und Nachname sowie die Groß-/Kleinschreibung ignoriert werden. String Bigram Comparator

Dynamische Regeln:

legal_regulations-Collection im ContentHub (Directus).

Relevant sind die Felder copyright_name und Status.

Nur legal_regulations mit dem Status: Published sollen berücksichtigt werden.

import_allowed: Boolean (true/false), der angibt, ob Bilder mit diesem Copyright importiert werden dürfen.

Zu prüfendes Feld: Das Copyright-Feld des aus der meinbezirk.at-API extrahierten Bildobjekts.

Akzeptanzkriterien:

Implementierung der statischen Regeln: Der Service filtert Bilder basierend auf dynamisch geladene Listen von Redakteurs- und Regionauten-Namen. Ein Bild darf importiert werden, wenn sein Copyright-Name exakt (unter Berücksichtigung der Reihenfolge und Groß-/Kleinschreibung) oder nach Normalisierung (Ignorieren von Reihenfolge und Groß-/Kleinschreibung) einem dieser Namen entspricht.
Oder der Bigram Wert einen schwellenwert überschreitet (Beispiel: Maria J Benedikt vs. Maria Jelenko Benedikt)

Integration der dynamischen Regeln: Der Service greift auf die legal_regulations-Collection im ContentHub zu, um die Liste der erlaubten copyright_name(s) dynamisch abzurufen.

Filterung nach dynamischen Regeln: Ein Bild darf importiert werden, wenn sein Copyright-Feld einen String enthält, der einem copyright_name aus den Published legal_regulations des ContentHubs entspricht UND für diesen Eintrag import_allowed auf true gesetzt ist. Die Prüfung sollte auf Übereinstimmung von Teil-Strings oder exakten Strings (je nach Definition im legal_regulations Feld) erfolgen.

Regelwerk-Priorität/Kombination: Ein Bild darf importiert werden, wenn es entweder einer statischen Regel oder einer dynamischen Regel entspricht.

Ausschluss nicht konformer Bilder: Bilder, die weder den statischen noch den dynamischen Regeln entsprechen, werden nicht weiterverarbeitet oder in BigQuery importiert.

Fehlerbehandlung und Logging bei Regel-Verletzung: Das System protokolliert im Google Cloud Logging, wenn ein Bild aufgrund der Copyright-Regelwerke vom Import ausgeschlossen wurde, inklusive Angabe des Copyright-Namens des Bildes und der Regel, die verletzt wurde.

