<Übergeordneter Task>
# Automatisiertes Content-Recycling für bestehende Beiträge

## Warum:

Als Redaktionsteam möchte ich bestehende Beiträge identifizieren und nach definierten Kriterien erneut veröffentlichen, damit relevante Inhalte mehrfach genutzt und die Sichtbarkeit populärer Beiträge erhöht werden kann. Dieser manuelle Task soll durch eine Automatisierung ein Zeitgewinn für den Redakteure bringen.

## Beschreibung:

Zur Optimierung der Content-Nutzung soll ein **automatisiertes Content-Recycling-System** entwickelt werden. Dieses System identifiziert bestehende Beiträge basierend auf definierten Kriterien (z. B. Views, Likes, Kommentare, Keywords) und veröffentlicht sie erneut als Sammelbeiträge in den passenden Regionen und unter einem festgelegten Nutzerprofil.

Ein eigener **recycling-service** wird stündlich durch den Google Cloud Scheduler ausgeführt. Dieser fragt Directus ab, um geplante Veröffentlichungen zu identifizieren, sucht relevante Beiträge über die peiq-API und erstellt neue Beiträge basierend auf den Ergebnissen.

Dazu müssen folgende technische Komponenten umgesetzt werden:

1. **Datenmodell in Directus:** Speichert die relevanten Kriterien für das Content-Recycling.
    
2. **Erweiterung des peiq-api-wrappers:** Ermöglicht die gezielte Suche nach bestehenden Beiträgen basierend auf den Directus-Filtern.
    
3. **Beitragserstellung über die peiq-API:** Generiert neue Sammelbeiträge gemäß den vordefinierten Parametern.
    
4. **Implementierung des recycling-service:** Automatisiert den gesamten Ablauf und stellt die regelmäßige Ausführung sicher.
    

## Acceptance Criterias:

- Ein Datenmodell für das Content-Recycling ist in Directus vorhanden.
    
- Der peiq-api-wrapper kann Beiträge basierend auf Views, Likes, Kommentaren, Keywords und Zeitraum filtern.
    
- Neue Sammelbeiträge können über die peiq-API automatisch erstellt werden.
    
- Der content-recycling-service läuft zuverlässig, verarbeitet passende Directus-Einträge und loggt seine Aktivitäten.
- 
</Übergeordneter Task>

<Untertask 1>  
# Implementierung des recycling-service

## Warum:

Als Entwickler möchte ich einen Service erstellen, der regelmäßig Beiträge aus Directus liest und entsprechend den Kriterien automatisch neue Beiträge erstellt.

## Input:

- Der Service wird als Hintergrundprozess in der Google Cloud via Scheduler getriggert
    
- Liest Daten aus Directus und führt eine Suche über den peiq-api-wrapper durch
    
- Erstellt einen neuen Beitrag über die API, wenn der Zeitpunkt erreicht ist
    
- Logging und Monitoring für Fehlerfälle
    

## Acceptance Criterias:

- **Vorbereitung: 2 Endpoints zum vorab testen**
    
    - 1 Testen mit mockdaten zum schreiben/erstellen eines beitrages in **DRAFT** mode
        
    - 2 Testen mit mockdaten zum suchen/filtern der Beiträge über peiqAPI
        
- Der Service läuft stündlich und verarbeitet passende Directus-Einträge
    
    - Fetched die Daten und prüft ob einer der Einträge im Zeitfenster liegt
        
    - Mögliche Zeitfenster sind: Täglich mit Uhrzeit, Wöchentlich mit Tag und Uhrzeit, Monatlich mit erster/letzter Tag und Uhrzeit
        
- Erfolgreiche Erstellung neuer Beiträge wird protokolliert
    
    - zurückspielen in Directus (Liste an URLs eigenes Datenmodell- Form muss noch überlegt werden)
        
- Fehlerhafte Einträge werden geloggt und übersprungen, ohne den Prozess zu unterbrechen
    
- Der Service ist skalierbar und performant
    
- Endpoint der von Directus aus getriggert werden kann um eine Definierte Config erneut laufen zu lassen (Flow button muss eingeschränkt werden auf Admin/Poweruser)
    
- Wenn “zu wenige” Beiträge vorhanden sind werden die gefundenen Beiträge gepostet
    
    - Die Überschriften lauten ja “Die Top Beiträge …” und sind nicht auf eine fixe Zahl definiert
        
- Heroimage vom Beitrag:
    
    - Das Bild des Overall Top Beitrages wird das Heroimage
    

## Technische Kriterien:

- *Terragrunt*
    
    - Neuen ServiceAccoutn erstellen
        
    - Cloud Scheduler konfigurieren (stündlicher Trigger)
        
- Deploy Pipeline einrichten 
</Untertask 1>



<Untertask 2>
# Erweiterung des peiq-api-wrappers zur Beitragserstellung

## Warum:

Als Entwickler möchte ich den peiq-api-wrapper erweitern, damit der Content-Recycling-Service neue Beiträge basierend auf den Suchergebnissen erstellen kann.

## Input:

- Der neue Beitrag muss:
    
    - Die Pflichtfelder der API enthalten (noch zu definieren)
        
    - Alle Metadaten lt. Directus sind gesetzt
        
        - keywords/tags
            
    - Unter dem im ContentHub definierten User veröffentlicht werden
        
    - In der im ContentHub definierten Region veröffentlicht werden
        
    - In der im ContentHub definierten Rubrik veröffentlicht werden
        
- Die Struktur wird noch vorgegeben enthält aber nur H2 und verwandte Beiträge
    

## Acceptance Criteria:

- Der peiq-api-wrapper erlaubt das Anlegen neuer Beiträge
    
- Beiträge enthalten alle notwendigen Pflichtfelder
    
- Der User und die Region können einfach übergeben werden (PEIQ arbeitet mit ID’s für Regionen)
    
- Es können Bilder (URL’s) direkt beim erstellen des Beitrages angegeben werden
    
    - zu den Bildern gehören auch entsprechende Metadaten (Alt Text, Uploader, Copyright) als min. Anforderung
        
    - Das Bild des Overall Top Beitrages wird das Heroimage
        
- Standardwerte für nicht definierte Felder setzen (Information lt. PEIQ API)
    
- Fehlerhafte API-Responses abfangen und verwertbare Fehlermeldungen zurückgeben
    
- **Manuelle und automatisierte Tests für Beitragserstellung durchführen**
    
    - Testpostings über die API erstellen
        
    - Validierung der erstellten Beiträge über das System
</Untertask 2>

<Untertask 3>
# Erweiterung Datenmodell ContentHub

## Input

Das Ziel ist es, das Datenmodell in directus/ContentHub zu erweitern. Es soll erfasst werden, welche Beiträge über den peiq-api-wrapper angelegt wurden.

## Akzeptanzkriterien

- Das Datenmodell muss um die Möglichkeit erweitert werden, Beiträge in Rubriken zu veröffentlichen.
    
- Schlüsselwörter müssen hinzugefügt werden, die Platzhalter erlauben
    
- Es muss ein Datenmodell angelegt und mit dem beitragssammler verknüpft werden, welches die neuen Beiträge abbildet. Folgende Elemente müssen enthalten sein:
    
    - URL zum erstellten Beitrag
        
    - Datum der erstellung (durch dys default feld von directus zu managen)
        
    - der Bezirk unter dem es veröffentlcih wurdwe (als Hilfe für eien bessere Filterung)
        
- Scheduler in Directus der alte Datensätze löscht (älter als 6 Monate)
</Untertask 3>


<Meeting Besprechung>
# content_recycling

## Speakers

- **Speaker 1**
- **Speaker 2**
- **Speaker 3**

## Transcription

### [0:00] Mario (Team Lead)

Der Service geht jetzt dann eben her, holt sich aus **Directus die Daten** raus, **liest sie durch**, geht dann an den **zuvorgeschriebenen PEIQ-Api-Wrapper** und holt sich die Beiträge, **kriegt sie kopiert raus** und schreibt sie dann über Bike Api Wrapper wieder rein. Das heißt, der Service wird als Hintergrundprozess in der Google Cloud via Scheduler getriggert, liest Daten aus Directus und führt eine Suche über Peiq Api Wrapper durch. Er erstellt neue Beiträge über die API, **wenn der Zeitpunkt erreicht ist**. Logging und Monitoring für Fehlerfelder. Eh klar, das haben wir von Haus aus implementiert. Das heißt, **der Service läuft stündlich** und verarbeitet passende Directus Einträge, fetch die Daten und prüft, ah ob einer der Einträge im Zeitfenster liegt. Mögliche Zeitfenster sind täglich mit Uhrzeit, wöchentlich mit Tag und Uhrzeit, monatlich mit erster oder letzter und Tag und Uhrzeit. Erfolgreiche Erstellung neuer Beiträge **wird protokolliert**. Das würde ich gerne noch besprechen mit euch, ob wir dann **die Beiträge**, die was der PEIQ Api Wrapper anlegt, ob wir die dann eben **wieder zurückschreiben** sollen **in Directus**, damit man eine zentrale Stelle hat, damit man sofort sieht, was angelegt worden ist.


### [1:20] Christian (Lead Dev)

Das habe ich mir schon gedacht, als Feature, das wollte ich sogar anbringen, im Refinement. **Also ja**, würde ich sagen, weil das ist dann **fürs Debugging** für uns leichter zu wissen, was erstellt wurde und ich dem man für die Enduser dann auch, um zu wissen, ob es ein : Ja. Ansonsten müssen wir hunderte Beiträge durchsuchen auf meinem CKT, irgendwas zu finden.


### [1:44] Mario

Ja, Form muss noch ah überlegt werden, gell?


### [1:49] Christian

Ja Ansonsten einfach eine Liste an YLs, die ja TimeStamp sortiert sind, oder?


### [1:56] Mario

Ja, es soll halt, ja, müssten wir eben, wir brauchen einen Timestamp dazu. Müssen wir schauen. Aber ob wir es jetzt so oder so reinschreiben, ich glaube, dann nicht so tragisch eben.


### [2:07] Christian

Oder?


### [2:07] Mario

Ja. Fehlerhafte Einträge werden geloggt und übersprungen, ohne den Prozess zu unterbrechen. Also es kann wirklich sein, dass entweder kommen wir drauf, dass das Datenmodell wurde geändert und der Code wird angepasst oder wir haben Pflichtfelder vergessen, dass wirklich eine User-Eingabe zum Fehler führt oder der PEIQ-Api-Wrapper beim Lesen zu einem Fehler führt oder eben auch das Schreiben zum Fehler führt. Mhm Ah Das soll es sollen, aber nicht brechen.


### [2:33] Christian

Sollen wir dann Also das Erste ist klar, quasi wenn die User-Daten fehlerhaft sind, dann kann es nie funktionieren, bis die Daten richtig sind. Das heißt, es sollte übersprungen werden. Ähm Sollen wir dann – wie würden wir confirment, dass es passiert ist oder nicht passiert ist? Oder ist das egal? Also wenn es beim Lesen oder Schreiben Fehler wirft, dann catchen wir es, wir springen einmal und will beim nächsten Intervall noch mal probieren? Oder Wenn jetzt der monatliche Post quasi verloren geht, dann – das wäre jetzt mein Gedanke: Wenn es täglich jetzt geht, dann wird ein Tag übersprungen vielleicht, aber wenn es jetzt der monatlich ist, dann wird es einen Monat lang nicht gepostet und eigentlich bräuchte ich ein System, irgendwie ein quasi zu wissen, ob der letzte Run funktioniert hat oder nicht, oder?


### [3:34] Mario

Ja, aber was machst du dann? Dann postest zwei Tage später unter Umständen, wenn irgendwas gefixt ist, oder wie? Nein, also quasi wenn die Config falsch ist, okay, whatever, aber jetzt eher, wenn Pike gerade in diesen fünfzehn Minuten irgendetwas hat, zum Teil erstellen, dann könnten wir auch beim nächsten Run zum Scheduler checken, hey, wo das erstellt, zum richtigen Zeitpunkt. Ähm Und das können wir theoretisch machen mit diesem Lock halt, oder? Wenn wir einen Time step haben, dann ist es... Ah, okay, das ist schon sehr komplex.


### [4:03] Christian

Ja.


### [4:04] Mario

Ich wüsste gerade nicht, wie ich es lösen müsste, ad hoc.


### [4:07] Christian

Nein, mir gehen mal von einer schönen heilen Welt aus und es läuft. Okay, passt super.


### [4:13] Mario

Und worst case ah ist es halt in diesem Monat nicht erstellt. Wir starten mal so rein.


### [4:18] Christian

Habe ich gerade im Nachhinein einfach einen manuellen Button gegeben, oder? Mit so Run now oder so was, verstelle jetzt. Weißt du, ob das hier eine Notwendigkeit ist, oder?


### [4:29] Mario

Ja, aber das ist ein super Punkt. Das ist die einfachste Lösung überhaupt. Du hast recht. **Endpoint, der von Directors aus getriggert werden kann, um eine definierte Config erneut laufen zu lassen**. Wobei, ist natürlich schwierig, wenn man jetzt sagt, ich habe jetzt mir eine Konfiguration entdeckt, wo ich hundertsiebenundzwanzig Bezirke angehackt habe gell und bei einem Bezirk hat es nicht funktioniert.


### [5:07] Christian

Ich habe mir auch gerade gedacht, es hat ähnliche Probleme wie vorher, weil ich will jetzt nicht unbedingt, dass ein User dreimal draufdrücken kann und dass er drei Beiträge erstellt.


### [5:17] Mario

Na ja, das ist dann eigentlich nicht der Admin.


### [5:19] Christian

Okay, okay, passt gut. Dann brauchen wir ihn nicht mehr. Gut.


### [5:22] Mario

Ah **Flow Button** muss eingeschränkt werden auf admin/poweruser.


### [5:36] Christian

Genau. Sobald man jetzt so was hat mit solchen heftigen Autorisierungen, die vielleicht hundert Beiträge stellen sollten pro Tag, wird es sehr schwierig, da irgendwie gute Systeme zu bauen, weil im Vergleich zum Voting, okay, das Voting fehlt, dann tue ich es einmal. Da kann halt echt viel schiefgehen, wenn wir einen Fehler einbauen, dann haben wir halt echte Beiträge, die fehlerhaft sind, die wir löschen müssen, oder? Also dann dafür wäre das Tracking super wichtig, die von den Links, damit wir im Nachhinein sagen können, da ist es schief gegangen. Wir löschen die letzten 30 Minuten von Beatringen. Ja, aber dann spricht es gegen eine Linkliste, weil dann muss es eine sauber verlinkte Entity sein. Inwiefern? Du sagst, wenn ich zum Konflikt einen Link gehe und einen Time state dabei habe, dann weiß ich genau, wenn ein Fehler in den letzten zwei Stunden war, kann ich in den letzten zwei Stunden einen Beitrag löschen.


### [6:34] Mario

Ja, das musst ein **strukturiertes JSON** sein, was dort ablegt eben, weil wie gesagt, wir haben eine Config angelegt für 127 Bezirke. Dann muss in diesem Log-File drin sein, diese URL mit diesem Bezirk und zu der Uhrzeit.


### [6:51] Christian

Das wäre schon gut zu haben, muss ich sagen.


### [6:53] Mario

Die frage ich nur, ob wir ein manuell **strukturiertes JSON** dort anlegen oder ob wir uns eine Entity anlegen, die was dann dazu verlinkt ist zu dieser Konfiguration.


### [7:03] Christian

Ich Also ich habe jetzt intuitiv nicht viel darüber nachgedacht, also ich habe nicht drei, vier Uhr nachgedacht, aber ich intuitiv glaube ich, eher Data Model legen mit Entity, weil es wird irgendwas schiefgehen. Also nicht jetzt, nicht in einem Monat, aber vielleicht in einem Jahr wird 100% irgendwas schiefgehen und je cleaner unsere Daten abgelegt sind und je leichter wir es nachvollziehen können, desto besser.


### [7:23] Mario

Also nicht in ein Feld rein, sondern in ein Datenmodell rein.


### [7:28] Christian

Genau, und dann quasi Anzeigen der Liste, wie wir es denn tun z. B.


### [7:33] Mario

Und wir können dann So: Scheduler in Directus, der alte Datensätze löscht. Passt das ist dann eine separate, das logge ich dann noch aus in einen eigenen Subtask eben, weil jetzt ist dann wieder komplett losgewürzt von der Story. Habe ich jetzt nur als einen Side Note reingeschrieben, schmeiße ich ihn gleich wieder raus. Prinzipiell klar. Die Story ist sonst so weit?


### [8:15] Marius

Ja, ich sehe es auch, dass da mehrere Fehler passieren könnten eigentlich. Da muss das wirklich sehr robust aufgepasst werden. Weil es können Zeitplanungsfehler passiert sein, es können Beitragserstellungsfehler passieren, also bei mir erstellen neue Beiträge über die API, duplizierte Beiträge, keine Ahnung, unvollständige Daten, Infrastruktur, zum Beispiel mit Timeouts bei längeren Wartungszeiten. Das können mehrere Sachen sein.


### [8:41] Christian

Ja.


### [8:41] Marius

Da muss... Kannst du noch einen extra Punkt hinzufügen mit: „Der ganze Flow ist super dokumentiert und mit allen Fehlermeldungen als Acceptors gefühlt, weil wir brauchen dafür eine Doku. Wir müssen da zuerst einmal die Tests bauen und dann wieder mit dem tatsächlichen Code.


### [9:01] Christian

Ich glaube, wir müssen mal zusammensetzen, wir haben ein paar Stunden und nachher wissen, dass es gut geht. Ja, weil da sind mehrere Sachen, auch Datenintegration.


### [9:09] Mario

Allein schon, ihr habt ja die Konfigurationsebene gesehen, was möglich ist. Allein schon, diese Konfigurationsebene aufzulösen, wie kann ich es dann über eine Schleife abarbeiten? Also technische Dokumentation ist vorhanden und Dokumentation für User im Content Hub ist vorhanden. Der Punkt gehört jetzt nicht zu der Story dazu, der gehört zur Parent Story eben oder als eigenen SubTask nur. Das kriegt dann eh ziemlich sicher die Elisabeth dann noch rübergespielt. Das muss sie ein bisschen aufsplitten. Aber ja, Dokumentation hat sie errechnet. Muss auf jeden Fall rein Prinzipiell ist ja nichts Schwieriges dran.


### [9:55] Christian

Es sind einige leichte Steps bis mittelschwere Steps und dann halt ganz viel zusammen und ganz viele Konsequenzen dementsprechend.


### [10:03] Marius

Wir müssen einfach viel Monitoring machen.


### [10:08] Christian

Und mit Code Reviews und testen und belegen und Fehlerhandling. Ich würde dem auch eine Acht geben.


### [10:13] Marius

Acht, ja. Einfach weil... Echt? Mit Bauchschmerzen und Beobachtung.


### [10:20] Mario

Ich wäre jetzt eher bei 13 gewesen.


### [10:22] Marius

Ja, es ist viel zum Beobachten und zum Schauen und Fehler wahrscheinlich Fehler fixen immer im Nachhinein.


### [10:29] Mario

Es ist nicht wegen „Es ist wahnsinnig schwierig, sondern die Komplexität in sich geben, weil es eben so verzahnt ist. Nehmen wir 10.


### [10:41] Christian

Ich bin auch für der 13. Wir haben keine 10, es gibt nur vier Abschlusszahlen. Ich habe jetzt was gedacht, einfach weil Part eins und zwei quasi schon gecovert ist durch die anderen Subtasks, aber ich bin auch keine 13. Ja, stimmt schon. Die Komplexität ist schon hoch.


### [10:58] Mario

Und du musst jetzt Error Handling von unseren eigenen Services dann wieder verbauen.


### [11:03] Christian

Das ist schon. Am liebsten hätte ich gerne, allein die Doktoration würde ich gerne ein bisschen aufblasen, dass wir vielleicht auch den Flow Chart machen dafür, weil es ist wirklich ein bisschen risky. Okay, dann nehmen wir 13. Nehmen wir 13, ja.

</Meeting Besprechung>


---


<Meeting Besprechung 2 - Refinement>

[0:00] Christian (Lead Dev): 
Dann kurz rauszoomen und unterm Recycling Service. wir sind also auf einen Punkt wieder draufgekommen. wir dürfen ja nicht quasi einen Artikel posten, wenn wir nicht den recycled Article in Directus anlegen können, weil das ist unsere Referenz zu, ob es einen Post schon gab in dieser Wahl und wenn das breaken würde for some reason, any reason, machen wir jede Iteration noch ein Beitrag, weil quasi keiner, gefunden werden kann in der Referenz.

[0:31] Max: Nicht geloggt wird wahrscheinlich.

[0:32] Christian: Ja, nicht geloggt, dementsprechend haben wir keine Referenz dazu, dass es schon einen gab. Das heißt nächste Stunde wird's wieder normalgestellt wahrscheinlich. Du weißt, was ich meine?

[0:41] Mario: Na, weiß ich nicht, weil wir ja gesagt haben, wenn einer nicht durchläuft, dann geht halt an diesem Tag keiner raus.

[0:48] Christian: Nein, nein, genau. Also stell dir vor, Beitragsstellung funktioniert, aber in Directus dann zurückschreiben aus irgendeinem Grund den Recycled Article, also quasi den Actual, die Entity Directus, wo wir die Referenz speichern, den Log- -dass erstellt wurde. Und wir müssten ja auf diese Daten gehen, um zu schauen quasi-

[1:08] Max: Wurde heute schon was gepostet?

[1:09] Christian: Wurde heute schon was gepostet? Nichts?

[1:14] Mario: Na, weil es ist ja eingestellt, ich stelle bei einem Beitragssammler ein, dass, um 18:00 Uhr, soll ein Post rausgehen. Dann springt eure Logik- -in der Stunde an und dann schaut sie nach, aha, ja, ich bin jetzt zwischen 18:00 - 19:00 Uhr, ich muss diesen Beitragssammler hernehmen, i sammle alles und erstelle einen Beitrag. Ah, hoppla, blöderweise hat das Schreiben im Directus nicht funktioniert. Der nächste lauf ist um neunzehn Uhr. Passt, ich hab keinen Beitragssammler mehr, also muss ich nichts schreiben.

[1:44] Christian: 
Okay, was für die Fälle, wenn wir ƒ1 stimmt soweit das haben, dann ist es wurscht. Gibt's irgendwelche, äh, äh, also wir probieren halt diese ganzen defensive Programming Sachen zu machen, damit wir ja nicht doppelte Beiträge posten oder so was in die Richtung. Ich überlege gerade, ob es irgendwelche anderen Loops Calls gibt, weil unsere Lösung wär da gewesen, dass wir sagen, wir erstellen zuerst den Recycled Article, schauen, was geht und erst dann erstellen wir den Post. Aber stimmt schon das recht, wenn wir es nur einmal zu einer Uhrzeit posten, dann kann es eigentlich schieflaufen.

[2:13] Mario: 
Worst case, wir haben keine Referenz in Directus, obwohl ein Beitrag online ist. Oder andersrum, also, andersrum ist wichtiger, dass wenn der Beitrag nicht, online gestellt werden kann, dass dann keine Referenz in Directus erstellt wird beziehungsweise nur noch Fehler erstellt wird.

[2:34] Christian: 
Ja, genau, passt, passt. Okay, passt. Das heißt, uns ist jetzt prinzipiell, also diese Strategie, dass wir zuerst den Recycled Article erstellen und dann nicht den Beitrag erstellen, falls das schiefgeht, ist sinnlos in deinen Augen?

[2:49] Mario: 
Ja, vor allem ist es dann eher andersrum gefährlich, dass wir am Direktusdatenmodell irgendwas umändern und dann kann eigentlich nur der Log-Eintrag nicht geschrieben werden kann. Und aufgrund vom fehlenden Log-Eintrag geht der ganze Beitrag nicht online. : Ja, das ist die ganze Sache- Ist sogar gefährlicher.

[3:06] Christian: 
Vor allem ist die Ganke, dass wenn's wir, wir immer wieder rechecken müssen auf einen alten Beitrag, dann, dann wär's gefährlich, weil wir dann das Datum-- aber stimmt schon, wenn wir's nicht machen müssten, sondern nach Zeitregeln gehen, dann ist es wurscht.

[3:17] Max: 
Gibt's eine Möglichkeit, dass wir zweimal die Stunde executen oder machen wir's wirklich nur einmal die Stunde?

[3:21] Christian: Das ist dann eine point of Failure, das ist einfach das Schedule. Das heißt, da müssen wir halt einfach sichergehen, dass das nicht passiert, aber ansonsten sollte es eigentlich gehen.

[3:30] Christian: Also worst case, wir machen einen Crown Job, quasi Expression falsch und dann könnte so was passieren, aber das ist der einzige Failure Point.

[3:37] Max: Das falsch Programmieren und das ist ein Base of of a Job.

[3:40] Christian: Genau, also ja , wir könnten auch falsch programmieren, du hast recht.

[3:43] Mario: Ja, aber also diesem, diesem Edge Case wü-- also das teilt mir die Komplexität dann auch nach oben eben. Wenn du wirklich sagst, immer abfragen und schauen, ist da schon einer erstellt worden? Also das würde ich komplett aus-- rauslassen.

[3:56] Christian: Ja, vor allem der Gedanke war, das mit den Recycled Articles zu machen, weil's halt dann eh schon da ist und quasi auch schon really really fetchbar ist, aber du hast recht. Also wenn wir's nicht brauchen, um noch fester sie zu haben.

[4:05] Mario: Ja, aber wie, wie fetche ich dann? Weil, jetzt blöd gesagt, ähm, wir haben einen Beitragssammler, wo alle 127 Bezirke angehakelt sind eben und dann musst du, zu allen 127 eben die Referenz prüfen eben.

[4:20] Christian: Referenz zu Recycled Article meinst du?

[4:22] Speaker 2: Ja, genau.

[4:25] Christian: Ja, ja.

[4:25] Mario: Macht's halt nur dort recht unübersichtlich. Also da hast eine riesige Überprüfungslogik drinnen eben, wo du über Maps und keine Ahnung was alles drüber gehst eben. Ähm, für, für den Fall, der was eigentlich so meiner Meinung nach nicht eintreten kann, weil wir sowieso nur einmal in der Stunde das machen.

[4:46] Christian: So, also mit, mit deinen Vorredner vorher, jetzt hab ich den ganzen Kopf gehabt, dass wir auf das gehen und nicht auf, ähm, Referenzen. Da ist es wurscht, ja.

[4:53] Mario: Ja, passt.

[4:54] Christian: Perfekt. Dann die andere Frage war, das habe ich mir kurz angedacht, ob wir irgendwie validieren sollen nach dem Erstellen, ob da etwas schiefgelaufen ist. Also noch mal den Artikel fetchen mit der ID und gegenprüfen. Ist es irgendwie interessant oder ist es eher wieder ein, wird eh alles passen in so eine Richtung?

[5:11] Mario: Es würde bedeuten, du vertraust nicht dem Response Code der PEIQ-API. Genau.

[5:17] Christian: Ähm, basically. Also ich vertraue quasi halt so, was, wenn in unserer Logik drinnen, quasi wir haben, wir beschreiben, wir geben mit, was

[5:25] Max: Entscheiden, wie die Seite läuft

[5:30] Marius: Entschuldigung. Wir können aber auch nie hundertprozentig sagen, ob PEIQ immer läuft, oder?

[5:36] Mario: Ja, es hat in den letzten zwölf Jahren hat es, ich glaub, eine Stunde, also in zwölf Jahren, im allem zusammen, es hat eine Stunde Ausfall gegeben vom Portal. ich mein, was willst du prüfen? Ich mein, wenn's nicht läuft, dann krieg ich aber auch keinen zweihunderter wieder.

[5:53] Christian: Ja. Gehen wir aber zurück. Ist ist da ein Tag richtig gesetzt worden, zum Beispiel?

[6:04] Mario: Ja, aber wo fängst du an? Wo hörst du auf? Also ich -

[6:07] Christian: Das ist die Frage, glaube ich. Also es ist sehr interessant. Ja, weil sonst müsste ich da noch überprüfen, sind auch alle Beiträge korrekt verknüpft eben? Ist das Header-Bild richtig gesetzt eben? Sind alle Text drinnen? Ah, ist kein Placeholder mehr vorhanden und ich glaube eher, dass das Teil von QA ist und ich meine, was machst du dann, wenn es so wäre? Ah, ein Rollback, also.......................... löschen. Wäre die Antwort gewesen.

[6:34] Mario: Und dann, äh, löschen oder auf Draft setzen?

[6:39] Christian: Boah, gute Frage. Also das ist-

[6:42] Mario: Wen informierst du dann? Das heißt, ah, du zeigst dann ein Error, der was dann uns triggert, gleichzeitig dann wieder einen weiteren Log-Eintrag in Directus, ah, wo dann vielleicht noch der User, ah, entsprechend informiert werden muss, dass es eben aktuell zu Problemen kommen. Also-

[6:59] Christian: Das haben wir schon, dass wir das genommen haben, oder? Also genau das, das wäre jetzt keine Extraarbeit, weil wir müssten so ein Eventlog haben und Alerts für uns. Also es ist die Frage, ob es sinnvoll ist und nicht, ob es komplex ist.

[7:09] Speaker 4: Mal schauen.

[7:12] Mario: Ich glaub's ehrlich gesagt net, weil ich mein, du fängst an, Tags zu überprüfen. Da müsst ich auch noch schauen, ah, ist SPIN-Tags richtig ausgelöst worden oder richtig ausgelöst worden? Das heißt, du überprüfst im Nachhinein eigentlich die SPIN-Tags-Library, was wir verwenden, weil du die SPIN-Tags-Library nicht vertraust eben. Gleichzeitig vertraust du dem Response Code vom PEIQ nicht eben und der API vom PEIQ nicht.

[7:35] Christian: Die Debate habe ich schon. Also wenn der Response zurückgegeben wird, die ist das mehr oder weniger passt. Das wird schon passen.

[7:41] Mario: Ja.

[7:42] Christian: Also ich würde unsere eigene Logik nicht unbedingt... Also mir wäre es bei jedem anderen Feature wurscht, da hätte ich jetzt nicht mal aufgebracht, aber du weißt, 127 Bezirke, wir machen einen Post am Tag, was, wenn irgendwas schiefläuft und wir haben dann halt ein Mess zum Fixen. Das war die Frage, wäre es irgendwie sinnvoll, gibt es irgendwas, was wir validieren können, was relevant wäre, weil wir können es ja easy machen. Die Frage ist nur, ob es sinnvoll ist.

[8:06] Marius: Ja, Titel, Titellänge zum Beispiel Min und Max kannst du validieren. Die Required Fields kannst du validieren. Die verbieten, also die negativ Words, was wir ja gestern besprochen haben.

[8:21] Max: Wenn man es an einer Stelle richtig machen, quasi dann soll es je passen. Wenn wir einmal quasi validieren beim Endpoint.

[8:29] Christian: Ja. Und vor allem Image Validation, ob überhaupt das Bild vorhanden ist.

[8:38] Mario: Ja, dann müsste ich den Beitrag wieder fetchen und schauen, ob es bildverknüpft ist.

[8:44] Christian: Prinzipiell, also, wie gesagt-

[8:46] Mario: Es sind eh zwei Schritte. Es sind ja ohnehin zwei Schritte, weil, ähm, also im PEIQ-AP-Wrapper sind es zwei Schritte, weil du musst jetzt erst einen Beitrag anlegen und danach kannst du das Bild zum Beitrag verknüpfen eben. Das heißt, wenn dort-- das heißt, du vertraust dort dem 200 vom PEIQ wieder nicht.

[9:05] Christian: Oder wir kicken nachher auf die Validierung. Ich würde es weniger ansehen mit so Vertrauen von quasi PEIQ oder sowas, sondern die Frage ist weniger so vertrauen wir unseren Systemen oder whatever, sondern: Gibt es irgendwas, was einfach zu testen wäre, was relevant zu testen wäre? Das ist jetzt eine Product-Frage, keine technische Frage.

[9:21] Mario: Das würde eher, äh-- ich kann es mit der Cornelia, also mit der Steiermark, mit den Content Managern noch mal durchspielen, eben ob sie sagen, dass irgendwas validiert werden muss. Aber er hat gesagt: „Vertraue ich mal unserer Logik so weit eben, dass wenn wir Text übergeben, dass dann auch genau dieser Text drinnen steht eben.

[9:39] Mario: Ich sehe es jetzt aber............................ kritisch.

[9:45] Marius: Ich verstehe den Mario irgendwie schon, weil, also PEIQ-API, die haben ja diese ganzen Validierungen für sich selber auch schon implementiert. Das heißt, wir würden das doppelt machen, weil sie liefern ja die Daten schon zu uns eigentlich. Das heißt, diese Validierungs-Steps haben sie ja schon implementiert. Das heißt, wenn Bike keine zweihundert gibt, dann loggen wir ein Error und fertig.

[10:11] Mario: Es wäre höchstens, dass wir inhaltlich was falsch setzen eben.

[10:18] Mario: Und da ist halt die Frage: Wie überprüfen wir den Inhalt eben? Ich meine, ah, es fehlt uns ein Leerzeichen raus. Ahhh Hoppla, Rechtschreibfehler drinnen. Ah, ich meine, wo fängst du an, wo hörst du auf? Ah, wir können-- das Einzige, was wir abfragen könnten, sind noch geschwungene Klammern drin, dass wir irgendeinen Placeholder nicht versetzt haben. Aber das würde ich eher machen, bevor es an die Bike-API geschickt wird eben.

[10:43] Christian: Auch da-- also wie gesagt, wir können jeden Step hier angehen. Also ich will keine technische Diskussion, sondern eine Product-Diskussion eigentlich um das Thema-

[10:50] Christian: -dass wir machen was, das Schaden erzeugen könnte, wenn wir es falsch machen, haben wir Safeguards, die wir einsetzen könnten, die sind gegeben, weil wir können sie machen, ohne Problem, weil wir wissen-

[10:59] Mario: Ich würde die Sachen, was du überprüfen möchtest, würde ich eher im Vorfeld überprüfen, bevor es an die PEIQ-API geschickt wird und nicht nachher eben. Weil wir, wenn wir das vorher noch mal validieren, dass keine doppelten, dass da keine Klammern mehr drinnen sind und so ein Zeug eben, dann ist für mich der API-Call safe.

[11:18] Christian: Mhm. Passt. Das heißt, wir sagen, wir überprüfen vorhin noch einen Double Check. Zählt bis in alle Daten, die wir rausgerentet haben? Ist, ist in diesem Text jetzt diese zehn Links, die in der Kopie angegeben sind? Sind die Gruppierungen richtig? Das ist auch wieder so ein PEIQ-API-Wrapper-Test eigentlich. Ja.

[11:37] Mario: Ja, aber ich meine, mit diesen Validierungen vorher könnte ich auch einen sauberen Test dafür schreiben.

[11:41] Christian: Ja, also die Tests sind schon so geschrieben, so jetzt beim Search Part, dass sie genau das testen, richtige Anzahl, richtige Gruppierungen und so weiter.

[11:49] Mario: Ja.

[11:50] Christian: Okay, passt. Also wir schließen mal ab mit: Wir machen jetzt nichts und wenn wir sehen, dass es irgendeinen Use Case gibt oder beim ersten Fehler, wo wir es hätten fixen können mit Validierung, ziehen wir es einfach nach.

[12:01] Mario: Wobei, wie gesagt, ich würde denn die Validierung, äh, machen, bevor es rübergeschickt (an PEIQ) wird.

[12:07] Christian: Wo auch immer sie dann auch davor danach, ähm, ja.

[12:10] Max: Gibts ein Save, was wir preventen können? Also quasi können wir sagen, wenn ein Placeholder in dem Feld ist, lasst den User, die Userin nicht speichern. Also können wir quasi Validationhooks haben oder irgend so was für quasi-

[12:22] Christian: Invalide Places.

[12:23] Max: Directus, genau. Also quasi, dass ich sagen kann, wenn ein Feld das und das contained, lasst die Person nicht speichern?

[12:29] Mario: Ja, wir können da noch Validation hinzufügen, ja.

[12:33] Max: Ja, das war die Frage. Also da würde ich zum Beispiel machen, weil da ist es halt noch die User oder Userin, die halt eingibt und wenn die Person einen Placeholder reingibt, muss auch die Person wissen, wie es ohne Placeholder ausschaut. Weil danach bringt es mir halt relativ wenig, da ist der Placeholder drin und ich bin so: „Ja, okay, kann sein des."

[12:47] Mario: Ja. Ja, da können wir noch eine Validation einbauen. Und-

[12:52] Max: Genau. Aber sprich uns einfach auf, was uns, was uns auffällt, gefühlt jetzt bringen, ja.

[12:56] Mario: Mhm, passt. Und ich finds besser, wenn wir es vorher abfangen eben, weil dann brauchen wir keinen Rollback. Weil einen Rollback zu machen, ist immer bissl mit, äh, bissel Hickhack verbunden eben, weil du müsstest dann ja eigentlich auch das Image wieder roll backen eben, äh, plus, denn, den Article roll backen. Ähm, puh. Also lieber, lieber vorher abfangen eben und auf Directus Seite noch eine Validation nachziehen.

[13:19] Christian: Ja.

[13:19] Mario: Wär schon ein guter Punkt, ja.

[13:21] Marius: Also Rollback komplett wegkicken.

[13:25] Mario: Yes. Ja.

[13:26] Marius: Passt. Ja. Okay.

[13:31] Christian: Genau. Ansonsten haben wir dann noch in Pseudocode durchgedacht. Es ist eh relativ simpel, aber auch nur relativ, wenn man rauszoomt, sieht man, dass es nicht so simpel ist, aber...

[13:40] Mario: Ja. Ja.

[13:44] Christian: Sind schon meine Gefühls, aber ja, ich denk, es geht schon.

[13:47] Mario: Ja, man muss einmal diese, äh, ich meine, es ist eine stupide Logik eben zum, zum Durchblättern. Ich meine, die Logik macht es ja dann komplex eben, weil, äh, ist nach A sortiert, ist nach B sortiert eben. Muss sie, äh, eine Gemeinde, muss sie drei Gemeinden nehmen? Äh, Negativkeywords berücksichtigen, positive Keywords berücksichtigen und, und, und eben. Es sind viele Parameter, aber eigentlich ist es recht stupide Filterlogik.

[14:13] Christian: Ja, da musst du nicht drüber reden, weil das war auch echt easy, dann den PEIQ Upriver zu schreiben. Es ist mehr so dieser Recycling Service, der Komplexität halt hält, weil er muss einmal die Prioritätes für das Suchen – oder Schreiben bedeutet weniger suchen vor allem – ah, gut mappen zum Directus Config und dann halt diese Doppellogik durchmachen. Es sind halt einige so Points, wo es halt sich verzweigt und, äh, Executions hat. Aber ja, ich glaube, wir haben alle am Ende gemeint, wir sind recht confident, dass wir das, ähm, recht nach Plan runterschreiben können.
</Meeting Besprechung 2 - Refinement>