ticket_id: CMS20-1248
summary: Sichtbarkeit des Bildersuche-Buttons basierend auf Benutzerrolle
issue_type: Story
status: In Progress
priority: Medium
assignee: Developer
components: ["api"]
labels: ["backend"]

---

# Sichtbarkeit des Bildersuche-Buttons basierend auf Benutzerrolle

Warum:

Als System möchte ich vor dem Rendern des "Bildersuche öffnen"-Buttons prüfen, ob der eingeloggte Benutzer die Rolle "Redakteur" besitzt, indem ich die Benutzer-ID an die PEIQ API sende und deren Antwort auswerte, damit der Button nur für berechtigte Redakteure sichtbar ist und andere Benutzer ihn nicht sehen.

Input:

Der "Bildersuche öffnen"-Button ist Teil des Beitrags-Erfassungsformulars.

Die Benutzeridentifikation erfolgt über eine userId, die im Frontend/Cookie/Sessionstorage verfügbar ist.

Die Rollenprüfung erfolgt über einen Aufruf an die PEIQ API.

## Akzeptanzkriterien:

- Bevor der "Bildersuche öffnen"-Button im Beitragsformular gerendert wird, wird eine Anfrage an die PEIQ API gesendet, um die Rolle des aktuell eingeloggten Benutzers anhand seiner userId zu überprüfen.

- Wenn die PEIQ API bestätigt, dass der Benutzer die Rolle "Redakteur" besitzt, wird der "Bildersuche öffnen"-Button im Beitragsformular sichtbar gerendert.

- Wenn die PEIQ API meldet, dass der Benutzer nicht die Rolle "Redakteur" besitzt, oder wenn die Abfrage fehlschlägt, wird der "Bildersuche öffnen"-Button nicht gerendert und ist für den Benutzer nicht sichtbar.

- Die Abfrage wird für 24h gecached

- Die Businesslogic ist in einem eigenen Service abgebildet, da zum aktuellen Zeitpunkt kein passender Service existiert. (user-service)

- User-service ist via API-key protected und der API-key auf Domain eingeschränkt

- API-KEY ist neu anzulegen für “next-module”

- Wenn die Rolle = Admin ist, dann sollen die Buttons auf jeden Fall angezeigt werden!






*Auto-generated by AI Ticket Converter on 2025-07-24 08:31:22*