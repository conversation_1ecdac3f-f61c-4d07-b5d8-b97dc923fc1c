Autorefresh von Luftqualität-Beiträgen 


Eine Logik überprüft täglich, in welchen Beiträgen das Spritpreis und Apotheken Widget verbaut ist. Bei diesen Beiträgen wird dann automatisch das “zuletzt aktualisiert am” auf den aktuellen Tag 6.00 Uhr aktualisiert.

Dieselbe Logik wird nun auch für die Beiträge benötigt, bei denen das Luftqualität-Widget verbaut ist.





Input:

Der ursprüngliche Gedanke war, dass die Beiträge, die in den letzten 24h (plus eventuellen puffer) aktualisiert/erstellt wurden, geladen werden und auf ein definiertes HTML Widget geprüft werden. 
Jene Beiträge werden dann erneut täglich aktualisiert

 Vorsicht: Es kann und wird sein, dass unterschiedliche Widgets zu unterschiedlichen Zeiten upgedatet werden müssen!