Cloud Run Service zur stündlichen Extraktion von Hero-Bild- und Beitragsmetadaten aus der meinbezirk.at

Warum:

Als Entwickler möchte ich einen dedizierten Cloud Run Service erstellen, der im Stundenintervall relevante Hero-Bilder und die dazugehörigen Beitragsmetadaten über die meinbezirk.at-API ausliest, damit die Rohdaten für nachfolgende Verarbeitungsschritte bereitgestellt werden können.

Input:

Quell-API: meinbezirk.at-API.

Zu extrahierende Bildinformationen: Es soll ausschließlich das Hero Image des jeweiligen Beitrags verwendet werden, da nur dieses eine Bildunterschrift enthält.

Uploader (referenziert über ID aus der meinbezirk.at-API).

Copyright.

Description (nicht ALT Text).

URL zum Bild.

ID des Bildes.

Zu extrahierende Beitragsinformationen (verknüpft mit Bildern):

Gesetzte Keywords.

Bundesland und Bezirk (wenn es kein nationaler Beitrag ist).

Kategorie des Beitrages.

Content/Zusammenfassung (Klärung, ob Short Summary oder Kompletttext, steht noch aus).

Überschrift.

Kicker.

ID des Beitrags.

QA:

Wird über logs in GoogleCloud stattfinden

Akzeptanzkriterien:

Service-Bereitstellung: Ein neuer Cloud Run Service ist erfolgreich in Google Cloud bereitgestellt.

API-Konnektivität: Der Service kann erfolgreich eine Verbindung zur meinbezirk.at-API herstellen und Daten abfragen.

Datenextraktion (Hero Image spezifisch): Der Service ist in der Lage, ausschließlich die definierten Metadaten des Hero Images eines Beitrags (Uploader, Copyright, Description, Bild-URL, Bild-ID) sowie die dazugehörigen Beitragsmetadaten (Keywords, Bundesland/Bezirk, Kategorie, Content/Zusammenfassung, Überschrift, Kicker, Beitrags-ID) aus der meinbezirk.at-API zu extrahieren.

Zeitfilterung: Der Service ist standardmäßig so konfiguriert, dass er Beiträge und deren Hero Images extrahiert, die in der letzten Stunde veröffentlicht wurden.

Iteration über PEIQ API maximum berücksichtigen (gilt als vorbereitung für historischen Import)

Initialer Extraktions-Endpoint: Es existiert ein separater Endpoint im Service, der die Extraktion von Beiträgen der letzten 2 Jahre ermöglicht, wobei die interne Logik zur Datenextraktion identisch ist. Sprich dieser Endpoint macht genau das Selbe wie die stündliche Logik, es werden nur die Daten von einem größeren Zeitraum gefetched.

Logging: Der Service protokolliert relevante Aktionen und etwaige Fehler während des Extraktionsprozesses im Google Cloud Logging, um die Überwachung und Fehlersuche zu erleichtern.

Sicherheit: Der Cloud Run Service ist mit einem eigenen Service Account konfiguriert und befolgt die Google Cloud Best Practices für Berechtigungen.

Architekturkonformität: Der Service ist gemäß der definierten Microservice-Architektur aufgebaut (Routing, Controller, Service Struktur). Business-Logik ist ausschließlich im Service-Layer implementiert, Controller parsen und validieren lediglich Variablen und rufen Services auf.

Testbarkeit: Der Service ist so konzipiert, dass die Extraktionslogik separat getestet werden kann.

Codetesting

