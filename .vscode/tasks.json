{"version": "2.0.0", "tasks": [{"label": "Start Multi-Agent Docker Services", "type": "shell", "command": "${workspaceFolder}/code-reviewer-service/scripts/docker-dev.sh", "args": ["up"], "options": {"cwd": "${workspaceFolder}/code-reviewer-service"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "Stop Multi-Agent Docker Services", "type": "shell", "command": "${workspaceFolder}/code-reviewer-service/scripts/docker-dev.sh", "args": ["down"], "options": {"cwd": "${workspaceFolder}/code-reviewer-service"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Install Multi-Agent Dependencies", "type": "shell", "command": "${workspaceFolder}/code-reviewer-service/.venv/bin/pip", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}/code-reviewer-service"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Install Frontend Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/web-app/review-comment-responder-react"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Install Legacy Dependencies", "type": "shell", "command": "pip", "args": ["install", "-r", "requirements.txt"], "options": {"cwd": "${workspaceFolder}", "env": {"PATH": "${workspaceFolder}/venv/bin:${env:PATH}"}}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Multi-Agent Tests", "type": "shell", "command": "${workspaceFolder}/code-reviewer-service/.venv/bin/python", "args": ["-m", "pytest", "tests/", "-v"], "options": {"cwd": "${workspaceFolder}/code-reviewer-service", "env": {"PYTHONPATH": "${workspaceFolder}/code-reviewer-service/src:${workspaceFolder}/code-reviewer-service"}}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Frontend Tests", "type": "shell", "command": "npm", "args": ["run", "test:run"], "options": {"cwd": "${workspaceFolder}/web-app/review-comment-responder-react"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Build Frontend", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/web-app/review-comment-responder-react"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Setup Development Environment", "dependsOrder": "sequence", "dependsOn": ["Install Multi-Agent Dependencies", "Install Legacy Dependencies", "Install Frontend Dependencies", "Start Multi-Agent Docker Services"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}