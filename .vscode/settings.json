{
  "python.pythonPath": "/usr/bin/python3",
  "python.defaultInterpreterPath": "/usr/bin/python3",
  "python.terminal.activateEnvironment": true,
  "python.analysis.autoImportCompletions": true,
  "typescript.preferences.importModuleSpecifier": "relative",
  
  // Multi-workspace Python configuration
  "python.analysis.extraPaths": [
    "./code-reviewer-service/src",
    "./core",
    "./web-app/review-comment-responder-react"
  ],
  
  // Debugging configuration
  "python.debugging.console": "integratedTerminal",
  "python.terminal.activateEnvInCurrentTerminal": true,
  
  // Poetry support
  "python.poetryPath": "poetry",
  
  // FastAPI specific settings
  "python.analysis.typeCheckingMode": "basic",
  "python.linting.enabled": true,
  "python.linting.pylintEnabled": false,
  "python.linting.flake8Enabled": true,
  "python.formatting.provider": "black",
  
  // File associations
  "files.associations": {
    "*.py": "python",
    "*.pyi": "python",
    "*.md": "markdown",
    "*.json": "jsonc",
    "docker-compose*.yml": "dockercompose",
    "docker-compose*.yaml": "dockercompose"
  },
  
  // Terminal settings
  "terminal.integrated.env.osx": {
    "PYTHONPATH": "${workspaceFolder}/code-reviewer-service/src:${workspaceFolder}/core"
  },
  "terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}/code-reviewer-service/src:${workspaceFolder}/core"
  },
  
  // React/TypeScript settings for frontend
  "typescript.preferences.noSemicolons": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  
  // Exclude patterns
  "files.exclude": {
    "**/.git": true,
    "**/.DS_Store": true,
    "**/node_modules": true,
    "**/__pycache__": true,
    "**/.pytest_cache": true,
    "**/dist": true,
    "**/build": true,
    "**/.venv": true,
    "**/venv": true
  },
  
  // Search exclude patterns
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.venv": true,
    "**/venv": true,
    "**/__pycache__": true,
    "**/.pytest_cache": true
  }
}