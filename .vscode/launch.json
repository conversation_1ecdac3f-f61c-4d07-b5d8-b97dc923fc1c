{"version": "0.2.0", "configurations": [{"name": "Debug Flask Backend (Enhanced Reviewer)", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/core/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/core", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1", "PYTHONPATH": "${workspaceFolder}"}, "python": "${workspaceFolder}/venv/bin/python", "args": [], "justMyCode": false, "stopOnEntry": false}, {"name": "Debug Comment Responder Backend", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/web-app/review-comment-responder-react/claude-integration.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}/web-app/review-comment-responder-react", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "python": "${workspaceFolder}/venv/bin/python", "args": [], "justMyCode": false}, {"name": "Debug Multi-Agent Code Reviewer (FastAPI)", "type": "debugpy", "request": "launch", "module": "src.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}/code-reviewer-service", "env": {"ENVIRONMENT": "development", "DEBUG": "true", "LOG_LEVEL": "DEBUG", "HOST": "0.0.0.0", "PORT": "8000", "PYTHONPATH": "${workspaceFolder}/code-reviewer-service"}, "python": "${workspaceFolder}/code-reviewer-service/.venv/bin/python", "args": [], "justMyCode": false, "stopOnEntry": false, "preLaunchTask": "Start Multi-Agent Docker Services"}, {"name": "Debug Multi-Agent (Alternative)", "type": "debugpy", "request": "launch", "module": "src.main", "console": "integratedTerminal", "cwd": "${workspaceFolder}/code-reviewer-service", "env": {"ENVIRONMENT": "development", "DEBUG": "true", "LOG_LEVEL": "DEBUG", "PYTHONPATH": "${workspaceFolder}/code-reviewer-service"}, "python": "${workspaceFolder}/code-reviewer-service/.venv/bin/python", "args": [], "justMyCode": true, "stopOnEntry": false}, {"name": "Debug Code Reviewer API", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/claude-code-reviewer-api.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"FLASK_ENV": "development", "FLASK_DEBUG": "1"}, "python": "${workspaceFolder}/venv/bin/python", "args": [], "justMyCode": false}, {"name": "Debug Frontend (Vite Dev Server)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/web-app/review-comment-responder-react", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "env": {"NODE_ENV": "development", "VITE_FEATURE_REALTIME_UPDATES": "true", "VITE_WEBSOCKET_AUTO_RECONNECT": "true", "VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS": "3", "VITE_WEBSOCKET_RECONNECT_DELAY": "3000"}, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Frontend (No WebSocket)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/web-app/review-comment-responder-react", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "console": "integratedTerminal", "env": {"NODE_ENV": "development", "VITE_FEATURE_REALTIME_UPDATES": "false", "VITE_WEBSOCKET_AUTO_RECONNECT": "false", "VITE_WEBSOCKET_MAX_RECONNECT_ATTEMPTS": "0"}, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Frontend (Chrome)", "type": "chrome", "request": "launch", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/web-app/review-comment-responder-react/src", "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/*"}, "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile"}, {"name": "Attach to Frontend", "type": "chrome", "request": "attach", "port": 9222, "url": "http://localhost:5173", "webRoot": "${workspaceFolder}/web-app/review-comment-responder-react/src"}], "compounds": [{"name": "Debug <PERSON> Stack (Legacy)", "configurations": ["Debug Flask Backend (Enhanced Reviewer)", "Debug Comment Responder Backend", "Debug Frontend (Vite Dev Server)"], "stopAll": true}, {"name": "Debug <PERSON> (Multi-Agent)", "configurations": ["Debug Multi-Agent Code Reviewer (FastAPI)", "Debug Comment Responder Backend", "Debug Frontend (Vite Dev Server)"], "stopAll": true}, {"name": "Debug <PERSON> Stack (Complete)", "configurations": ["Debug Multi-Agent Code Reviewer (FastAPI)", "Debug Flask Backend (Enhanced Reviewer)", "Debug Comment Responder Backend", "Debug Frontend (Vite Dev Server)"], "stopAll": true}, {"name": "Debug Full Stack (Multi-Agent - Safe Mode)", "configurations": ["Debug Multi-Agent Code Reviewer (FastAPI)", "Debug Comment Responder Backend", "Debug Frontend (No WebSocket)"], "stopAll": true}]}