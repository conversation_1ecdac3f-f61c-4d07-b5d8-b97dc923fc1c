#!/usr/bin/env python3
"""
Simple startup script for the Enhanced Claude Code Reviewer API
"""

import os
import sys
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Start the API server"""
    try:
        # Import and create the Flask app
        from core.api.app import create_app
        
        logger.info("🚀 Starting Enhanced Claude Code Reviewer API...")
        
        # Create app and socketio
        app, socketio = create_app()
        
        logger.info("📡 API Server starting on http://localhost:5002")
        logger.info("🔌 WebSocket support enabled for real-time updates")
        logger.info("💡 Frontend should connect to this service for enhanced code review")
        
        # Start the server
        socketio.run(
            app,
            host='0.0.0.0',
            port=5002,
            debug=True,
            allow_unsafe_werkzeug=True
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to start API server: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()